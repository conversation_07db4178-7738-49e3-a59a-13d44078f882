"""
Enhanced PDF dataset extraction with improved handling of split URLs and edge cases.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm
import time


def clean_text_for_urls(text):
    """Clean text to handle URLs split across lines."""
    # Fix common URL splits
    text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
    text = re.sub(r'doi\.org/\s*\n\s*', 'doi.org/', text)
    text = re.sub(r'https://doi\.\s+org/', 'https://doi.org/', text)
    text = re.sub(r'10\.\d+/\s*\n\s*', '10.', text)
    
    # Remove excessive whitespace but preserve single spaces
    text = re.sub(r'\s+', ' ', text)
    
    return text


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]  # Versão completa
    
    # Se começar com https://, adicionar versão sem https://
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        # Se contém doi.org, adicionar apenas a parte após doi.org/
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
            
            # Adicionar versão com doi: prefix
            variations.append(f"doi:{dataset_id}")
            variations.append(f"doi: {dataset_id}")
    
    # Se não começar com https:// mas contém doi.org, adicionar a parte após doi.org/
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
    
    return variations


def extract_preceding_words_enhanced(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto com melhor handling."""
    try:
        # Clean text first
        cleaned_text = clean_text_for_urls(text)
        
        # Try exact match first
        escaped_target = re.escape(target)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
        
        # If exact match fails, try partial matching for DOIs
        if "doi.org/" in target:
            doi_part = target.split("doi.org/", 1)[1]
            # Remove any trailing punctuation for matching
            doi_clean = re.sub(r'[^\w/.-]', '', doi_part)
            
            escaped_doi = re.escape(doi_clean)
            pattern = rf'(.+?)\b{escaped_doi}\b'
            match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
            
            if match:
                preceding_text = match.group(1)
                words = re.findall(r'\b\w+\b', preceding_text)
                if len(words) >= num_words:
                    return ' '.join(words[-num_words:])
                else:
                    return ' '.join(words)
    
    except Exception:
        pass
    
    return None


def find_dataset_urls_in_text(text):
    """Find all potential dataset URLs in text, including reconstructed split URLs."""
    urls = []
    
    # Clean text first
    cleaned_text = clean_text_for_urls(text)
    
    # Common dataset URL patterns
    patterns = [
        r'https://doi\.org/10\.\d+/[^\s\)]+',
        r'doi\.org/10\.\d+/[^\s\)]+',
        r'https://doi\.org/10\.5061/dryad\.[^\s\)]+',
        r'10\.5061/dryad\.[^\s\)]+',
        r'10\.17882/\d+',
        r'https://doi\.org/10\.17882/\d+'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
        urls.extend(matches)
    
    return urls


def extract_random_words(text, num_words=10):
    """Extrai palavras aleatórias do texto."""
    words = re.findall(r'\b\w+\b', text)
    if len(words) < num_words:
        return ' '.join(words)
    
    random_words = random.sample(words, num_words)
    return ' '.join(random_words)


def process_single_pdf_enhanced(pdf_path, article_id, dataset_id):
    """
    Processa um único PDF e extrai as palavras que antecedem o dataset_id.
    Versão melhorada com handling de URLs quebradas.
    """
    
    try:
        reader = PdfReader(pdf_path)
        
        # Extrair texto de todas as páginas
        full_text = ""
        for page in reader.pages:
            try:
                page_text = page.extract_text()
                full_text += page_text + "\n"
            except Exception:
                continue
        
        if dataset_id == "Missing":
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Clean the full text
        cleaned_text = clean_text_for_urls(full_text)
        
        # Tentar encontrar o dataset_id e suas variações
        variations = normalize_dataset_id(dataset_id)
        
        for variation in variations:
            preceding_words = extract_preceding_words_enhanced(cleaned_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        # If still not found, try to find any dataset URLs and see if they match
        found_urls = find_dataset_urls_in_text(cleaned_text)
        for url in found_urls:
            # Check if this URL matches any variation of our target
            for variation in variations:
                if variation in url or url in variation:
                    preceding_words = extract_preceding_words_enhanced(cleaned_text, url)
                    if preceding_words:
                        return article_id, dataset_id, preceding_words
        
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)[:50]}"


def main():
    """Função principal para processar todos os PDFs."""
    
    # Carregar dados
    df = pd.read_csv('data/train_labels.csv')
    
    # Filtrar apenas casos que não são Missing para teste
    test_cases = [
        ("10.1002_2017jc013030", "https://doi.org/10.17882/49388"),
        ("10.1002_ece3.4466", "https://doi.org/10.5061/dryad.r6nq870")
    ]
    
    results = []
    
    print("🚀 Testando casos específicos com extração melhorada...")
    
    for article_id, dataset_id in test_cases:
        pdf_path = f"data/train/PDF/{article_id}.pdf"
        
        if Path(pdf_path).exists():
            print(f"\n📄 Processando {article_id}...")
            result = process_single_pdf_enhanced(pdf_path, article_id, dataset_id)
            results.append(result)
            print(f"   Resultado: {result[2]}")
        else:
            print(f"❌ PDF não encontrado: {pdf_path}")
    
    # Salvar resultados
    if results:
        results_df = pd.DataFrame(results, columns=['article_id', 'dataset_id', 'preceding_words'])
        results_df.to_csv('results/enhanced_extraction_test.csv', index=False)
        print(f"\n✅ Resultados salvos em results/enhanced_extraction_test.csv")
        print(f"📊 Total processado: {len(results)} PDFs")


if __name__ == "__main__":
    main()
