"""
Complete enhanced PDF dataset extraction script that handles all edge cases including:
- Split URLs across lines
- URLs in acknowledgments and references
- Different DOI formats
- Data availability sections
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm
import time


def clean_text_for_urls(text):
    """Clean text to handle URLs split across lines and other formatting issues."""
    # Fix common URL splits
    text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
    text = re.sub(r'doi\.org/\s*\n\s*', 'doi.org/', text)
    text = re.sub(r'https://doi\.\s+org/', 'https://doi.org/', text)
    text = re.sub(r'10\.\d+/\s*\n\s*', '10.', text)
    
    # Fix dryad URLs specifically
    text = re.sub(r'dryad\.\s*\n\s*', 'dryad.', text)
    text = re.sub(r'10\.5061/\s*\n\s*dryad\.', '10.5061/dryad.', text)
    
    # Fix other common splits
    text = re.sub(r'(\d+)\s*\n\s*(\d+)', r'\1\2', text)
    
    # Remove excessive whitespace but preserve single spaces
    text = re.sub(r'\s+', ' ', text)
    
    return text


def normalize_dataset_id_comprehensive(dataset_id):
    """Create comprehensive variations of dataset_id for matching."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]  # Original version
    
    # Handle https:// URLs
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        # Handle doi.org URLs
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
            
            # Add doi: prefixed versions
            variations.append(f"doi:{dataset_id}")
            variations.append(f"doi: {dataset_id}")
            variations.append(f"doi:https://doi.org/{after_doi}")
            variations.append(f"doi: https://doi.org/{after_doi}")
    
    # Handle doi.org URLs without https://
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
        variations.append(f"https://{dataset_id}")
        variations.append(f"https://doi.org/{after_doi}")
    
    # Handle bare DOIs (like 10.5061/dryad.r6nq870)
    elif dataset_id.startswith("10."):
        variations.append(f"doi.org/{dataset_id}")
        variations.append(f"https://doi.org/{dataset_id}")
        variations.append(f"doi:{dataset_id}")
        variations.append(f"doi: {dataset_id}")
    
    # Remove duplicates while preserving order
    seen = set()
    unique_variations = []
    for var in variations:
        if var not in seen:
            seen.add(var)
            unique_variations.append(var)
    
    return unique_variations


def extract_preceding_words_robust(text, target, num_words=10):
    """Extract preceding words with robust handling of various text formats."""
    try:
        # Clean text first
        cleaned_text = clean_text_for_urls(text)
        
        # Try exact match first
        escaped_target = re.escape(target)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
        
        # If exact match fails, try flexible matching
        # Remove punctuation from target for more flexible matching
        flexible_target = re.sub(r'[^\w/.-]', '', target)
        if flexible_target != target:
            escaped_flexible = re.escape(flexible_target)
            pattern = rf'(.+?)\b{escaped_flexible}\b'
            match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
            
            if match:
                preceding_text = match.group(1)
                words = re.findall(r'\b\w+\b', preceding_text)
                if len(words) >= num_words:
                    return ' '.join(words[-num_words:])
                else:
                    return ' '.join(words)
        
        # Try partial matching for DOIs
        if "doi.org/" in target or target.startswith("10."):
            # Extract the DOI part
            if "doi.org/" in target:
                doi_part = target.split("doi.org/", 1)[1]
            else:
                doi_part = target
            
            # Clean DOI part
            doi_clean = re.sub(r'[^\w/.-]', '', doi_part)
            
            escaped_doi = re.escape(doi_clean)
            pattern = rf'(.+?)\b{escaped_doi}\b'
            match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
            
            if match:
                preceding_text = match.group(1)
                words = re.findall(r'\b\w+\b', preceding_text)
                if len(words) >= num_words:
                    return ' '.join(words[-num_words:])
                else:
                    return ' '.join(words)
    
    except Exception:
        pass
    
    return None


def find_all_dataset_urls(text):
    """Find all potential dataset URLs in text."""
    urls = []
    
    # Clean text first
    cleaned_text = clean_text_for_urls(text)
    
    # Comprehensive dataset URL patterns
    patterns = [
        r'https://doi\.org/10\.\d+/[^\s\)\],]+',
        r'doi\.org/10\.\d+/[^\s\)\],]+',
        r'https://doi\.org/10\.5061/dryad\.[^\s\)\],]+',
        r'10\.5061/dryad\.[^\s\)\],]+',
        r'10\.17882/\d+',
        r'https://doi\.org/10\.17882/\d+',
        r'10\.5061/dryad\.[a-zA-Z0-9]+',
        r'dryad\.[a-zA-Z0-9]+',
        r'10\.\d+/[a-zA-Z0-9._-]+',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
        urls.extend(matches)
    
    # Clean up URLs (remove trailing punctuation)
    cleaned_urls = []
    for url in urls:
        cleaned_url = re.sub(r'[.,;:)]+$', '', url)
        if cleaned_url:
            cleaned_urls.append(cleaned_url)
    
    return list(set(cleaned_urls))  # Remove duplicates


def extract_random_words(text, num_words=10):
    """Extract random words from text."""
    words = re.findall(r'\b\w+\b', text)
    if len(words) < num_words:
        return ' '.join(words)
    
    random_words = random.sample(words, num_words)
    return ' '.join(random_words)


def process_single_pdf_comprehensive(pdf_path, article_id, dataset_id):
    """
    Process a single PDF with comprehensive dataset ID extraction.
    Handles split URLs, various formats, and edge cases.
    """
    
    try:
        reader = PdfReader(pdf_path)
        
        # Extract text from all pages
        full_text = ""
        for page in reader.pages:
            try:
                page_text = page.extract_text()
                full_text += page_text + "\n"
            except Exception:
                continue
        
        if dataset_id == "Missing":
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Clean the full text
        cleaned_text = clean_text_for_urls(full_text)
        
        # Get all variations of the dataset ID
        variations = normalize_dataset_id_comprehensive(dataset_id)
        
        # Try to find each variation
        for variation in variations:
            preceding_words = extract_preceding_words_robust(cleaned_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        # If still not found, find all dataset URLs and try to match
        found_urls = find_all_dataset_urls(cleaned_text)
        for url in found_urls:
            # Check if this URL matches any variation of our target
            for variation in variations:
                if (variation in url or url in variation or 
                    url.endswith(variation) or variation.endswith(url)):
                    preceding_words = extract_preceding_words_robust(cleaned_text, url)
                    if preceding_words:
                        return article_id, dataset_id, preceding_words
        
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"

    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)[:50]}"


def process_all_pdfs_enhanced(batch_size=50):
    """Process all PDFs with enhanced extraction in batches."""

    # Load data
    df = pd.read_csv('data/train_labels.csv')

    # Create results directory if it doesn't exist
    Path('results').mkdir(exist_ok=True)

    # Filter out cases that are not Missing (we want to test our improvements)
    non_missing = df[df['dataset_id'] != 'Missing'].copy()

    print(f"🚀 Processing {len(non_missing)} non-missing PDFs with enhanced extraction...")
    print(f"📦 Batch size: {batch_size}")

    results = []
    total_batches = (len(non_missing) + batch_size - 1) // batch_size

    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, len(non_missing))
        batch_df = non_missing.iloc[start_idx:end_idx]

        print(f"\n📦 Processing batch {batch_num + 1}/{total_batches} ({len(batch_df)} PDFs)")

        batch_results = []
        for _, row in tqdm(batch_df.iterrows(), total=len(batch_df), desc=f"Batch {batch_num + 1}"):
            article_id = row['article_id']
            dataset_id = row['dataset_id']

            pdf_path = f"data/train/PDF/{article_id}.pdf"

            if Path(pdf_path).exists():
                result = process_single_pdf_comprehensive(pdf_path, article_id, dataset_id)
                batch_results.append(result)
            else:
                batch_results.append((article_id, dataset_id, "PDF_NOT_FOUND"))

        results.extend(batch_results)

        # Save intermediate results
        if batch_results:
            intermediate_df = pd.DataFrame(results, columns=['article_id', 'dataset_id', 'preceding_words'])
            intermediate_df.to_csv(f'results/enhanced_extraction_batch_{batch_num + 1}.csv', index=False)
            print(f"   ✅ Batch {batch_num + 1} saved")

        # Small delay between batches
        time.sleep(1)

    # Save final results
    if results:
        final_df = pd.DataFrame(results, columns=['article_id', 'dataset_id', 'preceding_words'])
        final_df.to_csv('results/enhanced_extraction_complete.csv', index=False)

        # Count successes
        found_count = len(final_df[~final_df['preceding_words'].isin(['DATASET_ID_NOT_FOUND', 'PDF_NOT_FOUND']) &
                                   ~final_df['preceding_words'].str.startswith('ERROR')])
        total_count = len(final_df)

        print(f"\n🎉 Processing complete!")
        print(f"📊 Total processed: {total_count} PDFs")
        print(f"✅ Successfully found: {found_count} dataset IDs")
        print(f"📈 Success rate: {found_count/total_count*100:.1f}%")
        print(f"💾 Results saved to: results/enhanced_extraction_complete.csv")

        return final_df

    return None


def test_specific_cases():
    """Test the specific problematic cases mentioned by the user."""

    test_cases = [
        ("10.1002_2017jc013030", "https://doi.org/10.17882/49388"),
        ("10.1002_ece3.4466", "https://doi.org/10.5061/dryad.r6nq870")
    ]

    print("🧪 Testing specific problematic cases...")

    results = []
    for article_id, dataset_id in test_cases:
        pdf_path = f"data/train/PDF/{article_id}.pdf"

        if Path(pdf_path).exists():
            print(f"\n📄 Testing {article_id}...")
            result = process_single_pdf_comprehensive(pdf_path, article_id, dataset_id)
            results.append(result)
            print(f"   Result: {result[2]}")
        else:
            print(f"❌ PDF not found: {pdf_path}")

    return results


def main():
    """Main function with options for different processing modes."""

    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # Test mode - only test specific cases
        test_specific_cases()
    elif len(sys.argv) > 1 and sys.argv[1] == "batch":
        # Batch mode - process all PDFs
        batch_size = int(sys.argv[2]) if len(sys.argv) > 2 else 50
        process_all_pdfs_enhanced(batch_size)
    else:
        # Default - test specific cases first, then ask user
        print("🔧 Enhanced PDF Dataset Extraction Tool")
        print("=" * 50)

        # Test specific cases first
        test_results = test_specific_cases()

        if test_results:
            print(f"\n✅ Test cases completed successfully!")

            # Ask user if they want to process all PDFs
            response = input("\n🤔 Do you want to process all non-missing PDFs? (y/n): ").lower().strip()
            if response in ['y', 'yes']:
                batch_size = input("📦 Enter batch size (default 50): ").strip()
                batch_size = int(batch_size) if batch_size.isdigit() else 50
                process_all_pdfs_enhanced(batch_size)
            else:
                print("👍 Stopping here. Use 'python complete_enhanced_extraction.py batch' to process all PDFs later.")


if __name__ == "__main__":
    main()
