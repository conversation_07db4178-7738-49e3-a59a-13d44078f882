"""
<PERSON><PERSON><PERSON> to fix all DATASET_ID_NOT_FOUND cases using enhanced extraction.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm
import time


def clean_text_for_urls(text):
    """Clean text to handle URLs split across lines."""
    # Fix common URL splits
    text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
    text = re.sub(r'doi\.org/\s*\n\s*', 'doi.org/', text)
    text = re.sub(r'https://doi\.\s+org/', 'https://doi.org/', text)
    text = re.sub(r'10\.\d+/\s*\n\s*', '10.', text)
    
    # Fix dryad URLs specifically
    text = re.sub(r'dryad\.\s*\n\s*', 'dryad.', text)
    text = re.sub(r'10\.5061/\s*\n\s*dryad\.', '10.5061/dryad.', text)
    
    # Remove excessive whitespace but preserve single spaces
    text = re.sub(r'\s+', ' ', text)
    
    return text


def normalize_dataset_id_comprehensive(dataset_id):
    """Create comprehensive variations of dataset_id for matching."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]  # Original version
    
    # Handle https:// URLs
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        # Handle doi.org URLs
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
    
    # Handle doi.org URLs without https://
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
        variations.append(f"https://{dataset_id}")
        variations.append(f"https://doi.org/{after_doi}")
    
    # Handle bare DOIs (like 10.5061/dryad.r6nq870)
    elif dataset_id.startswith("10."):
        variations.append(f"doi.org/{dataset_id}")
        variations.append(f"https://doi.org/{dataset_id}")
    
    # Remove duplicates while preserving order
    seen = set()
    unique_variations = []
    for var in variations:
        if var not in seen:
            seen.add(var)
            unique_variations.append(var)
    
    return unique_variations


def extract_preceding_words_robust(text, target, num_words=10):
    """Extract preceding words with robust handling."""
    try:
        # Clean text first
        cleaned_text = clean_text_for_urls(text)
        
        # Try exact match first
        escaped_target = re.escape(target)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
        
        # Try partial matching for DOIs
        if "doi.org/" in target or target.startswith("10."):
            # Extract the DOI part
            if "doi.org/" in target:
                doi_part = target.split("doi.org/", 1)[1]
            else:
                doi_part = target
            
            # Clean DOI part
            doi_clean = re.sub(r'[^\w/.-]', '', doi_part)
            
            escaped_doi = re.escape(doi_clean)
            pattern = rf'(.+?)\b{escaped_doi}\b'
            match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
            
            if match:
                preceding_text = match.group(1)
                words = re.findall(r'\b\w+\b', preceding_text)
                if len(words) >= num_words:
                    return ' '.join(words[-num_words:])
                else:
                    return ' '.join(words)
    
    except Exception:
        pass
    
    return None


def find_all_dataset_urls(text):
    """Find all potential dataset URLs in text."""
    urls = []
    
    # Clean text first
    cleaned_text = clean_text_for_urls(text)
    
    # Comprehensive dataset URL patterns
    patterns = [
        r'https://doi\.org/10\.\d+/[^\s\)\],]+',
        r'doi\.org/10\.\d+/[^\s\)\],]+',
        r'https://doi\.org/10\.5061/dryad\.[^\s\)\],]+',
        r'10\.5061/dryad\.[^\s\)\],]+',
        r'10\.17882/\d+',
        r'https://doi\.org/10\.17882/\d+',
        r'10\.5061/dryad\.[a-zA-Z0-9]+',
        r'dryad\.[a-zA-Z0-9]+',
        r'10\.\d+/[a-zA-Z0-9._-]+',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
        urls.extend(matches)
    
    # Clean up URLs (remove trailing punctuation)
    cleaned_urls = []
    for url in urls:
        cleaned_url = re.sub(r'[.,;:)]+$', '', url)
        if cleaned_url:
            cleaned_urls.append(cleaned_url)
    
    return list(set(cleaned_urls))  # Remove duplicates


def process_single_pdf_enhanced(pdf_path, article_id, dataset_id):
    """Process a single PDF with enhanced extraction."""
    
    try:
        reader = PdfReader(pdf_path)
        
        # Extract text from all pages
        full_text = ""
        for page in reader.pages:
            try:
                page_text = page.extract_text()
                full_text += page_text + "\n"
            except Exception:
                continue
        
        if dataset_id == "Missing":
            return article_id, dataset_id, "MISSING_CASE"
        
        # Get all variations of the dataset ID
        variations = normalize_dataset_id_comprehensive(dataset_id)
        
        # Try to find each variation
        for variation in variations:
            preceding_words = extract_preceding_words_robust(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        # If still not found, find all dataset URLs and try to match
        found_urls = find_all_dataset_urls(full_text)
        for url in found_urls:
            # Check if this URL matches any variation of our target
            for variation in variations:
                if (variation in url or url in variation or 
                    url.endswith(variation) or variation.endswith(url)):
                    preceding_words = extract_preceding_words_robust(full_text, url)
                    if preceding_words:
                        return article_id, dataset_id, preceding_words
        
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)[:50]}"


def main():
    """Fix all DATASET_ID_NOT_FOUND cases."""
    
    # Load the previous results
    df = pd.read_csv('results/final_extraction_1028_files_20250615_143447.csv')
    
    # Filter cases that need fixing
    not_found_cases = df[df['preceding_words'] == 'DATASET_ID_NOT_FOUND'].copy()
    
    print(f"🔧 Found {len(not_found_cases)} cases with DATASET_ID_NOT_FOUND")
    print(f"🚀 Starting enhanced extraction...")
    
    # Create results directory if it doesn't exist
    Path('results').mkdir(exist_ok=True)
    
    results = []
    fixed_count = 0
    
    for _, row in tqdm(not_found_cases.iterrows(), total=len(not_found_cases), desc="Processing"):
        article_id = row['article_id']
        dataset_id = row['dataset_id']
        
        pdf_path = f"data/train/PDF/{article_id}.pdf"
        
        if Path(pdf_path).exists():
            result = process_single_pdf_enhanced(pdf_path, article_id, dataset_id)
            results.append(result)
            
            # Check if we fixed it
            if result[2] not in ['DATASET_ID_NOT_FOUND', 'MISSING_CASE'] and not result[2].startswith('ERROR'):
                fixed_count += 1
                if fixed_count <= 10:  # Show first 10 fixes
                    print(f"✅ Fixed {article_id}: {result[2][:60]}...")
        else:
            results.append((article_id, dataset_id, "PDF_NOT_FOUND"))
    
    # Save results
    if results:
        results_df = pd.DataFrame(results, columns=['article_id', 'dataset_id', 'preceding_words'])
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = f'results/enhanced_fixes_{len(results)}_cases_{timestamp}.csv'
        results_df.to_csv(output_file, index=False)
        
        print(f"\n🎉 Processing complete!")
        print(f"📊 Total processed: {len(results)} cases")
        print(f"✅ Successfully fixed: {fixed_count} cases")
        print(f"📈 Fix rate: {fixed_count/len(results)*100:.1f}%")
        print(f"💾 Results saved to: {output_file}")
        
        # Show some statistics
        success_cases = results_df[~results_df['preceding_words'].isin(['DATASET_ID_NOT_FOUND', 'PDF_NOT_FOUND', 'MISSING_CASE']) & 
                                  ~results_df['preceding_words'].str.startswith('ERROR')]
        
        if len(success_cases) > 0:
            print(f"\n📋 Sample of fixed cases:")
            for i, (_, row) in enumerate(success_cases.head(5).iterrows()):
                print(f"  {i+1}. {row['article_id']}: {row['preceding_words'][:80]}...")
        
        return results_df
    
    return None


if __name__ == "__main__":
    main()
