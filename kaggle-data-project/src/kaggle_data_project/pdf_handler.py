"""
PDF handling utilities using pypdf.
All output files are saved to the 'results' folder.
"""

from pypdf import Pdf<PERSON><PERSON><PERSON>, PdfWriter
from pathlib import Path
from typing import List, Optional, Dict, Any
import re


class PDFHandler:
    """A class for handling PDF operations."""
    
    def __init__(self):
        self.current_pdf: Optional[PdfReader] = None
        self.current_path: Optional[str] = None
    
    def load_pdf(self, file_path: str) -> PdfReader:
        """Load a PDF file."""
        if not Path(file_path).exists():
            raise FileNotFoundError(f"PDF file not found: {file_path}")
        
        self.current_pdf = PdfReader(file_path)
        self.current_path = file_path
        return self.current_pdf
    
    def get_pdf_info(self) -> Dict[str, Any]:
        """Get basic information about the loaded PDF."""
        if self.current_pdf is None:
            raise ValueError("No PDF loaded. Use load_pdf() first.")
        
        info = {
            'num_pages': len(self.current_pdf.pages),
            'file_path': self.current_path,
        }
        
        # Try to get metadata
        try:
            metadata = self.current_pdf.metadata
            if metadata:
                info.update({
                    'title': metadata.get('/Title', 'N/A'),
                    'author': metadata.get('/Author', 'N/A'),
                    'subject': metadata.get('/Subject', 'N/A'),
                    'creator': metadata.get('/Creator', 'N/A'),
                    'producer': metadata.get('/Producer', 'N/A'),
                    'creation_date': metadata.get('/CreationDate', 'N/A'),
                    'modification_date': metadata.get('/ModDate', 'N/A'),
                })
        except Exception as e:
            info['metadata_error'] = str(e)
        
        return info
    
    def extract_text(self, page_numbers: Optional[List[int]] = None) -> str:
        """Extract text from PDF pages."""
        if self.current_pdf is None:
            raise ValueError("No PDF loaded. Use load_pdf() first.")
        
        if page_numbers is None:
            page_numbers = list(range(len(self.current_pdf.pages)))
        
        extracted_text = []
        
        for page_num in page_numbers:
            if 0 <= page_num < len(self.current_pdf.pages):
                page = self.current_pdf.pages[page_num]
                try:
                    text = page.extract_text()
                    extracted_text.append(f"--- Page {page_num + 1} ---\n{text}\n")
                except Exception as e:
                    extracted_text.append(f"--- Page {page_num + 1} (Error) ---\nError extracting text: {str(e)}\n")
            else:
                extracted_text.append(f"--- Page {page_num + 1} (Invalid) ---\nPage number out of range\n")
        
        return "\n".join(extracted_text)
    
    def extract_text_by_page(self) -> Dict[int, str]:
        """Extract text from all pages, returning a dictionary with page numbers as keys."""
        if self.current_pdf is None:
            raise ValueError("No PDF loaded. Use load_pdf() first.")
        
        text_by_page = {}
        
        for i, page in enumerate(self.current_pdf.pages):
            try:
                text = page.extract_text()
                text_by_page[i + 1] = text
            except Exception as e:
                text_by_page[i + 1] = f"Error extracting text: {str(e)}"
        
        return text_by_page
    
    def search_text(self, pattern: str, case_sensitive: bool = False) -> List[Dict[str, Any]]:
        """Search for text patterns in the PDF."""
        if self.current_pdf is None:
            raise ValueError("No PDF loaded. Use load_pdf() first.")
        
        flags = 0 if case_sensitive else re.IGNORECASE
        results = []
        
        for i, page in enumerate(self.current_pdf.pages):
            try:
                text = page.extract_text()
                matches = re.finditer(pattern, text, flags)
                
                for match in matches:
                    results.append({
                        'page': i + 1,
                        'match': match.group(),
                        'start': match.start(),
                        'end': match.end(),
                        'context': self._get_context(text, match.start(), match.end())
                    })
            except Exception as e:
                results.append({
                    'page': i + 1,
                    'error': f"Error searching page: {str(e)}"
                })
        
        return results
    
    def save_text_to_file(self, output_path: str, page_numbers: Optional[List[int]] = None) -> str:
        """Extract text and save to a text file. Automatically uses 'results' folder."""
        text = self.extract_text(page_numbers)
        
        # Ensure the file path starts with 'results/'
        if not output_path.startswith('results/'):
            output_path = f"results/{output_path}"
        
        # Ensure output directory exists
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text)
        
        return output_path
    
    def _get_context(self, text: str, start: int, end: int, context_length: int = 50) -> str:
        """Get context around a match."""
        context_start = max(0, start - context_length)
        context_end = min(len(text), end + context_length)
        
        context = text[context_start:context_end]
        
        # Add ellipsis if we truncated
        if context_start > 0:
            context = "..." + context
        if context_end < len(text):
            context = context + "..."
        
        return context
