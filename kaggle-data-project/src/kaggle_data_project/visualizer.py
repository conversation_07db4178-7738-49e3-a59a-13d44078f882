"""
Data visualization utilities using matplotlib.
All plots are saved to the 'results' folder.
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, List, Tuple, Dict, Any


class Visualizer:
    """A class for creating various data visualizations."""
    
    def __init__(self, style: str = 'default', figsize: Tuple[int, int] = (10, 6)):
        """Initialize the Visualizer."""
        plt.style.use(style)
        self.figsize = figsize
        self.current_fig = None
        self.current_ax = None
    
    def line_plot(self, 
                  data: pd.DataFrame, 
                  x: str, 
                  y: str, 
                  title: str = "Line Plot",
                  xlabel: Optional[str] = None,
                  ylabel: Optional[str] = None,
                  save_path: Optional[str] = None) -> plt.Figure:
        """Create a line plot."""
        fig, ax = plt.subplots(figsize=self.figsize)
        
        ax.plot(data[x], data[y], marker='o', linewidth=2, markersize=4)
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel(xlabel or x, fontsize=12)
        ax.set_ylabel(ylabel or y, fontsize=12)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            self._save_plot(fig, save_path)
        
        self.current_fig = fig
        self.current_ax = ax
        return fig
    
    def scatter_plot(self,
                     data: pd.DataFrame,
                     x: str,
                     y: str,
                     color: Optional[str] = None,
                     size: Optional[str] = None,
                     title: str = "Scatter Plot",
                     xlabel: Optional[str] = None,
                     ylabel: Optional[str] = None,
                     save_path: Optional[str] = None) -> plt.Figure:
        """Create a scatter plot."""
        fig, ax = plt.subplots(figsize=self.figsize)
        
        scatter_kwargs = {'alpha': 0.7}
        
        if color and color in data.columns:
            scatter_kwargs['c'] = data[color]
            scatter_kwargs['cmap'] = 'viridis'
        
        if size and size in data.columns:
            scatter_kwargs['s'] = data[size] * 10  # Scale for visibility
        
        scatter = ax.scatter(data[x], data[y], **scatter_kwargs)
        
        if color and color in data.columns:
            plt.colorbar(scatter, ax=ax, label=color)
        
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel(xlabel or x, fontsize=12)
        ax.set_ylabel(ylabel or y, fontsize=12)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            self._save_plot(fig, save_path)
        
        self.current_fig = fig
        self.current_ax = ax
        return fig
    
    def histogram(self,
                  data: pd.DataFrame,
                  column: str,
                  bins: int = 30,
                  title: str = "Histogram",
                  xlabel: Optional[str] = None,
                  ylabel: str = "Frequency",
                  save_path: Optional[str] = None) -> plt.Figure:
        """Create a histogram."""
        fig, ax = plt.subplots(figsize=self.figsize)
        
        ax.hist(data[column], bins=bins, alpha=0.7, color='skyblue', edgecolor='black')
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel(xlabel or column, fontsize=12)
        ax.set_ylabel(ylabel, fontsize=12)
        ax.grid(True, alpha=0.3, axis='y')
        
        # Add statistics text
        mean_val = data[column].mean()
        ax.axvline(mean_val, color='red', linestyle='--', alpha=0.8, label=f'Mean: {mean_val:.2f}')
        ax.legend()
        
        plt.tight_layout()
        
        if save_path:
            self._save_plot(fig, save_path)
        
        self.current_fig = fig
        self.current_ax = ax
        return fig
    
    def correlation_heatmap(self,
                           data: pd.DataFrame,
                           title: str = "Correlation Heatmap",
                           save_path: Optional[str] = None) -> plt.Figure:
        """Create a correlation heatmap."""
        # Select only numeric columns
        numeric_data = data.select_dtypes(include=[np.number])
        
        if numeric_data.empty:
            raise ValueError("No numeric columns found for correlation analysis")
        
        fig, ax = plt.subplots(figsize=self.figsize)
        
        correlation_matrix = numeric_data.corr()
        
        im = ax.imshow(correlation_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
        
        # Add colorbar
        plt.colorbar(im, ax=ax, label='Correlation')
        
        # Set ticks and labels
        ax.set_xticks(range(len(correlation_matrix.columns)))
        ax.set_yticks(range(len(correlation_matrix.columns)))
        ax.set_xticklabels(correlation_matrix.columns, rotation=45, ha='right')
        ax.set_yticklabels(correlation_matrix.columns)
        
        # Add correlation values as text
        for i in range(len(correlation_matrix.columns)):
            for j in range(len(correlation_matrix.columns)):
                text = ax.text(j, i, f'{correlation_matrix.iloc[i, j]:.2f}',
                             ha="center", va="center", color="black", fontsize=8)
        
        ax.set_title(title, fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            self._save_plot(fig, save_path)
        
        self.current_fig = fig
        self.current_ax = ax
        return fig
    
    def _save_plot(self, fig: plt.Figure, save_path: str, dpi: int = 300) -> None:
        """Save a plot to file. Automatically uses 'results' folder."""
        # Ensure the file path starts with 'results/'
        if not save_path.startswith('results/'):
            save_path = f"results/{save_path}"
            
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        fig.savefig(save_path, dpi=dpi, bbox_inches='tight')
    
    def show(self) -> None:
        """Show the current plot."""
        if self.current_fig:
            plt.show()
    
    def close(self) -> None:
        """Close the current plot."""
        if self.current_fig:
            plt.close(self.current_fig)
            self.current_fig = None
            self.current_ax = None
