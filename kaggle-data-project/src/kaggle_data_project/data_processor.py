"""
Data processing utilities using pandas and numpy.
All output files are saved to the 'results' folder.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, Dict, Any, List


class DataProcessor:
    """A class for common data processing operations."""
    
    def __init__(self):
        self.data: Optional[pd.DataFrame] = None
    
    def load_csv(self, file_path: str, **kwargs) -> pd.DataFrame:
        """Load data from a CSV file."""
        self.data = pd.read_csv(file_path, **kwargs)
        return self.data
    
    def load_excel(self, file_path: str, **kwargs) -> pd.DataFrame:
        """Load data from an Excel file."""
        self.data = pd.read_excel(file_path, **kwargs)
        return self.data
    
    def basic_info(self) -> Dict[str, Any]:
        """Get basic information about the loaded data."""
        if self.data is None:
            raise ValueError("No data loaded. Use load_csv() or load_excel() first.")
        
        return {
            'shape': self.data.shape,
            'columns': list(self.data.columns),
            'dtypes': self.data.dtypes.to_dict(),
            'missing_values': self.data.isnull().sum().to_dict(),
            'memory_usage': self.data.memory_usage(deep=True).sum()
        }
    
    def clean_data(self, 
                   drop_duplicates: bool = True,
                   fill_na_method: str = 'drop',
                   fill_na_value: Any = None) -> pd.DataFrame:
        """Clean the loaded data."""
        if self.data is None:
            raise ValueError("No data loaded. Use load_csv() or load_excel() first.")
        
        cleaned_data = self.data.copy()
        
        if drop_duplicates:
            cleaned_data = cleaned_data.drop_duplicates()
        
        if fill_na_method == 'drop':
            cleaned_data = cleaned_data.dropna()
        elif fill_na_method == 'fill':
            cleaned_data = cleaned_data.fillna(fill_na_value)
        elif fill_na_method == 'forward':
            cleaned_data = cleaned_data.fillna(method='ffill')
        elif fill_na_method == 'backward':
            cleaned_data = cleaned_data.fillna(method='bfill')
        
        self.data = cleaned_data
        return cleaned_data
    
    def get_numerical_summary(self) -> pd.DataFrame:
        """Get summary statistics for numerical columns."""
        if self.data is None:
            raise ValueError("No data loaded. Use load_csv() or load_excel() first.")
        
        return self.data.describe()
    
    def get_categorical_summary(self) -> Dict[str, pd.Series]:
        """Get summary for categorical columns."""
        if self.data is None:
            raise ValueError("No data loaded. Use load_csv() or load_excel() first.")
        
        categorical_cols = self.data.select_dtypes(include=['object', 'category']).columns
        return {col: self.data[col].value_counts() for col in categorical_cols}
    
    def filter_data(self, conditions: Dict[str, Any]) -> pd.DataFrame:
        """Filter data based on conditions."""
        if self.data is None:
            raise ValueError("No data loaded. Use load_csv() or load_excel() first.")
        
        filtered_data = self.data.copy()
        
        for column, condition in conditions.items():
            if column not in filtered_data.columns:
                raise ValueError(f"Column '{column}' not found in data")
            
            if isinstance(condition, (list, tuple)):
                filtered_data = filtered_data[filtered_data[column].isin(condition)]
            elif isinstance(condition, dict):
                if 'min' in condition:
                    filtered_data = filtered_data[filtered_data[column] >= condition['min']]
                if 'max' in condition:
                    filtered_data = filtered_data[filtered_data[column] <= condition['max']]
            else:
                filtered_data = filtered_data[filtered_data[column] == condition]
        
        return filtered_data
    
    def save_data(self, file_path: str, format: str = 'csv', **kwargs) -> None:
        """Save the current data to a file. Automatically uses 'results' folder."""
        if self.data is None:
            raise ValueError("No data loaded. Use load_csv() or load_excel() first.")
        
        # Ensure the file path starts with 'results/'
        if not file_path.startswith('results/'):
            file_path = f"results/{file_path}"
        
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        
        if format.lower() == 'csv':
            self.data.to_csv(file_path, index=False, **kwargs)
        elif format.lower() == 'excel':
            self.data.to_excel(file_path, index=False, **kwargs)
        elif format.lower() == 'json':
            self.data.to_json(file_path, **kwargs)
        elif format.lower() == 'parquet':
            self.data.to_parquet(file_path, **kwargs)
        else:
            raise ValueError(f"Unsupported format: {format}")
