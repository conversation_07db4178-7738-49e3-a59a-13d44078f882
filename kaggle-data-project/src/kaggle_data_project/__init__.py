"""
Kaggle Data Project - A comprehensive data analysis toolkit.

This package provides utilities for data analysis using numpy, pandas, 
matplotlib, and pypdf. All output files are saved to the 'results' folder.
"""

__version__ = "0.1.0"
__author__ = "Your Name"

from .data_processor import DataProcessor
from .visualizer import Visualizer
from .pdf_handler import PDFHandler

__all__ = ["DataProcessor", "Visualizer", "PDFHandler"]
