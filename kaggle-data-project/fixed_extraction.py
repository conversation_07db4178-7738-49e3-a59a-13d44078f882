"""
Script corrigido para extrair dataset_ids dos PDFs lidando com espaços inseridos.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]
    
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
            
            # Adicionar versão com doi: prefix
            variations.append(f"doi:{dataset_id}")
            variations.append(f"doi: {dataset_id}")
            
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
    
    return variations


def create_flexible_pattern(target):
    """Cria um padrão regex flexível que permite espaços entre caracteres."""
    # Extrair a parte final do identificador (após o último /)
    if '/' in target:
        identifier = target.split('/')[-1]
    else:
        identifier = target
    
    # Criar padrão que permite espaços opcionais entre grupos de caracteres
    # Por exemplo: "zw3r22854" vira "zw3r2\s*2854" ou similar
    
    patterns = []
    
    # Padrão 1: Permitir espaços entre qualquer caractere
    flexible_chars = '\\s*'.join(list(identifier))
    patterns.append(flexible_chars)
    
    # Padrão 2: Para identificadores como "zw3r22854", permitir espaço antes dos últimos dígitos
    if re.match(r'^[a-z]+\d+$', identifier):
        # Separar letras e números
        match = re.match(r'^([a-z]+)(\d+)$', identifier)
        if match:
            letters, numbers = match.groups()
            # Permitir espaço entre letras e números, e dentro dos números
            pattern = letters + r'\s*' + '\\s*'.join(list(numbers))
            patterns.append(pattern)
    
    # Padrão 3: Para identificadores como "37pvmcvgb", permitir espaços em pontos lógicos
    if len(identifier) > 6:
        # Dividir em grupos de 2-4 caracteres
        groups = []
        i = 0
        while i < len(identifier):
            if i + 4 <= len(identifier):
                groups.append(identifier[i:i+4])
                i += 4
            elif i + 2 <= len(identifier):
                groups.append(identifier[i:i+2])
                i += 2
            else:
                groups.append(identifier[i:])
                i = len(identifier)
        
        pattern = r'\s*'.join(groups)
        patterns.append(pattern)
    
    return patterns


def extract_preceding_words_flexible(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto com busca flexível."""
    try:
        # Primeiro, tentar busca exata
        escaped_target = re.escape(target)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
        
        # Se não encontrou, tentar busca flexível
        flexible_patterns = create_flexible_pattern(target)
        
        for pattern in flexible_patterns:
            regex_pattern = rf'(.+?){pattern}'
            match = re.search(regex_pattern, text, re.IGNORECASE | re.DOTALL)
            
            if match:
                preceding_text = match.group(1)
                words = re.findall(r'\b\w+\b', preceding_text)
                if len(words) >= num_words:
                    return f"FLEXIBLE_MATCH: {' '.join(words[-num_words:])}"
                else:
                    return f"FLEXIBLE_MATCH: {' '.join(words)}"
        
        # Busca ainda mais agressiva - procurar por partes do identificador
        if "dryad" in target.lower():
            # Procurar por "dryad" seguido de qualquer coisa que pareça com o identificador
            identifier = target.split('/')[-1] if '/' in target else target
            
            # Remover caracteres especiais e criar busca por proximidade
            clean_id = re.sub(r'[^\w\d]', '', identifier)
            
            # Procurar "dryad" seguido de algo parecido
            dryad_pattern = rf'(.+?)dryad[^\n]*{clean_id[:6]}'
            match = re.search(dryad_pattern, text, re.IGNORECASE | re.DOTALL)
            
            if match:
                preceding_text = match.group(1)
                words = re.findall(r'\b\w+\b', preceding_text)
                if len(words) >= num_words:
                    return f"PROXIMITY_MATCH: {' '.join(words[-num_words:])}"
                else:
                    return f"PROXIMITY_MATCH: {' '.join(words)}"
                    
    except Exception as e:
        print(f"Erro na extração: {e}")
    
    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias do texto."""
    try:
        words = re.findall(r'\b\w+\b', text)
        
        if len(words) < num_words:
            return ' '.join(words)
        
        start_pos = random.randint(0, len(words) - num_words)
        return ' '.join(words[start_pos:start_pos + num_words])
    except Exception:
        return "ERROR_EXTRACTING_RANDOM_WORDS"


def process_pdf_fixed(article_id, dataset_id, data_dir="data"):
    """Processa um PDF individual com busca flexível."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"
    
    if not pdf_path.exists():
        return article_id, dataset_id, "PDF_NOT_FOUND"
    
    try:
        reader = PdfReader(pdf_path)
        
        # Extrair texto de todas as páginas
        full_text = ""
        for page in reader.pages:
            try:
                page_text = page.extract_text()
                full_text += page_text + "\n"
            except Exception:
                continue
        
        if dataset_id == "Missing":
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Tentar encontrar o dataset_id e suas variações
        variations = normalize_dataset_id(dataset_id)
        
        for variation in variations:
            preceding_words = extract_preceding_words_flexible(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)[:50]}"


def test_fixed_cases():
    """Testa os casos específicos com a versão corrigida."""
    test_cases = [
        {
            'article_id': '10.1002_ece3.6144',
            'dataset_id': 'https://doi.org/10.5061/dryad.zw3r22854'
        },
        {
            'article_id': '10.1002_ece3.6303', 
            'dataset_id': 'https://doi.org/10.5061/dryad.37pvmcvgb'
        }
    ]
    
    print("=== Teste dos Casos Corrigidos ===\n")
    
    for i, case in enumerate(test_cases, 1):
        article_id = case['article_id']
        dataset_id = case['dataset_id']
        
        print(f"Teste {i}: {article_id}")
        print(f"Dataset ID: {dataset_id}")
        
        result = process_pdf_fixed(article_id, dataset_id)
        
        print(f"Resultado: {result[2]}")
        print("-" * 60)


def main():
    """Função principal."""
    print("=== Extração Corrigida de Dataset IDs ===")
    print("Versão com busca flexível para lidar com espaços inseridos.\n")
    
    # Primeiro, testar os casos específicos
    test_fixed_cases()
    
    # Perguntar se quer processar todos
    response = input("\nProcessar todos os arquivos com a versão corrigida? (s/n): ").strip().lower()
    
    if response not in ['s', 'sim', 'y', 'yes']:
        print("Processamento cancelado.")
        return
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    
    print(f"Processando {len(labels_df)} arquivos...")
    
    results = []
    
    for idx, row in labels_df.iterrows():
        article_id = row['article_id']
        dataset_id = row['dataset_id']
        
        if (idx + 1) % 50 == 0:
            print(f"Processando {idx + 1}/{len(labels_df)}: {article_id}")
        
        result = process_pdf_fixed(article_id, dataset_id)
        results.append(result)
        
        # Salvar progresso a cada 100 arquivos
        if (idx + 1) % 100 == 0:
            temp_df = pd.DataFrame(results, columns=[
                'article_id', 'dataset_id', 'preceding_words'
            ])
            
            results_dir = Path("results")
            results_dir.mkdir(exist_ok=True)
            
            temp_path = results_dir / f"fixed_progress_{idx + 1}.csv"
            temp_df.to_csv(temp_path, index=False, encoding='utf-8')
            print(f"Progresso salvo: {temp_path}")
    
    # Salvar resultados finais
    results_df = pd.DataFrame(results, columns=[
        'article_id', 'dataset_id', 'preceding_words'
    ])
    
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    output_path = results_dir / "fixed_extraction_results.csv"
    results_df.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados salvos em: {output_path}")
    
    # Estatísticas
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    flexible = len(results_df[results_df['preceding_words'].str.contains('FLEXIBLE_MATCH', na=False)])
    proximity = len(results_df[results_df['preceding_words'].str.contains('PROXIMITY_MATCH', na=False)])
    
    print(f"\n📊 Estatísticas:")
    print(f"Total: {total}")
    print(f"Matches exatos: {found - missing - flexible - proximity}")
    print(f"Matches flexíveis: {flexible}")
    print(f"Matches por proximidade: {proximity}")
    print(f"Casos Missing: {missing}")
    print(f"Não encontrados: {total - found}")
    
    # Taxa de sucesso melhorada
    non_missing = total - missing
    if non_missing > 0:
        success_rate = ((found - missing) / non_missing) * 100
        print(f"Taxa de sucesso total: {success_rate:.1f}%")
    
    print("\n🎉 Processamento corrigido concluído!")


if __name__ == "__main__":
    main()
