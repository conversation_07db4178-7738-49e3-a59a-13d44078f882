"""
Script de teste para verificar a extração de dataset_ids de PDFs.
Testa com alguns exemplos específicos antes de processar todos os arquivos.
"""

import sys
from pathlib import Path
import pandas as pd
import re

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from kaggle_data_project import PDFHandler


def test_specific_examples():
    """Testa com alguns exemplos específicos do train_labels.csv."""
    
    # Exemplos para teste (baseados no arquivo train_labels.csv)
    test_cases = [
        {
            'article_id': '10.1002_2017jc013030',
            'dataset_id': 'https://doi.org/10.17882/49388',
            'expected_variations': [
                'https://doi.org/10.17882/49388',
                'doi.org/10.17882/49388',
                '10.17882/49388'
            ]
        },
        {
            'article_id': '10.1002_ece3.4466',
            'dataset_id': 'https://doi.org/10.5061/dryad.r6nq870',
            'expected_variations': [
                'https://doi.org/10.5061/dryad.r6nq870',
                'doi.org/10.5061/dryad.r6nq870',
                '10.5061/dryad.r6nq870'
            ]
        },
        {
            'article_id': '10.1002_anie.201916483',
            'dataset_id': 'Missing',
            'expected_variations': []
        }
    ]
    
    pdf_handler = PDFHandler()
    data_dir = Path("data")
    
    print("=== Teste de Extração de Dataset IDs ===\n")
    
    for i, test_case in enumerate(test_cases, 1):
        article_id = test_case['article_id']
        dataset_id = test_case['dataset_id']
        
        print(f"Teste {i}: {article_id}")
        print(f"Dataset ID: {dataset_id}")
        
        # Caminho do PDF
        pdf_path = data_dir / "train" / "PDF" / f"{article_id}.pdf"
        
        if not pdf_path.exists():
            print(f"❌ PDF não encontrado: {pdf_path}\n")
            continue
        
        try:
            # Carregar PDF
            pdf_handler.load_pdf(str(pdf_path))
            
            # Extrair texto
            full_text = pdf_handler.extract_text()
            
            print(f"✅ PDF carregado com sucesso")
            print(f"📄 Texto extraído: {len(full_text)} caracteres")
            
            # Mostrar primeiras 200 caracteres do texto
            preview = full_text[:200].replace('\n', ' ').strip()
            print(f"📖 Preview: {preview}...")
            
            if dataset_id == "Missing":
                # Para casos Missing, extrair 10 palavras aleatórias
                words = re.findall(r'\b\w+\b', full_text)
                if len(words) >= 10:
                    sample_words = ' '.join(words[50:60])  # Pegar palavras do meio
                    print(f"🎲 Amostra de 10 palavras: {sample_words}")
                else:
                    print(f"⚠️  Texto muito curto: apenas {len(words)} palavras")
            else:
                # Testar variações do dataset_id
                variations = test_case['expected_variations']
                found_any = False
                
                for variation in variations:
                    # Procurar a variação no texto
                    escaped_variation = re.escape(variation)
                    pattern = rf'(.+?)\b{escaped_variation}\b'
                    match = re.search(pattern, full_text, re.IGNORECASE | re.DOTALL)
                    
                    if match:
                        preceding_text = match.group(1)
                        words = re.findall(r'\b\w+\b', preceding_text)
                        
                        if len(words) >= 10:
                            preceding_words = ' '.join(words[-10:])
                        else:
                            preceding_words = ' '.join(words)
                        
                        print(f"✅ Encontrado '{variation}'")
                        print(f"📝 10 palavras anteriores: {preceding_words}")
                        found_any = True
                        break
                
                if not found_any:
                    print(f"❌ Nenhuma variação do dataset_id encontrada no texto")
                    
                    # Verificar se pelo menos parte do ID aparece
                    if "10.17882" in full_text or "10.5061" in full_text:
                        print(f"🔍 Parte do ID encontrada no texto")
                    else:
                        print(f"🔍 Nenhuma parte do ID encontrada")
            
        except Exception as e:
            print(f"❌ Erro ao processar PDF: {e}")
        
        print("-" * 60)


def analyze_train_labels():
    """Analisa o arquivo train_labels.csv para entender os padrões."""
    
    print("=== Análise do train_labels.csv ===\n")
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    
    print(f"Total de entradas: {len(labels_df)}")
    print(f"Artigos únicos: {labels_df['article_id'].nunique()}")
    print(f"Dataset IDs únicos: {labels_df['dataset_id'].nunique()}")
    
    # Contar casos Missing
    missing_count = len(labels_df[labels_df['dataset_id'] == 'Missing'])
    print(f"Casos 'Missing': {missing_count}")
    print(f"Casos com dataset_id: {len(labels_df) - missing_count}")
    
    # Analisar tipos de dataset_id
    non_missing = labels_df[labels_df['dataset_id'] != 'Missing']['dataset_id']
    
    https_count = len(non_missing[non_missing.str.startswith('https://')])
    doi_count = len(non_missing[non_missing.str.contains('doi.org')])
    other_count = len(non_missing) - https_count
    
    print(f"\nTipos de dataset_id:")
    print(f"  - Começam com https://: {https_count}")
    print(f"  - Contêm doi.org: {doi_count}")
    print(f"  - Outros formatos: {other_count}")
    
    # Mostrar alguns exemplos
    print(f"\nExemplos de dataset_ids:")
    examples = non_missing.head(10).tolist()
    for example in examples:
        print(f"  - {example}")
    
    print("\n" + "=" * 60)


def main():
    """Função principal do teste."""
    
    # Primeiro, analisar os dados
    analyze_train_labels()
    
    # Depois, testar extração com exemplos específicos
    test_specific_examples()
    
    print("\n🎯 Teste concluído!")
    print("Se os testes estão funcionando bem, execute o script principal:")
    print("uv run python pdf_dataset_extractor.py")


if __name__ == "__main__":
    main()
