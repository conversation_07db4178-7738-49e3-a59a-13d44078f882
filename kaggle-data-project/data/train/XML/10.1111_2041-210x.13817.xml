<?xml version="1.0" encoding="UTF-8"?><component xmlns="http://www.wiley.com/namespaces/wiley" xmlns:wiley="http://www.wiley.com/namespaces/wiley/wiley" type="serialArticle" version="2.0" xml:id="mee313817" xml:lang="en"><header><publicationMeta level="product"><doi origin="wiley" registered="yes">10.1111/(ISSN)2041-210X</doi><issn type="print">2041-210X</issn><issn type="electronic">2041-210X</issn><idGroup><id type="product" value="MEE3"/></idGroup><titleGroup><title sort="METHODS IN ECOLOGY AND EVOLUTION" type="main">Methods in Ecology and Evolution</title><title type="short">Methods Ecol Evol</title></titleGroup></publicationMeta><publicationMeta level="part" position="50"><doi origin="wiley">10.1111/mee3.v13.5</doi><copyright ownership="publisher"><i>Methods in Ecology and Evolution</i> © 2022 British Ecological Society</copyright><numberingGroup><numbering number="13" type="journalVolume">13</numbering><numbering type="journalIssue">5</numbering></numberingGroup><coverDate startDate="2022-05">May 2022</coverDate></publicationMeta><publicationMeta level="unit" position="120" status="forIssue" type="article"><doi>10.1111/2041-210X.13817</doi><idGroup><id type="unit" value="MEE313817"/><id type="society" value="MEE-21-09-700.R1"/></idGroup><countGroup><count number="10" type="pageTotal"/></countGroup><titleGroup><title type="articleCategory">RESEARCH ARTICLE</title><title type="tocHeading1">RESEARCH ARTICLES</title></titleGroup><copyright ownership="thirdParty">© 2022 British Ecological Society.</copyright><eventGroup><event type="publishedOnlineAccepted" date="2022-02-08"/><event type="firstOnline" date="2022-02-28"/><event type="publishedOnlineFinalForm" date="2022-05-04"/><event type="xmlCreated" date="2022-02-10" agent="SPS"/><event type="manuscriptReceived" date="2021-09-03"/><event type="publishedOnlineEarlyUnpaginated" date="2022-02-28"/><event type="manuscriptAccepted" date="2022-01-27"/><event type="xmlConverted" agent="Converter:WML3G_To_WML3G version:6.1.7 mode:FullText" date="2022-06-18"/></eventGroup><numberingGroup><numbering type="pageFirst">1042</numbering><numbering type="pageLast">1051</numbering></numberingGroup><correspondenceTo><lineatedText><line><b>Correspondence</b></line><line>Hayri Önal</line><line>Email: <email normalForm="<EMAIL>">h‐<EMAIL></email></line></lineatedText></correspondenceTo><subjectInfo><subject href="http://entities.wiley.com/objects/BESED11">Conservation ecology</subject><subject href="http://entities.wiley.com/objects/BESED34">Landscape ecology</subject><subject href="http://entities.wiley.com/objects/BESED53">Spatial ecology</subject></subjectInfo><selfCitationGroup xml:id="MEE313817-selfc-2010"><citation type="self" xml:id="MEE313817-cit-2001"><author><familyName>Wang</familyName>, <givenNames>Y.</givenNames></author>, <author><familyName>Qin</familyName>, <givenNames>P.</givenNames></author> &amp; <author><familyName>Önal</familyName>, <givenNames>H.</givenNames></author> (<pubYear year="2022">2022</pubYear>). <articleTitle>An optimisation approach for designing wildlife corridors with ecological and spatial considerations</articleTitle>. <journalTitle>Methods in Ecology and Evolution</journalTitle>, <vol>13</vol>, <pageFirst>1042</pageFirst>–<pageLast>1051</pageLast>. <url href="https://doi.org/10.1111/2041-210X.13817">https://doi.org/10.1111/2041‐210X.13817</url></citation></selfCitationGroup><objectNameGroup><objectName elementName="tabular">TABLE</objectName><objectName elementName="figure">FIGURE</objectName></objectNameGroup><attributeValueGroup><attributeValue attribute="type" element="note" value="handlingEditor"/></attributeValueGroup><linkGroup><link type="toTypesetVersion" href="file:2041-210X.13817.pdf"/><link type="toAuthorManuscriptVersion" href="file:2041-210X.13817.am.pdf"/></linkGroup></publicationMeta><contentMeta><countGroup><count number="3" type="figureTotal"/><count number="2" type="tableTotal"/><count number="7029" type="wordTotal"/></countGroup><titleGroup><title type="main">An optimisation approach for designing wildlife corridors with ecological and spatial considerations</title><title type="shortAuthors">WANG et al.</title></titleGroup><creators><creator affiliationRef="#mee313817-aff-0001" creatorRole="author" xml:id="mee313817-cr-0001"><idGroup><id type="orcid" value="https://orcid.org/0000-0002-2308-0774"/></idGroup><personName><givenNames>Yicheng</givenNames><familyName>Wang</familyName></personName></creator><creator affiliationRef="#mee313817-aff-0001" creatorRole="author" xml:id="mee313817-cr-0002"><idGroup><id type="orcid" value="https://orcid.org/0000-0002-4388-6564"/></idGroup><personName><givenNames>Peng</givenNames><familyName>Qin</familyName></personName></creator><creator affiliationRef="#mee313817-aff-0002" corresponding="yes" creatorRole="author" xml:id="mee313817-cr-0003"><idGroup><id type="orcid" value="https://orcid.org/0000-0002-0307-1731"/></idGroup><personName><givenNames>Hayri</givenNames><familyName>Önal</familyName></personName><contactDetails><email normalForm="<EMAIL>">h‐<EMAIL></email></contactDetails></creator></creators><affiliationGroup><affiliation countryCode="CN" type="organization" xml:id="mee313817-aff-0001"><orgDiv>College of Resources and Environment</orgDiv><orgName>Qingdao Agricultural University</orgName><address><city>Qingdao</city><country>China</country></address></affiliation><affiliation countryCode="US" type="organization" xml:id="mee313817-aff-0002"><orgDiv>Department of Agricultural and Consumer Economics</orgDiv><orgName>University of Illinois</orgName><address><city>Urban‐Champaign</city><countryPart>IL</countryPart><country>USA</country></address></affiliation></affiliationGroup><keywordGroup type="author" xml:lang="en"><keyword xml:id="mee313817-kwd-0001">biological conservation</keyword><keyword xml:id="mee313817-kwd-0002">connectivity</keyword><keyword xml:id="mee313817-kwd-0003">conservation planning</keyword><keyword xml:id="mee313817-kwd-0004">mixed‐integer linear programming</keyword><keyword xml:id="mee313817-kwd-0005">spatial optimisation</keyword><keyword xml:id="mee313817-kwd-0006">wildlife corridor</keyword></keywordGroup><fundingInfo><fundingAgency>Illinois CREES Project</fundingAgency><fundingNumber>ILLU 05‐0361</fundingNumber></fundingInfo><fundingInfo><fundingAgency fundRefName="Natural Science Foundation of Shandong Province" funderDoi="10.13039/501100007129">Natural Science Foundation of Shandong Province</fundingAgency><fundingNumber>ZR2019MG011</fundingNumber></fundingInfo><fundingInfo><fundingAgency fundRefName="National Natural Science Foundation of China" funderDoi="10.13039/501100001809">National Natural Science Foundation of China</fundingAgency><fundingNumber>72172144</fundingNumber></fundingInfo><abstractGroup><abstract type="main" xml:lang="en"><title type="main">Abstract</title><p><list style="1" xml:id="mee313817-list-0001"><listItem xml:id="mee313817-li-0001">The fragmentation of wildlife habitats caused by anthropogenic activities has reduced biodiversity and impaired key ecosystem functions. Wildlife corridors play an important role in linking detached habitats. The optimal design of such corridors considering spatial, ecological and economic factors is addressed in this paper.</listItem><listItem xml:id="mee313817-li-0002">We present a novel graph‐theoretic optimisation approach and a mixed‐integer linear programming model to determine an optimal wildlife corridor connecting two given habitat patches. The model maximises the total quality of the corridor and satisfies pre‐specified corridor width and length requirements under a resource constraint.</listItem><listItem xml:id="mee313817-li-0003">Compared to the corridor design models presented in the literature, our model is conceptually simpler, and it is computationally convenient. We applied the model to a real dataset for Eldorado National Forest in California, USA, involving 1,363 irregular land parcels.</listItem><listItem xml:id="mee313817-li-0004">The model can be extended to design multiple corridors that connect two or more existing habitat patches.</listItem></list></p></abstract></abstractGroup></contentMeta><noteGroup xml:id="mee313817-ntgp-0001"><note numbered="no" type="handlingEditor" xml:id="mee313817-note-0001"><p><b>Handling Editor</b> Chris Sutherland</p></note></noteGroup></header><body sectionsNumbered="yes" xml:id="mee313817-body-0001"><section xml:id="mee313817-sec-0001"><title type="main">INTRODUCTION</title><p>The fragmentation of wildlife habitats caused by anthropogenic activities has reduced biodiversity and impaired key ecosystem functions (Díaz et al., <link href="#mee313817-bib-0011">2020</link>; Gibson et al., <link href="#mee313817-bib-0018">2013</link>; Haddad et al., <link href="#mee313817-bib-0021">2015</link>). Current systems of protected areas are not only insufficient to sustain biodiversity, but they also lack habitat connectivity. As of 2020, one third of the key biodiversity areas on the earth lack any coverage and &lt;8% of the land is both protected and connected (UNEP, <link href="#mee313817-bib-0038">2021</link>). Connectivity (spatial and/or functional) of protected areas is a crucial factor that facilitates species movements, gene flows, range shifts and metapopulation interactions between those habitats (Beier et al., <link href="#mee313817-bib-0004">2009</link>; Bolger et al., <link href="#mee313817-bib-0007">2001</link>). Wildlife corridors play an important role in promoting habitat connectivity by linking detached habitats patches. The optimal design of such wildlife corridors considering spatial, ecological and economic factors is the issue that we address in this paper.</p><p>Many factors may be taken into account to ensure a functional wildlife corridor. Spatial attributes of the corridor are among them. Two critical spatial attributes are the width and length of the corridor. A wildlife corridor must be wide enough since a narrow corridor is unlikely to provide adequate amenities such as food and refuge (Andreassen et al., <link href="#mee313817-bib-0003">1996</link>; Coster et al., <link href="#mee313817-bib-0010">2014</link>). A wildlife corridor must also be short enough to reduce movement resistance (Haddad, <link href="#mee313817-bib-0019">1999</link>; Haddad et al., <link href="#mee313817-bib-0020">2003</link>; Heintzman &amp; McIntyre, <link href="#mee313817-bib-0023">2021</link>) and avoid possible intervention from outside of the corridor (Haddad et al., <link href="#mee313817-bib-0022">2014</link>). A short corridor is also cost‐effective to establish and maintain. In addition to these spatial attributes, corridor quality is another critical aspect. A corridor with a high habitat quality presents lower resistance to traversal (Hodgson et al., <link href="#mee313817-bib-0025">2011</link>; Sawyer et al., <link href="#mee313817-bib-0035">2011</link>). Lastly, financial factors can be important determinants of the location and shape of the corridor.</p><p>The problem of identifying wildlife corridors has been studied using various methods, including heuristic algorithms (e.g. Alagador et al., <link href="#mee313817-bib-0002">2012</link>), geographic information systems (e.g. Matisziw et al., <link href="#mee313817-bib-0027">2015</link>) and graph theory, and its extensions including least‐cost modelling (e.g. Vanthomme et al., <link href="#mee313817-bib-0039">2019</link>), circuit theory (e.g. Koen et al., <link href="#mee313817-bib-0026">2014</link>) and centrality analyses (Estrada &amp; Bodin, <link href="#mee313817-bib-0013">2008</link>). Several conservation planning software, such as C‐Plan, Zonation, Marxan (see Sarkar (<link href="#mee313817-bib-0031">2012</link>) for a review) and MulTyLink (Brás et al., <link href="#mee313817-bib-0008">2013</link>), have been used for identifying desirable corridors between detached habitats. These tools employed heuristic algorithms (such as simulated annealing) that find quick solutions at the expense of some loss of optimality (ecological, economic or both). In contrast, as discussed in numerous papers (Billionnet, <link href="#mee313817-bib-0006">2013</link>; Williams et al., <link href="#mee313817-bib-0042">2005</link>), formal optimisation, specifically mixed‐integer linear programming (MILP), guarantees optimality of the site selection solutions and therefore promotes optimal allocation of scarce conservation resources. In general, dealing with conservation planning problems in an optimisation framework that incorporates spatial attributes presents methodological and computational challenges. The optimum wildlife corridor design problem is no exception. The challenge is greater especially when too many candidate land parcels of irregular shapes are involved, which is often the case in real‐world conservation planning. The corridors that connect two (or more) detached habitat patches must themselves be comprised of spatially connected areas. Enforcing spatial connectivity together with corridor length and width considerations is methodologically possible, but it can be difficult computationally. However, such problems are not solved on a frequent basis. Even relatively small gains provided by optimal or nearly optimal solutions may well overweigh the methodological and computational difficulties.</p><p>In the biological conservation literature, only a few studies have addressed the optimal design of wildlife corridors. Williams (<link href="#mee313817-bib-0041">1998</link>) modelled the problem as a Steiner‐tree problem using a two‐objective integer programming model. Williams and Snyder (<link href="#mee313817-bib-0043">2005</link>) formulated the problem as a shortest‐path problem and introduced an optimisation model based on percolation theory. Phillips et al. (<link href="#mee313817-bib-0030">2008</link>) used network flows and developed an integer programming model to optimise the dispersal corridors for a family of endemic plants, the Cape Proteaceae, in the Western Cape of South Africa. They used a regular grid of square cells covering the targeted landscape and minimised the total selected area while satisfying each species' range size. Alagador et al. (<link href="#mee313817-bib-0001">2016</link>) extended that method to identify dispersal corridors for the European mink and the four‐leaf clover in the Iberian Peninsula. Conrad et al. (<link href="#mee313817-bib-0009">2012</link>) cast the optimal corridor design problem as a connected subgraph problem where the amount of suitable habitat is maximised in a fully connected network of parcels linking two habitat patches subject to a constraint on the funds available for land acquisition. They varied the granularity of the parcels available for acquisition by assembling adjacent parcels into larger polygons and used those polygons as selection units for the potential wildlife corridor. Dilkina et al. (<link href="#mee313817-bib-0012">2017</link>) presented a MILP model which designs a wildlife corridor for multiple species with a given budget and applied their model to grizzly bears and wolverines in western Montana, USA. None of the studies mentioned above explicitly modelled the corridor width and length among the site selection criteria. Therefore, typically the selected corridors had a width of only one land parcel. Yemshanov et al. (<link href="#mee313817-bib-0044">2021</link>) developed a MILP model to achieve a fixed corridor width by imposing the selection of all parcels in the vicinity of selected path nodes. Specifically, if a node is selected to be part of the path, all parcels within a certain distance from that node must be selected into the corridor space. They identified optimal corridors for caribou populations in a region of northern Ontario, Canada. The requirement of a uniform grid partition is an important restrictiveness of their approach. To our knowledge, only two studies, St John et al. (<link href="#mee313817-bib-0036">2016</link>) and St John et al. (<link href="#mee313817-bib-0037">2018</link>), explicitly modelled the corridor width and length problem considering irregular land parcels. They combined path planning techniques from the theory of artificial intelligence and network optimisation; specifically, they selected eligible polygons as building blocks, identified gate pairs for each triplet of those polygons, found the optimal route and associated width and length for each triplet and each of its gate pairs, and then solved the optimal corridor problem using MILP.</p><p>In this paper, we present a novel approach and a MILP model to determine the optimal configuration of a wildlife corridor connecting two existing habitat patches while considering the corridor width, length and habitat quality of candidate sites as explicit site selection criteria. The model is applicable to both regular and irregular partitions. We present a numerical application of the model to a real landscape involving 1,363 sites and two habitat patches in Eldorado National Forest in California, USA. Finally, we discuss possible extensions of the model.</p></section><section type="materialsAndMethods" xml:id="mee313817-sec-0002"><title type="main">MATERIALS AND METHODS</title><p>We define a wildlife corridor as a ‘band’ of connected land parcels that links two or more wildlife habitat patches. The parcels in the band are enclosed by two boundaries, called the ‘left boundary’ and the ‘right boundary’, where each boundary is incident to (i.e. starts from or ends at) one of the two habitat patches. The nomination of the left and right is just to differentiate the two boundaries of the corridor, they are completely interchangeable and will be determined endogenously by the model. We use two habitat patches and one corridor to demonstrate the method. The extension of the model for linking multiple habitats with multiple corridors will be discussed in the Discussion section.</p><p>We use the <i>network flow theory</i> to determine the corridor boundaries. We start with a partition of the area where the spatial units are the candidate sites (land parcels) considered for corridor formation. We overlay a <i>directed planar network</i> structure on the partition. We say that two sites are <i>adjacent</i> if they share a common edge. A <i>junction point</i> is defined as the point where two or more edges of the land parcels meet. The <i>nodes</i> and <i>arcs</i> of the network correspond to the junction points and edges of the land parcels. We define two arcs for each edge, one in each direction, linking the corresponding junction points. Each boundary is composed of a flow of arcs starting from one junction point on the border of one habitat patch, runs through a series of mutually adjacent arcs and reaches one junction point on the border of the other habitat patch.</p><p>We illustrate these concepts using a simple landscape with six land parcels separating two wildlife habitat patches labelled with H1 and H2 in Figure <link href="#mee313817-fig-0001">1</link>. The land parcels are indexed with i1 to i6, the junction points are shown with black dots indexed with k1 to k13 and the parcel edges are indexed with e1 to e13. The parcels i1 and i2 are adjacent since they share the edge e2. An edge links two adjacent junction points, such as e1 linking the junction points k1 and k5. An arc directs from one junction point along the edge of a parcel to an adjacent junction point, such as the arc directing from k1 to k5 along edge e1. An edge either separates two adjacent parcels (such as e2 separating parcels i1 and i2), or it is on the border of the planning area (such as the edge e1 of parcel i1). The bold arcs represent the corridor boundaries. The left boundary (which we arbitrarily nominate here) starts from the junction point k1 on the border of H1, extends through k5 and k6, and ends at the junction point k12, which is on the border of H2. Likewise, the right boundary starts from the junction point k3, extends through k8, k9 and k10, and ends at the junction point k13. The junction points k1, k2, k3 and k4 are on the border of H1, while the junction points k11, k12 and k13 are on the border of H2. In the mathematical model, the designation of the start and end points of the boundaries is restricted only to these two sets of junction points. If an arc is selected as part of the boundary, such as the arc k5 → k6, then one of the two parcels separated by it (here i1 and i5) is selected into the corridor (in the figure i1 is selected). If a selected arc is on the border of the conservation planning area, such as the arc k10 → k13, the parcel to which it belongs must be selected (i6 in the figure). Conversely, when a parcel is selected as part of the corridor while an adjacent parcel is not selected, one of the two directed arcs corresponding to the edge between these two parcels must be on one of the two boundaries. In Figure <link href="#mee313817-fig-0001">1</link>, the parcels i1, i2, i4 and i6 are selected to form the corridor. Only one of the adjacent land parcels i2 and i3 is selected, thus the arc k3 → k8 that separates them must be on a boundary (the right boundary in the figure). On the other hand, the parcel i3 and the arc k4 → k10 are not included in the site and arc selection, thus they are not in the corridor.</p><figure xml:id="mee313817-fig-0001"><label>1</label><title type="figureName">FIGURE</title><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:2041210X:media:mee313817:mee313817-fig-0001"/><mediaResource alt="image" href="graphic/mee313817-fig-0001-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>An illustration of a wildlife corridor (light grey) connecting two existing habitats H1 and H2 (dark grey). Thin lines are the edges of the land parcels and the bold arcs represent the corridor boundaries. The black dots are the junction points where the parcel edges meet. The numbers preceded by letters i, e and k represent, respectively, the land parcels, edges and junction points. See the text for definitions of these concepts</caption></figure><p>The optimal corridor design problem is equivalent to the optimal selection of the arcs that form the two boundaries. The distance between any two arcs (edges) is measured as the shortest distance between them. For a partition composed of parcels with regular shapes, such as squares or rectangles, such distances can be measured as the shortest distances between the endpoints of the corresponding edges. When irregular shapes are involved, the shortest distances can be determined using geographical analysis tools, such as geographic information system, as we did in the numerical application. To ensure a minimal corridor width, we require that if the distance between any two arcs is less than a specified width threshold, then the two arcs cannot be selected simultaneously with one arc serving as part of one boundary while the other serving as part of the other boundary. We consider the total length of the two boundaries as a proxy to the length of the corridor, and we impose an upper limit on this length to ensure a sufficiently short corridor. The average or the shorter of the two boundaries can be used as alternative proxies. In the corridor optimisation model we present below, these properties can be modelled easily as explicit constraints. The objective function of the model is defined as the total habitat quality of the selected corridor sites, which is to be maximised under a given conservation resource constraint. The resource constraint can be a monetary budget restriction or an upper limit for the total area of the corridor.</p><section xml:id="mee313817-sec-0003"><title type="main">The model</title><p>We used the following notation in the model. <i>i</i>, <i>j</i>: indexes of candidate land parcels; <i>k</i>, <i>l</i>, <i>ka</i>, <i>la</i>: indexes of junction points; <i>e</i>, <i>ea</i>: indexes of land parcel edges; <i>c</i><sub><i>i</i></sub>: the cost of including parcel <i>i</i> in the corridor; <i>q</i><sub><i>i</i></sub>: the quality of parcel <i>i</i>; <i>l</i><sub><i>e</i></sub>: the length of edge <i>e</i>; <i>r</i>: the conservation resource limit; <i>lb</i>: the boundary length limit; <i>d</i><sub><i>e</i>,<i>ea</i></sub>: the distance between edges <i>e</i> and <i>ea</i>. This distance is the shortest distance between arcs (<i>k</i>, <i>l</i>) and (<i>ka</i>, <i>la</i>) where (<i>k</i>, <i>l</i>) and (<i>ka</i>, <i>la</i>) are the junction points on edge <i>e</i> and <i>ea</i> respectively; <i>N</i><sub><i>k</i></sub>: the set of junction points linked to junction point <i>k</i> by an edge; <i>B</i><sub>1</sub>, <i>B</i><sub>2</sub>: sets of junction points on the borders of H1 and H2 respectively. We require that each boundary of the corridor starts from a single junction point in <i>B</i><sub><i>1</i></sub> and reaches a single junction point in <i>B</i><sub>2</sub>; <i>p</i>(<i>i</i>, <i>j</i>, <i>k</i>, <i>l</i>): pairs of adjacent land parcels <i>i</i> and <i>j</i> and their common edge represented by junction points <i>k</i> and <i>l</i>. Note that <i>k</i> and <i>l</i> both cannot belong to <i>B</i><sub>1</sub> or <i>B</i><sub>2</sub>; <i>bp</i>(<i>i</i>, <i>k</i>, <i>l</i>): similar to <i>p</i>(<i>i</i>, <i>j</i>, <i>k</i>, <i>l</i>), but defined for border parcels <i>i</i>, with <i>k</i> and <i>l</i> being the two junction points of <i>i</i>’s edge on the border of the planning area; <i>w</i>: corridor width threshold; <i>U</i><sub><i>i</i></sub>: binary variable which equals 1 if parcel <i>i</i> is selected as part of the corridor, and 0 otherwise; <i>VL</i><sub><i>k</i></sub>, <i>VR</i><sub><i>k</i></sub>: binary variables which equal 1 if the junction point <i>k</i> is selected as part of the left or the right boundary, respectively, and 0 otherwise; <i>XL</i><sub><i>kl</i></sub>, <i>XR</i><sub><i>kl</i></sub>: binary variables which equal 1 if the arc directing from <i>k</i> to <i>l</i> is selected as part of the left or the right boundary, respectively, and 0 otherwise; <i>YL</i><sub><i>e</i></sub>, <i>YR</i><sub><i>e</i></sub>: binary variables which equal 1 if edge <i>e</i> is selected as part of the left or the right boundary, respectively, and 0 otherwise. These two variables are used when calculating the boundary length of the corridor.</p><p>The model presented below determines the highest‐quality wildlife corridor between H1 and H2 under the resource availability <i>r</i> while satisfying the minimum width and maximum boundary length requirements for the corridor.<displayedItem type="mathematics" xml:id="mee313817-disp-0001"><label>1</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0001" display="block" wiley:location="graphic/mee313817-math-0001.png"><mi>Max</mi><munder><mo movablelimits="false">∑</mo><mi>i</mi></munder><msub><mi>q</mi><mi>i</mi></msub><msub><mi>U</mi><mi>i</mi></msub><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0002"><label>2a</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0002" display="block" wiley:location="graphic/mee313817-math-0002.png"><munder><mo movablelimits="false">∑</mo><mrow><mi>k</mi><mo>∈</mo><msub><mi>B</mi><mn>1</mn></msub></mrow></munder><msub><mi mathvariant="italic">VL</mi><mi>k</mi></msub><mo linebreak="goodbreak">=</mo><mn>1</mn><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0003"><label>2b</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0003" display="block" wiley:location="graphic/mee313817-math-0003.png"><munder><mo movablelimits="false">∑</mo><mrow><mi>k</mi><mo>∈</mo><msub><mi>B</mi><mn>2</mn></msub></mrow></munder><msub><mi mathvariant="italic">VL</mi><mi>k</mi></msub><mo linebreak="goodbreak">=</mo><mn>1</mn><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0004"><label>2c</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0004" display="block" wiley:location="graphic/mee313817-math-0004.png"><munder><mo movablelimits="false">∑</mo><mrow><mi>k</mi><mo>∈</mo><msub><mi>B</mi><mn>1</mn></msub></mrow></munder><msub><mi mathvariant="italic">VR</mi><mi>k</mi></msub><mo linebreak="goodbreak">=</mo><mn>1</mn><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0005"><label>2d</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0005" display="block" wiley:location="graphic/mee313817-math-0005.png"><munder><mo movablelimits="false">∑</mo><mrow><mi>k</mi><mo>∈</mo><msub><mi>B</mi><mn>2</mn></msub></mrow></munder><msub><mi mathvariant="italic">VR</mi><mi>k</mi></msub><mo linebreak="goodbreak">=</mo><mn>1</mn><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0006"><label>3a</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0006" display="block" wiley:location="graphic/mee313817-math-0006.png"><munder><mo movablelimits="false">∑</mo><mrow><mi>l</mi><mo>∈</mo><msub><mi>N</mi><mi>k</mi></msub></mrow></munder><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">=</mo><msub><mi mathvariant="italic">VL</mi><mi>k</mi></msub><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.25em"/><mi>k</mi><mspace width="0.5em"/><mtext>not belonging to</mtext><mspace width="0.5em"/><msub><mi>B</mi><mn>2</mn></msub><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0007"><label>3b</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0007" display="block" wiley:location="graphic/mee313817-math-0007.png"><munder><mo movablelimits="false">∑</mo><mrow><mi>l</mi><mo>∈</mo><msub><mi>N</mi><mi>k</mi></msub></mrow></munder><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">lk</mi></msub><mo linebreak="goodbreak">=</mo><mspace width="0.5em"/><msub><mi mathvariant="italic">VL</mi><mi>k</mi></msub><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.25em"/><mi>k</mi><mspace width="0.5em"/><mtext>not belonging to</mtext><mspace width="0.5em"/><msub><mi>B</mi><mn>1</mn></msub><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0008"><label>3c</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0008" display="block" wiley:location="graphic/mee313817-math-0008.png"><munder><mo movablelimits="false">∑</mo><mrow><mi>l</mi><mo>∈</mo><msub><mi>N</mi><mi>k</mi></msub></mrow></munder><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">=</mo><msub><mi mathvariant="italic">VR</mi><mi>k</mi></msub><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.25em"/><mi>k</mi><mspace width="0.5em"/><mtext>not belonging to</mtext><mspace width="0.5em"/><msub><mi>B</mi><mn>2</mn></msub><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0009"><label>3d</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0009" display="block" wiley:location="graphic/mee313817-math-0009.png"><munder><mo movablelimits="false">∑</mo><mrow><mi>l</mi><mo>∈</mo><msub><mi>N</mi><mi>k</mi></msub></mrow></munder><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">lk</mi></msub><mo linebreak="goodbreak">=</mo><msub><mi mathvariant="italic">VR</mi><mi>k</mi></msub><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.25em"/><mi>k</mi><mspace width="0.5em"/><mtext>not belonging to</mtext><mspace width="0.5em"/><msub><mi>B</mi><mn>1</mn></msub><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0010"><label>4</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0010" display="block" wiley:location="graphic/mee313817-math-0010.png"><msub><mi mathvariant="italic">VL</mi><mi>k</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">VR</mi><mi>k</mi></msub><mo>≤</mo><mn>1</mn><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.25em"/><mi>k</mi><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0011"><label>5a</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0011" display="block" wiley:location="graphic/mee313817-math-0011.png"><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">lk</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">lk</mi></msub><mo>≥</mo><msub><mi>U</mi><mi>i</mi></msub><mo linebreak="goodbreak">−</mo><msub><mi>U</mi><mi>j</mi></msub><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.5em"/><mi>p</mi><mfenced close=")" open="("><mrow><mi>i</mi><mo>,</mo><mi>j</mi><mo>,</mo><mi>k</mi><mo>,</mo><mi>l</mi></mrow></mfenced><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0012"><label>5b</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0012" display="block" wiley:location="graphic/mee313817-math-0012.png"><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">lk</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">lk</mi></msub><mo>≥</mo><msub><mi>U</mi><mi>j</mi></msub><mo linebreak="goodbreak">−</mo><msub><mi>U</mi><mi>j</mi></msub><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.5em"/><mi>p</mi><mfenced close=")" open="("><mrow><mi>i</mi><mo>,</mo><mi>j</mi><mo>,</mo><mi>k</mi><mo>,</mo><mi>l</mi></mrow></mfenced><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0013"><label>5c</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0013" display="block" wiley:location="graphic/mee313817-math-0013.png"><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">lk</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">lk</mi></msub><mo linebreak="goodbreak">=</mo><msub><mi>U</mi><mi>i</mi></msub><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.5em"/><mi mathvariant="italic">bp</mi><mfenced close=")" open="("><mrow><mi>i</mi><mo>,</mo><mi>k</mi><mo>,</mo><mi>l</mi></mrow></mfenced><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0014"><label>6</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0014" display="block" wiley:location="graphic/mee313817-math-0014.png"><mtable><mtr><mtd columnalign="left"><mrow><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">lk</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XR</mi><mrow><mi mathvariant="italic">ka</mi><mo>,</mo><mi mathvariant="italic">la</mi></mrow></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XR</mi><mrow><mi mathvariant="italic">la</mi><mo>,</mo><mi mathvariant="italic">ka</mi></mrow></msub><mo>≤</mo><mn>1</mn><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.25em"/><mtext>pairs of arcs</mtext><mfenced close=")" open="("><mrow><mi>k</mi><mo>,</mo><mi>l</mi></mrow></mfenced><mspace width="0.5em"/><mtext>and</mtext></mrow></mtd></mtr><mtr><mtd columnalign="left"><mrow><mspace width="0.5em"/><mfenced close=")" open="(" separators=""><mi mathvariant="italic">ka</mi><mi mathvariant="italic">la</mi></mfenced><mspace width="0.5em"/><mtext>with</mtext><mspace width="0.25em"/><mi mathvariant="normal">a</mi><mspace width="0.25em"/><mtext>distance less than</mtext><mspace width="0.5em"/><mi>w</mi><mo>,</mo></mrow></mtd></mtr></mtable></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0015"><label>7a</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0015" display="block" wiley:location="graphic/mee313817-math-0015.png"><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XL</mi><mi mathvariant="italic">lk</mi></msub><mo linebreak="goodbreak">=</mo><msub><mi mathvariant="italic">YL</mi><mi>e</mi></msub><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.25em"/><mtext>edges</mtext><mspace width="0.25em"/><mi>e</mi><mspace width="0.25em"/><mtext>and their respective</mtext><mspace width="0.25em"/><mi>end</mi><mspace width="0.25em"/><mtext>points</mtext><mspace width="0.25em"/><mi>k</mi><mspace width="0.25em"/><mtext>and</mtext><mspace width="0.25em"/><mi>l</mi><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0016"><label>7b</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0016" display="block" wiley:location="graphic/mee313817-math-0016.png"><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">kl</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">XR</mi><mi mathvariant="italic">lk</mi></msub><mo linebreak="goodbreak">=</mo><msub><mi mathvariant="italic">YR</mi><mi>e</mi></msub><mspace width="0.5em"/><mtext>for</mtext><mspace width="0.25em"/><mi>all</mi><mspace width="0.25em"/><mtext>edges</mtext><mspace width="0.25em"/><mi>e</mi><mspace width="0.25em"/><mtext>and their respective</mtext><mspace width="0.25em"/><mi>end</mi><mspace width="0.25em"/><mtext>points</mtext><mspace width="0.25em"/><mi>k</mi><mspace width="0.25em"/><mtext>and</mtext><mspace width="0.25em"/><mi>l</mi><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0017"><label>8</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0017" display="block" wiley:location="graphic/mee313817-math-0017.png"><munder><mo movablelimits="false">∑</mo><mi>e</mi></munder><msub><mi>l</mi><mi>e</mi></msub><mo linebreak="goodbreak">×</mo><mfenced close=")" open="("><mrow><msub><mi mathvariant="italic">YL</mi><mi>e</mi></msub><mo linebreak="goodbreak">+</mo><msub><mi mathvariant="italic">YR</mi><mi>e</mi></msub></mrow></mfenced><mo>≤</mo><mi mathvariant="italic">lb</mi><mo>,</mo></math></displayedItem><displayedItem type="mathematics" xml:id="mee313817-disp-0018"><label>9</label><math xmlns="http://www.w3.org/1998/Math/MathML" altimg="urn:x-wiley:2041210X:media:mee313817:mee313817-math-0018" display="block" wiley:location="graphic/mee313817-math-0018.png"><munder><mo movablelimits="false">∑</mo><mi>i</mi></munder><msub><mi>c</mi><mi>i</mi></msub><mo linebreak="goodbreak">×</mo><msub><mi>U</mi><mi>i</mi></msub><mo>≤</mo><mi>r</mi><mo>.</mo></math></displayedItem></p><p>The objective function (1) represents the total quality of the corridor (to be maximised). Constraints (2a) through (2d) select one junction point for each boundary from the border of each habitat patch. Constraints (3a) through (3d) define the arcs between adjacent junction points. Constraint (4) states that a junction point can be selected either on the left or right boundary, but not both. Constraints (5a) through (5c) associate the selection of arcs with that of the land parcels. These constraints are adapted from Önal and Briers (<link href="#mee313817-bib-0028">2003</link>) where the purpose was the minimisation of the reserve boundary. Constraint (6) states the minimum width requirement. Constraints (7a) and (7b) associate the selection of the arcs with that of the edges. Constraint (8) states that the total length of the selected edges should not exceed the boundary length limit. Finally, constraint (9) ensures that the total cost does not exceed the budget availability.</p></section><section xml:id="mee313817-sec-0004"><title type="main">Illustrative examples</title><p>We first show the workings of the model using a regular grid partition involving 10 rows and 10 columns, thus 10 × 10 = 100 square cells. The side length of each cell is 1 (Figure <link href="#mee313817-fig-0002">2</link>). We supposed that two habitats, one located at the upper‐right corner and the other at the lower‐left corner, are to be connected by a corridor. We designed two scenarios. In the first scenario, we set the width threshold as one cell, the budget limit allows selecting at most 10 cells, and the limit on the boundary length is 20. In terms of the notation introduced above, <i>w</i> = 1, <i>r</i> = 10 and <i>lb</i> = 20. In the second scenario, we set those parameters as <i>w</i> = 2, <i>r</i> = 22 and <i>lb</i> = 22. We specified the values of <i>r</i> and <i>lb</i> in the first scenario based on the observation that at least 10 cells are needed to form a corridor with a width of one cell and a boundary length of 20. The values in the second scenario were specified similarly. In both scenarios, we randomly generated the site quality values from a uniform [1,5] distribution and rounded them to the nearest integers.</p><figure xml:id="mee313817-fig-0002"><label>2</label><title type="figureName">FIGURE</title><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:2041210X:media:mee313817:mee313817-fig-0002"/><mediaResource alt="image" href="graphic/mee313817-fig-0002-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Corridor selections on a landscape partitioned with 10 × 10 = 100 square cells. The darker shaded cells represent the two habitat areas to be connected, and the light‐shaded cells form the corridor. (a) The optimal corridor with highest habitat quality and a corridor width of one cell. The limits on the area and length of the corridor are 10 and 20 respectively. (b) The optimal corridor with highest habitat quality and a width of two cells. The limits on the area and length of the corridor are both 22. In each cell, the two numbers are the cell index and the habitat quality associated with that cell</caption></figure><p>The highest‐quality corridors determined by the model under the two scenarios are displayed in Figure <link href="#mee313817-fig-0002">2</link>. The total habitat qualities of the corridors are 35 and 65 respectively. The model could be solved to exact optimality in &lt;1 s in both cases.</p><p>We tested the model’s computational efficiency in solving larger size problems, where the number of eligible sites was increased systematically. Table <link href="#mee313817-tbl-0001">1</link> provides a summary of those test runs.</p><tabular xml:id="mee313817-tbl-0001"><label>1</label><titleGroup><title type="tabularName">TABLE</title><title type="main">Summary of the test runs and the model’s computational efficiency</title></titleGroup><table colsep="0" frame="topbot" pgwide="1" rowsep="0"><tgroup cols="7"><colspec align="left" colname="col1" colnum="1"/><colspec align="char" char="." colname="col2" colnum="2"/><colspec align="char" char="." colname="col3" colnum="3"/><colspec align="char" char="." colname="col4" colnum="4"/><colspec align="char" char="." colname="col5" colnum="5"/><colspec align="char" char="." colname="col6" colnum="6"/><colspec align="char" char="." colname="col7" colnum="7"/><thead valign="bottom"><row rowsep="1"><entry align="left" colname="col1" morerows="1" rowsep="1">Number of candidate sites</entry><entry align="left" nameend="col4" namest="col2" rowsep="1"><i>w</i> = 1</entry><entry align="left" nameend="col7" namest="col5" rowsep="1"><i>w</i> = 2</entry></row><row rowsep="1"><entry align="left" colname="col2"><i>r</i></entry><entry align="left" colname="col3"><i>lb</i></entry><entry align="left" colname="col4">Time (s)</entry><entry align="left" colname="col5"><i>r</i></entry><entry align="left" colname="col6"><i>lb</i></entry><entry align="left" colname="col7">Time (s)</entry></row></thead><tbody valign="top"><row><entry colname="col1">400 (=20 × 20)</entry><entry colname="col2">30</entry><entry colname="col3">60</entry><entry colname="col4">9.2</entry><entry colname="col5">62</entry><entry colname="col6">62</entry><entry colname="col7">143.0</entry></row><row><entry colname="col1">800 (=20 × 40)</entry><entry colname="col2">50</entry><entry colname="col3">100</entry><entry colname="col4">40.4</entry><entry colname="col5">102</entry><entry colname="col6">102</entry><entry colname="col7">2,614.4</entry></row><row><entry colname="col1">1,200 (=30 × 40)</entry><entry colname="col2">60</entry><entry colname="col3">120</entry><entry colname="col4">148.4</entry><entry colname="col5">122</entry><entry colname="col6">122</entry><entry colname="col7">*</entry></row><row><entry colname="col1">1,600 (=40 × 40)</entry><entry colname="col2">70</entry><entry colname="col3">140</entry><entry colname="col4">419.3</entry><entry colname="col5">142</entry><entry colname="col6">142</entry><entry colname="col7">*</entry></row><row><entry colname="col1">2,000 (=40 × 50)</entry><entry colname="col2">80</entry><entry colname="col3">160</entry><entry colname="col4">694.2</entry><entry colname="col5">162</entry><entry colname="col6">162</entry><entry colname="col7">*</entry></row></tbody></tgroup></table><noteGroup xml:id="mee313817-ntgp-0002"><note numbered="no" xml:id="mee313817-note-0002"><p><i>Notes</i>: <i>w</i> is measured as the number of square cells at the narrowest part of the corridor; <i>r</i> is measured as the maximum number of squares forming the corridor. The solution times are the average CPU times needed to solve the model to exact optimality. The *s indicate that the optimal solution could not be found within 2 hr. The computer used in the runs was a desktop Lenovo with a 64‐bit Windows, an Intel(R) Pentium(R) CPU G640 of 2.8 gigahertz (two processors) and a RAM of 16.0 gigabytes. As the optimisation solver, we used Gurobi 9.0.2 integrated with GAMS 32.1.0 (<url href="http://www.gams.com">http://www.gams.com</url>).</p></note></noteGroup></tabular><p>Table <link href="#mee313817-tbl-0001">1</link> shows that as the number of candidate sites is increased, the time needed to solve the model increases under both width specifications. When the width threshold is specified as <i>w</i> = 2 and more than 1,200 sites are considered, a solution very close to the unknown optimum solution (with a gap of &lt;3%) could be obtained within 1 hr.</p></section><section xml:id="mee313817-sec-0005"><title type="main">An empirical application</title><p>We applied the above model to a dataset for Eldorado National Forest in California, USA. We downloaded the data from the Integrated Forest Management Lab of the Forest Management Optimization Site, the University of New Brunswick (<url href="http://ifmlab.for.unb.ca/fmos/datasets/">http://ifmlab.for.unb.ca/fmos/datasets/</url>). Following St John et al. (<link href="#mee313817-bib-0037">2018</link>), we supposed that a wildlife corridor must connect the two habitat areas located at the north‐west and the south‐east sections of the forest (Figure <link href="#mee313817-fig-0003">3</link>). The dataset was in the format of polygon shapefiles and contained 1,363 irregular land parcels. We prepared the sets and the parameters involved in the mathematical model using ArcGIS (v10.2). Specifically, we transformed those 1,363 polygons into 4,214 polylines (edges of the land parcels) using the Polygon‐To‐Line function in ArcToolbox and identified all pairs of adjacent land parcels and their common edges using the attribute table of the resulting polyline file. We identified 2,858 junction points for those polylines using the Planarize‐Lines function in Advanced Editing (Figure <link href="#mee313817-fig-0003">3a</link>), and we found all pairs of edges within a distance under 300 m using the Generate‐Near‐Table function included in Analysis Tools in ArcToolbox (we used these pairs of edges in the corridor width constraint 6 in the model). We created a personal geodatabase in ArcGIS to place these data and used the program <i>mdb2gms.exe</i> included in GAMS to convert them to a GAMS readable format (.inc). We calculated the total forest area as 211.47 km<sup>2</sup>. We first specified the budget limit (corridor area) as 15% of the total forest area (as in St John et al., <link href="#mee313817-bib-0037">2018</link>), which is 31.72 km<sup>2</sup>, and the maximum corridor length as 100 km. Then, to provide an alternative plan, we reduced the budget limit to 30.00 km<sup>2</sup> and the corridor length limit to 95 km. The corridor width threshold specified below and the long distance between the two habitat areas did not allow us to reduce these limits further because the model was infeasible with lower limits. The original data did not include any land quality characteristics. To incorporate habitat quality in the site selection, we randomly generated the habitat quality of each land parcel from a uniform distribution of [1,5] and rounded it to the nearest integer. We fixed the corridor width threshold at 300 m.</p><figure xml:id="mee313817-fig-0003"><label>3</label><title type="figureName">FIGURE</title><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:2041210X:media:mee313817:mee313817-fig-0003"/><mediaResource alt="image" href="graphic/mee313817-fig-0003-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>The landscape map of Eldorado National Forest in California, USA, used in the empirical application (a). The lines represent the land parcel edges. The black dots are the junction points where the parcel edges meet. The two dark areas are the two existing habitat areas to be connected by a wildlife corridor. (b) The optimal wildlife corridor generated with an area limit of 31.72 km<sup>2</sup> and a boundary length limit of 100 km. (c) The optimal wildlife corridor generated with an area limit of 30.00 km<sup>2</sup> and a boundary length limit of 95 km. The corridor width threshold was 300 m. In (b) and (c), we did not mark the junction points for visual convenience. The areas selected by the optimisation model are shaded</caption></figure></section></section><section type="results" xml:id="mee313817-sec-0006"><title type="main">RESULTS</title><p>We show the spatial layouts of the optimum corridor alternatives obtained with the two parameter specifications in Figure <link href="#mee313817-fig-0003">3</link>. Some summary statistics obtained from the model results are presented in Table <link href="#mee313817-tbl-0002">2</link>.</p><tabular xml:id="mee313817-tbl-0002"><label>2</label><titleGroup><title type="tabularName">TABLE</title><title type="main">Summary of the corridor selection results with two alternative area and length specifications</title></titleGroup><table colsep="0" frame="topbot" pgwide="1" rowsep="0"><tgroup cols="5"><colspec align="left" colname="col1" colnum="1"/><colspec align="char" char="." colname="col2" colnum="2"/><colspec align="char" char="." colname="col3" colnum="3"/><colspec align="char" char="." colname="col4" colnum="4"/><colspec align="char" char="." colname="col5" colnum="5"/><thead valign="bottom"><row rowsep="1"><entry align="left" colname="col1">Area and length limits (km<sup>2</sup>; km)</entry><entry align="left" colname="col2">Corridor area (km<sup>2</sup>)</entry><entry align="left" colname="col3">Boundary length (km)</entry><entry align="left" colname="col4">Corridor quality</entry><entry align="left" colname="col5">CPU time (s)</entry></row></thead><tbody valign="top"><row><entry colname="col1">31.72; 100</entry><entry colname="col2">31.71</entry><entry colname="col3">99.39</entry><entry colname="col4">757</entry><entry colname="col5">4,222</entry></row><row><entry colname="col1">30.00; 95</entry><entry colname="col2">30.00</entry><entry colname="col3">94.98</entry><entry colname="col4">654</entry><entry colname="col5">5,574</entry></row></tbody></tgroup></table></tabular><p>As shown in Table <link href="#mee313817-tbl-0002">2</link>, the model selected a corridor with an area and boundary length almost equal to the specified area and boundary length limits (compare the values in columns two and three with those in column one). When the area and the length limits were decreased from 31.72 to 30.00 km<sup>2</sup> and from 100 to 95 km, respectively, the total quality of the corridor decreased from 757 to 654. This is an expected and intuitive result since a tighter limit on the conservation resource must normally generate a corridor with a lower total quality. We solved the model to exact optimality in about 70 and 90 min, respectively, for the two planning alternatives.</p><p>Habitat quality was not a consideration in St John et al. (<link href="#mee313817-bib-0037">2018</link>). Thus, the corridors selected by our model are not directly comparable to those found by St John et al. (<link href="#mee313817-bib-0037">2018</link>) in terms of their shape and location. However, we have observed that some segments of the corridors we have found were remarkably wider than those found by St John et al. (<link href="#mee313817-bib-0037">2018</link>). This is because their building block polygons were either the original land parcels (most of which have an area of 20 ha or less), or were restricted to multiple parcels with a combined area not exceeding 20 ha. This restriction made the polygon selection computationally tractable but generated polygons not much different in size (all under 20 ha except a few large original parcels). The corridor, then, was constructed as a row of those building block polygons, resulting in a width somewhat consistent throughout the corridor. Our model selects the land parcels to maximise the total quality of the corridor while satisfying the width and the length requirements. Some clustered, good‐quality parcels were selected by the model to the extent permitted by the budget constraint. This explains the presence of relatively ‘wide’ segments in the optimal corridors displayed in Figure <link href="#mee313817-fig-0003">3</link>.</p></section><section type="discussion" xml:id="mee313817-sec-0007"><title type="main">DISCUSSION</title><p>Among the four parameters involved in the model, the limit on the boundary length (<i>lb</i>) is relatively easy to specify based on the locations of the existing habitats to connect and the movement behaviour of the species of concern. This is an important advantage of the optimisation model we present in this paper compared to the model presented by St John et al. (<link href="#mee313817-bib-0037">2018</link>). Unlike the direct approach we use here, which employs raw spatial data, St John et al. (<link href="#mee313817-bib-0037">2018</link>) employ path planning techniques to generate the input data needed in the optimisation model. This is a complicated data processing procedure that requires additional steps (triangulation and determining pseudo‐edges, valid gate pairs, etc.) besides optimisation modelling and programming. The conservation resource availability (<i>r</i>) can be stated either as a monetary budget constraint or a corridor area constraint, which may be either given or can be specified by conservation planners. However, the parameters representing the habitat quality of land parcels (<i>q</i><sub><i>i</i></sub>) and the width threshold (<i>w</i>) may be difficult to compile and specify. These parameters have to be determined according to the needs of the targeted species and the characteristics of the corridor intended to serve those species. They can be specified in coordination with field experts based on the biological and/or ecological factors affecting the species' movement behaviour and habitat requirements, and environmental and ecological attributes of the individual sites in the potential corridor area. Mathematicians, biologists, ecologists and managers must work together to determine those attributes and processes. The quality of individual land parcels can be assessed better using species distribution models based on available species occurrence data, and it may also be evaluated by taking into account the socio‐economic aspects, such as productivity of those lands and the stakeholders' preferences (Beier et al., <link href="#mee313817-bib-0005">2011</link>). A recent study by Ford et al. (<link href="#mee313817-bib-0017">2020</link>) found that the width threshold may also depend on the surrounding landscape, such as the residential or recreational values of those areas.</p><p>Ensuring corridor contiguity in an optimisation framework is both methodologically and computationally intensive. The optimisation model becomes further difficult to solve when corridor width and length requirements are also imposed. To achieve corridor contiguity, we impose a tight limit on the length of the corridor boundary. This not only eliminates the possibility of generating a disconnected corridor, but it also remarkably improves the model’s computational performance. Such a limit on the boundary length can be estimated easily for a landscape composed of regular grid cells with straight edges (e.g. squares or rectangles). For landscapes partitioned with irregularly shaped land parcels, as we have used in the empirical application, a small value could make the model infeasible. When this occurs, the solution process is terminated quickly (typically within seconds or minutes). One may gradually increase the limit until a feasible solution is found. The first feasible solution provides a good starting point that may be helpful to generate an optimal corridor.</p><p>The method may be extended to address some other important concerns in wildlife corridor planning. One such concern is linking the existing habitats through a corridor that contains pre‐specified intermediate habitat areas. Including critical intermediate habitat areas in the corridor may be necessary because of various reasons, such as consideration of strategic points in the landscape that may sustain certain ecological processes, maintain ecosystem integrity or provide food and water amenities. To address this issue, additional constraints must be imposed to enforce that the sites in those intermediate habitats are selected and that the left and right boundaries extend to and leave from some junction points on the borders of those habitats.</p><p>An important issue is the presence of barriers and linear elements, such as a river or a gully, in the landscape considered for conservation planning. Such landscape characteristics may impose serious resistance to the movement of organisms through that parcel. This can be incorporated in the model indirectly during the parcel quality assessment, namely by assigning a very low parcel quality which would make the parcel unlikely to be part of the corridor.</p><p>Another extension of the one‐corridor model presented here is concerned with designing multiple corridors connecting two (or more) existing habitat areas. This may be desirable when the existing habitat areas are large and have long borders, therefore multiple corridors between them would serve species movements better than a single corridor. A simple case, for instance, is two existing habitat areas to be connected via two disjoint corridors (i.e. without intersection). This can be achieved by applying the model to find the first corridor, then applying the model again to find the second corridor but enforcing that the land parcels, junction points and edges included in the first corridor cannot be part of the second corridor. Note that this stepwise optimisation approach determines one corridor at a time, rather than determining the two corridors simultaneously. Therefore, this approach may lead to a suboptimal solution. The true optimal solution can be determined by defining two sets of variables, one set for each corridor. That, however, makes the model substantially larger, thus harder to solve.</p><p>In the past few decades, computation techniques and computer hardware have evolved very fast. The model we developed here can be solved using distributed parallel algorithms that allow the usage of multiple processors in a cooperative manner, or using cloud computing services, such as the Amazon Web Services. We anticipate that such services might enable us to solve large‐scale conservation planning problems, including the corridor design problem.</p><p>Finally, we emphasise that our focus here has been primarily on the ‘how‐to’ aspect of solving the wildlife corridor problem, assuming that we know which habitats to connect. The latter is an equally important and challenging problem since the impacts of fragmentation on biodiversity and ecosystems are still not well‐understood (Fahrig, <link href="#mee313817-bib-0014">2003</link>, <link href="#mee313817-bib-0015">2017</link>, <link href="#mee313817-bib-0016">2021</link>; Saura, <link href="#mee313817-bib-0032">2021</link>). Considering a landscape as a network of large habitat patches, the integral index of connectivity (Pascual‐Hortal &amp; Saura, <link href="#mee313817-bib-0029">2006</link>) or the probability of connectivity (Saura &amp; Pascual‐Hortal, <link href="#mee313817-bib-0033">2007</link>) can be used along with software like Conefor (Saura &amp; Torné, <link href="#mee313817-bib-0034">2009</link>) or Landscapemetrics (Hesselbarth et al., <link href="#mee313817-bib-0024">2019</link>) to determine the patches that should be connected. Once this is done, for each pair of patches to be connected the model we presented here can be used to design optimal corridors at a finer scale.</p></section><section numbered="no" type="acknowledgments" xml:id="mee313817-sec-0008"><title type="main">ACKNOWLEDGEMENTS</title><p>The authors thank the Associate Editor and two anonymous reviewers for their constructive comments. This work was partially supported by the National Natural Science Foundation of China (72172144), the Natural Science Foundation of Shandong Province (ZR2019MG011) and the Illinois CREES Project (ILLU 05–0361).</p></section><section numbered="no" type="conflictOfInterest" xml:id="mee313817-sec-0009"><title type="main">CONFLICT OF INTEREST</title><p>The authors declare no conflict of interest.</p></section><section numbered="no" xml:id="mee313817-sec-0010"><title type="main">AUTHORS' CONTRIBUTIONS</title><p>Y.W. and H.Ö. conceived the ideas and developed the methodology; P.Q. collected the data; Y.W. and P.Q. analysed the data; Y.W. and H.Ö. led the writing of the manuscript. All authors contributed critically to the drafts and gave final approval for publication.</p></section><section numbered="no" type="openResearch" xml:id="mee313817-sec-0011"><section numbered="no" type="transparentPeerReview" xml:id="mee313817-sec-0012"><title type="main">PEER REVIEW</title><p>The peer review history for this article is available at <url href="https://publons.com/publon/10.1111/2041-210X.13817">https://publons.com/publon/10.1111/2041‐210X.13817</url>.</p></section><section numbered="no" type="dataAvailability" xml:id="mee313817-sec-0013"><title type="main">DATA AVAILABILITY STATEMENT</title><p>The GAMS code for the computational efficiency test, and the GAMS code for the empirical application along with all the data involved can be found in <url href="https://doi.org/10.5061/dryad.fn2z34tw7">https://doi.org/10.5061/dryad.fn2z34tw7</url> (Wang et al., <link href="#mee313817-bib-0040">2022</link>).</p></section></section><bibliography cited="yes" style="nameDate" xml:id="mee313817-bibl-0001"><title type="main">REFERENCES</title><bib xml:id="mee313817-bib-0001"><citation type="journal" xml:id="mee313817-cit-0001"><author><familyName>Alagador</familyName>, <givenNames>D.</givenNames></author>, <author><familyName>Cerdeira</familyName>, <givenNames>J. O.</givenNames></author>, <author><familyName>Araújo</familyName>, <givenNames>M. B.</givenNames></author>, &amp; <author><familyName>Anderson</familyName>, <givenNames>B.</givenNames></author> (<pubYear year="2016">2016</pubYear>). <articleTitle>Climate change, species range shifts and dispersal corridors: An evaluation of spatial conservation models</articleTitle>. <journalTitle>Methods in Ecology &amp; Evolution</journalTitle>, <vol>7</vol>, <pageFirst>853</pageFirst>–<pageLast>866</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0002"><citation type="journal" xml:id="mee313817-cit-0002"><author><familyName>Alagador</familyName>, <givenNames>D.</givenNames></author>, <author><familyName>Trivino</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Cerdeira</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Bras</familyName>, <givenNames>R.</givenNames></author>, <author><familyName>Cabeza</familyName>, <givenNames>M.</givenNames></author>, &amp; <author><familyName>Araújo</familyName>, <givenNames>M.</givenNames></author> (<pubYear year="2012">2012</pubYear>). <articleTitle>Linking like with like: Optimising connectivity between environmentally‐similar habitats</articleTitle>. <journalTitle>Landscape Ecology</journalTitle>, <vol>27</vol>, <pageFirst>291</pageFirst>–<pageLast>301</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0003"><citation type="journal" xml:id="mee313817-cit-0003"><author><familyName>Andreassen</familyName>, <givenNames>H. P.</givenNames></author>, <author><familyName>Halle</familyName>, <givenNames>S.</givenNames></author>, &amp; <author><familyName>Ims</familyName>, <givenNames>R. A.</givenNames></author> (<pubYear year="1996">1996</pubYear>). <articleTitle>Optimal width of movement corridors for root voles: Not too narrow and not too wide</articleTitle>. <journalTitle>Journal of Applied Ecology</journalTitle>, <vol>33</vol>, <pageFirst>63</pageFirst>–<pageLast>70</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0004"><citation type="journal" xml:id="mee313817-cit-0004"><author><familyName>Beier</familyName>, <givenNames>P.</givenNames></author>, <author><familyName>Majka</familyName>, <givenNames>D. R.</givenNames></author>, &amp; <author><familyName>Newell</familyName>, <givenNames>S. L.</givenNames></author> (<pubYear year="2009">2009</pubYear>). <articleTitle>Uncertainty analysis of least‐cost modeling for designing wildlife linkages</articleTitle>. <journalTitle>Ecological Applications</journalTitle>, <vol>19</vol>, <pageFirst>2067</pageFirst>–<pageLast>2077</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0005"><citation type="journal" xml:id="mee313817-cit-0005"><author><familyName>Beier</familyName>, <givenNames>P.</givenNames></author>, <author><familyName>Spencer</familyName>, <givenNames>W.</givenNames></author>, <author><familyName>Baldwin</familyName>, <givenNames>R. F.</givenNames></author>, &amp; <author><familyName>McRae</familyName>, <givenNames>B. H.</givenNames></author> (<pubYear year="2011">2011</pubYear>). <articleTitle>Toward best practices for developing regional connectivity maps</articleTitle>. <journalTitle>Conservation Biology</journalTitle>, <vol>25</vol>, <pageFirst>879</pageFirst>–<pageLast>892</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0006"><citation type="journal" xml:id="mee313817-cit-0006"><author><familyName>Billionnet</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2013">2013</pubYear>). <articleTitle>Mathematical optimization ideas for biodiversity conservation</articleTitle>. <journalTitle>European Journal of Operational Research</journalTitle>, <vol>231</vol>, <pageFirst>514</pageFirst>–<pageLast>534</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0007"><citation type="journal" xml:id="mee313817-cit-0007"><author><familyName>Bolger</familyName>, <givenNames>D. T.</givenNames></author>, <author><familyName>Scott</familyName>, <givenNames>T. A.</givenNames></author>, &amp; <author><familyName>Rotenberry</familyName>, <givenNames>J. T.</givenNames></author> (<pubYear year="2001">2001</pubYear>). <articleTitle>Use of corridor‐like landscape structures by bird and small mammal species</articleTitle>. <journalTitle>Biological Conservation</journalTitle>, <vol>102</vol>, <pageFirst>213</pageFirst>–<pageLast>224</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0008"><citation type="journal" xml:id="mee313817-cit-0008"><author><familyName>Brás</familyName>, <givenNames>R.</givenNames></author>, <author><familyName>Cerdeira</familyName>, <givenNames>J. O.</givenNames></author>, <author><familyName>Alagador</familyName>, <givenNames>D.</givenNames></author>, &amp; <author><familyName>Araújo</familyName>, <givenNames>M. B.</givenNames></author> (<pubYear year="2013">2013</pubYear>). <articleTitle>Linking habitats for multiple species</articleTitle>. <journalTitle>Environmental Modelling &amp; Software</journalTitle>, <vol>40</vol>, <pageFirst>336</pageFirst>–<pageLast>339</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0009"><citation type="journal" xml:id="mee313817-cit-0009"><author><familyName>Conrad</familyName>, <givenNames>J. M.</givenNames></author>, <author><familyName>Gomes</familyName>, <givenNames>C. P.</givenNames></author>, <author><familyName>vanHoeve</familyName>, <givenNames>W. J.</givenNames></author>, <author><familyName>Sabharwal</familyName>, <givenNames>A.</givenNames></author>, &amp; <author><familyName>Suter</familyName>, <givenNames>J. F.</givenNames></author> (<pubYear year="2012">2012</pubYear>). <articleTitle>Wildlife corridors as a connected subgraph problem</articleTitle>. <journalTitle>Journal of Environmental Economics and Management</journalTitle>, <vol>63</vol>, <pageFirst>1</pageFirst>–<pageLast>18</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0010"><citation type="journal" xml:id="mee313817-cit-0010"><author><familyName>Coster</familyName>, <givenNames>S. S.</givenNames></author>, <author><familyName>Veysey Powell</familyName>, <givenNames>J. S.</givenNames></author>, &amp; <author><familyName>Babbitt</familyName>, <givenNames>K. J.</givenNames></author> (<pubYear year="2014">2014</pubYear>). <articleTitle>Characterizing the width of amphibian movements during postbreeding migration</articleTitle>. <journalTitle>Conservation Biology</journalTitle>, <vol>28</vol>, <pageFirst>756</pageFirst>–<pageLast>762</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0011"><citation type="other" xml:id="mee313817-cit-0011"><author><familyName>Díaz</familyName>, <givenNames>S.</givenNames></author>, <author><familyName>Settele</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Brondízio</familyName>, <givenNames>E.</givenNames></author>, <author><familyName>Ngo</familyName>, <givenNames>H.</givenNames></author>, <author><familyName>Guèze</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Agard</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Arneth</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Balvanera</familyName>, <givenNames>P.</givenNames></author>, <author><familyName>Brauman</familyName>, <givenNames>K.</givenNames></author>, <author><familyName>Butchart</familyName>, <givenNames>S.</givenNames></author>, <author><familyName>Chan</familyName>, <givenNames>K.</givenNames></author>, <author><familyName>Garibaldi</familyName>, <givenNames>L.</givenNames></author>, <author><familyName>Ichii</familyName>, <givenNames>K.</givenNames></author>, <author><familyName>Liu</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Subrmanian</familyName>, <givenNames>S.</givenNames></author>, <author><familyName>Midgley</familyName>, <givenNames>G.</givenNames></author>, <author><familyName>Miloslavich</familyName>, <givenNames>P.</givenNames></author>, <author><familyName>Molnár</familyName>, <givenNames>Z.</givenNames></author>, <author><familyName>Obura</familyName>, <givenNames>D.</givenNames></author>, … <author><familyName>Zaya</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2020">2020</pubYear>). Summary for policymakers of the global assessment report on biodiversity and ecosystem services of the intergovernmental science‐policy platform on biodiversity and ecosystem services. <url href="https://uwe-repository.worktribe.com/output/1493508/summary-for-policymakers-of-the-global-assessment-report-on-biodiversity-and-ecosystem-services-of-the-intergovernmental-science-policy-platform-on-biodiversity-and-ecosystem-services">https://uwe‐repository.worktribe.com/output/1493508/summary‐for‐policymakers‐of‐the‐global‐assessment‐report‐on‐biodiversity‐and‐ecosystem‐services‐of‐the‐intergovernmental‐science‐policy‐platform‐on‐biodiversity‐and‐ecosystem‐services</url></citation></bib><bib xml:id="mee313817-bib-0012"><citation type="journal" xml:id="mee313817-cit-0012"><author><familyName>Dilkina</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>Houtman</familyName>, <givenNames>R.</givenNames></author>, <author><familyName>Gomes</familyName>, <givenNames>C. P.</givenNames></author>, <author><familyName>Montgomery</familyName>, <givenNames>C. A.</givenNames></author>, <author><familyName>McKelvey</familyName>, <givenNames>K. S.</givenNames></author>, <author><familyName>Kendall</familyName>, <givenNames>K.</givenNames></author>, <author><familyName>Graves</familyName>, <givenNames>T. A.</givenNames></author>, <author><familyName>Bernstein</familyName>, <givenNames>R.</givenNames></author>, &amp; <author><familyName>Schwartz</familyName>, <givenNames>M. K.</givenNames></author> (<pubYear year="2017">2017</pubYear>). <articleTitle>Trade‐offs and efficiencies in optimal budget‐constrained multispecies corridor networks</articleTitle>. <journalTitle>Conservation Biology</journalTitle>, <vol>31</vol>, <pageFirst>192</pageFirst>–<pageLast>202</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0013"><citation type="journal" xml:id="mee313817-cit-0013"><author><familyName>Estrada</familyName>, <givenNames>E.</givenNames></author>, &amp; <author><familyName>Bodin</familyName>, <givenNames>Ö.</givenNames></author> (<pubYear year="2008">2008</pubYear>). <articleTitle>Using network centrality measures to manage landscape connectivity</articleTitle>. <journalTitle>Ecological Applications</journalTitle>, <vol>18</vol>, <pageFirst>1810</pageFirst>–<pageLast>1825</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0014"><citation type="journal" xml:id="mee313817-cit-0014"><author><familyName>Fahrig</familyName>, <givenNames>L.</givenNames></author> (<pubYear year="2003">2003</pubYear>). <articleTitle>Effects of habitat fragmentation on biodiversity</articleTitle>. <journalTitle>Annual Review of Ecology, Evolution, and Systematics</journalTitle>, <vol>34</vol>, <pageFirst>487</pageFirst>–<pageLast>515</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0015"><citation type="journal" xml:id="mee313817-cit-0015"><author><familyName>Fahrig</familyName>, <givenNames>L.</givenNames></author> (<pubYear year="2017">2017</pubYear>). <articleTitle>Ecological responses to habitat fragmentation per se</articleTitle>. <journalTitle>Annual Review of Ecology, Evolution &amp; Systematics</journalTitle>, <vol>48</vol>, <pageFirst>1</pageFirst>–<pageLast>23</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0016"><citation type="journal" xml:id="mee313817-cit-0016"><author><familyName>Fahrig</familyName>, <givenNames>L.</givenNames></author> (<pubYear year="2021">2021</pubYear>). <articleTitle>What the habitat amount hypothesis does and does not predict: A reply to Saura</articleTitle>. <journalTitle>Journal of Biogeography</journalTitle>, <vol>48</vol>, <pageFirst>1530</pageFirst>–<pageLast>1535</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0017"><citation type="journal" xml:id="mee313817-cit-0017"><author><familyName>Ford</familyName>, <givenNames>A. T.</givenNames></author>, <author><familyName>Sunter</familyName>, <givenNames>E. J.</givenNames></author>, <author><familyName>Fauvelle</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>Bradshaw</familyName>, <givenNames>J. L.</givenNames></author>, <author><familyName>Ford</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>Hutchen</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Phillipow</familyName>, <givenNames>N.</givenNames></author>, &amp; <author><familyName>Teichman</familyName>, <givenNames>K. J.</givenNames></author> (<pubYear year="2020">2020</pubYear>). <articleTitle>Effective corridor width: Linking the spatial ecology of wildlife with land use policy</articleTitle>. <journalTitle>European Journal of Wildlife Research</journalTitle>, <vol>66</vol>, <pageFirst>1</pageFirst>–<pageLast>10</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0018"><citation type="journal" xml:id="mee313817-cit-0018"><author><familyName>Gibson</familyName>, <givenNames>L.</givenNames></author>, <author><familyName>Lynam</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Bradshaw</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>He</familyName>, <givenNames>F.</givenNames></author>, <author><familyName>Bickford</familyName>, <givenNames>D.</givenNames></author>, <author><familyName>Woodruff</familyName>, <givenNames>D.</givenNames></author>, <author><familyName>Bumrungsri</familyName>, <givenNames>S.</givenNames></author>, &amp; <author><familyName>Laurance</familyName>, <givenNames>W.</givenNames></author> (<pubYear year="2013">2013</pubYear>). <articleTitle>Near‐complete extinction of native small mammal fauna 25 years after forest fragmentation</articleTitle>. <journalTitle>Science</journalTitle>, <vol>341</vol>, <pageFirst>1508</pageFirst>–<pageLast>1510</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0019"><citation type="journal" xml:id="mee313817-cit-0019"><author><familyName>Haddad</familyName>, <givenNames>N.</givenNames></author> (<pubYear year="1999">1999</pubYear>). <articleTitle>Corridor and distance effects on interpatch movements: A landscape experiment with butterflies</articleTitle>. <journalTitle>Ecological Applications</journalTitle>, <vol>9</vol>, <pageFirst>612</pageFirst>–<pageLast>622</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0020"><citation type="journal" xml:id="mee313817-cit-0020"><author><familyName>Haddad</familyName>, <givenNames>N. M.</givenNames></author>, <author><familyName>Bowne</familyName>, <givenNames>D. R.</givenNames></author>, <author><familyName>Cunningham</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Danielson</familyName>, <givenNames>B. J.</givenNames></author>, <author><familyName>Levey</familyName>, <givenNames>D. J.</givenNames></author>, <author><familyName>Sargent</familyName>, <givenNames>S.</givenNames></author>, &amp; <author><familyName>Spira</familyName>, <givenNames>T.</givenNames></author> (<pubYear year="2003">2003</pubYear>). <articleTitle>Corridor use by diverse taxa</articleTitle>. <journalTitle>Ecology</journalTitle>, <vol>84</vol>, <pageFirst>609</pageFirst>–<pageLast>615</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0021"><citation type="journal" xml:id="mee313817-cit-0021"><author><familyName>Haddad</familyName>, <givenNames>N. M.</givenNames></author>, <author><familyName>Brudvig</familyName>, <givenNames>L. A.</givenNames></author>, <author><familyName>Clobert</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Davies</familyName>, <givenNames>K. F.</givenNames></author>, <author><familyName>Gonzalez</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Holt</familyName>, <givenNames>R. D.</givenNames></author>, <author><familyName>Lovejoy</familyName>, <givenNames>T. E.</givenNames></author>, <author><familyName>Sexton</familyName>, <givenNames>J. O.</givenNames></author>, <author><familyName>Austin</familyName>, <givenNames>M. P.</givenNames></author>, <author><familyName>Collins</familyName>, <givenNames>C. D.</givenNames></author>, <author><familyName>Cook</familyName>, <givenNames>W. M.</givenNames></author>, <author><familyName>Damschen</familyName>, <givenNames>E. I.</givenNames></author>, <author><familyName>Ewers</familyName>, <givenNames>R. M.</givenNames></author>, <author><familyName>Foster</familyName>, <givenNames>B. L.</givenNames></author>, <author><familyName>Jenkins</familyName>, <givenNames>C. N.</givenNames></author>, <author><familyName>King</familyName>, <givenNames>A. J.</givenNames></author>, <author><familyName>Laurance</familyName>, <givenNames>W. F.</givenNames></author>, <author><familyName>Levey</familyName>, <givenNames>D. J.</givenNames></author>, <author><familyName>Margules</familyName>, <givenNames>C. R.</givenNames></author>, et al. (<pubYear year="2015">2015</pubYear>). <articleTitle>Habitat fragmentation and its lasting impact on Earth’s ecosystems</articleTitle>. <journalTitle>Science Advances</journalTitle>, <vol>1</vol>, <eLocator>e1500052</eLocator>.</citation></bib><bib xml:id="mee313817-bib-0022"><citation type="journal" xml:id="mee313817-cit-0022"><author><familyName>Haddad</familyName>, <givenNames>N. M.</givenNames></author>, <author><familyName>Brudvig</familyName>, <givenNames>L. A.</givenNames></author>, <author><familyName>Damschen</familyName>, <givenNames>E. I.</givenNames></author>, <author><familyName>Evans</familyName>, <givenNames>D. M.</givenNames></author>, <author><familyName>Johnson</familyName>, <givenNames>B. L.</givenNames></author>, <author><familyName>Levey</familyName>, <givenNames>D. J.</givenNames></author>, <author><familyName>Orrock</familyName>, <givenNames>J. L.</givenNames></author>, <author><familyName>Resasco</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Sullivan</familyName>, <givenNames>L. L.</givenNames></author>, <author><familyName>Tewksbury</familyName>, <givenNames>J. J.</givenNames></author>, <author><familyName>Wagner</familyName>, <givenNames>S. A.</givenNames></author>, &amp; <author><familyName>Weldon</familyName>, <givenNames>A. J.</givenNames></author> (<pubYear year="2014">2014</pubYear>). <articleTitle>Potential negative ecological effects of corridors</articleTitle>. <journalTitle>Conservation Biology</journalTitle>, <vol>28</vol>, <pageFirst>1178</pageFirst>–<pageLast>1187</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0023"><citation type="journal" xml:id="mee313817-cit-0023"><author><familyName>Heintzman</familyName>, <givenNames>L. J.</givenNames></author>, &amp; <author><familyName>McIntyre</familyName>, <givenNames>N. E.</givenNames></author> (<pubYear year="2021">2021</pubYear>). <articleTitle>Assessment of playa wetland network connectivity for amphibians of the south‐central Great Plains (USA) using graph‐theoretical, least‐cost path, and landscape resistance modelling</articleTitle>. <journalTitle>Landscape Ecology</journalTitle>, <vol>36</vol>, <pageFirst>1117</pageFirst>–<pageLast>1135</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0024"><citation type="journal" xml:id="mee313817-cit-0024"><author><familyName>Hesselbarth</familyName>, <givenNames>M. H. K.</givenNames></author>, <author><familyName>Sciaini</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>With</familyName>, <givenNames>K. A.</givenNames></author>, <author><familyName>Wiegand</familyName>, <givenNames>K.</givenNames></author>, &amp; <author><familyName>Nowosad</familyName>, <givenNames>J.</givenNames></author> (<pubYear year="2019">2019</pubYear>). <articleTitle>Landscapemetrics: An open‐source R tool to calculate landscape metrics</articleTitle>. <journalTitle>Ecography</journalTitle>, <vol>42</vol>, <pageFirst>1648</pageFirst>–<pageLast>1657</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0025"><citation type="journal" xml:id="mee313817-cit-0025"><author><familyName>Hodgson</familyName>, <givenNames>J. A.</givenNames></author>, <author><familyName>Moilanen</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Wintle</familyName>, <givenNames>B. A.</givenNames></author>, &amp; <author><familyName>Thomas</familyName>, <givenNames>C. D.</givenNames></author> (<pubYear year="2011">2011</pubYear>). <articleTitle>Habitat area, quality and connectivity: Striking the balance for efficient conservation</articleTitle>. <journalTitle>Journal of Applied Ecology</journalTitle>, <vol>48</vol>, <pageFirst>148</pageFirst>–<pageLast>152</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0026"><citation type="journal" xml:id="mee313817-cit-0026"><author><familyName>Koen</familyName>, <givenNames>E. L.</givenNames></author>, <author><familyName>Bowman</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Sadowski</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>Walpole</familyName>, <givenNames>A. A.</givenNames></author>, &amp; <author><familyName>Tatem</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2014">2014</pubYear>). <articleTitle>Landscape connectivity for wildlife: Development and validation of multispecies linkage maps</articleTitle>. <journalTitle>Methods in Ecology &amp; Evolution</journalTitle>, <vol>5</vol>, <pageFirst>626</pageFirst>–<pageLast>633</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0027"><citation type="journal" xml:id="mee313817-cit-0027"><author><familyName>Matisziw</familyName>, <givenNames>T. C.</givenNames></author>, <author><familyName>Alam</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Trauth</familyName>, <givenNames>K. M.</givenNames></author>, <author><familyName>Inniss</familyName>, <givenNames>E. C.</givenNames></author>, <author><familyName>Semlitsch</familyName>, <givenNames>R. D.</givenNames></author>, <author><familyName>McIntosh</familyName>, <givenNames>S.</givenNames></author>, &amp; <author><familyName>Horton</familyName>, <givenNames>J.</givenNames></author> (<pubYear year="2015">2015</pubYear>). <articleTitle>A vector approach for modeling landscape corridors and habitat connectivity</articleTitle>. <journalTitle>Environmental Modeling and Assessment</journalTitle>, <vol>20</vol>, <pageFirst>1</pageFirst>–<pageLast>16</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0028"><citation type="journal" xml:id="mee313817-cit-0028"><author><familyName>Önal</familyName>, <givenNames>H.</givenNames></author>, &amp; <author><familyName>Briers</familyName>, <givenNames>R. A.</givenNames></author> (<pubYear year="2003">2003</pubYear>). <articleTitle>Selection of a minimum‐boundary reserve network using integer programming</articleTitle>. <journalTitle>Proceedings of the Royal Society of London. Series B, Biological Sciences</journalTitle>, <vol>270</vol>, <pageFirst>1487</pageFirst>–<pageLast>1491</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0029"><citation type="journal" xml:id="mee313817-cit-0029"><author><familyName>Pascual‐Hortal</familyName>, <givenNames>L.</givenNames></author>, &amp; <author><familyName>Saura</familyName>, <givenNames>S.</givenNames></author> (<pubYear year="2006">2006</pubYear>). <articleTitle>Comparison and development of new graph‐based landscape connectivity indices: Towards the priorization of habitat patches and corridors for conservation</articleTitle>. <journalTitle>Landscape Ecology</journalTitle>, <vol>21</vol>, <pageFirst>959</pageFirst>–<pageLast>967</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0030"><citation type="journal" xml:id="mee313817-cit-0030"><author><familyName>Phillips</familyName>, <givenNames>S. J.</givenNames></author>, <author><familyName>Williams</familyName>, <givenNames>P.</givenNames></author>, <author><familyName>Midgley</familyName>, <givenNames>G.</givenNames></author>, &amp; <author><familyName>Archer</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2008">2008</pubYear>). <articleTitle>Optimizing dispersal corridors for the cape Proteaceae using network flow</articleTitle>. <journalTitle>Ecological Applications</journalTitle>, <vol>18</vol>, <pageFirst>1200</pageFirst>–<pageLast>1211</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0031"><citation type="journal" xml:id="mee313817-cit-0031"><author><familyName>Sarkar</familyName>, <givenNames>S.</givenNames></author> (<pubYear year="2012">2012</pubYear>). <articleTitle>Complementarity and the selection of nature reserves: Algorithms and the origins of conservation planning, 1980–1995</articleTitle>. <journalTitle>Archive for History of Exact Sciences</journalTitle>, <vol>66</vol>, <pageFirst>397</pageFirst>–<pageLast>426</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0032"><citation type="journal" xml:id="mee313817-cit-0032"><author><familyName>Saura</familyName>, <givenNames>S.</givenNames></author> (<pubYear year="2021">2021</pubYear>). <articleTitle>The habitat amount hypothesis predicts that fragmentation poses a threat to biodiversity: A reply to Fahrig</articleTitle>. <journalTitle>Journal of Biogeography</journalTitle>, <vol>48</vol>, <pageFirst>1536</pageFirst>–<pageLast>1540</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0033"><citation type="journal" xml:id="mee313817-cit-0033"><author><familyName>Saura</familyName>, <givenNames>S.</givenNames></author>, &amp; <author><familyName>Pascual‐Hortal</familyName>, <givenNames>L.</givenNames></author> (<pubYear year="2007">2007</pubYear>). <articleTitle>A new habitat availability index to integrate connectivity in landscape conservation planning: Comparison with existing indices and application to a case study</articleTitle>. <journalTitle>Landscape and Urban Planning</journalTitle>, <vol>83</vol>, <pageFirst>91</pageFirst>–<pageLast>103</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0034"><citation type="journal" xml:id="mee313817-cit-0034"><author><familyName>Saura</familyName>, <givenNames>S.</givenNames></author>, &amp; <author><familyName>Torné</familyName>, <givenNames>J.</givenNames></author> (<pubYear year="2009">2009</pubYear>). <articleTitle>Conefor Sensinode 2.2: A software package for quantifying the importance of habitat patches for landscape connectivity</articleTitle>. <journalTitle>Environmental Modelling &amp; Software</journalTitle>, <vol>24</vol>, <pageFirst>135</pageFirst>–<pageLast>139</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0035"><citation type="journal" xml:id="mee313817-cit-0035"><author><familyName>Sawyer</familyName>, <givenNames>S. C.</givenNames></author>, <author><familyName>Epps</familyName>, <givenNames>C. W.</givenNames></author>, &amp; <author><familyName>Brashares</familyName>, <givenNames>J. S.</givenNames></author> (<pubYear year="2011">2011</pubYear>). <articleTitle>Placing linkages among fragmented habitats: Do least‐cost models reflect how animals use landscapes?</articleTitle> <journalTitle>Journal of Applied Ecology</journalTitle>, <vol>48</vol>, <pageFirst>668</pageFirst>–<pageLast>678</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0036"><citation type="journal" xml:id="mee313817-cit-0036"><author><familyName>St John</familyName>, <givenNames>R.</givenNames></author>, <author><familyName>Öhman</familyName>, <givenNames>K.</givenNames></author>, <author><familyName>Tóth</familyName>, <givenNames>S. F.</givenNames></author>, <author><familyName>Sandström</familyName>, <givenNames>P.</givenNames></author>, <author><familyName>Korosuo</familyName>, <givenNames>A.</givenNames></author>, &amp; <author><familyName>Eriksson</familyName>, <givenNames>L. O.</givenNames></author> (<pubYear year="2016">2016</pubYear>). <articleTitle>Combining spatiotemporal corridor design for reindeer migration with harvest scheduling in northern Sweden</articleTitle>. <journalTitle>Scandinavian Journal of Forest Research</journalTitle>, <vol>31</vol>, <pageFirst>655</pageFirst>–<pageLast>663</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0037"><citation type="journal" xml:id="mee313817-cit-0037"><author><familyName>St John</familyName>, <givenNames>R.</givenNames></author>, <author><familyName>Tóth</familyName>, <givenNames>S. F.</givenNames></author>, &amp; <author><familyName>Zabinsky</familyName>, <givenNames>Z. B.</givenNames></author> (<pubYear year="2018">2018</pubYear>). <articleTitle>Optimizing the geometry of wildlife corridors in conservation reserve design</articleTitle>. <journalTitle>Operations Research</journalTitle>, <vol>66</vol>, <pageFirst>1471</pageFirst>–<pageLast>1485</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0038"><citation type="other" xml:id="mee313817-cit-0038"><groupName>UNEP</groupName>. (<pubYear year="2021">2021</pubYear>). <otherTitle><i>Protected planet report</i></otherTitle>. Retrieved from <url href="https://livereport.protectedplanet.net/">https://livereport.protectedplanet.net/</url></citation></bib><bib xml:id="mee313817-bib-0039"><citation type="journal" xml:id="mee313817-cit-0039"><author><familyName>Vanthomme</familyName>, <givenNames>H. P. A.</givenNames></author>, <author><familyName>Nzamba</familyName>, <givenNames>B. S.</givenNames></author>, <author><familyName>Alonso</familyName>, <givenNames>A.</givenNames></author>, &amp; <author><familyName>Todd</familyName>, <givenNames>A. F.</givenNames></author> (<pubYear year="2019">2019</pubYear>). <articleTitle>Empirical selection between least‐cost and current‐flow designs for establishing wildlife corridors in Gabon</articleTitle>. <journalTitle>Conservation Biology</journalTitle>, <vol>33</vol>, <pageFirst>329</pageFirst>–<pageLast>338</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0040"><citation type="journal" xml:id="mee313817-cit-0040"><author><familyName>Wang</familyName>, <givenNames>Y. C.</givenNames></author>, <author><familyName>Qin</familyName>, <givenNames>P.</givenNames></author>, &amp; <author><familyName>Önal</familyName>, <givenNames>H.</givenNames></author> (<pubYear year="2022">2022</pubYear>). <articleTitle>Dataset for: An optimisation approach for designing wildlife corridors with ecological and spatial considerations</articleTitle>. <journalTitle>Dryad Digital Repository</journalTitle>. <url href="https://doi.org/10.5061/dryad.fn2z34tw7">https://doi.org/10.5061/dryad.fn2z34tw7</url></citation></bib><bib xml:id="mee313817-bib-0041"><citation type="journal" xml:id="mee313817-cit-0041"><author><familyName>Williams</familyName>, <givenNames>J. C.</givenNames></author> (<pubYear year="1998">1998</pubYear>). <articleTitle>Delineating protected wildlife corridors with multi‐objective programming</articleTitle>. <journalTitle>Environmental Modeling and Assessment</journalTitle>, <vol>3</vol>, <pageFirst>77</pageFirst>–<pageLast>86</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0042"><citation type="journal" xml:id="mee313817-cit-0042"><author><familyName>Williams</familyName>, <givenNames>J. C.</givenNames></author>, <author><familyName>ReVelle</familyName>, <givenNames>C. S.</givenNames></author>, &amp; <author><familyName>Levin</familyName>, <givenNames>S. A.</givenNames></author> (<pubYear year="2005">2005</pubYear>). <articleTitle>Spatial attributes and reserve design models: A review</articleTitle>. <journalTitle>Environmental Modeling and Assessment</journalTitle>, <vol>10</vol>, <pageFirst>163</pageFirst>–<pageLast>181</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0043"><citation type="journal" xml:id="mee313817-cit-0043"><author><familyName>Williams</familyName>, <givenNames>J. C.</givenNames></author>, &amp; <author><familyName>Snyder</familyName>, <givenNames>S. A.</givenNames></author> (<pubYear year="2005">2005</pubYear>). <articleTitle>Restoring habitat corridors in fragmented landscapes using optimization and percolation models</articleTitle>. <journalTitle>Environmental Modeling &amp; Assessment</journalTitle>, <vol>10</vol>, <pageFirst>239</pageFirst>–<pageLast>250</pageLast>.</citation></bib><bib xml:id="mee313817-bib-0044"><citation type="journal" xml:id="mee313817-cit-0044"><author><familyName>Yemshanov</familyName>, <givenNames>D.</givenNames></author>, <author><familyName>Haight</familyName>, <givenNames>R. G.</givenNames></author>, <author><familyName>Liu</familyName>, <givenNames>N.</givenNames></author>, <author><familyName>Rempel</familyName>, <givenNames>R.</givenNames></author>, <author><familyName>Koch</familyName>, <givenNames>F. H.</givenNames></author>, &amp; <author><familyName>Rodgers</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2021">2021</pubYear>). <articleTitle>Exploring the tradeoffs among forest planning, roads and wildlife corridors: A new approach</articleTitle>. <journalTitle>Optimization Letters</journalTitle>, <vol>16</vol>. <url href="https://doi.org/10.1007/s11590-021-01745-w">https://doi.org/10.1007/s11590‐021‐01745‐w</url></citation></bib></bibliography></body></component>