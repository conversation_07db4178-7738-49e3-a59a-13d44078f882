<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7216352</article-id><article-id pub-id-type="publisher-id">3496</article-id><article-id pub-id-type="doi">10.1186/s12859-020-3496-8</article-id><article-categories><subj-group subj-group-type="heading"><subject>Methodology Article</subject></subj-group></article-categories><title-group><article-title>Cluster correlation based method for lncRNA-disease association prediction</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Yuan</surname><given-names>Qianqian</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Guo</surname><given-names>Xingli</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Ren</surname><given-names>Yang</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Wen</surname><given-names>Xiao</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Gao</surname><given-names>Lin</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1"><institution-wrap><institution-id institution-id-type="GRID">grid.440736.2</institution-id><institution-id institution-id-type="ISNI">0000 0001 0707 115X</institution-id><institution>School of Computer Science and Technology, </institution><institution>XIDIAN UNIVERSITY, </institution></institution-wrap>Xi&#x02019;an, Shaanxi China </aff></contrib-group><pub-date pub-type="epub"><day>11</day><month>5</month><year>2020</year></pub-date><pub-date pub-type="pmc-release"><day>11</day><month>5</month><year>2020</year></pub-date><pub-date pub-type="collection"><year>2020</year></pub-date><volume>21</volume><elocation-id>180</elocation-id><history><date date-type="received"><day>10</day><month>1</month><year>2020</year></date><date date-type="accepted"><day>15</day><month>4</month><year>2020</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2020</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is licensed under a Creative Commons Attribution 4.0 International License, which permits use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons licence, and indicate if changes were made. The images or other third party material in this article are included in the article's Creative Commons licence, unless indicated otherwise in a credit line to the material. If material is not included in the article's Creative Commons licence and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this licence, visit <ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated in a credit line to the data.</license-p></license></permissions><abstract id="Abs1"><sec><title><bold>Background</bold></title><p id="Par1">In recent years, increasing evidences have indicated that long non-coding RNAs (lncRNAs) are deeply involved in a wide range of human biological pathways. The mutations and disorders of lncRNAs are closely associated with many human diseases. Therefore, it is of great importance to predict potential associations between lncRNAs and complex diseases for the diagnosis and cure of complex diseases. However, the functional mechanisms of the majority of lncRNAs are still remain unclear. As a result, it remains a great challenge to predict potential associations between lncRNAs and diseases.</p></sec><sec><title>Results</title><p id="Par2">Here, we proposed a new method to predict potential lncRNA-disease associations. First, we constructed a bipartite network based on known associations between diseases and lncRNAs/protein coding genes. Then the cluster association scores were calculated to evaluate the strength of the inner relationships between disease clusters and gene clusters. Finally, the gene-disease association scores are defined based on disease-gene cluster association scores and used to measure the strength for potential gene-disease associations.</p></sec><sec><title>Conclusions</title><p id="Par3">Leave-One Out Cross Validation (LOOCV) and 5-fold cross validation tests were implemented to evaluate the performance of our method. As a result, our method achieved reliable performance in the LOOCV (AUCs of 0.8169 and 0.8410 based on Yang&#x02019;s dataset and Lnc2cancer 2.0 database, respectively), and 5-fold cross validation (AUCs of 0.7573 and 0.8198 based on Yang&#x02019;s dataset and Lnc2cancer 2.0 database, respectively), which were significantly higher than the other three comparative methods. Furthermore, our method is simple and efficient. Only the known gene-disease associations are exploited in a graph manner and further new gene-disease associations can be easily incorporated in our model. The results for melanoma and ovarian cancer have been verified by other researches. The case studies indicated that our method can provide informative clues for further investigation.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Long noncoding RNA</kwd><kwd>Disease</kwd><kwd>lncRNA-disease association</kwd><kwd>Cluster correlation</kwd><kwd>Bipartite network</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001809</institution-id><institution>National Natural Science Foundation of China</institution></institution-wrap></funding-source><award-id>No. 61672407</award-id><award-id>No. 61532014</award-id><award-id>No.61772395</award-id><award-id>No.61702397</award-id><principal-award-recipient><name><surname>Guo</surname><given-names>Xingli</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2020</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par15">About 3% of the human genome is the coding region, which produces multiple proteins, and other non-coding regions transcribe a large number of non-coding RNAs. Much of the non-coding region of the human genome has historically been regarded as junk DNA [<xref ref-type="bibr" rid="CR1">1</xref>]. However, for decades, researchers have discovered that multiple types of RNA exist, and among the most important is non-coding RNA (ncRNA). According to transcript lengths, ncRNAs could be further categorized into small ncRNAs and lncRNAs [<xref ref-type="bibr" rid="CR2">2</xref>]. LncRNAs are the biggest part of non-coding RNAs which are longer than 200 nucleotides and are not translated into proteins [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR4">4</xref>]. It is estimated that about 62% of the human genome is transcribed to produce long non-coding RNAs. Compared with protein-coding transcripts, lncRNAs have fewer exons and are expressed at lower levels [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR6">6</xref>]. However, lncRNAs show extensive mechanisms to play their biological roles compared to small ncRNAs [<xref ref-type="bibr" rid="CR7">7</xref>]. As shown by more and more studies that lncRNAs play crucial functional roles in cytoplasm and nucleus through cis or trans-regulatory mechanisms [<xref ref-type="bibr" rid="CR6">6</xref>], and play important roles in different cellular pathways [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR9">9</xref>].</p><p id="Par16">In recent years, with the rapid development of high-throughput sequencing technologies, researchers have identified many lncRNAs in eukaryotic organisms. For example, Cabili et al. integrated chromatin marks and RNA-sequencing data to identify more than 8000 long intergenic ncRNAs across 24 different human cell types and tissues [<xref ref-type="bibr" rid="CR10">10</xref>]. And accumulating evidences have shown that mutations and disorders of lncRNAs are closely related to many complex human diseases [<xref ref-type="bibr" rid="CR11">11</xref>]. The earliest lncRNAs to be discovered were XIST [<xref ref-type="bibr" rid="CR12">12</xref>] and H19 [<xref ref-type="bibr" rid="CR13">13</xref>]. These two genes have been demonstrated to be linked to several types of cancers. For example, One of the first lncRNAs to be identified, H19, acts as a decoy for several tumor suppressor miRNAs, with let-7 [<xref ref-type="bibr" rid="CR14">14</xref>]. Another important discovery of lncRNAs is that the lincRNA termed HOTAIR is increased in expression in primary breast tumors and metastases, and HOTAIR expression level in primary tumors is a powerful predictor of eventual metastasis and death [<xref ref-type="bibr" rid="CR15">15</xref>]. Yan et al. comprehensively analyzed the characteristics of lncRNAs in different types of human cancers at the genome, transcription and epigenetic levels [<xref ref-type="bibr" rid="CR16">16</xref>]. The results indicated that lncRNAs are more specific than mRNAs in expression and dysregulation in different cancers [<xref ref-type="bibr" rid="CR16">16</xref>]. With regard to liver cancer, Yang et al. not only analyzed the dysregulated lncRNAs, but also inferred its pathogenesis by combining methylation and copy number variation [<xref ref-type="bibr" rid="CR17">17</xref>]. Due to their functional significance, various databases have been developed to store lncRNA related information, such as lncRNAdb [<xref ref-type="bibr" rid="CR18">18</xref>], NONCODE [<xref ref-type="bibr" rid="CR5">5</xref>], including the information of lncRNA structure, expression, and so on. LncRNADisease [<xref ref-type="bibr" rid="CR19">19</xref>], Lnc2Cancer [<xref ref-type="bibr" rid="CR20">20</xref>] are mainly focused on different lncRNA-disease associations. These databases are crucial for deciphering lncRNA functions in human diseases. However, the functions and biological relevance of the vast majority of lncRNAs remain enigmatic.</p><p id="Par17">Recently, the functions of lncRNAs and their associations with human diseases have attracted much attention from researchers because increasing evidences indicated that lncRNAs play critical roles in the development of various human diseases. With the development of novel experimental and computational methods, researchers have proposed a variety of models to predict the biological functions of lncRNAs and lncRNA-disease associations. For example, Chen et al. constructed a computational tool named LRLSLDA to predict novel human lncRNA-disease associations [<xref ref-type="bibr" rid="CR21">21</xref>]. It is well known that LRLSLDA is the first lncRNA-disease association prediction model which is based on the assumption that the functions of lncRNAs associated with similar diseases are often similar. A semi-supervised learning framework of Laplacian Regularized Least Squares was mainly applied in this model. As a result, LRLSLDA significantly improved the performance of previous methods used to solve the similar computational biology problems. Based on the basic assumption that similar diseases tend to have associations with functionally similar lncRNAs, more computational models were developed, such as LNCSIM [<xref ref-type="bibr" rid="CR22">22</xref>] and LDAP [<xref ref-type="bibr" rid="CR23">23</xref>]. LNCSIM calculated lncRNA functional similarity on a large scale based on lncRNA-disease associations and disease semantic similarity. LDAP was proposed to predict potential lncRNA-disease associations by using a bagging SVM classifier based on lncRNA similarity and disease similarity. Furthermore, some models were developed by integrating multiple data sources into networks. In 2015, Guo et al. developed a reliable method named lncGFP [<xref ref-type="bibr" rid="CR24">24</xref>] based on a global network strategy to predict probable functions of lncRNAs at large scales, which may give clues to the potential associations between lncRNAs and diseases. Sun et al. proposed a computational method named RWRlncD [<xref ref-type="bibr" rid="CR25">25</xref>] by implementing random walk with restart (RWR) on the lncRNA functional similarity network. Chen et al. developed model named IRWRLDA [<xref ref-type="bibr" rid="CR26">26</xref>] which combined lncRNA expression similarity and disease semantic similarity to set the initial probability vector of the RWR to predict novel lncRNA-disease associations. Yang et al. constructed a coding-non-coding gene-disease bipartite network based on the known gene-disease associations and uncovered the hidden lncRNA-disease associations by implementing a global propagation algorithm on this network [<xref ref-type="bibr" rid="CR27">27</xref>]. Chen et al. developed a model called KATZLDA by integrating known lncRNA-disease associations, lncRNA expression profiles, lncRNA functional similarity, disease semantic similarity and Gaussian interaction profile kernel similarity to uncover potential lncRNA-disease associations [<xref ref-type="bibr" rid="CR28">28</xref>]. Furthermore, KATZLDA could work for both new diseases and lncRNAs. Due to few known lncRNA-disease associations, some researchers have developed methods that rely on other information besides the known lncRNA-disease associations. For example, Liu et al. identified potential lncRNA-disease associations based on known gene-disease associations and gene-lncRNA co-expression relationships which was the first computational method without the need to rely on known lncRNA-disease associations [<xref ref-type="bibr" rid="CR29">29</xref>]. All the lncRNA-disease association prediction models aforementioned were listed in the Table S1(see Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>).</p><p id="Par18">In this paper, a simple and efficient method was developed to predict novel lncRNA-disease associations. First, a bipartite network is constructed by integrating known lncRNA-disease associations and protein-coding gene-disease associations. Then the concept of disease clusters and gene clusters is presented in the bipartite network. The key idea behind this method is that the nodes in one part associated with the same node in another part are more similar with each other, which is similar to the assumption used by other methods [<xref ref-type="bibr" rid="CR22">22</xref>, <xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR26">26</xref>]. Based on the above, we proposed a new method to calculate association scores for potential gene-disease pairs. Cross-Validation tests were used to evaluate the performance of our method. As a result, our method obtained reliable AUCs of 0.8169, 0.8410 in the LOOCV based on Yang&#x02019;s [<xref ref-type="bibr" rid="CR27">27</xref>] dataset and Lnc2Cancer 2.0 [<xref ref-type="bibr" rid="CR30">30</xref>] database, respectively. We further implemented 5-fold cross validation on our method and obtained reliable AUCs of 0.7573, 0.8198 based on Yang&#x02019;s dataset and Lnc2Cancer 2.0 database, respectively. The performance of our method was superior to other similar methods on the two datasets. Moreover, case studies on melanoma and colon cancer demonstrated that it could give clues to further investigations.</p></sec><sec id="Sec2"><title>Results</title><sec id="Sec3"><title>Prediction of lncRNAs associated with diseases</title><p id="Par19">For the gene-disease pairs without edges in the bipartite network, our method can calculate an association score for a pair which can be used to measure the potential association strength of this gene-disease pair. Ultimately, we sorted the association scores of all potential gene-disease pairs and selected the top 1% as predicted results, and obtained a total of 2320 potential gene-disease associations (1321 lncRNA-disease pairs and 999 protein coding gene-disease pairs) (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>).</p></sec><sec id="Sec4"><title>Performance evaluation</title><p id="Par20">LOOCV and 5-fold cross validation were applied to evaluate the prediction performance of our method based on known lncRNA-disease associations from the dataset of Yang [<xref ref-type="bibr" rid="CR27">27</xref>] and Lnc2Cancer 2.0 database [<xref ref-type="bibr" rid="CR30">30</xref>]. When LOOCV was applied, each known lncRNA-disease association was removed from the lncRNA-disease bipartite network in turn as test sample. Our method was assessed by how well the removed lncRNA-disease association was ranked within all the lncRNA-disease associations. The receiver operating characteristics (ROC) curve can be obtained by plotting true positive rate (TPR) versus false positive rate (FPR) at different rank thresholds. Given the rank threshold k, TPR indicates the percentage of the removed edges with ranks higher than the threshold and FPR indicates the percentage of negative samples with ranks higher than this threshold. Therefore, ROC can be drawn and area under ROC curve (AUC) could be further calculated (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>). Considering the isolated nodes whose unique edges were removed, we cannot obtain any relevant information about them. So we removed all the nodes whose degrees were one before we performed LOOCV. The dataset of Yang contained 236 lncRNA-disease associations between 102 diseases and 44 lncRNAs (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>). And 1541 lncRNA-disease associations between 249 lncRNAs and 85 diseases were obtained from the Lnc2Cancer 2.0 database (see Additional file <xref rid="MOESM4" ref-type="media">4</xref>).</p><p id="Par21">Our method was compared with the following three state-of-the-art methods (Yang&#x02019;s method [<xref ref-type="bibr" rid="CR27">27</xref>], IRWRLDA [<xref ref-type="bibr" rid="CR26">26</xref>] and KATZLDA [<xref ref-type="bibr" rid="CR28">28</xref>]) by cross validation tests on two datasets (Yang&#x02019;s dataset and Lnc2Cancer 2.0). In LOOCV tests and 5-fold cross validation tests, the performance of our method was superior to other three methods. Details for LOOCV tests can be seen in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a and b, and the results of 5-fold cross-validation tests were shown in Fig. <xref rid="Fig1" ref-type="fig">1</xref>c and d.
<fig id="Fig1"><label>Fig. 1</label><caption><p>Cross validation tests of our method. <bold>a</bold> Comparative results of LOOCV on Yang&#x02019;s dataset. <bold>b</bold> Comparative results of LOOCV on the Lnc2Cancer 2.0 dataset. <bold>c</bold> Comparative results of 5-fold cross validation on Yang&#x02019;s dataset. <bold>d</bold> Comparative results of 5-fold cross validation on the Lnc2Cancer 2.0 dataset</p></caption><graphic xlink:href="12859_2020_3496_Fig1_HTML" id="MO1"/></fig></p></sec><sec id="Sec5"><title>Robustness of our method</title><p id="Par22">To test the robustness of our method in a network view for predicting potential gene-disease associations, the method of Multiple Survival Screening (MSS) [<xref ref-type="bibr" rid="CR31">31</xref>] is used to test our method under perturbation of the bipartite network. First, a total of 2320 potential gene-disease associations in our bipartite network was used to evaluate the performance of our method in these perturbation tests, which is called the set of verification edges. Then, a certain percentage of edges (10, 20, 30%, respectively) in the network were deleted randomly in these tests. Our method was utilized to predict potential associations on these remaining networks. The performance is evaluated on the verification set. At each different threshold, re-sampling experiments are performed 1000 times. A vector of size 2320 was constructed, corresponding to 2320 predicted edges. Each value in the vector represented the times of its corresponding verification edge could be predicted in 1000 experiments. Our method was more stable in comparison with Yang&#x02019;s method at different thresholds (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). The results of 1000 re-sampling experiments at different thresholds were shown in Additional&#x000a0;file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>. When 20% of edges were deleted, the prediction accuracy could be maintained at around 0.8 which was significantly higher than Yang&#x02019;s method (<italic>p</italic>-value&#x02009;=&#x02009;0.022). As the proportion of deleted edges increased, the accuracy decreased gradually (Fig. <xref rid="Fig2" ref-type="fig">2</xref>). Here, we also randomly rewired the edges to construct random network, while keeping the degree of each node in the bipartite network unchanged. Our method was also applied to the random network for comparison. The results indicated that the accuracy of our method was significantly higher than that of random network (<italic>p</italic>-value&#x0003c;&#x02009;10<sup>&#x02212;&#x02009;10</sup>).
<fig id="Fig2"><label>Fig. 2</label><caption><p>Comparative results of robustness test between our method and Yang&#x02019;s method at different thresholds. The vertical ordinate indicated the times predicted correctly in 1000 re-sampling experiments between our method and Yang&#x02019;s method at each different threshold. And the last graph represents times predicted correctly in 1000 re-sampling experiments on the random network by our method</p></caption><graphic xlink:href="12859_2020_3496_Fig2_HTML" id="MO2"/></fig></p></sec><sec id="Sec6"><title>Case study</title><p id="Par23">In order to further demonstrate the performance of our method in predicting potential lncRNA-disease associations, the results of colon cancer and melanoma were analyzed as case study. For each case, the genes associated with the disease were ranked according to their association scores. Based on our results (2320 potential gene-disease associations), we retained genes within top 5% related to these two diseases independently for further analysis. Our predictions were validated by other independent experiments, part of which were listed in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>.
<table-wrap id="Tab1"><label>Table 1</label><caption><p>Case studies of colorectal cancer and melanoma</p></caption><table frame="hsides" rules="groups"><thead><tr><th>LNCRNA</th><th>Disease</th><th>PMID</th><th>Rank</th></tr></thead><tbody><tr><td>MIR31HG</td><td>Colorectal cancer</td><td>30,195,788</td><td>Top23</td></tr><tr><td>CCND1</td><td>Colorectal cancer</td><td>27,191,497</td><td>Top23</td></tr><tr><td>lncRNA-HEIH</td><td>Colorectal cancer</td><td>29,081,216</td><td>Top28</td></tr><tr><td>LSINCT5</td><td>Colorectal cancer</td><td>25,526,476</td><td>Top29</td></tr><tr><td>MIR31HG</td><td>Melanoma</td><td>25,908,244</td><td>Top32</td></tr><tr><td>U47924.27</td><td>Melanoma</td><td>28,225,791</td><td>Top32</td></tr><tr><td>CCND1</td><td>Melanoma</td><td>23,001,925</td><td>Top32</td></tr></tbody></table></table-wrap></p><p id="Par24">Colorectal cancer (CRC) is a common malignant tumor of the digestive tract that occurs in the colon. In recent years, the prevalence rate of colorectal cancer has increased continuously [<xref ref-type="bibr" rid="CR32">32</xref>]. The studies indicated that lncRNAs played an important role in the development and progression of colorectal cancer [<xref ref-type="bibr" rid="CR33">33</xref>]. There were 31 lncRNAs predicted to have potential associations with colorectal cancer by our method. Part of them were validated by other independent experiments. For example, Zhou et al. determined that MIR31HG was closely related to the recurrence of colorectal cancer [<xref ref-type="bibr" rid="CR34">34</xref>]. The signature of MIR31HG held great potential for risk assessment of recurrence and personalized management of colorectal cancer patients. Chen et al. observed that miR-374a inhibited colorectal cancer progression by reducing CCND1 to inactivate the PI3K/AKT pathway [<xref ref-type="bibr" rid="CR35">35</xref>]. Cui et al. demonstrated that lncRNA-HEIH promoted CRC tumorigenesis through counteracting miR-939&#x02013;mediated transcriptional repression of Bcl-xL, and suggested that lncRNA-HEIH may serve as a prognostic biomarker and therapeutic target for CRC [<xref ref-type="bibr" rid="CR36">36</xref>]. They found that lncRNA-HEIH was significantly increased in colorectal cancer tissues and cell lines. The expression of lncRNA-HEIH was positively associated with tumor size, invasion depth, and poor prognosis of CRC patients [<xref ref-type="bibr" rid="CR36">36</xref>]. Moreover, Xu et al. found that the expression level of LSINCT5 was closely related to the disease-free survival and disease-specific survival rates based on Kaplan-Meier analysis in CRC patients [<xref ref-type="bibr" rid="CR37">37</xref>].</p><p id="Par25">Melanoma, also known as malignant melanoma, is a type of malignant tumor derived from melanocytes. As one of the most malignant tumors in skin tumors, melanoma is prone to distant metastasis, so early diagnosis and treatment are particularly imperative. Accumulating evidences have revealed that lncRNAs played critical roles in the development and progression of melanoma. There were 32 lncRNAs predicted to have potential associations with melanoma among our results. Some results were validated by other studies. For example, Montes et al. found that patients with higher levels of MIR31HG often have reduced p16INK4A expression, which suggested that MIR31HG with repression of p16INK4A in these patients favored cancer development [<xref ref-type="bibr" rid="CR38">38</xref>]. Wang et al. observed that the low expression of U47924.27 was significantly associated with decreased survival in melanoma patients, revealing the potential role of U47924.27 in melanoma tumorigenesis and metastasis [<xref ref-type="bibr" rid="CR39">39</xref>]. Furthermore, V&#x000ed;zkeleti et al. observed that CCND1 alterations were linked to melanoma progression and CCND1 amplification may have a prognostic relevance in cutaneous melanoma and emphasized that changes in CCND1 gene expression may influence the metastatic progression, survival and metastasis localization [<xref ref-type="bibr" rid="CR40">40</xref>].</p></sec></sec><sec id="Sec7"><title>Discussion</title><p id="Par26">LncRNAs are involved in the regulation of various processes in cells and the development of complex diseases through a variety of biological mechanisms. Therefore, predicting and discovering lncRNAs associated with complex diseases are important for the diagnosis and treatment of diseases. In this paper, we constructed a bipartite network using known gene-disease associations. Then we predicted potential lncRNA-disease associations only based on the topological information of the gene-disease bipartite network. It is assumed that genes (diseases) associated with the same disease (gene) are more similar. The assumption was incorporated into our bipartite network to proposed the definitions of gene clusters and disease clusters. The biological significance of the two kinds of clusters are analyzed in comparison with those in random networks. And then, the problem of predicting potential lncRNA-disease associations was formulated as a problem of measuring the association strength between gene clusters and disease clusters. The &#x02018; <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> &#x02019; index was first defined to estimate the association strength between clusters. Then the gene-disease association score was defined based on the <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> with regard to the influence of different degrees of the node in the bipartite network. Cross validation test was applied to evaluate the prediction performance of our method. In comparison with the state-of-the-art prediction methods, our method can achieve better performance in terms of AUC values and robustness. Moreover, case studies of melanoma and colon cancer were implemented to further demonstrate that it could be a useful and simple method for predicting potential relationships between lncRNAs and diseases as well.</p><p id="Par27">However, there are also some limitations existing in our current method. In spite of the fact that our method is significantly superior to the previous methods, its performance can also be improved by incorporating other information in our model. Due to the fact that only the known gene-disease associations were exploited in the model, our method cannot be applied to the diseases without any known associated genes. Further data integration will be helpful to improve the power of our model and characterize the complex relationships between new genes (without any known associated diseases) and new diseases (without any known associated lncRNAs) from different perspectives. For example, the Single Nucleotide Polymorphism (SNP) information, disease similarity information and lncRNA similarity information can be integrated in the network, which will be our further study. Moreover, the advancement of useful models in other fields such as miRNA-disease association prediction [<xref ref-type="bibr" rid="CR41">41</xref>, <xref ref-type="bibr" rid="CR42">42</xref>], drug-target interaction prediction [<xref ref-type="bibr" rid="CR43">43</xref>] and synergistic drug combination prediction [<xref ref-type="bibr" rid="CR44">44</xref>], would greatly facilitate the development of lncRNA-disease association prediction.</p></sec><sec id="Sec8"><title>Conclusion</title><p id="Par28">In this study, we proposed an effective method for predicting potential lncRNA-disease associations based on a bipartite network. Firstly, the gene-disease bipartite network was constructed based on known gene-disease associations. Then a formula of gene-disease association score was proposed to evaluate the strength of the potential associations between diseases and lncRNAs. Our method was estimated comprehensively by cross-validation, robustness analysis and case studies in comparison with other methods. The results showed that our method had higher prediction accuracy and robustness even if it was simple and easy.</p></sec><sec id="Sec9"><title>Methods</title><sec id="Sec10"><title>Data sources</title><p id="Par29">The dataset of known gene-disease associations used in this article were from the work of Yang [<xref ref-type="bibr" rid="CR27">27</xref>], including lncRNA-disease associations and protein-coding gene-disease associations. The lncRNA-disease associations contained two parts. One was from the LncRNADisease [<xref ref-type="bibr" rid="CR19">19</xref>] database included 480 experimentally confirmed associations between 118 lncRNAs and 166 diseases. The other part was from literature mining included 380 lncRNA-disease associations between 226 lncRNAs and 145 diseases. There were 578 associations between 295 lncRNAs and 214 diseases totally. Besides, protein-coding gene-disease associations from Yang&#x02019;s study [<xref ref-type="bibr" rid="CR27">27</xref>] were also incorporated into the current study. Finally, a total of 1558 gene-disease associations between 1096 genes (295 lncRNAs and 801 protein-coding genes) and 214 diseases were merged together to construct the gene-disease bipartite network (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>).</p><p id="Par30">Based on the known associations between lncRNAs and diseases, we constructed a bipartite network defined as <italic>G</italic>(<italic>X</italic>,&#x02009;<italic>Y</italic>,&#x02009;<italic>L</italic>). The <italic>X</italic> denoted a set of lncRNA nodes. The <italic>Y</italic> denoted a set of disease nodes in which the nodes were associated with the lncRNAs in <italic>X</italic>. <italic>L</italic> represented a set of edges between the nodes in <italic>X</italic> and the nodes in <italic>Y</italic>. Regarding to the associations between protein-coding genes and diseases which can provide more informative clues, these associations were further integrated into the bipartite network. Thus, the <italic>X</italic> was a family of protein-coding genes and lncRNAs. Ultimately, a bipartite network based on known protein-coding gene/lncRNA disease associations was constructed for the prediction of potential gene-disease associations.</p></sec><sec id="Sec11"><title>Disease cluster and gene cluster</title><p id="Par31">It is assumed that diseases (genes) associated with the mutual genes (diseases) are more similar [<xref ref-type="bibr" rid="CR45">45</xref>] which was exploited to predict novel gene-disease associations in our work. Therefore, as is for the gene-disease bipartite network <italic>G</italic>(<italic>X</italic>,&#x02009;<italic>Y</italic>,&#x02009;<italic>L</italic>), we defined the notion of disease cluster and gene cluster based on this assumption. For any given disease, we called the collection of its associated genes in the bipartite network as a gene cluster. Similarly, for any given gene, we called the collection of its associated diseases in the bipartite network as a disease cluster. As shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>, the gene cluster of disease <italic>d</italic> was denoted by <italic>gCluster</italic>(<italic>d</italic>) indicated by a green dashed line. The disease cluster of gene <italic>g</italic> was denoted by <italic>dCluster</italic>(<italic>g</italic>) indicated by a red dashed line. Moreover, for any node <italic>v</italic> in the network we built, the <italic>N</italic>(<italic>v</italic>) described a set of nodes linked to <italic>v</italic>. Obviously, we had <italic>N</italic>(<italic>d</italic>)&#x02009;=&#x02009;<italic>gCluster</italic>(<italic>d</italic>) and <italic>N</italic>(<italic>g</italic>)&#x02009;=&#x02009;<italic>dCluster</italic>(<italic>g</italic>).
<fig id="Fig3"><label>Fig. 3</label><caption><p>Disease cluster and gene cluster in bipartite network: The circle represented disease node, the hexagon represented gene node, the disease cluster <italic>dCluster</italic>(<italic>g</italic>) of gene <italic>g</italic> was identified by a red dotted line, the gene cluster <italic>gCluster</italic>(<italic>d</italic>) of disease <italic>d</italic> was identified by a green dotted line, and the blue line represented edge between <italic>gCluster</italic>(<italic>d</italic>) and <italic>dCluster</italic>(<italic>g</italic>)</p></caption><graphic xlink:href="12859_2020_3496_Fig3_HTML" id="MO3"/></fig></p><p id="Par32">The node similarity in the same cluster was calculated to explore the biological significance of these two kinds of clusters in the bipartite network to facilitate the application of the clusters. For this purpose, we examine the node similarity of these two kinds of clusters first in our bipartite network as follows. For any given gene <italic>g</italic> in the bipartite network, the similarity of corresponding <italic>dCluster</italic>(<italic>g</italic>) was calculated by the average similarity between any two diseases in the cluster. Analogously, the similarity of <italic>gCluster</italic>(<italic>d</italic>) for disease <italic>d</italic> was computed through the average functional similarities of any two genes in the cluster. Next, we constructed different random clusters which had the same size as the corresponding gene/disease clusters. The similarities of random clusters were calculated in the same way (see Additional&#x000a0;files&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>, <xref rid="MOESM7" ref-type="media">7</xref> and <xref rid="MOESM8" ref-type="media">8</xref>). The result of comparative functional similarities of random gene clusters with that of real gene clusters was represented in Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a. The result of comparative similarities of random disease clusters with that of real disease clusters was represented in Fig. <xref rid="Fig4" ref-type="fig">4</xref>b. As expected, the node similarities of real disease clusters were significantly higher than those of random disease clusters in the bipartite network (<italic>p</italic>-value&#x02009;=&#x02009;0.0004). It can be seen that the comparison of real gene clusters with random gene clusters had comparable results (<italic>p</italic>-value&#x02009;=&#x02009;0.0003). These results indicated that disease clusters and gene clusters really existed in our bipartite network and may have some biological significance. It is reasonable to infer that the existence of such clusters was due to the fact that nodes in the identical cluster were connected to at least one mutual node in the network. Additionally, the shortest topological distance between any two nodes in a disease cluster or a gene cluster was two, which was the minimum distance between nodes from the same side in a bipartite network. Consequently, for a potential gene-disease pair (<italic>g</italic>,&#x02009;<italic>d</italic>) whose relationship was remain unknown in the bipartite network, we explored the similarity of the disease cluster <italic>dCluster</italic>(<italic>g</italic>) and the functional similarity of the gene cluster <italic>gCluster</italic>(<italic>d</italic>) to calculate the association strength of a potential gene-disease pair (<italic>g</italic>,&#x02009;<italic>d</italic>).
<fig id="Fig4"><label>Fig. 4</label><caption><p>Cluster similarities in the bipartite network. <bold>a</bold> Comparison of functional similarities between real gene clusters and random gene clusters. <bold>b</bold> Comparison of similarities between real disease clusters and random disease clusters</p></caption><graphic xlink:href="12859_2020_3496_Fig4_HTML" id="MO4"/></fig></p></sec><sec id="Sec12"><title>Calculation of cluster association score</title><p id="Par33">Given a pair of gene-disease association (<italic>g</italic>,&#x02009;<italic>d</italic>), <italic>g</italic> and <italic>d</italic> represented a gene and a disease in the bipartite network, respectively. The cluster association score of the gene cluster corresponding to <italic>d</italic> and the disease cluster corresponding to <italic>g</italic> can be mathematically defined as follows:
<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ C\_ score\left(g,d\right)=\left|L\Big( dCluster(g), gCluster(d)\Big)\right| $$\end{document}</tex-math><mml:math id="M2" display="block"><mml:mi>C</mml:mi><mml:mo>_</mml:mo><mml:mtext mathvariant="italic">score</mml:mtext><mml:mfenced close=")" open="(" separators=","><mml:mi>g</mml:mi><mml:mi>d</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mfenced close="|" open="|" separators=","><mml:mrow><mml:mi>L</mml:mi><mml:mo stretchy="true">(</mml:mo><mml:mtext mathvariant="italic">dCluster</mml:mtext><mml:mfenced close=")" open="("><mml:mi>g</mml:mi></mml:mfenced></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">gCluster</mml:mtext><mml:mfenced close=")" open="("><mml:mi>d</mml:mi></mml:mfenced><mml:mo stretchy="true">)</mml:mo></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="12859_2020_3496_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par34">Where <italic>dCluster</italic>(<italic>g</italic>) and <italic>gCluster</italic>(<italic>d</italic>) were disease cluster of gene <italic>g</italic> and gene cluster of disease <italic>d</italic>, respectively. <italic>L</italic>(<italic>dCluster</italic>(<italic>g</italic>),&#x02009;<italic>gCluster</italic>(<italic>d</italic>)) was the edges set in which the element represented the edge between nodes in <italic>dCluster</italic>(<italic>g</italic>) and that in <italic>gCluster</italic>(<italic>d</italic>). In addition, |&#x02022;| denoted the size of the edges set. The eq. (<xref rid="Equ1" ref-type="">1</xref>) described the cluster association score of a gene-disease pair (<italic>g</italic>,&#x02009;<italic>d</italic>) is used to characterize how heavily the gene cluster was associated with the disease cluster. It was determined by the number of edges between the two clusters. For example, the value of <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic>(<italic>g</italic>,&#x02009;<italic>d</italic>) in Fig. <xref rid="Fig3" ref-type="fig">3</xref> was 7, because there were 7 connected edges between <italic>dCluster</italic>(<italic>g</italic>) and <italic>gCluster</italic>(<italic>d</italic>) which were drawn by blue lines.</p><p id="Par35">To better verify the performance of <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> in measuring the correlations between genes and diseases, we calculated <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> values of any gene-disease pairs as long as there was at least one edge between them in the bipartite network. Furthermore, according to the gene cluster and the disease cluster of each edge, we constructed random gene cluster and random disease cluster with the same size, respectively. Then we calculated <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> value based on the random gene cluster and the random disease cluster for comparison (see Additional files <xref rid="MOESM3" ref-type="media">3</xref>,<xref rid="MOESM9" ref-type="media">9</xref>). It was interesting that the results indicated that the <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> values of real clusters corresponding to the known edges were much greater than that derived from random clusters (Additional file <xref rid="MOESM1" ref-type="media">1</xref>, Fig. S1). It can be expected that the <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> can provide informative insights into the uncovering the potential disease-gene associations. As a result, the gene-disease association score was defined based on the <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> in the following section.</p></sec><sec id="Sec13"><title>Calculation of gene<bold>-</bold>disease association score</title><p id="Par36">While there was no known edge between gene <italic>g</italic> and disease <italic>d</italic> in the bipartite network, we can calculate the gene-disease association score based on the aforementioned formula of <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> (cluster association score) for gene-disease pair (<italic>g</italic>,&#x02009;<italic>d</italic>). Notably, the value of <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> was determined only by the number of connections between two clusters corresponding to the disease and the gene. It was influenced by the degrees of gene <italic>g</italic> and disease <italic>d</italic> in three different types of cases, which was exampled in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>. It was reasonable to expect that these three distinct types between two clusters may appear in the network, and all of them had a <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> value of 1. However, the association strength of the gene <italic>g</italic> and the disease <italic>d</italic> in the three conditions were obviously different. Apparently, the disease cluster corresponding to gene <italic>g</italic> and the gene cluster corresponding to disease <italic>d</italic> in Fig. <xref rid="Fig5" ref-type="fig">5</xref>a had the strongest association among three cases. Since the nodes in Fig. <xref rid="Fig5" ref-type="fig">5</xref>b and Fig. <xref rid="Fig5" ref-type="fig">5</xref>c had higher degrees, their values of <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> were equivalent to that in Fig. 5a. It was obvious that the cluster association score was a favorable method for nodes with large degrees. Therefore, considering the influence of nodes degree in the bipartite network, the association score between gene <italic>g</italic> and disease <italic>d</italic> was defined based on the <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> and the node degree as follows.
<disp-formula id="Equ2"><label>2</label><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ DG\_ score\left(g,d\right)=\left(\frac{1}{\left|N(g)\right|}+\frac{1}{\left|N(d)\right|}\right)\times C\_ score\left(g,d\right) $$\end{document}</tex-math><mml:math id="M4" display="block"><mml:mi mathvariant="italic">DG</mml:mi><mml:mo>_</mml:mo><mml:mtext mathvariant="italic">score</mml:mtext><mml:mfenced close=")" open="(" separators=","><mml:mi>g</mml:mi><mml:mi>d</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mfrac><mml:mn>1</mml:mn><mml:mfenced close="|" open="|"><mml:mrow><mml:mi>N</mml:mi><mml:mfenced close=")" open="("><mml:mi>g</mml:mi></mml:mfenced></mml:mrow></mml:mfenced></mml:mfrac><mml:mo>+</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mfenced close="|" open="|"><mml:mrow><mml:mi>N</mml:mi><mml:mfenced close=")" open="("><mml:mi>d</mml:mi></mml:mfenced></mml:mrow></mml:mfenced></mml:mfrac></mml:mrow></mml:mfenced><mml:mo>&#x000d7;</mml:mo><mml:mi>C</mml:mi><mml:mo>_</mml:mo><mml:mtext mathvariant="italic">score</mml:mtext><mml:mfenced close=")" open="(" separators=","><mml:mi>g</mml:mi><mml:mi>d</mml:mi></mml:mfenced></mml:math><graphic xlink:href="12859_2020_3496_Article_Equ2.gif" position="anchor"/></alternatives></disp-formula><fig id="Fig5"><label>Fig. 5</label><caption><p>The influence of node degree on the calculation of gene-disease association scores in the bipartite networks. The circle represented disease node, the hexagon represented gene node, the disease cluster <italic>dCluster</italic>(<italic>g</italic>) of gene <italic>g</italic> was identified by a red dotted line, the gene cluster <italic>gCluster</italic>(<italic>d</italic>) of disease <italic>d</italic> was identified by a green dotted line</p></caption><graphic xlink:href="12859_2020_3496_Fig5_HTML" id="MO5"/></fig></p><p id="Par37">Here, <italic>N</italic>(<italic>g</italic>) and <italic>N</italic>(<italic>d</italic>) represented the degrees of gene <italic>g</italic> and disease <italic>d</italic> in the bipartite network, respectively. <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic>(<italic>g</italic>,&#x02009;<italic>d</italic>) was the cluster association score which can be calculated by the formula (<xref rid="Equ1" ref-type="">1</xref>). The value of <italic>DG</italic>&#x02009;_&#x02009;<italic>score</italic>(<italic>g</italic>,&#x02009;<italic>d</italic>) reflected the association strength of gene-disease pairs with no known edges in the bipartite network.</p></sec></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec14"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12859_2020_3496_MOESM1_ESM.pdf"><caption><p><bold>Additional file 1.</bold> In this file we provide the supplementary table and figure referred to in the main text.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12859_2020_3496_MOESM2_ESM.xlsx"><caption><p><bold>Additional file 2.</bold> In this file we provide the results of prediction.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12859_2020_3496_MOESM3_ESM.docx"><caption><p><bold>Additional file 3.</bold> In this file we provide the details about how to calculate the similarity of gene cluster, the similarity of diseases cluster, the gene-disease association score as well as the procedure of leave-one-out cross validation.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12859_2020_3496_MOESM4_ESM.xlsx"><caption><p><bold>Additional file 4.</bold> In this file we provide the datasets used for cross-validation. In sheet 1 of this file the 236 lncRNA-disease associations from the work of Yang are given. In sheet 2 of this file the 1541 lncRNA-disease associations from the Lnc2Cancer 2.0 database are given.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12859_2020_3496_MOESM5_ESM.xlsx"><caption><p><bold>Additional file 5.</bold> In this file we provide the result of 1000 resampling experiments at different thresholds.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="12859_2020_3496_MOESM6_ESM.xlsx"><caption><p><bold>Additional file 6.</bold> In this file we provide the information of raw data. In sheet 1 of this file the serial numbers of genes and diseases are given. In sheet 2 of this file the edges in the bipartite network are given. In sheet 3 of this file the concrete information about the abbreviations of diseases is provided. In sheet 4 of this file the concrete information about the abbreviations of genes is provided.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="12859_2020_3496_MOESM7_ESM.xlsx"><caption><p><bold>Additional file 7.</bold> In this file we provide the result of similarities of disease clusters and random disease clusters. In sheet 1 of this file the information of diseases with ID is listed. In sheet 2 of this file the results of disease clusters corresponding to each gene are shown. In sheet 3 of this file the similarity of disease clusters corresponding to each gene and the average similarity of 10,000 random disease clusters with same size are provided. In sheet 4 of this file the average similarity of disease clusters and the average similarity of random disease clusters for each size are given.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="12859_2020_3496_MOESM8_ESM.xlsx"><caption><p><bold>Additional file 8.</bold> In this file we provide the result of functional similarity of gene clusters and random gene clusters. In sheet 1 of this file the information of genes with ID is listed. In sheet 2 of this file the result of gene clusters corresponding to each disease is given. In sheet 3 of this file the similarity of gene clusters corresponding to each disease and the average similarity of 10,000 random gene clusters with same size are shown. In sheet 4 of this file the average similarity of gene clusters and the average similarity of random gene clusters for each size are provided.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="12859_2020_3496_MOESM9_ESM.xlsx"><caption><p><bold>Additional file 9. </bold>In this file we provide the result of <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> values. For each edge in the bipartite network, we provide the disease cluster and gene cluster corresponding to the edge as well as its <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> value. In addition, we provide <italic>C</italic>&#x02009;_&#x02009;<italic>score</italic> value of random gene-disease cluster corresponding to each edge which was calculated by 1000 random experiments.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>lncRNAs</term><def><p id="Par4">Long non-coding RNAs</p></def></def-item><def-item><term>LOOCV</term><def><p id="Par5">Leave-one out cross validation</p></def></def-item><def-item><term>ncRNA</term><def><p id="Par6">Non-coding RNA</p></def></def-item><def-item><term>RWR</term><def><p id="Par7">Random walk with restart</p></def></def-item><def-item><term>ROC</term><def><p id="Par8">Receiver operating characteristic</p></def></def-item><def-item><term>TPR</term><def><p id="Par9">True positive rates</p></def></def-item><def-item><term>FPR</term><def><p id="Par10">False positive rates</p></def></def-item><def-item><term>AUC</term><def><p id="Par11">Areas under ROC curve</p></def></def-item><def-item><term>MSS</term><def><p id="Par12">Multiple survival screening</p></def></def-item><def-item><term>CRC</term><def><p id="Par13">Colorectal cancer</p></def></def-item><def-item><term>SNP</term><def><p id="Par14">Single nucleotide polymorphism</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s12859-020-3496-8.</p></sec><ack><title>Acknowledgements</title><p>Not applicable.</p></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>XLG and LG designed the study. XLG, YR, QQY, XW carried out analyses and wrote the program. XLG, YR and QQY wrote the paper. All authors read and approved the final manuscript.</p></notes><notes notes-type="funding-information"><title>Funding</title><p>This research is partly sponsored by the National Natural Science Foundation of China (No. 61672407, No. 61532014, No.61772395, No.61702397). The funding bodies did not play any roles in the design of the study, in the collection, analysis, or interpretation of data, or in writing the manuscript.</p></notes><notes notes-type="data-availability"><title>Availability of data and materials</title><p>The datasets supporting the conclusions of this article are included within the article and its additional files.</p></notes><notes id="FPar1"><title>Ethics approval and consent to participate</title><p id="Par38">Not applicable.</p></notes><notes id="FPar2"><title>Consent for publication</title><p id="Par39">Not applicable.</p></notes><notes id="FPar3" notes-type="COI-statement"><title>Competing interests</title><p id="Par40">The authors declare that they have no competing interests.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Slack</surname><given-names>FJ</given-names></name></person-group><article-title>Regulatory RNAs and the demise of "junk" DNA</article-title><source>Genome Biol</source><year>2006</year><volume>7</volume><issue>9</issue><fpage>328</fpage><pub-id pub-id-type="doi">10.1186/gb-2006-7-9-328</pub-id><?supplied-pmid 17020591?><pub-id pub-id-type="pmid">17020591</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kapranov</surname><given-names>P</given-names></name><name><surname>Cheng</surname><given-names>J</given-names></name><name><surname>Dike</surname><given-names>S</given-names></name><name><surname>Nix</surname><given-names>DA</given-names></name><name><surname>Duttagupta</surname><given-names>R</given-names></name><name><surname>Willingham</surname><given-names>AT</given-names></name><etal/></person-group><article-title>RNA maps reveal new RNA classes and a possible function for pervasive transcription</article-title><source>Science.</source><year>2007</year><volume>316</volume><issue>5830</issue><fpage>1484</fpage><lpage>1488</lpage><pub-id pub-id-type="doi">10.1126/science.1138341</pub-id><?supplied-pmid 17510325?><pub-id pub-id-type="pmid">17510325</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mercer</surname><given-names>TR</given-names></name><name><surname>Dinger</surname><given-names>ME</given-names></name><name><surname>Mattick</surname><given-names>JS</given-names></name></person-group><article-title>Long non-coding RNAs: insights into functions</article-title><source>Nat Rev Genet</source><year>2009</year><volume>10</volume><issue>3</issue><fpage>155</fpage><lpage>159</lpage><pub-id pub-id-type="doi">10.1038/nrg2521</pub-id><?supplied-pmid 19188922?><pub-id pub-id-type="pmid">19188922</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><mixed-citation publication-type="other">Guttman M, Russell P, Ingolia NT, Weissman JS, Lander ES, et al. Cell. 154(1):240&#x02013;51.</mixed-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Derrien</surname><given-names>T</given-names></name><name><surname>Johnson</surname><given-names>R</given-names></name><name><surname>Bussotti</surname><given-names>G</given-names></name><name><surname>Tanzer</surname><given-names>A</given-names></name><name><surname>Djebali</surname><given-names>S</given-names></name><name><surname>Tilgner</surname><given-names>H</given-names></name><etal/></person-group><article-title>The GENCODE v7 catalog of human long noncoding RNAs: analysis of their gene structure, evolution, and expression</article-title><source>Genome Res</source><year>2012</year><volume>22</volume><issue>9</issue><fpage>1775</fpage><lpage>1789</lpage><pub-id pub-id-type="doi">10.1101/gr.132159.111</pub-id><?supplied-pmid 22955988?><pub-id pub-id-type="pmid">22955988</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Iyer</surname><given-names>MK</given-names></name><name><surname>Niknafs</surname><given-names>YS</given-names></name><name><surname>Malik</surname><given-names>R</given-names></name><name><surname>Singhal</surname><given-names>U</given-names></name><name><surname>Sahu</surname><given-names>A</given-names></name><name><surname>Hosono</surname><given-names>Y</given-names></name><etal/></person-group><article-title>The landscape of long noncoding RNAs in the human transcriptome</article-title><source>Nat Genet</source><year>2015</year><volume>47</volume><issue>3</issue><fpage>199</fpage><lpage>208</lpage><pub-id pub-id-type="doi">10.1038/ng.3192</pub-id><?supplied-pmid 25599403?><pub-id pub-id-type="pmid">25599403</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Slack</surname><given-names>FJ</given-names></name><name><surname>Chinnaiyan</surname><given-names>AM</given-names></name></person-group><article-title>The role of non-coding RNAs in oncology</article-title><source>Cell.</source><year>2019</year><volume>179</volume><issue>5</issue><fpage>1033</fpage><lpage>1055</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2019.10.017</pub-id><?supplied-pmid 31730848?><pub-id pub-id-type="pmid">31730848</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Sun</surname><given-names>YZ</given-names></name><name><surname>Guan</surname><given-names>NN</given-names></name><name><surname>Qu</surname><given-names>J</given-names></name><name><surname>Huang</surname><given-names>ZA</given-names></name><name><surname>Zhu</surname><given-names>ZX</given-names></name><etal/></person-group><article-title>Computational models for lncRNA function prediction and functional similarity calculation</article-title><source>Brief Funct Genomics</source><year>2019</year><volume>18</volume><issue>1</issue><fpage>58</fpage><lpage>82</lpage><pub-id pub-id-type="doi">10.1093/bfgp/ely031</pub-id><?supplied-pmid 30247501?><pub-id pub-id-type="pmid">30247501</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Yan</surname><given-names>CC</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name><name><surname>You</surname><given-names>ZH</given-names></name></person-group><article-title>Long non-coding RNAs and complex diseases: from experimental results to computational models</article-title><source>Brief Bioinform</source><year>2017</year><volume>18</volume><issue>4</issue><fpage>558</fpage><lpage>576</lpage><?supplied-pmid 27345524?><pub-id pub-id-type="pmid">27345524</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cabili</surname><given-names>MN</given-names></name><name><surname>Trapnell</surname><given-names>C</given-names></name><name><surname>Goff</surname><given-names>L</given-names></name><name><surname>Koziol</surname><given-names>M</given-names></name><name><surname>Tazon-Vega</surname><given-names>B</given-names></name><name><surname>Regev</surname><given-names>A</given-names></name><etal/></person-group><article-title>Integrative annotation of human large intergenic noncoding RNAs reveals global properties and specific subclasses</article-title><source>Genes Dev</source><year>2011</year><volume>25</volume><issue>18</issue><fpage>1915</fpage><lpage>1927</lpage><pub-id pub-id-type="doi">10.1101/gad.17446611</pub-id><?supplied-pmid 21890647?><pub-id pub-id-type="pmid">21890647</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hajjari</surname><given-names>M</given-names></name><name><surname>Khoshnevisan</surname><given-names>A</given-names></name><name><surname>Shin</surname><given-names>YK</given-names></name></person-group><article-title>Molecular function and regulation of long non-coding RNAs: paradigms with potential roles in cancer</article-title><source>Tumor Biol</source><year>2014</year><volume>35</volume><issue>11</issue><fpage>10645</fpage><lpage>10663</lpage><pub-id pub-id-type="doi">10.1007/s13277-014-2636-z</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Brown</surname><given-names>CJ</given-names></name><name><surname>Ballabio</surname><given-names>A</given-names></name><name><surname>Rupert</surname><given-names>JL</given-names></name><name><surname>Lafreniere</surname><given-names>RG</given-names></name><name><surname>Grompe</surname><given-names>M</given-names></name><name><surname>Tonlorenzi</surname><given-names>R</given-names></name><etal/></person-group><article-title>A gene from the region of the human X inactivation Centre is expressed exclusively from the inactive X chromosome</article-title><source>Nature.</source><year>1991</year><volume>349</volume><issue>6304</issue><fpage>38</fpage><lpage>44</lpage><pub-id pub-id-type="doi">10.1038/349038a0</pub-id><?supplied-pmid 1985261?><pub-id pub-id-type="pmid">1985261</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bartolomei</surname><given-names>MS</given-names></name><name><surname>Zemel</surname><given-names>S</given-names></name><name><surname>Tilghman</surname><given-names>SM</given-names></name></person-group><article-title>Parental imprinting of the mouse H19 gene</article-title><source>Nature.</source><year>1991</year><volume>351</volume><issue>6322</issue><fpage>153</fpage><lpage>155</lpage><pub-id pub-id-type="doi">10.1038/351153a0</pub-id><?supplied-pmid 1709450?><pub-id pub-id-type="pmid">1709450</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kallen</surname><given-names>AN</given-names></name><name><surname>Zhou</surname><given-names>XB</given-names></name><name><surname>Xu</surname><given-names>J</given-names></name><name><surname>Qiao</surname><given-names>C</given-names></name><name><surname>Ma</surname><given-names>J</given-names></name><name><surname>Yan</surname><given-names>L</given-names></name><etal/></person-group><article-title>The imprinted H19 lncRNA antagonizes let-7 microRNAs</article-title><source>Mol Cell</source><year>2013</year><volume>52</volume><issue>1</issue><fpage>101</fpage><lpage>112</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2013.08.027</pub-id><?supplied-pmid 24055342?><pub-id pub-id-type="pmid">24055342</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gupta</surname><given-names>RA</given-names></name><name><surname>Shah</surname><given-names>N</given-names></name><name><surname>Wang</surname><given-names>KC</given-names></name><name><surname>Kim</surname><given-names>J</given-names></name><name><surname>Horlings</surname><given-names>HM</given-names></name><name><surname>Wong</surname><given-names>DJ</given-names></name><etal/></person-group><article-title>Long non-coding RNA HOTAIR reprograms chromatin state to promote cancer metastasis</article-title><source>Nature.</source><year>2010</year><volume>464</volume><issue>7291</issue><fpage>1071</fpage><lpage>1076</lpage><pub-id pub-id-type="doi">10.1038/nature08975</pub-id><?supplied-pmid 20393566?><pub-id pub-id-type="pmid">20393566</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yan</surname><given-names>X</given-names></name><name><surname>Hu</surname><given-names>Z</given-names></name><name><surname>Feng</surname><given-names>Y</given-names></name><name><surname>Hu</surname><given-names>X</given-names></name><name><surname>Yuan</surname><given-names>J</given-names></name><name><surname>Zhao</surname><given-names>SD</given-names></name><etal/></person-group><article-title>Comprehensive genomic characterization of long non-coding RNAs across human cancers</article-title><source>Cancer Cell</source><year>2015</year><volume>28</volume><issue>4</issue><fpage>529</fpage><lpage>540</lpage><pub-id pub-id-type="doi">10.1016/j.ccell.2015.09.006</pub-id><?supplied-pmid 26461095?><pub-id pub-id-type="pmid">26461095</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yang</surname><given-names>Y</given-names></name><name><surname>Chen</surname><given-names>L</given-names></name><name><surname>Gu</surname><given-names>J</given-names></name><name><surname>Zhang</surname><given-names>H</given-names></name><name><surname>Yuan</surname><given-names>J</given-names></name><name><surname>Lian</surname><given-names>Q</given-names></name><etal/></person-group><article-title>Recurrently deregulated lncRNAs in hepatocellular carcinoma</article-title><source>Nat Commun</source><year>2017</year><volume>8</volume><fpage>14421</fpage><pub-id pub-id-type="doi">10.1038/ncomms14421</pub-id><?supplied-pmid 28194035?><pub-id pub-id-type="pmid">28194035</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Quek</surname><given-names>XC</given-names></name><name><surname>Thomson</surname><given-names>DW</given-names></name><name><surname>Maag</surname><given-names>JLV</given-names></name><name><surname>Bartonicek</surname><given-names>N</given-names></name><name><surname>Signal</surname><given-names>B</given-names></name><name><surname>Clark</surname><given-names>MB</given-names></name><etal/></person-group><article-title>lncRNAdb v2. 0: expanding the reference database for functional long noncoding RNAs</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><issue>D1</issue><fpage>D168</fpage><lpage>D173</lpage><pub-id pub-id-type="doi">10.1093/nar/gku988</pub-id><?supplied-pmid 25332394?><pub-id pub-id-type="pmid">25332394</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>G</given-names></name><name><surname>Wang</surname><given-names>Z</given-names></name><name><surname>Wang</surname><given-names>D</given-names></name><name><surname>Qiu</surname><given-names>C</given-names></name><name><surname>Liu</surname><given-names>M</given-names></name><name><surname>Chen</surname><given-names>X</given-names></name><etal/></person-group><article-title>LncRNADisease: a database for long-non-coding RNA-associated diseases</article-title><source>Nucleic Acids Res</source><year>2013</year><volume>41</volume><issue>D1</issue><fpage>D983</fpage><lpage>D986</lpage><pub-id pub-id-type="doi">10.1093/nar/gks1099</pub-id><?supplied-pmid 23175614?><pub-id pub-id-type="pmid">23175614</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ning</surname><given-names>S</given-names></name><name><surname>Zhang</surname><given-names>J</given-names></name><name><surname>Wang</surname><given-names>P</given-names></name><name><surname>Zhi</surname><given-names>H</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name><name><surname>Liu</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Lnc2Cancer: a manually curated database of experimentally supported lncRNAs associated with various human cancers</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>44</volume><issue>D1</issue><fpage>D980</fpage><lpage>D985</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1094</pub-id><?supplied-pmid 26481356?><pub-id pub-id-type="pmid">26481356</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Yan</surname><given-names>GY</given-names></name></person-group><article-title>Novel human lncRNA&#x02013;disease association inference based on lncRNA expression profiles</article-title><source>Bioinformatics.</source><year>2013</year><volume>29</volume><issue>20</issue><fpage>2617</fpage><lpage>2624</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btt426</pub-id><?supplied-pmid 24002109?><pub-id pub-id-type="pmid">24002109</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Yan</surname><given-names>CC</given-names></name><name><surname>Luo</surname><given-names>C</given-names></name><name><surname>Ji</surname><given-names>W</given-names></name><name><surname>Zhang</surname><given-names>Y</given-names></name><name><surname>Dai</surname><given-names>Q</given-names></name></person-group><article-title>Constructing lncRNA functional similarity network based on lncRNA-disease associations and disease semantic similarity</article-title><source>Sci Rep</source><year>2015</year><volume>5</volume><fpage>11338</fpage><pub-id pub-id-type="doi">10.1038/srep11338</pub-id><?supplied-pmid 26061969?><pub-id pub-id-type="pmid">26061969</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lan</surname><given-names>W</given-names></name><name><surname>Li</surname><given-names>M</given-names></name><name><surname>Zhao</surname><given-names>K</given-names></name><name><surname>Liu</surname><given-names>J</given-names></name><name><surname>Wu</surname><given-names>F-X</given-names></name><name><surname>Pan</surname><given-names>Y</given-names></name><etal/></person-group><article-title>LDAP: a web server for lncRNA-disease association prediction</article-title><source>Bioinformatics.</source><year>2017</year><volume>33</volume><issue>3</issue><fpage>458</fpage><lpage>460</lpage><?supplied-pmid 28172495?><pub-id pub-id-type="pmid">28172495</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guo</surname><given-names>X</given-names></name><name><surname>Gao</surname><given-names>L</given-names></name><name><surname>Liao</surname><given-names>Q</given-names></name><name><surname>Xiao</surname><given-names>H</given-names></name><name><surname>Ma</surname><given-names>X</given-names></name><name><surname>Yang</surname><given-names>X</given-names></name><etal/></person-group><article-title>Long non-coding RNAs function annotation: a global prediction method based on bi-colored networks</article-title><source>Nucleic Acids Res</source><year>2013</year><volume>41</volume><issue>2</issue><fpage>e35</fpage><pub-id pub-id-type="doi">10.1093/nar/gks967</pub-id><?supplied-pmid 23132350?><pub-id pub-id-type="pmid">23132350</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sun</surname><given-names>J</given-names></name><name><surname>Shi</surname><given-names>H</given-names></name><name><surname>Wang</surname><given-names>Z</given-names></name><name><surname>Zhang</surname><given-names>C</given-names></name><name><surname>Liu</surname><given-names>L</given-names></name><name><surname>Wang</surname><given-names>L</given-names></name><etal/></person-group><article-title>Inferring novel lncRNA&#x02013;disease associations based on a random walk model of a lncRNA functional similarity network</article-title><source>Mol BioSyst</source><year>2014</year><volume>10</volume><issue>8</issue><fpage>2074</fpage><lpage>2081</lpage><pub-id pub-id-type="doi">10.1039/C3MB70608G</pub-id><?supplied-pmid 24850297?><pub-id pub-id-type="pmid">24850297</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>You</surname><given-names>ZH</given-names></name><name><surname>Yan</surname><given-names>GY</given-names></name><name><surname>Gong</surname><given-names>DW</given-names></name></person-group><article-title>IRWRLDA: improved random walk with restart for lncRNA-disease association prediction</article-title><source>Oncotarget.</source><year>2016</year><volume>7</volume><issue>36</issue><fpage>57919</fpage><lpage>57931</lpage><?supplied-pmid 27517318?><pub-id pub-id-type="pmid">27517318</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yang</surname><given-names>X</given-names></name><name><surname>Gao</surname><given-names>L</given-names></name><name><surname>Guo</surname><given-names>X</given-names></name><name><surname>Shi</surname><given-names>X</given-names></name><name><surname>Wu</surname><given-names>H</given-names></name><name><surname>Song</surname><given-names>F</given-names></name><etal/></person-group><article-title>A network based method for analysis of lncRNA-disease associations and prediction of lncRNAs implicated in diseases</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><issue>1</issue><fpage>e87797</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0087797</pub-id><?supplied-pmid 24498199?><pub-id pub-id-type="pmid">24498199</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name></person-group><article-title>KATZLDA: KATZ measure for the lncRNA-disease association prediction</article-title><source>Sci Rep</source><year>2015</year><volume>5</volume><issue>1</issue><fpage>16840</fpage><pub-id pub-id-type="doi">10.1038/srep16840</pub-id><?supplied-pmid 26577439?><pub-id pub-id-type="pmid">26577439</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>MX</given-names></name><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Chen</surname><given-names>G</given-names></name><name><surname>Cui</surname><given-names>QH</given-names></name><name><surname>Yan</surname><given-names>GY</given-names></name></person-group><article-title>A computational framework to infer human disease-associated long noncoding RNAs</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><issue>1</issue><fpage>e84408</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0084408</pub-id><?supplied-pmid 24392133?><pub-id pub-id-type="pmid">24392133</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gao</surname><given-names>Y</given-names></name><name><surname>Wang</surname><given-names>P</given-names></name><name><surname>Wang</surname><given-names>Y</given-names></name><name><surname>Ma</surname><given-names>X</given-names></name><name><surname>Zhi</surname><given-names>H</given-names></name><name><surname>Zhou</surname><given-names>D</given-names></name><etal/></person-group><article-title>Lnc2Cancer v2. 0: updated database of experimentally supported long non-coding RNAs in human cancers</article-title><source>Nucleic Acids Res</source><year>2019</year><volume>47</volume><issue>D1</issue><fpage>D1028</fpage><lpage>D1033</lpage><pub-id pub-id-type="doi">10.1093/nar/gky1096</pub-id><?supplied-pmid 30407549?><pub-id pub-id-type="pmid">30407549</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>J</given-names></name><name><surname>Lenferink</surname><given-names>AEG</given-names></name><name><surname>Deng</surname><given-names>Y</given-names></name><name><surname>Collins</surname><given-names>C</given-names></name><name><surname>Cui</surname><given-names>Q</given-names></name><name><surname>Purisima</surname><given-names>EO</given-names></name><etal/></person-group><article-title>Corrigendum: identification of high-quality cancer prognostic markers and metastasis network modules</article-title><source>Nat Commun</source><year>2012</year><volume>3</volume><fpage>655</fpage><pub-id pub-id-type="doi">10.1038/ncomms1400</pub-id><?supplied-pmid 4273681?><pub-id pub-id-type="pmid">4273681</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xue</surname><given-names>Y</given-names></name><name><surname>Ma</surname><given-names>G</given-names></name><name><surname>Gu</surname><given-names>D</given-names></name><name><surname>Zhu</surname><given-names>L</given-names></name><name><surname>Hua</surname><given-names>Q</given-names></name><name><surname>Du</surname><given-names>M</given-names></name><etal/></person-group><article-title>Genome-wide analysis of long noncoding RNA signature in human colorectal cancer</article-title><source>Gene.</source><year>2015</year><volume>556</volume><issue>2</issue><fpage>227</fpage><lpage>234</lpage><pub-id pub-id-type="doi">10.1016/j.gene.2014.11.060</pub-id><?supplied-pmid 25456707?><pub-id pub-id-type="pmid">25456707</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Han</surname><given-names>D</given-names></name><name><surname>Wang</surname><given-names>M</given-names></name><name><surname>Ma</surname><given-names>N</given-names></name><name><surname>Xu</surname><given-names>Y</given-names></name><name><surname>Jiang</surname><given-names>Y</given-names></name><name><surname>Gao</surname><given-names>X</given-names></name></person-group><article-title>Long noncoding RNAs: novel players in colorectal cancer</article-title><source>Cancer Lett</source><year>2015</year><volume>361</volume><issue>1</issue><fpage>13</fpage><lpage>21</lpage><pub-id pub-id-type="doi">10.1016/j.canlet.2015.03.002</pub-id><?supplied-pmid 25754818?><pub-id pub-id-type="pmid">25754818</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhou</surname><given-names>M</given-names></name><name><surname>Hu</surname><given-names>L</given-names></name><name><surname>Zhang</surname><given-names>Z</given-names></name><name><surname>Wu</surname><given-names>N</given-names></name><name><surname>Sun</surname><given-names>J</given-names></name><name><surname>Su</surname><given-names>J</given-names></name></person-group><article-title>Recurrence-associated long non-coding RNA signature for determining the risk of recurrence in patients with colon cancer</article-title><source>Mol Ther-Nucleic Acids</source><year>2018</year><volume>12</volume><fpage>518</fpage><lpage>529</lpage><pub-id pub-id-type="doi">10.1016/j.omtn.2018.06.007</pub-id><?supplied-pmid 30195788?><pub-id pub-id-type="pmid">30195788</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>Y</given-names></name><name><surname>Jiang</surname><given-names>J</given-names></name><name><surname>Zhao</surname><given-names>M</given-names></name><name><surname>Luo</surname><given-names>X</given-names></name><name><surname>Liang</surname><given-names>Z</given-names></name><name><surname>Zhen</surname><given-names>Y</given-names></name><etal/></person-group><article-title>microRNA-374a suppresses colon cancer progression by directly reducing CCND1 to inactivate the PI3K/AKT pathway</article-title><source>Oncotarget.</source><year>2016</year><volume>7</volume><issue>27</issue><fpage>41306</fpage><lpage>41319</lpage><?supplied-pmid 27191497?><pub-id pub-id-type="pmid">27191497</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cui</surname><given-names>C</given-names></name><name><surname>Zhai</surname><given-names>D</given-names></name><name><surname>Cai</surname><given-names>L</given-names></name><name><surname>Duan</surname><given-names>Q</given-names></name><name><surname>Xie</surname><given-names>L</given-names></name><name><surname>Yu</surname><given-names>J</given-names></name></person-group><article-title>Long noncoding RNA HEIH promotes colorectal Cancer tumorigenesis via counteracting miR-939&#x02013;mediated transcriptional repression of Bcl-xL</article-title><source>Cancer Res Treat</source><year>2018</year><volume>50</volume><issue>3</issue><fpage>992</fpage><lpage>1008</lpage><pub-id pub-id-type="doi">10.4143/crt.2017.226</pub-id><?supplied-pmid 29081216?><pub-id pub-id-type="pmid">29081216</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>MD</given-names></name><name><surname>Qi</surname><given-names>P</given-names></name><name><surname>Weng</surname><given-names>WW</given-names></name><name><surname>Shen</surname><given-names>XH</given-names></name><name><surname>Ni</surname><given-names>SJ</given-names></name><name><surname>Dong</surname><given-names>L</given-names></name><etal/></person-group><article-title>Long non-coding RNA LSINCT5 predicts negative prognosis and exhibits oncogenic activity in gastric cancer</article-title><source>Medicine.</source><year>2014</year><volume>93</volume><issue>28</issue><fpage>e303</fpage><pub-id pub-id-type="doi">10.1097/MD.0000000000000303</pub-id><?supplied-pmid 25526476?><pub-id pub-id-type="pmid">25526476</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Montes</surname><given-names>M</given-names></name><name><surname>Nielsen</surname><given-names>MM</given-names></name><name><surname>Maglieri</surname><given-names>G</given-names></name><name><surname>Jacobsen</surname><given-names>A</given-names></name><name><surname>H&#x000f8;jfeldt</surname><given-names>J</given-names></name><name><surname>Agrawal-Singh</surname><given-names>S</given-names></name><etal/></person-group><article-title>The lncRNA MIR31HG regulates p16 INK4A expression to modulate senescence</article-title><source>Nat Commun</source><year>2015</year><volume>6</volume><fpage>6967</fpage><pub-id pub-id-type="doi">10.1038/ncomms7967</pub-id><?supplied-pmid 25908244?><pub-id pub-id-type="pmid">25908244</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>S</given-names></name><name><surname>Fan</surname><given-names>W</given-names></name><name><surname>Wan</surname><given-names>B</given-names></name><name><surname>Tu</surname><given-names>M</given-names></name><name><surname>Jin</surname><given-names>F</given-names></name><name><surname>Liu</surname><given-names>F</given-names></name><etal/></person-group><article-title>Characterization of long noncoding RNA and messenger RNA signatures in melanoma tumorigenesis and metastasis</article-title><source>PLoS One</source><year>2017</year><volume>12</volume><issue>7</issue><fpage>e0181129</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0181129</pub-id><?supplied-pmid 28683105?><pub-id pub-id-type="pmid">28683105</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>V&#x000ed;zkeleti</surname><given-names>L</given-names></name><name><surname>Ecsedi</surname><given-names>S</given-names></name><name><surname>R&#x000e1;kosy</surname><given-names>Z</given-names></name><name><surname>Orosz</surname><given-names>A</given-names></name><name><surname>L&#x000e1;z&#x000e1;r</surname><given-names>V</given-names></name><name><surname>Emri</surname><given-names>G</given-names></name><etal/></person-group><article-title>The role of CCND1 alterations during the progression of cutaneous malignant melanoma</article-title><source>Tumor Biol</source><year>2012</year><volume>33</volume><issue>6</issue><fpage>2189</fpage><lpage>2199</lpage><pub-id pub-id-type="doi">10.1007/s13277-012-0480-6</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Wang</surname><given-names>L</given-names></name><name><surname>Qu</surname><given-names>J</given-names></name><name><surname>Guan</surname><given-names>NN</given-names></name><name><surname>Li</surname><given-names>JQ</given-names></name></person-group><article-title>Predicting miRNA&#x02013;disease association based on inductive matrix completion</article-title><source>Bioinformatics.</source><year>2018</year><volume>34</volume><issue>24</issue><fpage>4256</fpage><lpage>4265</lpage><?supplied-pmid 29939227?><pub-id pub-id-type="pmid">29939227</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Xie</surname><given-names>D</given-names></name><name><surname>Zhao</surname><given-names>Q</given-names></name><name><surname>You</surname><given-names>ZH</given-names></name></person-group><article-title>MicroRNAs and complex diseases: from experimental results to computational models</article-title><source>Brief Bioinform</source><year>2019</year><volume>20</volume><issue>2</issue><fpage>515</fpage><lpage>539</lpage><pub-id pub-id-type="doi">10.1093/bib/bbx130</pub-id><?supplied-pmid 29045685?><pub-id pub-id-type="pmid">29045685</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Yan</surname><given-names>CC</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name><name><surname>Dai</surname><given-names>F</given-names></name><name><surname>Yin</surname><given-names>J</given-names></name><etal/></person-group><article-title>Drug&#x02013;target interaction prediction: databases, web servers and computational models</article-title><source>Brief Bioinform</source><year>2016</year><volume>17</volume><issue>4</issue><fpage>696</fpage><lpage>712</lpage><pub-id pub-id-type="doi">10.1093/bib/bbv066</pub-id><?supplied-pmid 26283676?><pub-id pub-id-type="pmid">26283676</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Ren</surname><given-names>B</given-names></name><name><surname>Chen</surname><given-names>M</given-names></name><name><surname>Wang</surname><given-names>Q</given-names></name><name><surname>Zhang</surname><given-names>L</given-names></name><name><surname>Yan</surname><given-names>G</given-names></name></person-group><article-title>NLLSS: predicting synergistic drug combinations based on semi-supervised learning</article-title><source>PLoS Comput Biol</source><year>2016</year><volume>12</volume><issue>7</issue><fpage>e1004975</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1004975</pub-id><?supplied-pmid 27415801?><pub-id pub-id-type="pmid">27415801</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Goh</surname><given-names>K-I</given-names></name><name><surname>Cusick</surname><given-names>ME</given-names></name><name><surname>Valle</surname><given-names>D</given-names></name><name><surname>Childs</surname><given-names>B</given-names></name><name><surname>Vidal</surname><given-names>M</given-names></name><name><surname>Barab&#x000e1;si</surname><given-names>A-L</given-names></name></person-group><article-title>The human disease network</article-title><source>Proc Natl Acad Sci</source><year>2007</year><volume>104</volume><issue>21</issue><fpage>8685</fpage><lpage>8690</lpage><pub-id pub-id-type="doi">10.1073/pnas.0701361104</pub-id><?supplied-pmid 17502601?><pub-id pub-id-type="pmid">17502601</pub-id></element-citation></ref></ref-list></back></article>