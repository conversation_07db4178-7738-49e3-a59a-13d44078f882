<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Genome Biol</journal-id><journal-id journal-id-type="iso-abbrev">Genome Biol</journal-id><journal-title-group><journal-title>Genome Biology</journal-title></journal-title-group><issn pub-type="ppub">1474-7596</issn><issn pub-type="epub">1474-760X</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7265231</article-id><article-id pub-id-type="publisher-id">2048</article-id><article-id pub-id-type="doi">10.1186/s13059-020-02048-6</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>Systematic assessment of tissue dissociation and storage biases in single-cell and single-nucleus RNA-seq workflows</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Denisenko</surname><given-names>Elena</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Guo</surname><given-names>Belinda B.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Jones</surname><given-names>Matthew</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Hou</surname><given-names>Rui</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>de Kock</surname><given-names>Leanne</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Lassmann</surname><given-names>Timo</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Poppe</surname><given-names>Daniel</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Cl&#x000e9;ment</surname><given-names>Olivier</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Simmons</surname><given-names>Rebecca K.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Lister</surname><given-names>Ryan</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0003-4543-1675</contrib-id><name><surname>Forrest</surname><given-names>Alistair R. R.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="GRID">grid.1012.2</institution-id><institution-id institution-id-type="ISNI">0000 0004 1936 7910</institution-id><institution>Harry Perkins Institute of Medical Research, QEII Medical Centre and Centre for Medical Research, </institution><institution>the University of Western Australia, </institution></institution-wrap>PO Box 7214, 6 Verdun Street, Nedlands, Perth, Western Australia 6009 Australia </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="GRID">grid.1012.2</institution-id><institution-id institution-id-type="ISNI">0000 0004 1936 7910</institution-id><institution>Telethon Kids Institute, Perth&#x02019;s Children Hospital, </institution><institution>the University of Western Australia, </institution></institution-wrap>15 Hospital Avenue, Nedlands, Perth, Western Australia 6009 Australia </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="GRID">grid.1012.2</institution-id><institution-id institution-id-type="ISNI">0000 0004 1936 7910</institution-id><institution>Australian Research Council Centre of Excellence in Plant Energy Biology, School of Molecular Sciences, </institution><institution>the University of Western Australia, </institution></institution-wrap>35 Stirling Hwy, Crawley, Perth, Western Australia 6009 Australia </aff></contrib-group><pub-date pub-type="epub"><day>2</day><month>6</month><year>2020</year></pub-date><pub-date pub-type="pmc-release"><day>2</day><month>6</month><year>2020</year></pub-date><pub-date pub-type="collection"><year>2020</year></pub-date><volume>21</volume><elocation-id>130</elocation-id><history><date date-type="received"><day>9</day><month>1</month><year>2020</year></date><date date-type="accepted"><day>15</day><month>5</month><year>2020</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2020</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is licensed under a Creative Commons Attribution 4.0 International License, which permits use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons licence, and indicate if changes were made. The images or other third party material in this article are included in the article's Creative Commons licence, unless indicated otherwise in a credit line to the material. If material is not included in the article's Creative Commons licence and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this licence, visit <ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated in a credit line to the data.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Single-cell RNA sequencing has been widely adopted to estimate the cellular composition of heterogeneous tissues and obtain transcriptional profiles of individual cells. Multiple approaches for optimal sample dissociation and storage of single cells have been proposed as have single-nuclei profiling methods. What has been lacking is a systematic comparison of their relative biases and benefits.</p></sec><sec><title>Results</title><p id="Par2">Here, we compare gene expression and cellular composition of single-cell suspensions prepared from adult mouse kidney using two tissue dissociation protocols. For each sample, we also compare fresh cells to cryopreserved and methanol-fixed cells. Lastly, we compare this single-cell data to that generated using three single-nucleus RNA sequencing workflows. Our data confirms prior reports that digestion on ice avoids the stress response observed with 37&#x02009;&#x000b0;C dissociation. It also reveals cell types more abundant either in the cold or warm dissociations that may represent populations that require gentler or harsher conditions to be released intact. For cell storage, cryopreservation of dissociated cells results in a major loss of epithelial cell types; in contrast, methanol fixation maintains the cellular composition but suffers from ambient RNA leakage. Finally, cell type composition differences are observed between single-cell and single-nucleus RNA sequencing libraries. In particular, we note an underrepresentation of T, B, and NK lymphocytes in the single-nucleus libraries.</p></sec><sec><title>Conclusions</title><p id="Par3">Systematic comparison of recovered cell types and their transcriptional profiles across the workflows has highlighted protocol-specific biases and thus enables researchers starting single-cell experiments to make an informed choice.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Single-cell transcriptomics</kwd><kwd>RNA-seq</kwd><kwd>scRNA-seq</kwd><kwd>snRNA-seq</kwd><kwd>10x Genomics</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100000925</institution-id><institution>National Health and Medical Research Council</institution></institution-wrap></funding-source><award-id>APP1154524</award-id><principal-award-recipient><name><surname>Forrest</surname><given-names>Alistair R. R.</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2020</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par4">Single-cell RNA sequencing (scRNA-seq) is an increasingly powerful technology that enables analysis of gene expression in individual cells. ScRNA-seq has been recently used to study organism development [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR3">3</xref>], normal tissues [<xref ref-type="bibr" rid="CR4">4</xref>&#x02013;<xref ref-type="bibr" rid="CR6">6</xref>], cancer [<xref ref-type="bibr" rid="CR7">7</xref>&#x02013;<xref ref-type="bibr" rid="CR10">10</xref>], and other diseases [<xref ref-type="bibr" rid="CR11">11</xref>, <xref ref-type="bibr" rid="CR12">12</xref>]. These studies have shed light on tissue heterogeneity and provided previously inaccessible insights into tissue functioning.</p><p id="Par5">Advances in high-throughput droplet-based microfluidics technologies have facilitated analysis of thousands of cells in parallel [<xref ref-type="bibr" rid="CR13">13</xref>&#x02013;<xref ref-type="bibr" rid="CR15">15</xref>], and Chromium from 10x Genomics has become a widely used commercial platform [<xref ref-type="bibr" rid="CR15">15</xref>]. Multiple tissue preparation protocols are compatible with Chromium, but the protocol of choice should ideally maintain RNA integrity and cell composition of the original tissue.</p><p id="Par6">Solid tissues need to be dissociated to release individual cells suitable for 10x Genomics Chromium scRNA-seq. However, optimal dissociation needs to achieve a balance between releasing cell types that are difficult to dissociate while avoiding damage to those that are fragile. Tissue dissociation is most commonly conducted using enzymes which require incubation at 37&#x02009;&#x000b0;C for variable times based on tissue type. At this temperature, the cell transcriptional machinery is active; hence, gene expression can be altered in response to the dissociation and other environmental stresses [<xref ref-type="bibr" rid="CR16">16</xref>, <xref ref-type="bibr" rid="CR17">17</xref>]. A recent alternative approach minimizing this artifact uses cold-active protease to conduct tissue dissociation on ice [<xref ref-type="bibr" rid="CR18">18</xref>]. Alternatively, single-nucleus RNA sequencing protocols (snRNA-seq) use much harsher conditions to release nuclei from tissue and can be applied to snap frozen samples, thus avoiding many of the dissociation-related artifacts [<xref ref-type="bibr" rid="CR19">19</xref>, <xref ref-type="bibr" rid="CR20">20</xref>]. Single-nuclei methods should also permit profiling of nuclei from large cells (&#x0003e;&#x02009;40&#x02009;&#x003bc;m) that do not fit through the microfluidics.</p><p id="Par7">Additional restrictions and challenges are faced by complex experimental designs where specimens cannot be processed immediately. In this case, samples need to be preserved either as an intact tissue or in a dissociated form as a single-cell suspension. Each of the approaches mentioned above introduces specific biases and artifacts that can manifest themselves in altered transcriptional profiles or altered representation of cell types. These biases need to be considered when designing and analyzing data from a single-cell experiment; however, they are still incompletely understood.</p><p id="Par8">Some of the artifacts have been investigated in recent studies comparing single-cell profiles of methanol-fixed and live cells [<xref ref-type="bibr" rid="CR21">21</xref>, <xref ref-type="bibr" rid="CR22">22</xref>], cryopreserved and live cells [<xref ref-type="bibr" rid="CR22">22</xref>, <xref ref-type="bibr" rid="CR23">23</xref>], single-cell and single-nucleus protocols [<xref ref-type="bibr" rid="CR24">24</xref>&#x02013;<xref ref-type="bibr" rid="CR26">26</xref>], or tissue dissociation using cold-active protease and traditional digestion at 37&#x02009;&#x000b0;C [<xref ref-type="bibr" rid="CR18">18</xref>]. However, these assessments were performed in different tissues under different conditions and lack extensive comparison to bulk data.</p><p id="Par9">Here, we performed a comprehensive study in healthy adult mouse kidneys using 10x Genomics Chromium workflows for scRNA-seq and snRNA-seq, along with bulk RNA-seq of undissociated and dissociated tissue. We compare and contrast two tissue dissociation protocols (digestion at 37&#x02009;&#x000b0;C, further referred to as warm dissociation, or with cold-active protease, further referred to as cold dissociation), two single-cell suspension preservation methods (methanol fixation and cryopreservation) and three single-nuclei isolation protocols (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>). A total of 77,656 single-cell, 98,303 single-nucleus, and 15 bulk RNA-seq profiles were generated and made publicly available (GSE141115). Our dissection of artifacts associated with each of the approaches will serve as a valuable resource to aid interpretation of single-cell and single-nucleus gene expression data and help to guide the choice of experimental workflows.
<fig id="Fig1"><label>Fig. 1</label><caption><p>Overview of experiments performed in this study. All experiments were carried out in biological triplicate using three kidneys from three different mice. <bold>a</bold> 37&#x02009;&#x000b0;C dissociation used the Multi-tissue dissociation kit 2 from Miltenyi Biotec. <bold>b</bold> Cold dissociation was carried out on ice using <italic>B. Licheniformis</italic> protease. In <bold>a</bold> and <bold>b</bold>, methanol-fixed samples used 80% MeOH at &#x02212;&#x02009;20&#x02009;&#x000b0;C and then were stored at &#x02212;&#x02009;80&#x02009;&#x000b0;C. Cryopreservation was carried out using 50% FBS, 40% RPMI-1640, 10% and DMSO with gradient cooling to &#x02212;&#x02009;80&#x02009;&#x000b0;C then stored in liquid nitrogen. <bold>c&#x02013;e</bold> Whole kidneys were flash frozen using an isopentane bath &#x02212;&#x02009;30&#x02009;&#x000b0;C and then stored at &#x02212;&#x02009;80&#x02009;&#x000b0;C. Three different nuclei preparation methods were tested using either fluorescently activated nuclei sorting (FANS) or a sucrose gradient to enrich for singlet nuclei. <bold>f</bold> Bulk RNA-seq was carried out using the NEBNext Ultra II RNA Library Kit for Illumina with rRNA depletion or NEBNext Poly(A) mRNA isolation module. See &#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;&#x000a0;for more details </p></caption><graphic xlink:href="13059_2020_2048_Fig1_HTML" id="MO1"/></fig></p></sec><sec id="Sec2"><title>Results</title><sec id="Sec3"><title>Comparison of tissue dissociation protocols</title><p id="Par10">In the first series of experiments, we set out to compare two tissue dissociation protocols using kidneys from adult male C57BL/6J mice. Kidneys were dissociated at 37&#x02009;&#x000b0;C using a commercial Miltenyi Multi Tissue Dissociation Kit 2 or on ice using a cold-active protease from <italic>Bacillus licheniformis</italic> (&#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a, b). Aliquots of single-cell suspensions were profiled using 10x Genomics Chromium scRNA-seq and a bulk RNA-seq protocol (&#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a, b). All experiments were performed in triplicate, and data were processed as described in &#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;.</p><sec id="Sec4"><title>Warm tissue dissociation induces stress response</title><p id="Par11">Bulk RNA-seq profiling of single-cell suspensions revealed induction of stress response genes in warm-dissociated samples. Differential expression analysis identified 71 genes with higher expression in warm-dissociated kidneys and 5 genes with higher expression in cold-dissociated kidneys (logFC &#x0003e;&#x02009;2, FDR&#x02009;&#x0003c;&#x02009;0.05, edgeR exact test [<xref ref-type="bibr" rid="CR27">27</xref>], Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>). Gene ontology analysis with ToppGene [<xref ref-type="bibr" rid="CR28">28</xref>] reported &#x0201c;regulation of cell death&#x0201d; as the top significantly enriched biological process for the genes more highly expressed in warm-dissociated kidneys (an overlap of 22 genes, FDR&#x02009;=&#x02009;1.7E&#x02212;7, see Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>). Genes with the highest logFC values (&#x0003e;&#x02009;4) included immediate-early genes <italic>Fosb</italic>, <italic>Fos</italic>, <italic>Jun</italic>, <italic>Junb</italic>, <italic>Atf3</italic>, and <italic>Egr1</italic> and heat shock proteins <italic>Hspa1a</italic> and <italic>Hspa1b</italic> (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>a). These findings from bulk RNA-seq confirm the original observations of Adam et al. [<xref ref-type="bibr" rid="CR18">18</xref>] that warm tissue dissociation induces substantial stress-response-related changes.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Comparison of cold and warm tissue dissociation protocols. <bold>a</bold> Bulk RNA-seq profiles of dissociated kidneys. GeTMM-normalized counts [<xref ref-type="bibr" rid="CR29">29</xref>] were averaged across three biological replicates and log2-transformed after adding a pseudo count of 1. DEGs with FDR&#x02009;&#x0003c;&#x02009;0.05 and logFC threshold of 2 (edgeR exact test [<xref ref-type="bibr" rid="CR27">27</xref>]) are shown as red and blue dots; protein-coding genes with logFC &#x0003e;&#x02009;4 are labelled. <bold>b</bold> Number of differentially expressed genes (DEGs) between cold- and warm-dissociated scRNA-seq libraries. Calculated for each cell type separately using Wilcoxon test in Seurat [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;0.5, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05. Numbers on the right side of the plot indicate cell population size. <bold>c</bold> Stress score &#x02013; an expression score for a set of 17 stress-response-related genes (<italic>Fosb</italic>, <italic>Fos</italic>, <italic>Jun</italic>, <italic>Junb</italic>, <italic>Jund</italic>, <italic>Atf3</italic>, <italic>Egr1</italic>, <italic>Hspa1a</italic>, <italic>Hspa1b</italic>, <italic>Hsp90ab1</italic>, <italic>Hspa8</italic>, <italic>Hspb1</italic>, <italic>Ier3</italic>, <italic>Ier2</italic>, <italic>Btg1</italic>, <italic>Btg2</italic>, <italic>Dusp1</italic>). Calculated as average gene expression level of these genes subtracted by averaged expression of randomly selected control genes and then averaged for cell types. Significance was calculated in a Monte-Carlo procedure with 1000 randomly selected gene sets of the same size, asterisks denote <italic>p</italic> value &#x0003c;&#x02009;0.01. <bold>d</bold> Expression and detection rates of differentially expressed genes commonly induced in warm-dissociated samples (differentially expressed in at least four cell types). <bold>e</bold> Cell type composition of freshly profiled scRNA-seq libraries. Three biological replicates are shown per condition. Asterisks denote two-sided chi-square test <italic>p</italic> value &#x0003c;&#x02009;0.001. In <bold>b&#x02013;d</bold>, podocytes and transitional cells were excluded due to low cell numbers. aLOH: ascending loop of Henle; CD_IC: intercalated cells of collecting duct; CD_PC: principal cells of collecting duct; CNT: connecting tubule; DCT: distal convoluted tubule; PT: proximal tubule</p></caption><graphic xlink:href="13059_2020_2048_Fig2_HTML" id="MO2"/></fig></p></sec><sec id="Sec5"><title>Single-cell sequencing reveals heterogeneous stress response across cell populations</title><p id="Par12">We next characterized differences between the two tissue dissociation protocols by scRNA-seq profiling of fresh cell suspensions (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a, b). This dataset comprised 23,108 cells, including 11,851 cells from cold- and 11,257 cells from warm-dissociated kidneys (&#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;). Cells were classified into 15 cell types using scMatch [<xref ref-type="bibr" rid="CR31">31</xref>] by comparing their expression to reference expression profiles from three previous mouse kidney studies [<xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR32">32</xref>, <xref ref-type="bibr" rid="CR33">33</xref>], followed by gene signature-based refinement (&#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1&#x02013;3, Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>).</p><p id="Par13">Differential expression analysis identified 64 genes more highly expressed in warm-dissociated libraries in at least one cell type (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b, Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>) and gene ontology analysis again reported &#x0201c;regulation of cell death&#x0201d; as one of the top significantly enriched terms (an overlap of 23 genes, FDR&#x02009;=&#x02009;3.9E&#x02212;7, Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>). The genes most commonly overexpressed across cell populations are shown in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>d and include immediate-early response genes such as <italic>Junb</italic> and <italic>Jund</italic> (differentially expressed in seven cell types) and <italic>Jun</italic> and <italic>Fos</italic> (differentially expressed in five cell types).</p><p id="Par14">Notably, the numbers of differentially expressed genes varied among cell types (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b), suggesting that cell types respond differently to warm tissue dissociation. To quantify these differences, we selected a set of 17 known stress-response-related genes that were induced in the warm-dissociated samples (<italic>Fosb</italic>, <italic>Fos</italic>, <italic>Jun</italic>, <italic>Junb</italic>, <italic>Jund</italic>, <italic>Atf3</italic>, <italic>Egr1</italic>, <italic>Hspa1a</italic>, <italic>Hspa1b</italic>, <italic>Hsp90ab1</italic>, <italic>Hspa8</italic>, <italic>Hspb1</italic>, <italic>Ier3</italic>, <italic>Ier2</italic>, <italic>Btg1</italic>, <italic>Btg2</italic>, <italic>Dusp1</italic>) and used them to calculate a stress score (see &#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;). Figure&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>c shows that significantly high stress scores were detected only in warm-dissociated samples in eight out of 14 cell types. Taken together, these results highlight that certain cell types, such as immune and endothelial cells, are particularly sensitive to warm tissue dissociation.</p><p id="Par15">In contrast to the 64 genes with higher expression in the warm dissociation, only 20 genes had higher expression in the cold-dissociated cell populations, and only five of them (<italic>Hbb-bs</italic>, <italic>Hba-a1</italic>, <italic>Hba-a2</italic>, <italic>mt-Co1</italic>, <italic>Malat1</italic>) were identified in at least two cell types (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b, Additional&#x000a0;file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>). We note the levels of hemoglobin transcripts suggest contamination from erythrocytes is higher in the samples dissociated on ice.</p></sec><sec id="Sec6"><title>Cell composition differs between two tissue dissociation protocols</title><p id="Par16">In addition to expression changes, our analyses identified eight cell populations that were less abundant in warm-dissociated samples in comparison to cold-dissociated ones, including podocytes, mesangial cells, and endothelial cells (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>e left, chi-square test <italic>p</italic> value &#x0003c;&#x02009;0.001). These depleted populations also showed significantly high expression of the stress-response-related gene set as described above (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>c). Notably, only three podocytes were detected in warm-dissociated samples (0.03% of the total cell count), compared to 330 (2.78%) in the cold-dissociated samples. These findings suggest that these populations are sensitive to warm dissociation and consequently underrepresented.</p><p id="Par17">Conversely, we identified cells such as those of the ascending loop of Henle (aLOH) and proximal tubule (PT), that were more abundant in warm-dissociated samples (aLOH 4.99% vs. 2.52% in cold, PT 71.36% vs. 63.34% in cold), potentially indicating their less efficient dissociation by cold-active protease (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>e right).</p><p id="Par18">Finally, to determine whether microfluidic partitioning could affect cell composition, we compared bulk RNA-seq and scRNA-seq data generated on the same dissociated kidney samples (Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Supplementary Note 1). Our results suggest that proportions of aLOH cells in cell suspensions were higher before they were loaded on the Chromium Controller and that they are somehow underrepresented. As to possible mechanisms for this, it may be that they are more resistant to lysis in the device or that there is differential sampling of these cells due to cell size or shape as they enter the microfluidic device.</p></sec></sec><sec id="Sec7"><title>Comparison of cell preservation protocols</title><p id="Par19">We next evaluated whether cryopreservation and methanol fixation maintain cell composition and transcriptional profiles of kidneys. Aliquots of single-cell suspensions of cold- and warm-dissociated kidneys were cryopreserved (50% FBS, 40% RPMI-1640, 10% DMSO) and stored for 6&#x02009;weeks or methanol-fixed and stored for 3&#x02009;months. These stored samples were then profiled with 10x Genomics Chromium scRNA-seq (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a, b, &#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;). The resulting datasets consisted of 11,627 and 5545 methanol-fixed cells and 3519 and 3483 cryopreserved cells derived from cold- and warm-dissociated kidneys, respectively. Despite loading similar numbers of cells, the number of high-quality cells obtained from the cryopreserved samples after quality control and filtering (&#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;) was substantially lower (~&#x02009;30%) than that of the fresh and methanol-fixed samples.</p><sec id="Sec8"><title>Cryopreservation depletes epithelial cell types</title><p id="Par20">The most prominent difference in recovery rates pertained to cells of the proximal tubule (PT), the most populous cell type in kidney [<xref ref-type="bibr" rid="CR34">34</xref>]. In freshly profiled suspensions, PT composed 63.12% and 70.86% of all cells in cold- and warm-dissociated samples, respectively. In contrast, PT were scarcely detected in cryopreserved samples, at 0.31% and 0.57%, respectively (see Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>a for cold-dissociated samples, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S4 for warm-dissociated samples, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S5 for biological replicates). We next compared recovery rates of other cell populations in freshly profiled and cryopreserved samples relative to all non-PT cells. This comparison revealed significant underrepresentation (chi-square test <italic>p</italic> value &#x0003c;&#x02009;0.001) of five kidney cell types in cryopreserved samples prepared with the cold dissociation protocol, three of which were also underrepresented in the cryopreserved warm dissociation samples (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S6). Together with the loss of PT cells, this indicates that the cryopreservation and subsequent thawing protocol failed to efficiently recover kidney epithelial cell populations.
<fig id="Fig3"><label>Fig. 3</label><caption><p>Cell preservation protocol performance in cold-dissociated samples. <bold>a</bold> Cell type composition of freshly profiled and preserved cold-dissociated samples. <bold>b</bold> Number of differentially expressed genes (DEGs) detected between preserved and freshly profiled aliquots. Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with logFC&#x02009;=&#x02009;1, min detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05 as thresholds. <bold>c</bold> Expression and detection rates of DEGs with higher expression in cryopreserved samples in at least two cell types. <bold>d</bold> Expression and detection rates of DEGs with higher expression in methanol-fixed samples in at least nine cell types. aLOH: ascending loop of Henle; CD_IC: intercalated cells of collecting duct; CD_PC: principal cells of collecting duct; CNT: connecting tubule; DCT: distal convoluted tubule; PT: proximal tubule</p></caption><graphic xlink:href="13059_2020_2048_Fig3_HTML" id="MO3"/></fig></p><p id="Par21">Previous studies have reported cryopreserved cells generate comparable data to that of fresh cells [<xref ref-type="bibr" rid="CR22">22</xref>, <xref ref-type="bibr" rid="CR23">23</xref>]. Hence, we repeated the experiment comparing cryopreserved and freshly profiled cold-dissociated single-cell suspension aliquots using different mice (Balb/c female), 10x chemistry (v3 as opposed to v2), storage length (2&#x02009;weeks as opposed to 6&#x02009;weeks), and centrifugation speed for thawing and resuspension (1200<italic>g</italic> as opposed to 400<italic>g</italic>). Again, there was a significant depletion of PT cells, with them making up 55.55% of the freshly profiled cells but only 7.65% of the cryopreserved cells (Additional&#x000a0;file <xref rid="MOESM2" ref-type="media">2</xref>: Figure S7). Notably, only ~&#x02009;33% and ~&#x02009;32% of cells were recovered after cryostorage in the first and the repeated experiment, respectively; the average viability estimated by the Countess was 86% and 75%, respectively. From this, we conclude that, at least in the case of mouse kidneys, cryopreservation of dissociated cells using 50% FBS, 40% RPMI, and 10% DMSO can induce substantial deleterious changes in cell composition.</p><p id="Par22">In contrast to a previous report assessing storage of cell lines and immune cells [<xref ref-type="bibr" rid="CR22">22</xref>], in the case of dissociated mouse kidneys, methanol fixation better preserved cell type composition than cryopreservation (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>a, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S4&#x02013;5). Nevertheless, certain cell types were moderately underrepresented in the methanol-fixed samples in comparison to freshly profiled samples, with macrophages showing the largest reduction from 5.36 to 3.2% in cold-dissociated samples and from 4.28 to 2.54% in warm-dissociated samples.</p></sec><sec id="Sec9"><title>Cryopreservation induces stress response</title><p id="Par23">To gain further insights into preservation-related artifacts, we compared gene expression between preserved and freshly profiled samples in each cell type separately. In cold-dissociated samples, 31 and 27 genes were overexpressed in at least one cell type in cryopreserved and methanol-fixed cells, respectively, when compared to freshly profiled suspensions (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>b, see Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S4 for warm-dissociated samples).</p><p id="Par24">In cryopreserved samples, stress-response-related genes were induced, including multiple immediate-early response genes and heat shock proteins (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>c, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S4, Additional&#x000a0;files&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref>, <xref rid="MOESM8" ref-type="media">8</xref>). In contrast, genes overexpressed in methanol-fixed cells were those highly expressed in tubular cells and hemoglobin genes (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>d, Additional&#x000a0;files&#x000a0;<xref rid="MOESM9" ref-type="media">9</xref>, <xref rid="MOESM10" ref-type="media">10</xref>). The same set of transcripts contaminated most cell types suggesting methanol fixation damages cells and leads to ambient RNA contamination of droplets (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Supplementary Note 2 for more detailed investigation of the ambient RNA profile).</p></sec></sec><sec id="Sec10"><title>Comparison of single-cell and single-nucleus sequencing protocols</title><p id="Par25">Having identified cold-active protease as a less damaging tissue dissociation approach for scRNA-seq, we next compared it to snRNA-seq. We performed a series of experiments using kidneys from Balb/c male mice with v2 10x chemistry or female mice with v3 chemistry and prepared cells using cold tissue dissociation for scRNA-seq and nuclei using three variant protocols for snRNA-seq (&#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>b&#x02013;e). Two nuclei isolation protocols made use of fluorescence-activated nuclei sorting (FANS). The first protocol washed the nuclei three times and used a centrifugation speed of 500<italic>g</italic> (further referred to as SN_FANS_3x500g, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>c). In the second protocol, nuclei were washed once and a centrifugation speed of 2000<italic>g</italic> was used (SN_FANS_1x2000g, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>d). In the third protocol, nuclei were initially washed using a 500<italic>g</italic> spin and then cleaned using a sucrose cushion avoiding the requirement to sort isolated nuclei (SN_sucrose, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>e). The three nuclei isolation protocols yielded comparable results, with the most notable difference being a higher contamination with mitochondrial genes in SN_FANS_1x2000g (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Supplementary Note 3 and Additional&#x000a0;files&#x000a0;<xref rid="MOESM11" ref-type="media">11</xref>, <xref rid="MOESM12" ref-type="media">12</xref>, <xref rid="MOESM13" ref-type="media">13</xref>). Single-nuclei sequencing detected more genes per nuclei than single-cell sequencing per cell, with the median numbers of 1819 and 981 genes, respectively (genes detected in at least 10 cells/nuclei were retained in each sample). In addition, we performed bulk RNA-seq of intact flash-frozen whole kidneys and of cold-dissociated cell suspensions (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>b, f).</p><p id="Par26">Detection rates of non-epithelial kidney cell types were markedly different between scRNA-seq and snRNA-seq libraries (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S8, Additional&#x000a0;file&#x000a0;<xref rid="MOESM14" ref-type="media">14</xref>). Immune cells were detected at lower rates in snRNA-seq (average of 0.73%) than in scRNA-seq (average of 6.03%) across all experiments performed (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S8). Using the bulk RNA-seq from intact kidneys (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>f) and BSEQ-sc [<xref ref-type="bibr" rid="CR35">35</xref>] to predict the proportions of each cell type present, we estimated that approximately 1.51% should correspond to immune cells in Balb/c female mice and 4.84% in Balb/c male mice. This suggests an underrepresentation of immune cells in the snRNA-seq data. Furthermore, macrophages were the only type of immune cells recovered in snRNA-seq libraries, whereas in scRNA-seq libraries we also detected T cells (1.38% on average), B cells (0.77%), and NK cells (0.65%). Similarly, podocytes composed only 0.7% in snRNA-seq libraries as opposed to 3.28% in scRNA-seq (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S8). Cell types more abundant in snRNA-seq libraries included loop of Henle and mesangial cells (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S8).
<fig id="Fig4"><label>Fig. 4</label><caption><p>Comparison of single-cell and single-nucleus libraries. <bold>a</bold> Cell type composition for kidneys from Balb/c female mice. Average percentages for scRNA-seq libraries are shown in blue and for snRNA-seq libraries in gray. BSEQ-sc estimates are shown for bulk RNA-seq of intact and dissociated kidneys. Error bars are standard error of mean. <bold>b</bold> Abundance of renal epithelial cell types in Clark et al. study [<xref ref-type="bibr" rid="CR34">34</xref>] in comparison to our data from Balb/c female mice</p></caption><graphic xlink:href="13059_2020_2048_Fig4_HTML" id="MO4"/></fig></p><p id="Par27">We next compared the observed cell composition to estimates of epithelial cell type contribution based on quantitative renal anatomy, as reported by Clark et al. recently [<xref ref-type="bibr" rid="CR34">34</xref>]. Figure&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>b and Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S8 show that for some cell types, such as podocytes, scRNA-seq yields proportions most similar to the quantitative renal anatomy estimates, whereas for other cell types, such as loop of Henle cells, snRNA-seq better captures cell composition. Bulk RNA-seq-based proportions estimated from intact kidneys largely contradicted the anatomical estimates, which might reflect inaccurate deconvolution of the sample. Finally, comparison of bulk RNA-seq profiles of intact kidneys and cold-dissociated cell suspensions from female Balb/c mice suggested cell types that may be unequally represented in whole vs dissociated kidneys (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>: Supplementary Note 4 and Additional&#x000a0;file&#x000a0;<xref rid="MOESM15" ref-type="media">15</xref>).</p><p id="Par28">Differential expression analysis comparing individual cell types profiled by snRNA-seq and scRNA-seq suggested higher expression of long noncoding RNAs in snRNA-seq libraries and higher expression of genes related to mitochondrial and ribosomal functions in scRNA-seq, in agreement with previous reports [<xref ref-type="bibr" rid="CR24">24</xref>, <xref ref-type="bibr" rid="CR26">26</xref>] (Additional&#x000a0;file&#x000a0;<xref rid="MOESM16" ref-type="media">16</xref>).</p><p id="Par29">Finally, as mitotic cells lack a nuclear membrane and in principle should not be observed in the snRNA-seq data, we inferred cell cycle phases for cells and nuclei using Seurat (&#x0201c;<xref rid="Sec13" ref-type="sec">Methods</xref>&#x0201d;) [<xref ref-type="bibr" rid="CR30">30</xref>]. Notably, Seurat predicted a higher fraction of G1 phase cells and lower fraction of S phase cells in scRNA-seq libraries when compared to snRNA-seq libraries for virtually all cell types (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S9). This suggests that there are indeed underlying biases in cell cycle phase distributions in snRNA-seq data in comparison to scRNA-seq data; however, to fully dissect this, a classifier that can discriminate mitotic cells from early G1 and late G2 is required.</p></sec></sec><sec id="Sec11"><title>Discussion</title><p id="Par30">Interrogating complex tissues at the level of individual cells is essential to understand organ development, homeostasis, and pathological changes. Despite the rapid advancement and widespread adoption of scRNA-seq and snRNA-seq technologies, the associated biases remain incompletely understood. To characterize some of the biases, we performed a systematic comparison of recovered cell types and their transcriptional profiles across two tissue dissociation protocols, two single-cell suspension preservation methods and three single-nuclei isolation protocols, each with three biological replicates per experiment.</p><p id="Par31">Previous studies have reported on artifactual gene expression changes induced by proteolytic tissue digestion at 37&#x02009;&#x000b0;C in sensitive cell populations [<xref ref-type="bibr" rid="CR16">16</xref>, <xref ref-type="bibr" rid="CR18">18</xref>]. Our findings corroborate this bias and show induction of heat shock proteins and immediate-early response genes in warm-dissociated libraries when compared to cold-dissociated libraries. Cold-dissociated libraries can serve as a baseline in this case, since low temperature should minimize new transcription [<xref ref-type="bibr" rid="CR18">18</xref>]. Our results further indicate that cell populations prone to these transcriptional changes are also depleted from the samples, with podocytes being the extreme example of a cell type practically lost in warm-dissociated libraries. Overexpression of stress-response-related genes was also detected by bulk RNA-seq analysis of dissociated tissues, confirming that this artifact stems from the dissociation protocol rather than from microfluidic separation, single-cell sequencing, or data processing. These findings have important implications and suggest that data from samples digested at 37&#x02009;&#x000b0;C needs to be interpreted in light of this bias.</p><p id="Par32">One possible drawback of cold tissue dissociation is lower efficiency of releasing hard-to-dissociate cell types. In our study, this may have affected cells of loop of Henle, which were detected at 2.52% in cold- and at 4.99% in warm-dissociated samples. However, both protocols dramatically underestimated abundance of this second most populous kidney cell type. While one possible explanation could be incomplete tissue dissociation in both cases, deconvolution of bulk RNA-seq profiles of single-cell suspensions indicated that cells might be lost during cell encapsulation on the microfluidic device.</p><p id="Par33">Cold-dissociated samples showed higher contamination with hemoglobin transcripts than warm-dissociated ones. We note that red blood cell depletion methods were not applied in any of the experiments performed here and the observation likely stems from a higher rate of hemoglobin transcript degradation in warm tissue dissociation conditions.</p><p id="Par34">Two recent studies have shown that cryopreservation generated comparable data to that of fresh cells for cell lines and immune cells, and also for complex tissues cryopreserved prior to single-cell separation [<xref ref-type="bibr" rid="CR22">22</xref>, <xref ref-type="bibr" rid="CR23">23</xref>]. Here, however, we report that cryopreservation of single-cell suspensions of dissociated mouse kidneys resulted in depletion of epithelial cell types. This artifact was reproducible across two mouse strains, both sexes, and two 10x chemistry versions. However, we observed a higher fraction of recovered PT cells (7.65% vs. 0.57%) in the repeated experiment, which might be explained by either sex or strain differences, or higher sensitivity of 10x v3 chemistry. Together with the depletion of PT cells, we observed reduced contamination of other cells with highly expressed PT transcripts, which indicates that PT cells might be lost in the thawing and resuspension. A possible explanation for the differences from previous reports is the proportion of serum used in the freezing media. 10x Genomics recommends 40% FBS (10x Genomics, CG00039, Rev. D), whereas the other studies used either 90% FBS (peripheral blood, minced tissues, cell lines, and immune cells) or 10% FBS (cell lines) [<xref ref-type="bibr" rid="CR22">22</xref>, <xref ref-type="bibr" rid="CR23">23</xref>]. Notably, despite loading similar numbers of fresh, methanol-fixed and cryopreserved cells, the number of the usable cells observed in the cryopreserved samples was only ~&#x02009;30% of the others. This raises the possibilities that the missing PT cells may be present but are failing to make it into the microfluidics, failing to lyse, or are so badly damaged that there is insufficient RNA remaining to generate a usable library. In contrast to cryopreservation, cell composition of methanol-fixed suspensions resembled that of freshly profiled libraries. Similarly to previous studies, we observed ambient RNA contamination with highly abundant transcripts suggesting cell damage by methanol fixation [<xref ref-type="bibr" rid="CR22">22</xref>].</p><p id="Par35">Studies comparing scRNA-seq and snRNA-seq reported that, although the two technologies profile different RNA fractions, both detect sufficient genes and allow adequate representation of cell populations [<xref ref-type="bibr" rid="CR24">24</xref>&#x02013;<xref ref-type="bibr" rid="CR26">26</xref>]. In this work, one of the most notable differences between single-cell and single-nuclei experiments was the low detection rate of immune cells, in particular the failure to detect T, B, or NK cells in any of the snRNA-seq libraries. The depletion of lymphocytes was also observed in the Wu et al. [<xref ref-type="bibr" rid="CR26">26</xref>] dataset (commented upon by O&#x02019;Sullivan et al. [<xref ref-type="bibr" rid="CR36">36</xref>]). Notably Slyper et al. [<xref ref-type="bibr" rid="CR37">37</xref>] also observe much lower fractions of T, B, and NK cells in matched snRNA-seq and scRNA-seq datasets from adjacent pieces of a metastatic breast cancer and a neuroblastoma. As Wu et al. have suggested, although these differences might indicate underestimation of immune cells by snRNA-seq, another plausible explanation is that immune cell content is inflated in single-cell experiments as other cell types may be underrepresented due to incomplete dissociation.</p><p id="Par36">It is important to remember that cell type composition of single-cell or single-nucleus data differs from that of the original tissue and complementary approaches may be considered to improve the accuracy of the estimates. We note that even ISH/IHC/spatial transcriptomic methods, unless carried out on sufficient serial sections to completely survey the tissue of interest, will only give cellular proportions for the sections examined and will not give a whole (3D) organ estimate. Similarly, flow cytometry would help as an orthogonal approach to quantify cellular proportions within single-cell suspensions but still requires the organ to be dissociated, which as we have shown here has biases. Although in vitro cell mixtures or cell spiking experiments could be used to introduce different cell types at known ratios, we have not employed them in these experiments as they do not represent a true scenario of dissociation from a solid tissue.</p><p id="Par37">Clark et al. recently reported cell frequency estimates based on quantitative renal anatomy. However, these were restricted to renal epithelial cells [<xref ref-type="bibr" rid="CR34">34</xref>]. Based on these estimates, some cell types, such as podocytes, appear to be better represented in scRNA-seq, whereas others, such as loop of Henle cells, were captured more effectively by snRNA-seq. We also attempted to use computational deconvolution of bulk RNA-seq of intact kidneys to infer its cell composition. However, the approach is sensitive to the input marker gene list used and may overlook rare and novel cell types. In addition, cell abundance estimates from bulk data would be influenced by both cell number and relative mRNA content of each cell. We will continue to search for approaches to better define the &#x0201c;ground truth&#x0201d; for cell composition.</p><p id="Par38">In this study, we reported on a range of biases of experimental procedures. We would like to stress that computational tools are being actively developed to mitigate these biases. These include tools for decontamination such as DecontX [<xref ref-type="bibr" rid="CR38">38</xref>] or SoupX [<xref ref-type="bibr" rid="CR39">39</xref>], tools for batch-effect removal such as Seurat [<xref ref-type="bibr" rid="CR30">30</xref>] and Harmony [<xref ref-type="bibr" rid="CR40">40</xref>], and many others. Hence, even when a dataset in hand is affected by pronounced technical artifacts, certain computational techniques might be useful in reducing their effect.</p></sec><sec id="Sec12"><title>Conclusions</title><p id="Par39">From our experiments, we have confirmed several observations by others in the field that have direct relevance to designing single-cell experiments studying human disease biology. Specifically, the depletion of epithelial cell types in cryopreserved samples is concerning given the majority of human cancers are epithelial in origin. Similarly, the underrepresentation of T, B, and NK cells in snRNA-seq data is of critical relevance for studies of tumor immunology, tumor immunotherapy, and autoimmune disease. By comparing these protocols across a single system (mouse kidney), we are now using these results to guide our collaborative network on the best approach for their circumstances. We are instructing our collaborators to use cold dissociation and freshly profile samples wherever possible. For clinical samples (where we often need to wait for pathology results, or are constrained by how archival material has been stored), we discuss with the collaborators the different biases of cryopreservation, methanol fixation, or snRNA-seq from frozen tissue and together decide the best approach. Where information on lymphocytes (T, B, NK) is required, we do not currently recommend snRNA-seq. Researchers will undoubtedly continue to develop new methods aimed to reduce bias. Given the high cost of these experiments, it is critical that these methods are systematically compared to allow the community to decide which to adopt.</p></sec><sec id="Sec13"><title>Methods</title><sec id="Sec14"><title>Mice</title><p id="Par40">Acknowledging the principles of 3Rs (Replacement, Reduction, and Refinement), all kidneys used in this study were from mice that were euthanized by cervical dislocation as parts of other ongoing ethically approved experiments. In the first series of experiments, comparing cold and warm tissue dissociation and two preservation protocols, male AFAPIL.1DEL C57BL/6J mice from the same litter were used. These mice were 19&#x02009;weeks old when euthanized and had no exposure to any experimental procedures. For the subsequent experiments, comparing cold-dissociated scRNA-seq to single-nuclei isolation protocols, we used untreated 18-week-old male Balb/c mice from the same litter or untreated 15-week-old female wild type Balb/c mice that were previously used as breeders, as specified in Additional&#x000a0;file&#x000a0;<xref rid="MOESM17" ref-type="media">17</xref>.</p></sec><sec id="Sec15"><title>Kidney harvesting</title><p id="Par41">Mice were euthanized and their kidneys were dissected and placed into a 1.5-mL tube containing 1&#x02009;mL of ice-cold PBS. The capsules were then removed on ice, and the samples processed as detailed below.</p></sec><sec id="Sec16"><title>Warm tissue dissociation</title><p id="Par42">Kidneys were dissociated using the Multi-tissue dissociation kit 2 from Miltenyi Biotec [130-110-203] as per manufacturers&#x02019; instruction, with minor variations. Once the weight of the kidney was determined, the kidney was quartered and placed into a gentleMACS C-tube [Miltenyi Biotech; 130-096-334] containing the enzyme mix described in the kit&#x02019;s protocol. The tube was centrifuged briefly, then placed onto the gentleMACS octo dissociator (Miltenyi Biotech), and the 37C_Multi_E program was run after attaching the heating elements. Following completion of the program, the tube was briefly centrifuged. Complete tissue dissociation was checked by examining under the microscope and confirming the absence of visible tissue chunks.</p><p id="Par43">The homogenate was filtered through a 70-&#x003bc;m cell strainer [Greiner; 54,207] into a 50-mL centrifuge tube [Greiner; 227,270], the strainer was then rinsed with 15&#x02009;mL of PBS. The cell suspension was centrifuged at 400<italic>g</italic> for 10&#x02009;min; once complete, the supernatant was removed and the pellet was resuspended in 5&#x02009;mL of PBS&#x02009;+&#x02009;0.04% BSA [Sigma; A7638]. The cell suspension was then filtered through a 40-&#x003bc;m strainer [Greiner; 542,040], which was subsequently rinsed with 2&#x02009;mL of PBS&#x02009;+&#x02009;0.04% BSA. The cells were again centrifuged at 400<italic>g</italic> for 10&#x02009;min. The supernatant was then removed, and the pellet was resuspended in 5&#x02009;mL of PBS&#x02009;+&#x02009;0.04% BSA. Cell count and viability was estimated using the Countess II FL (Thermo Fisher) and the ReadyProbes Blue/Red kit [Invitrogen; R37610]. The cells were then diluted to 700 cells/&#x003bc;L and were immediately loaded onto a 10x chip A and processed on the 10x Chromium controller. The remaining cells were then either methanol fixed or cryopreserved. Cell viability is available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM18" ref-type="media">18</xref>.</p></sec><sec id="Sec17"><title>Cold tissue dissociation</title><p id="Par44">Kidneys were dissociated using a modified version of the published protocol described in [<xref ref-type="bibr" rid="CR18">18</xref>]. Based on the weight, in a pre-cooled Miltenyi C-tube, a protease solution (5&#x02009;mM CaCl<sub>2</sub> [Invitrogen; AM9530G], 10&#x02009;mg/mL <italic>B. Licheniformis</italic> protease [Sigma; P5380], 125&#x02009;U/mL DNase I [Sigma; D5025], 1xDPBS) was prepared for each kidney.</p><p id="Par45">The kidneys were then minced on ice into a smooth paste using a scalpel. The minced kidney was transferred into 4&#x02013;6&#x02009;mL of the protease solution (dependent on weight) and triturated using a 1&#x02009;mL pipette for 15&#x02009;s every 2&#x000a0;min for a total of 8&#x000a0;min.</p><p id="Par46">Following trituration, the C-tubes were placed onto a Miltenyi gentleMACS octo dissociator in a cool room (4&#x02009;&#x000b0;C), and the m_brain_03 program was run twice in succession. Once complete, the samples were triturated for 15&#x02009;s every 2&#x000a0;min on ice for an additional 16&#x02009;min using a 1-mL pipette. A total of 10&#x02009;&#x003bc;L of each sample was then loaded into a hemocytometer to assess whether tissue dissociation was complete. Complete tissue dissociation was also checked by examining under the microscope and confirming the absence of visible tissue chunks. The dissociated cells were transferred to a 15-mL centrifuge tube and 3&#x02009;mL of ice-cold PBS&#x02009;+&#x02009;10%FBS [Gibco; A3160401] was added.</p><p id="Par47">The cell suspension was centrifuged at 1200<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C. The supernatant was removed and the pellet was resuspended in 2&#x02009;mL of PBS&#x02009;+&#x02009;10%FBS. The cells were then filtered through a 70-&#x003bc;m cell strainer, which was subsequently rinsed with 2&#x02009;mL of PBS&#x02009;+&#x02009;0.01% BSA. The cells were then centrifuged again at 1200<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C followed by removal of the supernatant and resuspension of the pellet in 5&#x02009;mL of PBS&#x02009;+&#x02009;0.01%BSA. The cells were then filtered through a 40-&#x003bc;m cell strainer, which was subsequently rinsed with 2&#x02009;mL of PBS&#x02009;+&#x02009;0.01% BSA. The cells were again centrifuged at 1200<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C followed by removal of the supernatant and resuspension of the cells in 5&#x02009;mL of PBS&#x02009;+&#x02009;0.04%BSA. The cells were counted and checked for viability using the ReadyProbes Blue/Red Kit on the Countess II FL. The cells were further diluted to a concentration of 700 cells/&#x003bc;L with PBS/0.04%BSA and loaded directly onto a 10x chip (A/B depending on experiment) and isolated using the 10x Chromium controller. The remaining cells were either methanol fixed or cryopreserved. Cell viability is available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM18" ref-type="media">18</xref>.</p></sec><sec id="Sec18"><title>Methanol fixation</title><sec id="Sec19"><title>Fixing</title><p id="Par48">The methanol-fixation protocol was based on [<xref ref-type="bibr" rid="CR41">41</xref>]. After tissue dissociation, the cells were concentrated to approximately 5&#x02009;&#x000d7;&#x02009;10<sup>6</sup> cells/mL by centrifuging at 1000<italic>g</italic> for 10&#x02009;min. In total, 200&#x02009;&#x003bc;L of the cell suspensions was aliquoted into 2-mL cryovials resting on ice. A total of 800&#x02009;&#x003bc;L of 100% methanol [Sigma; 494,437] (chilled at &#x02212;&#x02009;20&#x02009;&#x000b0;C) was then added dropwise to each sample while gently stirring the cells to prevent clumping. The cryovials were stored at &#x02212;&#x02009;20&#x02009;&#x000b0;C for 30&#x02009;min, then directly transferred to &#x02212;&#x02009;80&#x02009;&#x000b0;C (no gradient cooling).</p></sec><sec id="Sec20"><title>Rehydrating</title><p id="Par49">Cryovials of methanol-fixed cells were removed from &#x02212;&#x02009;80&#x02009;&#x000b0;C and placed on ice to equilibrate to 4&#x02009;&#x000b0;C (approximately 10&#x02009;min). The cells were then transferred to a 1.5-mL centrifuge tube and centrifuged at 1000<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C. The supernatant was discarded and the pellet was resuspended in a small volume of SSC cocktail (3xSSC [Sigma; S0902], 0.04% BSA, 40&#x02009;mM DTT [Sigma; 43816], 0.5&#x02009;U/mL RNasin plus [Promega; N2615]) to reach a concentration of approximately 2000 cells/&#x003bc;L. The cells were then filtered through a pre-wetted (with 1&#x02009;mL of nuclease-free water) 40-&#x003bc;m pluristrainer mini filter [PluriSelect; 43-10040]. The cells were counted using the ReadyProbes Blue/Red Kit on the Countess II, then adjusted to 2000 cells/&#x003bc;L based on the count. The cells were loaded onto a 10x chip (A/B depending on version used) at a volume that dilutes the SSC to 0.125x to prevent reverse transcription inhibition. Cell viability is available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM18" ref-type="media">18</xref>.</p></sec></sec><sec id="Sec21"><title>Cryopreservation</title><sec id="Sec22"><title>Freezing</title><p id="Par50">After tissue dissociation, the cells were centrifuged at 400<italic>g</italic> for 10&#x02009;min (1200<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C for the repeated experiment), then resuspended in freezing media (50% FBS, 40% RPMI-1640 [Gibco; 11875093], 10% DMSO [Sigma; D4540]) to achieve a concentration of 1&#x02009;&#x000d7;&#x02009;10<sup>6</sup> cells/mL. One milliliter of the cell suspension was aliquoted into 2-mL cryovials, then placed into an isopropanol freezing container (Mr. Frosty) and stored at &#x02212;&#x02009;80&#x02009;&#x000b0;C overnight. The following day, the cells were transferred to liquid nitrogen storage.</p></sec><sec id="Sec23"><title>Thawing</title><p id="Par51">The samples were removed from &#x02212;&#x02009;80&#x02009;&#x000b0;C and immediately placed into a 37&#x02009;&#x000b0;C waterbath for 2&#x02013;3&#x02009;min to rapidly thaw. The cells were then mixed using a 1&#x02009;mL pipette with a wide-bore tip, and the entire volume was transferred to a 15-mL centrifuge tube [Greiner; 188261]. The cryovial was then rinsed twice with RMPI+&#x02009;10%FBS (rinse media); each time, the 1&#x02009;mL of media was added to the 15-mL centrifuge in a dropwise manner while gently shaking the tube. Seven milliliters of rinse media was added to the centrifuge tube using a serological pipette&#x02014;the first 4&#x02009;mL was added dropwise while gently shaking the tube, and the following 3&#x02009;mL added down the side of the tube over 2&#x000a0;s. The tube was then inverted to mix.</p><p id="Par52">The cells were centrifuged at 300<italic>g</italic> for 5&#x000a0;min. Once completed, the supernatant was removed (leaving 1&#x02009;mL), placed into another 15-mL centrifuge tube and centrifuged at 400<italic>g</italic> for 5&#x000a0;min. The supernatant was discarded (leaving 1&#x02009;mL). The pellet from the supernatant was then resuspended, combined with the pellet in the initial centrifuge tube, and mixed. Two&#x02009;milliliters of PBS&#x02009;+&#x02009;0.04% BSA was added to the centrifuge tube and shaken gently to mix. The cells were then centrifuged again at 400<italic>g</italic> for 5&#x000a0;min. The supernatant was discarded leaving 0.5&#x02009;mL behind. 0.5&#x02009;mL of PBS&#x02009;+&#x02009;0.04% BSA was added to the cells and gently pipette-mixed 10&#x02013;15 times to fully resuspend. The cells were then filtered through a pre-wetted (with 1&#x02009;mL of PBS&#x02009;+&#x02009;0.04% BSA) 40-&#x003bc;m pluristrainer mini filter. A 20&#x02009;&#x003bc;L aliquot of the cells was used to obtain an estimate of cell count and viability using the ReadyProbes Blue/Red Kit on the Countess II FL. Based on the count, the cells were diluted to a concentration of 700 cells/&#x003bc;L. The cells were then loaded onto a 10x chip (A/B depending on version) and immediately processed on the 10x Chromium controller.</p><p id="Par53">For the repeated experiment, the above method was altered: Rather than a 300<italic>g</italic> spin followed by two 400<italic>g</italic> spins, two 1200<italic>g</italic> spins were performed, omitting the second centrifugation step. Cell viability is available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM18" ref-type="media">18</xref>.</p></sec></sec><sec id="Sec24"><title>Flash freezing of whole kidney</title><p id="Par54">Following the removal of the renal capsule, the kidney was placed into an isopentane [Sigma; 320404] bath resting on dry ice for 5&#x000a0;min. The temperature of the bath was maintained between &#x02212;&#x02009;30&#x02009;&#x000b0;C and &#x02212;&#x02009;40&#x02009;&#x000b0;C. Once frozen, the kidney was placed into a pre-cooled (on dry ice) cryovial and then buried in dry ice. The process was repeated for all designated kidneys. The flash-frozen kidneys were then transferred to a &#x02212;&#x02009;80&#x02009;&#x000b0;C freezer for storage.</p></sec><sec id="Sec25"><title>Single-nuclei isolation</title><sec id="Sec26"><title>SN_FANS_3x500g</title><p id="Par55">This method is an adaptation of the Frankenstein protocol [<xref ref-type="bibr" rid="CR42">42</xref>] and the 10x demonstrated protocol [<xref ref-type="bibr" rid="CR43">43</xref>].</p><p id="Par56">The kidneys were removed from &#x02212;&#x02009;80&#x02009;&#x000b0;C and immediately placed on ice. Each kidney was then transferred to a 1.5-mL tube containing 300&#x02009;&#x003bc;L of chilled lysis buffer (10&#x02009;mM Tris-HCl [Invitrogen; AM9856], 3&#x02009;mM MgCl<sub>2</sub> [Invitrogen; AM9530G], 10&#x02009;mM NaCl [Sigma; 71386], 0.005% Nonidet P40 substitute [Roche; 11754599001], 0.2&#x02009;U/mL RNasin plus) and incubated on ice for 2&#x000a0;min. The tissue was then completely homogenized using a pellet pestle [Fisherbrand; FSB12-141-364] using up and down strokes without twisting. 1.2&#x02009;mL of chilled lysis buffer was added to the tube and pipette-mixed (wide-bore). The full volume was then transferred to a pre-cooled 2-mL tube. The homogenate was incubated on ice for 5&#x000a0;min and mixed with a wide-bore tip every 1.5&#x02009;min.</p><p id="Par57">Following the incubation, 500&#x02009;&#x003bc;L of the lysis buffer was added to the homogenate, which was subsequently pipette-mixed and split equally into four 2-mL tubes. One milliliter of chilled lysis buffer was added to each tube and pipette-mixed using a wide-bore tip. The four tubes were incubated for a further 5&#x000a0;min on ice, mixing with a wide-bore tip every 1.5&#x02009;min. The homogenate from the four tubes was then filtered through a 40-&#x003bc;m strainer into a pre-cooled 50-ml centrifuge tube. Following this, the sample was split again into four 2-mL tubes resting on ice.</p><p id="Par58">The samples were centrifuged at 500<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C. The supernatant was removed leaving 50&#x02009;&#x003bc;L in the tube. 1.5&#x02009;mL of lysis buffer was then added to two of the tubes and the pellet resuspended by mixing with a pipette. This resulted in two tubes containing 1.5&#x02009;mL resuspended nuclei in lysis buffer, and two tubes containing a nuclei pellet in 50&#x02009;&#x003bc;L of lysis buffer. The resuspended nuclei in one tube was then combined with the nuclei pellet of another, resulting in two tubes containing resuspended nuclei in lysis buffer.</p><p id="Par59">The nuclei were centrifuged again at 500<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C. The supernatant was removed completely and discarded. A total of 500&#x02009;&#x003bc;L of nuclei wash buffer (1xDPBS, 1% BSA, 0.2&#x02009;U/mL RNasin plus) was added to the tube containing the pellet and left to incubate without resuspending for 5&#x000a0;min. Following incubation, an additional 1&#x02009;mL of nuclei wash buffer was added, and the nuclei were resuspended by gently mixing with a pipette. The nuclei were again centrifuged at 500<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C, followed by discarding the supernatant. The pellets were resuspended in 1.4&#x02009;mL of nuclei wash buffer, then transferred into a pre-cooled 1.5-mL tube. Another 500<italic>g</italic> centrifugation step for 5&#x000a0;min at 4&#x02009;&#x000b0;C was performed. The supernatant was then discarded, and the nuclei pellet was resuspended in 1&#x02009;mL of nuclei wash buffer.</p><p id="Par60">The nuclei were then filtered through a 4-&#x003bc;m pluristrainer mini filter. A total of 200&#x02009;&#x003bc;L of the filtered nuclei suspension was transferred into a 0.5-mL tube and set aside to be used as the unstained control for sorting. To the remaining 800&#x02009;&#x003bc;L, 8&#x02009;&#x003bc;L of DAPI (10&#x02009;&#x003bc;g/mL) [Thermo Scientific; 62248] was added, and the nuclei were mixed with a pipette. A quality control step was performed by viewing the nuclei under a fluorescence microscope on a hemocytometer to check nuclei shape and count.</p><p id="Par61">A BD Influx Cell Sorter was then used to sort 100,000 DAPI-positive events using a 70-&#x003bc;m nozzle and a pressure of 22&#x02009;psi (as per gating strategy, Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S10). The post-sort nuclei concentration and quality were then checked using a fluorescence microscope and hemocytometer. Nuclei were then loaded onto a 10x chip (A/B depending on version used) and processed immediately on the 10x Chromium controller.</p></sec><sec id="Sec27"><title>SN_FANS_1x2000g</title><p id="Par62">The flash-frozen kidneys were removed from &#x02212;&#x02009;80&#x02009;&#x000b0;C and transferred to a 1.5-mL tube containing 500&#x02009;&#x003bc;L of pre-chilled lysis buffer same recipe as previous protocol) and allowed to rest on ice for 2&#x000a0;min. Each kidney was then homogenized with a pellet pestle with 40 up and down strokes without twisting the pellet. The resulting homogenate was mixed with a pipette and transferred to pre-cooled 15-mL centrifuge tube containing 2&#x02009;mL of lysis buffer. The homogenate was incubated for 12&#x02009;min on ice with mixing every 2&#x000a0;min using a glass fire-polished silanized Pasteur pipette [Kimble; 63A54]. Once incubation was complete, 2.5&#x02009;mL of nuclei wash buffer (same recipe as previous protocol) was added to the homogenate. The remaining tissue fragments were completely dissociated by repeated trituration of the homogenate using the glass Pasteur pipette.</p><p id="Par63">The homogenate was then filtered through a 30-&#x003bc;m MACS Smart Strainer [Miltenyi Biotech; 130-098-458] into a new 15-mL centrifuge tube. The nuclei were centrifuged at 2000<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C. The supernatant was removed and the nuclei pellet was resuspended in 1&#x02009;mL of nuclei wash buffer. A total of 200&#x02009;&#x003bc;L was aliquoted into a 0.5-mL tube to be used as an unstained control for sorting. Eight microliters of DAPI (10&#x02009;&#x003bc;g/mL) was added to the remaining 800&#x02009;&#x003bc;L of nuclei. Quality and quantity of the nuclei was checked using a fluorescence microscope prior to sorting. Sorting and post sorting QC was performed in the same manner as for the SN_FANS_3x500g protocol. Nuclei were then loaded onto a 10x chip B and processed immediately on the 10x Chromium controller.</p></sec><sec id="Sec28"><title>SN_sucrose</title><p id="Par64">Kidneys were removed from &#x02212;&#x02009;80&#x02009;&#x000b0;C and transferred to a 1.5-mL tube containing 500&#x02009;&#x003bc;L of pre-chilled lysis buffer II (same recipe as previous protocols, with 125&#x02009;U/mL of DNase I added) and allowed to rest on ice for 2&#x000a0;min. Each kidney was then homogenized using a pellet pestle with 40 up and down strokes without twisting. The homogenate was transferred to a 15-mL centrifuge tube containing 2&#x02009;mL of lysis buffer II and incubated for 12&#x02009;min on ice with mixing every 2&#x000a0;min using a glass fire-polished silanized Pasteur pipette. Following the incubation, 2.5&#x02009;mL of nuclei wash buffer II (1&#x000d7; DPBS&#x02009;+&#x02009;2%BSA) was added to the homogenate. Remaining tissue clumps were dissociated by repeated trituration of the homogenate using the glass Pasteur pipette.</p><p id="Par65">The homogenate was then filtered through a 30-&#x003bc;m MACS Smart Strainer into a new 15-mL centrifuge tube. Subsequently, the homogenate was centrifuged at 2000<italic>g</italic> for 5&#x000a0;min at 4&#x02009;&#x000b0;C. The supernatant was removed, and the pellet was resuspended in 510&#x02009;&#x003bc;L of nuclei wash buffer II. Ten microliters of the suspension was transferred to a 1.5-mL tube and placed on ice for use in nuclei recovery calculations. A total of 900&#x02009;&#x003bc;L of 1.8&#x02009;M sucrose solution [Sigma; NUC201] was added to the remaining 500&#x02009;&#x003bc;L of nuclei suspension and homogenized by mixing with a pipette. 3.6&#x02009;mL of 1.3&#x02009;M sucrose solution [Sigma; NUC201] was added to a 5-mL tube. The nuclei/sucrose homogenate was then gently layered on top of the 1.3&#x02009;M sucrose solution.</p><p id="Par66">The 5-mL tube containing the sucrose solutions and nuclei was then centrifuged at 3000<italic>g</italic> for 10&#x02009;min at 4&#x02009;&#x000b0;C. Once centrifugation was complete, the sucrose phase containing debris was soaked up using a Kimwipe wrapped around a pellet pestle. The remaining supernatant was removed and discarded using a pipette. The nuclei pellet was then resuspended in 5&#x02009;mL of wash buffer II, of which 10&#x02009;&#x003bc;L was transferred to a 1.5-mL tube to assess nuclei recovery.</p><p id="Par67">To the 10&#x02009;&#x003bc;L of nuclei suspension removed prior to the sucrose gradient, 980&#x02009;&#x003bc;L of wash buffer II and 10&#x02009;&#x003bc;L of DAPI (10&#x02009;&#x003bc;g/mL) was added. To the 10&#x02009;&#x003bc;L of nuclei suspension removed after the sucrose gradient, 89&#x02009;&#x003bc;L of wash buffer II and 1&#x02009;&#x003bc;L of DAPI (10&#x02009;&#x003bc;g/mL) was added. The yield from the pre- and post-sucrose aliquots was compared to assess nuclei recovery after filtration through the gradient. The post-sucrose count was used to dilute the nuclei to a concentration of 700 nuclei/&#x003bc;L, which was immediately loaded onto a 10x chip B and processed with the 10x Chromium controller.</p></sec></sec><sec id="Sec29"><title>Single-cell RNA-seq library preparation</title><p id="Par68">All single-cell libraries were constructed in biological triplicate using the 10x Chromium 3&#x02032; workflow as per the manufacturers&#x02019; directions. In the first series of experiments, comparing cold and warm tissue dissociation and two preservation protocols, version 2 chemistry was used. For single-cell versus single-nuclei comparisons, versions 2 and 3 were used as indicated in Additional file <xref rid="MOESM17" ref-type="media">17</xref>. All experiments and conditions aimed for a capture of approximately 9000 cells, except for methanol-fixed samples. Due to the reverse transcription inhibition of 3x SSC, the sample had to be loaded at a concentration of 0.125x SSC, resulting in an approximate cell capture of 4000&#x02013;5000 cells.</p></sec><sec id="Sec30"><title>Bulk RNA-seq library preparation</title><p id="Par69">For the undissociated samples, total RNA was extracted from flash-frozen kidneys using the Nucleospin RNA Midi kit [Macherey Nagel; 740,962.20] as per the manufacturers&#x02019; directions. For the dissociated samples, total RNA was extracted from the remaining cells from each of the tissue dissociation protocols. RNA was assessed for quantity and quality using the TapeStation 4200 RNA ScreenTape kit [Agilent; 5067-5576], which showed all RNA used had a RIN of &#x0003e;&#x02009;8. Bulk RNA-seq was performed using the NEBNext Ultra II RNA Library Kit for Illumina [NEB; E7760] and either NEBNext rRNA Depletion Kit (Human/Mouse/Rat) [NEB; E6310] or NEBNext Poly(A) mRNA isolation module [NEB; E7490] as described in the manufacturers&#x02019; protocol, with 100&#x02009;ng of total RNA as input.</p></sec><sec id="Sec31"><title>Sequencing</title><p id="Par70">All libraries were quantified with qPCR using the NEBnext Library Quant Kit for Illumina and checked for fragment size using the TapeStation D1000 kit (Agilent). The libraries were pooled in equimolar concentration for a total pooled concentration of 2&#x02009;nM. 10x single-cell libraries were sequenced using the Illumina NovaSeq 6000 and S2 flow cells (100&#x02009;cycle kit) with a read one length of 26&#x02009;cycles, and a read two length of 92 or 98&#x02009;cycles for version 2 chemistry. Version 3 chemistry had a read one length of 28&#x02009;cycles, and a read two length of 94&#x02009;cycles. Bulk libraries were sequenced on the Illumina NovaSeq 6000 using SP flow cells (100&#x02009;cycle kit) with read length of 150 for dissociated bulk in C57BL/6J mice, 51 for undissociated bulk in Balb/c male mice, and 60 for Balb/c female mice.</p></sec><sec id="Sec32"><title>Bulk RNA-seq data processing</title><p id="Par71">BCL files were demultiplexed and converted into FASTQ using bcl2fastq utility of Illumina BaseSpace Sequence Hub. FastQC was used for read quality control [<xref ref-type="bibr" rid="CR44">44</xref>]. Adapters and low-quality bases were trimmed using Trim Galore with parameters <italic>--paired --quality 5 --stringency 5 --length 20 --max_n 10</italic> [<xref ref-type="bibr" rid="CR45">45</xref>]. Reads matching to ribosomal DNA repeat sequence BK000964 [<xref ref-type="bibr" rid="CR46">46</xref>] and low complexity reads were removed with TagDust2 [<xref ref-type="bibr" rid="CR47">47</xref>]. The remaining reads were mapped to GRCm38.84 version of mouse genome using STAR version 2.6.1a with default settings [<xref ref-type="bibr" rid="CR48">48</xref>]. Picard MarkDuplicates tool was employed to identify duplicates [<xref ref-type="bibr" rid="CR49">49</xref>]. FeatureCounts was then used to derive gene count matrix [<xref ref-type="bibr" rid="CR50">50</xref>]. Counts were normalized to gene length and then to library sizes using weighted trimmed mean of M-values (TMM) method in edgeR [<xref ref-type="bibr" rid="CR27">27</xref>], to derive gene length corrected trimmed mean of M-values (GeTMM) as described in [<xref ref-type="bibr" rid="CR29">29</xref>].</p></sec><sec id="Sec33"><title>scRNA-seq and snRNA-seq data processing</title><p id="Par72">BCL files were demultiplexed and converted into FASTQ using bcl2fastq utility of Illumina BaseSpace Sequence Hub. scRNA-seq and snRNA-seq libraries were processed using Cell Ranger 2.1.1 with mm10-2.1.0 reference. Reads mapped to exons were used for scRNA-seq samples, whereas both intronic and exonic reads were counted for snRNA-seq. Custom pre-mRNA reference for snRNA-seq was built as described in [<xref ref-type="bibr" rid="CR51">51</xref>]. Raw gene-barcode matrices from Cell Ranger output were used for downstream processing. Cells were distinguished from background noise using EmptyDrops [<xref ref-type="bibr" rid="CR52">52</xref>]. Only genes detected in a minimum of 10 cells were retained; cells with 200&#x02013;3000 genes and under 50% of mitochondrial reads were retained, as per Park et al. study [<xref ref-type="bibr" rid="CR32">32</xref>]. Nuclei were additionally filtered to have at least 450 UMIs for v2 chemistry and 900 UMIs for v3 chemistry, and mitochondrial genes were removed. Outlier cells with high ratio of number of detected UMI to genes (&#x0003e;&#x02009;3 median absolute deviations from median) were removed using Scater [<xref ref-type="bibr" rid="CR53">53</xref>]. Seurat v2 was used for sample integration (canonical correlation analysis), normalization (dividing by the total counts, multiplying by 10,000 and natural-log transforming), scaling, clustering, and differential expression analysis (Wilcoxon test) [<xref ref-type="bibr" rid="CR30">30</xref>].</p></sec><sec id="Sec34"><title>Inferring cell identity</title><p id="Par73">To infer cell identity for freshly profiled samples in the first series of experiments, we performed a reference-based annotation using scMatch [<xref ref-type="bibr" rid="CR31">31</xref>] and refined cell labels based on marker gene expression in a two-step procedure described below (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1).</p><sec id="Sec35"><title>Reference dataset</title><p id="Par74">To construct the reference dataset for scMatch [<xref ref-type="bibr" rid="CR31">31</xref>], we obtained gene counts and cell types reported in three single-cell (or single-nuclei) adult mouse kidney studies [<xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR32">32</xref>, <xref ref-type="bibr" rid="CR33">33</xref>]. Counts were normalized to cell library size and averaged within each cell type to derive reference vectors (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1, Step 1). The reference vectors were clustered using Spearman correlation coefficient, and five vectors were removed as outliers. The remaining 66 vectors composed a reference dataset, available as Additional&#x000a0;file&#x000a0;<xref rid="MOESM19" ref-type="media">19</xref>. With this reference dataset, we ran scMatch [<xref ref-type="bibr" rid="CR31">31</xref>] (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1, Step 2) using options <italic>--testMethod s --keepZeros y</italic> to label each individual cell with the closest cell type identity from the reference dataset.</p></sec><sec id="Sec36"><title>Refining cell identities</title><p id="Par75">We next refined scMatch-derived cell types based on gene expression. First, for each cell type, we calculated gene signatures as genes overexpressed in the given cell type when compared to all other cells (<italic>FindMarkers</italic> function of Seurat [<xref ref-type="bibr" rid="CR30">30</xref>], minimum detection rate of 0.5, logFC threshold of 1 and FDR&#x02009;&#x0003c;&#x02009;0.05 were used as thresholds; only cell types with at least 10 cells were considered; Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1, Step 3). Second, cell type gene signature scores were calculated for each cell and for each gene signature (<italic>AddModuleScore</italic> function of Seurat [<xref ref-type="bibr" rid="CR30">30</xref>], genes attributed to signatures in more than two cell types were excluded; Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1, Step 4). Third, we used these scores to assign cell types to cells (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1, Step 5). A cell type was assigned to a cell if the score for that cell type was the highest among all cell types, positive and significant with FDR&#x02009;&#x0003c;&#x02009;0.05. Significance was determined in a Monte-Carlo procedure with 1000 randomly selected gene sets of the same size [<xref ref-type="bibr" rid="CR54">54</xref>], correction for multiple hypothesis testing was performed using Benjamini-Hochberg procedure [<xref ref-type="bibr" rid="CR55">55</xref>]. Cells without cell type annotation were manually explored to identify whether the corresponding cell type might be a novel one, absent from the reference.</p></sec><sec id="Sec37"><title>Second iteration</title><p id="Par76">Cell types inferred in our dataset were added to the reference dataset (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>: Figure S1, Step 6), and annotation with scMatch and gene set signature scoring was repeated. Cells left unannotated at this stage were labelled as &#x0201c;unknown.&#x0201d; Cell type gene signatures are available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM20" ref-type="media">20</xref>.</p><p id="Par77">This approach failed to identify cells of connecting tubule (CNT) and, instead, matched them to other similar cell types. To resolve this, annotation for cell types labelled as DCT, aLOH, CD_IC, CD_PC, and CD_Trans was additionally refined as follows. These cells were extracted from the dataset and clustered separately. Candidate CNT cells were identified as a cluster overexpressing <italic>Calb1</italic> and <italic>Klk1</italic> genes [<xref ref-type="bibr" rid="CR34">34</xref>, <xref ref-type="bibr" rid="CR56">56</xref>]. The cell type signature score procedure was then applied for this subset as described above.</p><p id="Par78">Cell type labels assigned to each cell are available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>.</p></sec><sec id="Sec38"><title>Preserved cells</title><p id="Par79">Cells of preserved single-cell suspensions from the first series of experiments were annotated using the cell type gene signatures derived from the corresponding freshly profiled samples (Additional&#x000a0;file&#x000a0;<xref rid="MOESM20" ref-type="media">20</xref>) and the gene set signature scoring procedure described above. Cell type labels assigned to each cell are available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>.</p></sec><sec id="Sec39"><title>Subsequent experiments</title><p id="Par80">In subsequent experiments, we used a combined reference dataset, which included the public data as well as data from freshly profiled cells generated in the first series of experiments (Additional&#x000a0;file&#x000a0;<xref rid="MOESM21" ref-type="media">21</xref>, note that two cell types were excluded from the reference as outliers). Single-cell datasets were annotated using a single iteration of scMatch. For single-nucleus datasets, we repeated the two-step annotation procedure described above. Cell type labels assigned to each cell or nucleus are available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>.</p></sec></sec><sec id="Sec40"><title>Stress response score</title><p id="Par81">To select genes for the stress response score, we looked at all genes induced in the warm-dissociated samples, and from that list, we manually selected genes which have been reported in the literature as stress-response-related genes. Stress response score was calculated for 17 genes (<italic>Fosb</italic>, <italic>Fos</italic>, <italic>Jun</italic>, <italic>Junb</italic>, <italic>Jund</italic>, <italic>Atf3</italic>, <italic>Egr1</italic>, <italic>Hspa1a</italic>, <italic>Hspa1b</italic>, <italic>Hsp90ab1</italic>, <italic>Hspa8</italic>, <italic>Hspb1</italic>, <italic>Ier3</italic>, <italic>Ier2</italic>, <italic>Btg1</italic>, <italic>Btg2</italic>, <italic>Dusp1</italic>) for each cell using <italic>AddModuleScore</italic> function of Seurat version 2 [<xref ref-type="bibr" rid="CR30">30</xref>]. The score represents an average expression level of these genes on a single-cell level, subtracted by the aggregated expression of control gene sets. All analyzed genes were binned based on averaged expression, and the control genes were randomly selected from each bin. Significance was determined in a Monte-Carlo procedure with 1000 randomly selected sets of 17 genes [<xref ref-type="bibr" rid="CR54">54</xref>], correction for multiple hypothesis testing was performed using Benjamini-Hochberg procedure [<xref ref-type="bibr" rid="CR55">55</xref>].</p></sec><sec id="Sec41"><title>Cell cycle phase prediction</title><p id="Par82">Cell cycle phases were inferred using <italic>CellCycleScoring</italic> function of Seurat version 2 [<xref ref-type="bibr" rid="CR30">30</xref>] with the following genes: S-genes: <italic>Atad2</italic>, <italic>Blm</italic>, <italic>Brip1</italic>, <italic>Casp8ap2</italic>, <italic>Ccne2</italic>, <italic>Cdc45</italic>, <italic>Cdc6</italic>, <italic>Cdca7</italic>, <italic>Chaf1b</italic>, <italic>Clspn</italic>, <italic>Dscc1</italic>, <italic>Dtl</italic>, <italic>E2f8</italic>, <italic>Exo1</italic>, <italic>Fen1</italic>, <italic>Gins2</italic>, <italic>Gmnn</italic>, <italic>Hells</italic>, <italic>Mcm2</italic>, <italic>Mcm4</italic>, <italic>Mcm5</italic>, <italic>Mcm6</italic>, <italic>Msh2</italic>, <italic>Nasp</italic>, <italic>Pcna</italic>, <italic>Pcna-ps2</italic>, <italic>Pola1</italic>, <italic>Pold3</italic>, <italic>Prim1</italic>, <italic>Rad51ap1</italic>, <italic>Rfc2</italic>, <italic>Rpa2</italic>, <italic>Rrm1</italic>, <italic>Rrm2</italic>, <italic>Slbp</italic>, <italic>Tipin</italic>, <italic>Tyms</italic>, <italic>Ubr7</italic>, <italic>Uhrf1</italic>, <italic>Ung</italic>, <italic>Usp1</italic>, <italic>Wdr76</italic>; G2M-genes: <italic>Anln</italic>, <italic>Anp32e</italic>, <italic>Aurka</italic>, <italic>Aurkb</italic>, <italic>Birc5</italic>, <italic>Bub1</italic>, <italic>Cbx5</italic>, <italic>Ccnb2</italic>, <italic>Cdc20</italic>, <italic>Cdc25c</italic>, <italic>Cdca2</italic>, <italic>Cdca3</italic>, <italic>Cdca8</italic>, <italic>Cdk1</italic>, <italic>Cenpa</italic>, <italic>Cenpe</italic>, <italic>Cenpf</italic>, <italic>Ckap2</italic>, <italic>Ckap2l</italic>, <italic>Ckap5</italic>, <italic>Cks1brt</italic>, <italic>Cks2</italic>, <italic>Ctcf</italic>, <italic>Dlgap5</italic>, <italic>Ect2</italic>, <italic>G2e3</italic>, <italic>Gas2l3</italic>, <italic>Gtse1</italic>, <italic>Hjurp</italic>, <italic>Hmgb2</italic>, <italic>Hmmr</italic>, <italic>Kif11</italic>, <italic>Kif20b</italic>, <italic>Kif23</italic>, <italic>Kif2c</italic>, <italic>Lbr</italic>, <italic>Mki67</italic>, <italic>Ncapd2</italic>, <italic>Ndc80</italic>, <italic>Nek2</italic>, <italic>Nuf2</italic>, <italic>Nusap1</italic>, <italic>Psrc1</italic>, <italic>Rangap1</italic>, <italic>Smc4</italic>, <italic>Tacc3</italic>, <italic>Tmpo</italic>, <italic>Top2a</italic>, <italic>Tpx2</italic>, <italic>Ttk</italic>, <italic>Tubb4b</italic>, <italic>Ube2c</italic>. Note cells not annotated as S or G2M phase are by default labelled as G1 phase.</p></sec><sec id="Sec42"><title>Bulk RNA-seq deconvolution</title><p id="Par83">BSEQ-sc was used for bulk expression deconvolution [<xref ref-type="bibr" rid="CR35">35</xref>]. In the first series of experiments, marker genes for the deconvolution were calculated from scRNA-seq data, using only cold-dissociated samples to avoid the influence of the identified warm dissociation-related biases. We also excluded cells labelled as &#x0201c;Unknown&#x0201d; and &#x0201c;CD_Trans&#x0201d; from the calculation. For each of the remaining cell types, marker genes were calculated using Seurat function <italic>FindMarkers</italic> with the following thresholds: <italic>logfc.threshold&#x02009;=&#x02009;1.5</italic>, <italic>min.pct&#x02009;=&#x02009;0.5</italic>, <italic>only.pos&#x02009;=&#x02009;T</italic>. Genes identified in more than one cell type were removed, and the remaining genes were used for the deconvolution. The same set of genes was used to deconvolve all bulk RNA-seq libraries.</p></sec></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec43"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="13059_2020_2048_MOESM1_ESM.xlsx"><caption><p><bold>Additional file 1: Table S1.</bold> Genes differentially expressed between bulk RNA-seq profiles of cold- and warm-dissociated kidney single-cell suspensions (FDR&#x02009;&#x0003c;&#x02009;0.05 and logFC &#x0003e;&#x02009;2, edgeR exact test [<xref ref-type="bibr" rid="CR27">27</xref>]); includes results of functional analysis with ToppGene [<xref ref-type="bibr" rid="CR28">28</xref>] and Gene Ontology Biological Process for differentially expressed genes with higher expression in warm-dissociated kidneys.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="13059_2020_2048_MOESM2_ESM.pdf"><caption><p><bold>Additional file 2: Supplementary Figures. Figure S1.</bold> Cell annotation procedure. <bold>Figure S2.</bold> TSNE plots showing freshly profiled cells from cold- and warm-dissociated kidneys. <bold>Figure S3.</bold> Expression and detection levels of selected marker genes. <bold>Figure S4.</bold> Cell preservation protocol performance in warm-dissociated samples. <bold>Figure S5.</bold> Cell type composition of fresh and preserved kidneys. <bold>Figure S6.</bold> Cell type composition of non-proximal tubule cells in fresh and preserved kidneys. <bold>Figure S7.</bold> Cell type composition of fresh and cryopreserved cold-dissociated samples in the repeated experiment using Balb/c female mice, 10x v3 chemistry, 2&#x02009;weeks storage, and 1200&#x02009;g spin. <bold>Figure S8.</bold> Comparison of single-cell and single-nucleus libraries in Balb/c male mice. <bold>Figure S9.</bold> Cell cycle phases inferred in scRNA-seq and snRNA-seq libraries from Balb/c male mice. <bold>Figure S10.</bold> FANS gating strategy. <bold>Figure S11.</bold> BSEQ-sc deconvolution of bulk RNA-seq profiles of cold- and warm-dissociated kidney single-cell suspensions. Three biological replicates are shown per condition. <bold>Figure S12.</bold> Comparison of ambient RNA contamination in methanol-fixed and freshly profiled aliquots of cold-dissociated samples. <bold>Figures S13&#x02013;14.</bold> Comparison of nuclei isolation protocols. <bold>Figure S15.</bold> Comparison of bulk RNA-seq profiles of intact kidneys and cold-dissociated single-cell suspensions. <bold>Figure S16</bold>. Expression of genes differentially expressed between bulk RNA-seq profiles of intact and dissociated kidneys in the matching single-cell dataset, Balb/c female mice.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="13059_2020_2048_MOESM3_ESM.xlsx"><caption><p><bold>Additional file 3: Table S2.</bold> Cell type labels assigned to cells and nuclei in this study. aLOH: ascending loop of Henle; CD_IC: intercalated cells of collecting duct; CD_IC_A: type A intercalated cells of collecting duct; CD_IC_B: type B intercalated cells of collecting duct; CD_PC: principal cells of collecting duct; CD_Trans: transitional cells; CNT: connecting tubule; DCT: distal convoluted tubule; dLOH: descending loop of Henle; MC: mesangial cells; MPH: macrophages; PT: proximal tubule.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="13059_2020_2048_MOESM4_ESM.xlsx"><caption><p><bold>Additional file 4: Table S3.</bold> Differentially expressed genes with higher expression in cell populations of warm-dissociated kidneys (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;0.5, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05); includes an incidence table indicating in how many cell types each gene was identified as differentially expressed and results of functional analysis with ToppGene [<xref ref-type="bibr" rid="CR28">28</xref>] and Gene Ontology Biological Process for differentially expressed genes identified in at least one cell type.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="13059_2020_2048_MOESM5_ESM.xlsx"><caption><p><bold>Additional file 5: Table S4.</bold> Differentially expressed genes with higher expression in cell populations of cold-dissociated kidneys (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;0.5, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05); includes an incidence table indicating in how many cell types each gene was identified as differentially expressed.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="13059_2020_2048_MOESM6_ESM.pdf"><caption><p><bold>Additional file 6.</bold> Supplementary Notes.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="13059_2020_2048_MOESM7_ESM.xlsx"><caption><p><bold>Additional file 7: Table S5.</bold> Genes differentially expressed between cryopreserved and freshly profiled cold-dissociated kidney single-cell suspensions (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;1, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05), positive logFC indicates higher expression in cryopreserved samples; includes incidence tables indicating in how many cell types each gene was identified as differentially expressed.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="13059_2020_2048_MOESM8_ESM.xlsx"><caption><p><bold>Additional file 8: Table S6.</bold> Genes differentially expressed between cryopreserved and freshly profiled warm-dissociated kidney single-cell suspensions (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;1, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05), positive logFC indicates higher expression in cryopreserved samples; includes incidence tables indicating in how many cell types each gene was identified as differentially expressed.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="13059_2020_2048_MOESM9_ESM.xlsx"><caption><p><bold>Additional file 9: Table S7.</bold> Genes differentially expressed between methanol-fixed and freshly profiled cold-dissociated kidney single-cell suspensions (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;1, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05), positive logFC indicates higher expression in methanol-fixed samples; includes incidence tables indicating in how many cell types each gene was identified as differentially expressed.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM10"><media xlink:href="13059_2020_2048_MOESM10_ESM.xlsx"><caption><p><bold>Additional file 10: Table S8.</bold> Genes differentially expressed between methanol-fixed and freshly profiled warm-dissociated kidney single-cell suspensions (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;1, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05), positive logFC indicates higher expression in methanol-fixed samples; includes incidence tables indicating in how many cell types each gene was identified as differentially expressed.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM11"><media xlink:href="13059_2020_2048_MOESM11_ESM.xlsx"><caption><p><bold>Additional file 11: Table S9.</bold> Genes differentially expressed in each cell type between SN_FANS_1x2000g and SN_FANS_3x500g protocols (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;0.5, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05), positive logFC indicates higher expression in SN_FANS_1x2000g libraries; includes incidence tables indicating in how many cell types each gene was identified as differentially expressed.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM12"><media xlink:href="13059_2020_2048_MOESM12_ESM.xlsx"><caption><p><bold>Additional file 12: Table S10.</bold> Genes differentially expressed in each cell type between SN_FANS_1x2000g and SN_sucrose protocols (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;0.5, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05), positive logFC indicates higher expression in SN_FANS_1x2000g libraries; includes incidence tables indicating in how many cell types each gene was identified as differentially expressed.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM13"><media xlink:href="13059_2020_2048_MOESM13_ESM.xlsx"><caption><p><bold>Additional file 13: Table S11.</bold> Genes differentially expressed in each cell type between SN_sucrose and SN_FANS_3x500g protocols (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;0.5, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05), positive logFC indicates higher expression in SN_sucrose libraries; includes incidence tables indicating in how many cell types each gene was identified as differentially expressed.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM14"><media xlink:href="13059_2020_2048_MOESM14_ESM.xlsx"><caption><p><bold>Additional file 14: Table S12.</bold> Number of cells in each cell population across single-cell and single-nuclei experiments and BSEQ-sc estimates for bulk RNA-seq libraries.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM15"><media xlink:href="13059_2020_2048_MOESM15_ESM.xlsx"><caption><p><bold>Additional file 15: Table S13.</bold> Genes differentially expressed between bulk RNA-seq profiles of intact kidneys and cold-dissociated single-cell suspensions; includes results of functional analysis with ToppGene [<xref ref-type="bibr" rid="CR28">28</xref>] for genes with higher expression in intact kidneys.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM16"><media xlink:href="13059_2020_2048_MOESM16_ESM.xlsx"><caption><p><bold>Additional file 16: Table S14.</bold> Genes differentially expressed between single-cell and single-nuclei libraries in Balb/c male mice profiled with v2 10x chemistry (Seurat Wilcoxon test [<xref ref-type="bibr" rid="CR30">30</xref>] with thresholds of logFC&#x02009;=&#x02009;0.5, minimum detection rate 0.5, FDR&#x02009;&#x0003c;&#x02009;0.05), positive logFC indicates higher expression in single-cell libraries; includes incidence tables indicating in how many cell types each gene was identified as differentially expressed.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM17"><media xlink:href="13059_2020_2048_MOESM17_ESM.xlsx"><caption><p><bold>Additional file 17: Table S15.</bold> Mice used and workflows tested. The experiments were performed in three batches separated in time. For each batch, we used kidneys from mice available at that time and the workflows were used with modifications as indicated in this table.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM18"><media xlink:href="13059_2020_2048_MOESM18_ESM.xlsx"><caption><p><bold>Additional file 18: Table S16.</bold> Cell viability.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM19"><media xlink:href="13059_2020_2048_MOESM19_ESM.xlsx"><caption><p><bold>Additional file 19: Table S17.</bold> Public reference dataset used for the first scMatch run.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM20"><media xlink:href="13059_2020_2048_MOESM20_ESM.xlsx"><caption><p><bold>Additional file 20: Table S18.</bold> Cell type gene signatures for refined annotation.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM21"><media xlink:href="13059_2020_2048_MOESM21_ESM.xlsx"><caption><p><bold>Additional file 21: Table S19.</bold> Extended reference dataset used for scMatch annotation.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM22"><media xlink:href="13059_2020_2048_MOESM22_ESM.docx"><caption><p><bold>Additional file 22.</bold> Review history.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s13059-020-02048-6.</p></sec><ack><title>Acknowledgements</title><p>Not applicable.</p><sec id="FPar1"><title>Review history</title><p id="Par84">The review history is available as Additional&#x000a0;file&#x000a0;<xref rid="MOESM22" ref-type="media">22</xref>.</p></sec><sec id="FPar2"><title>Peer review information</title><p id="Par85">Barbara Cheifet was the primary editor on this article and managed its editorial process and peer review in collaboration with the rest of the editorial team.</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>AF contributed to the conception and design. ED contributed to the analysis and interpretation of data with help from RH and TL. ED and AF contributed to the writing, review, and revision of the manuscript with input from all authors. DP and BS developed the SN_FANS_1x2000g protocol. OC developed the SN_sucrose protocol. BS and DP performed FANS. BG adapted the dissociation and SN_FANS_3x500g protocols. MJ, BG, and LdK generated the single cell/nucleus and bulk libraries. MJ and LdK adapted the SN_sucrose protocol. MJ performed the sequencing for the libraries. AF contributed to the study supervision. All authors read and approved the final manuscript.</p><sec id="FPar3"><title>Authors&#x02019; information</title><p id="Par86">Twitter handles: @ElenaDenisenko9 (Elena Denisenko); @BelindaGuoDr (Belinda B. Guo); @rui_hou_ (Rui Hou); @TimoLassmann (Timo Lassmann); @ryanlisterlab (Ryan Lister); @Al__Forrest (Alistair R. R. Forrest).</p></sec></notes><notes notes-type="funding-information"><title>Funding</title><p>This work was carried out with the support of a collaborative cancer research grant provided by the Cancer Research Trust &#x0201c;Enabling advanced single-cell cancer genomics in Western Australia&#x0201d; and an enabling grant from the Cancer Council of Western Australia. Genomic data was generated at the Australian Cancer Research Foundation Centre for Advanced Cancer Genomics. AF is supported by an Australian National Health and Medical Research Council Fellowship APP1154524. TL is supported by a Fellowship from the Feilman Foundation. RL was supported by a Sylvia and Charles Viertel Senior Medical Research Fellowship and Howard Hughes Medical Institute International Research Scholarship. RH is supported by an Australian Government Research Training Program (RTP) Scholarship. AF was also supported by funds raised by the MACA Ride to Conquer Cancer, and a Senior Cancer Research Fellowship from the Cancer Research Trust. Analysis was made possible with computational resources provided by the Pawsey Supercomputing Centre with funding from the Australian Government and the Government of Western Australia.</p></notes><notes notes-type="data-availability"><title>Availability of data and materials</title><p>The datasets generated and analyzed during the current study are available from the Gene Expression Omnibus (GEO) repository with the primary accession code GSE141115 [<xref ref-type="bibr" rid="CR57">57</xref>].</p></notes><notes id="FPar4"><title>Ethics approval and consent to participate</title><p id="Par87">Acknowledging the principles of 3Rs (Replacement, Reduction and Refinement), all kidneys used in this study were from mice that were euthanized by cervical dislocation as parts of other ongoing ethically approved experiments.</p></notes><notes id="FPar5"><title>Consent for publication</title><p id="Par88">Not applicable.</p></notes><notes id="FPar6" notes-type="COI-statement"><title>Competing interests</title><p id="Par89">The authors declare that they have no competing interests.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cao</surname><given-names>J</given-names></name><name><surname>Spielmann</surname><given-names>M</given-names></name><name><surname>Qiu</surname><given-names>X</given-names></name><name><surname>Huang</surname><given-names>X</given-names></name><name><surname>Ibrahim</surname><given-names>DM</given-names></name><name><surname>Hill</surname><given-names>AJ</given-names></name><etal/></person-group><article-title>The single-cell transcriptional landscape of mammalian organogenesis</article-title><source>Nature.</source><year>2019</year><volume>566</volume><fpage>496</fpage><lpage>502</lpage><?supplied-pmid 30787437?><pub-id pub-id-type="pmid">30787437</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hochane</surname><given-names>M</given-names></name><name><surname>van den Berg</surname><given-names>PR</given-names></name><name><surname>Fan</surname><given-names>X</given-names></name><name><surname>Berenger-Currias</surname><given-names>N</given-names></name><name><surname>Adegeest</surname><given-names>E</given-names></name><name><surname>Bialecka</surname><given-names>M</given-names></name><etal/></person-group><article-title>Single-cell transcriptomics reveals gene expression dynamics of human fetal kidney development</article-title><source>PLoS Biol</source><year>2019</year><volume>17</volume><fpage>e3000152</fpage><?supplied-pmid 30789893?><pub-id pub-id-type="pmid">30789893</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><mixed-citation publication-type="other">Combes AN, Phipson B, Lawlor KT, Dorison A, Patrick R, Zappia L, et al. Single cell analysis of the developing mouse kidney provides deeper insight into marker gene expression and ligand-receptor crosstalk. Development. 2019;146:dev178673.</mixed-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Aizarani</surname><given-names>N</given-names></name><name><surname>Saviano</surname><given-names>A</given-names></name><name><surname>Sagar</surname><given-names>ML</given-names></name><name><surname>Durand</surname><given-names>S</given-names></name><name><surname>Herman</surname><given-names>JS</given-names></name><etal/></person-group><article-title>A human liver cell atlas reveals heterogeneity and epithelial progenitors</article-title><source>Nature.</source><year>2019</year><volume>572</volume><fpage>199</fpage><lpage>204</lpage><?supplied-pmid 31292543?><pub-id pub-id-type="pmid">31292543</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><collab>Tabula Muris Consortium</collab></person-group><article-title>Single-cell transcriptomics of 20 mouse organs creates a Tabula Muris</article-title><source>Nature.</source><year>2018</year><volume>562</volume><fpage>367</fpage><lpage>372</lpage><pub-id pub-id-type="pmid">30283141</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lukowski</surname><given-names>SW</given-names></name><name><surname>Lo</surname><given-names>CY</given-names></name><name><surname>Sharov</surname><given-names>AA</given-names></name><name><surname>Nguyen</surname><given-names>Q</given-names></name><name><surname>Fang</surname><given-names>L</given-names></name><name><surname>Hung</surname><given-names>SS</given-names></name><etal/></person-group><article-title>A single-cell transcriptome atlas of the adult human retina</article-title><source>EMBO J</source><year>2019</year><volume>38</volume><fpage>e100811</fpage><?supplied-pmid 31436334?><pub-id pub-id-type="pmid">31436334</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Puram</surname><given-names>SV</given-names></name><name><surname>Tirosh</surname><given-names>I</given-names></name><name><surname>Parikh</surname><given-names>AS</given-names></name><name><surname>Patel</surname><given-names>AP</given-names></name><name><surname>Yizhak</surname><given-names>K</given-names></name><name><surname>Gillespie</surname><given-names>S</given-names></name><etal/></person-group><article-title>Single-cell transcriptomic analysis of primary and metastatic tumor ecosystems in head and neck cancer</article-title><source>Cell</source><year>2017</year><volume>171</volume><fpage>1611</fpage><lpage>24.e24</lpage><?supplied-pmid 29198524?><pub-id pub-id-type="pmid">29198524</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tirosh</surname><given-names>I</given-names></name><name><surname>Izar</surname><given-names>B</given-names></name><name><surname>Prakadan</surname><given-names>SM</given-names></name><name><surname>Wadsworth</surname><given-names>MH</given-names><suffix>2nd</suffix></name><name><surname>Treacy</surname><given-names>D</given-names></name><name><surname>Trombetta</surname><given-names>JJ</given-names></name><etal/></person-group><article-title>Dissecting the multicellular ecosystem of metastatic melanoma by single-cell RNA-seq</article-title><source>Science.</source><year>2016</year><volume>352</volume><fpage>189</fpage><lpage>196</lpage><?supplied-pmid 27124452?><pub-id pub-id-type="pmid">27124452</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><mixed-citation publication-type="other">Kim KT, Lee HW, Lee HO, Song HJ, Jeong da E, Shin S, et al. Application of single-cell RNA sequencing in optimizing a combinatorial therapeutic strategy in metastatic renal cell carcinoma Genome Biol 2016;17<bold>:</bold>80.</mixed-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Courtois</surname><given-names>ET</given-names></name><name><surname>Sengupta</surname><given-names>D</given-names></name><name><surname>Tan</surname><given-names>Y</given-names></name><name><surname>Chen</surname><given-names>KH</given-names></name><name><surname>Goh</surname><given-names>JJL</given-names></name><etal/></person-group><article-title>Reference component analysis of single-cell transcriptomes elucidates cellular heterogeneity in human colorectal tumors</article-title><source>Nat Genet</source><year>2017</year><volume>49</volume><fpage>708</fpage><lpage>718</lpage><?supplied-pmid 28319088?><pub-id pub-id-type="pmid">28319088</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><mixed-citation publication-type="other">Zanini F, Pu SY, Bekerman E, Einav S, Quake SR. Single-cell transcriptional dynamics of flavivirus infection. Elife. 2018;7:e32942.</mixed-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Martin</surname><given-names>JC</given-names></name><name><surname>Chang</surname><given-names>C</given-names></name><name><surname>Boschetti</surname><given-names>G</given-names></name><name><surname>Ungaro</surname><given-names>R</given-names></name><name><surname>Giri</surname><given-names>M</given-names></name><name><surname>Grout</surname><given-names>JA</given-names></name><etal/></person-group><article-title>Single-cell analysis of Crohn&#x02019;s disease lesions identifies a pathogenic cellular module associated with resistance to anti-TNF therapy</article-title><source>Cell</source><year>2019</year><volume>178</volume><fpage>1493</fpage><lpage>508.e20</lpage><?supplied-pmid 31474370?><pub-id pub-id-type="pmid">31474370</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Klein</surname><given-names>AM</given-names></name><name><surname>Mazutis</surname><given-names>L</given-names></name><name><surname>Akartuna</surname><given-names>I</given-names></name><name><surname>Tallapragada</surname><given-names>N</given-names></name><name><surname>Veres</surname><given-names>A</given-names></name><name><surname>Li</surname><given-names>V</given-names></name><etal/></person-group><article-title>Droplet barcoding for single-cell transcriptomics applied to embryonic stem cells</article-title><source>Cell.</source><year>2015</year><volume>161</volume><fpage>1187</fpage><lpage>1201</lpage><?supplied-pmid 26000487?><pub-id pub-id-type="pmid">26000487</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Macosko</surname><given-names>EZ</given-names></name><name><surname>Basu</surname><given-names>A</given-names></name><name><surname>Satija</surname><given-names>R</given-names></name><name><surname>Nemesh</surname><given-names>J</given-names></name><name><surname>Shekhar</surname><given-names>K</given-names></name><name><surname>Goldman</surname><given-names>M</given-names></name><etal/></person-group><article-title>Highly parallel genome-wide expression profiling of individual cells using nanoliter droplets</article-title><source>Cell.</source><year>2015</year><volume>161</volume><fpage>1202</fpage><lpage>1214</lpage><?supplied-pmid 26000488?><pub-id pub-id-type="pmid">26000488</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zheng</surname><given-names>GX</given-names></name><name><surname>Terry</surname><given-names>JM</given-names></name><name><surname>Belgrader</surname><given-names>P</given-names></name><name><surname>Ryvkin</surname><given-names>P</given-names></name><name><surname>Bent</surname><given-names>ZW</given-names></name><name><surname>Wilson</surname><given-names>R</given-names></name><etal/></person-group><article-title>Massively parallel digital transcriptional profiling of single cells</article-title><source>Nat Commun</source><year>2017</year><volume>8</volume><fpage>14049</fpage><?supplied-pmid 28091601?><pub-id pub-id-type="pmid">28091601</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>van den Brink</surname><given-names>SC</given-names></name><name><surname>Sage</surname><given-names>F</given-names></name><name><surname>Vertesy</surname><given-names>A</given-names></name><name><surname>Spanjaard</surname><given-names>B</given-names></name><name><surname>Peterson-Maduro</surname><given-names>J</given-names></name><name><surname>Baron</surname><given-names>CS</given-names></name><etal/></person-group><article-title>Single-cell sequencing reveals dissociation-induced gene expression in tissue subpopulations</article-title><source>Nat Methods</source><year>2017</year><volume>14</volume><fpage>935</fpage><lpage>936</lpage><?supplied-pmid 28960196?><pub-id pub-id-type="pmid">28960196</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Potter</surname><given-names>SS</given-names></name></person-group><article-title>Single-cell RNA sequencing for the study of development, physiology and disease</article-title><source>Nat Rev Nephrol</source><year>2018</year><volume>14</volume><fpage>479</fpage><lpage>492</lpage><?supplied-pmid 29789704?><pub-id pub-id-type="pmid">29789704</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Adam</surname><given-names>M</given-names></name><name><surname>Potter</surname><given-names>AS</given-names></name><name><surname>Potter</surname><given-names>SS</given-names></name></person-group><article-title>Psychrophilic proteases dramatically reduce single-cell RNA-seq artifacts: a molecular atlas of kidney development</article-title><source>Development.</source><year>2017</year><volume>144</volume><fpage>3625</fpage><lpage>3632</lpage><?supplied-pmid 28851704?><pub-id pub-id-type="pmid">28851704</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lake</surname><given-names>BB</given-names></name><name><surname>Ai</surname><given-names>R</given-names></name><name><surname>Kaeser</surname><given-names>GE</given-names></name><name><surname>Salathia</surname><given-names>NS</given-names></name><name><surname>Yung</surname><given-names>YC</given-names></name><name><surname>Liu</surname><given-names>R</given-names></name><etal/></person-group><article-title>Neuronal subtypes and diversity revealed by single-nucleus RNA sequencing of the human brain</article-title><source>Science.</source><year>2016</year><volume>352</volume><fpage>1586</fpage><lpage>1590</lpage><?supplied-pmid 27339989?><pub-id pub-id-type="pmid">27339989</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Krishnaswami</surname><given-names>SR</given-names></name><name><surname>Grindberg</surname><given-names>RV</given-names></name><name><surname>Novotny</surname><given-names>M</given-names></name><name><surname>Venepally</surname><given-names>P</given-names></name><name><surname>Lacar</surname><given-names>B</given-names></name><name><surname>Bhutani</surname><given-names>K</given-names></name><etal/></person-group><article-title>Using single nuclei for RNA-seq to capture the transcriptome of postmortem neurons</article-title><source>Nat Protoc</source><year>2016</year><volume>11</volume><fpage>499</fpage><lpage>524</lpage><?supplied-pmid 26890679?><pub-id pub-id-type="pmid">26890679</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alles</surname><given-names>J</given-names></name><name><surname>Karaiskos</surname><given-names>N</given-names></name><name><surname>Praktiknjo</surname><given-names>SD</given-names></name><name><surname>Grosswendt</surname><given-names>S</given-names></name><name><surname>Wahle</surname><given-names>P</given-names></name><name><surname>Ruffault</surname><given-names>PL</given-names></name><etal/></person-group><article-title>Cell fixation and preservation for droplet-based single-cell transcriptomics</article-title><source>BMC Biol</source><year>2017</year><volume>15</volume><fpage>44</fpage><?supplied-pmid 28526029?><pub-id pub-id-type="pmid">28526029</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wohnhaas</surname><given-names>CT</given-names></name><name><surname>Leparc</surname><given-names>GG</given-names></name><name><surname>Fernandez-Albert</surname><given-names>F</given-names></name><name><surname>Kind</surname><given-names>D</given-names></name><name><surname>Gantner</surname><given-names>F</given-names></name><name><surname>Viollet</surname><given-names>C</given-names></name><etal/></person-group><article-title>DMSO cryopreservation is the method of choice to preserve cells for droplet-based single-cell RNA sequencing</article-title><source>Sci Rep</source><year>2019</year><volume>9</volume><fpage>10699</fpage><?supplied-pmid 31337793?><pub-id pub-id-type="pmid">31337793</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guillaumet-Adkins</surname><given-names>A</given-names></name><name><surname>Rodriguez-Esteban</surname><given-names>G</given-names></name><name><surname>Mereu</surname><given-names>E</given-names></name><name><surname>Mendez-Lago</surname><given-names>M</given-names></name><name><surname>Jaitin</surname><given-names>DA</given-names></name><name><surname>Villanueva</surname><given-names>A</given-names></name><etal/></person-group><article-title>Single-cell transcriptome conservation in cryopreserved cells and tissues</article-title><source>Genome Biol</source><year>2017</year><volume>18</volume><fpage>45</fpage><?supplied-pmid 28249587?><pub-id pub-id-type="pmid">28249587</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bakken</surname><given-names>TE</given-names></name><name><surname>Hodge</surname><given-names>RD</given-names></name><name><surname>Miller</surname><given-names>JA</given-names></name><name><surname>Yao</surname><given-names>Z</given-names></name><name><surname>Nguyen</surname><given-names>TN</given-names></name><name><surname>Aevermann</surname><given-names>B</given-names></name><etal/></person-group><article-title>Single-nucleus and single-cell transcriptomes compared in matched cortical cell types</article-title><source>PLoS One</source><year>2018</year><volume>13</volume><fpage>e0209648</fpage><?supplied-pmid 30586455?><pub-id pub-id-type="pmid">30586455</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lake</surname><given-names>BB</given-names></name><name><surname>Codeluppi</surname><given-names>S</given-names></name><name><surname>Yung</surname><given-names>YC</given-names></name><name><surname>Gao</surname><given-names>D</given-names></name><name><surname>Chun</surname><given-names>J</given-names></name><name><surname>Kharchenko</surname><given-names>PV</given-names></name><etal/></person-group><article-title>A comparative strategy for single-nucleus and single-cell transcriptomes confirms accuracy in predicted cell-type expression from nuclear RNA</article-title><source>Sci Rep</source><year>2017</year><volume>7</volume><fpage>6031</fpage><?supplied-pmid 28729663?><pub-id pub-id-type="pmid">28729663</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>H</given-names></name><name><surname>Kirita</surname><given-names>Y</given-names></name><name><surname>Donnelly</surname><given-names>EL</given-names></name><name><surname>Humphreys</surname><given-names>BD</given-names></name></person-group><article-title>Advantages of single-nucleus over single-cell RNA sequencing of adult kidney: rare cell types and novel cell states revealed in fibrosis</article-title><source>J Am Soc Nephrol</source><year>2019</year><volume>30</volume><fpage>23</fpage><lpage>32</lpage><?supplied-pmid 30510133?><pub-id pub-id-type="pmid">30510133</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Robinson</surname><given-names>MD</given-names></name><name><surname>McCarthy</surname><given-names>DJ</given-names></name><name><surname>Smyth</surname><given-names>GK</given-names></name></person-group><article-title>edgeR: a Bioconductor package for differential expression analysis of digital gene expression data</article-title><source>Bioinformatics.</source><year>2010</year><volume>26</volume><fpage>139</fpage><lpage>140</lpage><?supplied-pmid 19910308?><pub-id pub-id-type="pmid">19910308</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>J</given-names></name><name><surname>Bardes</surname><given-names>EE</given-names></name><name><surname>Aronow</surname><given-names>BJ</given-names></name><name><surname>Jegga</surname><given-names>AG</given-names></name></person-group><article-title>ToppGene Suite for gene list enrichment analysis and candidate gene prioritization</article-title><source>Nucleic Acids Res</source><year>2009</year><volume>37</volume><fpage>W305</fpage><lpage>W311</lpage><?supplied-pmid 19465376?><pub-id pub-id-type="pmid">19465376</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><mixed-citation publication-type="other">Smid M, Coebergh van den Braak RRJ, van de Werken HJG, van Riet J, van Galen A, de Weerd V, et al. Gene length corrected trimmed mean of M-values (GeTMM) processing of RNA-seq data performs similarly in intersample analyses while improving intrasample comparisons. BMC Bioinformatics. 2018;19:236.</mixed-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Butler</surname><given-names>A</given-names></name><name><surname>Hoffman</surname><given-names>P</given-names></name><name><surname>Smibert</surname><given-names>P</given-names></name><name><surname>Papalexi</surname><given-names>E</given-names></name><name><surname>Satija</surname><given-names>R</given-names></name></person-group><article-title>Integrating single-cell transcriptomic data across different conditions, technologies, and species</article-title><source>Nat Biotechnol</source><year>2018</year><volume>36</volume><fpage>411</fpage><lpage>420</lpage><?supplied-pmid 29608179?><pub-id pub-id-type="pmid">29608179</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hou</surname><given-names>R</given-names></name><name><surname>Denisenko</surname><given-names>E</given-names></name><name><surname>Forrest</surname><given-names>ARR</given-names></name></person-group><article-title>scMatch: a single-cell gene expression profile annotation tool using reference datasets</article-title><source>Bioinformatics.</source><year>2019</year><volume>35</volume><fpage>4688</fpage><lpage>4695</lpage><?supplied-pmid 31028376?><pub-id pub-id-type="pmid">31028376</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Park</surname><given-names>J</given-names></name><name><surname>Shrestha</surname><given-names>R</given-names></name><name><surname>Qiu</surname><given-names>C</given-names></name><name><surname>Kondo</surname><given-names>A</given-names></name><name><surname>Huang</surname><given-names>S</given-names></name><name><surname>Werth</surname><given-names>M</given-names></name><etal/></person-group><article-title>Single-cell transcriptomics of the mouse kidney reveals potential cellular targets of kidney disease</article-title><source>Science.</source><year>2018</year><volume>360</volume><fpage>758</fpage><lpage>763</lpage><?supplied-pmid 29622724?><pub-id pub-id-type="pmid">29622724</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Karaiskos</surname><given-names>N</given-names></name><name><surname>Rahmatollahi</surname><given-names>M</given-names></name><name><surname>Boltengagen</surname><given-names>A</given-names></name><name><surname>Liu</surname><given-names>H</given-names></name><name><surname>Hoehne</surname><given-names>M</given-names></name><name><surname>Rinschen</surname><given-names>M</given-names></name><etal/></person-group><article-title>A single-cell transcriptome atlas of the mouse glomerulus</article-title><source>J Am Soc Nephrol</source><year>2018</year><volume>29</volume><fpage>2060</fpage><lpage>2068</lpage><?supplied-pmid 29794128?><pub-id pub-id-type="pmid">29794128</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Clark</surname><given-names>JZ</given-names></name><name><surname>Chen</surname><given-names>L</given-names></name><name><surname>Chou</surname><given-names>CL</given-names></name><name><surname>Jung</surname><given-names>HJ</given-names></name><name><surname>Lee</surname><given-names>JW</given-names></name><name><surname>Knepper</surname><given-names>MA</given-names></name></person-group><article-title>Representation and relative abundance of cell-type selective markers in whole-kidney RNA-Seq data</article-title><source>Kidney Int</source><year>2019</year><volume>95</volume><fpage>787</fpage><lpage>796</lpage><?supplied-pmid 30826016?><pub-id pub-id-type="pmid">30826016</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Baron</surname><given-names>M</given-names></name><name><surname>Veres</surname><given-names>A</given-names></name><name><surname>Wolock</surname><given-names>SL</given-names></name><name><surname>Faust</surname><given-names>AL</given-names></name><name><surname>Gaujoux</surname><given-names>R</given-names></name><name><surname>Vetere</surname><given-names>A</given-names></name><etal/></person-group><article-title>A single-cell transcriptomic map of the human and mouse pancreas reveals inter- and intra-cell population structure</article-title><source>Cell Syst</source><year>2016</year><volume>3</volume><fpage>346</fpage><lpage>60.e4</lpage><?supplied-pmid 27667365?><pub-id pub-id-type="pmid">27667365</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>O'Sullivan</surname><given-names>ED</given-names></name><name><surname>Mylonas</surname><given-names>KJ</given-names></name><name><surname>Hughes</surname><given-names>J</given-names></name><name><surname>Ferenbach</surname><given-names>DA</given-names></name></person-group><article-title>Complementary roles for single-nucleus and single-cell RNA sequencing in kidney disease research</article-title><source>J Am Soc Nephrol</source><year>2019</year><volume>30</volume><fpage>712</fpage><lpage>713</lpage><?supplied-pmid 30867246?><pub-id pub-id-type="pmid">30867246</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><mixed-citation publication-type="other">Slyper M, Porter CBM, Ashenberg O, Waldman J, Drokhlyansky E, Wakiro I, et al. A single-cell and single-nucleus RNA-seq toolbox for fresh and frozen human tumors. bioRxiv. 2019; 10.1101/761429.</mixed-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yang</surname><given-names>S</given-names></name><name><surname>Corbett</surname><given-names>SE</given-names></name><name><surname>Koga</surname><given-names>Y</given-names></name><name><surname>Wang</surname><given-names>Z</given-names></name><name><surname>Johnson</surname><given-names>WE</given-names></name><name><surname>Yajima</surname><given-names>M</given-names></name><etal/></person-group><article-title>Decontamination of ambient RNA in single-cell RNA-seq with DecontX</article-title><source>Genome Biol</source><year>2020</year><volume>21</volume><fpage>57</fpage><?supplied-pmid 32138770?><pub-id pub-id-type="pmid">32138770</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><mixed-citation publication-type="other">Young MD, Behjati S. SoupX removes ambient RNA contamination from droplet based single-cell RNA sequencing data. bioRxiv. 2020; 10.1101/303727.</mixed-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Korsunsky</surname><given-names>I</given-names></name><name><surname>Millard</surname><given-names>N</given-names></name><name><surname>Fan</surname><given-names>J</given-names></name><name><surname>Slowikowski</surname><given-names>K</given-names></name><name><surname>Zhang</surname><given-names>F</given-names></name><name><surname>Wei</surname><given-names>K</given-names></name><etal/></person-group><article-title>Fast, sensitive and accurate integration of single-cell data with Harmony</article-title><source>Nat Methods</source><year>2019</year><volume>16</volume><fpage>1289</fpage><lpage>1296</lpage><?supplied-pmid 31740819?><pub-id pub-id-type="pmid">31740819</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>J</given-names></name><name><surname>Cheung</surname><given-names>F</given-names></name><name><surname>Shi</surname><given-names>R</given-names></name><name><surname>Zhou</surname><given-names>H</given-names></name><name><surname>Lu</surname><given-names>W</given-names></name></person-group><article-title>PBMC fixation and processing for chromium single-cell RNA sequencing</article-title><source>J Transl Med</source><year>2018</year><volume>16</volume><fpage>198</fpage><?supplied-pmid 30016977?><pub-id pub-id-type="pmid">30016977</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><mixed-citation publication-type="other">Martelotto L. &#x02018;Frankenstein&#x02019; protocol for nuclei isolation from fresh and frozen tissue for snRNA-seq. Protocolsio. 2019. 10.17504/protocols.io.3eqgjdw.</mixed-citation></ref><ref id="CR43"><label>43.</label><mixed-citation publication-type="other">10x Genomics: Isolation of nuclei for single cell RNA sequencing. In: Demonstrated Protocols. 10x Genomics Support. 2018. <ext-link ext-link-type="uri" xlink:href="https://support.10xgenomics.com/single-cell-gene-expression/sample-prep/doc/demonstrated-protocol-isolation-of-nuclei-for-single-cell-rna-sequencing">https://support.10xgenomics.com/single-cell-gene-expression/sample-prep/doc/demonstrated-protocol-isolation-of-nuclei-for-single-cell-rna-sequencing</ext-link>. Accessed 5 Sept 2018.</mixed-citation></ref><ref id="CR44"><label>44.</label><mixed-citation publication-type="other">Babraham Bioinformatics. FastQC. <ext-link ext-link-type="uri" xlink:href="https://www.bioinformatics.babraham.ac.uk/projects/fastqc/">https://www.bioinformatics.babraham.ac.uk/projects/fastqc/</ext-link>. Accessed 4 Jan 2019.</mixed-citation></ref><ref id="CR45"><label>45.</label><mixed-citation publication-type="other">Babraham Bioinformatics. Trim Galore. <ext-link ext-link-type="uri" xlink:href="https://www.bioinformatics.babraham.ac.uk/projects/trim_galore/">https://www.bioinformatics.babraham.ac.uk/projects/trim_galore/</ext-link>. Accessed 4 Jan 2019.</mixed-citation></ref><ref id="CR46"><label>46.</label><mixed-citation publication-type="other"><italic>Mus musculus</italic> ribosomal DNA, complete repeating unit. <ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/nuccore/BK000964">https://www.ncbi.nlm.nih.gov/nuccore/BK000964</ext-link>. Accessed 24 Jan 2019.</mixed-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lassmann</surname><given-names>T</given-names></name></person-group><article-title>TagDust2: a generic method to extract reads from sequencing data</article-title><source>BMC Bioinformatics</source><year>2015</year><volume>16</volume><fpage>24</fpage><?supplied-pmid 25627334?><pub-id pub-id-type="pmid">25627334</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dobin</surname><given-names>A</given-names></name><name><surname>Davis</surname><given-names>CA</given-names></name><name><surname>Schlesinger</surname><given-names>F</given-names></name><name><surname>Drenkow</surname><given-names>J</given-names></name><name><surname>Zaleski</surname><given-names>C</given-names></name><name><surname>Jha</surname><given-names>S</given-names></name><etal/></person-group><article-title>STAR: ultrafast universal RNA-seq aligner</article-title><source>Bioinformatics.</source><year>2013</year><volume>29</volume><fpage>15</fpage><lpage>21</lpage><?supplied-pmid 23104886?><pub-id pub-id-type="pmid">23104886</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><mixed-citation publication-type="other">Broad Institute of MIT and Harvard. Picard MarkDuplicates. <ext-link ext-link-type="uri" xlink:href="https://broadinstitute.github.io/picard/command-line-overview.html#MarkDuplicates">https://broadinstitute.github.io/picard/command-line-overview.html#MarkDuplicates</ext-link>. Accessed 4 Jan 2019.</mixed-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liao</surname><given-names>Y</given-names></name><name><surname>Smyth</surname><given-names>GK</given-names></name><name><surname>Shi</surname><given-names>W</given-names></name></person-group><article-title>featureCounts: an efficient general purpose program for assigning sequence reads to genomic features</article-title><source>Bioinformatics.</source><year>2014</year><volume>30</volume><fpage>923</fpage><lpage>930</lpage><?supplied-pmid 24227677?><pub-id pub-id-type="pmid">24227677</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><mixed-citation publication-type="other">10x Genomics. Creating a Reference Package. <ext-link ext-link-type="uri" xlink:href="https://support.10xgenomics.com/single-cell-gene-expression/software/pipelines/latest/advanced/references#premrna">https://support.10xgenomics.com/single-cell-gene-expression/software/pipelines/latest/advanced/references#premrna</ext-link>. Accessed 1 May 2019.</mixed-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lun</surname><given-names>ATL</given-names></name><name><surname>Riesenfeld</surname><given-names>S</given-names></name><name><surname>Andrews</surname><given-names>T</given-names></name><name><surname>Dao</surname><given-names>TP</given-names></name><name><surname>Gomes</surname><given-names>T</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name></person-group><article-title>EmptyDrops: distinguishing cells from empty droplets in droplet-based single-cell RNA sequencing data</article-title><source>Genome Biol</source><year>2019</year><volume>20</volume><fpage>63</fpage><?supplied-pmid 30902100?><pub-id pub-id-type="pmid">30902100</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>McCarthy</surname><given-names>DJ</given-names></name><name><surname>Campbell</surname><given-names>KR</given-names></name><name><surname>Lun</surname><given-names>AT</given-names></name><name><surname>Wills</surname><given-names>QF</given-names></name></person-group><article-title>Scater: pre-processing, quality control, normalization and visualization of single-cell RNA-seq data in R</article-title><source>Bioinformatics.</source><year>2017</year><volume>33</volume><fpage>1179</fpage><lpage>1186</lpage><?supplied-pmid 28088763?><pub-id pub-id-type="pmid">28088763</pub-id></element-citation></ref><ref id="CR54"><label>54.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>North</surname><given-names>BV</given-names></name><name><surname>Curtis</surname><given-names>D</given-names></name><name><surname>Sham</surname><given-names>PC</given-names></name></person-group><article-title>A note on the calculation of empirical P values from Monte Carlo procedures</article-title><source>Am J Hum Genet</source><year>2002</year><volume>71</volume><fpage>439</fpage><lpage>441</lpage><?supplied-pmid 12111669?><pub-id pub-id-type="pmid">12111669</pub-id></element-citation></ref><ref id="CR55"><label>55.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Benjamini</surname><given-names>Y</given-names></name><name><surname>Hochberg</surname><given-names>Y</given-names></name></person-group><article-title>Controlling the false discovery rate: a practical and powerful approach to multiple testing</article-title><source>J R Stat Soc Ser B Methodol</source><year>1995</year><volume>57</volume><fpage>289</fpage><lpage>300</lpage></element-citation></ref><ref id="CR56"><label>56.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rohrwasser</surname><given-names>A</given-names></name><name><surname>Ishigami</surname><given-names>T</given-names></name><name><surname>Gociman</surname><given-names>B</given-names></name><name><surname>Lantelme</surname><given-names>P</given-names></name><name><surname>Morgan</surname><given-names>T</given-names></name><name><surname>Cheng</surname><given-names>T</given-names></name><etal/></person-group><article-title>Renin and kallikrein in connecting tubule of mouse</article-title><source>Kidney Int</source><year>2003</year><volume>64</volume><fpage>2155</fpage><lpage>2162</lpage><?supplied-pmid 14633138?><pub-id pub-id-type="pmid">14633138</pub-id></element-citation></ref><ref id="CR57"><label>57.</label><mixed-citation publication-type="other">Denisenko E, Guo B, Jones M, Hou R, de Kock L, Lassmann T, et al. Systematic assessment of tissue dissociation and storage biases in single-cell and single-nucleus RNA-seq workflows. Datasets. Gene Expression Omnibus. 2020. <ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE141115">https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE141115</ext-link>. Accessed 11 May 2020.</mixed-citation></ref></ref-list></back></article>