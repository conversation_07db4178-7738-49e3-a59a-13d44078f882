<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">5905221</article-id><article-id pub-id-type="publisher-id">2146</article-id><article-id pub-id-type="doi">10.1186/s12859-018-2146-x</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>Prediction of microRNA-disease associations based on distance correlation set</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Zhao</surname><given-names>Haochen</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Kuang</surname><given-names>Linai</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-5065-8447</contrib-id><name><surname>Wang</surname><given-names>Lei</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Ping</surname><given-names>Pengyao</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Xuan</surname><given-names>Zhanwei</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Pei</surname><given-names>Tingrui</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Wu</surname><given-names>Zhelun</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="GRID">grid.448798.e</institution-id><institution>College of Computer Engineering &#x00026; Applied Mathematics, Changsha University, </institution></institution-wrap>Changsha, 410001 Hunan People&#x02019;s Republic of China </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0000 8633 7608</institution-id><institution-id institution-id-type="GRID">grid.412982.4</institution-id><institution>Key Laboratory of Intelligent Computing &#x00026; Information Processing (Xiangtan University), Ministry of Education, China, </institution></institution-wrap>Xiangtan, 411105 Hunan People&#x02019;s Republic of China </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0687 7127</institution-id><institution-id institution-id-type="GRID">grid.258900.6</institution-id><institution>Department of Computer Science, </institution><institution>Lakehead University, </institution></institution-wrap>Thunder Bay, ON P7B5E1 Canada </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2097 5006</institution-id><institution-id institution-id-type="GRID">grid.16750.35</institution-id><institution>Department of Computer Science, </institution><institution>Princeton University, </institution></institution-wrap>Princeton, New Jersey USA </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0000 8633 7608</institution-id><institution-id institution-id-type="GRID">grid.412982.4</institution-id><institution>College of Information Engineering, </institution><institution>Xiangtan University, </institution></institution-wrap>Xiangtan, Hunan People&#x02019;s Republic of China </aff></contrib-group><pub-date pub-type="epub"><day>17</day><month>4</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>17</day><month>4</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>19</volume><elocation-id>141</elocation-id><history><date date-type="received"><day>18</day><month>10</month><year>2017</year></date><date date-type="accepted"><day>3</day><month>4</month><year>2018</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Recently, numerous laboratory studies have indicated that many microRNAs (miRNAs) are involved in and associated with human diseases and can serve as potential biomarkers and drug targets. Therefore, developing effective computational models for the prediction of novel associations between diseases and miRNAs could be beneficial for achieving an understanding of disease mechanisms at the miRNA level and the interactions between diseases and miRNAs at the disease level. Thus far, only a few miRNA-disease association pairs are known, and models analyzing miRNA-disease associations based on lncRNA are limited.</p></sec><sec><title>Results</title><p id="Par2">In this study, a new computational method based on a distance correlation set is developed to predict miRNA-disease associations (DCSMDA) by integrating known lncRNA-disease associations, known miRNA-lncRNA associations, disease semantic similarity, and various lncRNA and disease similarity measures. The novelty of DCSMDA is due to the construction of a miRNA-lncRNA-disease network, which reveals that DCSMDA can be applied to predict potential lncRNA-disease associations without requiring any known miRNA-disease associations. Although the implementation of DCSMDA does not require known disease-miRNA associations, the area under curve is 0.8155 in the leave-one-out cross validation. Furthermore, DCSMDA was implemented in case studies of prostatic neoplasms, lung neoplasms and leukaemia, and of the top 10 predicted associations, 10, 9 and 9 associations, respectively, were separately verified in other independent studies and biological experimental studies. In addition, 10 of the 10 (100%) associations predicted by DCSMDA were supported by recent bioinformatical studies.</p></sec><sec><title>Conclusions</title><p id="Par3">According to the simulation results, DCSMDA can be a great addition to the biomedical research field.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s12859-018-2146-x) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>MiRNA-disease association predictions</kwd><kwd>Distance correlation set</kwd><kwd>Disease-lncRNA-miRNA network</kwd><kwd>Similarity measure</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001809</institution-id><institution>National Natural Science Foundation of China</institution></institution-wrap></funding-source><award-id>No.61640210</award-id><award-id>No.61672447</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution>CERNET Next Generation Internet Technology Innovation Project</institution></funding-source><award-id>No. NGII20160305</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution>Science &#x00026; Education Joint Project of Hunan Natural Science Foundation</institution></funding-source><award-id>No.2017JJ5036</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution>Upgrading Project of Industry-University- Research of Xiangtan University</institution></funding-source><award-id>No.11KZ|KZ03051</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par13">For a long time, RNA was considered a DNA-to-protein gene sequence transporter [<xref ref-type="bibr" rid="CR1">1</xref>]. The sequencing of the human genome indicates that only approximately 2% of the sequences in human RNA are used to encode proteins [<xref ref-type="bibr" rid="CR2">2</xref>]. Furthermore, numerous studies performing biological experiments have indicated that noncoding RNA (ncRNA) plays an important role in numerous critical biological processes, such as chromosome dosage compensation, epigenetic regulation and cell growth [<xref ref-type="bibr" rid="CR3">3</xref>&#x02013;<xref ref-type="bibr" rid="CR5">5</xref>]. MicroRNAs (miRNAs) are endogenous single-stranded ncRNA molecules approximately 22&#x000a0;nt in length that regulate the expression of target genes by base pairing with the 3&#x02032;-untranslated regions (UTRs) of the target genes [<xref ref-type="bibr" rid="CR6">6</xref>, <xref ref-type="bibr" rid="CR7">7</xref>]. Recently, several studies have reported that more than one-third of genes are regulated by miRNAs [<xref ref-type="bibr" rid="CR8">8</xref>], and more than 1000 miRNAs have been identified using various experimental methods and computational models [<xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR10">10</xref>]. In addition, accumulating evidence indicates that many microRNAs (miRNAs) are involved in and associated with human diseases, such as myocardial disease, Alzheimer&#x02019;s disease, cardiovascular disease and heart disease [<xref ref-type="bibr" rid="CR11">11</xref>&#x02013;<xref ref-type="bibr" rid="CR14">14</xref>]. Therefore, identifying disease-miRNA associations could not only improve our knowledge of the underlying disease mechanism at the miRNA level but also facilitate disease biomarker detection and drug discovery for disease diagnosis, treatment, prognosis and prevention. However, compared with the rapidly increasing number of newly discovered miRNAs, only a few miRNA-disease associations are known [<xref ref-type="bibr" rid="CR15">15</xref>, <xref ref-type="bibr" rid="CR16">16</xref>]. Developing efficient, successful computational approaches that predict potential miRNA-disease associations is challenging and urgently needed.</p><p id="Par14">Recently, several heterogeneous biological datasets, such as HMDD and miR2Disease, have been constructed [<xref ref-type="bibr" rid="CR17">17</xref>&#x02013;<xref ref-type="bibr" rid="CR19">19</xref>], and several computational methods are used to predict potential miRNA-disease associations based these datasets [<xref ref-type="bibr" rid="CR20">20</xref>&#x02013;<xref ref-type="bibr" rid="CR22">22</xref>]. For example, Jiang et al. developed a scoring system to assess the likelihood that a microRNA is involved in a specific disease phenotype based on the assumption that functionally related microRNAs tend to be associated with phenotypically similar diseases [<xref ref-type="bibr" rid="CR23">23</xref>]. K. Han et al. developed a prediction method called DismiPred that combines functional similarity and common association information to predict potential miRNA-disease associations based on the central hypothesis offered in several previous studies that miRNAs with similar functions are often involved in similar diseases [<xref ref-type="bibr" rid="CR24">24</xref>]. Furthermore, Xuan et al. proposed a method called HDMP to predict potential disease-miRNA associations based on weighted k most similar neighbours [<xref ref-type="bibr" rid="CR25">25</xref>] and developed a method for predicting potential disease-associated microRNAs based on random walk (MIDP) [<xref ref-type="bibr" rid="CR26">26</xref>]. Chen et al. proposed a prediction method called RWRMDA by implementing random walk on the miRNA functional similarity network and further proposed a model called RLSMDA based on semi-supervised learning by integrating a disease-disease semantic similarity network, miRNA-miRNA functional similarity network, and known human miRNA-disease associations for the prediction of potential disease-miRNA associations [<xref ref-type="bibr" rid="CR27">27</xref>]. In 2016, based on the assumption that functionally similar miRNAs tend to be involved in similar diseases, Chen et al. developed a prediction model called WBSMDA by integrating known miRNA-disease associations, miRNA functional similarity networks, disease semantic similarity networks, and Gaussian interaction profile kernel similarity networks to uncover potential disease-miRNA associations [<xref ref-type="bibr" rid="CR28">28</xref>].</p><p id="Par15">In the abovementioned computational models, known miRNA-disease associations are required. However, few lncRNA-disease associations have been recorded in several biological datasets, such as MNDR and LncRNADisease [<xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR30">30</xref>], and several studies have shown that lncRNA-miRNA associations are involved in and associated with human diseases [<xref ref-type="bibr" rid="CR31">31</xref>&#x02013;<xref ref-type="bibr" rid="CR33">33</xref>]. Thus, in this article, a new model based on the Distance Correlation Set for MiRNA-Disease Association inference (DCSMDA) was developed to predict potential miRNA-disease associations by integrating known lncRNA-disease and lncRNA-miRNA associations, the semantic similarity and functional similarity of the disease pairs, the functional similarity of the miRNA pairs, and the Gaussian interaction profile kernel similarity for the lncRNA, miRNA and disease. Compared with existing state-of-the-art models, the advantage of DCSMDA is its integration of the similarity of the disease pairs, lncRNA pairs, miRNA pairs, and introduction of the distance correlation set; thus, DCSMDA does not require known miRNA-disease associations. Moreover, leave-one-out cross-validation (LOOCV) was implemented to evaluate the performance of DCSMDA based on known miRNA-disease associations downloaded from the HMDD database, and DCSMDA achieved a reliable area under the ROC curve (AUC) of 0.8155. Moreover, case studies of lung neoplasms, prostatic neoplasms and leukaemia were implemented to further evaluate the prediction performance of DCSMDA, and 9, 10 and 9 of the top 10 predicted associations in these three important human complex diseases have been confirmed by recent biological experiments. In addition, a case study identifying the top 10 lncRNA-disease associations showed that 10 of the 10 (100%) associations predicted by DCSMDA were supported by recent bioinformatical studies and the latest HMDD dataset, effectively demonstrating that DCSMDA had a good prediction performance in inferring potential disease-miRNA associations.</p></sec><sec id="Sec2"><title>Results</title><p id="Par16">To evaluate the prediction performance of DCSMDA, first, our method was compared with other state-of-the-art methods in the framework of the LOOCV, and then, we analyzed the stability of DCSMDA using three lncRNA-disease datasets. Second, we analyzed the effect of the pre-determined threshold parameter <italic>b</italic>. Finally, several additional experiments were performed to validate the feasibility of our method.</p><sec id="Sec3"><title>Performance comparison with other methods</title><p id="Par17">Since our method is unsupervised (i.e., known miRNA-disease associations are not used in the training) and the few proposed prediction models for the large-scale forecasting of the associations between miRNAs and diseases are simultaneously based on known miRNA-lncRNA associations and known lncRNA-disease associations, to validate the prediction performance of our novel model, we compared the prediction performance of DCSMDA with that of three state-of-the-art computational prediction models, including WBSMDA [<xref ref-type="bibr" rid="CR28">28</xref>], RLSMDA [<xref ref-type="bibr" rid="CR27">27</xref>] and HGLDA [<xref ref-type="bibr" rid="CR31">31</xref>]; WBSMDA and RLSMDA are semi-supervised methods that do not require any negative samples, and HGLDA is an unsupervised method developed to predict potential lncRNA-disease associations by integrating known miRNA-disease associations and lncRNA-miRNA interactions.</p><p id="Par18">To compare the performance of DCSMDA with that of WBSMDA and RLSMDA, we adopted the <italic>DS</italic><sub>5</sub> dataset and the framework of the LOOCV. While the LOOCV was implemented for these three methods, each known miRNA-disease association was left out in turn as the test sample, and we further evaluated how well this test association ranked relative to the candidate sample. Here, the candidate samples comprised all potential miRNA-disease associations without any known association evidence. Then, the testing samples with a prediction rank higher than the given threshold were considered successfully predicted. If the testing samples with a prediction rank higher than the given threshold were considered successfully predicted, then DCSMDA, RLSMDA and WBSMDA were checked in the LOOCV.</p><p id="Par19">To compare the performance of DCSMDA with that of HGLDA, we adopted the <italic>DS</italic><sub>3</sub> dataset and the framework of the LOOCV. While the LOOCV was implemented for HGLDA, each known lncRNA-disease association was removed individually as a testing sample, and we further evaluated how well this test lncRNA-disease association ranked relative to the candidate sample. Here, the candidate samples comprised all potential lncRNA-disease associations without any known association evidence.</p><p id="Par20">Thus, we could further obtain the corresponding true positive rates (TPR, sensitivity) and false positive rates (FPR, 1-specificity) by setting different thresholds. Here, sensitivity refers to the percentage of test samples that were predicted with ranks higher than the given threshold, and the specificity was computed as the percentage of negative samples with ranks lower than the threshold. The receiver-operating characteristic (ROC) curves were generated by plotting the TPR versus the FPR at different thresholds. Then, the AUCs were further calculated to evaluate the prediction performance of DCSMDA.</p><p id="Par21">An AUC value of 1 represented a perfect prediction, while an AUC value of 0.5 indicated a purely random performance. The performance comparison in terms of the LOOCV results is shown in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>. In the LOOCV, the DCSMDA (when <italic>b</italic> was set to 6), RLSMDA, WBSMDA and HGLDA achieved AUCs of 0.8155, 0.7826, 0.7582 and 0.7621, respectively. DCSMDA predicted potential miRNA-disease associations without requiring known miRNA-disease associations. To the best of our knowledge, no methods that rely on known miRNA-disease associations exist. More importantly, considering that known disease-lncRNA associations remain very limited, the performance of DCSMDA can be further improved as additional known miRNA-disease associations are obtained in the future.<fig id="Fig1"><label>Fig. 1</label><caption><p>Performance comparisons between DCSMDA, RLSMDA and HGLDA in terms of ROC curve and AUC based on LOOCV</p></caption><graphic xlink:href="12859_2018_2146_Fig1_HTML" id="MO1"/></fig></p><sec id="Sec4"><title>The stability analysis of DCSMDA</title><p id="Par22">Because the current lncRNA-disease databases remain in their infancy and most existing methods are always evaluated using a specific dataset, the stability of the different datasets is ignored. To enhance the credibility of the prediction results, DCSMDA was further implemented using three different known lncRNA-disease association datasets, including <italic>DS</italic><sub>1</sub>, <italic>DS</italic><sub>2</sub>, and <italic>DS</italic><sub>3,</sub> and the known lncRNA-miRNA association dataset <italic>DS</italic><sub>4</sub>.</p><p id="Par23">The comparison results of the ROC are shown in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, and the corresponding AUCs are 0.8155, 0.8089 and 0.7642 when DCSMDA (<italic>b</italic> was set to 6) was evaluated in the framework of the LOOCV using the three different lncRNA-disease association datasets. DCSMDA achieved a reliable and effective prediction performance.<fig id="Fig2"><label>Fig. 2</label><caption><p>Comparison of different lncRNA-disease datasets to the prediction performance of DCSMDA</p></caption><graphic xlink:href="12859_2018_2146_Fig2_HTML" id="MO2"/></fig></p></sec></sec><sec id="Sec5"><title>Effects of the pre-given threshold parameter <italic>b</italic></title><p id="Par24">In DCSMDA, the pre-determined threshold <italic>b</italic> plays a critical role, and the value of <italic>b</italic> influences the performance of predicting potential miRNA-disease associations. In this section, we implemented a series of comparison experiments to evaluate the effects of <italic>b</italic> on the prediction performance of DCSMDA. The LOOCV was implemented, experiments were performed, and <italic>b</italic> was assigned different values. Considering the time complexity, and that the value of SPM(i, j) always equals 6, when <italic>b</italic> &#x02265;6, we set <italic>b</italic> to a value no greater than 6 in our experiments.</p><p id="Par25">As shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>, DCSMDA showed an increasing trend in its prediction performance as the value of the pre-determined threshold parameter <italic>b</italic> increased and achieved the best prediction performance when <italic>b</italic> was set to 6. When <italic>b</italic> was set to 6, DCSMDA achieved an AUC of 0.8089 using <italic>DS</italic><sub>3</sub> and <italic>DS</italic><sub>4</sub>. In the analysis, we found that the main reason was that the number of known miRNA-lncRNA associations and lncRNA-disease associations was small; thus, when <italic>b</italic> is set to a larger value, more nodes could be linked to each other in the miRNA-lncRNA-disease interactive network, improving the prediction performance of DCSMDA. Therefore, we finally set <italic>b</italic>&#x02009;=&#x02009;6 in our experiments.<fig id="Fig3"><label>Fig. 3</label><caption><p>Comparison of effects of the pre-given threshold parameter <italic>b</italic> to the prediction performance of DCSMDA while <italic>b</italic> was assigned different values</p></caption><graphic xlink:href="12859_2018_2146_Fig3_HTML" id="MO3"/></fig></p></sec></sec><sec id="Sec6"><title>Case study</title><p id="Par26">Currently, cancer is the leading cause of death in humans worldwide [<xref ref-type="bibr" rid="CR34">34</xref>&#x02013;<xref ref-type="bibr" rid="CR36">36</xref>], and the incidence of cancer is high in both developed and developing countries. Therefore, to estimate the effective predictive performance of DCSMDA, case studies of two important cancers and leukaemia were implemented. The prediction results were verified using recently published experimental studies (see Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>).<table-wrap id="Tab1"><label>Table 1</label><caption><p>DCSMDA was applied to case studies of three important cancers. In total, 10, 9 and 8 of the top 10 predicted pairs for these diseases were confirmed based on recent experimental studies</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Disease</th><th>miRNA</th><th>Evidence (PMID and PMCID)</th></tr></thead><tbody><tr><td>Prostatic Neoplasms</td><td>hsa-mir-15a</td><td>PMID: 25418933</td></tr><tr><td>Prostatic Neoplasms</td><td>hsa-mir-15b</td><td>PMID: 24661838</td></tr><tr><td>Prostatic Neoplasms</td><td>hsa-mir-16</td><td>PMID: 21880514</td></tr><tr><td>Prostatic Neoplasms</td><td>hsa-mir-195</td><td>PMID: 26080838</td></tr><tr><td>Prostatic Neoplasms</td><td>hsa-mir-424</td><td>PMID: 27820701</td></tr><tr><td>Prostatic Neoplasms</td><td>hsa-mir-497</td><td>PMID: 23886135</td></tr><tr><td>Prostatic Neoplasms</td><td>hsa-mir-125a</td><td>PMCID: PMC3979818</td></tr><tr><td>Prostatic Neoplasms</td><td>hsa-mir-106b</td><td>PMID: 26124181</td></tr><tr><td>Prostatic Neoplasms</td><td>hsa-mir-17</td><td>PMCID: PMC3008681</td></tr><tr><td>Prostatic Neoplasms</td><td>hsa-mir-93</td><td>PMID: 26124181</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-15a</td><td>PMID: 26314859</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-195</td><td>PMID: 25840419</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-424</td><td>PMID: 27666545</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-497</td><td>PMCID: PMC4537005</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-16</td><td>PMID: 21192009</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-15b</td><td>Unconfirmed</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-125a</td><td>PMID: 24044511</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-106a</td><td>PMID: 18328430</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-106b</td><td>PMID: 18328430</td></tr><tr><td>Lung Neoplasms</td><td>hsa-mir-93</td><td>PMID: 24037530</td></tr><tr><td>Leukaemia</td><td>hsa-mir-424</td><td>PMID: 27013583</td></tr><tr><td>Leukaemia</td><td>hsa-mir-195</td><td>PMCID: PMC4713510</td></tr><tr><td>Leukaemia</td><td>hsa-mir-16</td><td>PMID:22912766</td></tr><tr><td>Leukaemia</td><td>hsa-mir-15a</td><td>PMID: 24392455</td></tr><tr><td>Leukaemia</td><td>hsa-mir-15b</td><td>PMCID: PMC4577143</td></tr><tr><td>Leukaemia</td><td>hsa-mir-497</td><td>Unconfirmed</td></tr><tr><td>Leukaemia</td><td>hsa-mir-125a</td><td>PMID: 22456625</td></tr><tr><td>Leukaemia</td><td>hsa-mir-19b</td><td>PMID: 28765931</td></tr><tr><td>Leukaemia</td><td>hsa-mir-19a</td><td>PMID: 28765931</td></tr><tr><td>Leukaemia</td><td>hsa-mir-17</td><td>PMID: 20439436</td></tr></tbody></table></table-wrap></p><p id="Par27">Prostate cancer (prostatic neoplasms), which is the second leading cause of cancer-related death in males, is among the most common malignant cancers and the most commonly diagnosed cancer in men worldwide. In 2012, prostate cancer occurred in 1.1 million men and caused 307,000 deaths. Accumulating evidence shows that microRNAs are strongly associated with prostate cancer. Therefore, DCSMDA was implemented to predict potential prostate cancer-related miRNAs. Consequently, ten of the top ten predicted prostate cancer-related miRNAs were validated by recent biological experimental studies (see Table <xref rid="Tab1" ref-type="table">1</xref>). For example, Junfeng Jiang et al. reconstructed five prostate cancer co-expressed modules using functional gene sets defined by Gene Ontology (GO) annotation (biological process, GO_BP) and found that hsa-mir15a (ranked 1st) regulated these five candidate modules [<xref ref-type="bibr" rid="CR37">37</xref>]. Medina-Villaamil V et al. analyzed circulating miRNAs in whole blood as non-invasive markers in patients with localized prostate cancer and healthy individuals and found that hsa-mir-15b (ranked 2nd) showed a statistically significant differential expression between the different risk groups and healthy controls [<xref ref-type="bibr" rid="CR38">38</xref>]. Furthermore, Chao Cai et al. confirmed the tumour suppressive role of hsa-mir-195 (ranked 4th) using prostate cancer cell invasion, migration and apoptosis assays in vitro and tumour xenograft growth, angiogenesis and invasion in vivo by performing both gain-of-function and loss-of-function experiments [<xref ref-type="bibr" rid="CR39">39</xref>].</p><p id="Par28">Lung cancer (lung neoplasms) has the poorest prognosis among cancers and is the largest threat to people&#x02019;s health and life. The incidence and mortality of lung cancer are rapidly increasing in China, and approximately 1.4 million deaths are due to lung cancer annually. Recent studies show that miRNAs play critical roles in the progression of lung cancer. Therefore, we used lung cancer as a case study and implemented DCSMDA; nine predicted lung cancer-associated miRNAs of the top ten prediction list were verified based on experimental reports. For example, Bozok &#x000c7;etinta&#x0015f; V et al. analyzed the effects of selected miRNAs on the development of cisplatin resistance and found that hsa-mir-15a (ranked 1st) was among the most significantly downregulated miRNAs conferring resistance to cisplatin in Calu1 epidermoid lung carcinoma cells [<xref ref-type="bibr" rid="CR40">40</xref>]. Hsa-mir-195, which ranked 2nd, was further confirmed to suppress tumour growth and was associated with better survival outcomes in several malignancies, including lung cancer [<xref ref-type="bibr" rid="CR41">41</xref>]. Additionally, according to the biological experiments reported in several studies, hsa-mir-424 (ranked 3rd) plays an important role in lung cancer [<xref ref-type="bibr" rid="CR42">42</xref>].</p><p id="Par29">Leukaemia refers to a group of diseases that usually begin in the bone marrow and result in high numbers of abnormal white blood cells. The exact cause of leukaemia is unknown, and a combination of genetic factors and environmental factors is believed to play a role. In 2015, leukaemia presented in 2.3 million people and caused 353,500 deaths. Several studies suggest that miRNAs are effective prognostic biomarkers in leukaemia. For example, independent experimental observations showed relatively lower expression levels of mir-424 (ranked 1st) in TRAIL-resistant and semi-resistant acute myeloid leukaemia (AML) cell lines and newly diagnosed patient samples. The overexpression of mir-424 by targeting the 3&#x02032; UTR of PLAG1 enhanced TRAIL sensitivity in AML cells [<xref ref-type="bibr" rid="CR43">43</xref>]. Hsa-mir-16 ranked 3rd, its expression was inversely correlated with Bcl2 expression in leukaemia, and both microRNAs negatively regulate B cell lymphoma 2 (Bcl2) at a posttranscriptional level. Bcl2 repression by these microRNAs induces apoptosis in a leukaemic cell line model [<xref ref-type="bibr" rid="CR44">44</xref>]. The lncRNA H19 is considered an independent prognostic marker in patients with tumours. The expression of lncRNA H19 is significantly upregulated in bone marrow samples from patients with AML-M2. The results of the current study suggest that lncRNA H19 regulates the expression of inhibitor of DNA binding 2 (ID2) by competitively binding to hsa-mir-19b (ranked 8) and hsa-mir-19a (ranked 9), which may play a role in AML cell proliferation [<xref ref-type="bibr" rid="CR45">45</xref>].</p><p id="Par30">In addition, DCSMDA predicted all potential associations between the diseases and miRNAs in G<sub>3</sub> simultaneously. In addition, notably, potential associations with a high predicted value can be publicly released and benefit from biological experimental validation. To further illustrate the effective performance of DCSMDA, the predicted results were sorted from best to worse, and the top 10 results were selected for analysis (see Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). Consequently, 100% of the results were confirmed by recent biological experiments and the HMDD dataset, and thus, DCSMDA can be used as an efficient computational tool in biomedical research studies.<table-wrap id="Tab2"><label>Table 2</label><caption><p>The top 10 predicted miRNA-disease associations by DCSMDA</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Disease</th><th>MiRNA</th><th>Evidence</th></tr></thead><tbody><tr><td>Carcinoma, Hepatocellular</td><td>hsa-mir-15a</td><td>HMDD</td></tr><tr><td>Carcinoma, Hepatocellular</td><td>hsa-mir-15b</td><td>HMDD</td></tr><tr><td>Carcinoma, Hepatocellular</td><td>hsa-mir-16</td><td>HMDD</td></tr><tr><td>Carcinoma, Hepatocellular</td><td>hsa-mir-195</td><td>HMDD</td></tr><tr><td>Carcinoma, Hepatocellular</td><td>hsa-mir-424</td><td>PMID: 26823812</td></tr><tr><td>Carcinoma, Hepatocellular</td><td>hsa-mir-497</td><td>HMDD</td></tr><tr><td>Colorectal Neoplasms</td><td>hsa-mir-497</td><td>HMDD</td></tr><tr><td>Colorectal Neoplasms</td><td>hsa-mir-15b</td><td>PMID: 23267864</td></tr><tr><td>Colorectal Neoplasms</td><td>hsa-mir-16</td><td>HMDD</td></tr><tr><td>Colorectal Neoplasms</td><td>hsa-mir-195</td><td>HMDD</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec7"><title>Discussion</title><p id="Par31">Accumulating evidence shows that miRNAs play a very important role in several key biological functions and signalling pathways. A large-scale systematic analysis of miRNA-disease data performed by combining relevant biological data is highly important for humans and attractive topics in the field of computational biology. However, only a few prediction models have been proposed for the large-scale forecasting of associations between miRNAs and diseases based on lncRNA information. To utilize the wealth of disease-lncRNA, miRNA-lncRNA and disease-lncRNA association data recorded in four datasets and recently published experimental studies, in this article, we proposed a novel prediction model called DCSMDA to infer the potential associations between diseases and miRNAs. We first constructed a miRNA-lncRNA-disease interactive network and further integrated a distance correlation set, disease semantic similarity, functional similarity and Gaussian interaction profile kernel similarity for DCSMDA. The important difference between DCSMDA and previous computational models is that DCSMDA does not rely on any known miRNA-disease associations and predicts disease-miRNA associations based only on known disease-lncRNA associations and known lncRNA-miRNA associations. To evaluate the prediction performance of DCSMDA, the validation frameworks of the LOOCV were implemented using the HMDD database. Furthermore, case studies were further implemented using three important diseases and the top 10 predicted miRNA-disease associations based on recently published experimental studies and databases. The simulation results showed that DCSMDA achieved a reliable and effective prediction performance. Hence, DCSMDA could be used as an effective and important biological tool that benefits the early diagnosis and treatment of diseases and improves human health in the future.</p><p id="Par32">However, although DCSMDA is a powerful method for predicting novel relationships between diseases and miRNAs, there are several limitations in our method. First, the value of the threshold parameter <italic>b</italic> plays an important role in DCSMDA, and the selection of a suitable value for <italic>b</italic> is a critical problem that should be addressed in future studies. Second, although DCSMDA does not rely on any known experimentally verified miRNA-disease relationships, the performance of DCSMDA was not very satisfactory compared with that of several existing methods, such as LRSMDA and WBSMDA [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>]. Introducing more reliable measures for the calculations of the disease similarity, miRNA similarity, and lncRNA similarity and developing a more reliable similarity integration method could improve the performance of DCSMDA. Finally, DCSMDA cannot be applied to unknown diseases or miRNAs that are not present in the disease-miRNA or lncRNA-miRNA databases; such genes are poorly investigated and have no known disease-lncRNA and lncRNA-miRNA associations. The performance of DCSMDA will be further improved once more known associations are obtained.</p></sec><sec id="Sec8"><title>Conclusion</title><p id="Par33">In this article, we mainly achieved the following contributions: (1) we constructed a miRNA-lncRNA-disease interactive network based on common assumptions that similar diseases tend to show similar interaction and non-interaction patterns with lncRNAs, and similar miRNAs tend to show similar interaction and non-interaction patterns with lncRNAs; (2) the concept of a distance correlation set was introduced; (3) the sematic disease similarity, functionally similarity (including disease functionally similarity and miRNA functionally similarity) and Gaussian interaction profile kernel similarity (including disease Gaussian interaction profile kernel similarity, miRNA Gaussian interaction profile kernel similarity and lncRNA Gaussian interaction profile kernel similarity) were integrated; (4) the concept of an optimized matrix was introduced by integrating the Gaussian interaction profile kernel similarity of the miRNA pairs and disease pairs; (5) negative samples are not required in DCSMDA; and (6) DCSMDA can be applied to human diseases without relying on any known miRNA-disease associations.</p></sec><sec id="Sec9"><title>Methods</title><sec id="Sec10"><title>Known disease-lncRNA associations</title><p id="Par34">Because the number of lncRNA-disease associations is limited and many heterogeneous biological datasets have been constructed, we collected 8842 known disease-lncRNA associations from the MNDR dataset (<ext-link ext-link-type="uri" xlink:href="http://www.bioinformatics.ac.cn/mndr/index.html">http://www.bioinformatics.ac.cn/mndr/index.html</ext-link>) and 2934 known disease-lncRNA associations from the LncRNADisease dataset (<ext-link ext-link-type="uri" xlink:href="http://www.cuilab.cn/lncrnadisease">http://www.cuilab.cn/lncrnadisease</ext-link>). Since the disease names in the LncRNADisease database differ from those in the MNDR dataset, we mapped the diseases in these two disease-lncRNA association datasets to their MeSH descriptors. After eliminating diseases without any MeSH descriptors, merging the diseases with the same MeSH descriptors and removing the lncRNAs that were not present in the lncRNA-miRNA dataset (<italic>DS</italic><sub>4</sub>) used in this paper, 583 known lncRNA-disease associations (<italic>DS</italic><sub>1</sub>) were obtained from the LncRNADisease dataset (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>), and 702 known lncRNA-disease associations (<italic>DS</italic><sub>2</sub>) were obtained from the MNDR dataset (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>). Furthermore, after integrating the <italic>DS</italic><sub>1</sub> and <italic>DS</italic><sub>2</sub> datasets and removing the duplicate associations, we obtained the <italic>DS</italic><sub>3</sub> dataset, which included 1073 disease-lncRNA associations (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>).</p></sec><sec id="Sec11"><title>Known lncRNA-miRNA associations</title><p id="Par35">To construct the lncRNA-miRNA network, the lncRNA-miRNA association dataset <italic>DS</italic><sub>4</sub> was obtained from the starBasev2.0 database (<ext-link ext-link-type="uri" xlink:href="http://starbase.sysu.edu.cn">http://starbase.sysu.edu.cn</ext-link>/) in February 2, 2017 and provided the most comprehensive experimentally confirmed lncRNA-miRNA interactions based on large-scale CLIP-Seq data. After the data pre-processing (including the elimination of duplicate values, erroneous data, and disorganized data), removing the lncRNAs that did not exist in the <italic>DS</italic><sub>3</sub> dataset and merging the miRNA copies that produced the same mature miRNA, we finally obtained 1883 lncRNA-miRNA associations (<italic>DS</italic><sub>4</sub>) (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>).</p></sec><sec id="Sec12"><title>Known disease-miRNA associations</title><p id="Par36">To validate the performance of DCSMDA, the known human miRNA-disease associations were downloaded from the latest version of the HMDD database, which is considered the golden-standard dataset. In this dataset, after eliminating the duplicate associations and miRNA-disease associations involved with other diseases or lncRNAs not contained in the <italic>DS</italic><sub>3</sub> or <italic>DS</italic><sub><italic>4</italic></sub>, we finally obtained 3252 high-quality lncRNA-disease associations (<italic>DS</italic><sub>5</sub>) (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>).</p></sec><sec id="Sec13"><title>Construction of the disease-lncRNA-miRNA interaction network</title><p id="Par37">To clearly demonstrate the process of constructing the disease-lncRNA-miRNA interaction network, we use the disease-lncRNA dataset <italic>DS</italic><sub>3</sub> and the lncRNA-miRNA dataset <italic>DS</italic><sub>4</sub> as examples. We defined <italic>L</italic> to represent all the different lncRNA terms in <italic>DS</italic><sub>3</sub> and <italic>DS</italic><sub>4</sub> and then constructed the disease-lncRNA-miRNA interactive network based on <italic>DS</italic><sub>3</sub> and <italic>DS</italic><sub>4</sub> according to the following 3 steps:</p><p id="Par38">Step 1 (Construction of the disease-lncRNA network): Let <italic>D</italic> and <italic>L</italic> be the number of different diseases and lncRNAs obtained from <italic>DS</italic><sub>3</sub>, respectively. <italic>S</italic><sub><italic>D</italic></sub>&#x02009;=&#x02009;{<italic>d</italic><sub><italic>1</italic></sub><italic>, d</italic><sub><italic>2</italic></sub><italic>,..., d</italic><sub><italic>D</italic></sub>} represents the set of all <italic>D</italic> different diseases in <italic>DS</italic><sub>3</sub>. <italic>S</italic><sub><italic>L</italic></sub>&#x02009;=&#x02009;{<italic>l</italic><sub><italic>1</italic></sub><italic>, l</italic><sub><italic>2</italic></sub><italic>,..., l</italic><sub><italic>L</italic></sub>} represents the set of all <italic>L</italic> different lncRNAs in <italic>DS</italic><sub>3</sub>, and for any given <italic>d</italic><sub><italic>i</italic></sub> &#x02208; <italic>S</italic><sub><italic>D</italic></sub> and <italic>l</italic><sub><italic>j</italic></sub>&#x02208;<italic>S</italic><sub><italic>L</italic></sub>, we can construct the <italic>D*L</italic> dimensional matrix KAM1 as follows:<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ KAM1\left(i,j\right)=\Big\{{\displaystyle \begin{array}{c}1\kern0.5em if\kern0.2em {d}_i\kern0.2em is\kern0.34em related\kern0.34em to\kern0.2em {l}_j\kern0.2em in\kern0.2em {DS}_3\\ {}0\kern7.8em otherwise\end{array}} $$\end{document}</tex-math><mml:math id="M2" display="block"><mml:mi>K</mml:mi><mml:mi>A</mml:mi><mml:mi>M</mml:mi><mml:mn>1</mml:mn><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mo>{</mml:mo><mml:mtable columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:mn>1</mml:mn><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mspace width="0.2em"/><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:mi>e</mml:mi><mml:mi>d</mml:mi><mml:mspace width="0.2em"/><mml:mi>t</mml:mi><mml:mi>o</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>i</mml:mi><mml:mi>n</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>D</mml:mi><mml:mi>S</mml:mi></mml:mrow><mml:mn>3</mml:mn></mml:msub></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow><mml:mn>0</mml:mn><mml:mspace width="7.8em"/><mml:mi>o</mml:mi><mml:mi>t</mml:mi><mml:mi>h</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mi>w</mml:mi><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mi>e</mml:mi></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par39">Step 2 (Construction of the lncRNA-miRNA network): Let <italic>M</italic> be the number of different miRNAs obtained from <italic>DS</italic><sub>4</sub>. <italic>S</italic><sub><italic>M</italic></sub>&#x02009;=&#x02009;{<italic>m</italic><sub><italic>1</italic></sub><italic>, m</italic><sub><italic>2</italic></sub><italic>,..., m</italic><sub><italic>M</italic></sub>} represents the set of all <italic>M</italic> different miRNAs in <italic>DS</italic><sub>4</sub>, and for any given <italic>m</italic><sub><italic>i</italic></sub>&#x02208;<italic>S</italic><sub><italic>M</italic></sub> and <italic>l</italic><sub><italic>j</italic></sub>&#x02208;<italic>S</italic><sub><italic>L</italic></sub>, we can construct the <italic>M*L</italic> dimensional matrix <italic>KAM2</italic> as follows:<disp-formula id="Equ2"><label>2</label><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ KAM2\left(i,j\right)=\left\{\begin{array}{c}1\kern0.5em if\ {m}_i\  is\ related\ to\ {l}_j\  in\ {DS}_4\\ {}0\kern5.25em otherwise\end{array}\right. $$\end{document}</tex-math><mml:math id="M4" display="block"><mml:mi mathvariant="italic">KAM</mml:mi><mml:mn>2</mml:mn><mml:mfenced close=")" open="(" separators=","><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>1</mml:mn><mml:mspace width="0.5em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.25em"/><mml:msub><mml:mi>m</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:mspace width="0.25em"/><mml:mtext mathvariant="italic">is related to</mml:mtext><mml:mspace width="0.25em"/><mml:msub><mml:mi>l</mml:mi><mml:mi>j</mml:mi></mml:msub><mml:mspace width="0.25em"/><mml:mtext mathvariant="italic">in</mml:mtext><mml:mspace width="0.25em"/><mml:msub><mml:mi mathvariant="italic">DS</mml:mi><mml:mn>4</mml:mn></mml:msub></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>0</mml:mn><mml:mspace width="5.25em"/><mml:mtext mathvariant="italic">otherwise</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ2.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par40">Step 3 (Constriction of the disease-lncRNA-miRNA interactive network): Based on the disease-lncRNA network and lncRNA-miRNA network, we can obtain the undirected graph <italic>G</italic><sub><italic>3</italic></sub>&#x000a0;<italic>=</italic>&#x000a0;(<italic>V</italic><sub><italic>3</italic></sub><italic>, E</italic><sub><italic>3</italic></sub>), where <italic>V</italic><sub><italic>3</italic></sub>&#x02009;=&#x02009;<italic>S</italic>
<sub><italic>D</italic></sub> &#x0222a;<italic>S</italic>
<sub><italic>L</italic></sub> &#x0222a;<italic>S</italic>
<sub><italic>M</italic></sub>&#x02009;=&#x02009;{<italic>d</italic><sub><italic>1</italic></sub><italic>, d</italic><sub><italic>2,</italic></sub><italic>..., d</italic><sub><italic>D</italic></sub><italic>, l</italic><sub><italic>D&#x02009;+&#x02009;1</italic></sub><italic>, l</italic><sub><italic>D&#x02009;+&#x02009;2</italic></sub><italic>..., l</italic><sub><italic>D&#x02009;+&#x02009;L</italic></sub><italic>, m</italic><sub><italic>D&#x02009;+&#x02009;L&#x02009;+&#x02009;1</italic></sub><italic>, m</italic><sub><italic>D&#x02009;+&#x02009;L&#x02009;+&#x02009;2</italic></sub><italic>..., m</italic><sub><italic>D&#x02009;+&#x02009;L&#x02009;+&#x02009;M</italic></sub>} is the set of vertices, <italic>E</italic><sub><italic>3</italic></sub> is the edge set of <italic>G</italic><sub><italic>3</italic></sub>, and <italic>d</italic><sub><italic>i</italic></sub>&#x02208;<italic>S</italic><sub><italic>D</italic></sub>, <italic>l</italic><sub><italic>j</italic></sub>&#x02208;<italic>S</italic><sub><italic>L</italic></sub>, m<sub>k</sub>&#x02208;S<sub>M</sub>. Here, an edge exists between <italic>d</italic><sub><italic>i</italic></sub> and <italic>l</italic><sub><italic>j</italic></sub> in <italic>E</italic><sub><italic>3</italic></sub><italic>KAM1</italic>(<italic>d</italic><sub><italic>i</italic></sub>, <italic>l</italic><sub><italic>j</italic></sub>)&#x02009;=&#x02009;1, an edge exists between <italic>l</italic><sub><italic>j</italic></sub> and <italic>m</italic><sub><italic>k</italic></sub> in <italic>E</italic><sub><italic>3</italic></sub> if <italic>KAM2</italic>(<italic>m</italic><sub><italic>k</italic></sub><italic>, l</italic><sub><italic>j</italic></sub>)&#x02009;=&#x02009;1. Then, for any given <italic>a</italic>, <italic>b</italic>&#x02208;<italic>V</italic><sub><italic>3</italic></sub>, we can define the Strong Correlation (<italic>SC</italic>) between <italic>a</italic> and <italic>b</italic> as follows:<disp-formula id="Equ3"><label>3</label><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ SC\left(a,b\right)=\left\{\begin{array}{c}1\kern0.5em if\kern0.34em there\kern0.34em is\kern0.34em an\kern0.34em edge\kern0.34em between\kern0.2em a\kern0.2em and\kern0.2em b\\ {}0\kern11em otherwise\end{array}\right. $$\end{document}</tex-math><mml:math id="M6" display="block"><mml:mi>S</mml:mi><mml:mi>C</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:mn>1</mml:mn><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.2em"/><mml:mi>t</mml:mi><mml:mi>h</mml:mi><mml:mi>e</mml:mi><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mspace width="0.2em"/><mml:mi>i</mml:mi><mml:mi>s</mml:mi><mml:mspace width="0.2em"/><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mspace width="0.2em"/><mml:mi>e</mml:mi><mml:mi>d</mml:mi><mml:mi>g</mml:mi><mml:mi>e</mml:mi><mml:mspace width="0.2em"/><mml:mi>b</mml:mi><mml:mi>e</mml:mi><mml:mi>t</mml:mi><mml:mi>w</mml:mi><mml:mi>e</mml:mi><mml:mi>e</mml:mi><mml:mi>n</mml:mi><mml:mspace width="0.2em"/><mml:mi>a</mml:mi><mml:mspace width="0.2em"/><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mi>d</mml:mi><mml:mspace width="0.2em"/><mml:mi>b</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mn>0</mml:mn><mml:mspace width="11em"/><mml:mtext mathvariant="italic">otherwise</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ3.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par41">Notably, although we did not use any known disease-miRNA associations, the diseases and miRNAs can still be indirectly linked by integrating the edges between the disease nodes, the lncRNA nodes and edges between the miRNA nodes and lncRNA nodes in <italic>G</italic><sub><italic>3</italic></sub>.</p></sec><sec id="Sec14"><title>Disease semantic similarity</title><p id="Par42">We downloaded the MeSH descriptors of the diseases from the National Library of Medicine (<ext-link ext-link-type="uri" xlink:href="http://www.nlm.nih.gov/">http://www.nlm.nih.gov/</ext-link>), which introduced the concept of Categories and Subcategories and provided a strict system for disease classification. The topology of each disease was visualized as a Directed Acyclic Graph (DAG) in which the nodes represented the disease MeSH descriptors, and all MeSH descriptors in the DAG were linked from more general terms (parent nodes) to more specific terms (child nodes) by a direct edge (see Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>). Let <italic>DAG(A)</italic>&#x02009;=&#x02009;(<italic>A, T</italic>(<italic>A</italic>)<italic>, E</italic>(<italic>A</italic>)), where <italic>A</italic> represents disease <italic>A</italic>, <italic>T</italic>(<italic>A</italic>) represents the node set, including node <italic>A</italic> and its ancestor nodes, and <italic>E</italic>(<italic>A</italic>) represents the corresponding edge set. Then, we defined the contribution of disease term <italic>d</italic> in <italic>DAG</italic>(<italic>A</italic>) to the semantic value of disease <italic>A</italic> as follows:<disp-formula id="Equ4"><label>4</label><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \left\{\begin{array}{c}{D}_A(d)=1\kern16.8em if\kern0.3em d=A\\ {}{D}_A(d)=\max \left\{0.5\ast {D}_A\left({d}^{\ast}\right)|{d}^{\ast}\in children\kern0.3em of\kern0.3em d\right\}\kern0.3em if\kern0.3em d\ne A\end{array}\right. $$\end{document}</tex-math><mml:math id="M8" display="block"><mml:mrow><mml:mfenced close="" open="{"><mml:mtable columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:msub><mml:mrow><mml:mi>D</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>d</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mn>1</mml:mn><mml:mspace width="16.8em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.3em"/><mml:mi>d</mml:mi><mml:mo>=</mml:mo><mml:mi>A</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow><mml:msub><mml:mrow><mml:mi>D</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>d</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mo form="prefix" movablelimits="true">max</mml:mo><mml:mrow><mml:mo>{</mml:mo><mml:mn>0.5</mml:mn><mml:mo>&#x02217;</mml:mo><mml:msub><mml:mrow><mml:mi>D</mml:mi></mml:mrow><mml:mi>A</mml:mi></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:msup><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mo>)</mml:mo></mml:mrow><mml:mrow><mml:mo stretchy="false">|</mml:mo></mml:mrow><mml:msup><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mo>&#x02208;</mml:mo><mml:mspace width="0.25em"/><mml:mi>c</mml:mi><mml:mi>h</mml:mi><mml:mi>i</mml:mi><mml:mi>l</mml:mi><mml:mi>d</mml:mi><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>n</mml:mi><mml:mspace width="0.3em"/><mml:mi>o</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.3em"/><mml:mi>d</mml:mi><mml:mo>}</mml:mo></mml:mrow><mml:mspace width="0.3em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.3em"/><mml:mi>d</mml:mi><mml:mo>&#x02260;</mml:mo><mml:mi>A</mml:mi></mml:mrow></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:mrow></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ4.gif" position="anchor"/></alternatives></disp-formula><fig id="Fig4"><label>Fig. 4</label><caption><p>The disease <italic>DAGs</italic> of Prostatic Neoplasms and Gastrointestinal Neoplasms</p></caption><graphic xlink:href="12859_2018_2146_Fig4_HTML" id="MO4"/></fig></p><p id="Par43">For example, the semantic value of the disease &#x02018;Gastrointestinal Neoplasms&#x02019; shown in Fig. <xref rid="Fig4" ref-type="fig">4</xref> is calculated by summing the weighted contribution of &#x02018;Neoplasms&#x02019; (0.125), &#x02018;Neoplasms by Site&#x02019; (0.25), &#x02018;Digestive System Diseases&#x02019; (0.25), &#x02018;Digestive System Neoplasms&#x02019; (0.5), &#x02018;Digestive System Neoplasms&#x02019; (0.5) and &#x02018;Gastrointestinal Diseases&#x02019; (0.5) to &#x02018;Gastrointestinal Neoplasms&#x02019; and the contribution to &#x02018;Gastrointestinal Neoplasms&#x02019; (1) by &#x02018;Gastrointestinal Neoplasms&#x02019;.</p><p id="Par44">Then, the sematic value of disease <italic>A</italic> can be obtained by summing the contribution from all disease terms in&#x02009;=&#x02009;<italic>DAG</italic>(<italic>A</italic>), and the semantic similarity between the two diseases <italic>d</italic><sub><italic>i</italic></sub> and <italic>d</italic><sub><italic>j</italic></sub> can be calculated as follows:<disp-formula id="Equ5"><label>5</label><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ SSD\left({d}_i,{d}_j\right)=\frac{\sum \limits_{d\in \left(T\left({d}_i\right)\cap T\left({d}_j\right)\right)}\left({D}_{d_i}(d)+{D}_{d_j}(d)\right)}{\sum \limits_{d\in T\left({d}_i\right)}{D}_{d_i}(d)+{\sum}_{d\in T\left({d}_j\right)}{D}_{d_j}(d)} $$\end{document}</tex-math><mml:math id="M10" display="block"><mml:mi>S</mml:mi><mml:mi>S</mml:mi><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mi>T</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02229;</mml:mo><mml:mi>T</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mo stretchy="false">)</mml:mo><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:munder><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>D</mml:mi></mml:mrow><mml:mrow><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>d</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>D</mml:mi></mml:mrow><mml:mrow><mml:msub><mml:mi>d</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>d</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mrow><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi>T</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:munder><mml:msub><mml:mrow><mml:mi>D</mml:mi></mml:mrow><mml:mrow><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>d</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>d</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi>T</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:msub><mml:msub><mml:mrow><mml:mi>D</mml:mi></mml:mrow><mml:mrow><mml:msub><mml:mi>d</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mrow></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>d</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ5.gif" position="anchor"/></alternatives></disp-formula>where <italic>SSD</italic> is the disease semantic similarity matrix.</p></sec><sec id="Sec15"><title>MiRNA Gaussian interaction profile kernel similarity</title><p id="Par45">Based on the assumption that similar miRNAs tend to show similar interaction and non-interaction patterns with lncRNAs, in this section, we introduce the Gaussian interaction profile kernel used to calculate the network topologic similarity between miRNAs and used the vector <italic>MLP</italic>(<italic>m</italic><sub><italic>i</italic></sub>) to denote the ith row of the adjacency matrix <italic>KAM2</italic>. Then, the Gaussian interaction profile kernel similarity for all investigated miRNAs can be calculated as follows:<disp-formula id="Equ6"><label>6</label><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ MGS\left({m}_i,{m}_j\right)=\exp \left(-\frac{M\ast {\left\Vert MLP\left({m}_i\right)- MLP\left({m}_j\right)\right\Vert}^2}{\sum \limits_{i=1}^M{\left\Vert MLP\left({m}_i\right)\right\Vert}^2}\right) $$\end{document}</tex-math><mml:math id="M12" display="block"><mml:mi mathvariant="italic">MGS</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>m</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>m</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>=</mml:mo><mml:mo>exp</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mfrac><mml:mrow><mml:mi>M</mml:mi><mml:mo>&#x02217;</mml:mo><mml:msup><mml:mfenced close="&#x02016;" open="&#x02016;"><mml:mrow><mml:mi mathvariant="italic">MLP</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>m</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="italic">MLP</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>m</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mfenced><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mrow><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>M</mml:mi></mml:munderover><mml:msup><mml:mfenced close="&#x02016;" open="&#x02016;"><mml:mrow><mml:mi mathvariant="italic">MLP</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>m</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mfenced><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ6.gif" position="anchor"/></alternatives></disp-formula>where parameter <italic>M</italic> is the number of miRNAs in <italic>DS</italic><sub>4</sub>.</p></sec><sec id="Sec16"><title>Disease Gaussian interaction profile kernel similarity</title><p id="Par46">Based on the assumption that similar diseases tend to show similar interaction and non-interaction patterns with lncRNAs, the Gaussian interaction profile kernel similarity for all investigated diseases can be calculated as follows:<disp-formula id="Equ7"><label>7</label><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ DGS\left({d}_i,{d}_j\right)=\exp \left(-\frac{D\ast {\left\Vert DLP\left({d}_i\right)- DLP\left({d}_j\right)\right\Vert}^2}{\sum \limits_{i=1}^D{\left\Vert DLP\left({d}_i\right)\right\Vert}^2}\right) $$\end{document}</tex-math><mml:math id="M14" display="block"><mml:mi mathvariant="italic">DGS</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>d</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>=</mml:mo><mml:mo>exp</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mfrac><mml:mrow><mml:mi>D</mml:mi><mml:mo>&#x02217;</mml:mo><mml:msup><mml:mfenced close="&#x02016;" open="&#x02016;"><mml:mrow><mml:mi mathvariant="italic">DLP</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="italic">DLP</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>d</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mfenced><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mrow><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>D</mml:mi></mml:munderover><mml:msup><mml:mfenced close="&#x02016;" open="&#x02016;"><mml:mrow><mml:mi mathvariant="italic">DLP</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mfenced><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ7.gif" position="anchor"/></alternatives></disp-formula>where parameter <italic>D</italic> is the number of diseases in <italic>DS</italic><sub>3,</sub> and <italic>DLP</italic>(<italic>d</italic><sub><italic>i</italic></sub>) represent the ith row of the matrix <italic>KAM1</italic>. Then, based on previous work [<xref ref-type="bibr" rid="CR46">46</xref>], we can improve the predictive accuracy problems by logistic function transformation as follows:<disp-formula id="Equ8"><label>8</label><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ FDGS\left({d}_i,{d}_j\right)=\frac{1}{1+{e}^{-15\ast DGS\left({d}_i,{d}_j\right)+\log (9999)}} $$\end{document}</tex-math><mml:math id="M16" display="block"><mml:mi>F</mml:mi><mml:mi>D</mml:mi><mml:mi>G</mml:mi><mml:mi>S</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mn>1</mml:mn><mml:mo>+</mml:mo><mml:msup><mml:mrow><mml:mi>e</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mn>15</mml:mn><mml:mo>&#x02217;</mml:mo><mml:mi>D</mml:mi><mml:mi>G</mml:mi><mml:mi>S</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>+</mml:mo><mml:mi>log</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mn>9999</mml:mn><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:msup></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ8.gif" position="anchor"/></alternatives></disp-formula></p></sec><sec id="Sec17"><title>lncRNA Gaussian interaction profile kernel similarity</title><p id="Par47">Based on the assumption that similar lncRNAs tend to show similar interaction and non-interaction patterns with miRNAs and similar lncRNAs tend to show similar interaction and non-interaction patterns with diseases, the Gaussian interaction profile kernel similarity matrix for all investigated lncRNAs in <italic>DS</italic><sub>3</sub> can be computed in a similar way as that for disease, as follows:<disp-formula id="Equ9"><label>9</label><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ LGS1\left({l}_i,{l}_j\right)=\exp \left(-\frac{L\ast {\left\Vert LDP\left({l}_i\right)- LDP\left({l}_j\right)\right\Vert}^2}{\sum \limits_{i=1}^L{\left\Vert LDP\left({l}_i\right)\right\Vert}^2}\right) $$\end{document}</tex-math><mml:math id="M18" display="block"><mml:mi mathvariant="italic">LGS</mml:mi><mml:mn>1</mml:mn><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>l</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>l</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>=</mml:mo><mml:mo>exp</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mfrac><mml:mrow><mml:mi>L</mml:mi><mml:mo>&#x02217;</mml:mo><mml:msup><mml:mfenced close="&#x02016;" open="&#x02016;"><mml:mrow><mml:mi mathvariant="italic">LDP</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>l</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced><mml:mo>&#x02212;</mml:mo><mml:mi mathvariant="italic">LDP</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>l</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mfenced><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mrow><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mi>L</mml:mi></mml:munderover><mml:msup><mml:mfenced close="&#x02016;" open="&#x02016;"><mml:mrow><mml:mi mathvariant="italic">LDP</mml:mi><mml:mfenced close=")" open="("><mml:msub><mml:mi>l</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mfenced></mml:mrow></mml:mfenced><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ9.gif" position="anchor"/></alternatives></disp-formula>where parameter <italic>L</italic> is the number of lncRNAs in <italic>DS</italic><sub>3,</sub> and <italic>LDP</italic>(<italic>l</italic><sub><italic>i</italic></sub>) represents the ith column of the matrix <italic>KAM1</italic>.</p><p id="Par48">Obviously, the Gaussian interaction profile kernel similarity for all investigated lncRNAs in <italic>DS</italic><sub>4</sub> can be computed as follows:<disp-formula id="Equ10"><label>10</label><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ LGS2\left({d}_i,{d}_j\right)=\exp \left(-\frac{L\ast \parallel LMP\left({l}_i\right)- LMP\left({l}_j\right){\parallel}^2}{\sum \limits_{i=1}^L\parallel LMP\left({l}_i\right){\parallel}^2}\right) $$\end{document}</tex-math><mml:math id="M20" display="block"><mml:mi>L</mml:mi><mml:mi>G</mml:mi><mml:mi>S</mml:mi><mml:mn>2</mml:mn><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mi>exp</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mfrac><mml:mrow><mml:mi>L</mml:mi><mml:mo>&#x02217;</mml:mo><mml:msup><mml:mrow><mml:mrow><mml:mo>&#x02225;</mml:mo><mml:mi>L</mml:mi><mml:mi>M</mml:mi><mml:mi>P</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mi>L</mml:mi><mml:mi>M</mml:mi><mml:mi>P</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02225;</mml:mo></mml:mrow></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mrow><mml:munderover><mml:mo movablelimits="false">&#x02211;</mml:mo><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>L</mml:mi></mml:mrow></mml:munderover><mml:msup><mml:mrow><mml:mrow><mml:mo>&#x02225;</mml:mo><mml:mi>L</mml:mi><mml:mi>M</mml:mi><mml:mi>P</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02225;</mml:mo></mml:mrow></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfrac><mml:mo>)</mml:mo></mml:mrow></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ10.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par49">where <italic>LMP</italic>(<italic>l</italic><sub><italic>i</italic></sub>) represents the ith column of the matrix <italic>KAM2</italic>.</p></sec><sec id="Sec18"><title>Disease functional similarity based on the lncRNAs</title><p id="Par50">To calculate the functional similarity of the diseases, we first constructed the undirected graph <italic>G</italic><sub><italic>1</italic></sub>&#x02009;=&#x02009;(<italic>V</italic><sub><italic>1</italic></sub><italic>, E</italic><sub><italic>1</italic></sub>) based on <italic>KAM1</italic>, where <italic>V</italic><sub><italic>1</italic></sub>&#x02009;=&#x02009;<italic>S</italic><sub><italic>D</italic></sub>&#x0222a;<italic>S</italic><sub><italic>M</italic></sub>&#x02009;=&#x02009;{<italic>d</italic><sub><italic>1</italic></sub><italic>, d</italic><sub><italic>2</italic></sub><italic>, &#x02026;, d</italic><sub><italic>D</italic></sub><italic>, l</italic><sub><italic>D&#x02009;+&#x02009;1</italic></sub><italic>, l</italic><sub><italic>D&#x02009;+&#x02009;2</italic></sub><italic>,&#x02026;, l</italic><sub><italic>D&#x02009;+&#x02009;M</italic></sub>} is the set of vertices, <italic>E</italic><sub><italic>1</italic></sub> is the set of edges, and for any two nodes <italic>a, b</italic>&#x02208;<italic>V</italic><sub><italic>1</italic></sub>, an edge exists between a and b in <italic>E</italic><sub><italic>1</italic></sub> if <italic>KAM1</italic>(<italic>a, b</italic>)&#x02009;=&#x02009;1. Therefore, we can calculate the similarities between two disease nodes by comparing and integrating the similarities of the lncRNA nodes associated with these two disease nodes based on the assumption that similar diseases tend to show similar interaction and non-interaction patterns with lncRNAs. The procedure used to calculate the disease functional similarity is shown in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>.<fig id="Fig5"><label>Fig. 5</label><caption><p>The Flow chart of the disease functional similarity calculation model</p></caption><graphic xlink:href="12859_2018_2146_Fig5_HTML" id="MO5"/></fig></p><p id="Par51">Because different lncRNA terms in <italic>DS</italic><sub>3</sub> may relate to several diseases, assigning the same contribution value to all miRNAs is not suitable, and therefore, we defined the contribution value of each lncRNA as follows:<disp-formula id="Equ11"><label>11</label><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ C\left({l}_i\right)=\frac{\mathrm{The}\kern0.34em \mathrm{number}\kern0.34em \mathrm{of}\kern0.2em {l}_i-\mathrm{related}\kern0.34em \mathrm{edges}\ \mathrm{in}\ {E}_1}{\mathrm{The}\ \mathrm{number}\ \mathrm{of}\ \mathrm{all}\ \mathrm{edges}\ \mathrm{in}\ {E}_1} $$\end{document}</tex-math><mml:math id="M22" display="block"><mml:mrow><mml:mi>C</mml:mi></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mrow><mml:mi mathvariant="normal">T</mml:mi><mml:mi mathvariant="normal">h</mml:mi><mml:mi mathvariant="normal">e</mml:mi></mml:mrow><mml:mspace width="0.2em"/><mml:mrow><mml:mi mathvariant="normal">n</mml:mi><mml:mi mathvariant="normal">u</mml:mi><mml:mi mathvariant="normal">m</mml:mi><mml:mi mathvariant="normal">b</mml:mi><mml:mi mathvariant="normal">e</mml:mi><mml:mi mathvariant="normal">r</mml:mi></mml:mrow><mml:mspace width="0.2em"/><mml:mrow><mml:mi mathvariant="normal">o</mml:mi><mml:mi mathvariant="normal">f</mml:mi></mml:mrow><mml:mspace width="0.2em"/><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mstyle displaystyle="false" scriptlevel="0"><mml:mtext>-</mml:mtext></mml:mstyle><mml:mrow><mml:mi mathvariant="normal">r</mml:mi><mml:mi mathvariant="normal">e</mml:mi><mml:mi mathvariant="normal">l</mml:mi><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">t</mml:mi><mml:mi mathvariant="normal">e</mml:mi><mml:mi mathvariant="normal">d</mml:mi></mml:mrow><mml:mspace width="0.2em"/><mml:mrow><mml:mi mathvariant="normal">e</mml:mi><mml:mi mathvariant="normal">d</mml:mi><mml:mi mathvariant="normal">g</mml:mi><mml:mi mathvariant="normal">e</mml:mi><mml:mi mathvariant="normal">s</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:mrow><mml:mi mathvariant="normal">i</mml:mi><mml:mi mathvariant="normal">n</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mrow><mml:mi mathvariant="normal">T</mml:mi><mml:mi mathvariant="normal">h</mml:mi><mml:mi mathvariant="normal">e</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:mrow><mml:mi mathvariant="normal">n</mml:mi><mml:mi mathvariant="normal">u</mml:mi><mml:mi mathvariant="normal">m</mml:mi><mml:mi mathvariant="normal">b</mml:mi><mml:mi mathvariant="normal">e</mml:mi><mml:mi mathvariant="normal">r</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:mrow><mml:mi mathvariant="normal">o</mml:mi><mml:mi mathvariant="normal">f</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:mrow><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">l</mml:mi><mml:mi mathvariant="normal">l</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:mrow><mml:mi mathvariant="normal">e</mml:mi><mml:mi mathvariant="normal">d</mml:mi><mml:mi mathvariant="normal">g</mml:mi><mml:mi mathvariant="normal">e</mml:mi><mml:mi mathvariant="normal">s</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:mrow><mml:mi mathvariant="normal">i</mml:mi><mml:mi mathvariant="normal">n</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ11.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par52">Based on the definition of <italic>C</italic>(<italic>l</italic><sub><italic>i</italic></sub>), we can define the contribution value of each lncRNA to the functional similarity of each disease pair as follows:<disp-formula id="Equ12"><label>12</label><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {CD}_{ij}\left({l}_k\right)=\Big\{{\displaystyle \begin{array}{c}1\kern2.30em if\kern0.3em lncRNA\kern0.3em {l}_k\kern0.2em related\kern0.34em to\kern0.2em {d}_i\kern0.2em and\kern0.2em {d}_j\kern0.2em simultaneously\\ {}C\left({l}_k\right)\kern6em if\kern0.34em lncRNA\kern0.3em {l}_k\kern0.2em only\kern0.34em related\kern0.34em to\kern0.2em {d}_i\kern0.2em or\kern0.2em {d}_j\end{array}}\operatorname{} $$\end{document}</tex-math><mml:math id="M24" display="block"><mml:msub><mml:mrow><mml:mi>C</mml:mi><mml:mi>D</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>k</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mo>{</mml:mo><mml:mtable columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:mn>1</mml:mn><mml:mspace width="2em"/><mml:mspace width="0.3em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.3em"/><mml:mi>l</mml:mi><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>R</mml:mi><mml:mi>N</mml:mi><mml:mi>A</mml:mi><mml:mspace width="0.3em"/><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>k</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:mi>e</mml:mi><mml:mi>d</mml:mi><mml:mspace width="0.2em"/><mml:mi>t</mml:mi><mml:mi>o</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mi>d</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>s</mml:mi><mml:mi>i</mml:mi><mml:mi>m</mml:mi><mml:mi>u</mml:mi><mml:mi>l</mml:mi><mml:mi>t</mml:mi><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mi>e</mml:mi><mml:mi>o</mml:mi><mml:mi>u</mml:mi><mml:mi>s</mml:mi><mml:mi>l</mml:mi><mml:mi>y</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow/><mml:mi>C</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>k</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mspace width="6em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.2em"/><mml:mi>l</mml:mi><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>R</mml:mi><mml:mi>N</mml:mi><mml:mi>A</mml:mi><mml:mspace width="0.3em"/><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>k</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>o</mml:mi><mml:mi>n</mml:mi><mml:mi>l</mml:mi><mml:mi>y</mml:mi><mml:mspace width="0.2em"/><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:mi>e</mml:mi><mml:mi>d</mml:mi><mml:mspace width="0.2em"/><mml:mi>t</mml:mi><mml:mi>o</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>o</mml:mi><mml:mi>r</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub></mml:mtd></mml:mtr></mml:mtable><mml:mo fence="true" stretchy="true"/></mml:mrow></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ12.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par53">Finally, we can define the functional similarity between diseases <italic>d</italic><sub><italic>i</italic></sub> and <italic>d</italic><sub>j</sub> by integrating lncRNAs related to <italic>d</italic><sub><italic>i</italic></sub><italic>, d</italic><sub><italic>j</italic></sub> or both as follows:<disp-formula id="Equ13"><label>13</label><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ FSD\left({d}_i,{d}_j\right)=\frac{\sum \limits_{l_k\in \left(D\left({d}_i\right)\cup D\left({d}_j\right)\right)}C{D}_{ij}\left({l}_k\right)}{\mid D\left({d}_i\right)\mid +\mid D\left({d}_j\right)\mid -\mid D\left({d}_i\right)\cap D\left({d}_j\right)\mid } $$\end{document}</tex-math><mml:math id="M26" display="block"><mml:mrow><mml:mi>F</mml:mi><mml:mi>S</mml:mi><mml:mi>D</mml:mi></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x0222a;</mml:mo><mml:mrow><mml:mi>D</mml:mi></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo stretchy="false">)</mml:mo><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:munder><mml:mi>C</mml:mi><mml:msub><mml:mi>D</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mrow><mml:mo stretchy="false">&#x02223;</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02223;</mml:mo><mml:mo>+</mml:mo><mml:mo>&#x02223;</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02223;</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mo>&#x02223;</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02229;</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>d</mml:mi><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo stretchy="false">&#x02223;</mml:mo></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ13.gif" position="anchor"/></alternatives></disp-formula>where <italic>D</italic>(<italic>d</italic><sub><italic>i</italic></sub>) and <italic>D</italic>(<italic>d</italic><sub><italic>j</italic></sub>) represent all lncRNAs related to <italic>di</italic> and <italic>d</italic><sub><italic>j</italic></sub> in <italic>E</italic><sub><italic>1</italic></sub>, respectively.</p></sec><sec id="Sec19"><title>MiRNA functional similarity based on lncRNAs</title><p id="Par54">Based on the assumption that similar miRNAs tend to show similar interaction and non-interaction patterns with lncRNAs, we can also calculate the miRNA functional similarity in the lncRNA-miRNA interactive network. Similar to the procedure used to calculate the disease functional similarity, first, we constructed the undirected graph <italic>G</italic><sub><italic>2</italic></sub>&#x02009;=&#x02009;(<italic>V</italic><sub><italic>2</italic></sub><italic>, E</italic><sub><italic>2</italic></sub>), where <italic>V</italic><sub><italic>2</italic></sub>&#x02009;=&#x02009;<italic>S</italic><sub><italic>M</italic></sub>&#x0222a;<italic>S</italic><sub><italic>L</italic></sub>&#x02009;=&#x02009;{<italic>m</italic><sub><italic>1</italic></sub><italic>, m</italic><sub><italic>2</italic></sub><italic>,&#x02026;, l</italic><sub><italic>M&#x02009;+&#x02009;1</italic></sub><italic>, l</italic><sub><italic>M&#x02009;+&#x02009;2</italic></sub><italic>,&#x02026;, l</italic><sub><italic>M&#x02009;+&#x02009;L</italic></sub>} is the set of vertices, <italic>E</italic><sub><italic>2</italic></sub> is the set of edges, and for any two nodes <italic>a, b</italic> &#x02208; <italic>V</italic><sub><italic>2</italic></sub>, an edge exists between <italic>a</italic> and <italic>b</italic> in <italic>E</italic><sub><italic>2</italic></sub> if <italic>KAM2</italic>(<italic>a</italic>, <italic>b</italic>)&#x02009;=&#x02009;1. Then, we defined the contribution of each lncRNA to the functional similarity of each miRNA pair as follows:<disp-formula id="Equ14"><label>14</label><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {CM}_{ij}\left({l}_k\right)=\Big\{{\displaystyle \begin{array}{c}1\kern1.20em if\kern0.34em lncRNA\kern0.3em {l}_k\kern0.2em related\kern0.2em {m}_i\kern0.2em and\kern0.2em {m}_j\kern0.2em simultaneously\\ {}C\left({l}_k\right)\kern5em if\kern0.34em lncRNA\kern0.3em {l}_k\kern0.2em only\kern0.34em related\kern0.2em {m}_i\kern0.2em or\kern0.2em {m}_j\end{array}}\operatorname{} $$\end{document}</tex-math><mml:math id="M28" display="block"><mml:msub><mml:mrow><mml:mi>C</mml:mi><mml:mi>M</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>k</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mo>{</mml:mo><mml:mtable columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:mn>1</mml:mn><mml:mspace width="1em"/><mml:mspace width="0.2em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.2em"/><mml:mi>l</mml:mi><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>R</mml:mi><mml:mi>N</mml:mi><mml:mi>A</mml:mi><mml:mspace width="0.3em"/><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>k</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:mi>e</mml:mi><mml:mi>d</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mi>d</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>s</mml:mi><mml:mi>i</mml:mi><mml:mi>m</mml:mi><mml:mi>u</mml:mi><mml:mi>l</mml:mi><mml:mi>t</mml:mi><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mi>e</mml:mi><mml:mi>o</mml:mi><mml:mi>u</mml:mi><mml:mi>s</mml:mi><mml:mi>l</mml:mi><mml:mi>y</mml:mi></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow/><mml:mi>C</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>k</mml:mi></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mspace width="5em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.2em"/><mml:mi>l</mml:mi><mml:mi>n</mml:mi><mml:mi>c</mml:mi><mml:mi>R</mml:mi><mml:mi>N</mml:mi><mml:mi>A</mml:mi><mml:mspace width="0.3em"/><mml:msub><mml:mrow><mml:mi>l</mml:mi></mml:mrow><mml:mi>k</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>o</mml:mi><mml:mi>n</mml:mi><mml:mi>l</mml:mi><mml:mi>y</mml:mi><mml:mspace width="0.2em"/><mml:mi>r</mml:mi><mml:mi>e</mml:mi><mml:mi>l</mml:mi><mml:mi>a</mml:mi><mml:mi>t</mml:mi><mml:mi>e</mml:mi><mml:mi>d</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mi>i</mml:mi></mml:msub><mml:mspace width="0.2em"/><mml:mi>o</mml:mi><mml:mi>r</mml:mi><mml:mspace width="0.2em"/><mml:msub><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mi>j</mml:mi></mml:msub></mml:mtd></mml:mtr></mml:mtable><mml:mo fence="true" stretchy="true"/></mml:mrow></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ14.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par55">Additionally, we can define the functional similarity between <italic>m</italic><sub><italic>i</italic></sub> and <italic>m</italic><sub><italic>j</italic></sub> as follows:<disp-formula id="Equ15"><label>15</label><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ FSM\left({m}_i,{m}_j\right)=\frac{\sum \limits_{l_k\in \left(D\left({m}_i\right)\cup D\left({m}_j\right)\right)}C{M}_{ij}\left({m}_k\right)}{\mid D\left({m}_i\right)\mid +\mid \mathrm{D}\left({m}_j\right)\mid -\mid D\left({m}_i\right)\cap D\left({m}_j\right)\mid } $$\end{document}</tex-math><mml:math id="M30" display="block"><mml:mi>F</mml:mi><mml:mi>S</mml:mi><mml:mi>M</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:munder><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:msub><mml:mi>l</mml:mi><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02208;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x0222a;</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo stretchy="false">)</mml:mo><mml:mo>)</mml:mo></mml:mrow></mml:mrow></mml:munder><mml:mi>C</mml:mi><mml:msub><mml:mi>M</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mrow><mml:mo stretchy="false">&#x02223;</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02223;</mml:mo><mml:mo>+</mml:mo><mml:mo>&#x02223;</mml:mo><mml:mrow><mml:mi mathvariant="normal">D</mml:mi></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02223;</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mo>&#x02223;</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02229;</mml:mo><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mi>m</mml:mi><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mo stretchy="false">&#x02223;</mml:mo></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ15.gif" position="anchor"/></alternatives></disp-formula>where <italic>D</italic>(<italic>m</italic><sub><italic>i</italic></sub>) represents all lncRNAs related to <italic>m</italic><sub><italic>i</italic></sub>, and <italic>D</italic>(<italic>m</italic><sub><italic>j</italic></sub>) represents lncRNAs relate to <italic>m</italic><sub><italic>j</italic></sub> in <italic>E</italic><sub><italic>2</italic></sub>.</p></sec><sec id="Sec20"><title>Integrated similarity</title><p id="Par56">The processes used to calculate the integrated similarities of the diseases, lncRNAs and miRNAs are illustrated in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>. Combining the disease semantic similarity, the disease Gaussian interaction profile kernel similarity and the disease functional similarity mentioned above, we can construct the disease integrated similarity matrix <italic>FDD</italic> as follows:<disp-formula id="Equ16"><label>16</label><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ FDD=\frac{SSD+ FDGS+ FSD}{3} $$\end{document}</tex-math><mml:math id="M32" display="block"><mml:mi mathvariant="italic">FDD</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi mathvariant="italic">SSD</mml:mi><mml:mo>+</mml:mo><mml:mtext mathvariant="italic">FDGS</mml:mtext><mml:mo>+</mml:mo><mml:mi mathvariant="italic">FSD</mml:mi></mml:mrow><mml:mn>3</mml:mn></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ16.gif" position="anchor"/></alternatives></disp-formula><fig id="Fig6"><label>Fig. 6</label><caption><p>Flow chart of calculation of diseases integrated similarity, lncRNA integrated similarity and miRNA integrated similarity</p></caption><graphic xlink:href="12859_2018_2146_Fig6_HTML" id="MO6"/></fig></p><p id="Par57">Additionally, based on the miRNA Gaussian interaction profile kernel similarity and the miRNA functional similarity, we can construct the miRNA integrated similarity matrix <italic>FMM</italic> as follows:<disp-formula id="Equ17"><label>17</label><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ FMM=\frac{MGS+ FSM}{2} $$\end{document}</tex-math><mml:math id="M34" display="block"><mml:mi mathvariant="italic">FMM</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi mathvariant="italic">MGS</mml:mi><mml:mo>+</mml:mo><mml:mi mathvariant="italic">FSM</mml:mi></mml:mrow><mml:mn>2</mml:mn></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ17.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par58">Furthermore, based on the Gaussian interaction profile kernel similarity matrices <italic>LGS1</italic> and <italic>LGS2</italic>, we can construct the lncRNA integrated similarity matrix <italic>FLL</italic> as follows:<disp-formula id="Equ18"><label>18</label><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ FLL=\frac{LGS1+ LGS2}{2} $$\end{document}</tex-math><mml:math id="M36" display="block"><mml:mi mathvariant="italic">FLL</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi mathvariant="italic">LGS</mml:mi><mml:mn>1</mml:mn><mml:mo>+</mml:mo><mml:mi mathvariant="italic">LGS</mml:mi><mml:mn>2</mml:mn></mml:mrow><mml:mn>2</mml:mn></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ18.gif" position="anchor"/></alternatives></disp-formula></p></sec><sec id="Sec21"><title>Prediction of disease-miRNA associations based on a distance correlation set</title><p id="Par59">In this section, we developed a novel computational method, i.e., DCSMDA, to predict potential disease-miRNA associations by introducing a distance correlation set based on the following assumptions: similar diseases tend to show similar interaction and non-interaction patterns with lncRNAs, and similar lncRNAs tend to show similar interaction and non-interaction patterns with miRNAs. As illustrated in Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref>, the DCSMDA procedure consists of the following 5 major steps:<fig id="Fig7"><label>Fig. 7</label><caption><p>The procedures of DCSMDA</p></caption><graphic xlink:href="12859_2018_2146_Fig7_HTML" id="MO7"/></fig></p><p id="Par60">Step 1 (Construction of the adjacency matrix based on <italic>G</italic><sub><italic>3</italic></sub>): First, we construct a (<italic>D&#x02009;+&#x02009;L&#x02009;+&#x02009;M</italic>) * (<italic>D&#x02009;+&#x02009;L&#x02009;+&#x02009;M</italic>) Adjacency Matrix (<italic>AM</italic>) based on the undirected graph <italic>G</italic><sub><italic>3</italic></sub> and <italic>SC</italic>, and then for any two nodes <italic>v</italic><sub><italic>i</italic></sub><italic>, v</italic><sub><italic>j</italic></sub>&#x02208;<italic>V</italic><sub><italic>3</italic></sub><italic>,</italic> we can define the <italic>AM</italic>(<italic>i, j</italic>) as follows:<disp-formula id="Equ19"><label>19</label><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ AM\left(i,j\right)=\left\{\begin{array}{c} SC\left({d}_i,{d}_j\right),\kern0.75em if\kern0.5em i\in \left[1,D\right]\ \mathrm{and}\ j\in \left[1,D\right].\kern6.25em \\ {} SC\left({d}_i,{l}_j\right),\kern0.75em if\kern0.5em i\in \left[1,D\right]\ \mathrm{and}\kern0.5em j\in \left[D,D+L\right].\kern4.75em \\ {} SC\left({d}_i,{m}_j\right),\kern1.25em if\kern0.5em i\in \left[1,D\right]\ \mathrm{and}\ j\in \left[D+L,D+L+M\right].\kern3em \\ {} SC\left({m}_i,{d}_j\right),\kern1em if\kern0.5em i\in \left[D,D+L\right]\ \mathrm{and}\ j\in \left[1,D\right].\kern4.75em \\ {} SC\left({m}_i,{m}_j\right),\kern1.25em if\kern0.5em i\in \left[D,D+L\right]\ \mathrm{and}\ j\in \left[\mathrm{D},D+L\right].\kern3.25em \\ {} SC\left({m}_i,{l}_j\right),\kern1.25em if\kern0.5em i\in \left[D,D+L\right]\ \mathrm{and}\ j\in \left[D+L,D+L+M\right].\kern1.75em \\ {} SC\left({l}_i,{d}_j\right),\kern1.25em if\kern0.5em i\in \left[D+L,D+L+M\right]\ \mathrm{and}\ j\in \left[1,D\right].\kern3em \\ {} SC\left({l}_i,{m}_j\right),\kern1.25em if\kern0.5em i\in \left[D+L,D+L+M\right]\ \mathrm{and}\ j\in \left[\mathrm{D},D+L\right].\kern1.75em \\ {} SC\left({l}_i,{m}_j\right),\kern1.25em if\kern0.5em i\in \left[D+L,D+L+M\right]\ \mathrm{and}\ j\in \left[D+L,D+L+M\right]\end{array}\right. $$\end{document}</tex-math><mml:math id="M38" display="block"><mml:mi mathvariant="italic">AM</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SC</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>d</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="0.75em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mn>1</mml:mn><mml:mi>D</mml:mi></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mn>1</mml:mn><mml:mi>D</mml:mi></mml:mfenced><mml:mo>.</mml:mo><mml:mspace width="6.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SC</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>l</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="0.75em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mn>1</mml:mn><mml:mi>D</mml:mi></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.5em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mi>D</mml:mi><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow></mml:mfenced><mml:mo>.</mml:mo><mml:mspace width="4.75em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SC</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>d</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>m</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="1.25em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mn>1</mml:mn><mml:mi>D</mml:mi></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:mfenced><mml:mo>.</mml:mo><mml:mspace width="3em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SC</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>m</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>d</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="1em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mi>D</mml:mi><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mn>1</mml:mn><mml:mi>D</mml:mi></mml:mfenced><mml:mo>.</mml:mo><mml:mspace width="4.75em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SC</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>m</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>m</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="1.25em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mi>D</mml:mi><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mi mathvariant="normal">D</mml:mi><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow></mml:mfenced><mml:mo>.</mml:mo><mml:mspace width="3.25em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SC</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>m</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>l</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="1.25em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mi>D</mml:mi><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:mfenced><mml:mo>.</mml:mo><mml:mspace width="1.75em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SC</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>l</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>d</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="1.25em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mn>1</mml:mn><mml:mi>D</mml:mi></mml:mfenced><mml:mo>.</mml:mo><mml:mspace width="3em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SC</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>l</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>m</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="1.25em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mi mathvariant="normal">D</mml:mi><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow></mml:mfenced><mml:mo>.</mml:mo><mml:mspace width="1.75em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SC</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:msub><mml:mi>l</mml:mi><mml:mi>i</mml:mi></mml:msub><mml:msub><mml:mi>m</mml:mi><mml:mi>j</mml:mi></mml:msub></mml:mfenced><mml:mo>,</mml:mo><mml:mspace width="1.25em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext>and</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mfenced close="]" open="[" separators=","><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi></mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:mfenced></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ19.gif" position="anchor"/></alternatives></disp-formula>where <italic>i</italic>&#x02208;[1<italic>, D</italic>&#x02009;+&#x02009;<italic>L&#x02009;+&#x02009;M</italic>] and <italic>j</italic>&#x02208;[1<italic>, D&#x02009;+&#x02009;L</italic>&#x02009;+&#x02009;<italic>M</italic>], and to calculate the shortest distance matrix in step 2, we define <italic>AM</italic> (<italic>i, j</italic>)&#x02009;=&#x02009;1 if <italic>i&#x02009;=&#x02009;j</italic>.</p><p id="Par61">Step 2 (Construction of the shortest distance matrix based on adjacency matrix <italic>AM</italic>): First, we set parameter <italic>b</italic> to control the bandwidth of the distance correlation set and let <italic>b</italic> be a pre-determined positive integer, and then, we can obtain <italic>b</italic> matrices, such as <italic>AM</italic><sup><italic>1</italic></sup><italic>, AM</italic><sup><italic>2</italic></sup><italic>,..., AM</italic><sup><italic>b</italic></sup>, based on the above formula (<xref rid="Equ19" ref-type="">19</xref>), and the Shortest Path Matrix is calculated as follows:<disp-formula id="Equ20"><label>20</label><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ SPM\left(i,j\right)=\left\{\ \begin{array}{c}1,\kern2.5em if\  AM\left(i,j\right)=1\\ {}k,\kern2.25em otherwise\kern1.25em \end{array}\right. $$\end{document}</tex-math><mml:math id="M40" display="block"><mml:mi mathvariant="italic">SPM</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mrow><mml:mspace width="0.25em"/><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mspace width="2.5em"/><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.25em"/><mml:mi mathvariant="italic">AM</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi>k</mml:mi><mml:mo>,</mml:mo><mml:mspace width="2.25em"/><mml:mtext mathvariant="italic">otherwise</mml:mtext><mml:mspace width="1.25em"/></mml:mtd></mml:mtr></mml:mtable></mml:mrow></mml:mfenced></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ20.gif" position="anchor"/></alternatives></disp-formula>where <italic>i</italic>&#x02208;[1<italic>, D</italic>&#x02009;+&#x02009;M&#x02009;+&#x02009;<italic>L</italic>], <italic>j</italic>&#x02208;[1<italic>, D</italic>&#x02009;+&#x02009;M&#x02009;+&#x02009;<italic>L</italic>], <italic>k</italic>&#x02208;[2<italic>, b</italic>], and <italic>k</italic> satisfies the following: <italic>AM</italic>
<sup><italic>k</italic></sup>(<italic>i</italic>, <italic>j</italic>)&#x02260;0, while <italic>AM</italic>
<sup>1</sup>(<italic>i</italic>, <italic>j</italic>)&#x02009;=&#x02009;<italic>AM</italic>
<sup>2</sup>(<italic>i</italic>, <italic>j</italic>)&#x02009;=&#x02009;&#x02026;&#x02009;=&#x02009;<italic>AM</italic>
<sup><italic>k-</italic>1</sup>(<italic>i</italic>, <italic>j</italic>)&#x02009;=&#x02009;0.</p><p id="Par62">Step 3 (Calculation of distance correlation sets and distance coefficient of each node pair in <italic>G</italic><sub><italic>3</italic></sub>):</p><p id="Par63">For each node <italic>v</italic><sub><italic>i</italic></sub> &#x02208; <italic>V</italic><sub><italic>3</italic></sub>, we can obtain distance correlation set <italic>DCS</italic>(<italic>i</italic>) according to the shortest distance matrix as follows:<disp-formula id="Equ21"><label>21</label><alternatives><tex-math id="M41">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ DCS(i)=\left\{{v}_j|r\ge SPM\left(i,j\right)&#x0003e;0\right\} $$\end{document}</tex-math><mml:math id="M42" display="block"><mml:mi>D</mml:mi><mml:mi>C</mml:mi><mml:mi>S</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>i</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mrow><mml:mo>{</mml:mo><mml:msub><mml:mi>v</mml:mi><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mrow><mml:mo stretchy="false">|</mml:mo></mml:mrow><mml:mi>r</mml:mi><mml:mo>&#x02265;</mml:mo><mml:mi>S</mml:mi><mml:mi>P</mml:mi><mml:mi>M</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x0003e;</mml:mo><mml:mn>0</mml:mn><mml:mo>}</mml:mo></mml:mrow></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ21.gif" position="anchor"/></alternatives></disp-formula>where <italic>DCS</italic>(<italic>i</italic>) of each node contains itself and all nodes with the shortest distance less than <italic>b</italic>.</p><p id="Par64">For instance, in the disease-miRNA-lncRNA interaction network illustrated in Fig. <xref rid="Fig7" ref-type="fig">7</xref>, <italic>DCS</italic> (seed node) is all candidate nodes when <italic>b</italic> is set to 2.</p><p id="Par65">Then, we can calculate the distance coefficient (<italic>DC</italic>) of the node pair (v<sub>i</sub>, v<sub>j</sub>) as follows:<disp-formula id="Equ22"><label>22</label><alternatives><tex-math id="M43">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ P\left(i,j\right)=\left\{\begin{array}{c} SPM{\left(i,j\right)}^{b+1}, if\ i\in DCS(j)\  or\ j\in DCS(i)\\ {}0,\kern3.5em otherwise\end{array}\right. $$\end{document}</tex-math><mml:math id="M44" display="block"><mml:mi>P</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mfenced><mml:mo>=</mml:mo><mml:mfenced close="" open="{"><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:mi mathvariant="italic">SPM</mml:mi><mml:msup><mml:mfenced close=")" open="(" separators=","><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mfenced><mml:mrow><mml:mi>b</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msup><mml:mo>,</mml:mo><mml:mtext mathvariant="italic">if</mml:mtext><mml:mspace width="0.25em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi mathvariant="italic">DCS</mml:mi><mml:mfenced close=")" open="("><mml:mi>j</mml:mi></mml:mfenced><mml:mspace width="0.25em"/><mml:mtext mathvariant="italic">or</mml:mtext><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi mathvariant="italic">DCS</mml:mi><mml:mfenced close=")" open="("><mml:mi>i</mml:mi></mml:mfenced></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mspace width="3.5em"/><mml:mtext mathvariant="italic">otherwise</mml:mtext></mml:mtd></mml:mtr></mml:mtable></mml:mfenced><mml:mspace width="0.25em"/></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ22.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par66">Furthermore, we can construct a Distance Correlation Matrix (<italic>DCM</italic>) based on the disease integrated similarity, the lncRNA integrated similarity, and the miRNA integrated similarity as follows:<disp-formula id="Equ23"><label>23</label><alternatives><tex-math id="M45">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ DCM\left(i,j\right)=\Big\{{\displaystyle \begin{array}{c}P\left(i,j\right)\ast \exp \left( FDD\left(i,j\right)\right),\kern7.9em if\kern0.5em i\in \left[1,D\right]\ \mathrm{and}\ j\in \left[1,D\right].\kern6.3em \\ {}P\left(i,j\right)\ast \exp \left( FLL\left(i,j\right)\right),\kern6em if\kern0.5em i\in \left[D,D+L\right]\ \mathrm{and}\ j\in \left[\mathrm{D},D+L\right].\kern4.75em \\ {}P\left(i,j\right)\ast \exp \left( FMM\left(i,j\right)\right),\kern0.5em if\kern0.5em i\in \left[D+L,D+L+M\right]\ \mathrm{and}\ j\in \left[D+L,D+L+M\right]\kern3em \\ {}P\left(\mathrm{i},\mathrm{j}\right)\ast \frac{SPM\left(i,j\right)}{b},\kern18.5em \mathrm{otherwise}\kern5.5em \end{array}}\operatorname{} $$\end{document}</tex-math><mml:math id="M46" display="block"><mml:mi>D</mml:mi><mml:mi>C</mml:mi><mml:mi>M</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mrow><mml:mo>{</mml:mo><mml:mtable columnspacing="1em" rowspacing="4pt"><mml:mtr><mml:mtd><mml:mi>P</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02217;</mml:mo><mml:mi>exp</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>F</mml:mi><mml:mi>D</mml:mi><mml:mi>D</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mo>,</mml:mo><mml:mspace width="7.9em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mi>D</mml:mi><mml:mo>]</mml:mo></mml:mrow><mml:mspace width="0.25em"/><mml:mrow><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">n</mml:mi><mml:mi mathvariant="normal">d</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mi>D</mml:mi><mml:mo>]</mml:mo></mml:mrow><mml:mo>.</mml:mo><mml:mspace width="6.3em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow/><mml:mi>P</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02217;</mml:mo><mml:mi>exp</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>F</mml:mi><mml:mi>L</mml:mi><mml:mi>L</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mo>,</mml:mo><mml:mspace width="6em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mi>D</mml:mi><mml:mo>,</mml:mo><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>]</mml:mo></mml:mrow><mml:mspace width="0.25em"/><mml:mrow><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">n</mml:mi><mml:mi mathvariant="normal">d</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:mi mathvariant="normal">D</mml:mi></mml:mrow><mml:mo>,</mml:mo><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>]</mml:mo></mml:mrow><mml:mo>.</mml:mo><mml:mspace width="4.75em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow/><mml:mi>P</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02217;</mml:mo><mml:mi>exp</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>F</mml:mi><mml:mi>M</mml:mi><mml:mi>M</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mo>,</mml:mo><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mi>f</mml:mi><mml:mspace width="0.5em"/><mml:mi>i</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>,</mml:mo><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi><mml:mo>]</mml:mo></mml:mrow><mml:mspace width="0.25em"/><mml:mrow><mml:mi mathvariant="normal">a</mml:mi><mml:mi mathvariant="normal">n</mml:mi><mml:mi mathvariant="normal">d</mml:mi></mml:mrow><mml:mspace width="0.25em"/><mml:mi>j</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mrow><mml:mo>[</mml:mo><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>,</mml:mo><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi><mml:mo>]</mml:mo></mml:mrow><mml:mspace width="3em"/></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:mrow/><mml:mi>P</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mi mathvariant="normal">i</mml:mi></mml:mrow><mml:mo>,</mml:mo><mml:mrow><mml:mi mathvariant="normal">j</mml:mi></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mo>&#x02217;</mml:mo><mml:mfrac><mml:mrow><mml:mi>S</mml:mi><mml:mi>P</mml:mi><mml:mi>M</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mi>b</mml:mi></mml:mfrac><mml:mo>,</mml:mo><mml:mspace width="18.5em"/><mml:mrow><mml:mi mathvariant="normal">o</mml:mi><mml:mi mathvariant="normal">t</mml:mi><mml:mi mathvariant="normal">h</mml:mi><mml:mi mathvariant="normal">e</mml:mi><mml:mi mathvariant="normal">r</mml:mi><mml:mi mathvariant="normal">w</mml:mi><mml:mi mathvariant="normal">i</mml:mi><mml:mi mathvariant="normal">s</mml:mi><mml:mi mathvariant="normal">e</mml:mi></mml:mrow><mml:mspace width="5.5em"/></mml:mtd></mml:mtr></mml:mtable><mml:mo fence="true" stretchy="true"/></mml:mrow></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ23.gif" position="anchor"/></alternatives></disp-formula> where <italic>i</italic>&#x02208;[1, <italic>D&#x02009;+&#x02009;L&#x02009;+&#x02009;M</italic>] and <italic>j</italic>&#x02208;[1, <italic>D&#x02009;+&#x02009;L&#x02009;+&#x02009;M</italic>].</p><p id="Par67">Step 4 (Estimation of the association degree between a pair of nodes): Based on formula (<xref rid="Equ23" ref-type="">23</xref>), we can estimate the association degree between v<sub>i</sub> and v<sub>j</sub> as follows:<disp-formula id="Equ24"><label>24</label><alternatives><tex-math id="M47">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ PM\left(i,j\right)=\frac{\sum \limits_{k=1}^{D+L+M} DCM\left(i,k\right)+{\sum}_{k=1}^{D+L+M} DCM\left(k,j\right)}{D+L+M} $$\end{document}</tex-math><mml:math id="M48" display="block"><mml:mi>P</mml:mi><mml:mi>M</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:munderover><mml:mo>&#x02211;</mml:mo><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:munderover><mml:mi>D</mml:mi><mml:mi>C</mml:mi><mml:mi>M</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>k</mml:mi><mml:mo>)</mml:mo></mml:mrow><mml:mo>+</mml:mo><mml:msubsup><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:msubsup><mml:mi>D</mml:mi><mml:mi>C</mml:mi><mml:mi>M</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mi>k</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mrow><mml:mi>D</mml:mi><mml:mo>+</mml:mo><mml:mi>L</mml:mi><mml:mo>+</mml:mo><mml:mi>M</mml:mi></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ24.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par68">Thus, we can obtain prediction matrix <italic>PM</italic>, where the entity <italic>PM (i, j)</italic> in row <italic>i</italic> column <italic>j</italic> represents the predicted association between node <italic>v</italic><sub><italic>i</italic></sub> and <italic>v</italic><sub><italic>j</italic></sub>.</p><p id="Par69">Step 5 (Calculation of the final prediction result matrix between the miRNAs and diseases): Let <inline-formula id="IEq1"><alternatives><tex-math id="M49">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ PM=\left[\begin{array}{c}{C}_{11}\kern0.75em {C}_{12}\kern1em {C}_{13}\\ {}{C}_{21}\kern0.75em {C}_{22}\kern1em {C}_{23}\\ {}{C}_{31}\kern0.75em {C}_{32}\kern0.75em {C}_{33}\end{array}\right] $$\end{document}</tex-math><mml:math id="M50" display="inline"><mml:mi mathvariant="italic">PM</mml:mi><mml:mo>=</mml:mo><mml:mfenced close="]" open="["><mml:mtable><mml:mtr><mml:mtd><mml:maligngroup/><mml:msub><mml:mi>C</mml:mi><mml:mn>11</mml:mn></mml:msub><mml:mspace width="0.75em"/><mml:msub><mml:mi>C</mml:mi><mml:mn>12</mml:mn></mml:msub><mml:mspace width="1em"/><mml:msub><mml:mi>C</mml:mi><mml:mn>13</mml:mn></mml:msub></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:msub><mml:mi>C</mml:mi><mml:mn>21</mml:mn></mml:msub><mml:mspace width="0.75em"/><mml:msub><mml:mi>C</mml:mi><mml:mn>22</mml:mn></mml:msub><mml:mspace width="1em"/><mml:msub><mml:mi>C</mml:mi><mml:mn>23</mml:mn></mml:msub></mml:mtd></mml:mtr><mml:mtr><mml:mtd><mml:maligngroup/><mml:msub><mml:mi>C</mml:mi><mml:mn>31</mml:mn></mml:msub><mml:mspace width="0.25em"/><mml:mspace width="0.5em"/><mml:msub><mml:mi>C</mml:mi><mml:mn>32</mml:mn></mml:msub><mml:mspace width="0.75em"/><mml:msub><mml:mi>C</mml:mi><mml:mn>33</mml:mn></mml:msub></mml:mtd></mml:mtr></mml:mtable></mml:mfenced></mml:math><inline-graphic xlink:href="12859_2018_2146_Article_IEq1.gif"/></alternatives></inline-formula>, where <italic>C</italic><sub>11</sub> is a <italic>D</italic>&#x000d7;<italic>D</italic> matrix, <italic>C</italic><sub>12</sub> is a <italic>D</italic>&#x000d7;<italic>L</italic> matrix, <italic>C</italic><sub>13</sub> is a <italic>D</italic>&#x000d7;<italic>M</italic> matrix, <italic>C</italic><sub>21</sub> is an <italic>L</italic>&#x000d7;<italic>D</italic> matrix, <italic>C</italic><sub><italic>22</italic></sub> is an <italic>L</italic> &#x000d7;<italic>L</italic> matrix, <italic>C</italic><sub><italic>23</italic></sub> is an L&#x000d7;M matrix, C<sub>31</sub> is an M&#x000d7;D matrix, <italic>C</italic><sub><italic>32</italic></sub> is an <italic>M</italic>&#x000d7;<italic>L</italic> matrix and <italic>C</italic><sub><italic>33</italic></sub> is an <italic>M</italic> &#x000d7;<italic>M</italic> matrix. Obviously, <italic>C</italic><sub><italic>13</italic></sub> is our predicted result, which provides the association probability between each disease and miRNA. A previous study [<xref ref-type="bibr" rid="CR27">27</xref>] demonstrated that the Gaussian interaction profile kernel similarity is a high-efficiency tool for optimizing the result of prediction, and therefore, we used the miRNA Gaussian interaction profile kernel similarity and the disease Gaussian interaction profile kernel similarity to optimize the result of the DCSMDA as follows:<disp-formula id="Equ25"><label>25</label><alternatives><tex-math id="M51">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ FAD= FDD\ast {C}_{13}\ast FMM $$\end{document}</tex-math><mml:math id="M52" display="block"><mml:mi>F</mml:mi><mml:mi>A</mml:mi><mml:mi>D</mml:mi><mml:mo>=</mml:mo><mml:mi>F</mml:mi><mml:mi>D</mml:mi><mml:mi>D</mml:mi><mml:mo>&#x02217;</mml:mo><mml:msub><mml:mrow><mml:mi>C</mml:mi></mml:mrow><mml:mrow><mml:mn>13</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02217;</mml:mo><mml:mi>F</mml:mi><mml:mi>M</mml:mi><mml:mi>M</mml:mi></mml:math><graphic xlink:href="12859_2018_2146_Article_Equ25.gif" position="anchor"/></alternatives></disp-formula>where the matrix FAD denotes the relationship between the miRNA-disease pairs.</p></sec></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec22"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12859_2018_2146_MOESM1_ESM.xls"><label>Additional file 1:</label><caption><p>The known lncRNA-disease associations for constructing the <italic>DS</italic><sub>1</sub>. We list 583 known lncRNA-disease associations which were collected from LncRNAdisease dataset to construct the <italic>DS</italic><sub>1</sub>. (XLS 58&#x000a0;kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12859_2018_2146_MOESM2_ESM.xls"><label>Additional file 2:</label><caption><p>The known lncRNA-disease associations for constructing the <italic>DS</italic><sub>2</sub>. We list 702 known lncRNA-disease associations which were collected from MNDR dataset to construct the <italic>DS</italic><sub>2</sub>. (XLS 63&#x000a0;kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12859_2018_2146_MOESM3_ESM.xls"><label>Additional file 3:</label><caption><p>The integrated lncRNA-disease associations for constructing the <italic>DS</italic><sub>3</sub>. We list 1073 lncRNA-disease associations which were collected by integrating the datasets of <italic>DS</italic><sub>1</sub> and <italic>DS</italic><sub>2</sub>. (XLS 83&#x000a0;kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12859_2018_2146_MOESM4_ESM.xls"><label>Additional file 4:</label><caption><p>The known lncRNA-miRNA associations for constructing the <italic>DS</italic><sub>4</sub>. We list 1883 known lncRNA-miRNA associations which were collected from starBasev2.0 database to construct the <italic>DS</italic><sub>4</sub>. (XLS 123&#x000a0;kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12859_2018_2146_MOESM5_ESM.xls"><label>Additional file 5:</label><caption><p>The known miRNA-disease associations for constructing the <italic>DS</italic><sub>5</sub>. We list 3252 high-quality miRNA-disease associations which were collected from HMDD database to validate the performance of our method. (XLS 191&#x000a0;kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>AUC</term><def><p id="Par4">Areas under ROC curve</p></def></def-item><def-item><term>DCSMDA</term><def><p id="Par5">Distance Correlation Set is developed to predict MiRNA-Disease Associations</p></def></def-item><def-item><term>FPR</term><def><p id="Par6">False positive rates</p></def></def-item><def-item><term>GO</term><def><p id="Par7">Gene Ontology</p></def></def-item><def-item><term>miRNA</term><def><p id="Par8">MicroRNA</p></def></def-item><def-item><term>ncRNA</term><def><p id="Par9">Noncoding RNA</p></def></def-item><def-item><term>ROC</term><def><p id="Par10">Receiver-operating characteristics</p></def></def-item><def-item><term>TPR</term><def><p id="Par11">True positive rates</p></def></def-item><def-item><term>LOOCV</term><def><p id="Par12">Leave-One Out Cross Validation</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Electronic supplementary material</bold></p><p>The online version of this article (10.1186/s12859-018-2146-x) contains supplementary material, which is available to authorized users.</p></fn></fn-group><ack><title>Acknowledgements</title><p>The authors thank the anonymous referees for suggestions that helped improve the paper substantially.</p><sec id="FPar1"><title>Funding</title><p id="Par70">The project is partly sponsored by the Construct Program of the Key Discipline in Hunan province, the National Natural Science Foundation of China (No.61640210, No.61672447), the CERNET Next Generation Internet Technology Innovation Project (No. NGII20160305), the Science &#x00026; Education Joint Project of Hunan Natural Science Foundation (No.2017JJ5036), and the Upgrading Project of Industry-University- Research of Xiangtan University (No.11KZ|KZ03051).</p></sec><sec id="FPar2"><title>Availability of data and materials</title><p id="Par71">All data generated or analyzed during this study are included in this published article [Additional file <xref rid="MOESM1" ref-type="media">1</xref>, Additional file <xref rid="MOESM2" ref-type="media">2</xref>, Additional file <xref rid="MOESM3" ref-type="media">3</xref>, Additional file <xref rid="MOESM4" ref-type="media">4</xref> and Additional file <xref rid="MOESM5" ref-type="media">5</xref>].</p></sec><sec id="FPar3"><title>Author&#x02019;s contributions</title><p id="Par72">HCZ conceived the study. HCZ, LAK and LW developed the method. PYP and ZWX implemented the algorithms. HCZ and TRP analyzed the data. LW supervised the study. HCZ and LW wrote the manuscript. ZLW, PYP and LW reviewed and improved the manuscript, ZLW provided supplementary data. All authors read and approved the final manuscript.</p></sec></ack><notes notes-type="COI-statement"><sec id="FPar4"><title>Ethics approval and consent to participate</title><p id="Par73">Not applicable.</p></sec><sec id="FPar5"><title>Competing interests</title><p id="Par74">The authors declare that they have no competing interests.</p></sec><sec id="FPar6"><title>Publisher&#x02019;s Note</title><p id="Par75">Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Z</given-names></name><name><surname>Gerstein</surname><given-names>M</given-names></name><name><surname>Snyder</surname><given-names>M</given-names></name></person-group><article-title>RNA-Seq: a revolutionary tool for transcriptomics</article-title><source>Nat Rev Genet</source><year>2009</year><volume>10</volume><issue>1</issue><fpage>57</fpage><lpage>63</lpage><pub-id pub-id-type="doi">10.1038/nrg2484</pub-id><?supplied-pmid 19015660?><pub-id pub-id-type="pmid">19015660</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Crick</surname><given-names>FHC</given-names></name><name><surname>Barnett</surname><given-names>L</given-names></name><name><surname>Brenner</surname><given-names>S</given-names></name><name><surname>Watts-Tobin</surname><given-names>RJ</given-names></name></person-group><article-title>General nature of the genetic code for proteins</article-title><source>Nature</source><year>1961</year><volume>192</volume><issue>4809</issue><fpage>1227</fpage><lpage>1232</lpage><pub-id pub-id-type="doi">10.1038/1921227a0</pub-id><?supplied-pmid 13882203?><pub-id pub-id-type="pmid">13882203</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mattick</surname><given-names>JS</given-names></name><name><surname>Makunin</surname><given-names>IV</given-names></name></person-group><article-title>Non-coding RNA</article-title><source>Hum Mol Genet</source><year>2006</year><volume>15</volume><issue>suppl_1</issue><fpage>R17</fpage><pub-id pub-id-type="doi">10.1093/hmg/ddl046</pub-id><?supplied-pmid 16651366?><pub-id pub-id-type="pmid">16651366</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Esteller</surname><given-names>M</given-names></name></person-group><article-title>Non-coding RNAs in human disease</article-title><source>Nat Rev Genet</source><year>2011</year><volume>12</volume><issue>12</issue><fpage>861</fpage><lpage>874</lpage><pub-id pub-id-type="doi">10.1038/nrg3074</pub-id><?supplied-pmid 22094949?><pub-id pub-id-type="pmid">22094949</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mattick</surname><given-names>JS</given-names></name><name><surname>Rinn</surname><given-names>JL</given-names></name></person-group><article-title>Discovery and annotation of long noncoding RNAs</article-title><source>Nat Struct Mol Biol</source><year>2015</year><volume>22</volume><issue>1</issue><fpage>5</fpage><pub-id pub-id-type="doi">10.1038/nsmb.2942</pub-id><?supplied-pmid 25565026?><pub-id pub-id-type="pmid">25565026</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ambros</surname><given-names>V</given-names></name></person-group><article-title>The functions of animal micrornas</article-title><source>Nature</source><year>2004</year><volume>431</volume><issue>7006</issue><fpage>350</fpage><pub-id pub-id-type="doi">10.1038/nature02871</pub-id><?supplied-pmid 15372042?><pub-id pub-id-type="pmid">15372042</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cheng</surname><given-names>AM</given-names></name><name><surname>Byrom</surname><given-names>MW</given-names></name><name><surname>Shelton</surname><given-names>J</given-names></name><name><surname>Ford</surname><given-names>LP</given-names></name></person-group><article-title>Antisense inhibition of human mirnas and indications for an involvement of mirna in cell growth and apoptosis</article-title><source>Nucleic Acids Res</source><year>2005</year><volume>33</volume><issue>4</issue><fpage>1290</fpage><lpage>1297</lpage><pub-id pub-id-type="doi">10.1093/nar/gki200</pub-id><?supplied-pmid 15741182?><pub-id pub-id-type="pmid">15741182</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Taguchi</surname><given-names>Y</given-names></name></person-group><article-title>Inference of target gene regulation via mirnas during cell senescence by using the mirage server</article-title><source>Aging Dis</source><year>2012</year><volume>3</volume><issue>4</issue><fpage>301</fpage><?supplied-pmid 23185711?><pub-id pub-id-type="pmid">23185711</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Peng</surname><given-names>H</given-names></name><name><surname>Lan</surname><given-names>C</given-names></name><name><surname>Zheng</surname><given-names>Y</given-names></name><name><surname>Hutvagner</surname><given-names>G</given-names></name><name><surname>Tao</surname><given-names>D</given-names></name><name><surname>Li</surname><given-names>J</given-names></name></person-group><article-title>Cross disease analysis of co-functional microrna pairs on a reconstructed network of disease-gene-microrna tripartite</article-title><source>Bmc Bioinformatics</source><year>2017</year><volume>18</volume><issue>1</issue><fpage>193</fpage><pub-id pub-id-type="doi">10.1186/s12859-017-1605-0</pub-id><?supplied-pmid 28340554?><pub-id pub-id-type="pmid">28340554</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Weber</surname><given-names>MJ</given-names></name></person-group><article-title>New human and mouse microrna genes found by homology search</article-title><source>FEBS J</source><year>2005</year><volume>272</volume><issue>1</issue><fpage>59</fpage><pub-id pub-id-type="doi">10.1111/j.1432-1033.2004.04389.x</pub-id><?supplied-pmid 15634332?><pub-id pub-id-type="pmid">15634332</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thum</surname><given-names>T</given-names></name><name><surname>Gross</surname><given-names>C</given-names></name><name><surname>Fiedler</surname><given-names>J</given-names></name><name><surname>Fischer</surname><given-names>T</given-names></name><name><surname>Kissler</surname><given-names>S</given-names></name><name><surname>Bussen</surname><given-names>M</given-names></name><etal/></person-group><article-title>Microrna-21 contributes to myocardial disease by stimulating map kinase signalling in fibroblasts</article-title><source>Nature</source><year>2008</year><volume>456</volume><issue>7224</issue><fpage>980</fpage><lpage>984</lpage><pub-id pub-id-type="doi">10.1038/nature07511</pub-id><?supplied-pmid 19043405?><pub-id pub-id-type="pmid">19043405</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cogswell</surname><given-names>JP</given-names></name><name><surname>Ward</surname><given-names>J</given-names></name><name><surname>Taylor</surname><given-names>IA</given-names></name><name><surname>Waters</surname><given-names>M</given-names></name><name><surname>Shi</surname><given-names>Y</given-names></name><name><surname>Cannon</surname><given-names>B</given-names></name><etal/></person-group><article-title>Identification of mirna changes in alzheimer's disease brain and csf yields putative biomarkers and insights into disease pathways</article-title><source>J Alzheimers Dis</source><year>2008</year><volume>14</volume><issue>1</issue><fpage>27</fpage><lpage>41</lpage><pub-id pub-id-type="doi">10.3233/JAD-2008-14103</pub-id><?supplied-pmid 18525125?><pub-id pub-id-type="pmid">18525125</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Corsten</surname><given-names>MF</given-names></name><name><surname>Dennert</surname><given-names>R</given-names></name><name><surname>Jochems</surname><given-names>S</given-names></name><name><surname>Kuznetsova</surname><given-names>T</given-names></name><name><surname>Devaux</surname><given-names>Y</given-names></name><name><surname>Hofstra</surname><given-names>L</given-names></name><etal/></person-group><article-title>Circulating microrna-208b and microrna-499 reflect myocardial damage in cardiovascular disease</article-title><source>Circ Cardiovasc Genet</source><year>2010</year><volume>3</volume><issue>6</issue><fpage>499</fpage><pub-id pub-id-type="doi">10.1161/CIRCGENETICS.110.957415</pub-id><?supplied-pmid 20921333?><pub-id pub-id-type="pmid">20921333</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ikeda</surname><given-names>S</given-names></name><name><surname>Kong</surname><given-names>SW</given-names></name><name><surname>Lu</surname><given-names>J</given-names></name><name><surname>Bisping</surname><given-names>E</given-names></name><name><surname>Zhang</surname><given-names>H</given-names></name><name><surname>Allen</surname><given-names>PD</given-names></name><etal/></person-group><article-title>Altered microrna expression in human heart disease</article-title><source>Physiol Genomics</source><year>2007</year><volume>31</volume><issue>3</issue><fpage>367</fpage><lpage>373</lpage><pub-id pub-id-type="doi">10.1152/physiolgenomics.00144.2007</pub-id><?supplied-pmid 17712037?><pub-id pub-id-type="pmid">17712037</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lu</surname><given-names>M</given-names></name><name><surname>Zhang</surname><given-names>Q</given-names></name><name><surname>Deng</surname><given-names>M</given-names></name><name><surname>Miao</surname><given-names>J</given-names></name><name><surname>Guo</surname><given-names>Y</given-names></name><name><surname>Gao</surname><given-names>W</given-names></name><etal/></person-group><article-title>An analysis of human microrna and disease associations</article-title><source>PLoS One</source><year>2008</year><volume>3</volume><issue>10</issue><fpage>e3420</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0003420</pub-id><?supplied-pmid 18923704?><pub-id pub-id-type="pmid">18923704</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Liu</surname><given-names>MX</given-names></name><name><surname>Yan</surname><given-names>GY</given-names></name></person-group><article-title>Rwrmda: predicting novel human microrna-disease associations</article-title><source>Mol BioSyst</source><year>2012</year><volume>8</volume><issue>10</issue><fpage>2792</fpage><pub-id pub-id-type="doi">10.1039/c2mb25180a</pub-id><?supplied-pmid 22875290?><pub-id pub-id-type="pmid">22875290</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>Y</given-names></name><name><surname>Qiu</surname><given-names>C</given-names></name><name><surname>Tu</surname><given-names>J</given-names></name><name><surname>Geng</surname><given-names>B</given-names></name><name><surname>Yang</surname><given-names>J</given-names></name><name><surname>Jiang</surname><given-names>T</given-names></name><etal/></person-group><article-title>HMDD v2.0: a database for experimentally supported human microrna and disease associations</article-title><source>Nucleic Acids Res</source><year>2014</year><volume>42</volume><issue>Database issue</issue><fpage>1070</fpage><lpage>1074</lpage><pub-id pub-id-type="doi">10.1093/nar/gkt1023</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>D</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name><name><surname>Lu</surname><given-names>M</given-names></name><name><surname>Song</surname><given-names>F</given-names></name><name><surname>Cui</surname><given-names>Q</given-names></name></person-group><article-title>Inferring the human microrna functional similarity and functional network based on microrna-associated diseases</article-title><source>Bioinformatics</source><year>2010</year><volume>26</volume><issue>13</issue><fpage>1644</fpage><lpage>1650</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btq241</pub-id><?supplied-pmid 20439255?><pub-id pub-id-type="pmid">20439255</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jiang</surname><given-names>Q</given-names></name><name><surname>Wang</surname><given-names>Y</given-names></name><name><surname>Hao</surname><given-names>Y</given-names></name><name><surname>Juan</surname><given-names>L</given-names></name><name><surname>Teng</surname><given-names>M</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name><etal/></person-group><article-title>Mir2disease: a manually curated database for microrna deregulation in human disease</article-title><source>Nucleic Acids Res</source><year>2009</year><volume>37</volume><issue>1</issue><fpage>D98</fpage><lpage>104</lpage><pub-id pub-id-type="doi">10.1093/nar/gkn714</pub-id><?supplied-pmid 18927107?><pub-id pub-id-type="pmid">18927107</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zou</surname><given-names>Q</given-names></name><name><surname>Li</surname><given-names>J</given-names></name><name><surname>Hong</surname><given-names>Q</given-names></name><name><surname>Lin</surname><given-names>Z</given-names></name><name><surname>Wu</surname><given-names>Y</given-names></name><name><surname>Shi</surname><given-names>H</given-names></name><etal/></person-group><article-title>Prediction of microrna-disease associations based on social network analysis methods</article-title><source>Biomed Res Int</source><year>2015</year><volume>2015</volume><issue>10</issue><fpage>810514</fpage><?supplied-pmid 26273645?><pub-id pub-id-type="pmid">26273645</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><mixed-citation publication-type="other">You ZH, Wang LP, Chen X, et al. PRMDA: personalized recommendation-based MiRNA-disease association prediction[J]. Oncotarget. 2017;8(49):85568-83.</mixed-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shi</surname><given-names>H</given-names></name><name><surname>Xu</surname><given-names>J</given-names></name><name><surname>Zhang</surname><given-names>G</given-names></name><name><surname>Xu</surname><given-names>L</given-names></name><name><surname>Li</surname><given-names>C</given-names></name><name><surname>Wang</surname><given-names>L</given-names></name><etal/></person-group><article-title>Walking the interactome to identify human mirna-disease associations through the functional link between mirna targets and disease genes</article-title><source>BMC Syst Biol</source><year>2013</year><volume>7</volume><issue>1</issue><fpage>1</fpage><lpage>12</lpage><pub-id pub-id-type="doi">10.1186/1752-0509-7-101</pub-id><pub-id pub-id-type="pmid">23280066</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jiang</surname><given-names>Q</given-names></name><name><surname>Hao</surname><given-names>Y</given-names></name><name><surname>Wang</surname><given-names>G</given-names></name><name><surname>Juan</surname><given-names>L</given-names></name><name><surname>Zhang</surname><given-names>T</given-names></name><name><surname>Teng</surname><given-names>M</given-names></name><etal/></person-group><article-title>Prioritization of disease micrornas through a human phenome-micrornaome network</article-title><source>BMC Syst Biol</source><year>2010</year><volume>4</volume><issue>S1</issue><fpage>S2</fpage><pub-id pub-id-type="doi">10.1186/1752-0509-4-S1-S2</pub-id><?supplied-pmid 20522252?><pub-id pub-id-type="pmid">20522252</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Han</surname><given-names>K</given-names></name><name><surname>Xuan</surname><given-names>P</given-names></name><name><surname>Ding</surname><given-names>J</given-names></name><name><surname>Zhao</surname><given-names>ZJ</given-names></name><name><surname>Hui</surname><given-names>L</given-names></name><name><surname>Zhong</surname><given-names>YL</given-names></name></person-group><article-title>Prediction of disease-related micrornas by incorporating functional similarity and common association information</article-title><source>Gen Mol Res Gmr</source><year>2014</year><volume>13</volume><issue>1</issue><fpage>2009</fpage><lpage>2019</lpage><pub-id pub-id-type="doi">10.4238/2014.March.24.5</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xuan</surname><given-names>P</given-names></name><name><surname>Han</surname><given-names>K</given-names></name><name><surname>Guo</surname><given-names>M</given-names></name><name><surname>Guo</surname><given-names>Y</given-names></name><name><surname>Li</surname><given-names>J</given-names></name><name><surname>Ding</surname><given-names>J</given-names></name><etal/></person-group><article-title>Prediction of micrornas associated with human diseases based on weighted k most similar neighbors</article-title><source>PLoS One</source><year>2013</year><volume>8</volume><issue>9</issue><fpage>e70204</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0070204</pub-id><?supplied-pmid 23950912?><pub-id pub-id-type="pmid">23950912</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xuan</surname><given-names>P</given-names></name><name><surname>Han</surname><given-names>K</given-names></name><name><surname>Guo</surname><given-names>Y</given-names></name><name><surname>Li</surname><given-names>J</given-names></name><name><surname>Li</surname><given-names>X</given-names></name><name><surname>Zhong</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Prediction of potential disease-associated micrornas based on random walk</article-title><source>Bioinformatics</source><year>2015</year><volume>31</volume><issue>11</issue><fpage>1805</fpage><lpage>1815</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btv039</pub-id><?supplied-pmid 25618864?><pub-id pub-id-type="pmid">25618864</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Yan</surname><given-names>GY</given-names></name></person-group><article-title>Semi-supervised learning for potential human microrna-disease associations inference</article-title><source>Sci Rep</source><year>2014</year><volume>4</volume><fpage>5501</fpage><pub-id pub-id-type="doi">10.1038/srep05501</pub-id><?supplied-pmid 24975600?><pub-id pub-id-type="pmid">24975600</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name><name><surname>Yan</surname><given-names>CC</given-names></name><name><surname>Zhang</surname><given-names>X</given-names></name><name><surname>You</surname><given-names>ZH</given-names></name><name><surname>Deng</surname><given-names>L</given-names></name><name><surname>Liu</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Wbsmda: within and between score for mirna-disease association prediction</article-title><source>Sci Rep</source><year>2016</year><volume>6</volume><fpage>21106</fpage><pub-id pub-id-type="doi">10.1038/srep21106</pub-id><?supplied-pmid 26880032?><pub-id pub-id-type="pmid">26880032</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Y</given-names></name><name><surname>Chen</surname><given-names>L</given-names></name><name><surname>Chen</surname><given-names>B</given-names></name><name><surname>Li</surname><given-names>X</given-names></name><name><surname>Kang</surname><given-names>J</given-names></name><name><surname>Fan</surname><given-names>K</given-names></name><etal/></person-group><article-title>Mammalian ncrna-disease repository: a global view of ncrna-mediated disease network</article-title><source>Cell Death Dis</source><year>2013</year><volume>4</volume><issue>8</issue><fpage>e765</fpage><pub-id pub-id-type="doi">10.1038/cddis.2013.292</pub-id><?supplied-pmid 23928704?><pub-id pub-id-type="pmid">23928704</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>G</given-names></name><name><surname>Wang</surname><given-names>Z</given-names></name><name><surname>Wang</surname><given-names>D</given-names></name><name><surname>Qiu</surname><given-names>C</given-names></name><name><surname>Liu</surname><given-names>M</given-names></name><name><surname>Chen</surname><given-names>X</given-names></name><etal/></person-group><article-title>lncrna-disease: a database for long-non-coding rna-associated diseases</article-title><source>Nucleic Acids Res</source><year>2013</year><volume>41</volume><issue>Database issue</issue><fpage>983</fpage><lpage>986</lpage></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>X</given-names></name></person-group><article-title>Predicting lncrna-disease associations and constructing lncrna functional similarity network based on the information of mirna</article-title><source>Sci Rep</source><year>2015</year><volume>5</volume><fpage>13186</fpage><pub-id pub-id-type="doi">10.1038/srep13186</pub-id><?supplied-pmid 26278472?><pub-id pub-id-type="pmid">26278472</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>WT</given-names></name><name><surname>Guo</surname><given-names>XQ</given-names></name><name><surname>Dai</surname><given-names>JP</given-names></name><name><surname>Chen</surname><given-names>RS</given-names></name></person-group><article-title>Microrna and lncrna in neurodegenerative diseases*: microrna and lncrna in neurodegenerative diseases</article-title><source>Prog Biochem Biophys</source><year>2010</year><volume>37</volume><issue>8</issue><fpage>826</fpage><lpage>833</lpage><pub-id pub-id-type="doi">10.3724/SP.J.1206.2010.00104</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><mixed-citation publication-type="other">Guo L, Peng Y, Meng Y, et al. Expression profiles analysis reveals an integrated miRNA-lncRNA signature to predict survival in ovarian cancer patients with wild-type BRCA1/2. Oncotarget. 2017;8(40):68483.</mixed-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Spiess</surname><given-names>PE</given-names></name><name><surname>Dhillon</surname><given-names>J</given-names></name><name><surname>Baumgarten</surname><given-names>AS</given-names></name><name><surname>Johnstone</surname><given-names>PA</given-names></name><name><surname>Giuliano</surname><given-names>AR</given-names></name></person-group><article-title>Pathophysiological basis of human papillomavirus in penile cancer: key to prevention and delivery of more effective therapies</article-title><source>CA-A Cancer J Clinicians</source><year>2016</year><volume>6</volume><fpage>481</fpage><lpage>495</lpage><pub-id pub-id-type="doi">10.3322/caac.21354</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ruprecht</surname><given-names>B</given-names></name><name><surname>Zaal</surname><given-names>EA</given-names></name><name><surname>Zecha</surname><given-names>J</given-names></name><name><surname>Wu</surname><given-names>W</given-names></name><name><surname>Berkers</surname><given-names>CR</given-names></name><name><surname>Kuster</surname><given-names>B</given-names></name><name><surname>Lemeer</surname><given-names>S</given-names></name></person-group><article-title>Lapatinib resistance in breast Cancer cells is accompanied by phosphorylation-mediated reprogramming of glycolysis</article-title><source>Cancer Res</source><year>2017</year><volume>77</volume><issue>8</issue><fpage>1842</fpage><lpage>1853</lpage><pub-id pub-id-type="doi">10.1158/0008-5472.CAN-16-2976</pub-id><?supplied-pmid 28209619?><pub-id pub-id-type="pmid">28209619</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Barton</surname><given-names>MK</given-names></name></person-group><article-title>Local consolidative therapy may be beneficial in patients with oligometastatic non-small cell lung cancer</article-title><source>CA-A Cancer J Clinicians</source><year>2017</year><volume>2</volume><fpage>89</fpage><lpage>90</lpage><pub-id pub-id-type="doi">10.3322/caac.21363</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jiang</surname><given-names>J</given-names></name><name><surname>Jia</surname><given-names>P</given-names></name><name><surname>Zhao</surname><given-names>Z</given-names></name><name><surname>Shen</surname><given-names>B</given-names></name></person-group><article-title>Key regulators in prostate cancer identified by co-expression module analysis</article-title><source>BMC Genomics</source><year>2014</year><volume>15</volume><issue>1</issue><fpage>1015</fpage><pub-id pub-id-type="doi">10.1186/1471-2164-15-1015</pub-id><?supplied-pmid 25418933?><pub-id pub-id-type="pmid">25418933</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Medina-Villaamil</surname><given-names>V</given-names></name><name><surname>Mart&#x000ed;nez-Breijo</surname><given-names>S</given-names></name><name><surname>Portela-Pereira</surname><given-names>P</given-names></name><name><surname>Quind&#x000f3;s-Varela</surname><given-names>M</given-names></name><name><surname>Santamarina-Ca&#x000ed;nzos</surname><given-names>I</given-names></name><name><surname>Ant&#x000f3;n-Aparicio</surname><given-names>LM</given-names></name><etal/></person-group><article-title>Circulating micrornas in blood of patients with prostate cancer</article-title><source>Actas Urol Esp</source><year>2014</year><volume>38</volume><issue>10</issue><fpage>633</fpage><lpage>639</lpage><pub-id pub-id-type="doi">10.1016/j.acuro.2014.02.008</pub-id><?supplied-pmid 24661838?><pub-id pub-id-type="pmid">24661838</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cai</surname><given-names>C</given-names></name><name><surname>Chen</surname><given-names>QB</given-names></name><name><surname>Han</surname><given-names>ZD</given-names></name><name><surname>Zhang</surname><given-names>YQ</given-names></name><name><surname>He</surname><given-names>HC</given-names></name><name><surname>Chen</surname><given-names>JH</given-names></name><etal/></person-group><article-title>Mir-195 inhibits tumor progression by targeting rps6kb1 in human prostate cancer</article-title><source>Clin Cancer Res</source><year>2015</year><volume>21</volume><issue>21</issue><fpage>4922</fpage><pub-id pub-id-type="doi">10.1158/1078-0432.CCR-15-0217</pub-id><?supplied-pmid 26080838?><pub-id pub-id-type="pmid">26080838</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bozok</surname><given-names>&#x000c7;V</given-names></name><name><surname>Tetik</surname><given-names>VA</given-names></name><name><surname>D&#x000fc;zg&#x000fc;n</surname><given-names>Z</given-names></name><name><surname>Tezcanl&#x00131;</surname><given-names>KB</given-names></name><name><surname>A&#x000e7;&#x00131;kg&#x000f6;z</surname><given-names>E</given-names></name><name><surname>Aktu&#x0011f;</surname><given-names>H</given-names></name><etal/></person-group><article-title>Mir-15a enhances the anticancer effects of cisplatin in the resistant non-small cell lung cancer cells</article-title><source>Tumor Biol</source><year>2016</year><volume>37</volume><issue>2</issue><fpage>1739</fpage><lpage>1751</lpage><pub-id pub-id-type="doi">10.1007/s13277-015-3950-9</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>B</given-names></name><name><surname>Qu</surname><given-names>J</given-names></name><name><surname>Xu</surname><given-names>F</given-names></name><name><surname>Guo</surname><given-names>Y</given-names></name><name><surname>Wang</surname><given-names>Y</given-names></name><name><surname>Yu</surname><given-names>H</given-names></name><etal/></person-group><article-title>Mir-195 suppresses non-small cell lung cancer by targeting chek1</article-title><source>Oncotarget</source><year>2015</year><volume>6</volume><issue>11</issue><fpage>9445</fpage><lpage>9456</lpage><?supplied-pmid 25840419?><pub-id pub-id-type="pmid">25840419</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Lan</surname><given-names>H</given-names></name><name><surname>Zhang</surname><given-names>M</given-names></name><name><surname>An</surname><given-names>N</given-names></name><name><surname>Yu</surname><given-names>R</given-names></name><name><surname>He</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Effects of mir-424 on proliferation and migration abilities in non-small cell lung cancer a549 cells and its molecular mechanism</article-title><source>Zhongguo Fei Ai Za Zhi</source><year>2016</year><volume>19</volume><fpage>571</fpage><lpage>576</lpage><?supplied-pmid 27666545?><pub-id pub-id-type="pmid">27666545</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><mixed-citation publication-type="other">Sun YP, Lu F, Han XY, et al. MiR-424 and miR-27a increase TRAIL sensitivity of acute myeloid leukemia by targeting PLAG1. Oncotarget. 2016;7(18):25276-90.</mixed-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cimmino</surname><given-names>A</given-names></name><name><surname>Calin</surname><given-names>GA</given-names></name><name><surname>Fabbri</surname><given-names>M</given-names></name><name><surname>Iorio</surname><given-names>MV</given-names></name><name><surname>Ferracin</surname><given-names>M</given-names></name><name><surname>Shimizu</surname><given-names>M</given-names></name><etal/></person-group><article-title>Mir-15 and mir-16 induce apoptosis by targeting bcl2</article-title><source>Proc Natl Acad Sci U S A</source><year>2005</year><volume>102</volume><issue>39</issue><fpage>13944</fpage><pub-id pub-id-type="doi">10.1073/pnas.0506654102</pub-id><?supplied-pmid 16166262?><pub-id pub-id-type="pmid">16166262</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhao</surname><given-names>TF</given-names></name><name><surname>Jia</surname><given-names>HZ</given-names></name><name><surname>Zhang</surname><given-names>ZZ</given-names></name><name><surname>Zhao</surname><given-names>XS</given-names></name><name><surname>Zou</surname><given-names>YF</given-names></name><name><surname>Zhang</surname><given-names>W</given-names></name><etal/></person-group><article-title>Lncrna h19 regulates id2 expression through competitive binding to hsa-mir-19a/b in acute myelocytic leukemia</article-title><source>Mol Med Rep</source><year>2017</year><volume>16</volume><issue>3</issue><fpage>3687</fpage><pub-id pub-id-type="doi">10.3892/mmr.2017.7029</pub-id><?supplied-pmid 28765931?><pub-id pub-id-type="pmid">28765931</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vanunu</surname><given-names>O</given-names></name><name><surname>Magger</surname><given-names>O</given-names></name><name><surname>Ruppin</surname><given-names>E</given-names></name><name><surname>Shlomi</surname><given-names>T</given-names></name><name><surname>Sharan</surname><given-names>R</given-names></name></person-group><article-title>Associating genes and protein complexes with disease via network propagation</article-title><source>PLoS Comput Biol</source><year>2010</year><volume>6</volume><issue>1</issue><fpage>e1000641</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1000641</pub-id><?supplied-pmid 20090828?><pub-id pub-id-type="pmid">20090828</pub-id></element-citation></ref></ref-list></back></article>