<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">5850946</article-id><article-id pub-id-type="publisher-id">2036</article-id><article-id pub-id-type="doi">10.1186/s12859-018-2036-2</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>An integrated approach to infer cross-talks between intracellular protein transport and signaling pathways</article-title></title-group><contrib-group><contrib contrib-type="author" corresp="yes" equal-contrib="yes"><name><surname>Tripathi</surname><given-names>Kumar Parijat</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Piccirillo</surname><given-names>Marina</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Guarracino</surname><given-names>Mario Rosario</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1"><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 1940 4177</institution-id><institution-id institution-id-type="GRID">grid.5326.2</institution-id><institution>Lab-GTP, High Performance Computing and Networking Institute, National Research Council, </institution></institution-wrap>Via Pietro Castellino, 111, Naples, 80131 Italy </aff></contrib-group><pub-date pub-type="epub"><day>8</day><month>3</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>8</day><month>3</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>19</volume><issue>Suppl 2</issue><issue-sponsor>Publication of this supplement has not been supported by sponsorship. Information about the source of funding for publication charges can be found in the individual articles. The articles have undergone the journal's standard peer review process for supplements. The Supplement Editors declare that they have no competing interests.</issue-sponsor><elocation-id>58</elocation-id><permissions><copyright-statement>&#x000a9; The Author(s) 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold> This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p>The endomembrane system, known as secretory pathway, is responsible for the synthesis and transport of protein molecules in cells. Therefore, genes involved in the secretory pathway are essential for the cellular development and function. Recent scientific investigations show that ER and Golgi apparatus may provide a convenient drug target for cancer therapy. On the other hand, it is known that abundantly expressed genes in different cellular organelles share interconnected pathways and co-regulate each other activities. The cross-talks among these genes play an important role in signaling pathways, associated to the regulation of intracellular protein transport.</p></sec><sec><title>Results</title><p>In the present study, we device an integrated approach to understand these complex interactions. We analyze gene perturbation expression profiles, reconstruct a directed gene interaction network and decipher the regulatory interactions among genes involved in protein transport signaling. In particular, we focus on expression signatures of genes involved in the secretory pathway of MCF7 breast cancer cell line. Furthermore, network biology analysis delineates these gene-centric cross-talks at the level of specific modules/sub-networks, corresponding to different signaling pathways.</p></sec><sec><title>Conclusions</title><p>We elucidate the regulatory connections between genes constituting signaling pathways such as PI3K-Akt, Ras, Rap1, calcium, JAK-STAT, EFGR and FGFR signaling. Interestingly, we determine some key regulatory cross-talks between signaling pathways (PI3K-Akt signaling and Ras signaling pathway) and intracellular protein transport.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s12859-018-2036-2) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Perturbation</kwd><kwd>Genetic Network</kwd><kwd>Secretory Pathway</kwd></kwd-group><conference xlink:href="http://www.bmtl.it/"><conf-name>Bringing Maths to Life 2017</conf-name><conf-acronym>BMTL 2017</conf-acronym><conf-loc>Naples, Italy</conf-loc><conf-date>07-09 June 2017</conf-date></conference><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>The secretory pathway is composed of different organelles suspended in the cytoplasm. It includes rough endoplasmic reticulum (rough ER), ER exit sites (ERESs), the ER-to-Golgi intermediate compartment (ERGIC) and the Golgi complex cellular organelles, which have distinct functions in the transport of proteins to their final destination in the cell. Not only does the secretory pathway play an important role in proteins synthesis and delivery, but it also facilitates the proper folding and post-translational modifications of protein [<xref ref-type="bibr" rid="CR1">1</xref>]. At present, we know that these organelles are able to interact dynamically with each other and play an important role in the establishment of cellular homeostasis; furthermore, the cross-talks between these inter cellular compartments are also required to maintain the structure and shape of the cell and for its survival [<xref ref-type="bibr" rid="CR2">2</xref>]. Recent studies show all these cellular organelles within the secretory pathway are sensitive to stress conditions and capable to propagate the signaling for cell death [<xref ref-type="bibr" rid="CR2">2</xref>]. Basically, signaling implies the conversion of mechanical or chemical stimuli directed towards the cell into a specific cellular response. In a general signaling pathway, a signal is received by the receptor molecules, which leads to a change in functioning and modulation of the cellular response driven by series of molecular interactions within the cellular boundary. These interactions include the activation and inhibition of numerous kinases and signaling molecules producing a complex inter dependent molecular cross-talks. To understand the complex relationship between signaling and secretory pathway in a broader perspective, it is important to study the genetic interactions within the cell and determine the gene regulatory network. Previously, researchers have been using correlation and gene co-expression based networks, to infer a genome wide representation of the complex functional organization of gene interaction networks [<xref ref-type="bibr" rid="CR3">3</xref>]. These networks are predicted on the similarity of the gene expression profiles. However, these reconstructed gene networks are undirected, and therefore it is difficult to infer the causality relationship between two connected genes. The other caveat associated with co-expression network analysis regards the handling of large data sets, which limits the biological interpretation of the data [<xref ref-type="bibr" rid="CR4">4</xref>, <xref ref-type="bibr" rid="CR5">5</xref>]. Though regression methods have been used to determine directed edges and to identify the set of genes having regulatory effects on their target, these methods are generally computational demanding and often limited to predict the set of genes regulated by transcription factors [<xref ref-type="bibr" rid="CR6">6</xref>, <xref ref-type="bibr" rid="CR7">7</xref>]. Recently, gene perturbation studies have started playing an important role in directed gene networks reconstruction and in determining their reciprocal influence [<xref ref-type="bibr" rid="CR8">8</xref>&#x02013;<xref ref-type="bibr" rid="CR11">11</xref>]. In the present work, we study the gene-gene interactions in MCF7 breast cancer cell line using an integrated approach (shown in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">1</xref>) based on functional genomics and network analysis, derived from expression profiles of knocked-down or over-expressed genes within the secretory pathway. Signaling associated to and from protein transport machinery provides convenient therapeutic targets for drug development in cancer therapy [<xref ref-type="bibr" rid="CR12">12</xref>]. Therefore, we try to decipher the direct and indirect genetic regulatory components of secretory pathway and their corresponding cross-talk with cellular signaling within the cell. Our goal is to understand the complex interactions among genes, constituting important signaling pathways with respect to protein transport in a cancer cells. Furthermore, we investigate the cause and disturbance in the delicate balance of cross-talks among these genes, which can lead to cancer progression. We try to highlight the interesting aspects of gene-gene interactions, which they could be as potential drug target for cancer therapies.
<fig id="Fig1"><label>Fig. 1</label><caption><p>Schematic representation of the pipeline. <bold>a</bold> Perturbation experiments. <bold>b</bold> Perturbation expression profiles matrix. <bold>c</bold> Co-regulated gene-gene interaction network. <bold>d</bold> Identification and clustering of functional modules. <bold>e</bold> Identification of expression activated seb network (hotspot identification). <bold>f</bold> pathways and GO term enrichment analysis to infer cross-talk between different clusters. <bold>g</bold> Interaction database analysis along with functional annotation to infer regulatory patterns</p></caption><graphic xlink:href="12859_2018_2036_Fig1_HTML" id="MO1"/></fig></p></sec><sec id="Sec2"><title>Methods</title><sec id="Sec3"><title>Data retrieval</title><p>To reconstruct the regulatory networks of secretory pathway we use the library of integrated network-based cellular signatures (LINCS) L1000, which contains more than a million gene expression profiles of perturbed human cancer cell lines (<ext-link ext-link-type="uri" xlink:href="http://lincs.hms.harvard.edu">http://lincs.hms.harvard.edu</ext-link>). In more detail, it consists of 1328098 expression profiles of 22268 genes (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a). A set of 978 genes, named <italic>landmark genes</italic>, were directly measured using a microarray technology. The remaining 21290 are <italic>target genes</italic>, whose expression has been inferred by a deep learning algorithm (D-GEX) trained on GEO data. Perturbation experiments were executed on different cell lines, at different time points, silencing or over-expressing genes, or treating cells with chemical compounds. For our work, we studied 3647 experiments in which genes have been knocked-down/over-expressed in MCF7 breast cancer cell line. Among the 2552 genes of the secretory pathway, only 591 were perturbed in L1000 dataset. For each perturbation experiment, we collect data for all the biological and technical replicates at two different time points (96H, 144H). We used the so called <italic>level 3</italic> dataset, which includes standardized gene expression profiles of directly measured landmark transcripts plus imputed genes (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>b). Finally, to map transcription factors and gene interactions information on the 591 perturbation experiments, we use the human transcription factors from AnimalTFDB 2.0 [<xref ref-type="bibr" rid="CR13">13</xref>] and manually curated human signaling network data from Edwin Wang and associated group (<ext-link ext-link-type="uri" xlink:href="http://www.cancer-systemsbiology.org/data-software">http://www.cancer-systemsbiology.org/data-software</ext-link>). This signaling network dataset contains more than 6000 proteins and 63,000 relations. These relations represent activation, inhibition and physical interactions, which in turn describe complexes that play crucial roles in cell signaling.</p><sec id="Sec4"><title>Reconstruction of Regulatory Interactions</title><p>To obtain gene regulatory interactions and reconstruct gene network (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>c) from gene perturbation experiments, we developed a computational pipeline, which is divided into several steps. The z-scored perturbation expression profiles are represented as <italic>Z</italic><sub><italic>j</italic></sub>&#x02208;<italic>Z</italic> with <italic>j</italic> = 1,&#x02026;,<italic>n</italic>, in our case <italic>n</italic>=591. Each profile <italic>Z</italic><sub><italic>j</italic></sub> comprises <italic>m</italic><sub><italic>j</italic></sub> biological replicates (usually between 2 and 4), which are repeated measurements of biologically distinct samples and capture random biological variation [<xref ref-type="bibr" rid="CR14">14</xref>]. Each biological replicate is the average of <italic>q</italic><sub><italic>k</italic></sub> technical replicates, which are repeated measurements of the same sample (usually between 4 and 6). A given perturbation experiment is represented as: 
<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} {}P_{j,k,l} \ where \ j = {1,\ldots,n}; \ k = {1,\ldots,m_{j}}; \quad l = {1,\ldots,q_{k}} \end{array} $$ \end{document}</tex-math><mml:math id="M2"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>,</mml:mo><mml:mi>k</mml:mi><mml:mo>,</mml:mo><mml:mi>l</mml:mi></mml:mrow></mml:msub><mml:mspace width="1em"/><mml:mtext mathvariant="italic">where</mml:mtext><mml:mspace width="1em"/><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo>,</mml:mo><mml:mi>n</mml:mi><mml:mo>;</mml:mo><mml:mspace width="1em"/><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>;</mml:mo><mml:mspace width="1em"/><mml:mi>l</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>q</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2036_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula></p><p>where <italic>m</italic><sub><italic>j</italic></sub> is the number of biological replicates in <italic>j</italic>-th profile, and <italic>q</italic><sub><italic>k</italic></sub> is the number of technical replicates for the <italic>k</italic>-th biological sample in the perturbation experiment. The mean biological sample <inline-formula id="IEq1"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\overline {P}_{j,k}$\end{document}</tex-math><mml:math id="M4"><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>,</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12859_2018_2036_Article_IEq1.gif"/></alternatives></inline-formula> from the <italic>q</italic><sub><italic>k</italic></sub> technical replicates is calculated as: 
<disp-formula id="Equ2"><label>2</label><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} \overline{P}_{j,k} = \sum\limits_{l=1}^{q_{k}}P_{j,k,l}/q_{k} \end{array} $$ \end{document}</tex-math><mml:math id="M6"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>,</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:munderover accent="false" accentunder="false"><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>l</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>q</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:munderover><mml:msub><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>,</mml:mo><mml:mi>k</mml:mi><mml:mo>,</mml:mo><mml:mi>l</mml:mi></mml:mrow></mml:msub><mml:mo>/</mml:mo><mml:msub><mml:mrow><mml:mi>q</mml:mi></mml:mrow><mml:mrow><mml:mi>k</mml:mi></mml:mrow></mml:msub></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2036_Article_Equ2.gif" position="anchor"/></alternatives></disp-formula></p><p>In the second step, we create a matrix with all biological replicates (averages), for which we calculate the first principal component, that is the linear combination of the biological replicates pointing in the direction of maximum variance. Before performing the Principal Component Analysis, we pre-processed the data to normalize their mean as follow: 
<disp-formula id="Equ3"><label>3</label><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} \overline{\overline{P}}_{j} = \sum\limits_{k=1}^{m_{j}}\overline{P}_{j,k}/m_{j} \end{array} $$ \end{document}</tex-math><mml:math id="M8"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:munderover accent="false" accentunder="false"><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>k</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:munderover><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>,</mml:mo><mml:mi>k</mml:mi></mml:mrow></mml:msub><mml:mo>/</mml:mo><mml:msub><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2036_Article_Equ3.gif" position="anchor"/></alternatives></disp-formula></p><p>
<disp-formula id="Equ4"><label>4</label><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document} $$\begin{array}{@{}rcl@{}} Z_{j}=\left(\frac{\overline{P}_{j,1} - \overline{\overline{P}}_{j}}{\sigma_{j}}, \ldots, \frac{\overline{P}_{j,m_{j}} - \overline{\overline{P}}_{j}}{\sigma_{j}} \right) \quad \: j \: = {1,\ldots,n}, \end{array} $$ \end{document}</tex-math><mml:math id="M10"><mml:mtable class="eqnarray" columnalign="left center right"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mfenced close=")" open="(" separators=""><mml:mrow><mml:mfrac><mml:mrow><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>&#x003c3;</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mfrac><mml:mo>,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo>,</mml:mo><mml:mfrac><mml:mrow><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>&#x003c3;</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced><mml:mspace width="1em"/><mml:mspace width="2.22144pt"/><mml:mi>j</mml:mi><mml:mspace width="2.22144pt"/><mml:mo>=</mml:mo><mml:mn>1</mml:mn><mml:mo>,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo>,</mml:mo><mml:mi>n</mml:mi><mml:mo>,</mml:mo></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12859_2018_2036_Article_Equ4.gif" position="anchor"/></alternatives></disp-formula>
</p><p>where <italic>&#x003c3;</italic><sub><italic>j</italic></sub> is the standard deviation of <inline-formula id="IEq2"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\overline {P}_{j,1},\ldots,\overline {P}_{j,m_{j}}$\end{document}</tex-math><mml:math id="M12"><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>,</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mover accent="false"><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mo accent="true">&#x000af;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>m</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12859_2018_2036_Article_IEq2.gif"/></alternatives></inline-formula>.</p><p>For each perturbation <italic>j</italic>, we select one biological replicate <inline-formula id="IEq3"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\hat {Z}_{j}$\end{document}</tex-math><mml:math id="M14"><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mo>&#x0005e;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12859_2018_2036_Article_IEq3.gif"/></alternatives></inline-formula>, with maximum correlation with first principal component of <italic>Z</italic><sub><italic>j</italic></sub>. Each column of the matrix <inline-formula id="IEq4"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$A=\left [\hat {Z}_{1}, \ldots, \hat {Z}_{n} \right ]$\end{document}</tex-math><mml:math id="M16"><mml:mi>A</mml:mi><mml:mo>=</mml:mo><mml:mfenced close="]" open="[" separators=""><mml:mrow><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mo>&#x0005e;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:mo>&#x02026;</mml:mo><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mover accent="true"><mml:mrow><mml:mi>Z</mml:mi></mml:mrow><mml:mo>&#x0005e;</mml:mo></mml:mover></mml:mrow><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mfenced></mml:math><inline-graphic xlink:href="12859_2018_2036_Article_IEq4.gif"/></alternatives></inline-formula> represents the influence of perturbation on the expression values of all the genes in the experiments. Finally, for each perturbation, we selected only those genes, for which we observed a differential fold change &#x02265;&#x02009;4 or &#x02264;&#x02212;4, in case of over expressed or under expressed genes respectively, in at least one biological replicate. Generally, any selection based solely on fold change is arbitrary and there is no right nor wrong threshold; but fold change (FC) cut-off of &#x02265;&#x02009;2 or &#x02264;&#x02212;2, leads to look only at genes which vary widely among the other genes. For this reason in our work, to reduce the number of genes, we decided to use a most stringent FC, finally obtaining a list of 576 perturbed genes. The construction of the network is now straightforward, because the perturbation of a gene directly or indirectly affects the regulation of the others that have been detected as differentially expressed in that experiment. We applied a simplification algorithm to get rid of direct regulations introduced by the described reconstruction method [<xref ref-type="bibr" rid="CR9">9</xref>].</p></sec></sec><sec id="Sec5"><title>Network analysis</title><p>In this work we consider only gene interaction networks in which directed edges connecting two genes represent a biochemical process such as a reaction, transformation, interaction, activation or inhibition. We have not considered gene co-expression networks (GCN) in which the direction and type of co-expression relationships are not determined, because they are an undirected graphs where each node corresponds to a gene, and a pair of nodes is connected with an edge if they show a similar expression pattern. Therefore with the help of Cytoscape network visualization tool [<xref ref-type="bibr" rid="CR15">15</xref>], we visualize the gene-gene interaction network from obtained regulatory interactions (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>c) and carry out directed network analysis using Network Analyzer [<xref ref-type="bibr" rid="CR16">16</xref>]. We computed the topological parameters, such as number of nodes, edges and connected components for directed regulatory network. Further, we also computed the network diameter, radius, clustering coefficient, characteristic path length, betweenness and closeness, as well as the distributions of degrees, neighborhood connectivity and number of shared neighbors.</p></sec><sec id="Sec6"><title>Network hot-spot identification</title><p>In Cytoscape, the j-Active Modules plugin identifies expression activated sub-networks (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>e) from previously obtained molecular interaction network [<xref ref-type="bibr" rid="CR17">17</xref>]. These sub-networks are highly connected components of the existing network, where the genes show similar significant expression changes in response to particular subsets of conditions (perturbations). The method uses a statistical approach to score sub-networks with a search algorithm for finding sub-networks with high score. The idea of finding these sub-networks is to determine functional modules represented by highly connected network regions with similar responses to experimental conditions. We run j-Active Modules on our gene interaction network in default mode, taking betweenness centrality and neighborhood connectivity as node attributes. In advanced parameter section, we set number of modules to 5 and overlap threshold to 0.8. Further, we employ <italic>search</italic> strategy to obtain high-scoring modules using local and greedy search.</p></sec><sec id="Sec7"><title>Reactome functional interaction (FI) network analysis</title><p>To study the pathways enrichment and network patterns in the sub-network with respect to signaling and intracellular protein transport, we use ReactomeFI-plugin [<xref ref-type="bibr" rid="CR18">18</xref>] in Cytoscape, to integrate the Reactome database [<xref ref-type="bibr" rid="CR19">19</xref>], and other tools such as Transcriptator [<xref ref-type="bibr" rid="CR20">20</xref>] and Metabox library [<xref ref-type="bibr" rid="CR21">21</xref>]. Taking a FDR cut-off value &#x02264;0.05, we carry out pathway enrichment analysis for a set of genes in a given sub network, and investigate the functional relationships among genes in enriched pathways. With the help of this plugin, we first access the Reactome Functional Interaction (FI) network, and fetch FI indexing for all the nodes (genes) present in sub-network. Later, we build a FI sub-network based on a set of genes, query the FI data source for the underlying evidence for the interaction to construct modules by running a network clustering algorithm (spectral partition based network clustering) [<xref ref-type="bibr" rid="CR22">22</xref>] and analyze these network modules of highly interacting groups of genes (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>d). Finally, we carried out functional enrichment analysis to annotate the modules, and expand the network by finding genes related to the experimental data set.</p></sec><sec id="Sec8"><title>Inferring gene regulatory interactions with respect to protein transport signaling</title><p>To understand the functional and regulatory relationship among genes in expression activated sub-networks, we use GeneMania plugin [<xref ref-type="bibr" rid="CR23">23</xref>] (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>g). It extends the sub-networks by searching publicly available biological datasets to find related genes. These include protein-protein, protein-DNA and genetic interactions, pathways, reactions, gene and protein expression data, protein domains and phenotype screening profiles. Integration of physical interaction, genetic interaction, co-localization and pathway information related to the nodes present in the sub-networks, helps to show regulatory interactions among specific genes involved in signaling of protein transport.</p></sec><sec id="Sec9"><title>Extending regulatory interaction network through external resources to determine genetic cross-talks</title><p>By analyzing manually curated human signaling network data, we obtain further signaling interactions and introduce them to extend the regulatory interaction network already obtained from ReactomeFI network analysis. The signaling interaction data contain activation, inhibition and physical interactions. The physical relations represent complexes that play a role in cell signaling. Furthermore, we also map the transcription factors, gene ontology, and gene specific enriched reactome and KEGG pathways information on the reconstructed direct sub-network to infer direct physical and genetic interactions between perturbed genes and their effected components (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>f). We primarily focus on Ras and PI3K-Akt signaling pathways, to study and infer cross-talks among genetic components between them.</p></sec></sec><sec id="Sec10"><title>Results and Discussion</title><sec id="Sec11"><title>Biological function enrichment analysis of perturbed genes</title><p>We carried out Gene Ontology and pathway enrichment analysis of the complete list of perturbed genes, for which corresponding expression profiles are utilized in this study. It includes 576 unique perturbed genes which have regulatory effects on the other genes. DAVID and Transcriptator functional annotation tools are used to carry out enrichment analysis taking a multiple correction p-value cutoff &#x02264;&#x02009;0.05. The complete list of perturbed genes is provided as Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>. The functional term enrichment analysis suggests the role of these genes in intracellular protein transport such as exocytosis and endocytosis. As expected, the cellular components enrichment analysis suggests that most of these genes functions are localized in ER, Golgi apparatus, Golgi membrane, ER lumen, extracellular exosome, plasma membrane, trans-Golgi network, ER to Golgi transport vesicle membrane, endosome, endocytic vesicle membrane and lysosome etc. Similarly, biological process enrichment analysis shows biological processes related to regulation of intracellular protein transport such as exocytic and endocytic cellular mechanism, protein folding and modification process, as highly enriched terms. Some of these enriched biological functional terms are: positive regulation of protein phosphorylation, protein phosphorylation, protein glycosylation, ER to Golgi vesicle-mediated transport, retrograde vesicle-mediated transport, Golgi to ER, endocytosis, autophagy, Golgi organization, vesicle-mediated transport, sphingolipid biosynthetic process, ER unfolded protein response, ER calcium ion homeostasis, response to ER stress, IRE1-mediated unfolded protein response, lipoprotein biosynthetic process, protein autophosphorylation, chaperone-mediated protein folding, intrinsic apoptotic signaling pathway in response to ER stress and positive regulation of ERK1 and ERK2 cascade. The complete results of enrichment analysis is provided in Additional file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>.</p></sec><sec id="Sec12"><title>Regulatory interaction network from 591 gene perturbation expression data profile</title><p>Using our pipeline we reconstructed a network based on regulatory interactions consisting of 4467 nodes and 12871 edges (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>a). The edges between the nodes in this network represent the 4 folds up or down regulation of affected genes in response to perturbation experiments. The characteristic path length of this network is 5.24 and average number of neighbors is 5.76. We calculate the following network statistics for all the constituting nodes in the network: topological coefficients, betweenness, closeness, distributions of degrees, neighborhood connectivity, average clustering coefficient and stress centrality. The table with all topological parameters for each node in the network is provided in Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>. VHL shows a maximum out-degree of 923, which implies that its perturbation has a regulatory effect on the whole network. From the analysis of the network, we obtained 349 nodes having out-degree greater or equal to 10 (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b). Among these 349 high out-degree nodes (represented in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>a, in darker color), there are 15 nodes which represent the maximum (&#x02265;&#x02009;100) of out-degrees, in other words, the perturbations in these genes have a significant effect on the transcriptional response. These nodes are represented in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>. Enriched Gene ontology terms associated with these genes are protein transport, localization, protein and lipid metabolism. Nodes (effected genes) with high in-degree&#x02019;s (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>c) are highly enriched in kinase activity, transcriptional regulation, cell adhesion, RNA binding, protein binding, purine nucleotide binding, signal transduction, catalytic activities. The perturbed genes with high out-degrees in this directed network, are enriched in protein transport, localization and protein and lipid metabolism (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>d) and perturbation of these genes have direct and indirect regulatory effects on the elements of signal transduction, binding, transcriptional regulation and kinase activities (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>e). The complete functional enrichments are provided in the Additional file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Regulatory interaction network. <bold>a</bold> Regulatory interaction network obtained from 591 perturbation experiments. The node color represents the degree of nodes; hub nodes are represented in red and yellow color. <bold>b</bold> out-degree distribution of nodes (perturbations). <bold>c</bold> in-degree distribution of nodes (effected genes). <bold>d</bold> Gene ontology enrichment analysis of perturbed genes. <bold>e</bold> Gene ontology enrichment analysis of effected genes</p></caption><graphic xlink:href="12859_2018_2036_Fig2_HTML" id="MO2"/></fig>
<table-wrap id="Tab1"><label>Table 1</label><caption><p>15 nodes with the maximum (&#x02265;&#x02009;100) of out-degrees</p></caption><table frame="hsides" rules="groups"><tbody><tr><td align="left">VHL</td><td align="left">TOR1A</td><td align="left">CSNK1D</td></tr><tr><td align="left">CHEK2</td><td align="left">STK16</td><td align="left">POR</td></tr><tr><td align="left">SP3</td><td align="left">USP32</td><td align="left">SRPRB</td></tr><tr><td align="left">GPR107</td><td align="left">RAF1</td><td align="left">FEZ1</td></tr><tr><td align="left">CFTR</td><td align="left">PPAP2B</td><td align="left">CRTAP</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec13"><title>Inferring gene regulatory components through the identification and analysis of expression activated (hotspot) sub-network</title><p>With the help of j-Active plugin, we disaggregate the larger perturbation network into 5 smaller expression activated sub-network modules represented in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>. In the sub-networks, all the nodes are directly connected to the hub node/perturbed gene, and show similar significant changes (up or down-regulations) in expression in response to particular subsets of conditions (perturbations). For the sake of understanding the underlying gene-regulatory interaction within these sub networks, we selected the smallest module 5 for our study (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>). Firstly, we obtained the pathways, molecular function and biological processes enrichment analysis of module 5 sub-network using Reactome FI analysis. The genes are divided into three functional modules and are regulated by the perturbations of FAM3C, PPAP2B and CLTA respectively. Enrichment analysis shows that initiation, elongation and termination of translation processes, along with nonsense mediated decay are significantly enriched in genes regulated by FAM3C and perturbations of PPAP2B and CLTA do not have any effect on the regulation of these pathways. The perturbation of FAM3C may plays an important role in the eukaryotic translation pathways as well as nonsense mediated decay. Further, integration of co-expression, physical interaction, genetic interaction, co-localization and pathway information related to the nodes present in the sub-network, strengthen the relationship between FAM3C and its regulatory effects on the genes with respect to nonsense mediated decay. While many biological process, such as translation, RNA-metabolic process, cellular protein metabolic process, mesenchymal to epithelial transition, positive regulation of transcription, meiosis, negative regulation of interferon-gamma production, rRNA processing are not directly effected by the perturbation of FAM3C, they are regulated by the PPAP2B perturbation.
<fig id="Fig3"><label>Fig. 3</label><caption><p>Network hot-spot Network hot-spot identified as gene regulatory network with respect to their regulatory interaction. Analysis of this network hot-spot through GeneMania analysis. Gene ontology (biological process) enrichment analysis for the network regulated by FAM3C perturbation. The edges with arrow signs represent the 4x increase in the expression of connected genes, while edges with dot represent the down-regulations. We map the information of perturbed genes (represented by green color) and transcription factor (in red color) on the network</p></caption><graphic xlink:href="12859_2018_2036_Fig3_HTML" id="MO3"/></fig>
<table-wrap id="Tab2"><label>Table 2</label><caption><p>The 5 modules obtained with j-Active Modules</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Module</th><th align="left">n of nodes</th><th align="left">n of edges</th></tr></thead><tbody><tr><td align="left">1</td><td align="left">3136</td><td align="left">4424</td></tr><tr><td align="left">2</td><td align="left">375</td><td align="left">573</td></tr><tr><td align="left">3</td><td align="left">334</td><td align="left">364</td></tr><tr><td align="left">4</td><td align="left">225</td><td align="left">231</td></tr><tr><td align="left">5</td><td align="left">159</td><td align="left">162</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec14"><title>Cross-talks in signaling pathway</title><p>To understand the complex gene-gene interaction network of cell signaling in the context of cellular protein transport and cancer progression, we carried out functional gene ontology and reactome pathways enrichment analysis for the constructed gene regulatory network. Based on the pathways enrichment, we extracted the sub-network consisting of gene/nodes involved in prominent signaling such as PI3K-Akt, RAP1, Ras pathway, calcium, P53 and MAPK signaling. Furthermore, we carried out the indexing of nodes with the help of ReactomeFI plugin and we clustered the sub-network into 5 functional modules based on gene ontology terms in biological process, molecular function, cellular components and reactome pathways enrichment analysis. The results are provided in the Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>. Taking into account highly enriched signaling pathways (<italic>p</italic>-value &#x02264; 0.05) and cut-off for constituent nodes &#x02265;&#x02009;12 in each module, we observed high intensity of cross-talks between signaling involved in protein transport (such as Ras signaling) and cancer progression (PI3K-Akt). Genes associated with PI3K-Akt signaling interacts with calcium signaling pathway. Considering module wise study, we notice that PI3K-Akt signaling is the most prominent and enriched signaling pathway, forming the core of each cluster (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>).
<fig id="Fig4"><label>Fig. 4</label><caption><p>Clustering of regulatory network into 5 functional modules. Signaling pathway enrichment analysis in each functional module obtained from clustering of gene-gene interaction network. The color represents the functional module in clustered network. The blue color represents the module 0, light green represents module 1, khaki color represents module 2, bright green represents module 3 and in the last, module 4 is represented by violet color. The bar plot for each functional module shows the number of genes enriched in signaling pathways, by taking the cut-off for corrected <italic>p</italic>-value &#x02264; 0.05</p></caption><graphic xlink:href="12859_2018_2036_Fig4_HTML" id="MO4"/></fig></p><p>In cluster/module 0, PI3K-Akt interacts with Ras and Rap1 signaling pathways, while Ras pathway forms the major component of protein transport. Ras is a part of well described mitogen activated protein (MAP) kinase-Ras-Raf-MEK-ERK- pathway downstream initiated by receptor tyrosine kinase and integrins, leading to several cellular processes such proliferation, differentiation of cell, membrane genesis, protein synthesis and secretion and also has an intermediate effect on gene expression [<xref ref-type="bibr" rid="CR1">1</xref>].</p><p>In cluster/module1, cluster/module2 and cluster/module3, we observe the interactions between PI3K-Akt and kinase signaling such as <italic>ERBB2</italic>, <italic>EGFR</italic> and <italic>ERBB4</italic>. In the last module/cluster 4, enrichment of PI3K-Akt signaling, JAK-STAT and calcium signaling suggests activation of PI3K-Akt signaling through both calcium and stress-activated protein Jun kinases and vice versa. In the past, researchers portrays the role of intracellular Ca2+ and disturbances in its cellular concentration, with respect to tumor initiation, angiogenesis, progression and metastasis in the normal cells [<xref ref-type="bibr" rid="CR24">24</xref>]. To delve further into the relationship between Ras and PI3K-Akt signaling, we extracted the constituent genes/nodes from regulatory interactions within module 0, and we observed interesting relationship among them, as shown in Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>. The regulatory interactions among these genes (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>) show that important constituent genes in PI3K-Akt signaling are regulated by the genetic components of Ras signaling pathway. In some cases, common genes regulate both pathways, exhibiting higher level of cross-talk between them. It is worth noting that perturbation of <italic>CDH1</italic> leads to the 4 fold decrease in the expression of <italic>YWHAZ</italic> gene, which is a member of the 14-3-3 protein family and a central hub protein for many signal transduction pathways. YWHAZ gene regulates apoptotic pathways critical to cell survival and plays a key role in a number of cancers and neuro-degenerative diseases [<xref ref-type="bibr" rid="CR25">25</xref>]. This gene is a well-known target for cancer therapy (14-3-3 zeta as novel molecular target for cancer therapy). Hence <italic>CDH1</italic> could be a potential gene as a molecular target for cancer therapy.
<fig id="Fig5"><label>Fig. 5</label><caption><p>Interesting gene regulatory interactions with respect to PI3K-Akt signaling and Ras signaling pathway. PI3K-Akt signaling components are represented by oval shape. Genes involved in Ras signaling are represented by rectangular shape. Yellow color represent the perturbed genes involved in both signaling pathway. In this figure, we have shown the PI3K-Akt enriched genes in red color, while the genes enriched in Ras pathway are represented by rectangular shape. The yellow color represents the genes which undergoes perturbation experiments to obtain the gene-gene regulatory network. The edges with arrow signs represent the four fold increase in the expression of target/effected genes, while edges with dot represent the four fold down-regulation</p></caption><graphic xlink:href="12859_2018_2036_Fig5_HTML" id="MO5"/></fig>
<table-wrap id="Tab3"><label>Table 3</label><caption><p>Genes enriched in PI3K-Akt and Ras signaling</p></caption><table frame="hsides" rules="groups"><tbody><tr><td align="left">Signaling pathway</td><td align="left">Genes</td></tr><tr><td align="left">PI3K-Akt</td><td align="left">YWHAZ,COL4A5,SGK1,ITGB5,TSC2,LAMA2,</td></tr><tr><td align="left"/><td align="left">PTEN,GRB2,PP2R3A</td></tr><tr><td align="left">Ras signaling pathway</td><td align="left">CDH1,HRAS,CALM1,FGFR3,KDR,AKT1,FGFR4,</td></tr><tr><td align="left"/><td align="left">Rap1A,PRKC1</td></tr><tr><td align="left">PI3k-AKT and Ras signaling</td><td align="left">HRAS,KDR,FGFR3,AKT1,FGFR2,FGFR4</td></tr></tbody></table></table-wrap></p><p>All these genetic regulatory interactions might provide a viable target for cancer drug therapy. From our results, we observed that PTEN is heavily down-regulated by the perturbation of <italic>TSC2</italic> gene. Both the genes play an important role in PI3K-Akt signaling. <italic>PTEN</italic> gene is a tumor suppressor [<xref ref-type="bibr" rid="CR26">26</xref>], and mutation in this gene leads to cancer developments. <italic>TSC2</italic> mutations lead to tuberous sclerosis, and its gene products is supposedly a tumor suppressor [<xref ref-type="bibr" rid="CR27">27</xref>]. From this information, we can infer that perturbation of TSC2 gene plays an important role in increasing cancer risk in muscular dystrophy, as it regulates LAMA2 gene. Genetic mutations in LAMA2 genes have their implication in a severe form of muscular dystrophy [<xref ref-type="bibr" rid="CR28">28</xref>]. <italic>TSC2, LAMA2</italic> and <italic>PTEN</italic> interactions could be useful to study a potential drug therapy for cancer as well as muscular dystrophy. Similarly <italic>CCND1</italic> amplification and its protein expression is strongly correlated with breast cancer [<xref ref-type="bibr" rid="CR29">29</xref>], the perturbation of KDR gene, which is a type III receptor tyrosine kinase involved in Ras pathway, down-regulates the <italic>CCND1</italic> expression and controls its amplification with respect to cancer.</p><p>From the regulatory interaction network analysis, we infer that genetic perturbations involved in protein transport have profound effects on the signal transduction, and transcriptional regulation activities of the cell. We also carried out functional analysis of the nodes with high value of betweenness centrality, as these nodes do play an important role in bridging between the sub-networks and hub nodes. The results show several enriched pathway: ER-nucleus signaling pathway (GO:0006984), cellular response to topologically incorrect protein (GO:0035967), response to topologically incorrect protein (GO:0035966), response to unfolded protein (GO:0006986) with significant <italic>p</italic>-value &#x02264; 0.05. Furthermore, functional enrichment analysis of the nodes with respect to out-degree, in-degree and betweenness centrality, helps us to understand the underlying cross-talk between protein transport, localization on the signal transduction and transcriptional rewiring and their mutual effects on protein folding. In our regulatory network, we find some interesting and significantly enriched signaling pathways, such as PI3K-Akt, Ras, Rap1, calcium, JAK-STAT, EFGR and FGFR signaling. In recent years, researchers observed the role of PI3K-Akt signaling in cancer progression, which is basically a disturbance in the balance of cell division and growth with respect to programmed cell death. This particular signaling pathway is disturbed in many human cancer and not only does it play a major role in tumor development, but also in its potential response to the treatment [<xref ref-type="bibr" rid="CR30">30</xref>]. In our results, we observe that PI3K-Akt signaling interacts with several kinases, such as ERBB2, EGFR and ERBB4. These kinases are known to play an important role in a very aggressive form of breast cancer [<xref ref-type="bibr" rid="CR31">31</xref>]. This kind of signaling leads to a characteristic behavior of cancer cells such as uncontrolled proliferation, resistance to apoptosis and increased motility. Apart from this, PI3K-Akt signaling shares interactions with platelets and fibroblast growth factors signaling pathways, which play a very important part in cell growth regulation, proliferation, survival, differentiation and angiogenesis [<xref ref-type="bibr" rid="CR31">31</xref>]. Most of these pathways are involved in the normal deployment of protein transport but also have a potential role in activating both upstream and downstream important signaling pathways. Some of these functions are cell proliferation, differentiation, membrane biogenesis, inflammation protein syntheses, cell migration and gene expression regulation. In a broader sense, all these signaling networks comprise a fine tuning balance for cellular function. Any disturbance in such a balance leads to negative signaling cascades and has a deteriorating effect on cell functioning, possible leading to cancer progression.</p></sec><sec id="Sec15"><title>Cross-talks between intracellular protein transport and signaling pathways</title><p>In addition to study the cross-talks between different signaling pathways in intracellular protein transport, we also infer the regulatory effects of signaling pathways on intracellular protein trafficking mechanism related to exocytic and endocytic pathways [<xref ref-type="bibr" rid="CR32">32</xref>]. Through exocytic pathway, protein cargo moves from ER, via Golgi apparatus, to the plasma membrane. During this movement, it also undergoes to a modification by the addition of sugar and lipids. On the other hand, moving through this forward exocytic pathway via ER-Golgi-plasma membrane compartments, the protein cargo has to be retrieved back to its original compartment in a reverse direction, to maintain the compartment identity. This backward movement of protein cargo from plasma membrane to Golgi to ER compartment is known as <italic>retrograde protein transport</italic>. There is also an endocytic pathway, through which cargo is internalized from the cell milieu. The best characterized endocytic pathway proceeds from clathrin coated vesicles through early and late endosomes to lysosomes. The lysosomes is a major degradation site for internalized cargo and cellular membrane proteins [<xref ref-type="bibr" rid="CR32">32</xref>]. In our results, we observed regulatory interactions among genes involved in intracellular protein transport and PI3K-Akt, Ras, MAPK, interferon and calcium signaling. In the modules study, we specifically focus on PI3K-Akt and Ras signaling pathways and their regulatory interactions with intracellular protein transport components in MCF-7 cell line. In the previously described module 0, as shown in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>, genes enriched in PI3K-Akt signaling pathway such as IL7R,TNN, ITGB4, CSF1R, STK11, ITGB1, ITGB7, FGF17, COL5A3, PDGFRA, PP2R3A, AKT1, HSP90AA, CCND1 and PTEN and YWHAZ are regulated by the perturbations in CSNK1D, TSC2, PML, SHH, KDR and SGK1 genes. Out of these perturbed genes, KDR,SGK1 and TSC2 also play an important role in PI3K-Akt signaling [<xref ref-type="bibr" rid="CR33">33</xref>, <xref ref-type="bibr" rid="CR34">34</xref>]. The CSNK1D gene is involved in protein phosphorylation process, endocytosis and Golgi organization [<xref ref-type="bibr" rid="CR35">35</xref>]. The result agrees with the fact that PI3K-Akt is a signal transduction pathway, which helps in cell survival, growth, proliferation, cell migration and angiogenesis. The key proteins are PI3K (phosphatidylinositol 3-kinase) and AKT (Protein Kinase B). It is interesting to observe that CSNK1D perturbation positively regulates most of the components of PI3K-Akt signaling except it down-regulates the PTEN gene. The PTEN (phosphatase and tensin homolog) gene is a major antagonist of PI3K activity [<xref ref-type="bibr" rid="CR36">36</xref>]. It is a tumor suppressor gene and often mutated or lost in cancer cells. Additionally, CSNK1D perturbation also down-regulate YWHAZ gene, which is also a major regulator of apoptotic pathways and plays an important role in cell survival [<xref ref-type="bibr" rid="CR25">25</xref>, <xref ref-type="bibr" rid="CR37">37</xref>, <xref ref-type="bibr" rid="CR38">38</xref>]. Being a constituent of PI3K-Akt signaling, TSC2 gene also contributes to endocytosis, and when perturbed, down-regulates the PTEN gene. PML and SHH genes, which are associated with ER calcium ion homeostasis and endocytosis process respectively, along with CSNK1D gene, down-regulate HSP90AA2 (Heat shock protein 90kDa alpha (cytosolic), class A member 2) gene. HSP90AA2 is a heat shock gene, generally expressed to combat a stressful situation and whose protein product functions as chaperon by stabilizing new proteins to ensure correct folding [<xref ref-type="bibr" rid="CR39">39</xref>]. In summary, we observe that the perturbation in genes involved in protein phosphorylation, endocytosis, Golgi organization and calcium ion homeostasis in ER, have a stronger effect in the activation of PI3K-Akt signaling, and in the down regulation of PTEN, YWHAZ and HSP90AA2 genes which are important for the normal functioning of the cell.
<fig id="Fig6"><label>Fig. 6</label><caption><p>Regulatory interactions between PI3K-Akt signaling and intracellular protein transport. Genes enriched in PI3K-Akt signaling pathway are represented by diamond shape in pink color such as IL7R,TNN, ITGB4, CSF1R, STK11, ITGB1, ITGB7, FGF17, COL5A3, PDGFRA, PP2R3A, AKT1, HSP90AA, CCND1 and PTEN and YWHAZ. These genes are regulated by the perturbation in CSNK1D, TSC2, PML, SHH, KDR and SGK1 gene represented by oval shape in red color. Out of these perturbed genes, KDR, SGK1 and TSC2 also plays an important role in PI3K-Akt signaling; and they are represented by diamond shape and in red color. The edges with arrow signs represent the four fold increase in the expression of target/effected genes, while edges with dot represent the four fold down-regulation</p></caption><graphic xlink:href="12859_2018_2036_Fig6_HTML" id="MO6"/></fig></p><p>Similarly, we also observe the cross-talks between Ras signaling pathways and intracellular protein transport mechanism. The cross regulatory interactions between the components of Ras and intracellular protein transport pathways are depicted in resultant (Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref>). We observe constituent genes of Ras pathway such as CSF1R, PDGFRA, FGF17 [<xref ref-type="bibr" rid="CR40">40</xref>], FGFR2, FGFR4, RAP1A [<xref ref-type="bibr" rid="CR41">41</xref>], GRB2 [<xref ref-type="bibr" rid="CR42">42</xref>] and KDR are either regulated by the components of intracellular protein transport or they also regulate each other. CSF1R (Colony stimulating factor 1 receptor (CSF1R)) is a receptor for a cytokine called colony stimulating factor 1. PDGFRA (platelet-derived growth factor receptor A) encodes a typical receptor tyrosine kinase, which binds to platelets derived growth factors and plays an active role in initiating cell signaling pathways responsible for cellular growth and differentiation [<xref ref-type="bibr" rid="CR43">43</xref>&#x02013;<xref ref-type="bibr" rid="CR45">45</xref>]. Both of them are positively regulated by the perturbation in CSNK1D gene [<xref ref-type="bibr" rid="CR46">46</xref>]. The CSNK1D gene is involved in endocytosis, Golgi organization,positive regulation of protein phosphorylation,protein phosphorylation. Some of the components of Ras pathway such as KDR [<xref ref-type="bibr" rid="CR47">47</xref>], FGFR4 [<xref ref-type="bibr" rid="CR48">48</xref>], FGFR2 [<xref ref-type="bibr" rid="CR48">48</xref>, <xref ref-type="bibr" rid="CR49">49</xref>] are also involved in intracellular protein transport mechanism [<xref ref-type="bibr" rid="CR50">50</xref>]. KDR which is a type III receptor tyrosine kinase, also known as vascular endothelial growth factor receptor 2 (VEGFR-2), is also involved in positive regulation of protein phosphorylation [<xref ref-type="bibr" rid="CR50">50</xref>]. Perturbation in KDR, affects the ETS2 gene in Ras pathway. Fibroblast growth factor receptor 2 (FGFR2) and Fibroblast growth factor receptor 4 (FGFR4) are members of the fibroblast growth factor receptor family. These receptors signal by binding to their ligand and dimerisation and initiate a cascade of intracellular signals. These signals are involved in cell division, growth and differentiation [<xref ref-type="bibr" rid="CR51">51</xref>, <xref ref-type="bibr" rid="CR52">52</xref>]. Perturbation in FGFR2, down-regulates TMED10 and AKT1. TMED10 is involved in ER to Golgi vesicle-mediated transport, retrograde vesicle-mediated transport, Golgi to ER, Golgi organization. While AKT1 is a serine-threonine protein kinase, activation of this gene phosphorylates and inactivates components of the apoptotic machinery [<xref ref-type="bibr" rid="CR53">53</xref>&#x02013;<xref ref-type="bibr" rid="CR55">55</xref>]. Perturbation in FGFR4 gene, down-regulates VAMP7 [<xref ref-type="bibr" rid="CR56">56</xref>, <xref ref-type="bibr" rid="CR57">57</xref>] and RAP1A expression [<xref ref-type="bibr" rid="CR58">58</xref>], which are involved in ER to Golgi vesicle-mediated transport, endocytosis, vesicle-mediated transport and Ras Pathway respectively. The results of all the regulatory interactions and subsequent enriched pathways are provided in the Additional file&#x000a0;<xref rid="MOESM6" ref-type="media">6</xref>.
<fig id="Fig7"><label>Fig. 7</label><caption><p>Ras signaling cross-talk protein transport. The cross regulatory interactions between the components of Ras (represented by diamond shape and in pink color) and intracellular pathways represented by oval shape and in red color. Constituent genes of Ras pathway such as CSF1R, PDGFRA, FGF17, FGFR2, FGFR4, RAP1A, GRB2 and KDR are either regulated by the components of intracellular protein transport or they also regulate each other variably. Some of the components of Ras pathway such as KDR, FGFR4, FGFR2 are also involved in intracellular protein transport mechanism. The edges with arrow signs represent the four fold increase in the expression of target/effected genes, while edges with dot represent the four fold down-regulation</p></caption><graphic xlink:href="12859_2018_2036_Fig7_HTML" id="MO7"/></fig></p></sec></sec><sec id="Sec16" sec-type="conclusion"><title>Conclusions</title><p>In this work, we develop a computational integrated pipeline to analyze genes perturbation experimental data and uncover the regulatory interactions among genes. We use functional genomics and network biology approach to create a directed network, where nodes represent the perturbed and impacted genes, while direct edges represents the positive and negative regulatory effects of perturbation on its neighboring elements. We implemented this approach to infer regulatory cross-talk between signaling pathways and intracellular protein transport in MCF7 cell line. Our aim is to elucidate the regulatory connection between genes constituting signaling pathways such as PI3K-Akt, Ras, Rap1, calcium, JAK-STAT, EFGR and FGFR signaling and intracellular protein transport mechanism in MCF7 cell line. We focus on PI3k-Akt signaling and Ras pathway, to highlight some of their mutual key regulatory features. In our results, we find some interesting regulatory components of PI3k-AKT signaling with respect to Ras pathway as well as intracellular protein transport mechanism. From the literature, it is known that development of resistance to cancer therapy is an important clinical problem [<xref ref-type="bibr" rid="CR30">30</xref>]. Inactivation of apoptotic programme leads to drug resistance in tumor cells. This resistance is mainly supported by PI3K-Akt signaling and hence this signaling contributes to the resistance of cancer cell [<xref ref-type="bibr" rid="CR59">59</xref>, <xref ref-type="bibr" rid="CR60">60</xref>]. As it is known that Ras and calcium signaling activate the PI3K-Akt signaling in a cell, targeting the upstream and downstream signaling pathways with respect PI3K-Akt signaling is a feasible approach to procrastinate resistance in cancer cells. In future, we will hopefully extend this work and develop a methodology as well as computational integrated platform to construct an interaction network from perturbation data not only from one cell line but simultaneously from multiple tissue samples/cell lines, for the comparative analysis of putative regulatory interactions among genes in different experimental conditions.</p></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec17"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12859_2018_2036_MOESM1_ESM.zip"><label>Additional file 1</label><caption><p>Perturbed gene list. Complete list of perturbed genes. (ZIP 11 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12859_2018_2036_MOESM2_ESM.zip"><label>Additional file 2</label><caption><p>DAVID Enrichment analysis 591 genes. Gene ontology and pathway enrichment analysis of the complete list of perturbed genes. (ZIP 203 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12859_2018_2036_MOESM3_ESM.zip"><label>Additional file 3</label><caption><p>Node statistics table. Topological parameters of each nodes in the network. (ZIP 50 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12859_2018_2036_MOESM4_ESM.zip"><label>Additional file 4</label><caption><p>Network enrichment. Gene Ontology and Pathways enrichment analysis results for the complete regulatory interaction network from 591 gene perturbation expression data. (ZIP 41 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12859_2018_2036_MOESM5_ESM.zip"><label>Additional file 5</label><caption><p>Network functional modules enrichment. Module wise functional and pathways enrichment analysis of the network. (ZIP 131 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="12859_2018_2036_MOESM6_ESM.zip"><label>Additional file 6</label><caption><p>Enrichment terms for gene cross-talks. All regulatory interactions and enriched pathways for genes involved in cross-talk between intracellular protein transport and signaling pathway. (ZIP 100 kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><fn-group><fn><p><bold>Electronic supplementary material</bold></p><p>The online version of this article (10.1186/s12859-018-2036-2) contains supplementary material, which is available to authorized users.</p></fn></fn-group><ack><title>Acknowledgements</title><p>We would like to acknowledge the efforts of Mr Giuseppe Trerotola for the technical support, and Dr Gennaro Oliva for the deployment of hardware and software infrastructure used in the analysis.</p><sec id="d29e1593"><title>Funding</title><p>Publication of this article was sponsored by funding support from MIUR Interomics Flagship project, PON02-00612-3461281 and PON02-006193470457. Mario R. Guarracino work has been conducted at National Research University Higher School of Economics and supported by RSF grant 14-41-00039.</p></sec><sec id="d29e1598"><title>Availability of data and materials</title><p>The data are all publicly available. We will provide the python script to reconstruct regulatory interactions, on demand, and it is freely available.</p></sec><sec id="d29e1603"><title>About this supplement</title><p>This article has been published as part of <italic>BMC Bioinformatics</italic> Volume 19 Supplement 2, 2018: Proceedings of Bringing Maths to Life (BMTL) 2017. The full contents of the supplement are available online at <ext-link ext-link-type="uri" xlink:href="https://bmcbioinformatics.biomedcentral.com/articles/supplements/volume-19-supplement-2">https://bmcbioinformatics.biomedcentral.com/articles/supplements/volume-19-supplement-2</ext-link>.</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>KPT and MRG conceived the experiment(s), KPT and MP conducted the experiment(s), KPT and MP analyzed the results. KPT, MP and MRG wrote the manuscript. All of the authors have read and approve the manuscript.</p></notes><notes notes-type="COI-statement"><sec id="d29e1622"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec><sec id="d29e1627"><title>Consent for publication</title><p>Not applicable.</p></sec><sec id="d29e1632"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec id="d29e1637"><title>Publisher&#x02019;s Note</title><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Farhan</surname><given-names>H</given-names></name></person-group><article-title>Regulation of egfr surface levels by copii-dependent trafficking</article-title><source>J Cell Biol</source><year>2016</year><volume>215</volume><issue>4</issue><fpage>441</fpage><lpage>443</lpage><pub-id pub-id-type="doi">10.1083/jcb.201611014</pub-id><?supplied-pmid 27872251?><pub-id pub-id-type="pmid">27872251</pub-id></element-citation></ref><ref id="CR2"><label>2</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wlodkowic</surname><given-names>D</given-names></name><name><surname>Skommer</surname><given-names>J</given-names></name><name><surname>McGuinness</surname><given-names>D</given-names></name><name><surname>Hillier</surname><given-names>C</given-names></name><name><surname>Darzynkiewicz</surname><given-names>Z</given-names></name></person-group><article-title>Er&#x02013;golgi network&#x02014;a future target for anti-cancer therapy</article-title><source>Leuk Res</source><year>2009</year><volume>33</volume><issue>11</issue><fpage>1440</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1016/j.leukres.2009.05.025</pub-id><?supplied-pmid 19595459?><pub-id pub-id-type="pmid">19595459</pub-id></element-citation></ref><ref id="CR3"><label>3</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Serin</surname><given-names>EA</given-names></name><name><surname>Nijveen</surname><given-names>H</given-names></name><name><surname>Hilhorst</surname><given-names>HW</given-names></name><name><surname>Ligterink</surname><given-names>W</given-names></name></person-group><article-title>Learning from co-expression networks: possibilities and challenges</article-title><source>Front Plant Sci</source><year>2016</year><volume>7</volume><fpage>444</fpage><pub-id pub-id-type="doi">10.3389/fpls.2016.00444</pub-id><?supplied-pmid 27092161?><pub-id pub-id-type="pmid">27092161</pub-id></element-citation></ref><ref id="CR4"><label>4</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Usadel</surname><given-names>B</given-names></name><name><surname>Obayashi</surname><given-names>T</given-names></name><name><surname>Mutwil</surname><given-names>M</given-names></name><name><surname>Giorgi</surname><given-names>FM</given-names></name><name><surname>Bassel</surname><given-names>GW</given-names></name><name><surname>Tanimoto</surname><given-names>M</given-names></name><name><surname>Chow</surname><given-names>A</given-names></name><name><surname>Steinhauser</surname><given-names>D</given-names></name><name><surname>Persson</surname><given-names>S</given-names></name><name><surname>Provart</surname><given-names>NJ</given-names></name></person-group><article-title>Co-expression tools for plant biology: opportunities for hypothesis generation and caveats</article-title><source>Plant Cell Environ</source><year>2009</year><volume>32</volume><issue>12</issue><fpage>1633</fpage><lpage>51</lpage><pub-id pub-id-type="doi">10.1111/j.1365-3040.2009.02040.x</pub-id><?supplied-pmid 19712066?><pub-id pub-id-type="pmid">19712066</pub-id></element-citation></ref><ref id="CR5"><label>5</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stuart</surname><given-names>JM</given-names></name><name><surname>Segal</surname><given-names>E</given-names></name><name><surname>Koller</surname><given-names>D</given-names></name><name><surname>Kim</surname><given-names>SK</given-names></name></person-group><article-title>A gene-coexpression network for global discovery of conserved genetic modules</article-title><source>Science</source><year>2003</year><volume>302</volume><issue>5643</issue><fpage>249</fpage><lpage>55</lpage><pub-id pub-id-type="doi">10.1126/science.1087447</pub-id><?supplied-pmid 12934013?><pub-id pub-id-type="pmid">12934013</pub-id></element-citation></ref><ref id="CR6"><label>6</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vignes</surname><given-names>M</given-names></name><name><surname>Vandel</surname><given-names>J</given-names></name><name><surname>Allouche</surname><given-names>D</given-names></name><name><surname>Ramadan-Alban</surname><given-names>N</given-names></name><name><surname>Cierco-Ayrolles</surname><given-names>C</given-names></name><name><surname>Schiex</surname><given-names>T</given-names></name><name><surname>Mangin</surname><given-names>B</given-names></name><name><surname>De Givry</surname><given-names>S</given-names></name></person-group><article-title>Gene regulatory network reconstruction using bayesian networks, the dantzig selector, the lasso and their meta-analysis</article-title><source>PloS ONE</source><year>2011</year><volume>6</volume><issue>12</issue><fpage>29165</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0029165</pub-id></element-citation></ref><ref id="CR7"><label>7</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Marbach</surname><given-names>D</given-names></name><name><surname>Costello</surname><given-names>JC</given-names></name><name><surname>K&#x000fc;ffner</surname><given-names>R</given-names></name><name><surname>Vega</surname><given-names>NM</given-names></name><name><surname>Prill</surname><given-names>RJ</given-names></name><name><surname>Camacho</surname><given-names>DM</given-names></name><name><surname>Allison</surname><given-names>KR</given-names></name><name><surname>Kellis</surname><given-names>M</given-names></name><name><surname>Collins</surname><given-names>JJ</given-names></name><name><surname>Stolovitzky</surname><given-names>G</given-names></name><etal/></person-group><article-title>Wisdom of crowds for robust gene network inference</article-title><source>Nat Methods</source><year>2012</year><volume>9</volume><issue>8</issue><fpage>796</fpage><lpage>804</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2016</pub-id><?supplied-pmid 22796662?><pub-id pub-id-type="pmid">22796662</pub-id></element-citation></ref><ref id="CR8"><label>8</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wagner</surname><given-names>A</given-names></name></person-group><article-title>Estimating coarse gene network structure from large-scale gene perturbation data</article-title><source>Genome Res</source><year>2002</year><volume>12</volume><issue>2</issue><fpage>309</fpage><lpage>15</lpage><pub-id pub-id-type="doi">10.1101/gr.193902</pub-id><?supplied-pmid 11827950?><pub-id pub-id-type="pmid">11827950</pub-id></element-citation></ref><ref id="CR9"><label>9</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Piccirillo</surname><given-names>M</given-names></name><name><surname>Tripathi</surname><given-names>KP</given-names></name><name><surname>Chavan</surname><given-names>SG</given-names></name><name><surname>Varavallo</surname><given-names>A</given-names></name><name><surname>Parashuraman</surname><given-names>S</given-names></name><name><surname>Guarracino</surname><given-names>MR</given-names></name></person-group><article-title>Reconstructing a genetic network from gene perturbations in secretory pathway of cancer cell lines</article-title><source>Dynamics of Mathematical Models in Biology</source><year>2016</year><publisher-loc>Cham</publisher-loc><publisher-name>Springer</publisher-name></element-citation></ref><ref id="CR10"><label>10</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Chavan</surname><given-names>SG</given-names></name><name><surname>Tripathi</surname><given-names>KP</given-names></name><name><surname>Piccirilo</surname><given-names>M</given-names></name><name><surname>Roy</surname><given-names>PD</given-names></name><name><surname>Guarracino</surname><given-names>M</given-names></name><name><surname>Luini</surname><given-names>A</given-names></name><name><surname>Parashuraman</surname><given-names>S</given-names></name></person-group><article-title>Dissecting the functions of the secretory pathway by transcriptional profiling</article-title><source>Dynamics of Mathematical Models in Biology</source><year>2016</year><publisher-loc>Cham</publisher-loc><publisher-name>Springer</publisher-name></element-citation></ref><ref id="CR11"><label>11</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Tripathi</surname><given-names>KP</given-names></name><name><surname>Chavan</surname><given-names>SG</given-names></name><name><surname>Parashuraman</surname><given-names>S</given-names></name><name><surname>Piccirillo</surname><given-names>M</given-names></name><name><surname>Magliocca</surname><given-names>S</given-names></name><name><surname>Guarracino</surname><given-names>MR</given-names></name></person-group><article-title>Comparison of gene expression signature using rank based statistical inference</article-title><source>International Meeting on Computational Intelligence Methods for Bioinformatics and Biostatistics</source><year>2015</year><publisher-loc>Cham</publisher-loc><publisher-name>Springer</publisher-name></element-citation></ref><ref id="CR12"><label>12</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Colanzi</surname><given-names>A</given-names></name><name><surname>Suetterlin</surname><given-names>C</given-names></name><name><surname>Malhotra</surname><given-names>V</given-names></name></person-group><article-title>Cell-cycle-specific golgi fragmentation: how and why?</article-title><source>Curr Opin Cell Biol</source><year>2003</year><volume>15</volume><issue>4</issue><fpage>462</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1016/S0955-0674(03)00067-X</pub-id><?supplied-pmid 12892787?><pub-id pub-id-type="pmid">12892787</pub-id></element-citation></ref><ref id="CR13"><label>13</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>H-M</given-names></name><name><surname>Chen</surname><given-names>H</given-names></name><name><surname>Liu</surname><given-names>W</given-names></name><name><surname>Liu</surname><given-names>H</given-names></name><name><surname>Gong</surname><given-names>J</given-names></name><name><surname>Wang</surname><given-names>H</given-names></name><name><surname>Guo</surname><given-names>A-Y</given-names></name></person-group><article-title>Animaltfdb: a comprehensive animal transcription factor database</article-title><source>Nucleic Acids Res</source><year>2012</year><volume>40</volume><issue>D1</issue><fpage>144</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1093/nar/gkr965</pub-id></element-citation></ref><ref id="CR14"><label>14</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Blainey</surname><given-names>P</given-names></name><name><surname>Krzywinski</surname><given-names>M</given-names></name><name><surname>Altman</surname><given-names>N</given-names></name></person-group><article-title>Points of significance: replication</article-title><source>Nat Methods</source><year>2014</year><volume>11</volume><issue>9</issue><fpage>879</fpage><lpage>80</lpage><pub-id pub-id-type="doi">10.1038/nmeth.3091</pub-id><?supplied-pmid 25317452?><pub-id pub-id-type="pmid">25317452</pub-id></element-citation></ref><ref id="CR15"><label>15</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shannon</surname><given-names>P</given-names></name><name><surname>Markiel</surname><given-names>A</given-names></name><name><surname>Ozier</surname><given-names>O</given-names></name><name><surname>Baliga</surname><given-names>NS</given-names></name><name><surname>Wang</surname><given-names>JT</given-names></name><name><surname>Ramage</surname><given-names>D</given-names></name><name><surname>Amin</surname><given-names>N</given-names></name><name><surname>Schwikowski</surname><given-names>B</given-names></name><name><surname>Ideker</surname><given-names>T</given-names></name></person-group><article-title>Cytoscape: a software environment for integrated models of biomolecular interaction networks</article-title><source>Genome Res</source><year>2003</year><volume>13</volume><issue>11</issue><fpage>2498</fpage><lpage>504</lpage><pub-id pub-id-type="doi">10.1101/gr.1239303</pub-id><?supplied-pmid 14597658?><pub-id pub-id-type="pmid">14597658</pub-id></element-citation></ref><ref id="CR16"><label>16</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Assenov</surname><given-names>Y</given-names></name><name><surname>Ram&#x000ed;rez</surname><given-names>F</given-names></name><name><surname>Schelhorn</surname><given-names>S-E</given-names></name><name><surname>Lengauer</surname><given-names>T</given-names></name><name><surname>Albrecht</surname><given-names>M</given-names></name></person-group><article-title>Computing topological parameters of biological networks</article-title><source>Bioinformatics</source><year>2008</year><volume>24</volume><issue>2</issue><fpage>282</fpage><lpage>4</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btm554</pub-id><?supplied-pmid 18006545?><pub-id pub-id-type="pmid">18006545</pub-id></element-citation></ref><ref id="CR17"><label>17</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ideker</surname><given-names>T</given-names></name><name><surname>Ozier</surname><given-names>O</given-names></name><name><surname>Schwikowski</surname><given-names>B</given-names></name><name><surname>Siegel</surname><given-names>AF</given-names></name></person-group><article-title>Discovering regulatory and signalling circuits in molecular interaction networks</article-title><source>Bioinformatics</source><year>2002</year><volume>18</volume><issue>suppl 1</issue><fpage>233</fpage><lpage>40</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/18.suppl_1.S233</pub-id></element-citation></ref><ref id="CR18"><label>18</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>G</given-names></name><name><surname>Dawson</surname><given-names>E</given-names></name><name><surname>Duong</surname><given-names>A</given-names></name><name><surname>Haw</surname><given-names>R</given-names></name><name><surname>Stein</surname><given-names>L</given-names></name></person-group><article-title>Reactomefiviz: a cytoscape app for pathway and network-based data analysis</article-title><source>F1000Research</source><year>2014</year><volume>3</volume><fpage>146</fpage><?supplied-pmid 25309732?><pub-id pub-id-type="pmid">25309732</pub-id></element-citation></ref><ref id="CR19"><label>19</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Croft</surname><given-names>D</given-names></name><name><surname>O&#x02019;Kelly</surname><given-names>G</given-names></name><name><surname>Wu</surname><given-names>G</given-names></name><name><surname>Haw</surname><given-names>R</given-names></name><name><surname>Gillespie</surname><given-names>M</given-names></name><name><surname>Matthews</surname><given-names>L</given-names></name><name><surname>Caudy</surname><given-names>M</given-names></name><name><surname>Garapati</surname><given-names>P</given-names></name><name><surname>Gopinath</surname><given-names>G</given-names></name><name><surname>Jassal</surname><given-names>B</given-names></name><etal/></person-group><article-title>Reactome: a database of reactions, pathways and biological processes</article-title><source>Nucleic Acids Res</source><year>2011</year><volume>39</volume><issue>suppl_1</issue><fpage>691</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1093/nar/gkq1018</pub-id></element-citation></ref><ref id="CR20"><label>20</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tripathi</surname><given-names>KP</given-names></name><name><surname>Evangelista</surname><given-names>D</given-names></name><name><surname>Zuccaro</surname><given-names>A</given-names></name><name><surname>Guarracino</surname><given-names>MR</given-names></name></person-group><article-title>Transcriptator: an automated computational pipeline to annotate assembled reads and identify non coding rna</article-title><source>PloS ONE</source><year>2015</year><volume>10</volume><issue>11</issue><fpage>0140268</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0140268</pub-id></element-citation></ref><ref id="CR21"><label>21</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Maiorano</surname><given-names>F</given-names></name><name><surname>Ambrosino</surname><given-names>L</given-names></name><name><surname>Guarracino</surname><given-names>MR</given-names></name></person-group><article-title>The metabox library: building metabolic networks from kegg database</article-title><source>International Conference on Bioinformatics and Biomedical Engineering</source><year>2015</year><publisher-loc>Cham</publisher-loc><publisher-name>Springer</publisher-name></element-citation></ref><ref id="CR22"><label>22</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Newman</surname><given-names>ME</given-names></name></person-group><article-title>Modularity and community structure in networks</article-title><source>Proc Natl Acad Sci</source><year>2006</year><volume>103</volume><issue>23</issue><fpage>8577</fpage><lpage>82</lpage><pub-id pub-id-type="doi">10.1073/pnas.**********</pub-id><?supplied-pmid 16723398?><pub-id pub-id-type="pmid">16723398</pub-id></element-citation></ref><ref id="CR23"><label>23</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Montojo</surname><given-names>J</given-names></name><name><surname>Zuberi</surname><given-names>K</given-names></name><name><surname>Rodriguez</surname><given-names>H</given-names></name><name><surname>Bader</surname><given-names>GD</given-names></name><name><surname>Morris</surname><given-names>Q</given-names></name></person-group><article-title>Genemania: Fast gene network construction and function prediction for cytoscape</article-title><source>F1000Research</source><year>2014</year><volume>3</volume><fpage>153</fpage><?supplied-pmid 25254104?><pub-id pub-id-type="pmid">25254104</pub-id></element-citation></ref><ref id="CR24"><label>24</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cui</surname><given-names>C</given-names></name><name><surname>Merritt</surname><given-names>R</given-names></name><name><surname>Fu</surname><given-names>L</given-names></name><name><surname>Pan</surname><given-names>Z</given-names></name></person-group><article-title>Targeting calcium signaling in cancer therapy</article-title><source>Acta Pharm Sin B</source><year>2016</year><volume>7</volume><issue>1</issue><fpage>3</fpage><lpage>17</lpage><pub-id pub-id-type="doi">10.1016/j.apsb.2016.11.001</pub-id><?supplied-pmid 28119804?><pub-id pub-id-type="pmid">28119804</pub-id></element-citation></ref><ref id="CR25"><label>25</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nishimura</surname><given-names>Y</given-names></name><name><surname>Komatsu</surname><given-names>S</given-names></name><name><surname>Ichikawa</surname><given-names>D</given-names></name><name><surname>Nagata</surname><given-names>H</given-names></name><name><surname>Hirajima</surname><given-names>S</given-names></name><name><surname>Takeshita</surname><given-names>H</given-names></name><name><surname>Kawaguchi</surname><given-names>T</given-names></name><name><surname>Arita</surname><given-names>T</given-names></name><name><surname>Konishi</surname><given-names>H</given-names></name><name><surname>Kashimoto</surname><given-names>K</given-names></name><etal/></person-group><article-title>Overexpression of ywhaz relates to tumor cell proliferation and malignant outcome of gastric carcinoma</article-title><source>Br J Cancer</source><year>2013</year><volume>108</volume><issue>6</issue><fpage>1324</fpage><lpage>31</lpage><pub-id pub-id-type="doi">10.1038/bjc.2013.65</pub-id><?supplied-pmid 23422756?><pub-id pub-id-type="pmid">23422756</pub-id></element-citation></ref><ref id="CR26"><label>26</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chu</surname><given-names>EC</given-names></name><name><surname>Tarnawski</surname><given-names>AS</given-names></name></person-group><article-title>Pten regulatory functions in tumor suppression and cell biology</article-title><source>Med Sci Monit</source><year>2004</year><volume>10</volume><issue>10</issue><fpage>235</fpage><lpage>41</lpage></element-citation></ref><ref id="CR27"><label>27</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dan</surname><given-names>HC</given-names></name><name><surname>Sun</surname><given-names>M</given-names></name><name><surname>Yang</surname><given-names>L</given-names></name><name><surname>Feldman</surname><given-names>RI</given-names></name><name><surname>Sui</surname><given-names>X-M</given-names></name><name><surname>Ou</surname><given-names>CC</given-names></name><name><surname>Nellist</surname><given-names>M</given-names></name><name><surname>Yeung</surname><given-names>RS</given-names></name><name><surname>Halley</surname><given-names>DJ</given-names></name><name><surname>Nicosia</surname><given-names>SV</given-names></name><etal/></person-group><article-title>Phosphatidylinositol 3-kinase/akt pathway regulates tuberous sclerosis tumor suppressor complex by phosphorylation of tuberin</article-title><source>J Biol Chem</source><year>2002</year><volume>277</volume><issue>38</issue><fpage>35364</fpage><lpage>70</lpage><pub-id pub-id-type="doi">10.1074/jbc.M205838200</pub-id><?supplied-pmid 12167664?><pub-id pub-id-type="pmid">12167664</pub-id></element-citation></ref><ref id="CR28"><label>28</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>L&#x000f8;kken</surname><given-names>N</given-names></name><name><surname>Born</surname><given-names>AP</given-names></name><name><surname>Duno</surname><given-names>M</given-names></name><name><surname>Vissing</surname><given-names>J</given-names></name></person-group><article-title>Lama2-related myopathy: Frequency among congenital and limb-girdle muscular dystrophies</article-title><source>Muscle Nerve</source><year>2015</year><volume>52</volume><issue>4</issue><fpage>547</fpage><lpage>53</lpage><pub-id pub-id-type="doi">10.1002/mus.24588</pub-id><?supplied-pmid 25663498?><pub-id pub-id-type="pmid">25663498</pub-id></element-citation></ref><ref id="CR29"><label>29</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Elsheikh</surname><given-names>S</given-names></name><name><surname>Green</surname><given-names>AR</given-names></name><name><surname>Aleskandarany</surname><given-names>MA</given-names></name><name><surname>Grainge</surname><given-names>M</given-names></name><name><surname>Paish</surname><given-names>CE</given-names></name><name><surname>Lambros</surname><given-names>MB</given-names></name><name><surname>Reis-Filho</surname><given-names>JS</given-names></name><name><surname>Ellis</surname><given-names>IO</given-names></name></person-group><article-title>Ccnd1 amplification and cyclin d1 expression in breast cancer and their relation with proteomic subgroups and patient outcome</article-title><source>Breast Cancer Res Treat</source><year>2008</year><volume>109</volume><issue>2</issue><fpage>325</fpage><lpage>35</lpage><pub-id pub-id-type="doi">10.1007/s10549-007-9659-8</pub-id><?supplied-pmid 17653856?><pub-id pub-id-type="pmid">17653856</pub-id></element-citation></ref><ref id="CR30"><label>30</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vara</surname><given-names>J&#x000c1;F</given-names></name><name><surname>Casado</surname><given-names>E</given-names></name><name><surname>de Castro</surname><given-names>J</given-names></name><name><surname>Cejas</surname><given-names>P</given-names></name><name><surname>Belda-Iniesta</surname><given-names>C</given-names></name><name><surname>Gonz&#x000e1;lez-Bar&#x000f3;n</surname><given-names>M</given-names></name></person-group><article-title>Pi3k/akt signalling pathway and cancer</article-title><source>Cancer Treat Rev</source><year>2004</year><volume>30</volume><issue>2</issue><fpage>193</fpage><lpage>204</lpage><pub-id pub-id-type="doi">10.1016/j.ctrv.2003.07.007</pub-id><pub-id pub-id-type="pmid">15023437</pub-id></element-citation></ref><ref id="CR31"><label>31</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Badache</surname><given-names>A</given-names></name><name><surname>Gon&#x000e7;alves</surname><given-names>A</given-names></name></person-group><article-title>The erbb2 signaling network as a target for breast cancer therapy</article-title><source>J Mammary Gland Biol Neoplasia</source><year>2006</year><volume>11</volume><issue>1</issue><fpage>13</fpage><lpage>25</lpage><pub-id pub-id-type="doi">10.1007/s10911-006-9009-1</pub-id><?supplied-pmid 16947083?><pub-id pub-id-type="pmid">16947083</pub-id></element-citation></ref><ref id="CR32"><label>32</label><mixed-citation publication-type="other">Alfonso A, Payne GS, Donaldson J. Trafficking inside cells: Pathways, mechanisms and regulation. Springer Science + Business Media. 2010;:1&#x02013;14. ISBN 9780387938769.</mixed-citation></ref><ref id="CR33"><label>33</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>H</given-names></name><name><surname>Cicchetti</surname><given-names>G</given-names></name><name><surname>Onda</surname><given-names>H</given-names></name><name><surname>Koon</surname><given-names>HB</given-names></name><name><surname>Asrican</surname><given-names>K</given-names></name><name><surname>Bajraszewski</surname><given-names>N</given-names></name><name><surname>Vazquez</surname><given-names>F</given-names></name><name><surname>Carpenter</surname><given-names>CL</given-names></name><name><surname>Kwiatkowski</surname><given-names>DJ</given-names></name></person-group><article-title>Loss of tsc1/tsc2 activates mtor and disrupts pi3k-akt signaling through downregulation of pdgfr</article-title><source>J Clin Investig</source><year>2003</year><volume>112</volume><issue>8</issue><fpage>1223</fpage><pub-id pub-id-type="doi">10.1172/JCI200317222</pub-id><?supplied-pmid 14561707?><pub-id pub-id-type="pmid">14561707</pub-id></element-citation></ref><ref id="CR34"><label>34</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Orlacchio</surname><given-names>A</given-names></name><name><surname>Ranieri</surname><given-names>M</given-names></name><name><surname>Brave</surname><given-names>M</given-names></name><name><surname>Arciuch</surname><given-names>VA</given-names></name><name><surname>Forde</surname><given-names>T</given-names></name><name><surname>De Martino</surname><given-names>D</given-names></name><name><surname>Anderson</surname><given-names>KE</given-names></name><name><surname>Hawkins</surname><given-names>P</given-names></name><name><surname>Di Cristofano</surname><given-names>A</given-names></name></person-group><article-title>Sgk1 is a critical component of an akt-independent pathway essential for pi3k-mediated tumor development and maintenance</article-title><source>Cancer Res</source><year>2017</year><volume>77</volume><issue>24</issue><fpage>6914</fpage><lpage>6926</lpage><pub-id pub-id-type="doi">10.1158/0008-5472.CAN-17-2105</pub-id><?supplied-pmid 29055016?><pub-id pub-id-type="pmid">29055016</pub-id></element-citation></ref><ref id="CR35"><label>35</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pelkmans</surname><given-names>L</given-names></name><name><surname>Fava</surname><given-names>E</given-names></name><name><surname>Grabner</surname><given-names>H</given-names></name><name><surname>Hannus</surname><given-names>M</given-names></name><name><surname>Habermann</surname><given-names>B</given-names></name><name><surname>Krausz</surname><given-names>E</given-names></name><name><surname>Zerial</surname><given-names>M</given-names></name></person-group><article-title>Genome-wide analysis of human kinases in clathrin-and caveolae/raft-mediated endocytosis</article-title><source>Nature</source><year>2005</year><volume>436</volume><issue>7047</issue><fpage>78</fpage><lpage>86</lpage><pub-id pub-id-type="doi">10.1038/nature03571</pub-id><?supplied-pmid 15889048?><pub-id pub-id-type="pmid">15889048</pub-id></element-citation></ref><ref id="CR36"><label>36</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chalhoub</surname><given-names>N</given-names></name><name><surname>Baker</surname><given-names>SJ</given-names></name></person-group><article-title>Pten and the pi3-kinase pathway in cancer</article-title><source>Annu Rev Pathological Mech Dis</source><year>2009</year><volume>4</volume><fpage>127</fpage><lpage>50</lpage><pub-id pub-id-type="doi">10.1146/annurev.pathol.4.110807.092311</pub-id></element-citation></ref><ref id="CR37"><label>37</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Weerasekara</surname><given-names>VK</given-names></name><name><surname>Panek</surname><given-names>DJ</given-names></name><name><surname>Broadbent</surname><given-names>DG</given-names></name><name><surname>Mortenson</surname><given-names>JB</given-names></name><name><surname>Mathis</surname><given-names>AD</given-names></name><name><surname>Logan</surname><given-names>GN</given-names></name><name><surname>Prince</surname><given-names>JT</given-names></name><name><surname>Thomson</surname><given-names>DM</given-names></name><name><surname>Thompson</surname><given-names>JW</given-names></name><name><surname>Andersen</surname><given-names>JL</given-names></name></person-group><article-title>Metabolic-stress-induced rearrangement of the 14-3-3 <italic>&#x003b6;</italic> interactome promotes autophagy via a ulk1-and ampk-regulated 14-3-3 <italic>&#x003b6;</italic> interaction with phosphorylated atg9</article-title><source>Mol Cell Biol</source><year>2014</year><volume>34</volume><issue>24</issue><fpage>4379</fpage><lpage>88</lpage><pub-id pub-id-type="doi">10.1128/MCB.00740-14</pub-id><?supplied-pmid 25266655?><pub-id pub-id-type="pmid">25266655</pub-id></element-citation></ref><ref id="CR38"><label>38</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Matta</surname><given-names>A</given-names></name><name><surname>Siu</surname><given-names>KM</given-names></name><name><surname>Ralhan</surname><given-names>R</given-names></name></person-group><article-title>14-3-3 zeta as novel molecular target for cancer therapy</article-title><source>Expert Opin Ther Targets</source><year>2012</year><volume>16</volume><issue>5</issue><fpage>515</fpage><lpage>23</lpage><pub-id pub-id-type="doi">10.1517/14728222.2012.668185</pub-id><?supplied-pmid 22512284?><pub-id pub-id-type="pmid">22512284</pub-id></element-citation></ref><ref id="CR39"><label>39</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Taipale</surname><given-names>M</given-names></name><name><surname>Jarosz</surname><given-names>DF</given-names></name><name><surname>Lindquist</surname><given-names>S</given-names></name></person-group><article-title>Hsp90 at the hub of protein homeostasis: emerging mechanistic insights</article-title><source>Nat Rev Mol Cell Biol</source><year>2010</year><volume>11</volume><issue>7</issue><fpage>515</fpage><lpage>28</lpage><pub-id pub-id-type="doi">10.1038/nrm2918</pub-id><?supplied-pmid 20531426?><pub-id pub-id-type="pmid">20531426</pub-id></element-citation></ref><ref id="CR40"><label>40</label><mixed-citation publication-type="other">Guillemot F, Zimmer C. From cradle to grave: The multiple roles of fibroblast growth factors in neural development. Neuron. 2011; 71(4):574&#x02013;88. 10.1016/j.neuron.2011.08.002.</mixed-citation></ref><ref id="CR41"><label>41</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mizuta</surname><given-names>K</given-names></name><name><surname>Tsujii</surname><given-names>R</given-names></name><name><surname>Warner</surname><given-names>JR</given-names></name><name><surname>Nishiyama</surname><given-names>M</given-names></name></person-group><article-title>The c-terminal silencing domain of rap1p is essential for the repression of ribosomal protein genes in response to a defect in the secretory pathway</article-title><source>Nucleic Acids Res</source><year>1998</year><volume>26</volume><issue>4</issue><fpage>1063</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1093/nar/26.4.1063</pub-id><?supplied-pmid 9461469?><pub-id pub-id-type="pmid">9461469</pub-id></element-citation></ref><ref id="CR42"><label>42</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yamazaki</surname><given-names>T</given-names></name><name><surname>Zaal</surname><given-names>K</given-names></name><name><surname>Hailey</surname><given-names>D</given-names></name><name><surname>Presley</surname><given-names>J</given-names></name><name><surname>Lippincott-Schwartz</surname><given-names>J</given-names></name><name><surname>Samelson</surname><given-names>LE</given-names></name></person-group><article-title>Role of grb2 in egf-stimulated egfr internalization</article-title><source>J Cell Sci</source><year>2002</year><volume>115</volume><issue>9</issue><fpage>1791</fpage><lpage>802</lpage><?supplied-pmid 11956311?><pub-id pub-id-type="pmid">11956311</pub-id></element-citation></ref><ref id="CR43"><label>43</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Montmayeur</surname><given-names>J-P</given-names></name><name><surname>Valius</surname><given-names>M</given-names></name><name><surname>Vandenheede</surname><given-names>J</given-names></name><name><surname>Kazlauskas</surname><given-names>A</given-names></name></person-group><article-title>The platelet-derived growth factor <italic>&#x003b2;</italic> receptor triggers multiple cytoplasmic signaling cascades that arrive at the nucleus as distinguishable inputs</article-title><source>J Biol Chem</source><year>1997</year><volume>272</volume><issue>51</issue><fpage>32670</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1074/jbc.272.51.32670</pub-id><?supplied-pmid 9405485?><pub-id pub-id-type="pmid">9405485</pub-id></element-citation></ref><ref id="CR44"><label>44</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Valius</surname><given-names>M</given-names></name><name><surname>Kazlauskas</surname><given-names>A</given-names></name></person-group><article-title>Phospholipase c- <italic>&#x003b3;</italic>1 and phosphatidylinositol 3 kinase are the downstream mediators of the pdgf receptor&#x02019;s mitogenic signal</article-title><source>Cell</source><year>1993</year><volume>73</volume><issue>2</issue><fpage>321</fpage><lpage>34</lpage><pub-id pub-id-type="doi">10.1016/0092-8674(93)90232-F</pub-id><?supplied-pmid 7682895?><pub-id pub-id-type="pmid">7682895</pub-id></element-citation></ref><ref id="CR45"><label>45</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>E</given-names></name><name><surname>Palmer</surname><given-names>N</given-names></name><name><surname>Tian</surname><given-names>Z</given-names></name><name><surname>Moseman</surname><given-names>AP</given-names></name><name><surname>Galdzicki</surname><given-names>M</given-names></name><name><surname>Wang</surname><given-names>X</given-names></name><name><surname>Berger</surname><given-names>B</given-names></name><name><surname>Zhang</surname><given-names>H</given-names></name><name><surname>Kohane</surname><given-names>IS</given-names></name></person-group><article-title>Comprehensive dissection of pdgf-pdgfr signaling pathways in pdgfr genetically defined cells</article-title><source>PloS ONE</source><year>2008</year><volume>3</volume><issue>11</issue><fpage>3794</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0003794</pub-id></element-citation></ref><ref id="CR46"><label>46</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schittek</surname><given-names>B</given-names></name><name><surname>Sinnberg</surname><given-names>T</given-names></name></person-group><article-title>Biological functions of casein kinase 1 isoforms and putative roles in tumorigenesis</article-title><source>Mol Cancer</source><year>2014</year><volume>13</volume><issue>1</issue><fpage>231</fpage><pub-id pub-id-type="doi">10.1186/1476-4598-13-231</pub-id><?supplied-pmid 25306547?><pub-id pub-id-type="pmid">25306547</pub-id></element-citation></ref><ref id="CR47"><label>47</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Manickam</surname><given-names>V</given-names></name><name><surname>Tiwari</surname><given-names>A</given-names></name><name><surname>Jung</surname><given-names>J-J</given-names></name><name><surname>Bhattacharya</surname><given-names>R</given-names></name><name><surname>Goel</surname><given-names>A</given-names></name><name><surname>Mukhopadhyay</surname><given-names>D</given-names></name><name><surname>Choudhury</surname><given-names>A</given-names></name></person-group><article-title>Regulation of vascular endothelial growth factor receptor 2 trafficking and angiogenesis by golgi localized t-snare syntaxin 6</article-title><source>Blood</source><year>2011</year><volume>117</volume><issue>4</issue><fpage>1425</fpage><lpage>35</lpage><pub-id pub-id-type="doi">10.1182/blood-2010-06-291690</pub-id><?supplied-pmid 21063020?><pub-id pub-id-type="pmid">21063020</pub-id></element-citation></ref><ref id="CR48"><label>48</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Katz</surname><given-names>M</given-names></name><name><surname>Amit</surname><given-names>I</given-names></name><name><surname>Yarden</surname><given-names>Y</given-names></name></person-group><article-title>Regulation of mapks by growth factors and receptor tyrosine kinases</article-title><source>Biochim Biophys Acta (BBA) - Mol Cell Res</source><year>2007</year><volume>1773</volume><issue>8</issue><fpage>1161</fpage><lpage>76</lpage><pub-id pub-id-type="doi">10.1016/j.bbamcr.2007.01.002</pub-id></element-citation></ref><ref id="CR49"><label>49</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ahmad</surname><given-names>I</given-names></name><name><surname>Iwata</surname><given-names>T</given-names></name><name><surname>Leung</surname><given-names>HY</given-names></name></person-group><article-title>Mechanisms of fgfr-mediated carcinogenesis</article-title><source>Biochim Biophys Acta (BBA) - Mol Cell Res</source><year>2012</year><volume>1823</volume><issue>4</issue><fpage>850</fpage><lpage>60</lpage><pub-id pub-id-type="doi">10.1016/j.bbamcr.2012.01.004</pub-id></element-citation></ref><ref id="CR50"><label>50</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Miaczynska</surname><given-names>M</given-names></name></person-group><article-title>Effects of membrane trafficking on signaling by receptor tyrosine kinases</article-title><source>Cold Spring Harb Perspect Biol</source><year>2013</year><volume>5</volume><issue>11</issue><fpage>009035</fpage><pub-id pub-id-type="doi">10.1101/cshperspect.a009035</pub-id></element-citation></ref><ref id="CR51"><label>51</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cha</surname><given-names>JY</given-names></name><name><surname>Maddileti</surname><given-names>S</given-names></name><name><surname>Mitin</surname><given-names>N</given-names></name><name><surname>Harden</surname><given-names>TK</given-names></name><name><surname>Der</surname><given-names>CJ</given-names></name></person-group><article-title>Aberrant receptor internalization and enhanced frs2-dependent signaling contribute to the transforming activity of the fibroblast growth factor receptor 2 iiib c3 isoform</article-title><source>J Biol Chem</source><year>2009</year><volume>284</volume><issue>10</issue><fpage>6227</fpage><lpage>40</lpage><pub-id pub-id-type="doi">10.1074/jbc.M803998200</pub-id><?supplied-pmid 19103595?><pub-id pub-id-type="pmid">19103595</pub-id></element-citation></ref><ref id="CR52"><label>52</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wei</surname><given-names>W</given-names></name><name><surname>Liu</surname><given-names>W</given-names></name><name><surname>Cassol</surname><given-names>CA</given-names></name><name><surname>Zheng</surname><given-names>W</given-names></name><name><surname>Asa</surname><given-names>SL</given-names></name><name><surname>Ezzat</surname><given-names>S</given-names></name></person-group><article-title>The breast cancer susceptibility gene product fibroblast growth factor receptor 2 serves as a scaffold for regulation of nf- <italic>&#x003ba;</italic>b signaling</article-title><source>Mol Cell Biol</source><year>2012</year><volume>32</volume><issue>22</issue><fpage>4662</fpage><lpage>73</lpage><pub-id pub-id-type="doi">10.1128/MCB.00935-12</pub-id><?supplied-pmid 22988296?><pub-id pub-id-type="pmid">22988296</pub-id></element-citation></ref><ref id="CR53"><label>53</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Riggio</surname><given-names>M</given-names></name><name><surname>Perrone</surname><given-names>MC</given-names></name><name><surname>Polo</surname><given-names>ML</given-names></name><name><surname>Rodriguez</surname><given-names>MJ</given-names></name><name><surname>May</surname><given-names>M</given-names></name><name><surname>Abba</surname><given-names>M</given-names></name><name><surname>Lanari</surname><given-names>C</given-names></name><name><surname>Novaro</surname><given-names>V</given-names></name></person-group><article-title>Akt1 and akt2 isoforms play distinct roles during breast cancer progression through the regulation of specific downstream proteins</article-title><source>Sci Reports</source><year>2017</year><volume>7</volume><fpage>44244</fpage><pub-id pub-id-type="doi">10.1038/srep44244</pub-id></element-citation></ref><ref id="CR54"><label>54</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Priore</surname><given-names>R</given-names></name><name><surname>Dailey</surname><given-names>L</given-names></name><name><surname>Basilico</surname><given-names>C</given-names></name></person-group><article-title>Downregulation of akt activity contributes to the growth arrest induced by fgf in chondrocytes</article-title><source>J Cell Physiol</source><year>2006</year><volume>207</volume><issue>3</issue><fpage>800</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1002/jcp.20620</pub-id><?supplied-pmid 16523491?><pub-id pub-id-type="pmid">16523491</pub-id></element-citation></ref><ref id="CR55"><label>55</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>X</given-names></name><name><surname>Shi</surname><given-names>Y</given-names></name><name><surname>Han</surname><given-names>EK-H</given-names></name><name><surname>Chen</surname><given-names>Z</given-names></name><name><surname>Rosenberg</surname><given-names>SH</given-names></name><name><surname>Giranda</surname><given-names>VL</given-names></name><name><surname>Luo</surname><given-names>Y</given-names></name><name><surname>Ng</surname><given-names>S-C</given-names></name></person-group><article-title>Downregulation of akt1 inhibits anchorage-independent cell growth and induces apoptosis in cancer cells</article-title><source>Neoplasia</source><year>2001</year><volume>3</volume><issue>4</issue><fpage>278</fpage><lpage>86</lpage><pub-id pub-id-type="doi">10.1038/sj.neo.7900163</pub-id><?supplied-pmid 11571628?><pub-id pub-id-type="pmid">11571628</pub-id></element-citation></ref><ref id="CR56"><label>56</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ward</surname><given-names>DM</given-names></name><name><surname>Pevsner</surname><given-names>J</given-names></name><name><surname>Scullion</surname><given-names>MA</given-names></name><name><surname>Vaughn</surname><given-names>M</given-names></name><name><surname>Kaplan</surname><given-names>J</given-names></name></person-group><article-title>Syntaxin 7 and vamp-7 are solublen-ethylmaleimide&#x02013;sensitive factor attachment protein receptors required for late endosome&#x02013;lysosome and homotypic lysosome fusion in alveolar macrophages</article-title><source>Mol Biol Cell</source><year>2000</year><volume>11</volume><issue>7</issue><fpage>2327</fpage><lpage>33</lpage><pub-id pub-id-type="doi">10.1091/mbc.11.7.2327</pub-id><?supplied-pmid 10888671?><pub-id pub-id-type="pmid">10888671</pub-id></element-citation></ref><ref id="CR57"><label>57</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Marcet-Palacios</surname><given-names>M</given-names></name><name><surname>Odemuyiwa</surname><given-names>SO</given-names></name><name><surname>Coughlin</surname><given-names>JJ</given-names></name><name><surname>Garofoli</surname><given-names>D</given-names></name><name><surname>Ewen</surname><given-names>C</given-names></name><name><surname>Davidson</surname><given-names>CE</given-names></name><name><surname>Ghaffari</surname><given-names>M</given-names></name><name><surname>Kane</surname><given-names>KP</given-names></name><name><surname>Lacy</surname><given-names>P</given-names></name><name><surname>Logan</surname><given-names>MR</given-names></name><etal/></person-group><article-title>Vesicle-associated membrane protein 7 (vamp-7) is essential for target cell killing in a natural killer cell line</article-title><source>Biochem Biophys Res Commun</source><year>2008</year><volume>366</volume><issue>3</issue><fpage>617</fpage><lpage>23</lpage><pub-id pub-id-type="doi">10.1016/j.bbrc.2007.11.079</pub-id><?supplied-pmid 18042464?><pub-id pub-id-type="pmid">18042464</pub-id></element-citation></ref><ref id="CR58"><label>58</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guo</surname><given-names>X-X</given-names></name><name><surname>An</surname><given-names>S</given-names></name><name><surname>Yang</surname><given-names>Y</given-names></name><name><surname>Liu</surname><given-names>Y</given-names></name><name><surname>Hao</surname><given-names>Q</given-names></name><name><surname>Xu</surname><given-names>T-R</given-names></name></person-group><article-title>Rap-interacting proteins are key players in the rap symphony orchestra</article-title><source>Cell Physiol Biochem</source><year>2016</year><volume>39</volume><issue>1</issue><fpage>137</fpage><lpage>56</lpage><pub-id pub-id-type="doi">10.1159/000445612</pub-id><?supplied-pmid 27322838?><pub-id pub-id-type="pmid">27322838</pub-id></element-citation></ref><ref id="CR59"><label>59</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cai</surname><given-names>Y</given-names></name><name><surname>Tan</surname><given-names>X</given-names></name><name><surname>Liu</surname><given-names>J</given-names></name><name><surname>Shen</surname><given-names>Y</given-names></name><name><surname>Wu</surname><given-names>D</given-names></name><name><surname>Ren</surname><given-names>M</given-names></name><name><surname>Huang</surname><given-names>P</given-names></name><name><surname>Yu</surname><given-names>D</given-names></name></person-group><article-title>Inhibition of pi3k/akt/mtor signaling pathway enhances the sensitivity of the skov3/ddp ovarian cancer cell line to cisplatin in vitro</article-title><source>Chin J Cancer Res</source><year>2014</year><volume>26</volume><issue>5</issue><fpage>564</fpage><?supplied-pmid 25400422?><pub-id pub-id-type="pmid">25400422</pub-id></element-citation></ref><ref id="CR60"><label>60</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>K-F</given-names></name><name><surname>Chen</surname><given-names>H-L</given-names></name><name><surname>Tai</surname><given-names>W-T</given-names></name><name><surname>Feng</surname><given-names>W-C</given-names></name><name><surname>Hsu</surname><given-names>C-H</given-names></name><name><surname>Chen</surname><given-names>P-J</given-names></name><name><surname>Cheng</surname><given-names>A-L</given-names></name></person-group><article-title>Activation of phosphatidylinositol 3-kinase/akt signaling pathway mediates acquired resistance to sorafenib in hepatocellular carcinoma cells</article-title><source>J Pharmacol Exp Ther</source><year>2011</year><volume>337</volume><issue>1</issue><fpage>155</fpage><lpage>61</lpage><pub-id pub-id-type="doi">10.1124/jpet.110.175786</pub-id><?supplied-pmid 21205925?><pub-id pub-id-type="pmid">21205925</pub-id></element-citation></ref></ref-list></back></article>