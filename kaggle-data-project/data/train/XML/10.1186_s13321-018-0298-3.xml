<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">J Cheminform</journal-id><journal-id journal-id-type="iso-abbrev">J Cheminform</journal-id><journal-title-group><journal-title>Journal of Cheminformatics</journal-title></journal-title-group><issn pub-type="epub">1758-2946</issn><publisher><publisher-name>Springer International Publishing</publisher-name><publisher-loc>Cham</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6115327</article-id><article-id pub-id-type="publisher-id">298</article-id><article-id pub-id-type="doi">10.1186/s13321-018-0298-3</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>The influence of solid state information and descriptor selection on statistical models of temperature dependent aqueous solubility</article-title></title-group><contrib-group><contrib contrib-type="author"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0001-7648-8645</contrib-id><name><surname>Marchese Robinson</surname><given-names>Richard L.</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Roberts</surname><given-names>Kevin J.</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Martin</surname><given-names>Elaine B.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1"><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1936 8403</institution-id><institution-id institution-id-type="GRID">grid.9909.9</institution-id><institution>School of Chemical and Process Engineering, </institution><institution>University of Leeds, </institution></institution-wrap>Leeds, LS2 9JT UK </aff></contrib-group><pub-date pub-type="epub"><day>29</day><month>8</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>29</day><month>8</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>10</volume><elocation-id>44</elocation-id><history><date date-type="received"><day>20</day><month>2</month><year>2018</year></date><date date-type="accepted"><day>17</day><month>8</month><year>2018</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><p id="Par1">Predicting the equilibrium solubility of organic, crystalline materials at all relevant temperatures is crucial to the digital design of manufacturing unit operations in the chemical industries. The work reported in our current publication builds upon the limited number of recently published quantitative structure&#x02013;property relationship studies which modelled the temperature dependence of aqueous solubility. One set of models was built to directly predict temperature dependent solubility, including for materials with no solubility data at any temperature. We propose that a modified cross-validation protocol is required to evaluate these models. Another set of models was built to predict the related enthalpy of solution term, which can be used to estimate solubility at one temperature based upon solubility data for the same material at another temperature. We investigated whether various kinds of solid state descriptors improved the models obtained with a variety of molecular descriptor combinations: lattice energies or 3D descriptors calculated from crystal structures or melting point data. We found that none of these greatly improved the best direct predictions of temperature dependent solubility or the related enthalpy of solution endpoint. This finding is surprising because the importance of the solid state contribution to both endpoints is clear. We suggest our findings may, in part, reflect limitations in the descriptors calculated from crystal structures and, more generally, the limited availability of polymorph specific data. We present curated temperature dependent solubility and enthalpy of solution datasets, integrated with molecular and crystal structures, for future investigations.
<graphic position="anchor" xlink:href="13321_2018_298_Figa_HTML" id="MO30"/>
</p><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s13321-018-0298-3) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Quantitative structure&#x02013;property relationships</kwd><kwd>Solubility</kwd><kwd>Temperature dependent solubility data</kwd><kwd>Enthalpy of solution</kwd><kwd>Machine learning</kwd><kwd>Random forest</kwd><kwd>Multiple linear regression</kwd><kwd>Feature selection</kwd><kwd>Crystal structure</kwd><kwd>Lattice energy</kwd><kwd>Melting point</kwd></kwd-group><funding-group><award-group><funding-source><institution>United Kingdom Advanced Manufacturing Supply Chain Initiative</institution></funding-source><award-id>&#x02018;Advanced Digital Design of Pharmaceutical Therapeutics&#x02019; (ADDoPT) project</award-id><principal-award-recipient><name><surname>Roberts</surname><given-names>Kevin J.</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Introduction</title><p id="Par7">A plethora of computational approaches currently exist to predict the equilibrium solubility of organic chemicals, as well as related thermodynamic terms such as the free energy of solvation [<xref ref-type="bibr" rid="CR1">1</xref>]. These approaches include data driven statistical modelling approaches, such as quantitative structure&#x02013;property relationships (QSPRs), as well as various kinds of physics based models. The focus of much of this work is on the prediction of aqueous solubility at a single temperature, or a nominal single value around typical ambient temperatures, to support estimation of product performance, e.g. in terms of the bioavailability of active pharmaceutical ingredients (APIs) or the environmental fate of pollutants [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR3">3</xref>].</p><p id="Par8">In contrast, we are interested in predicting the temperature dependence of equilibrium solubility. Predictions of the solubility of relevant organic crystalline materials, in all relevant solvents, across a range of temperatures are crucial for digital design of unit operations in pharmaceutical manufacturing. For example, they could support the design of cooling crystallization operations [<xref ref-type="bibr" rid="CR4">4</xref>]. Determination of aqueous solubility at elevated temperatures may also be relevant to the design of wet granulation processes [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR6">6</xref>].</p><p id="Par9">It is important to note that various kinds of physics based approaches to modelling solution thermodynamics are capable of capturing temperature dependence, including in complex mixtures [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR7">7</xref>&#x02013;<xref ref-type="bibr" rid="CR9">9</xref>]. If combined with estimations of solid state thermodynamic contributions, these might be applied to predict the temperature dependence of solubility [<xref ref-type="bibr" rid="CR10">10</xref>&#x02013;<xref ref-type="bibr" rid="CR13">13</xref>].</p><p id="Par10">However, physics based models are not necessarily more accurate and may be more computationally expensive than QSPR approaches [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR14">14</xref>]. Interestingly, however, few QSPR models have been developed to capture the temperature dependence of solubility. Some QSPR models were reported to predict the solubilities of organic chemicals, across a range of temperatures, in supercritical carbon dioxide for small (less than 30 chemicals), non-diverse datasets [<xref ref-type="bibr" rid="CR15">15</xref>, <xref ref-type="bibr" rid="CR16">16</xref>]. More recently, two QSPR studies sought to capture the temperature dependence of aqueous solubility for large, chemically diverse datasets [<xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR17">17</xref>].</p><p id="Par11">Specifically, Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] developed QSPR models for the standard enthalpy of solution, for the unionized solute, in water. Under certain assumptions, the variation in solubility with temperature may be expressed in terms of the van&#x02019;t Hoff relationship in Eq.&#x000a0;(<xref rid="Equ1" ref-type="">1</xref>), where <italic>S</italic> is the solubility (in molar concentration units), <italic>T</italic> is the temperature (in Kelvin), <italic>R</italic> is the molar gas constant and <inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta H_{sol}^{0} $$\end{document}</tex-math><mml:math id="M2"><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msubsup><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">sol</mml:mi></mml:mrow><mml:mn>0</mml:mn></mml:msubsup></mml:mrow></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq1.gif"/></alternatives></inline-formula> is the standard enthalpy of solution [<xref ref-type="bibr" rid="CR17">17</xref>&#x02013;<xref ref-type="bibr" rid="CR20">20</xref>]. If it is assumed that the standard enthalpy of solution is effectively constant over the temperature range of interest, Eq.&#x000a0;(<xref rid="Equ1" ref-type="">1</xref>) can be used to interpolate solubility values between temperatures or extrapolate solubility data from one temperature to another [<xref ref-type="bibr" rid="CR17">17</xref>].<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \log_{10} S = \frac{{ - \Delta H_{sol}^{0} }}{{\ln \left( {10} \right)RT}} + constant $$\end{document}</tex-math><mml:math id="M4" display="block"><mml:mrow><mml:msub><mml:mo>log</mml:mo><mml:mn>10</mml:mn></mml:msub><mml:mi>S</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mo>-</mml:mo><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msubsup><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">sol</mml:mi></mml:mrow><mml:mn>0</mml:mn></mml:msubsup></mml:mrow><mml:mrow><mml:mo>ln</mml:mo><mml:mfenced close=")" open="("><mml:mn>10</mml:mn></mml:mfenced><mml:mi>R</mml:mi><mml:mi>T</mml:mi></mml:mrow></mml:mfrac><mml:mo>+</mml:mo><mml:mi>c</mml:mi><mml:mi>o</mml:mi><mml:mi>n</mml:mi><mml:mi>s</mml:mi><mml:mi>t</mml:mi><mml:mi>a</mml:mi><mml:mi>n</mml:mi><mml:mi>t</mml:mi></mml:mrow></mml:math><graphic xlink:href="13321_2018_298_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula>The models for the enthalpy of solution developed by Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] were based on different combinations of molecular descriptors and melting point values and built using multiple linear regression (MLR) [<xref ref-type="bibr" rid="CR21">21</xref>], recursive partition tree [<xref ref-type="bibr" rid="CR22">22</xref>] and random forest [<xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR24">24</xref>]. The melting points were measured or predicted from a molecular descriptors based model [<xref ref-type="bibr" rid="CR25">25</xref>].</p><p id="Par12">In contrast, Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] built a model for directly predicting aqueous solubility at a specified temperature. Their predictions were based on molecular descriptors and a descriptor derived from experimental temperature, with random forest used to train the model.</p><p id="Par13">In the work reported in our current article, we extended the work of Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] and Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] as follows. Firstly, we investigated the effect of incorporating crystallographic information, in the form of lattice energies or 3D descriptors calculated from an experimental crystal structure, into the models. Secondly, we compared models for the enthalpy of solution based on molecular descriptors with or without melting point values and examined the effect of including melting point values into direct predictions of temperature dependent solubility. In both respects, this means our work is a contribution to the wider debate in the recent literature regarding the importance of explicitly capturing solid state contributions in QSPR models of solubility and whether the availability of crystallographic or melting point information is essential to achieve this [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR26">26</xref>&#x02013;<xref ref-type="bibr" rid="CR30">30</xref>]. Indeed, it has recently been suggested that the major source of error in QSPR prediction of solubility is the failure of molecular descriptors to fully capture solid state contributions [<xref ref-type="bibr" rid="CR28">28</xref>]. Thirdly, we considered a larger variety of molecular descriptor permutations, with or without the explicit solid state contribution descriptors, including the application of a feature selection algorithm to produce parsimonious models from high dimensional descriptor sets. Finally, we introduced a novel pseudo-cross-validation protocol for evaluating direct models of temperature dependent solubility. This novel validation protocol allowed us to investigate potential optimistic bias when validating those models.</p></sec><sec id="Sec2"><title>Methods and data</title><p id="Par14">For brevity, the essential points are provided below and further details are provided, under corresponding sub-headings, in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.</p><sec id="Sec3"><title>Solubility data curation</title><p id="Par15">Electronic datasets were curated for two endpoints related to temperature dependent solubility: enthalpy of solution values and temperature specific solubility values. Enthalpy of solution data (in kJ/mol) were curated from the publication of Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] and temperature dependent solubility data (log<sub>10</sub>[molar concentration]) were curated from the publication of Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>].</p><p id="Par16">Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] reported enthalpy of solution values derived from temperature dependent intrinsic solubility values via van&#x02019;t Hoff analysis. (Intrinsic solubility refers to the solubility of the unionized solute [<xref ref-type="bibr" rid="CR1">1</xref>]. Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] estimated the intrinsic solubility values from experimental values reported in various literature studies.) Avdeef also presented curated enthalpy of solution values obtained from direct calorimetric measurements, which were considered more reliable [<xref ref-type="bibr" rid="CR17">17</xref>]. Here, it has been assumed that all curated enthalpy of solution values closely corresponded to the standard enthalpy of solution, such that they could be used via Eq.&#x000a0;(<xref rid="Equ1" ref-type="">1</xref>) to interpolate or extrapolate intrinsic solubility data between temperatures.</p><p id="Par17">As well as curating the endpoint values, we curated the corresponding metadata, including chemical names (or CAS numbers) identifying the molecular species and corresponding polymorph metadata, where this was reported. This included curating the data quality assessments made by Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>]. An overview of this curation process is provided in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>.<fig id="Fig1"><label>Fig.&#x000a0;1</label><caption><p>An overview of the curation of endpoint data and associated metadata for endpoints related to temperature dependent aqueous solubility which was carried out for our article. These endpoints were the enthalpy of solution and temperature specific solubility measurements for datasets curated starting from the work of Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] and Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] respectively. The descriptions on the right hand side of this image refer to the curated datasets we prepared, starting from the information reported in these earlier studies and based on cross-referencing against other references where necessary, from which datasets for QSPR modelling were derived. Full details of the curation process, including explanations of how these curated datasets differed from those reported in the literature, are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref>. See the sections &#x0201c;Solubility data curation&#x0201d; and &#x0201c;Comparison to the literature&#x0201d; therein</p></caption><graphic xlink:href="13321_2018_298_Fig1_HTML" id="MO3"/></fig>
</p><p id="Par18">It should be noted that the &#x0201c;Avdeef (2015) derived dataset&#x0201d; and &#x0201c;Klimenko et al. (2016) derived dataset&#x0201d; labels (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>) refer to the datasets curated in this work into the electronic template, starting from the work of Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] and Klimenko et al. respectively [<xref ref-type="bibr" rid="CR14">14</xref>], where differences in the datasets arose during the curation process. One key difference between our versions of these datasets and those reported in these earlier studies is that we filtered dataset entries where there was no evidence that the enthalpy of solution or solubility data corresponded to dissolution from the solid state.</p></sec><sec id="Sec4"><title>Integration with molecular structures</title><p id="Par19">In the first instance, SMILES representations of molecular structures were retrieved via querying the following online resources: the Chemical Identifier Resolver service [<xref ref-type="bibr" rid="CR31">31</xref>], ChemSpider [<xref ref-type="bibr" rid="CR32">32</xref>] and PubChem [<xref ref-type="bibr" rid="CR33">33</xref>, <xref ref-type="bibr" rid="CR34">34</xref>]. For those scenarios where no, or inconsistent, molecular structures were retrieved, other references were consulted to determine the molecular structures.</p></sec><sec id="Sec5"><title>Integration with crystal structures</title><p id="Par20">Where possible, Cambridge Structural Database (CSD) refcodes were obtained for each combination of molecular structure identifier and polymorph description (i.e. each material), each refcode denoting a crystal structure [<xref ref-type="bibr" rid="CR35">35</xref>]. Only a small proportion (&#x0003c;&#x02009;3%) of solubility or enthalpy of solution data points were associated with a description of the corresponding polymorphic form in the Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>]. or Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] derived datasets respectively, i.e. the polymorph description was typically blank. Hence, in the majority of cases, only a possible match could be determined based upon cross-referencing the molecular identifiers (names and CAS numbers) and molecular structures associated with the data points against the CSD. Nonetheless, where polymorph information was available in the dataset and CSD for provisional matches, conflicting polymorph descriptions were manually identified and the corresponding matches deleted. In keeping with literature precedence, all multiple matches remaining were filtered to only keep the putative lowest energy structure, based upon calculated lattice energy [<xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR30">30</xref>].</p></sec><sec id="Sec6"><title>Calculation of lattice energies</title><p id="Par21">Lattice energies were calculated from the available crystal structures, using the COMPASS force field [<xref ref-type="bibr" rid="CR36">36</xref>&#x02013;<xref ref-type="bibr" rid="CR38">38</xref>], and used as a descriptor of solid state contribution to the modelled endpoints. This is justified by the fact that solubility can be related to the standard Gibbs free energy change, comprising enthalpic and entropic contributions, upon moving from the solid state to the solution phase [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR4">4</xref>]. In turn, this may be decomposed into the free energy change of sublimation (breaking of the crystal lattice to form a gaseous phase) and solvation (transfer from the gas phase to the solution phase), i.e. hydration in the case of an aqueous solution [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR29">29</xref>]. Hence, the enthalpy of solution may be decomposed into the sublimation enthalpy and the solvation enthalpy. The lattice energy is a contribution to the sublimation enthalpy. It is defined as the energy change upon forming the crystal lattice from infinitely separated gas phase molecules [<xref ref-type="bibr" rid="CR29">29</xref>]. Under certain assumptions, the enthalpy of sublimation may be related to the lattice energy as per Eq.&#x000a0;(<xref rid="Equ2" ref-type="">2</xref>) [<xref ref-type="bibr" rid="CR29">29</xref>]. In Eq.&#x000a0;(<xref rid="Equ2" ref-type="">2</xref>), <inline-formula id="IEq2"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta H_{sub} $$\end{document}</tex-math><mml:math id="M6"><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">sub</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq2.gif"/></alternatives></inline-formula> represents the enthalpy of sublimation, <inline-formula id="IEq3"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ E_{latt} $$\end{document}</tex-math><mml:math id="M8"><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi mathvariant="italic">latt</mml:mi></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq3.gif"/></alternatives></inline-formula> the lattice energy, <inline-formula id="IEq4"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ R $$\end{document}</tex-math><mml:math id="M10"><mml:mi>R</mml:mi></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq4.gif"/></alternatives></inline-formula> the gas constant and <inline-formula id="IEq5"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ T $$\end{document}</tex-math><mml:math id="M12"><mml:mi>T</mml:mi></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq5.gif"/></alternatives></inline-formula> the temperature in Kelvin.<disp-formula id="Equ2"><label>2</label><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta H_{sub} = - E_{latt} - 2RT $$\end{document}</tex-math><mml:math id="M14" display="block"><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">sub</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mo>-</mml:mo><mml:msub><mml:mi>E</mml:mi><mml:mrow><mml:mi mathvariant="italic">latt</mml:mi></mml:mrow></mml:msub><mml:mo>-</mml:mo><mml:mn>2</mml:mn><mml:mi>R</mml:mi><mml:mi>T</mml:mi></mml:mrow></mml:math><graphic xlink:href="13321_2018_298_Article_Equ2.gif" position="anchor"/></alternatives></disp-formula>
</p></sec><sec id="Sec7"><title>Validation of lattice energies</title><p id="Par22">The calculated lattice energies were compared to the experimental estimates of lattice energies, obtained from experimental sublimation enthalpies via Eq.&#x000a0;(<xref rid="Equ2" ref-type="">2</xref>) and assuming a constant temperature of 298 Kelvin, for a subset of the SUB-48 dataset from McDonagh et al. [<xref ref-type="bibr" rid="CR29">29</xref>]. (See &#x0201c;Filtering of SUB-48 Dataset&#x0201d; in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.) In keeping with the solubility and enthalpy of solution datasets, this dataset also comprised a set of single component crystals, was a mixture of general organic and pharmaceutical API small molecules and was filtered in keeping with the crystal structure selection criteria applied when integrating the QSPR datasets with crystal structures.</p></sec><sec id="Sec8"><title>Preparation of molecular structures for descriptor calculations</title><p id="Par23">Prior to calculating 2D molecular descriptors, all molecular structures were standardized and filtered. Prior to calculating 3D molecular descriptors, from the conformer generator structure but not the crystal structure, similar standardization was applied to the structures retained for the QSPR ready datasets, yet stereochemistry was retained prior to conformer generation.</p></sec><sec id="Sec9"><title>Calculation of 2D molecular descriptors</title><p id="Par24">The choice of 2D molecular descriptors was based upon the different permutations considered by Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] and Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>]. Where possible, we sought to calculate the same subsets of descriptors as per these previous studies, and to consider the same combinations of these subsets, as well as the combined pool of all molecular descriptors. Each of the different subsets is denoted by a label explained in Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table S1 and the combinations of 2D molecular descriptors evaluated are enumerated, therein, using these labels. These labels are also used in the file names of the versions of the QSPR ready datasets (see Table <xref rid="Tab1" ref-type="table">1</xref>) provided in Additional File <xref rid="MOESM2" ref-type="media">2</xref>, to denote the 2D molecular descriptors incorporated into the applicable combination of the available descriptors.</p></sec><sec id="Sec10"><title>Calculation of crystal structure based 3D molecular descriptors</title><p id="Par25">In addition to employing calculated lattice energies as a descriptor, the value of crystallographic information was evaluated via computing 3D molecular descriptors from the molecular structure found in the crystal. Specifically, charged partial surface area (CPSA) descriptors, representing the charge distribution at the molecular surface [<xref ref-type="bibr" rid="CR39">39</xref>&#x02013;<xref ref-type="bibr" rid="CR41">41</xref>], were calculated using Mordred [<xref ref-type="bibr" rid="CR42">42</xref>, <xref ref-type="bibr" rid="CR43">43</xref>]. These may partially capture intermolecular interactions in the solid state. Whereas the calculated lattice energies and experimental melting point data explicitly convey information about the solid state contribution, these descriptors may&#x02014;in part&#x02014;implicitly represent this information. However, if the solution state structures are not wholly different, they might partially capture molecular interactions associated with non-solid state contributions. Moreover, these descriptors may also be calculated for 3D molecular structures estimated from the available molecular information. Hence, in order to assess whether these descriptors added value due to their having been computed from the crystal structure, corresponding models were built using CPSA descriptors calculated from the 3D molecular structure derived from the originally curated SMILES using the ETKDG conformer generator algorithm [<xref ref-type="bibr" rid="CR44">44</xref>, <xref ref-type="bibr" rid="CR45">45</xref>] and UFF force-field [<xref ref-type="bibr" rid="CR46">46</xref>] geometry refinement. These descriptors were only calculated for those dataset entries which could be integrated with crystal structures.</p></sec><sec id="Sec11"><title>Temperature descriptor</title><p id="Par26">For a given material, assuming the standard enthalpy of solution may be approximated as a constant over the relevant temperature range, as well as other assumptions, the logarithm of solubility may be linearly related to (1/T), where T is the temperature in Kelvin [c.f. Eq.&#x000a0;(<xref rid="Equ1" ref-type="">1</xref>)] [<xref ref-type="bibr" rid="CR17">17</xref>&#x02013;<xref ref-type="bibr" rid="CR20">20</xref>]. Hence, as temperature dependent solubility in log<sub>10</sub>[molar concentration] units was modelled for the Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>]. derived dataset, the experimental temperature values were transformed to (1/T) to use as a descriptor. N.B. It should be noted that Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] proposed a more complicated temperature descriptor. However, for simplicity, and due to the grounding of the (1/T) dependence, under certain assumptions [<xref ref-type="bibr" rid="CR17">17</xref>&#x02013;<xref ref-type="bibr" rid="CR20">20</xref>], in fundamental thermodynamics, we chose to use (1/T) as the descriptor.</p></sec><sec id="Sec12"><title>Melting point descriptor</title><p id="Par27">Experimental melting point data were used as a descriptor for all datasets. The data retrieved do not necessarily correspond to the polymorph for which enthalpy of solution or solubility data were modelled.</p></sec><sec id="Sec13"><title>QSPR ready datasets</title><p id="Par28">Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> summarizes the QSPR ready datasets which were used for the evaluation of modelling approaches investigated in our work. A summary of the derivation of these QSPR ready datasets is provided in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>. These datasets were derived from the curated datasets summarized in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>, following integration with structural information, standardizing molecular structures, and calculating descriptors. For the enthalpy of solution datasets, data points noted to be low quality by Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] were also filtered. The derived dataset matched instance IDs to an endpoint value and a vector of descriptors.<table-wrap id="Tab1"><label>Table&#x000a0;1</label><caption><p>QSPR ready datasets</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Dataset name</th><th align="left">Instance identifier<sup>a</sup></th><th align="left">Extra filtering?</th><th align="left">No. instances</th><th align="left">Total no. descriptors<sup>b</sup></th><th align="left">Endpoint</th><th align="left">Source<sup>c</sup></th><th align="left">Integrated with crystal structures?</th></tr></thead><tbody><tr><td align="left">Avdeef_ExDPs_CS_False</td><td align="left">[name]_[CAS no.]_[polymorph description]</td><td align="left">Low quality data points removed [<xref ref-type="bibr" rid="CR17">17</xref>]</td><td char="." align="char">364</td><td char="." align="char">4776</td><td align="left" rowspan="2">Enthalpy of solution</td><td align="left" rowspan="2">Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] derived dataset</td><td align="left" rowspan="3">No</td></tr><tr><td align="left">Avdeef_ExDPs_Cal_CS_False</td><td align="left">[name]_[CAS no.]_[polymorph description]</td><td align="left">Only calorimetry data points retained [<xref ref-type="bibr" rid="CR17">17</xref>]</td><td char="." align="char">50</td><td char="." align="char">4776</td></tr><tr><td align="left">Klimenko_CS_False</td><td align="left">[name]_[CAS no.]_[polymorph description]_[temperature value]</td><td align="left">No</td><td char="." align="char">882</td><td char="." align="char">3764</td><td align="left">Solubility at some defined temperature</td><td align="left">Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] derived dataset</td></tr><tr><td align="left">Avdeef_ExDPs_CS_True</td><td align="left">[name]_[CAS no.]_[refcode]</td><td align="left">Low quality data points removed [<xref ref-type="bibr" rid="CR17">17</xref>]</td><td char="." align="char">169</td><td char="." align="char">4820</td><td align="left" rowspan="2">Enthalpy of solution</td><td align="left" rowspan="2">Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] derived dataset</td><td align="left" rowspan="3">Yes</td></tr><tr><td align="left">Avdeef_ExDPs_Cal_CS_True</td><td align="left">[name]_[CAS no.]_[refcode]</td><td align="left">Only calorimetry data points retained [<xref ref-type="bibr" rid="CR17">17</xref>]</td><td char="." align="char">30</td><td char="." align="char">4820</td></tr><tr><td align="left">Klimenko_CS_True</td><td align="left">[name]_[CAS no.]_[refcode]_[temperature value]</td><td align="left">No</td><td char="." align="char">530</td><td char="." align="char">3808</td><td align="left">Solubility at some defined temperature</td><td align="left">Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] derived dataset</td></tr></tbody></table><table-wrap-foot><p><sup>a</sup>The [name] (or [CAS no.]) and/or [polymorph description] could be &#x0201c;none&#x0201d;, denoting the absence of the relevant information</p><p><sup>b</sup>This denotes the complete set of all 2D molecular, temperature (for the solubility datasets), melting point and, for crystal structure integrated datasets, lattice energy and 3D descriptors calculated for instances in this dataset. Different subsets were considered for different models, as described under &#x0201c;<xref rid="Sec14" ref-type="sec">Descriptor combinations investigated</xref>&#x0201d;</p><p><sup>c</sup>See Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref></p></table-wrap-foot></table-wrap>
<fig id="Fig2"><label>Fig.&#x000a0;2</label><caption><p>A summary of the steps taken to transform the curated experimental endpoint datasets (see Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>) into the QSPR-ready datasets used for modelling studies (see Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). As is explained in the text of Additional file <xref rid="MOESM1" ref-type="media">1</xref>, some of these steps were carried out iteratively</p></caption><graphic xlink:href="13321_2018_298_Fig2_HTML" id="MO5"/></fig>
</p><p id="Par29">The instances in these datasets, i.e. the unique identifiers associated with endpoint values and corresponding descriptor vectors used for modelling, represent different organic crystalline materials, typically corresponding to different molecular chemicals, and&#x02014;for the Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] derived QSPR datasets&#x02014;different temperatures. Where multiple endpoint data points were associated with a given instance identifier, the arithmetic mean endpoint value was assigned. Hence, each instance identifier only occurred once.</p></sec><sec id="Sec14"><title>Descriptor combinations investigated</title><p id="Par30">For the QSPR ready datasets which were not integrated with crystallographic information, all previously described combinations of 2D molecular descriptors were considered, with or without the melting point descriptor and in combination with the temperature descriptor for the Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] derived datasets. For the QSPR ready datasets integrated with crystallographic information, the same combinations of descriptors were considered, with or without the calculated lattice energy. In addition, a new set of descriptor combinations were evaluated for these datasets based upon the 3D descriptors calculated from the corresponding crystal structure, or the conformer generator structure. These descriptor combinations were obtained via adding the 3D descriptors, to all descriptor combinations involving the combined set of 2D molecular descriptors, or substituting the combined set of 2D molecular descriptors for the 3D descriptors. Finally, for the high dimensional descriptor combinations containing the complete set of 2D molecular descriptors, but not the 3D descriptors, feature selection was applied to yield another set of descriptor combinations. Feature selection was not applied to the sets containing the 3D descriptors, as initial results obtained with the 2D molecular descriptors were worse when feature selection was applied.</p></sec><sec id="Sec15"><title>Feature selection</title><p id="Par31">The feature selection algorithm and rationale is documented in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.</p></sec><sec id="Sec16"><title>Descriptor scaling</title><p id="Par32">All descriptor values were range scaled to lie between 0 and 1, using the training set ranges, prior to modelling.</p></sec><sec id="Sec17"><title>Machine learning</title><p id="Par33">Models were built using Multiple Linear Regression (MLR) [<xref ref-type="bibr" rid="CR21">21</xref>] and the non-linear random forest regression (RFR) [<xref ref-type="bibr" rid="CR23">23</xref>, <xref ref-type="bibr" rid="CR24">24</xref>] algorithms. For all RFR models, the model was built five times using a different random number generator seed and each tree was grown on a training set sample without replacement, rather than bootstrapping. All cross-validation statistics were averaged (arithmetic mean) across these seeds, as were all descriptor importance values.</p></sec><sec id="Sec18"><title>Validation statistics</title><p id="Par34">Model performance was assessed in terms of the coefficient of determination (<italic>R</italic><sup>2</sup>) and the root mean squared error (RMSE) [<xref ref-type="bibr" rid="CR47">47</xref>]. (Definitions are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.) For the comparison of models on the same test set, these statistics are redundant. However, as <italic>R</italic><sup>2</sup> is a composite of the mean squared error and the variance for the test set endpoint values, propagation of errors necessarily makes it less robust. Hence, for comparisons on the same dataset, using the same cross-validation folds, the mean RMSE values were compared. However, RMSE estimates are not comparable for different endpoints or test sets where the range in endpoint values differs [<xref ref-type="bibr" rid="CR47">47</xref>]. Hence, for comparisons across datasets, or on the same dataset using different cross-validation folds, the mean <italic>R</italic><sup>2</sup> values were compared.</p></sec><sec id="Sec19"><title>Cross-validation protocols</title><p id="Par35">Initially, a &#x0201c;vanilla&#x0201d; cross-validation protocol was applied: R repetitions of stratified K-fold cross-validation (R&#x02009;=&#x02009;5, K&#x02009;=&#x02009;5). In addition, results for a novel &#x0201c;pseudo cross-validation&#x0201d; protocol are reported: the &#x0201c;remove temperature&#x0201d; protocol, labelled the &#x0201c;CV&#x02009;=&#x02009;rt&#x0201d; protocol for brevity.</p><p id="Par36">The application of the CV&#x02009;=&#x02009;rt protocol ensured that solubility values for the same organic material, measured at different temperatures, could not be included in the corresponding training and test set. The motivation for introducing this protocol was to assess whether simply applying a &#x0201c;vanilla&#x0201d; cross-validation protocol could give optimistically biased results, when applied to temperature dependent data&#x02014;where the dataset instances could correspond to the same material, yet with the endpoint value measured at a different temperature. (Hence, this protocol was only applicable to the datasets derived from Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>], where the endpoint was temperature dependent solubility.) The difference between the &#x0201c;vanilla&#x0201d; cross-validation protocol (CV&#x02009;=&#x02009;v) and the novel pseudo-cross-validation protocol (CV&#x02009;=&#x02009;rt) is illustrated by Figs.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> and <xref rid="Fig4" ref-type="fig">4</xref> respectively.<fig id="Fig3"><label>Fig.&#x000a0;3</label><caption><p>The application of a standard, or &#x0201c;vanilla&#x0201d;, cross-validation protocol (fivefold CV) to temperature dependent endpoint data, where the instance IDs comprise the [MATERIAL IDENTITY]_[TEMPERATURE]. As shown here, instances corresponding to the same material, yet with endpoint values measured at different temperatures, might be assigned to different folds. (For this hypothetical dataset, this means [M1]_[T&#x02009;=&#x02009;25] and [M1]_[T&#x02009;=&#x02009;30] were assigned to folds F1 and F2 respectively.) Since each fold is used, in turn, as the test set, with the remaining data being used as the training set, this allows the same material to appear in corresponding training and test sets, when the corresponding endpoint values were measured at different temperatures</p></caption><graphic xlink:href="13321_2018_298_Fig3_HTML" id="MO6"/></fig>
<fig id="Fig4"><label>Fig.&#x000a0;4</label><caption><p>The application of the CV&#x02009;=&#x02009;rt pseudo-cross-validation protocol to the same hypothetical dataset shown in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>. The first step entails the transformation of <underline>1</underline> into <underline>2</underline>, via removing the temperature [T&#x02009;=&#x02009;x] suffix from the ID, deleting all but one occurrence of each truncated ID and assigning this truncated ID the arithmetic mean endpoint value associated with all corresponding original IDs. The transformation of <underline>2</underline> into <underline>3</underline> just entails the application of the standard cross-validation protocol. (In the current case, the nominal endpoint values were required as stratified sampling, based on the distribution of endpoint values, was employed for cross-validation.) Finally, the original dataset IDs are assigned the folds associated with their truncated IDs, in <underline>3</underline>, to give the CV&#x02009;=&#x02009;rt folds <underline>4</underline>. This ensures that instance IDs corresponding to the same material, yet with endpoint IDs measured at different temperatures, are always assigned to the same fold. (For this hypothetical dataset, this means [M1]_[T&#x02009;=&#x02009;25] and [M1]_[T&#x02009;=&#x02009;30] were both assigned to fold F1.) This ensures they can never be placed in corresponding training and test sets</p></caption><graphic xlink:href="13321_2018_298_Fig4_HTML" id="MO7"/></fig>
</p></sec><sec id="Sec20"><title>Statistical significance of differences in cross-validated results</title><p id="Par37">Pairwise differences in arithmetic mean validation statistics, from cross-validation, were evaluated for statistical significance for the key scenarios of interest. These key scenarios were pairwise comparisons of all corresponding modelling protocols, or cross-validation protocols, differing only with respect to the following: (1) whether the lattice energy descriptor was included; (2) whether the melting point descriptor was included; (3) whether the crystal structure based 3D descriptors, as opposed to the conformer generator based 3D descriptors, were used; (4) whether feature selection was applied; (5) whether the CV&#x02009;=&#x02009;v or CV&#x02009;=&#x02009;rt cross-validation protocol was applied. For scenarios (1&#x02013;4), <italic>p</italic> values were computed based on the paired RMSE values. For scenario (5), <italic>p</italic> values were computed based on the <italic>R</italic><sup>2</sup> values.</p><p id="Par38">Statistical significance was assessed via calculating approximate <italic>p</italic> values which were then adjusted, separately for each key scenario, to account for the multiple comparisons made. All references to statistically significant results refer to adjusted <italic>p</italic> values&#x02009;&#x0003c;&#x02009;0.05. However, only approximate assessments of statistical significance could be made and it is possible that the applied analysis somewhat overstated the degree to which statistically significant findings were obtained. Hence, all adjusted <italic>p</italic> values are considered apparent indicators of statistical significance.</p></sec><sec id="Sec21"><title>Descriptor importance analysis</title><p id="Par39">A final model, or set of models using five different random seeds for RFR, was built on the entirety of the relevant dataset and the corresponding descriptor importance values, or arithmetic mean values for RFR, were analyzed. For MLR, the magnitudes of the descriptor coefficients were retrieved. For RFR, the descriptor permutation based importance measure was employed [<xref ref-type="bibr" rid="CR23">23</xref>].</p></sec><sec id="Sec22"><title>Lattice energy predictions using molecular descriptors</title><p id="Par40">To get some insight into the extent to which calculating lattice energies from the crystal structures added information to the models of enthalpy of solution and temperature dependent solubility, beyond that inherent in the molecular descriptors, models for the lattice energy descriptor were built using the combined set of 2D molecular descriptors and random forest regression. For the Avdeef_ExDPs_CS_True and Avdeef_ExDPs_Cal_CS_True datasets, the same cross-validation folds were used as per the enthalpy of solution models. For the Klimenko_CS_True dataset, the CV&#x02009;=&#x02009;v cross-validation folds were used and all repeated occurrences of the same combination of lattice energy and descriptor values, due to different solubility values at different temperatures, were removed. Each set of modelling results was generated five times, using different random seeds. Descriptor importance analysis was carried out as per the models of enthalpy of solution and temperature dependent solubility.</p></sec><sec id="Sec23"><title>Computational details</title><p id="Par41">Further details related to the software and hardware used to generate our results are documented in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.</p></sec></sec><sec id="Sec24"><title>Results and discussion</title><sec id="Sec25"><title>Summary of cross-validated results</title><p id="Par42">Ultimately, cross-validated modelling results were generated for the Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] (enthalpy of solution endpoint) and Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] (temperature dependent solubility endpoint) derived datasets, according to a variety of different combinations of molecular (plus temperature, for the temperature dependent solubility endpoint) descriptors, with or without computed lattice energies and with or without melting point values, modelling algorithms (RFR or MLR), feature selection (yes or no) and cross-validation schemes. (N.B. For brevity, we refer to different modelling approaches&#x02014;meaning a given combination of modelling algorithm, descriptor set and use, or not, of feature selection&#x02014;as different models.) The predictive performance has been summarized, for each scenario, in terms of the arithmetic mean RMSE and R<sup>2</sup> values on the validation sets. Detailed results are presented, in Excel workbooks, in Additional file <xref rid="MOESM3" ref-type="media">3</xref>. These detailed results include all R<sup>2</sup> and RMSE values obtained from cross-validation, along with the mean of those values and, for key scenarios described under &#x0201c;Statistical significance of differences in cross-validated results&#x0201d;, pairwise differences in those mean values and the corresponding adjusted <italic>p</italic> values. As explained under &#x0201c;Methods and Data&#x0201d;, models were ranked on the same dataset using the mean RMSE and, with the exception of comparisons between results obtained using different cross-validation folds, <italic>p</italic> values were computed based on the mean RMSE values. All code and dataset files required to generate these cross-validated results are, as documented in Additional file <xref rid="MOESM1" ref-type="media">1</xref>, provided in Additional files <xref rid="MOESM4" ref-type="media">4</xref>, <xref rid="MOESM5" ref-type="media">5</xref>, <xref rid="MOESM6" ref-type="media">6</xref>, <xref rid="MOESM7" ref-type="media">7</xref>, <xref rid="MOESM8" ref-type="media">8</xref>, <xref rid="MOESM9" ref-type="media">9</xref>, <xref rid="MOESM10" ref-type="media">10</xref>.</p></sec><sec id="Sec26"><title>Choosing the most suitable cross-validation protocol</title><p id="Par43">For the Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>]. derived datasets, pairwise comparison of all corresponding performance estimates obtained with the same model evaluated via the CV&#x02009;=&#x02009;v and CV&#x02009;=&#x02009;rt cross-validation protocols clearly indicated that lower estimates of performance were almost always obtained using the CV&#x02009;=&#x02009;rt protocol. For 107 out of the relevant 116 scenarios, there was an apparent reduction in performance, in terms of the cross-validated mean R<sup>2</sup>, upon moving from the standard cross-validation (CV&#x02009;=&#x02009;v) to the pseudo-cross-validation (CV&#x02009;=&#x02009;rt) protocol. (Of the remaining nine scenarios, all of these corresponded to extreme overfitting of MLR models.) For 90 of these 107 pairwise comparisons, the mean differences appeared to be statistically significant (Additional file <xref rid="MOESM3" ref-type="media">3</xref>).</p><p id="Par44">These results are expected: allowing data for the same material to appear in corresponding training and test sets at different temperatures is expected to lead to inflated performance. As we are most interested in predicting temperature dependent solubility profiles for untested materials, we focus on the results obtained with our novel CV&#x02009;=&#x02009;rt protocol, except when comparing our results to those obtained by Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>].</p></sec><sec id="Sec27"><title>Comparison to the literature</title><p id="Par45">For the modelling scenarios which were most directly comparable to the work of Avdeef [<xref ref-type="bibr" rid="CR17">17</xref>] and Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>], we typically obtained similar results. Our best results, obtained using different modelling approaches, were better or fairly similar. However, due to refinements we made to their datasets and some differences in descriptor calculations, modelling protocols and validation protocols, we do not report perfectly like-for-like comparisons. Further details are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.</p></sec><sec id="Sec28"><title>Summary of best cross-validated results</title><p id="Par46">In order to assess the effect of incorporating different sets of descriptors on the predictive performance for both endpoints, we focus on the top performing results. We present the relevant top ranking and second best results in Tables&#x000a0;<xref rid="Tab2" ref-type="table">2</xref> and <xref rid="Tab3" ref-type="table">3</xref>. (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table S3 includes the top ranking CV&#x02009;=&#x02009;v results for the Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] derived datasets.)<table-wrap id="Tab2"><label>Table&#x000a0;2</label><caption><p>Top ranked results according to various scenarios for the enthalpy of solution datasets</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Dataset</th><th align="left">Rank</th><th align="left">Molecular descriptors</th><th align="left">3D descriptors from crystal structure?</th><th align="left">Melting point descriptor included</th><th align="left">Lattice energy descriptor included</th><th align="left">Method</th><th align="left">R<sup>2</sup></th><th align="left">RMSE (kJ/mol)</th></tr></thead><tbody><tr><td align="left">Avdeef_ExDPs_Cal_CS_False</td><td align="left">1st</td><td align="left">IntegSub, SiRMSSub</td><td align="left">False</td><td align="left">True</td><td align="left">False</td><td align="left">RFR</td><td char="." align="char">0.63</td><td char="." align="char">8.56</td></tr><tr><td align="left">Avdeef_ExDPs_Cal_CS_False</td><td align="left">2nd</td><td align="left">IntegSub, SiRMSSub</td><td align="left">False</td><td align="left">False</td><td align="left">False</td><td align="left">RFR</td><td char="." align="char">0.63</td><td char="." align="char">8.58</td></tr><tr><td align="left">Avdeef_ExDPs_Cal_CS_True</td><td align="left">1st</td><td align="left">IntegSub, SiRMSSub</td><td align="left">False</td><td align="left">True</td><td align="left">False</td><td align="left">RFR</td><td char="." align="char">0.26</td><td char="." align="char">11.45</td></tr><tr><td align="left">Avdeef_ExDPs_Cal_CS_True</td><td align="left">2nd</td><td align="left">IntegSub, SiRMSSub</td><td align="left">False</td><td align="left">True</td><td align="left">True</td><td align="left">RFR</td><td char="." align="char">0.26</td><td char="." align="char">11.47</td></tr><tr><td align="left">Avdeef_ExDPs_CS_False</td><td align="left">1st</td><td align="left">Rdk</td><td align="left">False</td><td align="left">False</td><td align="left">False</td><td align="left">RFR</td><td char="." align="char">0.34</td><td char="." align="char">13.82</td></tr><tr><td align="left">Avdeef_ExDPs_CS_False</td><td align="left">2nd</td><td align="left">Rdk</td><td align="left">False</td><td align="left">True</td><td align="left">False</td><td align="left">RFR</td><td char="." align="char">0.34</td><td char="." align="char">13.84</td></tr><tr><td align="left">Avdeef_ExDPs_CS_True</td><td align="left">1st</td><td align="left">3D</td><td align="left">True</td><td align="left">False</td><td align="left">True</td><td align="left">RFR</td><td char="." align="char">0.18</td><td char="." align="char">14.93</td></tr><tr><td align="left">Avdeef_ExDPs_CS_True</td><td align="left">2nd</td><td align="left">3D</td><td align="left">True</td><td align="left">True</td><td align="left">True</td><td align="left">RFR</td><td char="." align="char">0.18</td><td char="." align="char">14.95</td></tr></tbody></table><table-wrap-foot><p>All results were obtained without feature selection. All results are rounded to 2dp. The definitions of the 2D molecular descriptors subsets are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table S1. All references to R<sup>2</sup> and RMSE denote arithmetic mean values obtained from cross-validation and all model rankings were generated based on the mean RMSE values</p></table-wrap-foot></table-wrap>
<table-wrap id="Tab3"><label>Table&#x000a0;3</label><caption><p>Top ranked results according to various scenarios for the temperature dependent solubility datasets</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Dataset</th><th align="left">CV protocol</th><th align="left">Rank</th><th align="left">Molecular descriptors</th><th align="left">3D descriptors from crystal Structure?</th><th align="left">Melting point descriptor included</th><th align="left">Lattice energy descriptor included</th><th align="left">Method</th><th align="left">R<sup>2</sup></th><th align="left">RMSE (log units)</th></tr></thead><tbody><tr><td align="left">Klimenko_CS_False</td><td align="left">rt</td><td align="left">1st</td><td align="left">IntegSub, SiRMSSub, Absolv, Ind, Rdk</td><td align="left">False</td><td align="left">True</td><td align="left">False</td><td align="left">RFR</td><td char="." align="char">0.92</td><td char="." align="char">0.70</td></tr><tr><td align="left">Klimenko_CS_False</td><td align="left">rt</td><td align="left">2nd</td><td align="left">Rdk, Absolv</td><td align="left">False</td><td align="left">True</td><td align="left">False</td><td align="left">RFR</td><td char="." align="char">0.92</td><td char="." align="char">0.70</td></tr><tr><td align="left">Klimenko_CS_True</td><td align="left">rt</td><td align="left">1st</td><td align="left">3D, IntegSub, SiRMSSub, Absolv, Ind, Rdk</td><td align="left">False</td><td align="left">True</td><td align="left">False</td><td align="left">RFR</td><td char="." align="char">0.85</td><td char="." align="char">0.83</td></tr><tr><td align="left">Klimenko_CS_True</td><td align="left">rt</td><td align="left">2nd</td><td align="left">Rdk, Absolv</td><td align="left">False</td><td align="left">True</td><td align="left">False</td><td align="left">RFR</td><td char="." align="char">0.85</td><td char="." align="char">0.83</td></tr></tbody></table><table-wrap-foot><p>All results were obtained without feature selection. All results are rounded to 2dp. The definitions of 2D molecular descriptors subsets are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table S1. All references to R<sup>2</sup> and RMSE denote arithmetic mean values obtained from cross-validation (CV&#x02009;=&#x02009;rt) and all model rankings were generated based on the mean RMSE values</p></table-wrap-foot></table-wrap>
</p></sec><sec id="Sec29"><title>Effect of incorporating crystallographic information: lattice energy descriptor</title><p id="Par47">The top performing model for enthalpy of solution, evaluated on the Avdeef_ExDPs_CS_True dataset, included calculated lattice energy as a descriptor (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). However, none of the other top models for enthalpy of solution (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>) or direct prediction of temperature dependent solubility (Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>) incorporated the lattice energy descriptor.</p><p id="Par48">This may be partially attributed to the presence of the melting point or crystal structure based 3D descriptors acting as a partial proxy for the solid state contribution which the lattice energy descriptor is designed to capture. When only those models including neither the melting point nor crystal structure based 3D descriptors are considered (see Additional file <xref rid="MOESM3" ref-type="media">3</xref>), it remains the case that the top model for the Avdeef_ExDPs_CS_True dataset does and the top model for the Avdeef_ExDPs_Cal_CS_True dataset does not incorporate the lattice energy descriptor. (However, the results on the smaller Avdeef_ExDPs_Cal_CS_True dataset may be less robust.) However, for the Klimenko_CS_True dataset, the new top ranking model does incorporate the lattice energy descriptor.</p><p id="Par49">Nonetheless, for all scenarios in which the top performing model incorporated the lattice energy descriptor, the apparent performance enhancement over the corresponding model which did not incorporate the lattice energy descriptor was negligible (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>) and was never statistically significant. This remains the case when only those models not incorporating melting point or crystal structure based 3D descriptors are considered (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>). For all such scenarios, the increases in mean RMSE, upon removing the lattice energy descriptor, were around 0.03&#x000a0;kJ/mol and 0.00 (2dp) log units for predictions of enthalpy of solution and temperature dependent solubility respectively. The differences in mean R<sup>2</sup> were around 0.00 (2dp).<fig id="Fig5"><label>Fig.&#x000a0;5</label><caption><p>Cross-validated performance (RMSE) of the top performing model where the lattice energy (LE) descriptor was incorporated (LHS), compared to the corresponding model which didn&#x02019;t include the lattice energy descriptor (RHS): dataset&#x02009;=&#x02009;Avdeef_ExDPs_CS_True. The distributions of cross-validated results are presented as a boxplot, with whiskers extending 1.5 times the interquartile range beyond the upper and lower quartiles, with the arithmetic mean superimposed as a black circle. The presence of a star denotes an apparently statistically significant difference in cross-validated mean RMSE</p></caption><graphic xlink:href="13321_2018_298_Fig5_HTML" id="MO8"/></fig>
<fig id="Fig6"><label>Fig.&#x000a0;6</label><caption><p>Cross-validated performance (RMSE) of the top performing models (excluding models incorporating the melting point or crystal structure based 3D descriptors) where the lattice energy (LE) descriptor was incorporated (LHS), compared to the corresponding model which didn&#x02019;t include the lattice energy descriptor (RHS): <bold>a</bold> dataset&#x02009;=&#x02009;Avdeef_ExDPs_CS_True; <bold>b</bold> dataset&#x02009;=&#x02009;Klimenko_CS_True, CV&#x02009;=&#x02009;rt. All results are presented as per Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref></p></caption><graphic xlink:href="13321_2018_298_Fig6_HTML" id="MO9"/></fig>
</p><p id="Par50">Pairwise comparison of all relevant corresponding models identified only a minority of paired results, for both enthalpy of solution datasets and direct predictions of temperature dependent solubility (CV&#x02009;=&#x02009;rt), for which the inclusion of the lattice energy descriptor appeared to result in a statistically significant reduction in mean RMSE. Further discussion of the trends across all datasets is presented in Additional file <xref rid="MOESM1" ref-type="media">1</xref> and the details for all pairwise comparisons are presented in Additional file <xref rid="MOESM3" ref-type="media">3</xref>.</p></sec><sec id="Sec30"><title>Effect of incorporating crystallographic information: 3D descriptors based on crystal structure</title><p id="Par51">Only in the case of the Avdeef_ExDPs_CS_True dataset did the top performing model include the crystal structure based 3D descriptors (Tables&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>, <xref rid="Tab3" ref-type="table">3</xref>). However, upon removing the potentially confounding factors of the melting point and lattice energy descriptors, the best modelling results for Avdeef_ExDPs_CS_True and Klimenko_CS_True (CV&#x02009;=&#x02009;rt) were obtained using these descriptors (Additional file <xref rid="MOESM3" ref-type="media">3</xref>).</p><p id="Par52">All of these results for the Avdeef_ExDPs_CS_True dataset (Figs.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref>, <xref rid="Fig8" ref-type="fig">8</xref>) appeared statistically significantly different to the corresponding result obtained using the 3D descriptors based upon conformer generator structures. (This is in spite of the lattice energy descriptor also being incorporated into the overall top Avdeef_ExDPs_CS_True model.) This suggests the models may genuinely have benefited from the solid state information implicit in the 3D descriptors based upon the crystal structure. However, the same comparison for the top Klimenko_CS_True (CV&#x02009;=&#x02009;rt) model, after removing models with the lattice energy or melting point descriptor, found the difference in mean RMSE to the corresponding model using 3D descriptors based upon conformer generator structures appeared statistically insignificant.<fig id="Fig7"><label>Fig.&#x000a0;7</label><caption><p>Cross-validated performance (RMSE) of the top performing model where the crystal structure based 3D descriptors were incorporated (LHS), compared to the corresponding model using the conformer generator based 3D descriptors (RHS): dataset&#x02009;=&#x02009;Avdeef_ExDPs_CS_True. All results are presented as per Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref></p></caption><graphic xlink:href="13321_2018_298_Fig7_HTML" id="MO10"/></fig>
<fig id="Fig8"><label>Fig.&#x000a0;8</label><caption><p>Cross-validated performance (RMSE) of the top performing models (excluding models incorporating the melting point or lattice energy descriptor) where the crystal structure based 3D descriptors were incorporated (LHS), compared to the corresponding model using the conformer generator based 3D descriptors (RHS): <bold>a</bold> dataset&#x02009;=&#x02009;Avdeef_ExDPs_CS_True; <bold>b</bold> dataset&#x02009;=&#x02009;Klimenko_CS_True, CV&#x02009;=&#x02009;rt. All results are presented as per Fig. <xref rid="Fig5" ref-type="fig">5</xref></p></caption><graphic xlink:href="13321_2018_298_Fig8_HTML" id="MO11"/></fig>
</p><p id="Par53">Pairwise comparison identified only a minority of paired results, for one of the enthalpy of solution datasets (Avdeef_ExDPs_CS_True), for which the inclusion of crystal structure based 3D descriptors appeared to result in a statistically significant reduction in mean RMSE compared to the corresponding model using conformer generator based 3D descriptors. For the other enthalpy of solution dataset and direct prediction of temperature dependent solubility, no apparently significantly different results were obtained. Further discussion of the trends across all datasets is presented in Additional file <xref rid="MOESM1" ref-type="media">1</xref> and the details for all pairwise comparisons are presented in Additional file <xref rid="MOESM3" ref-type="media">3</xref>.</p></sec><sec id="Sec31"><title>Effect of incorporating melting point</title><p id="Par54">For all relevant scenarios, save for models developed using the Avdeef_ExDPs_CS_False or Avdeef_ExDPs_CS_True datasets, the best performing models incorporated the melting point descriptor (Tables&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>, <xref rid="Tab3" ref-type="table">3</xref>). When models incorporating the other solid state contribution descriptors&#x02014;i.e. the lattice energy and crystal structure based 3D descriptors&#x02014;were excluded, the top models for all crystal structure integrated datasets incorporated the melting point descriptor. However, it should be noted that the apparent increase in best predictive performance upon incorporating the melting point descriptor was, at most, modest (Figs.&#x000a0;<xref rid="Fig9" ref-type="fig">9</xref>, <xref rid="Fig10" ref-type="fig">10</xref>). Moreover, only the performance increases for the Klimenko_CS_False and Klimenko_CS_True datasets appeared statistically significant.<fig id="Fig9"><label>Fig.&#x000a0;9</label><caption><p>Cross-validated performance (RMSE) of the top performing models for all scenarios where they incorporated the melting point (MP) descriptor (LHS), compared to the corresponding model which didn&#x02019;t include the MP descriptor (RHS): <bold>a</bold> dataset&#x02009;=&#x02009;Avdeef_ExDPs_Cal_CS_True; <bold>b</bold> dataset&#x02009;=&#x02009;Avdeef_ExDPs_Cal_CS_False; <bold>c</bold> dataset&#x02009;=&#x02009;Klimenko_CS_True, CV&#x02009;=&#x02009;rt; <bold>d</bold> Klimenko_CS_False, CV&#x02009;=&#x02009;rt. All results are presented as per Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref></p></caption><graphic xlink:href="13321_2018_298_Fig9_HTML" id="MO12"/></fig>
<fig id="Fig10"><label>Fig.&#x000a0;10</label><caption><p>Cross-validated performance (RMSE) of the top performing models for all crystal structure integrated datasets, excluding models involving the lattice energy or crystal structure based 3D descriptors, where they incorporated the melting point (MP) descriptor (LHS), compared to the corresponding model which didn&#x02019;t include the MP descriptor (RHS): <bold>a</bold> dataset&#x02009;=&#x02009;Avdeef_ExDPs_CS_True; <bold>b</bold> dataset&#x02009;=&#x02009;Avdeef_ExDPs_Cal_CS_True; <bold>c</bold> dataset&#x02009;=&#x02009;Klimenko_CS_True, CV&#x02009;=&#x02009;rt. All results are presented as per Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref></p></caption><graphic xlink:href="13321_2018_298_Fig10_HTML" id="MO13"/></fig>
</p><p id="Par55">Pairwise comparison identified that, for a majority of scenarios, the inclusion of the melting point descriptor appeared to result in statistically significant enhancement in direct predictions of temperature dependent solubility. However, this was almost never observed from pairwise comparison of the models of enthalpy of solution. Further discussion of the trends across all datasets is presented in Additional file <xref rid="MOESM1" ref-type="media">1</xref> and the details for all pairwise comparisons are presented in Additional file <xref rid="MOESM3" ref-type="media">3</xref>.</p></sec><sec id="Sec32"><title>Effect of feature selection</title><p id="Par56">The application of the feature selection algorithm never yielded one of the top models as assessed according to any of the evaluation protocols. Hence, it cannot be claimed that feature selection improved the best predictive performance.</p><p id="Par57">This is in keeping with the pairwise comparison of modelling protocols which differed only in terms of whether feature selection was employed. All such comparisons where feature selection appeared to improve the mean RMSE corresponded to scenarios in which MLR was applied in combination with the high dimensional combination of all 2D molecular descriptors. Indeed, all scenarios involving RFR indicated a reduction in predictive performance upon applying feature selection.</p></sec><sec id="Sec33"><title>Significance of the temperature descriptor</title><p id="Par58">The importance of the (1/T) descriptor, in terms of its coefficient magnitude, was always close to the lowest for any descriptor for the evaluated MLR models. Conversely, for the evaluated models built using the non-linear RFR algorithm, the (1/T) descriptor was consistently in the top 20% of descriptors, excluding those models for which the molecular descriptors were based solely on the Absolv or Absolv and Ind (see Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table S1) or 3D descriptor sets.</p><p id="Par59">These results can be explained by the van&#x02019;t Hoff relationship (see Eq.&#x000a0;<xref rid="Equ1" ref-type="">1</xref>), which posits that, if the standard enthalpy of solution is roughly constant over the relevant temperature (T) range, log10(solubility) should be linearly related to (1/T) for a given material, with the slope of the trend line being proportional to the standard enthalpy of solution. Hence, due to the variation in the standard enthalpy of solution across materials, a non-linear relationship will exist between (1/T) and log10(solubility) across materials. We found that the van&#x02019;t Hoff relationship and the assumption that the standard enthalpy of solution is temperature independent holds well for most entries in the Klimenko et al. [<xref ref-type="bibr" rid="CR14">14</xref>] derived datasets, for which an assessment was possible, and that the standard enthalpy of solution varied considerably across materials.</p><p id="Par60">Detailed results supporting these comments are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.</p></sec><sec id="Sec34"><title>Significant molecular descriptors</title><p id="Par61">Regarding the question of which sets of molecular descriptors yielded the most predictive models, it can be seen (Tables&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>, <xref rid="Tab3" ref-type="table">3</xref>) that the top models for enthalpy of solution or direct prediction of temperature dependent solubility, under different scenarios, were built using a variety of descriptor sets. Regarding the question of which individual molecular descriptors were found to be most important for the models, descriptor analysis suggested that no single molecular descriptor stood out as being consistently important, but the descriptors identified as most important could generally be rationalized in terms of the information they conveyed regarding the potential for specific kinds of solid state and/or solution state interactions.</p><p id="Par62">Detailed results supporting these comments are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.</p></sec><sec id="Sec35"><title>Discussion of the main findings</title><p id="Par63">The main findings from this work relate to the outcome of evaluating temperature dependent solubility models via a novel (CV&#x02009;=&#x02009;rt) cross-validation scheme and the effect of explicitly incorporating various kinds of solid state information into these models or models of the related enthalpy of solution endpoint. Here, we offer possible explanations for our findings and put them within the context of previous studies.</p><sec id="Sec36"><title>Standard cross-validation protocols can overestimate the performance of models of temperature dependent solubility</title><p id="Par64">Our findings make clear that standard cross-validation protocols are likely to significantly overestimate the performance of models designed to estimate temperature dependent solubility for untested materials, by allowing solubility data points measured for the same material at slightly different temperatures to be placed in corresponding training and test sets. Of course, this scenario could still be a fair test for a model designed to interpolate solubility data measured for a given material at a couple of temperatures for other relevant temperatures. Nonetheless, we can recommend our modified (CV&#x02009;=&#x02009;rt) cross-validation scheme for scenarios in which the predictive performance of QSPR models for the temperature dependent solubility profile of untested materials are being evaluated.</p></sec><sec id="Sec37"><title>Solid state descriptors based on crystallographic information or melting point data never substantially improved the best models of temperature dependent solubility or the related enthalpy of solution endpoint</title><p id="Par65">Our results suggest that incorporating the lattice energy descriptor, calculated from the assigned crystal structure, may improve predictive performance for both temperature dependent solubility related endpoints under some scenarios. However, no statistically significant findings were obtained to indicate that this descriptor improves the best predictions of enthalpy of solution or the best direct predictions of temperature dependent solubility. This remained the case when only models without either of the other solid state descriptors were considered.</p><p id="Par66">The inclusion of crystallographic information in the form of crystal structure based 3D descriptors may genuinely improve predictions of enthalpy of solution. However, only for one of the two enthalpy of solution datasets modelled were apparently statistically significant improvements in the best predictions, due to the incorporation of crystallographic information in this fashion, observed. These descriptors never appeared to statistically significantly enhance the best direct predictions of temperature dependent solubility. This remained the case when the other solid state descriptors were removed.</p><p id="Par67">We found that the inclusion of a melting point descriptor almost never appeared to yield statistically significant improvements in predictions of enthalpy of solution. Indeed, this descriptor was never observed to result in statistically significant improvement in the best enthalpy of solution predictions. This remained the case when only those models not incorporating any other solid state descriptors were considered. Contrastingly, we found that the inclusion of the melting point descriptor appears to result in statistically significant performance enhancement for the best direct predictions of temperature dependent solubility. This was in keeping with the observation that the inclusion of the melting point descriptor often led to apparently statistically significant improvements in direct predictions of temperature dependent solubility.</p><p id="Par68">However, even when apparently statistically significant improvements in the best predictions were observed, they were not substantial (Figs.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>, <xref rid="Fig6" ref-type="fig">6</xref>, <xref rid="Fig7" ref-type="fig">7</xref>
<xref rid="Fig8" ref-type="fig">8</xref>, <xref rid="Fig9" ref-type="fig">9</xref>, <xref rid="Fig10" ref-type="fig">10</xref>). The failure of any of the solid state descriptors to substantially improve either the best direct predictions of temperature dependent solubility or the best predictions of the related enthalpy of solution endpoint, even when the other solid state descriptors were not included in the models, may be attributed to a variety of possible, non-mutually exclusive, explanations. (1) The variation in the endpoint data, for the investigated datasets, might be primarily dominated by variations in non-solid state contributions. (2) The solid state descriptors are insufficiently good at capturing the variation in solid state contributions. (3) The molecular descriptors, not including the crystal structure based 3D descriptors, implicitly capture the variation in solid state contributions to a considerable extent. Each of these possible explanations is considered in turn.</p></sec><sec id="Sec38"><title>Do our findings reflect greater variation in non-solid state contributions for the modelled datasets?</title><p id="Par69">If it were the case that non-solid state contributions to temperature dependent solubility, or the related enthalpy of solution, made a greater contribution to the variation in the solubility (or enthalpy) values for our datasets, this would suggest that being able to better capture the variations in solid state contributions would only lead to a modest improvement in predictive power for these endpoints. In practice, such a modest improvement might be sufficiently small to be deemed statistically insignificant. However, whether this could be expected to be the case for our modelled datasets is unclear. Recent experimental and computational studies have variously indicated that the variation in solubility across different public datasets was [<xref ref-type="bibr" rid="CR26">26</xref>] and was not [<xref ref-type="bibr" rid="CR48">48</xref>] dominated by non-solid state contributions. Moreover, we can only speculate on whether the relative importance of solid and non-solid state contributions to the variability in solubility suggested by these analyses is representative of the situation for the datasets studied in our work.</p></sec><sec id="Sec39"><title>Do our findings reflect the limitations of the solid state descriptors?</title><p id="Par70">It should be noted that there are two kinds of possible limitations to the ability of the solid state descriptors to capture solid state contributions to the modelled endpoints: (a) inherent limitations; (b) limitations arising from the possibility that the solid state descriptors were calculated or, in the case of melting point data, measured for the wrong polymorph. As discussed under &#x0201c;Integration with crystal structures&#x0201d;, a very small proportion of endpoint data points were annotated with the assessed polymorph and, typically, the predicted most stable available crystal structure was selected. It has previously been suggested that it is not essential to calculate lattice energies from the correct polymorph in order to predict solubility [<xref ref-type="bibr" rid="CR49">49</xref>] and computational studies suggest most polymorph energies differ by less than 7&#x000a0;kJ/mol [<xref ref-type="bibr" rid="CR50">50</xref>]. Nonetheless, the experimental solubilities of polymorphs may differ by around 0.60 log units (log10[molar]) [<xref ref-type="bibr" rid="CR51">51</xref>]. (Elsewhere, higher apparent solubility differences between polymorphs are reported, although it is suggested that these differences are typically less than 1.0 log units (log10[molar]) [<xref ref-type="bibr" rid="CR52">52</xref>].) However, given that whether the solid form corresponding to the solid state descriptors differs from the polymorph corresponding to the solubility or enthalpy of solution data modelled in the current work is typically unknown, we are unable to assess the extent to which non-inherent limitations of the solid state descriptors affect our findings.</p><p id="Par71">The lattice energy descriptor obtained a Pearson&#x02019;s correlation coefficient of 0.77 (one-tail <italic>p</italic> value&#x02009;=&#x02009;10<sup>&#x02212;6</sup>) with the experimentally estimated lattice energies for the 27 SUB-48 [<xref ref-type="bibr" rid="CR29">29</xref>] dataset entries which complied with our filtering criteria. This confirms that the force-field protocol used to compute the lattice energy descriptor was a reasonable choice. If this statistic was representative of the performance of the lattice energy descriptor for the modelled datasets, the lattice energy descriptor should significantly capture the solid state contribution to the temperature dependent solubility related endpoints. Since the SUB-48 dataset was [<xref ref-type="bibr" rid="CR29">29</xref>], like the datasets modelled in our work, a mixture of pharmaceutical APIs and general organic compounds and, as per most entries in our datasets, the calculated lowest energy crystal structure was assigned in the absence of polymorph specific information, this statistic could reasonably be expected to be representative of how the lattice energy descriptor would perform on the modelled datasets.</p><p id="Par72">However, whilst experimental lattice energy estimates were not available for the modelled datasets, the correlation between the lattice energy descriptor and the available melting point data may be considered indicative of the extent to which the former captures the solid state contributions to the enthalpy of solution and temperature dependent solubility data. This can be seen from consideration of Eqs.&#x000a0;(<xref rid="Equ2" ref-type="">2</xref>), (<xref rid="Equ3" ref-type="">3</xref>) [<xref ref-type="bibr" rid="CR53">53</xref>] and (4), from which it can be expected that lattice energy and melting point are negatively correlated. (In Eqs.&#x000a0;<xref rid="Equ3" ref-type="">3</xref> and <xref rid="Equ4" ref-type="">4</xref>, <inline-formula id="IEq6"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ T_{m} $$\end{document}</tex-math><mml:math id="M16"><mml:msub><mml:mi>T</mml:mi><mml:mi>m</mml:mi></mml:msub></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq6.gif"/></alternatives></inline-formula> denotes melting point, <inline-formula id="IEq7"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta H_{fus} $$\end{document}</tex-math><mml:math id="M18"><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">fus</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq7.gif"/></alternatives></inline-formula> and <inline-formula id="IEq8"><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta S_{fus} $$\end{document}</tex-math><mml:math id="M20"><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>S</mml:mi><mml:mrow><mml:mi mathvariant="italic">fus</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq8.gif"/></alternatives></inline-formula> the enthalpy and entropy of fusion respectively, <inline-formula id="IEq9"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta H_{sub} $$\end{document}</tex-math><mml:math id="M22"><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">sub</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq9.gif"/></alternatives></inline-formula> the sublimation enthalpy and <inline-formula id="IEq10"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta H_{cond} $$\end{document}</tex-math><mml:math id="M24"><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">cond</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math><inline-graphic xlink:href="13321_2018_298_Article_IEq10.gif"/></alternatives></inline-formula> the condensation enthalpy.)<disp-formula id="Equ3"><label>3</label><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ T_{m} = \frac{{\Delta H_{fus} }}{{\Delta S_{fus} }} $$\end{document}</tex-math><mml:math id="M26" display="block"><mml:mrow><mml:msub><mml:mi>T</mml:mi><mml:mi>m</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">fus</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>S</mml:mi><mml:mrow><mml:mi mathvariant="italic">fus</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:mfrac></mml:mrow></mml:math><graphic xlink:href="13321_2018_298_Article_Equ3.gif" position="anchor"/></alternatives></disp-formula>
<disp-formula id="Equ4"><label>4</label><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \Delta H_{fus} = \Delta H_{sub} + \Delta H_{cond} $$\end{document}</tex-math><mml:math id="M28" display="block"><mml:mrow><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">fus</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">sub</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:mi mathvariant="normal">&#x00394;</mml:mi><mml:msub><mml:mi>H</mml:mi><mml:mrow><mml:mi mathvariant="italic">cond</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:math><graphic xlink:href="13321_2018_298_Article_Equ4.gif" position="anchor"/></alternatives></disp-formula>
</p><p id="Par73">Hence, the weak negative correlations between the lattice energy descriptor and the melting point descriptor could suggest the lattice energy descriptor did not capture solid state contributions well for the entirety of these datasets (Fig.&#x000a0;<xref rid="Fig11" ref-type="fig">11</xref>a&#x02013;c). Moreover, the fact that these correlations were observed to increase when only the subset of entries for which crystal structure specific melting point data were available was considered (Fig.&#x000a0;<xref rid="Fig11" ref-type="fig">11</xref>d&#x02013;f) could further suggest that the lattice energy descriptor was not uniformly good at capturing solid state contributions for all entries in the modelled datasets. (Here, it should be noted that around 2% of the Avdeef_ExDPs_CS_True and Klimenko_CS_True crystal structures were disordered. However, this was only observed to have caused lattice energy calculation errors for one structure in the Avdeef_ExDPs_CS_True dataset. Full details are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.) The fact that those correlations were negligibly changed when the actual crystal structure specific melting points, retrieved from the CSD (version 5.38) using the CSD Python API [<xref ref-type="bibr" rid="CR54">54</xref>], were used (Fig.&#x000a0;<xref rid="Fig11" ref-type="fig">11</xref>g&#x02013;i) suggests that the poor correlations observed between the lattice energy descriptor and the melting point descriptor across the entirety of the modelled datasets did not reflect the fact that the melting point data used for the latter descriptor may not have corresponded to the polymorph for which the lattice energy descriptor was calculated.<fig id="Fig11"><label>Fig.&#x000a0;11</label><caption><p>Correlation, in terms of the Pearson correlation coefficients (r) and one-tail <italic>p</italic> values (p), between all N corresponding pairs of lattice energy (LE) descriptor values and melting point (MP) values, from different sources, for different datasets: <bold>a</bold> Klimenko_CS_True dataset, melting point descriptor values (N&#x02009;=&#x02009;129, r&#x02009;=&#x02009;&#x02212;&#x02009;0.29, <italic>p</italic>&#x02009;=&#x02009;0.00); <bold>b</bold> Avdeef_ExDPs_CS_True dataset, melting point descriptor values (N&#x02009;=&#x02009;169, r&#x02009;=&#x02009;&#x02212;&#x02009;0.15, <italic>p</italic>&#x02009;=&#x02009;0.02); <bold>c</bold> Avdeef_ExDPs_Cal_CS_True dataset, melting point descriptor values (N&#x02009;=&#x02009;30, r&#x02009;=&#x02009;&#x02212;&#x02009;0.24, <italic>p</italic>&#x02009;=&#x02009;0.10); <bold>d</bold> Klimenko_CS_True subset with CSD melting point data, melting point descriptor values (N&#x02009;=&#x02009;17, r&#x02009;=&#x02009;&#x02212;&#x02009;0.39, <italic>p</italic>&#x02009;=&#x02009;0.06); <bold>e</bold> Avdeef_ExDPs_CS_True subset with CSD melting point data, melting point descriptor values (N&#x02009;=&#x02009;22, r&#x02009;=&#x02009;&#x02212;&#x02009;0.61, <italic>p</italic>&#x02009;=&#x02009;0.00); <bold>f</bold> Avdeef_ExDPs_Cal_CS_True subset with CSD melting point data, melting point descriptor values (N&#x02009;=&#x02009;5, r&#x02009;=&#x02009;&#x02212;&#x02009;0.48, <italic>p</italic>&#x02009;=&#x02009;0.21); <bold>g</bold> Klimenko_CS_True subset with CSD melting point data, CSD melting point data (N&#x02009;=&#x02009;17, r&#x02009;=&#x02009;&#x02212;&#x02009;0.37, <italic>p</italic>&#x02009;=&#x02009;0.07); <bold>h</bold> Avdeef_ExDPs_CS_True subset with CSD melting point data, CSD melting point data (N&#x02009;=&#x02009;22, r&#x02009;=&#x02009;&#x02212;&#x02009;0.60, <italic>p</italic>&#x02009;=&#x02009;0.00); <bold>i</bold> Avdeef_ExDPs_Cal_CS_True subset with CSD melting point data, CSD melting point data (N&#x02009;=&#x02009;5, r&#x02009;=&#x02009;&#x02212;&#x02009;0.52, <italic>p</italic>&#x02009;=&#x02009;0.19). N.B. (1) These one tail <italic>p</italic> values denote the probability of getting as negative a correlation coefficient as observed, by chance, given the null-hypothesis of zero correlation. (2) In order to make the plot legible, one outlier (CSD refcode TEPHTH13, calculated lattice energy &#x02212;&#x02009;2735.71&#x000a0;kcal/mol) was excluded from plot (b). (3) Where a range of melting points was retrieved for the specific crystal structure from the CSD, the mean value was used</p></caption><graphic xlink:href="13321_2018_298_Fig11_HTML" id="MO16"/></fig>
</p><p id="Par74">Nonetheless, it should be noted that an imperfect correlation would still be expected between the lattice energy descriptor and the crystal structure specific melting point, even if the former corresponded perfectly to the true lattice energy and there were no experimental errors in the latter. Melting point will be imperfectly correlated with the enthalpy of fusion, due to the variation in the entropy of fusion across materials (Eq.&#x000a0;<xref rid="Equ3" ref-type="">3</xref>). The enthalpy of fusion, in turn, will be imperfectly correlated with the enthalpy of sublimation, due to the variation in the condensation enthalpy across materials (Eq.&#x000a0;<xref rid="Equ4" ref-type="">4</xref>), and the latter, in turn, is only linearly related to the lattice energy under certain assumptions [<xref ref-type="bibr" rid="CR29">29</xref>] and at constant temperature (Eq.&#x000a0;<xref rid="Equ2" ref-type="">2</xref>). Hence, also taking into account the possibility that the melting point descriptor may correspond to a different polymorph, the poor correlation of the lattice energy descriptor with the melting point descriptor across the modelled datasets may overstate the extent to which the lattice energy descriptor fails to capture the solid state contribution to the modelled endpoints.</p><p id="Par75">The fact that the melting point descriptor did not necessarily correspond to the experimental melting point for the polymorph for which enthalpy of solution or temperature dependent solubility data were available may have contributed to this descriptor failing to fully capture the solid state contribution to the modelled endpoints. Consideration of the previously mentioned subset of dataset entries for which crystal structure specific melting point data were retrieved from the CSD, indicates that melting point data for the same chemical can differ significantly in some cases. (Full details are provided in Additional file <xref rid="MOESM3" ref-type="media">3</xref>. Here, it should also be noted that we cannot be certain that these discrepancies in melting point data from different data sources necessarily reflected melting point differences between polymorphs.) For the Klimenko_CS_True dataset, across all 17 pairs of corresponding CSD retrieved melting points and melting point descriptor values, the median absolute deviation was 1.5, the 95th percentile value was 41.4 and the maximum value was 85 degrees Kelvin. The corresponding statistics for the 22 (5) pairs of melting point values obtained for the Avdeef_ExDPs_CS_True (Avdeef_ExDPs_Cal_CS_True) dataset were as follows: median&#x02009;=&#x02009;1.75 (2), 95th percentile&#x02009;=&#x02009;35.9 (12.5), maximum&#x02009;=&#x02009;52.2 (14.2) degrees Kelvin.</p><p id="Par76">Finally, regarding the inherent limitations of the solid state descriptors considered in this work, the crystal structure based 3D descriptors would not be able to fully capture all information relevant to lattice interactions. These CPSA descriptors [<xref ref-type="bibr" rid="CR39">39</xref>&#x02013;<xref ref-type="bibr" rid="CR41">41</xref>] capture information related to polar and non-polar intermolecular forces, based on electrostatic distributions at the molecular surface, and a CPSA descriptor, calculated from the molecular structure via a conformer generator, was amongst those making a significant contribution to the enthalpy of sublimation model of Salahinejad et al. [<xref ref-type="bibr" rid="CR53">53</xref>]. However, these descriptors would have been unable to take account of dispersion forces or properly take account of localized interactions such as hydrogen bonding. Indeed, they are indicated to be weakly dependent on molecular conformation [<xref ref-type="bibr" rid="CR39">39</xref>, <xref ref-type="bibr" rid="CR40">40</xref>].</p><p id="Par77">Future studies should consider computing additional 3D descriptors, based upon the crystal structure, which more fully account for those interactions. Ideally, these would explicitly take account of the lattice structure. This would arguably result in them better taking account of the actual solid state intermolecular interactions than crystal structure based 3D molecular descriptors, which can only implicitly take account of that information. However, as per our current work, if 3D molecular descriptors were computed using the crystal structure, they would need to be benchmarked against the performance of the same 3D descriptors computed without knowledge of the crystal structure, using a conformer generator.</p></sec><sec id="Sec40"><title>Do our findings reflect the ability of the molecular descriptors to implicitly capture solid state contributions?</title><p id="Par78">It should be acknowledged that molecular descriptors cannot capture variations in solid state contributions arising from polymorphism. Hence, decent solid state descriptors, calculated or measured for the solid form for which the endpoint data were measured, would be expected to add additional information that molecular descriptors cannot capture. However, as previously discussed, the experimentally assessed polymorph was not typically known for the modelled datasets.</p><p id="Par79">Under this scenario, molecular descriptors may be just as good at capturing solid state contributions to the enthalpy of solution or temperature dependent solubility as the solid state descriptors. The extent to which molecular descriptors can capture solid state structural information may be illustrated by the reasonable quality of models built for the lattice energy descriptor using the combined set of 2D molecular descriptors and random forest for the Klimenko_CS_True dataset. The cross-validated mean R<sup>2</sup> was 0.60&#x02009;&#x000b1;&#x02009;0.01 (standard error of the mean).</p><p id="Par80">The lower performance for the Avdeef_ExDPs_Cal_CS_True dataset (mean R<sup>2</sup>&#x02009;=&#x02009;0.40&#x02009;&#x000b1;&#x02009;0.04) may reflect the fewer data points available for training. However, the poor cross-validation results obtained for the Avdeef_ExDPs_CS_True dataset (mean R<sup>2</sup>&#x02009;=&#x02009;&#x02212;&#x02009;172.20&#x02009;&#x000b1;&#x02009;26.56) are surprising. This arguably reflects the presence of some problematic instances distorting the results. For three out of five cross-validation repetitions, all R<sup>2</sup> values were negative whilst, for two repetitions, a single fold yielded R<sup>2</sup> values between 0.70 and 0.78. Nine instances were identified with absolute predictions errors greater than 50&#x000a0;kcal/mol. (These instances are identified in Additional file <xref rid="MOESM1" ref-type="media">1</xref>. Other than the fact that one of them, CSD refcode TEPHTH13, was an extreme lattice energy outlier of &#x02212;&#x02009;2735.71&#x000a0;kcal/mol, there was no obvious reason for them being prediction outliers.) When the modelling results were generated again without these instances, a much better mean R<sup>2</sup> (0.76&#x02009;&#x000b1;&#x02009;0.01) was obtained.</p><p id="Par81">Descriptor importance analysis suggested partial consistency between the most important molecular descriptors, for predictions of calculated lattice energies, across all datasets. The common most important descriptors are expected to have a close link to solid state intermolecular interactions. Further details are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref>.</p></sec><sec id="Sec41"><title>How do our findings regarding the importance of solid state descriptors compare to previous modelling studies of related endpoints?</title><p id="Par82">Both our findings regarding the effect of incorporating the crystal structure derived descriptors (calculated lattice energy or 3D descriptors) and/or the melting point descriptor into our models for temperature dependent solubility, and the related enthalpy of solution endpoint, should be seen in the context of the wider debate in the recent literature regarding the importance of explicitly representing solid state contributions in models of aqueous solubility and the extent to which molecular descriptors can capture solid state contributions [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR26">26</xref>&#x02013;<xref ref-type="bibr" rid="CR30">30</xref>]. Emami et al. [<xref ref-type="bibr" rid="CR27">27</xref>] found that two parameter QSPR models for aqueous solubility incorporating experimental melting point as a descriptor did not perform better than two parameter models based solely on molecular descriptors. (However, their results did suggest that a two parameter model incorporating the entropy of melting outperformed molecular descriptor models.) This is consistent with our finding that the inclusion of a melting point descriptor within molecular descriptor based models was not responsible for statistically significant improvements in the best predictions of the related enthalpy of solution endpoint. However, it is possibly at odds with our finding that melting point data did result in an apparently statistically significant improvement in the best direct predictions of temperature dependent solubility. Nonetheless, it should be remembered that even these apparently statistically significant improvements were not substantial.</p><p id="Par83">In keeping with our finding, that incorporating a lattice energy descriptor did not lead to a statistically significant improvement in the best model for temperature dependent aqueous solubility or the related enthalpy of solution, Salahinejad et al. [<xref ref-type="bibr" rid="CR3">3</xref>] found that the availability of lattice energy or sublimation enthalpy descriptors did not significantly improve models of aqueous solubility. However, whilst those authors [<xref ref-type="bibr" rid="CR3">3</xref>] used sublimation enthalpies, converted to lattice energies as per Eq.&#x000a0;(<xref rid="Equ2" ref-type="">2</xref>), estimated from molecular structure [<xref ref-type="bibr" rid="CR53">53</xref>], our own analysis was based on lattice energies estimated from crystal structures. The fact that we obtained similar findings might suggest their results were not an artefact of failing to incorporate crystallographic information into their models. On the other hand, the fact that we needed to assign a nominal crystal structure in many cases, due to the few data points associated with polymorph information in our dataset, might be a contributory factor to this finding.</p><p id="Par84">In contrast to our findings, McDonagh et al. [<xref ref-type="bibr" rid="CR30">30</xref>] suggested that random forest models of aqueous solubility were statistically significantly improved upon adding theoretical descriptors, calculated in part from crystal structures assigned using a similar protocol to our own, to molecular descriptors. However, it should be noted that the theoretical descriptors calculated by these authors [<xref ref-type="bibr" rid="CR30">30</xref>] were a combination of solid state energetic contributions, calculated from crystal structures using the DMACRYS program [<xref ref-type="bibr" rid="CR55">55</xref>], and non-solid state contributions, computed using Hartree&#x02013;Fock or MO6-2X calculations, and they only reported statistically significant improvements in their models when those theoretical descriptors were calculated using MO6-2X calculations. This suggests that the theoretical descriptors capturing the non-solid state contributions may have been most important here. (It should also be noted that their assessment of statistically significant differences was not identical to the protocol employed herein.) Hence, their findings are not <italic>necessarily</italic> at odds with our own observation that incorporating lattice energy descriptors, calculated from crystal structures, do not statistically significantly improve the best QSPR models of temperature dependent aqueous solubility or the related enthalpy of solution.</p><p id="Par85">As regards our suggestion that this finding reflects, in part, the ability of molecular descriptors to serve, to a considerable degree, as proxies for solid state contributions, various recent studies have considered the extent to which molecular descriptors can capture solid state contributions to solubility [<xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR28">28</xref>, <xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR53">53</xref>]. Both Salahinejad et al. [<xref ref-type="bibr" rid="CR53">53</xref>] and Docherty et al. [<xref ref-type="bibr" rid="CR26">26</xref>] reported that molecular descriptor based QSPR models could capture most of the variation in enthalpy of sublimation data for diverse organic compounds, with test set R<sup>2</sup> values&#x02009;&#x0003e;&#x02009;0.90. However, Abramov [<xref ref-type="bibr" rid="CR28">28</xref>] recently suggested that the failure of molecular descriptors to fully capture solid state contributions was the major limiting factor in the prediction of aqueous solubility using QSPR methods and that the good performance reported for molecular models of enthalpy of sublimation could represent their ability to capture short range molecular interactions in the solid state, as opposed to long range interactions within the crystal. Furthermore, even for enthalpy of sublimation, McDonagh et al. [<xref ref-type="bibr" rid="CR29">29</xref>] found that QSPR models built using theoretical chemistry descriptors, calculated from crystal structures, appeared substantially more predictive than models built using molecular descriptors, albeit for the relatively small SUB-48 dataset.</p><p id="Par86">Hence, these earlier studies support the hypothesis that incorporating crystallographic information should be able to capture solid state contributions to solubility and its temperature dependence better than simply using molecular descriptors. This may be reflected in the fact that our results offer some evidence that the incorporation of this information, in the form of crystal based 3D molecular descriptors, may genuinely improve the best QSPR models of the related enthalpy of solution term. However, the fact that our results do not suggest statistically significant improvement, upon incorporating calculated lattice energies, in the best predictions of either temperature dependent solubility related endpoint could well reflect, in part, the limited extent to which our lattice energy calculations capture solid state contributions above and beyond the degree to which this is captured by molecular descriptors. In part, this may reflect greater discrepancy between the polymorphs for which endpoint data were available and for which the lattice energies were calculated, compared to some previous studies, albeit McDonagh et al. [<xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR30">30</xref>] were obliged to handle missing polymorph data in much the same way as per our current work. It may also reflect, as suggested by our correlation analyses of melting point data and calculated lattice energies (Fig.&#x000a0;<xref rid="Fig11" ref-type="fig">11</xref>), variations in the performance of the lattice energy calculations for different subsets of the modelled datasets.</p></sec></sec></sec><sec id="Sec42"><title>Conclusions</title><p id="Par87">In this work, we have built upon the few QSPR studies published to date which have explored the prediction of temperature dependent solubility. Specifically, we have extended previous work looking at modelling the enthalpy of solution, which can be related to temperature dependent solubility via the van&#x02019;t Hoff relationship, and direct prediction of temperature dependent solubility for aqueous solutions. We built upon these earlier studies via investigating the following factors: (a) the incorporation of crystallographic information, in the form of lattice energies or 3D descriptors calculated from crystal structures, into the models; (b) the effect of adding versus excluding melting point data from the models; (c) a larger variety of molecular descriptor permutations; (d) the use of feature selection to produce parsimonious models; (e) a novel pseudo-cross-validation protocol.</p><p id="Par88">All the different descriptors of solid state contributions (crystal structure calculated lattice energies, crystal structure based 3D descriptors, melting point data) were indicated to improve the models for at least one of the modelled endpoints for some scenarios. However, none of these descriptors was responsible for any substantial improvement in the best direct predictions of temperature dependent solubility or the best predictions of the related enthalpy of solution endpoint. This remained the case when the effect of one kind of solid state descriptors was considered in isolation. This finding is noteworthy and surprising, since the importance of the solid state contribution to both endpoints is clear from the underlying thermodynamics and, since a variety of solid state arrangements are possible for the same molecular structure, molecular descriptors are unlikely to fully capture this contribution. Indeed, it has recently been suggested that the major source of error in QSPR prediction of solubility is the failure of molecular descriptors to fully capture solid state contributions. This finding may, in part, reflect limitations in the calculated 3D descriptors and lattice energies. In the case of the lattice energies, correlation analysis with melting point data suggests that the quality of the lattice energy calculations varies across different dataset entries. Our findings may also, in part, reflect the limited availability of polymorph metadata. Both of these reasons may have contributed to molecular descriptors implicitly capturing solid state contributions to the modelled endpoints comparably to the solid state descriptors for at least some of the scenarios considered, limiting the value added by incorporating the solid state descriptors.</p><p id="Par89">Our best modelling results were typically comparable to those previously reported in the literature, albeit we cannot claim to have performed a perfectly like-for-like comparison, partly due to refinements we made to the previously modelled datasets. We found that feature selection, as applied in our work, never improved the best modelling results.</p><p id="Par90">Finally, we found that, for direct prediction of temperature dependent solubility data, standard cross-validation protocols tend to overestimate the performance of models designed to predict temperature dependent solubility for untested materials, by allowing solubility data points measured for the same material at slightly different temperatures to be placed in corresponding training and test sets. Hence, we recommend the use of our novel pseudo-cross-validation protocol, which avoids including data points measured for the same material at different temperatures in corresponding training and test sets.</p></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec43"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="13321_2018_298_MOESM1_ESM.pdf"><caption><p><bold>Additional file 1.</bold> Extensions of the Methods and Data section (section A), step-by-step instructions for reproducing our results using the datasets and source code we have made available (section B) and detailed comparisons to results reported in the literature, along with further details regarding our results (Section C).</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="13321_2018_298_MOESM2_ESM.zip"><caption><p><bold>Additional file 2.</bold> All QSPR ready datasets, prior to feature selection, associated with all considered sets of descriptors. N.B. In the case of the temperature (T) values, these were replaced with (1/T) using additional scripts prior to both feature selection and modelling.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="13321_2018_298_MOESM3_ESM.zip"><caption><p><bold>Additional file 3.</bold> Additional results files, in electronic format. These additional results are (a) SUB-48 calculated lattice energies for the complete set and filtered set of 27 crystal structures, (b) results from analysis of the correspondence between our temperature dependent solubility data and (1/T), (c) Excel workbooks documenting all R<sup>2</sup> and RMSE values obtained from cross-validation, their mean values and the <italic>p</italic> values (both raw and adjusted) obtained from pairwise comparisons of corresponding models, (d) comparison of melting point data used for the melting point descriptor and retrieved from the CSD for linked refcodes.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="13321_2018_298_MOESM4_ESM.zip"><caption><p><bold>Additional file 4.</bold> The &#x0201c;QSPR Data Processing Tools Suite&#x0201d;, used by the scripts employed to generate all QSPR results.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="13321_2018_298_MOESM5_ESM.zip"><caption><p><bold>Additional file 5.</bold> An automated test suite for &#x0201c;QSPR Data Processing Tools Suite&#x0201d;.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="13321_2018_298_MOESM6_ESM.zip"><caption><p><bold>Additional file 6.</bold> The &#x0201c;Feature Selection Tool&#x0201d;.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="13321_2018_298_MOESM7_ESM.zip"><caption><p><bold>Additional file 7.</bold> The Perl script used for Materials Studio lattice energy calculations, along with the Python scripts used to generate CIF input files from CSD refcodes, including filtering, as well as the Python scripts used for evaluation of the lattice energy protocol on the SUB-48 dataset.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="13321_2018_298_MOESM8_ESM.zip"><caption><p><bold>Additional file 8.</bold> The curated temperature dependent solubility and enthalpy of solution datasets, including the calculated lattice energies, and the SUB-48 dataset.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="13321_2018_298_MOESM9_ESM.zip"><caption><p><bold>Additional file 9.</bold> All scripts used to generate QSPR input files, modelling results and perform analysis.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM10"><media xlink:href="13321_2018_298_MOESM10_ESM.zip"><caption><p><bold>Additional file 10.</bold> SMARTS patterns used by one of the QSPR input file scripts (see Additional file <xref rid="MOESM1" ref-type="media">1</xref>), which were adapted from the Rdkit documentation [56].</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>RFR</term><def><p id="Par3">random forest regression</p></def></def-item><def-item><term>MLR</term><def><p id="Par4">multiple linear regression</p></def></def-item><def-item><term>API</term><def><p id="Par5">active pharmaceutical ingredient</p></def></def-item><def-item><term>Python API</term><def><p id="Par6">Python Application Programming Interface</p></def></def-item></def-list></glossary><ack><title>Authors&#x02019; contributions</title><p>The manuscript was written through contributions of all authors. All authors have given approval to the final version of the manuscript. K.R. proposed investigating temperature dependent solubility. R.L.M.R. prepared the curated datasets, designed the modelling and analysis workflows in consultation with E.M., wrote the &#x0201c;QSPR Data Processing Tools Suite&#x0201d;, all Python and R scripts, the final version of the Perl script for lattice energies, the final version of the &#x0201c;Feature Selection Tool&#x0201d; and the first draft of the paper. All authors read and approved the final manuscript.</p><sec id="FPar1"><title>Acknowledgements</title><p id="Par91">Dr. Alex Avdeef (in-ADME Research) and Dr. Kyrylo Klimenko (Universit&#x000e9; de Strasbourg) are thanked for very helpful correspondence regarding their published work. Dr. Anatoly Artemenko (O.V. Bogatsky Physico-Chemical Institute National Academy of Science of Ukraine) is thanked for providing a copy of the HiT-QSAR software. Dr. James McDonagh (IBM research UK) and Dr. John Mitchell (University of St. Andrews) are thanked for providing a copy of the SUB-48 dataset in electronic form. Dr. Andrew Maloney (Cambridge Crystallographic Data Centre) is thanked for providing guidance on identifying CSD entries with one molecule in the asymmetric unit and for assisting with the identification of racemic crystals in the CSD. Jakub Janowiak (University of Leeds) is thanked for providing assistance with the curation of polymorph descriptions and melting point data from the Handbook of Aqueous Solubility (2nd edition). Alex Moldovan (University of Leeds) is thanked for providing assistance with scripting for Materials Studio. Dr. Ernest Chow (Pfizer), Dr. Klimentina Pencheva (Pfizer), Dr. Jian-Jie Liang (Biovia) and Dr. Carsten Menke (Biovia) are thanked for guidance regarding the use of Materials Studio. Dr. Rebecca Mackenzie (Science and Technology Facilities Council) is thanked for writing feature selection code which was adapted for use in the current work. Chris Morris (Science and Technology Facilities Council) is thanked for his input to the design of the feature selection algorithm and for useful discussions which improved the analysis and presentation of results in this manuscript. Dr. Dawn Geatches (Science and Technology Facilities Council) is thanked for her contribution to scripting. We also gratefully acknowledge the funding provided from the &#x02018;Advanced Digital Design of Pharmaceutical Therapeutics&#x02019; (ADDoPT) project, funded by the UK&#x02019;s Advanced Manufacturing Supply Chain Initiative (AMSCI).</p></sec><sec id="FPar2"><title>Competing interests</title><p id="Par92">The authors declare that they have no competing interests.</p></sec><sec id="FPar3"><title>Availability of data and materials</title><p id="Par93">The datasets supporting the conclusions of this article are included within the article and its Additional files. The curated datasets, QSPR ready datasets containing descriptor values, the &#x0201c;QSPR Data Processing Tools Suite&#x0201d; and &#x0201c;Feature Selection Tool&#x0201d;, along with all relevant scripts required to reproduce our results, have been made available as ZIP archives under &#x0201c;Additional files&#x0201d; for this article. As documented in the LICENSE.txt file assigned within each archive, all datasets have been made available under the terms of the Creative Commons Attribution License (<ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/2.0/uk/">https://creativecommons.org/licenses/by/2.0/uk/</ext-link>) and all source code files are made available under the terms of the BSD Open Source license (<ext-link ext-link-type="uri" xlink:href="https://opensource.org/licenses/BSD-3-Clause">https://opensource.org/licenses/BSD-3-Clause</ext-link>). In addition, an automated test suite for the &#x0201c;QSPR Data Processing Tools Suite&#x0201d; has been made available under the terms of the GPL Open Source license (<ext-link ext-link-type="uri" xlink:href="https://www.gnu.org/licenses/gpl-3.0.en.html">https://www.gnu.org/licenses/gpl-3.0.en.html</ext-link>). Adaptations of SMARTS patterns taken from the Rdkit [<xref ref-type="bibr" rid="CR56">56</xref>] online documentation are made available under the terms of the Creative Commons Attribution-ShareAlike License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by-sa/4.0/">http://creativecommons.org/licenses/by-sa/4.0/</ext-link>).</p></sec><sec id="FPar4"><title>Funding</title><p id="Par94">We gratefully acknowledge the funding provided from the &#x02018;Advanced Digital Design of Pharmaceutical Therapeutics&#x02019; (ADDoPT) project, funded by the UK&#x02019;s Advanced Manufacturing Supply Chain Initiative (AMSCI).</p></sec><sec id="FPar5"><title>Publisher&#x02019;s Note</title><p id="Par95">Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Skyner</surname><given-names>RE</given-names></name><name><surname>McDonagh</surname><given-names>JL</given-names></name><name><surname>Groom</surname><given-names>CR</given-names></name><etal/></person-group><article-title>A review of methods for the calculation of solution free energies and the modelling of systems in solution</article-title><source>Phys Chem Chem Phys</source><year>2015</year><volume>17</volume><fpage>6174</fpage><lpage>6191</lpage><pub-id pub-id-type="doi">10.1039/C5CP00288E</pub-id><pub-id pub-id-type="pmid">25660403</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Palmer</surname><given-names>DS</given-names></name><name><surname>Mitchell</surname><given-names>JBO</given-names></name></person-group><article-title>Is experimental data quality the limiting factor in predicting the aqueous solubility of druglike molecules?</article-title><source>Mol Pharma</source><year>2014</year><volume>11</volume><fpage>2962</fpage><lpage>2972</lpage><pub-id pub-id-type="doi">10.1021/mp500103r</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Salahinejad</surname><given-names>M</given-names></name><name><surname>Le</surname><given-names>TC</given-names></name><name><surname>Winkler</surname><given-names>DA</given-names></name></person-group><article-title>Aqueous solubility prediction: do crystal lattice interactions help?</article-title><source>Mol Pharm</source><year>2013</year><volume>10</volume><fpage>2757</fpage><lpage>2766</lpage><pub-id pub-id-type="doi">10.1021/mp4001958</pub-id><pub-id pub-id-type="pmid">23718811</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Muller</surname><given-names>FL</given-names></name><name><surname>Fielding</surname><given-names>M</given-names></name><name><surname>Black</surname><given-names>S</given-names></name></person-group><article-title>A practical approach for using solubility to design cooling crystallisations</article-title><source>Org Process Res Dev</source><year>2009</year><volume>13</volume><fpage>1315</fpage><lpage>1321</lpage><pub-id pub-id-type="doi">10.1021/op9001438</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kristensen</surname><given-names>HG</given-names></name><name><surname>Schaefer</surname><given-names>T</given-names></name></person-group><article-title>Granulation: a review on pharmaceutical wet-granulation</article-title><source>Drug Dev Ind Pharm</source><year>1987</year><volume>13</volume><fpage>803</fpage><lpage>872</lpage><pub-id pub-id-type="doi">10.3109/03639048709105217</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><mixed-citation publication-type="other">Pharmaceutical Granulation|Ensuring Better Control of Granulation|Pharmaceutical Manufacturing. <ext-link ext-link-type="uri" xlink:href="http://www.pharmamanufacturing.com/articles/2008/096/">http://www.pharmamanufacturing.com/articles/2008/096/</ext-link>. Accessed 13 June 2017</mixed-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Papaioannou</surname><given-names>V</given-names></name><name><surname>Lafitte</surname><given-names>T</given-names></name><name><surname>Avenda&#x000f1;o</surname><given-names>C</given-names></name><etal/></person-group><article-title>Group contribution methodology based on the statistical associating fluid theory for heteronuclear molecules formed from Mie segments</article-title><source>J Chem Phys</source><year>2014</year><volume>140</volume><fpage>54107</fpage><pub-id pub-id-type="doi">10.1063/1.4851455</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Klamt</surname><given-names>A</given-names></name></person-group><article-title>The COSMO and COSMO-RS solvation models</article-title><source>WIREs Comput Mol Sci</source><year>2011</year><volume>1</volume><fpage>699</fpage><lpage>709</lpage><pub-id pub-id-type="doi">10.1002/wcms.56</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Klamt</surname><given-names>A</given-names></name><name><surname>Sch&#x000fc;&#x000fc;rmann</surname><given-names>G</given-names></name></person-group><article-title>COSMO: a new approach to dielectric screening in solvents with explicit expressions for the screening energy and its gradient</article-title><source>J Chem Soc, Perkin Trans</source><year>1993</year><volume>2</volume><fpage>799</fpage><lpage>805</lpage><pub-id pub-id-type="doi">10.1039/P29930000799</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Palmer</surname><given-names>DS</given-names></name><name><surname>McDonagh</surname><given-names>JL</given-names></name><name><surname>Mitchell</surname><given-names>JBO</given-names></name><etal/></person-group><article-title>First-principles calculation of the intrinsic aqueous solubility of crystalline druglike molecules</article-title><source>J Chem Theory Comput</source><year>2012</year><volume>8</volume><fpage>3322</fpage><lpage>3337</lpage><pub-id pub-id-type="doi">10.1021/ct300345m</pub-id><pub-id pub-id-type="pmid">26605739</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Misin</surname><given-names>M</given-names></name><name><surname>Fedorov</surname><given-names>MV</given-names></name><name><surname>Palmer</surname><given-names>DS</given-names></name></person-group><article-title>Communication: accurate hydration free energies at a wide range of temperatures from 3D-RISM</article-title><source>J Chem Phys</source><year>2015</year><volume>142</volume><fpage>91105</fpage><pub-id pub-id-type="doi">10.1063/1.4914315</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><mixed-citation publication-type="other">PSE: Events&#x02014;Webinars&#x02014;1609 gSAFT Series. <ext-link ext-link-type="uri" xlink:href="https://www.psenterprise.com/events/webinars/2016/16-09-gsaft-series">https://www.psenterprise.com/events/webinars/2016/16-09-gsaft-series</ext-link>. Accessed 13 June 2017</mixed-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kholod</surname><given-names>YA</given-names></name><name><surname>Gryn&#x02019;ova</surname><given-names>G</given-names></name><name><surname>Gorb</surname><given-names>L</given-names></name><etal/></person-group><article-title>Evaluation of the dependence of aqueous solubility of nitro compounds on temperature and salinity: a COSMO-RS simulation</article-title><source>Chemosphere</source><year>2011</year><volume>83</volume><fpage>287</fpage><lpage>294</lpage><pub-id pub-id-type="doi">10.1016/j.chemosphere.2010.12.065</pub-id><pub-id pub-id-type="pmid">21215986</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Klimenko</surname><given-names>K</given-names></name><name><surname>Kuz&#x02019;min</surname><given-names>V</given-names></name><name><surname>Ognichenko</surname><given-names>L</given-names></name><etal/></person-group><article-title>Novel enhanced applications of QSPR models: temperature dependence of aqueous solubility</article-title><source>J Comput Chem</source><year>2016</year><volume>37</volume><fpage>2045</fpage><lpage>2051</lpage><pub-id pub-id-type="doi">10.1002/jcc.24424</pub-id><pub-id pub-id-type="pmid">27338156</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Khayamian</surname><given-names>T</given-names></name><name><surname>Esteki</surname><given-names>M</given-names></name></person-group><article-title>Prediction of solubility for polycyclic aromatic hydrocarbons in supercritical carbon dioxide using wavelet neural networks in quantitative structure property relationship</article-title><source>J Supercrit Fluids</source><year>2004</year><volume>32</volume><fpage>73</fpage><lpage>78</lpage><pub-id pub-id-type="doi">10.1016/j.supflu.2004.02.003</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tabaraki</surname><given-names>R</given-names></name><name><surname>Khayamian</surname><given-names>T</given-names></name><name><surname>Ensafi</surname><given-names>AA</given-names></name></person-group><article-title>Wavelet neural network modeling in QSPR for prediction of solubility of 25 anthraquinone dyes at different temperatures and pressures in supercritical carbon dioxide</article-title><source>J Mol Graph Model</source><year>2006</year><volume>25</volume><fpage>46</fpage><lpage>54</lpage><pub-id pub-id-type="doi">10.1016/j.jmgm.2005.10.012</pub-id><pub-id pub-id-type="pmid">16337156</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Avdeef</surname><given-names>A</given-names></name></person-group><article-title>Solubility temperature dependence predicted from 2D structure</article-title><source>ADMET &#x00026; DMPK</source><year>2015</year></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rosbottom</surname><given-names>I</given-names></name><name><surname>Ma</surname><given-names>CY</given-names></name><name><surname>Turner</surname><given-names>TD</given-names></name><etal/></person-group><article-title>Influence of solvent composition on the crystal morphology and structure of <italic>p</italic>-Aminobenzoic acid crystallized from mixed ethanol and nitromethane solutions</article-title><source>Cryst Growth Des</source><year>2017</year><volume>17</volume><fpage>4151</fpage><lpage>4161</lpage><pub-id pub-id-type="doi">10.1021/acs.cgd.7b00425</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Prankerd</surname><given-names>RJ</given-names></name></person-group><article-title>Solid-state properties of drugs. I. Estimation of heat capacities for fusion and thermodynamic functions for solution from aqueous solubility-temperature dependence measurements</article-title><source>Int J Pharm</source><year>1992</year><volume>84</volume><fpage>233</fpage><lpage>244</lpage><pub-id pub-id-type="doi">10.1016/0378-5173(92)90161-T</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Prankerd</surname><given-names>RJ</given-names></name><name><surname>McKeown</surname><given-names>RH</given-names></name></person-group><article-title>Physico-chemical properties of barbituric acid derivatives Part I. Solubility-temperature dependence for 5,5-disubstituted barbituric acids in aqueous solutions</article-title><source>Int J Pharm</source><year>1990</year><volume>62</volume><fpage>37</fpage><lpage>52</lpage><pub-id pub-id-type="doi">10.1016/0378-5173(90)90029-4</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><mixed-citation publication-type="other">Jensen F (2007) Chapter 17: statistics and QSAR. In: Introduction to computational chemistry, 2nd edn. Wiley, New York, pp 547&#x02013;561</mixed-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Strobl</surname><given-names>C</given-names></name><name><surname>Malley</surname><given-names>J</given-names></name><name><surname>Tutz</surname><given-names>G</given-names></name></person-group><article-title>An introduction to recursive partitioning: rationale, application and characteristics of classification and regression trees, bagging and random forests</article-title><source>Psychol Methods</source><year>2009</year><volume>14</volume><fpage>323</fpage><lpage>348</lpage><pub-id pub-id-type="doi">10.1037/a0016973</pub-id><pub-id pub-id-type="pmid">19968396</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Svetnik</surname><given-names>V</given-names></name><name><surname>Liaw</surname><given-names>A</given-names></name><name><surname>Tong</surname><given-names>C</given-names></name><etal/></person-group><article-title>Random forest: a classification and regression tool for compound classification and QSAR modeling</article-title><source>J Chem Inf Comput Sci</source><year>2003</year><volume>43</volume><fpage>1947</fpage><lpage>1958</lpage><pub-id pub-id-type="doi">10.1021/ci034160g</pub-id><pub-id pub-id-type="pmid">14632445</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Breiman</surname><given-names>L</given-names></name></person-group><article-title>Random forests</article-title><source>Mach Learn</source><year>2001</year><volume>45</volume><fpage>5</fpage><lpage>32</lpage><pub-id pub-id-type="doi">10.1023/A:1010933404324</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><mixed-citation publication-type="other">Lang AS, Bradley J-C QDB archive #104. QsarDB repository. <pub-id pub-id-type="doi">10.15152/QDB.104</pub-id>. Accessed 20 July 2017</mixed-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Docherty</surname><given-names>R</given-names></name><name><surname>Pencheva</surname><given-names>K</given-names></name><name><surname>Abramov</surname><given-names>YA</given-names></name></person-group><article-title>Low solubility in drug development: de-convoluting the relative importance of solvation and crystal packing</article-title><source>J Pharm Pharmacol</source><year>2015</year><volume>67</volume><fpage>847</fpage><lpage>856</lpage><pub-id pub-id-type="doi">10.1111/jphp.12393</pub-id><pub-id pub-id-type="pmid">25880016</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Emami</surname><given-names>S</given-names></name><name><surname>Jouyban</surname><given-names>A</given-names></name><name><surname>Valizadeh</surname><given-names>H</given-names></name><name><surname>Shayanfar</surname><given-names>A</given-names></name></person-group><article-title>Are crystallinity parameters critical for drug solubility prediction?</article-title><source>J Solut Chem</source><year>2015</year><volume>44</volume><fpage>2297</fpage><lpage>2315</lpage><pub-id pub-id-type="doi">10.1007/s10953-015-0410-5</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Abramov</surname><given-names>YA</given-names></name></person-group><article-title>Major source of error in QSPR prediction of intrinsic thermodynamic solubility of drugs: solid vs nonsolid state contributions?</article-title><source>Mol Pharm</source><year>2015</year><volume>12</volume><fpage>2126</fpage><lpage>2141</lpage><pub-id pub-id-type="doi">10.1021/acs.molpharmaceut.5b00119</pub-id><pub-id pub-id-type="pmid">25880026</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>McDonagh</surname><given-names>JL</given-names></name><name><surname>Palmer</surname><given-names>DS</given-names></name><name><surname>van Mourik</surname><given-names>T</given-names></name><name><surname>Mitchell</surname><given-names>JBO</given-names></name></person-group><article-title>Are the sublimation thermodynamics of organic molecules predictable?</article-title><source>J Chem Inf Model</source><year>2016</year><volume>56</volume><fpage>2162</fpage><lpage>2179</lpage><pub-id pub-id-type="doi">10.1021/acs.jcim.6b00033</pub-id><pub-id pub-id-type="pmid">27749062</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>McDonagh</surname><given-names>JL</given-names></name><name><surname>Nath</surname><given-names>N</given-names></name><name><surname>De Ferrari</surname><given-names>L</given-names></name><etal/></person-group><article-title>Uniting cheminformatics and chemical theory to predict the intrinsic aqueous solubility of crystalline druglike molecules</article-title><source>J Chem Inf Model</source><year>2014</year><volume>54</volume><fpage>844</fpage><lpage>856</lpage><pub-id pub-id-type="doi">10.1021/ci4005805</pub-id><pub-id pub-id-type="pmid">24564264</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><mixed-citation publication-type="other">NCI/CADD Chemical Identifier Resolver. <ext-link ext-link-type="uri" xlink:href="https://cactus.nci.nih.gov/chemical/structure">https://cactus.nci.nih.gov/chemical/structure</ext-link>. Accessed 21 July 2017</mixed-citation></ref><ref id="CR32"><label>32.</label><mixed-citation publication-type="other">ChemSpider | Search and share chemistry. <ext-link ext-link-type="uri" xlink:href="http://www.chemspider.com/">http://www.chemspider.com/</ext-link>. Accessed 21 July 2017</mixed-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kim</surname><given-names>S</given-names></name><name><surname>Thiessen</surname><given-names>PA</given-names></name><name><surname>Bolton</surname><given-names>EE</given-names></name><etal/></person-group><article-title>PubChem substance and compound databases</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>44</volume><fpage>D1202</fpage><lpage>D1213</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv951</pub-id><pub-id pub-id-type="pmid">26400175</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><mixed-citation publication-type="other">The PubChem Project. <ext-link ext-link-type="uri" xlink:href="http://pubchem.ncbi.nlm.nih.gov/">http://pubchem.ncbi.nlm.nih.gov/</ext-link>. Accessed 24 Nov 2011</mixed-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Groom</surname><given-names>CR</given-names></name><name><surname>Bruno</surname><given-names>IJ</given-names></name><name><surname>Lightfoot</surname><given-names>MP</given-names></name><name><surname>Ward</surname><given-names>SC</given-names></name></person-group><article-title>The Cambridge structural database</article-title><source>Acta Cryst B, Acta Cryst Sect B, Acta Crystallogr B, Acta Crystallogr Sect B, Acta Crystallogr B Struct Crystallogr Cryst Chem, Acta Crystallogr Sect B Struct Crystallogr Cryst Chem</source><year>2016</year><volume>72</volume><fpage>171</fpage><lpage>179</lpage></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sun</surname><given-names>H</given-names></name></person-group><article-title>COMPASS: an ab initio force-field optimized for condensed-phase applications overview with details on alkane and benzene compounds</article-title><source>J Phys Chem B</source><year>1998</year><volume>102</volume><fpage>7338</fpage><lpage>7364</lpage><pub-id pub-id-type="doi">10.1021/jp980939v</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sun</surname><given-names>H</given-names></name><name><surname>Ren</surname><given-names>P</given-names></name><name><surname>Fried</surname><given-names>JR</given-names></name></person-group><article-title>The COMPASS force field: parameterization and validation for phosphazenes</article-title><source>Comput Theor Polym Sci</source><year>1998</year><volume>8</volume><fpage>229</fpage><lpage>246</lpage><pub-id pub-id-type="doi">10.1016/S1089-3156(98)00042-7</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rigby</surname><given-names>D</given-names></name><name><surname>Sun</surname><given-names>H</given-names></name><name><surname>Eichinger</surname><given-names>BE</given-names></name></person-group><article-title>Computer simulations of poly(ethylene oxide): force field, PVT diagram and cyclization behaviour</article-title><source>Polym Int</source><year>1997</year><volume>44</volume><fpage>311</fpage><lpage>330</lpage><pub-id pub-id-type="doi">10.1002/(SICI)1097-0126(199711)44:3&#x0003c;311::AID-PI880&#x0003e;3.0.CO;2-H</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stanton</surname><given-names>DT</given-names></name><name><surname>Jurs</surname><given-names>PC</given-names></name></person-group><article-title>Development and use of charged partial surface area structural descriptors in computer-assisted quantitative structure-property relationship studies</article-title><source>Anal Chem</source><year>1990</year><volume>62</volume><fpage>2323</fpage><lpage>2329</lpage><pub-id pub-id-type="doi">10.1021/ac00220a013</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stanton</surname><given-names>DT</given-names></name><name><surname>Dimitrov</surname><given-names>S</given-names></name><name><surname>Grancharov</surname><given-names>V</given-names></name><name><surname>Mekenyan</surname><given-names>OG</given-names></name></person-group><article-title>Charged partial surface area (CPSA) descriptors QSAR applications</article-title><source>SAR QSAR Environ Res</source><year>2002</year><volume>13</volume><fpage>341</fpage><lpage>351</lpage><pub-id pub-id-type="doi">10.1080/10629360290002811</pub-id><pub-id pub-id-type="pmid">12071660</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><mixed-citation publication-type="other">Mordred CPSA Module Documentation. <ext-link ext-link-type="uri" xlink:href="http://mordred-descriptor.github.io/documentation/master/api/mordred.CPSA.html">http://mordred-descriptor.github.io/documentation/master/api/mordred.CPSA.html</ext-link>. Accessed 11 June 2018</mixed-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Moriwaki</surname><given-names>H</given-names></name><name><surname>Tian</surname><given-names>Y-S</given-names></name><name><surname>Kawashita</surname><given-names>N</given-names></name><name><surname>Takagi</surname><given-names>T</given-names></name></person-group><article-title>Mordred: a molecular descriptor calculator</article-title><source>J Cheminform</source><year>2018</year><volume>10</volume><fpage>4</fpage><pub-id pub-id-type="doi">10.1186/s13321-018-0258-y</pub-id><pub-id pub-id-type="pmid">29411163</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><mixed-citation publication-type="other">(2018) Mordred: a molecular descriptor calculator (version 1.0.0). <ext-link ext-link-type="uri" xlink:href="https://github.com/mordred-descriptor/mordred">https://github.com/mordred-descriptor/mordred</ext-link></mixed-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Riniker</surname><given-names>S</given-names></name><name><surname>Landrum</surname><given-names>GA</given-names></name></person-group><article-title>Better informed distance geometry: using what we know to improve conformation generation</article-title><source>J Chem Inf Model</source><year>2015</year><volume>55</volume><fpage>2562</fpage><lpage>2574</lpage><pub-id pub-id-type="doi">10.1021/acs.jcim.5b00654</pub-id><pub-id pub-id-type="pmid">26575315</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><mixed-citation publication-type="other">RDKit (version 2017.03.1). <ext-link ext-link-type="uri" xlink:href="http://www.rdkit.org/">http://www.rdkit.org/</ext-link>. Accessed 25 July 2017</mixed-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rappe</surname><given-names>AK</given-names></name><name><surname>Casewit</surname><given-names>CJ</given-names></name><name><surname>Colwell</surname><given-names>KS</given-names></name><etal/></person-group><article-title>UFF, a full periodic table force field for molecular mechanics and molecular dynamics simulations</article-title><source>J Am Chem Soc</source><year>1992</year><volume>114</volume><fpage>10024</fpage><lpage>10035</lpage><pub-id pub-id-type="doi">10.1021/ja00051a040</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alexander</surname><given-names>DLJ</given-names></name><name><surname>Tropsha</surname><given-names>A</given-names></name><name><surname>Winkler</surname><given-names>DA</given-names></name></person-group><article-title>Beware of R2: simple, unambiguous assessment of the prediction accuracy of QSAR and QSPR models</article-title><source>J Chem Inf Model</source><year>2015</year><volume>55</volume><fpage>1316</fpage><lpage>1322</lpage><pub-id pub-id-type="doi">10.1021/acs.jcim.5b00206</pub-id><pub-id pub-id-type="pmid">26099013</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Perlovich</surname><given-names>GL</given-names></name></person-group><article-title>Poorly soluble drugs: disbalance of thermodynamic characteristics of crystal lattice and solvation</article-title><source>RSC Adv</source><year>2016</year><volume>6</volume><fpage>77870</fpage><lpage>77886</lpage><pub-id pub-id-type="doi">10.1039/C6RA14333D</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Palmer</surname><given-names>DS</given-names></name><name><surname>Llin&#x000e0;s</surname><given-names>A</given-names></name><name><surname>Morao</surname><given-names>I</given-names></name><etal/></person-group><article-title>Predicting intrinsic aqueous solubility by a thermodynamic cycle</article-title><source>Mol Pharm</source><year>2008</year><volume>5</volume><fpage>266</fpage><lpage>279</lpage><pub-id pub-id-type="doi">10.1021/mp7000878</pub-id><pub-id pub-id-type="pmid">18290628</pub-id></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nyman</surname><given-names>J</given-names></name><name><surname>Day</surname><given-names>GM</given-names></name></person-group><article-title>Static and lattice vibrational energy differences between polymorphs</article-title><source>CrystEngComm</source><year>2015</year><volume>17</volume><fpage>5154</fpage><lpage>5165</lpage><pub-id pub-id-type="doi">10.1039/C5CE00045A</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Abramov</surname><given-names>YA</given-names></name><name><surname>Pencheva</surname><given-names>K</given-names></name></person-group><person-group person-group-type="editor"><name><surname>Ende</surname><given-names>DJ</given-names></name></person-group><article-title>Thermodynamics and relative solubility prediction of polymorphic systems</article-title><source>Chemical engineering in the pharmaceutical industry</source><year>2010</year><publisher-loc>New York</publisher-loc><publisher-name>Wiley</publisher-name><fpage>477</fpage><lpage>490</lpage></element-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huang</surname><given-names>L-F</given-names></name><name><surname>Tong</surname><given-names>W-Q</given-names></name></person-group><article-title>Impact of solid state properties on developability assessment of drug candidates</article-title><source>Adv Drug Deliv Rev</source><year>2004</year><volume>56</volume><fpage>321</fpage><lpage>334</lpage><pub-id pub-id-type="doi">10.1016/j.addr.2003.10.007</pub-id><pub-id pub-id-type="pmid">14962584</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Salahinejad</surname><given-names>M</given-names></name><name><surname>Le</surname><given-names>TC</given-names></name><name><surname>Winkler</surname><given-names>DA</given-names></name></person-group><article-title>Capturing the crystal: prediction of enthalpy of sublimation, crystal lattice energy, and melting points of organic compounds</article-title><source>J Chem Inf Model</source><year>2013</year><volume>53</volume><fpage>223</fpage><lpage>229</lpage><pub-id pub-id-type="doi">10.1021/ci3005012</pub-id><pub-id pub-id-type="pmid">23215043</pub-id></element-citation></ref><ref id="CR54"><label>54.</label><mixed-citation publication-type="other">CSD Python API (version 1.3.0). Quick primer to using the CSD Python API. <ext-link ext-link-type="uri" xlink:href="https://downloads.ccdc.cam.ac.uk/documentation/API/descriptive_docs/primer.html">https://downloads.ccdc.cam.ac.uk/documentation/API/descriptive_docs/primer.html</ext-link>. Accessed 24 July 2017</mixed-citation></ref><ref id="CR55"><label>55.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Price</surname><given-names>SL</given-names></name><name><surname>Leslie</surname><given-names>MA</given-names></name><name><surname>Welch</surname><given-names>GW</given-names></name><etal/></person-group><article-title>Modelling organic crystal structures using distributed multipole and polarizability-based model intermolecular potentials</article-title><source>Phys Chem Chem Phys</source><year>2010</year><volume>12</volume><fpage>8478</fpage><lpage>8490</lpage><pub-id pub-id-type="doi">10.1039/c004164e</pub-id><pub-id pub-id-type="pmid">20607186</pub-id></element-citation></ref><ref id="CR56"><label>56.</label><mixed-citation publication-type="other">RDKit (version 2016.03.1). <ext-link ext-link-type="uri" xlink:href="http://www.rdkit.org/">http://www.rdkit.org/</ext-link>. Accessed 25 July 2017</mixed-citation></ref></ref-list></back></article>