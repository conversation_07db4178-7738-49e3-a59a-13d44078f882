<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7119163</article-id><article-id pub-id-type="pmid">32245365</article-id><article-id pub-id-type="publisher-id">3415</article-id><article-id pub-id-type="doi">10.1186/s12859-020-3415-z</article-id><article-categories><subj-group subj-group-type="heading"><subject>Methodology Article</subject></subj-group></article-categories><title-group><article-title>Visualizing metabolic network dynamics through time-series metabolomic data</article-title></title-group><contrib-group><contrib contrib-type="author" equal-contrib="yes"><name><surname>Buchweitz</surname><given-names>Lea F.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" equal-contrib="yes"><name><surname>Yurkovich</surname><given-names>James T.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Blessing</surname><given-names>Christoph</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Kohler</surname><given-names>Veronika</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Schwarzkopf</surname><given-names>Fabian</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid">https://orcid.org/0000-0003-1238-1499</contrib-id><name><surname>King</surname><given-names>Zachary A.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff5">5</xref><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid">https://orcid.org/0000-0001-6663-7643</contrib-id><name><surname>Yang</surname><given-names>Laurence</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff7">7</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid">https://orcid.org/0000-0001-6460-2463</contrib-id><name><surname>J&#x000f3;hannsson</surname><given-names>Freyr</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff8">8</xref></contrib><contrib contrib-type="author"><name><surname>Sigurj&#x000f3;nsson</surname><given-names>&#x000d3;lafur E.</given-names></name><xref ref-type="aff" rid="Aff9">9</xref><xref ref-type="aff" rid="Aff10">10</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid">https://orcid.org/0000-0003-4258-6057</contrib-id><name><surname>Rolfsson</surname><given-names>&#x000d3;ttar</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff8">8</xref></contrib><contrib contrib-type="author"><contrib-id contrib-id-type="orcid">https://orcid.org/0000-0002-5353-3914</contrib-id><name><surname>Heinrich</surname><given-names>Julian</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author" corresp="yes" equal-contrib="yes"><name><surname>Dr&#x000e4;ger</surname><given-names>Andreas</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff11">11</xref></contrib><aff id="Aff1"><label>1</label>Computational Systems Biology of Infection and Antimicrobial-Resistant Pathogens, Institute for Biomedical Informatics (IBMI), Sand 14, T&#x000fc;bingen, 72076 Germany </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="GRID">grid.64212.33</institution-id><institution-id institution-id-type="ISNI">0000 0004 0463 2320</institution-id><institution>Institute for Systems Biology, </institution></institution-wrap>401 Terry Ave. N., Seattle, 98109 WA United States </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="GRID">grid.10392.39</institution-id><institution-id institution-id-type="ISNI">0000 0001 2190 1447</institution-id><institution>Department of Computer Science, University of T&#x000fc;bingen, </institution></institution-wrap>Sand 14, T&#x000fc;bingen, 72076 Germany </aff><aff id="Aff4"><label>4</label>yWorks GmbH, Vor dem Kreuzberg 28, T&#x000fc;bingen, 72070 Germany </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="GRID">grid.266100.3</institution-id><institution-id institution-id-type="ISNI">0000 0001 2107 4242</institution-id><institution>Systems Biology Research Group, Department of Bioengineering, University of California, San Diego, </institution></institution-wrap>9500 Gilman Drive, La Jolla, CA 92093-0412 United States </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="GRID">grid.5170.3</institution-id><institution-id institution-id-type="ISNI">0000 0001 2181 8870</institution-id><institution>Novo Nordisk Foundation Center for Biosustainability, Technical University of Denmark, </institution></institution-wrap>Building 220, Kemitorvet, Kgs.Lyngby, 2800 Denmark </aff><aff id="Aff7"><label>7</label><institution-wrap><institution-id institution-id-type="GRID">grid.410356.5</institution-id><institution-id institution-id-type="ISNI">0000 0004 1936 8331</institution-id><institution>Department of Chemical Engineering, Queen&#x02019;s University, </institution></institution-wrap>Kingston, ON K7L 3N6 Canada </aff><aff id="Aff8"><label>8</label><institution-wrap><institution-id institution-id-type="GRID">grid.14013.37</institution-id><institution-id institution-id-type="ISNI">0000 0004 0640 0021</institution-id><institution>Center for Systems Biology, University of Iceland, </institution></institution-wrap>Sturlugata 8, Reykjav&#x000ed;k, 101 Iceland </aff><aff id="Aff9"><label>9</label><institution-wrap><institution-id institution-id-type="GRID">grid.410540.4</institution-id><institution-id institution-id-type="ISNI">0000 0000 9894 0842</institution-id><institution>The Blood Bank, Landsp&#x000ed;tali-University Hospital, </institution></institution-wrap>Reykjav&#x000ed;k, 101 Iceland </aff><aff id="Aff10"><label>10</label><institution-wrap><institution-id institution-id-type="GRID">grid.9580.4</institution-id><institution-id institution-id-type="ISNI">0000 0004 0643 5232</institution-id><institution>School of Science and Engineering, Reykjav&#x000ed;k University, </institution></institution-wrap>Menntavegi 1, Reykjav&#x000ed;k, 101 Iceland </aff><aff id="Aff11"><label>11</label><institution-wrap><institution-id institution-id-type="GRID">grid.452463.2</institution-id><institution>German Center for Infection Research (DZIF), partner site T&#x000fc;bingen, </institution></institution-wrap>T&#x000fc;bingen, 72076 Germany </aff></contrib-group><pub-date pub-type="epub"><day>7</day><month>7</month><year>2020</year></pub-date><pub-date pub-type="pmc-release"><day>7</day><month>7</month><year>2020</year></pub-date><pub-date pub-type="collection"><year>2020</year></pub-date><volume>21</volume><elocation-id>130</elocation-id><history><date date-type="received"><day>9</day><month>4</month><year>2019</year></date><date date-type="accepted"><day>12</day><month>2</month><year>2020</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2020</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold> This article is licensed under a Creative Commons Attribution 4.0 International License, which permits use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons licence, and indicate if changes were made. The images or other third party material in this article are included in the article&#x02019;s Creative Commons licence, unless indicated otherwise in a credit line to the material. If material is not included in the article&#x02019;s Creative Commons licence and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this licence, visit <ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated in a credit line to the data.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">New technologies have given rise to an abundance of -omics data, particularly metabolomic data. The scale of these data introduces new challenges for the interpretation and extraction of knowledge, requiring the development of innovative computational visualization methodologies. Here, we present GEM-Vis, an original method for the visualization of time-course metabolomic data within the context of metabolic network maps. We demonstrate the utility of the GEM-Vis method by examining previously published data for two cellular systems&#x02014;the human platelet and erythrocyte under cold storage for use in transfusion medicine.</p></sec><sec><title>Results</title><p id="Par2">The results comprise two animated videos that allow for new insights into the metabolic state of both cell types. In the case study of the platelet metabolome during storage, the new visualization technique elucidates a nicotinamide accumulation that mirrors that of hypoxanthine and might, therefore, reflect similar pathway usage. This visual analysis provides a possible explanation for why the salvage reactions in purine metabolism exhibit lower activity during the first few days of the storage period. The second case study displays drastic changes in specific erythrocyte metabolite pools at different times during storage at different temperatures.</p></sec><sec><title>Conclusions</title><p id="Par3">The new visualization technique GEM-Vis introduced in this article constitutes a well-suitable approach for large-scale network exploration and advances hypothesis generation. This method can be applied to any system with data and a metabolic map to promote visualization and understand physiology at the network level. More broadly, we hope that our approach will provide the blueprints for new visualizations of other longitudinal -omics data types. The supplement includes a comprehensive user&#x02019;s guide and links to a series of tutorial videos that explain how to prepare model and data files, and how to use the software SBMLsimulator in combination with further tools to create similar animations as highlighted in the case studies.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Data visualization</kwd><kwd>Metabolism</kwd><kwd>Metabolomics</kwd><kwd>Platelet</kwd><kwd>Red blood cell</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source><award-id>R01-GM070923</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source><award-id>U01-GM102098</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100009708</institution-id><institution>Novo Nordisk Fonden</institution></institution-wrap></funding-source><award-id>NNF10CC1016517</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100004103</institution-id><institution>Landsp?tali H?sk?lasj?krah?s</institution></institution-wrap></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>Institute for Systems Biology?s Translational Research Fellowship</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100006785</institution-id><institution>Google</institution></institution-wrap></funding-source><award-id>Summer of Code</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100001659</institution-id><institution>Deutsche Forschungsgemeinschaft</institution></institution-wrap></funding-source><award-id>Open Access Publishing Fund of the University of T?bingen</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution>yWorks GmbH</institution></funding-source><award-id>yFiles for Java</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2020</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>Over the last few decades, new technological developments have enabled the generation of vast amounts of &#x0201c;-omics&#x0201d; data [<xref ref-type="bibr" rid="CR1">1</xref>]. These various -omic data types have helped bring new insights to a vast array of biological questions [<xref ref-type="bibr" rid="CR2">2</xref>&#x02013;<xref ref-type="bibr" rid="CR4">4</xref>]. As more and more data are generated, however, researchers are faced with the enormous challenge of integrating, interpreting, and visualizing these data. The community has recognized these needs, focusing efforts on data visualization as a way to maximize the utility of biological data [<xref ref-type="bibr" rid="CR5">5</xref>]. Data visualization is particularly crucial for a systems-level perspective of metabolic networks and pathways. Several excellent software tools were made available for drawing and exploring biological network graphs [<xref ref-type="bibr" rid="CR6">6</xref>&#x02013;<xref ref-type="bibr" rid="CR10">10</xref>]. These tools provide impressive descriptions of the network and support for diverse analyses, including the mapping of omics data to networks. In this study, we present GEM-Vis as a new approach for the visualization of time-course metabolomic data in the context of large-scale metabolic network maps.</p><p>Metabolomic data provide snapshots of cellular biochemistry, presenting essential insights into a cell&#x02019;s metabolic state [<xref ref-type="bibr" rid="CR11">11</xref>, <xref ref-type="bibr" rid="CR12">12</xref>]. Visualization tools often allow users to overlay pathway maps with static data sets [<xref ref-type="bibr" rid="CR6">6</xref>]. Recently, time-course metabolomic data sets that detail cellular changes over time are becoming more prevalent [<xref ref-type="bibr" rid="CR13">13</xref>&#x02013;<xref ref-type="bibr" rid="CR16">16</xref>], leading to the need for dynamic visualizations that can capture the aspect of time [<xref ref-type="bibr" rid="CR17">17</xref>]&#x02014;an essential aspect of understanding complex processes such as changes in metabolic activity, concentration, or availability. Many visualization tools [<xref ref-type="bibr" rid="CR18">18</xref>&#x02013;<xref ref-type="bibr" rid="CR21">21</xref>], however, do not yet provide support for the representation of dynamic content. Those visualization tools whose features do include time series visualization [<xref ref-type="bibr" rid="CR5">5</xref>, <xref ref-type="bibr" rid="CR17">17</xref>, <xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR22">22</xref>&#x02013;<xref ref-type="bibr" rid="CR24">24</xref>] only provide static depictions of the data. Some progress has been made to provide a stepwise temporal representation of metabolomic data [<xref ref-type="bibr" rid="CR25">25</xref>], but a robust and smooth dynamic solution for mapping time series data to networks has yet to be presented.</p><p>One reason for the current lack of convincing visual analysis methods for dynamically changing data sets is that time-dependent data add additional layers of complexity to the already difficult problem of visual network exploration. First of all, genome-scale metabolic networks (GEMs) can have enormous sizes: Some published metabolic network maps comprise several thousand biochemical reactions [<xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR27">27</xref>], of which human beholders can simultaneously only grasp a very small fraction [<xref ref-type="bibr" rid="CR28">28</xref>].</p><p>With the development of new experimental technologies and the subsequent generation of -omics data sets, life scientists are faced with the challenge of extracting actionable knowledge. New visualization methods are a critical way that the community can make strides toward making the most of complex data. Here, we present a new method for the visualization of longitudinal metabolomic data in the context of the metabolic network. We provide two case studies that examine (1) a baseline characterization of a physiological process and (2) a set of experimental perturbations that allowed for a side-by-side comparison of different experimental conditions. The introduction of this new visualization method has two significant implications.</p><p>The method introduced in this article provides a dynamic visualization of cellular processes. Tools such as Cytoscape [<xref ref-type="bibr" rid="CR29">29</xref>] provide visual analysis of networks and supports plugins like TiCoNE [<xref ref-type="bibr" rid="CR18">18</xref>] and CyDataSeries [<xref ref-type="bibr" rid="CR30">30</xref>] for the visualization of time-course data. However, tools such as these or VANTED [<xref ref-type="bibr" rid="CR20">20</xref>] only offer static representations of dynamic data. To our knowledge, only KEGGanim [<xref ref-type="bibr" rid="CR25">25</xref>] offers a dynamic visualization of time-course data. The method presented here builds on KEGGanim by offering a smooth interpolation between time points and offers the further advantage of customization concerning the display of both data and the network itself. The method presented outlines an original development for visualizing complex biological data based in a way that a cognition study has found to be useful and support perception [<xref ref-type="bibr" rid="CR31">31</xref>].</p><p>With a steadily increasing number of carefully prepared metabolic network layouts being published, we here assume a map to be available for the system of interest. If this is not yet the case, a map can be easily drawn using software such as Escher [<xref ref-type="bibr" rid="CR6">6</xref>]. This paper focuses on the problem of displaying dynamically changing quantitative data of network components. The aim is to answer the question: How to create expressive visual displays of dynamic metabolic networks? Needed are strategies to visually present the data in a way that beholders can best perceive and estimate quantities of network individual components and that at the same time enable them to conceptually narrow down parts of interest even within large networks.</p><p>In the next sections, we present the new method GEM-Vis (<italic>Genome-Scale Metabolic model Visualization</italic>) for the visualization and contextualization of longitudinal metabolomic data in metabolic networks. We developed three different graphical representations of metabolic concentration that allow for different interpretations of metabolomic data through a smooth animation. The method is implemented in the freely available software SBMLsimulator. The supplementary material of this article includes links to a series of four short tutorial videos that explain all aspects needed for creating a GEM-Vis: (1) where to obtain SBMLsimulator, how to run it, (2) where to obtain systems biology models and how to load them into the application, and how to create a simulated time-course data set from the model that can be mapped to an automatically generated pathway map, (3) where to obtain a manually drawn pathway map as well as published time-course data of the yeast <italic>Saccharomyces cerevisiae</italic> and how to prepare the data set for the import to SBMLsimulator and how to embed this layout information [<xref ref-type="bibr" rid="CR32">32</xref>] in an SBML file [<xref ref-type="bibr" rid="CR33">33</xref>], (4) how to load model and data into SBMLsimulator to create a GEM-Vis animation video including variation of several visual attribues and to save it to a movie file. Finally, we present two case studies using this method that examine two different cellular systems&#x02014;the human platelet and the human red blood cell (RBC)&#x02014;to show how visualizing existing data can provide new insights into cellular metabolism. The result are two animated videos that give detailed information about the systems under study and highlight new insights that were not previously apparent. We hope that this method will aid researchers in visualizing, perceiving, and interpreting complex data sets.</p></sec><sec id="Sec2" sec-type="results"><title>Results</title><p>The idea of GEM-Vis is that time series can be adequately observed in the form of an animated sequence of a dynamically changing network map when using an appropriate representation of metabolic quantities. To this end, our technique exploits the repeatedly observed ability of human beholders to estimate quantities most precisely when these are mapped to a lengths scale [<xref ref-type="bibr" rid="CR31">31</xref>]. Since metabolic maps commonly represent nodes with circles [<xref ref-type="bibr" rid="CR6">6</xref>, <xref ref-type="bibr" rid="CR34">34</xref>], we suggest using the fill level of each node as a visual element to represent its amount at each time point. We experimented with visualization of data in several different ways, based on node size, color, a combination of size and color, or fill level (Supplementary Figure S1). Each of these visual representations provides some advantages over the others, but according to [<xref ref-type="bibr" rid="CR31">31</xref>] the notion of the fill level of a node can be the most intuitive as it allows for the user to understand and gauge its minimum or maximum value quickly (see Discussion).</p><p>Using this technique, we created such an animation for given longitudinal metabolomic data and a metabolic network map that corresponds to the observed cell type (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>). To provide a smooth animation, additional time points are interpolated in the provided time series. Further details regarding the development and use of the implementation of the method can be found in the Supplementary Information.
<fig id="Fig1"><label>Fig. 1</label><caption><p>Dynamic visualization of metabolomic data. We take metabolomic data as input and generates a dynamic animation of the data over time which enables the visualization of pool sizes for individually measured metabolites. Several different options are discussed in this article for the visualization of the data based on node size, color, and fill level. The method has been implemented in SBMLsimulator including an export function to save the resulting output in a video file. For creation of animation videos highlighted in Tables&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> and <xref rid="Tab2" ref-type="table">2</xref> post-processing steps are needed as descibed in the Supplementary Information</p></caption><graphic xlink:href="12859_2020_3415_Fig1_HTML" id="MO1"/></fig><table-wrap id="Tab1"><label>Table 1</label><caption><p>Visualization of biochemical processes &#x02013; storage of platelets <inline-graphic xlink:href="12859_2020_3415_Figa_HTML.gif" id="d30e776"/> 8 min 26 s</p></caption><table frame="hsides" rules="groups"><tbody><tr><td align="left">This video introduces a new method for visualizing metabolic processes in the context of a full biochemical network. Representing the metabolic network as a graph where metabolites are nodes and reactions are edges can help elucidate complex relationships within the network. While viewing a network in this manner is not new, overlaying -omics data onto the map allows for an accurate integration of disparate data types. By visually interpreting the information in this dynamic, graphical format, we can more easily distinguish important characteristics of the network. This video utilizes the metabolomic data from the study &#x0201c;Comprehensive metabolomic study of platelets reveals the expression of discrete metabolic phenotypes during storage&#x0201d; [<xref ref-type="bibr" rid="CR13">13</xref>]. <ext-link ext-link-type="uri" xlink:href="https://youtu.be/GQuT7R-ldS4">https://youtu.be/ GQuT7R-ldS4</ext-link></td><td align="left"/></tr></tbody></table></table-wrap><table-wrap id="Tab2"><label>Table 2</label><caption><p>Visualization of biochemical processes &#x02013; temperature dependence of red blood cells <inline-graphic xlink:href="12859_2020_3415_Figb_HTML.gif" id="d30e802"/> 1 min 33 s</p></caption><table frame="hsides" rules="groups"><tbody><tr><td align="left">This video visually compares the biochemical effects of increasing the storage temperature from (4<sup>&#x02218;</sup>C to 13<sup>&#x02218;</sup>C) of stored RBCs on metabolic processes. The relative node size shows changes in metabolite concentrations for each measured metabolite. Zooming in on various parts of the network helps visualize how specific metabolite pools undergo more drastic changes at different points during storage. This video utilizes the metabolomic data from the study &#x0201c;Quantitative time-course metabolomic in human red blood cells reveal the temperature dependence of human metabolic networks&#x0201d; [<xref ref-type="bibr" rid="CR14">14</xref>]. <ext-link ext-link-type="uri" xlink:href="https://youtu.be/0INItST4FQc">https://youtu.be/0INItST4FQc</ext-link></td><td align="left"/></tr></tbody></table></table-wrap></p><p>To demonstrate the utility of this method, we applied these visualization methods to four different cellular systems&#x02014;human hepatocytes [<xref ref-type="bibr" rid="CR35">35</xref>], platelets [<xref ref-type="bibr" rid="CR36">36</xref>] and RBCs [<xref ref-type="bibr" rid="CR37">37</xref>], as well as to yeast [<xref ref-type="bibr" rid="CR38">38</xref>]. For the two human blood cell types and for yeast longitudinal quantitative data sets were available in the literature [<xref ref-type="bibr" rid="CR13">13</xref>, <xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR39">39</xref>]. Consequently, all four models provide very different use-case scenarios. Since the hepatocyte model [<xref ref-type="bibr" rid="CR35">35</xref>] is a fully-specified kinetic model and available in SBML format from BioModels database [<xref ref-type="bibr" rid="CR40">40</xref>], it is well suitable to demonstrate how simulated data can be generated and visualized in the context of an algorithmically generated network (see Additional file&#x000a0;<xref rid="MOESM11" ref-type="media">11</xref>). The genome-scale model of yeast [<xref ref-type="bibr" rid="CR38">38</xref>] can be downloaded in SBML and JSON format from BiGG Models Database [<xref ref-type="bibr" rid="CR41">41</xref>], where it comes with a manually drawn network of the organism&#x02019;s central carbon metabolism. It is, therefore, usable to demonstrate mapping a published time-course metabolomic data set [<xref ref-type="bibr" rid="CR39">39</xref>] in the context of a hand-made pathway map (see Additional file&#x000a0;<xref rid="MOESM13" ref-type="media">13</xref>).</p><p>After gaining experience in working with the visualization method, the focus will be applying the GEM-Vis method to study human blood cells in more detail. Transfusion medicine plays a vital role in modern healthcare, making the storage of different blood components important physiological processes to understand. In particular, platelets and RBCs represent relatively simple human cell types that can be intensely studied in the well-defined, static environment provided by blood storage (packed in plastic bags and stored at 22<sup>&#x02218;</sup>C and 4<sup>&#x02218;</sup>C for platelets and RBCs, respectively). While the cells are stored in these conditions, biochemical and morphological changes occur (the &#x0201c;storage lesion&#x0201d;) that are well-studied through the use of metabolomic data [<xref ref-type="bibr" rid="CR12">12</xref>, <xref ref-type="bibr" rid="CR42">42</xref>]. Metabolic models were previously available for both the platelet [<xref ref-type="bibr" rid="CR36">36</xref>] and RBC [<xref ref-type="bibr" rid="CR37">37</xref>], enabling the creation of network maps for both reconstructions. Thus, these data could be visualized in the context of the entire metabolic network.</p><p><italic>Case study: human platelets under storage conditions</italic> Our first case study examined the storage of platelets. We manually created a metabolic map for the complete metabolic network of the platelet using Escher [<xref ref-type="bibr" rid="CR6">6</xref>]. We then overlaid metabolomic data which characterized the baseline storage conditions with eight time points over ten days of storage [<xref ref-type="bibr" rid="CR13">13</xref>] to produce a network-level visualization of the data (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). Using this network-level visualization, we examined the dynamics of the platelet metabolome.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Network map in SBGN style [<xref ref-type="bibr" rid="CR43">43</xref>] for the human platelet with metabolomic data [<xref ref-type="bibr" rid="CR13">13</xref>] overlaid. This figure represents a visualization in which the fill level of a node represents the relative size of the corresponding metabolite pool</p></caption><graphic xlink:href="12859_2020_3415_Fig2_HTML" id="MO2"/></fig></p><p>During the first part of storage, stress due to the non-physiological conditions of storage (i.e., packed in a plastic bag at 22<sup>&#x02218;</sup>C) slows metabolic activity through glycolysis, the pentose phosphate pathway, and purine salvage pathways [<xref ref-type="bibr" rid="CR13">13</xref>]. Several metabolites are secreted by the cells and accumulate in the storage media, such as hypoxanthine. The metabolite 5-Phospho- <italic>&#x003b1;</italic>-<sc>D</sc>-ribose 1-diphosphate (PRPP) is produced from the pentose phosphate pathway and is a cofactor in the salvage reactions that break down hypoxanthine. Because flux through the pentose phosphate pathway is lower, the cells have less capacity to recycle hypoxanthine using the salvage pathways.</p><p>By viewing all of the data simultaneously at the network level, we were able to discover that the concentration profile of nicotinamide mirrors that of hypoxanthine. This observation suggests a similar rationale for the accumulation of nicotinamide, providing a hypothesis as to why the salvage pathway within purine metabolism has lower activity during the first few days of storage. These findings are demonstrated in the video highlighted in (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>), helping show how network-level visualization allows for improved extraction of biological insight from large, complex data sets.</p><p><italic>Case study: human red blood cells under storage conditions</italic> Our second case study examined the storage of RBCs. A metabolic map was already available for the RBC [<xref ref-type="bibr" rid="CR44">44</xref>] and captures the complete metabolic network [<xref ref-type="bibr" rid="CR37">37</xref>]. Here, we sought to examine a data set that provided the opportunity to visualize different experimental conditions for the same network. Recently, a study was published [<xref ref-type="bibr" rid="CR14">14</xref>] that used quantitative longitudinal metabolomic data to examine the state of the RBC metabolome under four different storage temperatures: 4<sup>&#x02218;</sup>C (storage temperature), 13<sup>&#x02218;</sup>C, 22<sup>&#x02218;</sup>C, and 37<sup>&#x02218;</sup>C (body temperature). For this system, we opted to visualize the dynamics of the metabolite concentrations as nodes with variable size where smaller nodes represent smaller pool sizes, and larger nodes represent larger pool sizes (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>).
<fig id="Fig3"><label>Fig. 3</label><caption><p>Overview of the RBC metabolic network under storage conditions at 4<sup>&#x02218;</sup>C. The size and color of the nodes reflects their absolute abundance. The oval area on the top magnifies a region in the center of the map that appears in the style of Escher [<xref ref-type="bibr" rid="CR6">6</xref>] in contrast to the SBGN style shown in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref></p></caption><graphic xlink:href="12859_2020_3415_Fig3_HTML" id="MO3"/></fig></p><p>To highlight the differences between the experimental conditions, we examined two of the conditions side-by-side (see the video highlighted in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). This visualization helps supplement the type of statistical and modeling analyses performed previously and helps contextualize the effects of the temperature change across different parts of the network. In particular, it is obvious from a network-level view of the system that certain parts of the network are more active at different points in the time-course. A side-by-side comparison helped emphasize that the availability of reduced glutathione is different with increased temperature, an important physiological feature due to the role of glutathione in neutralizing reactive oxygen species [<xref ref-type="bibr" rid="CR45">45</xref>] that accumulate during storage and contribute to the storage lesion [<xref ref-type="bibr" rid="CR46">46</xref>]. Finally, it can be seen that hypoxanthine&#x02014;a known toxic metabolite whose concentration has been shown to inversely correlate with the post-transfusion recovery rates of transfusion patients [<xref ref-type="bibr" rid="CR47">47</xref>]&#x02014;accumulates faster at higher temperatures. Like in the other case study presented above, the new insights into complex processes (which are not yet fully understood) provide evidence that this method can be beneficial for the simplification and understanding of large, complex data analyses.</p></sec><sec id="Sec3" sec-type="discussion"><title>Discussion</title><p>In this article, we proposed GEM-Vis as a new method for visualizing time-course metabolomic data in the context of large-scale metabolic networks. The approach was evaluated with a range of different use-cases, ranging from the display of simulated data on automatically generated network layouts to experimentally obtained metabolite concentration data on manually drawn network maps. All experiments were described in elaborate tutorial videos (see supplementary material). Subsequently, the method was applied to study two different cases of human blood cells (platelets and erythrocytes) in more detail.</p><p>As a result, a network-level representation of large metabolomic data sets presents a more holistic view of the data than does statistical analysis alone. While visual inspection of data is indeed not a replacement for more detailed statistical or modeling analyses, this method provides an important supplement to existing data analysis pipelines. We demonstrate its utility in such an analysis pipeline by highlighting findings from existing data sets [<xref ref-type="bibr" rid="CR13">13</xref>, <xref ref-type="bibr" rid="CR14">14</xref>]. Visualizing the metabolomic data in the context of the full metabolic network allowed for new insights into existing data sets. A potential explanation why the salvage pathway lowers its activity during the first few days of platelet storage could be deduced for the network of the human platelet. In the RBC network, it could easily be seen that concentrations in certain parts of the network (e.g., nucleotide metabolism) accumulated or depleted together. These findings illustrate the promising potential of visualized time-course data and&#x02014;combined with in-depth computational data analysis&#x02014;can help perceiving information and elucidate physiological processes.</p><p>The simplification of experimental data interpretation became extremely relevant in the age of high-throughput technologies. The visualization concept presented here offers a systems-level interpretation of metabolomic data. Combined with other data analytics, this method helps provide a holistic view of a data set, moving us closer to being able to realize the full potential of a given data set. More broadly, we hope that the method presented here will provide the starting point for further visualization improvements not only for metabolomic data but for the visualization and contextualization of other data types. Future work may include combining a dynamic representation with static concentration graphs that will continue to improve the capabilities of such software to fully meet the needs of life science researchers.</p></sec><sec id="Sec4"><title>Methods</title><p>The method described in this paper utilizes existing software libraries to visually represent metabolomic data in the context of a metabolic network map.</p><p>In brief, the metabolic map must be embedded as SBML Layout extension (version 1) into an SBML Level 3 Version 1 file that is provided to the software SBMLsimulator [<xref ref-type="bibr" rid="CR48">48</xref>]. In this study, the design of metabolic network maps was created using the web-based software Escher [<xref ref-type="bibr" rid="CR6">6</xref>] within the web browser Safari 11 and stored in JSON format, resulting in Additional file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref> for <italic>i</italic>AT-PLT-636 and Additional file&#x000a0;<xref rid="MOESM8" ref-type="media">8</xref> for <italic>i</italic>AB-RBC-283. Subsequently, the generated maps have been converted to SBML using the software EscherConverter (available at <ext-link ext-link-type="uri" xlink:href="https://github.com/draeger-lab/EscherConverter/">https://github.com/draeger-lab/EscherConverter/</ext-link>) and embedded into the metabolic model using a custom Java&#x02122; program (Additional file&#x000a0;<xref rid="MOESM14" ref-type="media">14</xref>). The resulting SBML file with embedded layout for <italic>i</italic>AT-PLT-636 can be found in Additional file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>, and in Additional file&#x000a0;<xref rid="MOESM9" ref-type="media">9</xref> for <italic>i</italic>AB-RBC-283 (both files are compressed using GZIP). The metabolomic time-course data are provided to SBMLsimulator in a *.csv file format with identifiers matching those of the map (Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref> for <italic>i</italic>AT-PLT-636 and Additional file&#x000a0;<xref rid="MOESM7" ref-type="media">7</xref> for <italic>i</italic>AB-RBC-283, the latter is a compressed ZIP archive). SBMLsimulator reads in the SBML file with embedded layout and the time-course data. Subsequently, SBMLsimulator uses splines to interpolate the data over time with input from the user. Other features are selected, such as the speed of animation and how metabolite concentrations are represented (e.g., fill level). An optional *.csv file can be provided to SBMLsimulator to define a moving camera animation. To this end, this CSV file needs to contain as the first value the zoom level of the camera followed by a tab-separated list of corner points along the way of the moving camera (these points are the top left corners of the camera&#x02019;s view port). The result is a smooth animation that allows features such as zooming and panning across different areas of the map, which the user can safe to a video file in one of the supported formats, e.g., AVI, MP4, MPG, WMV, FLV, or MOV. The procedure is depicted in Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> and demonstrated in detail in Additional files&#x000a0;<xref rid="MOESM10" ref-type="media">10</xref>, <xref rid="MOESM11" ref-type="media">11</xref>, <xref rid="MOESM12" ref-type="media">12</xref> and <xref rid="MOESM13" ref-type="media">13</xref>.
<fig id="Fig4"><label>Fig. 4</label><caption><p>Creation of an animated video from an SBML file, a pathway map, and a time-course metabolomic data set. EscherConverter converts the manually drawn pathway map in Escher&#x02019;s JSON format [<xref ref-type="bibr" rid="CR6">6</xref>] to SBML [<xref ref-type="bibr" rid="CR33">33</xref>] with Layout extension [<xref ref-type="bibr" rid="CR32">32</xref>]. The resulting SBML file is merged with the corresponding GEM (in SBML Level 3 Version 1 format, e.g., using the Java code from Additional file&#x000a0;<xref rid="MOESM14" ref-type="media">14</xref>). After opening the merged SBML file in SBMLsimulator [<xref ref-type="bibr" rid="CR48">48</xref>], a time-course metabolomic data set in CSV format (character-separated values) is also loaded to SBMLsimulator. An export function is provided in SBMLsimulator to generate a dynamic time-course animation</p></caption><graphic xlink:href="12859_2020_3415_Fig4_HTML" id="MO4"/></fig></p><p><media xlink:href="12859_2020_3415_MOESM10_ESM.mp4" id="MOESM10"><label>Additional file 10</label><caption><p>This tutorial video (36.3 MB) demonstrates how to download, installation, and launch the software SBMLsimulator. The video is available at <ext-link ext-link-type="uri" xlink:href="https://youtu.be/Eu4uSPmNXVI">https://youtu.be/Eu4uSPmNXVI</ext-link>.</p></caption></media></p><p><media xlink:href="12859_2020_3415_MOESM11_ESM.mp4" id="MOESM11"><label>Additional file 11</label><caption><p>This tutorial video (61.8 MB) demonstrates how to load model files in SBML format and how to run a simulation using the software SBMLsimulator. The video is available at <ext-link ext-link-type="uri" xlink:href="https://youtu.be/CVzp_XtIaHU">https://youtu.be/CVzp_XtIaHU</ext-link>.</p></caption></media></p><p><media xlink:href="12859_2020_3415_MOESM12_ESM.mp4" id="MOESM12"><label>Additional file 12</label><caption><p>This tutorial video (66.7 MB) demonstrates how to embedding a model layout in an SBML file and how to prepare experimental data for loading it into the software SBMLsimulator. The video is available at <ext-link ext-link-type="uri" xlink:href="https://youtu.be/CoeOh2sFFSQ">https://youtu.be/CoeOh2sFFSQ</ext-link>.</p></caption></media></p><p><media xlink:href="12859_2020_3415_MOESM13_ESM.mp4" id="MOESM13"><label>Additional file 13</label><caption><p>This tutorial video (52.9 MB) demonstrates how to visualize manually created layouts using the software SBMLsimulator. The video is available at <ext-link ext-link-type="uri" xlink:href="https://youtu.be/qv3qPyzofhI">https://youtu.be/qv3qPyzofhI</ext-link>.</p></caption></media></p><p>SBMLsimulator is implemented in Java&#x02122; SE 8 under macOS High Sierra on a MacBook Pro 15&#x0201d;, 2016. All computation for the animation videos has been performed under macOS High Sierra version 10.13.2. The animation videos for the two case studies were created using Windows 10.</p><p>Audio recording was performed using a ZOOM Handy Recorder H4 and a Steinberg UR22 mkII USB Audio Interface 24 bit/192 kHz (Steinberg Media Technologies GmbH, Hamburg, Germany) in combination with a R&#x000f8;de NT1-A (R&#x000f8;de, Silverwater, NSW, Australia), and the recording software Quicktime (Apple Inc., Cuppertino, CA, USA). Sony VEGAS Pro (version 12) was used for video post-processing, resulting in Additional files&#x000a0;2 and 6.</p><p>Full details for the implementation and use of the software are provided in the Supplemental Material (Additional files&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref> and <xref rid="MOESM15" ref-type="media">15</xref>).</p></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec5"><supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12859_2020_3415_MOESM1_ESM.pdf"><caption><p><bold>Additional file 1</bold> Supplementary information about details of the method and implementation can be found in file 12859_2020_3415_MOESM1_ESM.pdf.</p></caption></media></supplementary-material><p><bold>Additional file 2</bold> The animated movie iAT-PLT-636.mp4 (547.4 MB) about <italic>i</italic>AT-PLT-636 available at <ext-link ext-link-type="uri" xlink:href="https://youtu.be/GQuT7R-ldS4">https://youtu.be/GQuT7R-ldS4</ext-link>.</p><p><supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12859_2020_3415_MOESM3_ESM.csv"><caption><p><bold>Additional file 3</bold> The data set iAT-PLT-636_Data.csv used in the <italic>i</italic>AT-PLT-636 animation (15 kB).</p></caption></media></supplementary-material></p><p><supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12859_2020_3415_MOESM4_ESM.gz"><caption><p><bold>Additional file 4</bold> The pathway map in Escher format iAT-PLT-636_Map.json.gz compressed with gzip (200 kB).</p></caption></media></supplementary-material></p><p><supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12859_2020_3415_MOESM5_ESM.gz"><caption><p><bold>Additional file 5</bold> The pathway map in SBML Level 3 Version 1 format with Layout and FBC (flux balance constraints) packages iAT-PLT-636_Map.xml.gz compressed with gzip (457 kB).</p></caption></media></supplementary-material></p><p><bold>Additional file 6</bold> The animated movie iAB-RBC-283.mp4 (118.9 MB) about <italic>i</italic>AB-RBC-283 available at <ext-link ext-link-type="uri" xlink:href="https://youtu.be/0INItST4FQc">https://youtu.be/0INItST4FQc</ext-link>.</p><p><supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="12859_2020_3415_MOESM7_ESM.zip"><caption><p><bold>Additional file 7</bold> The data set iAB-RBC-283_Data.zip used in the <italic>i</italic>AB-RBC-283 animation (13 kB).</p></caption></media></supplementary-material></p><p><supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="12859_2020_3415_MOESM8_ESM.gz"><caption><p><bold>Additional file 8</bold> The pathway map in Escher format iAB-RBC-283_Map_cell-outline.json.gz (94 kB).</p></caption></media></supplementary-material></p><p><supplementary-material content-type="local-data" id="MOESM9"><media xlink:href="12859_2020_3415_MOESM9_ESM.gz"><caption><p><bold>Additional file 9</bold> The pathway map iAB-RBC-283_Map_cell-outline.xml.gz in SBML Level 3 Version 1 format with Layout and FBC packages compressed with gzip (153 kB). Please note that the core model in this file has been reduced to the content of the pathway map and does not comprise all reactions and metabolites of the original model by [<xref ref-type="bibr" rid="CR37">37</xref>].</p></caption></media></supplementary-material></p><p><supplementary-material content-type="local-data" id="MOESM14"><media xlink:href="12859_2020_3415_MOESM14_ESM.java"><caption><p><bold>Additional file 14</bold> A brief Java&#x02122; program that embeds a metabolic map in the format of SBML Layout extensions into an SBML Level 3 Version 1 file.</p></caption></media></supplementary-material></p><p><supplementary-material content-type="local-data" id="MOESM15"><media xlink:href="12859_2020_3415_MOESM15_ESM.pdf"><caption><p><bold>Additional file 15</bold> This document describes the features of the application SBMLsimulator and explains how to use them.</p></caption></media></supplementary-material></p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>AVI</term><def><p>Audio video interleave</p></def></def-item><def-item><term>BiGG</term><def><p>Biochemically, genetically, genomically structured</p></def></def-item><def-item><term>CSV</term><def><p>Character-separated values</p></def></def-item><def-item><term>FLV</term><def><p>Flash video</p></def></def-item><def-item><term>GEM</term><def><p>Genome-scale metabolic model</p></def></def-item><def-item><term>GEM-Vis</term><def><p>GEM-visualization</p></def></def-item><def-item><term>GZIP</term><def><p>GNU zip</p></def></def-item><def-item><term>JSON</term><def><p>JavaScript object notation</p></def></def-item><def-item><term>MOV</term><def><p>QuickTime file format</p></def></def-item><def-item><term>MPG</term><def><p>Moving pictures expert group</p></def></def-item><def-item><term>MP4</term><def><p>Multimedia file format</p></def></def-item><def-item><term>PRPP</term><def><p>5-phospho- <italic>&#x003b1;</italic>-<sc>d</sc>-ribose 1-diphosphate</p></def></def-item><def-item><term>RBC</term><def><p>Red blood cell</p></def></def-item><def-item><term>SBML</term><def><p>Systems biology markup language</p></def></def-item><def-item><term>WMV</term><def><p>Windows media video</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn><fn><p>Lea F. Buchweitz, James T. Yurkovich and Andreas Dr&#x000e4;ger contributed equally to this work.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s12859-020-3415-z.</p></sec><ack><p>The authors would like to thank Prof. Dr. Robert Feil, Katrin Keppler for her music contribution, Jan D. Rudolph, and Jakob Matthes for implementing a basic Java&#x02122; interpreter of SBML layout.</p></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>JH and AD designed the study. FJ, &#x000d2;ES, OR, and JTY consulted on platelet and RBC physiology. LFB and CB implemented the analysis in SBMLsimulator and Escher, respectively, mentored by AD and ZAK, based on works of FS, ZAK, and AD. JTY and VK drew the erythrocyte and platelet maps. LFB created animation videos, mentored by JTY, ZAK, FJ, &#x000d2;ES, OR, and AD, and drafted the manuscript with JTY and contributions from all other authors. AD wrote the Users&#x02019; Guide about SBMLsimulator and created the series of tutorial videos about the use of SBMLsimulator, SBML file use, and data mapping with the help of LFB and JTY.</p></notes><notes notes-type="data-availability"><title>Availability of data and materials</title><p>Data and materials required to reproduce the findings in this article are freely available. See the appendix in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref> for details. For post-processing generated animation files, a commercial video editing software, such as Sony VEGAS Pro (version 12) that was used for creating Additional files&#x000a0;2 and 6, can be obtained for purchase. Computers and audio recording devices need to be purchased.</p></notes><notes id="FPar1"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></notes><notes id="FPar2"><title>Consent for publication</title><p>Not applicable.</p></notes><notes id="FPar3" notes-type="COI-statement"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Robinson</surname><given-names>JL</given-names></name><name><surname>Nielsen</surname><given-names>J</given-names></name></person-group><article-title>Integrative analysis of human omics data using biomolecular networks</article-title><source>Mol BioSyst</source><year>2016</year><volume>12</volume><issue>10</issue><fpage>2953</fpage><lpage>64</lpage><?supplied-pmid 27510223?><pub-id pub-id-type="pmid">27510223</pub-id></element-citation></ref><ref id="CR2"><label>2</label><mixed-citation publication-type="other">&#x000d6;sterlund T, Cvijovic M, Kristiansson E. Integrative Analysis of Omics Data In: Nielsen J, Hohmann S, editors. Systems Biology. Weinheim: Wiley-VCH Verlag GmbH &#x00026; Co. KGaA: 2017. p. 1&#x02013;24. Chap. 1. 10.1002/9783527696130.ch1.</mixed-citation></ref><ref id="CR3"><label>3</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nielsen</surname><given-names>J</given-names></name></person-group><article-title>Systems Biology of Metabolism</article-title><source>Ann Rev Biochem</source><year>2017</year><volume>86</volume><issue>1</issue><fpage>245</fpage><lpage>75</lpage><?supplied-pmid 28301739?><pub-id pub-id-type="pmid">28301739</pub-id></element-citation></ref><ref id="CR4"><label>4</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yurkovich</surname><given-names>JT</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name></person-group><article-title>Quantitative -omic data empowers bottom-up systems biology</article-title><source>Curr Opin Biotechnol</source><year>2018</year><volume>51</volume><fpage>130</fpage><lpage>6</lpage><?supplied-pmid 29414439?><pub-id pub-id-type="pmid">29414439</pub-id></element-citation></ref><ref id="CR5"><label>5</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Callaway</surname><given-names>E</given-names></name></person-group><article-title>The visualizations transforming biology</article-title><source>Nature</source><year>2016</year><volume>535</volume><fpage>187</fpage><lpage>8</lpage><?supplied-pmid 27383989?><pub-id pub-id-type="pmid">27383989</pub-id></element-citation></ref><ref id="CR6"><label>6</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>King</surname><given-names>ZA</given-names></name><name><surname>Dr&#x000e4;ger</surname><given-names>A</given-names></name><name><surname>Ebrahim</surname><given-names>A</given-names></name><name><surname>Sonnenschein</surname><given-names>N</given-names></name><name><surname>Lewis</surname><given-names>NE</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name></person-group><article-title>Escher: A web application for building, sharing, and embedding Data-Rich visualizations of biological pathways</article-title><source>PLoS Comput Biol</source><year>2015</year><volume>11</volume><issue>8</issue><fpage>1004321</fpage></element-citation></ref><ref id="CR7"><label>7</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Smoot</surname><given-names>ME</given-names></name><name><surname>Ono</surname><given-names>K</given-names></name><name><surname>Ruscheinski</surname><given-names>J</given-names></name><name><surname>Wang</surname><given-names>P-L</given-names></name><name><surname>Ideker</surname><given-names>T</given-names></name></person-group><article-title>Cytoscape 2.8: new features for data integration and network visualization</article-title><source>Bioinformatics</source><year>2011</year><volume>27</volume><issue>3</issue><fpage>431</fpage><lpage>2</lpage><?supplied-pmid 21149340?><pub-id pub-id-type="pmid">21149340</pub-id></element-citation></ref><ref id="CR8"><label>8</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Droste</surname><given-names>P</given-names></name><name><surname>N&#x000f6;h</surname><given-names>K</given-names></name><name><surname>Wiechert</surname><given-names>W</given-names></name></person-group><article-title>Omix &#x02013; a visualization tool for metabolic networks with highest usability and customizability in focus</article-title><source>Chem Ing Tech</source><year>2013</year><volume>85</volume><issue>6</issue><fpage>849</fpage><lpage>62</lpage></element-citation></ref><ref id="CR9"><label>9</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Funahashi</surname><given-names>A</given-names></name><name><surname>Matsuoka</surname><given-names>Y</given-names></name><name><surname>Jouraku</surname><given-names>A</given-names></name><name><surname>Morohashi</surname><given-names>M</given-names></name><name><surname>Kikuchi</surname><given-names>N</given-names></name><name><surname>Kitano</surname><given-names>H</given-names></name></person-group><article-title>CellDesigner 3.5: A versatile modeling tool for biochemical networks</article-title><source>Proc IEEE</source><year>2008</year><volume>96</volume><issue>8</issue><fpage>1254</fpage><lpage>65</lpage></element-citation></ref><ref id="CR10"><label>10</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kelder</surname><given-names>T</given-names></name><name><surname>van Iersel</surname><given-names>MP</given-names></name><name><surname>Hanspers</surname><given-names>K</given-names></name><name><surname>Kutmon</surname><given-names>M</given-names></name><name><surname>Conklin</surname><given-names>BR</given-names></name><name><surname>Evelo</surname><given-names>CT</given-names></name><name><surname>Pico</surname><given-names>AR</given-names></name></person-group><article-title>WikiPathways: building research communities on biological pathways</article-title><source>Nucleic Acids Res</source><year>2012</year><volume>40</volume><issue>Database issue</issue><fpage>1301</fpage><lpage>7</lpage></element-citation></ref><ref id="CR11"><label>11</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Patti</surname><given-names>GJ</given-names></name><name><surname>Yanes</surname><given-names>O</given-names></name><name><surname>Siuzdak</surname><given-names>G</given-names></name></person-group><article-title>Innovation: Metabolomics: the apogee of the omics trilogy</article-title><source>Nat Rev Mol Cell Biol</source><year>2012</year><volume>13</volume><fpage>263</fpage><lpage>9</lpage><?supplied-pmid 22436749?><pub-id pub-id-type="pmid">22436749</pub-id></element-citation></ref><ref id="CR12"><label>12</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nemkov</surname><given-names>T</given-names></name><name><surname>Hansen</surname><given-names>KC</given-names></name><name><surname>Dumont</surname><given-names>LJ</given-names></name><name><surname>D&#x02019;Alessandro</surname><given-names>A</given-names></name></person-group><article-title>Metabolomics in transfusion medicine</article-title><source>Transfusion</source><year>2016</year><volume>56</volume><issue>4</issue><fpage>980</fpage><lpage>93</lpage><?supplied-pmid 26662506?><pub-id pub-id-type="pmid">26662506</pub-id></element-citation></ref><ref id="CR13"><label>13</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Paglia</surname><given-names>G</given-names></name><name><surname>Sigurj&#x000f3;nsson</surname><given-names>OE</given-names></name><name><surname>Rolfsson</surname><given-names>O</given-names></name><name><surname>Valgeirsdottir</surname><given-names>S</given-names></name><name><surname>Hansen</surname><given-names>MB</given-names></name><name><surname>Brynj&#x000f3;lfsson</surname><given-names>S</given-names></name><name><surname>Gudmundsson</surname><given-names>S</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name></person-group><article-title>Comprehensive metabolomic study of platelets reveals the expression of discrete metabolic phenotypes during storage</article-title><source>Transfusion</source><year>2014</year><volume>54</volume><fpage>2911</fpage><lpage>23</lpage><?supplied-pmid 24840017?><pub-id pub-id-type="pmid">24840017</pub-id></element-citation></ref><ref id="CR14"><label>14</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yurkovich</surname><given-names>JT</given-names></name><name><surname>Zielinski</surname><given-names>DC</given-names></name><name><surname>Yang</surname><given-names>L</given-names></name><name><surname>Paglia</surname><given-names>G</given-names></name><name><surname>Rolfsson</surname><given-names>O</given-names></name><name><surname>Sigurj&#x000f3;nsson</surname><given-names>OE</given-names></name><name><surname>Broddrick</surname><given-names>JT</given-names></name><name><surname>Bordbar</surname><given-names>A</given-names></name><name><surname>Wichuk</surname><given-names>K</given-names></name><name><surname>Brynj&#x000f3;lfsson</surname><given-names>S</given-names></name><name><surname>Palsson</surname><given-names>S</given-names></name><name><surname>Gudmundsson</surname><given-names>S</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name></person-group><article-title>Quantitative time-course metabolomics in human red blood cells reveal the temperature dependence of human metabolic networks</article-title><source>J Biol Chem</source><year>2017</year><volume>292</volume><fpage>117</fpage><lpage>804914</lpage></element-citation></ref><ref id="CR15"><label>15</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bordbar</surname><given-names>A</given-names></name><name><surname>Yurkovich</surname><given-names>JT</given-names></name><name><surname>Paglia</surname><given-names>G</given-names></name><name><surname>Rolfsson</surname><given-names>O</given-names></name><name><surname>Sigurj&#x000f3;nsson</surname><given-names>OE</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name></person-group><article-title>Elucidating dynamic metabolic physiology through network integration of quantitative timecourse metabolomics</article-title><source>Sci Rep</source><year>2017</year><volume>7</volume><issue>46249</issue><fpage>1</fpage><lpage>12</lpage><pub-id pub-id-type="pmid">28127051</pub-id></element-citation></ref><ref id="CR16"><label>16</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Paglia</surname><given-names>G</given-names></name><name><surname>Sigurj&#x000f3;nsson</surname><given-names>&#x000d3;E</given-names></name><name><surname>Rolfsson</surname><given-names>&#x000d3;.</given-names></name><name><surname>Hansen</surname><given-names>MB</given-names></name><name><surname>Brynj&#x000f3;lfsson</surname><given-names>S</given-names></name><name><surname>Gudmundsson</surname><given-names>S</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name></person-group><article-title>Metabolomic analysis of platelets during storage: a comparison between apheresis- and buffy coat-derived platelet concentrates</article-title><source>Transfusion</source><year>2015</year><volume>55</volume><issue>2</issue><fpage>301</fpage><lpage>13</lpage><?supplied-pmid 25156572?><pub-id pub-id-type="pmid">25156572</pub-id></element-citation></ref><ref id="CR17"><label>17</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Secrier</surname><given-names>M</given-names></name><name><surname>Schneider</surname><given-names>R</given-names></name></person-group><article-title>Visualizing time-related data in biology, a review</article-title><source>Brief Bioinforma</source><year>2014</year><volume>15</volume><issue>5</issue><fpage>771</fpage><lpage>82</lpage></element-citation></ref><ref id="CR18"><label>18</label><mixed-citation publication-type="other">Wiwie C, Rauch A, Haakonsson A, Barrio-Hernandez I, Blagoev B, Mandrup S, R&#x000f6;ttger R, Baumbach J. Elucidation of time-dependent systems biology cell response patterns with time course network enrichment. 2017. <ext-link ext-link-type="uri" xlink:href="https://arxiv.org/abs/1710.10262">https://arxiv.org/abs/1710.10262</ext-link>.</mixed-citation></ref><ref id="CR19"><label>19</label><mixed-citation publication-type="other">&#x0010c;ern&#x000fd; M. Improve handling of time series and similar in Cytoscape. 2017. <ext-link ext-link-type="uri" xlink:href="https://github.com/nrnb/GoogleSummerOfCode/issues/76">https://github.com/nrnb/GoogleSummerOfCode/issues/76</ext-link>. Accessed 8 Apr 2019.</mixed-citation></ref><ref id="CR20"><label>20</label><mixed-citation publication-type="other">Rohn H, Junker A, Hartmann A, Grafahrend-Belau E, Treutler H, Klapperst&#x000fc;ck M, Czauderna T, Klukas C, Schreiber F. VANTED v2: a framework for systems biology applications. BMC Syst Biol. 2012; 6(139). 10.1186/1752-0509-6-139.</mixed-citation></ref><ref id="CR21"><label>21</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ma</surname><given-names>DKG</given-names></name><name><surname>Stolte</surname><given-names>C</given-names></name><name><surname>Kaur</surname><given-names>S</given-names></name><name><surname>Bain</surname><given-names>M</given-names></name><name><surname>O&#x02019;Donoghue</surname><given-names>SI</given-names></name></person-group><article-title>Visual analytics of phosphorylation time-series data on insulin response</article-title><source>AIP Conf Proc</source><year>2013</year><volume>1559</volume><fpage>185</fpage><lpage>96</lpage></element-citation></ref><ref id="CR22"><label>22</label><mixed-citation publication-type="other">Pavlopoulos GA, Wegener A-L, Schneider R. A survey of visualization tools for biological network analysis. BioData Min. 2008; 1(12). 10.1186/1756-0381-1-12.</mixed-citation></ref><ref id="CR23"><label>23</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>K&#x000f6;nig</surname><given-names>M</given-names></name><name><surname>Dr&#x000e4;ger</surname><given-names>A</given-names></name><name><surname>Holzh&#x000fc;tter</surname><given-names>H-G</given-names></name></person-group><article-title>CySBML: a Cytoscape plugin for SBML</article-title><source>Bioinformatics</source><year>2012</year><volume>28</volume><issue>18</issue><fpage>2402</fpage><lpage>3</lpage><?supplied-pmid 22772946?><pub-id pub-id-type="pmid">22772946</pub-id></element-citation></ref><ref id="CR24"><label>24</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gehlenborg</surname><given-names>N</given-names></name><name><surname>O&#x02019;Donoghue</surname><given-names>SI</given-names></name><name><surname>Baliga</surname><given-names>NS</given-names></name><name><surname>Goesmann</surname><given-names>A</given-names></name><name><surname>Hibbs</surname><given-names>H</given-names></name><name><surname>Kitano</surname><given-names>MA</given-names></name><name><surname>Kohlbacher</surname><given-names>O</given-names></name><name><surname>Neuweger</surname><given-names>H</given-names></name><name><surname>Schneider</surname><given-names>R</given-names></name><name><surname>Tenenbaum</surname><given-names>D</given-names></name><name><surname>Gavin</surname><given-names>A-C</given-names></name></person-group><article-title>Visualization of omics data for systems biology</article-title><source>Nat Methods</source><year>2010</year><volume>7</volume><issue>3 Suppl</issue><fpage>56</fpage><lpage>68</lpage><pub-id pub-id-type="pmid">20010831</pub-id></element-citation></ref><ref id="CR25"><label>25</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Adler</surname><given-names>P</given-names></name><name><surname>Reimand</surname><given-names>J</given-names></name><name><surname>J&#x000e4;nes</surname><given-names>J</given-names></name><name><surname>Kolde</surname><given-names>R</given-names></name><name><surname>Peterson</surname><given-names>H</given-names></name><name><surname>Vilo</surname><given-names>J</given-names></name></person-group><article-title>KEGGanim: pathway animations for high-throughput data</article-title><source>Bioinformatics</source><year>2008</year><volume>24</volume><issue>4</issue><fpage>588</fpage><lpage>90</lpage><?supplied-pmid 18056068?><pub-id pub-id-type="pmid">18056068</pub-id></element-citation></ref><ref id="CR26"><label>26</label><mixed-citation publication-type="other">Brunk E, Sahoo S, Zielinski DC, Altunkaya A, Dr&#x000e4;ger A, Mih N, Gatto F, Nilsson A, Preciat Gonzalez GA, Aurich MK, Prli&#x00107; A, Sastry A, Danielsdottir AD, Heinken A, Noronha A, Rose PW, Burley SK, Fleming RMT, Nielsen J, Thiele I, Palsson BO. Recon3D enables a three-dimensional view of gene variation in human metabolism. Nat Biotechnol. 2018:1&#x02013;37. 10.1038/nbt.4072.</mixed-citation></ref><ref id="CR27"><label>27</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Noronha</surname><given-names>A</given-names></name><name><surname>Dan&#x000ed;elsd&#x000f3;ttir</surname><given-names>AD</given-names></name><name><surname>Gawron</surname><given-names>P</given-names></name><name><surname>J&#x000f3;hannsson</surname><given-names>F</given-names></name><name><surname>J&#x000f3;nsd&#x000f3;ttir</surname><given-names>S</given-names></name><name><surname>Jarlsson</surname><given-names>S</given-names></name><name><surname>Gunnarsson</surname><given-names>JP</given-names></name><name><surname>Brynj&#x000f3;lfsson</surname><given-names>S</given-names></name><name><surname>Schneider</surname><given-names>R</given-names></name><name><surname>Thiele</surname><given-names>I</given-names></name><name><surname>Fleming</surname><given-names>RMT</given-names></name></person-group><article-title>Reconmap: an interactive visualization of human metabolism</article-title><source>Bioinformatics</source><year>2017</year><volume>33</volume><issue>4</issue><fpage>605</fpage><lpage>7</lpage><?supplied-pmid 27993782?><pub-id pub-id-type="pmid">27993782</pub-id></element-citation></ref><ref id="CR28"><label>28</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Halford</surname><given-names>GS</given-names></name><name><surname>Baker</surname><given-names>R</given-names></name><name><surname>McCredden</surname><given-names>JE</given-names></name><name><surname>Bain</surname><given-names>JD</given-names></name></person-group><article-title>How many variables can humans process?,</article-title><source>Psychol Sci</source><year>2005</year><volume>16</volume><issue>1</issue><fpage>70</fpage><lpage>76</lpage><?supplied-pmid 15660854?><pub-id pub-id-type="pmid">15660854</pub-id></element-citation></ref><ref id="CR29"><label>29</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shannon</surname><given-names>P</given-names></name><name><surname>Markiel</surname><given-names>A</given-names></name><name><surname>Ozier</surname><given-names>O</given-names></name><name><surname>Baliga</surname><given-names>NS</given-names></name><name><surname>Wang</surname><given-names>JT</given-names></name><name><surname>Ramage</surname><given-names>D</given-names></name><name><surname>Amin</surname><given-names>N</given-names></name><name><surname>Schwikowski</surname><given-names>BB</given-names></name><name><surname>Ideker</surname><given-names>T</given-names></name></person-group><article-title>Cytoscape: A Software Environment for Integrated Models of Biomolecular Interaction Networks</article-title><source>Genome Res</source><year>2003</year><volume>13</volume><issue>11</issue><fpage>2498</fpage><lpage>504</lpage><?supplied-pmid 14597658?><pub-id pub-id-type="pmid">14597658</pub-id></element-citation></ref><ref id="CR30"><label>30</label><mixed-citation publication-type="other">&#x0010c;ern&#x000fd; M. CyDataSeries - Add time series and the like to your networks. 2017. <ext-link ext-link-type="uri" xlink:href="http://apps.cytoscape.org/apps/cydataseries">http://apps.cytoscape.org/apps/cydataseries</ext-link>. Accessed 8 Apr 2019.</mixed-citation></ref><ref id="CR31"><label>31</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cleveland</surname><given-names>WS</given-names></name><name><surname>McGrill</surname><given-names>R</given-names></name></person-group><article-title>Graphical Perception: Theory, Experimentation and Application to the Development of Graphical Methods</article-title><source>J Am Stat Assoc</source><year>1984</year><volume>79</volume><issue>387</issue><fpage>531</fpage><lpage>54</lpage></element-citation></ref><ref id="CR32"><label>32</label><mixed-citation publication-type="other">Gauges R, Rost U, Sahle S, Wengler K, Bergmann FT. The Systems Biology Markup Language (SBML) Level 3 Package: Layout, Version 1 Core. J Integr Bioinforma. 2015. 10.2390/biecoll-jib-2015-267.</mixed-citation></ref><ref id="CR33"><label>33</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hucka</surname><given-names>M</given-names></name><name><surname>Bergmann</surname><given-names>FT</given-names></name><name><surname>Dr&#x000e4;ger</surname><given-names>A</given-names></name><name><surname>Hoops</surname><given-names>S</given-names></name><name><surname>Keating</surname><given-names>SM</given-names></name><name><surname>Le Nov&#x000e8;re</surname><given-names>N</given-names></name><name><surname>Myers</surname><given-names>CJ</given-names></name><name><surname>Olivier</surname><given-names>BG</given-names></name><name><surname>Sahle</surname><given-names>S</given-names></name><name><surname>Schaff</surname><given-names>JC</given-names></name><name><surname>Smith</surname><given-names>LP</given-names></name><name><surname>Waltemath</surname><given-names>D</given-names></name><name><surname>Wilkinson</surname><given-names>DJ</given-names></name></person-group><article-title>Systems Biology Markup Language (SBML) Level 3 Version 1 Core</article-title><source>J Integr Bioinforma</source><year>2018</year><volume>15</volume><issue>1</issue><fpage>1</fpage></element-citation></ref><ref id="CR34"><label>34</label><mixed-citation publication-type="other">Rougny A, Tour&#x000e9; V, Moodie S, Balaur I, Czauderna T, Borlinghaus H, Dogrusoz U, Mazein A, Dr&#x000e4;ger A, Blinov ML, Vill&#x000e9;ger AC, Haw R, Demir E, Mi H, Sorokin A, Schreiber F, Luna A. Systems Biology Graphical Notation: Process Description language Level 1 Version 2.0. J Integr Bioinforma. 2019; 16(2). 10.1515/jib-2019-0022.</mixed-citation></ref><ref id="CR35"><label>35</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bucher</surname><given-names>J</given-names></name><name><surname>Riedmaier</surname><given-names>S</given-names></name><name><surname>Schnabel</surname><given-names>A</given-names></name><name><surname>Marcus</surname><given-names>K</given-names></name><name><surname>Vacun</surname><given-names>G</given-names></name><name><surname>Weiss</surname><given-names>TS</given-names></name><name><surname>Thasler</surname><given-names>WE</given-names></name><name><surname>N&#x000fc;ssler</surname><given-names>AK</given-names></name><name><surname>Zanger</surname><given-names>UM</given-names></name><name><surname>Reuss</surname><given-names>M</given-names></name></person-group><article-title>A systems biology approach to dynamic modeling and inter-subject variability of statin pharmacokinetics in human hepatocytes</article-title><source>BMC Syst Biol</source><year>2011</year><volume>5</volume><issue>1</issue><fpage>66</fpage><?supplied-pmid 21548957?><pub-id pub-id-type="pmid">21548957</pub-id></element-citation></ref><ref id="CR36"><label>36</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thomas</surname><given-names>A</given-names></name><name><surname>Rahmanian</surname><given-names>S</given-names></name><name><surname>Bordbar</surname><given-names>A</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name><name><surname>Jamshidi</surname><given-names>N</given-names></name></person-group><article-title>Network reconstruction of platelet metabolism identifies metabolic signature for aspirin resistance</article-title><source>Sci Rep</source><year>2014</year><volume>4</volume><issue>3925</issue><fpage>1</fpage><lpage>10</lpage></element-citation></ref><ref id="CR37"><label>37</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bordbar</surname><given-names>A</given-names></name><name><surname>Jamshidi</surname><given-names>N</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name></person-group><article-title><italic>i</italic>AB-RBC-283: A proteomically derived knowledge-base of erythrocyte metabolism that can be used to simulate its physiological and patho-physiological states</article-title><source>BMC Syst Biol</source><year>2011</year><volume>5</volume><issue>1</issue><fpage>110</fpage><?supplied-pmid 21749716?><pub-id pub-id-type="pmid">21749716</pub-id></element-citation></ref><ref id="CR38"><label>38</label><mixed-citation publication-type="other">Mo ML, Palsson B, Herrg&#x000e5;rd MJ. Connecting extracellular metabolomic measurements to intracellular flux states in yeast. BMC Syst Biol. 2009. 10.1186/1752-0509-3-37.</mixed-citation></ref><ref id="CR39"><label>39</label><mixed-citation publication-type="other">Bergdahl B, Heer D, Sauer U, Hahn-H&#x000e4;gerdal, B author=Van Niel EW. Dynamic metabolomics differentiates between carbon and energy starvation in recombinant saccharomyces cerevisiae fermenting xylose. Biotechnol Biofuels. 2012. 10.1186/1754-6834-5-34.</mixed-citation></ref><ref id="CR40"><label>40</label><mixed-citation publication-type="other">Chelliah V, Juty N, Ajmera I, Ali R, Dumousseau M, Glont M, Hucka M, Jalowicki G, Keating S, Knight-Schrijver V, Lloret-Villas A, Nath Natarajan K, Pettit J-B, Rodriguez N, Schubert M, Wimalaratne SM, Zhao Y, Hermjakob H, Le Nov&#x000e8;re N, Laibe C. BioModels: ten-year anniversary. Nucl Acids Res. 2015. 10.1093/nar/gku1181.</mixed-citation></ref><ref id="CR41"><label>41</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>King</surname><given-names>ZA</given-names></name><name><surname>Lu</surname><given-names>JS</given-names></name><name><surname>Dr&#x000e4;ger</surname><given-names>A</given-names></name><name><surname>Miller</surname><given-names>PC</given-names></name><name><surname>Federowicz</surname><given-names>S</given-names></name><name><surname>Lerman</surname><given-names>JA</given-names></name><name><surname>Ebrahim</surname><given-names>A</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name><name><surname>Lewis</surname><given-names>NE</given-names></name></person-group><article-title>BiGG Models: A platform for integrating, standardizing and sharing genome-scale models</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>44</volume><issue>D1</issue><fpage>515</fpage><lpage>22</lpage></element-citation></ref><ref id="CR42"><label>42</label><mixed-citation publication-type="other">Yurkovich JT, Bordbar A, Sigurj&#x000f3;nsson &#x000d3;E, Palsson BO. Systems biology as an emerging paradigm in transfusion medicine. BMC Syst Biol. 2018; 12(1). 10.1186/s12918-018-0558-x.</mixed-citation></ref><ref id="CR43"><label>43</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Le Nov&#x000e8;re</surname><given-names>N</given-names></name><name><surname>Hucka</surname><given-names>M</given-names></name><name><surname>Mi</surname><given-names>H</given-names></name><name><surname>Moodie</surname><given-names>S</given-names></name><name><surname>Schreiber</surname><given-names>F</given-names></name><name><surname>Sorokin</surname><given-names>A</given-names></name><name><surname>Demir</surname><given-names>E</given-names></name><name><surname>Wegner</surname><given-names>K</given-names></name><name><surname>Aladjem</surname><given-names>MI</given-names></name><name><surname>Wimalaratne</surname><given-names>SM</given-names></name><name><surname>Bergmann</surname><given-names>FT</given-names></name><name><surname>Gauges</surname><given-names>R</given-names></name><name><surname>Ghazal</surname><given-names>P</given-names></name><name><surname>Kawaji</surname><given-names>H</given-names></name><name><surname>Li</surname><given-names>L</given-names></name><name><surname>Matsuoka</surname><given-names>Y</given-names></name><name><surname>Vill&#x000e9;ger</surname><given-names>A</given-names></name><name><surname>Boyd</surname><given-names>SE</given-names></name><name><surname>Calzone</surname><given-names>L</given-names></name><name><surname>Courtot</surname><given-names>M</given-names></name><name><surname>Dogrusoz</surname><given-names>U</given-names></name><name><surname>Freeman</surname><given-names>TC</given-names></name><name><surname>Funahashi</surname><given-names>A</given-names></name><name><surname>Ghosh</surname><given-names>S</given-names></name><name><surname>Jouraku</surname><given-names>A</given-names></name><name><surname>Kim</surname><given-names>S</given-names></name><name><surname>Kolpakov</surname><given-names>F</given-names></name><name><surname>Luna</surname><given-names>A</given-names></name><name><surname>Sahle</surname><given-names>S</given-names></name><name><surname>Schmidt</surname><given-names>E</given-names></name><name><surname>Watterson</surname><given-names>S</given-names></name><name><surname>Wu</surname><given-names>G</given-names></name><name><surname>Goryanin</surname><given-names>I</given-names></name><name><surname>Kell</surname><given-names>DB</given-names></name><name><surname>Sander</surname><given-names>C</given-names></name><name><surname>Sauro</surname><given-names>H</given-names></name><name><surname>Snoep</surname><given-names>JL</given-names></name><name><surname>Kohn</surname><given-names>K</given-names></name><name><surname>Kitano</surname><given-names>H</given-names></name></person-group><article-title>The Systems Biology Graphical Notation</article-title><source>Nat Biotechnol</source><year>2009</year><volume>27</volume><issue>8</issue><fpage>735</fpage><lpage>41</lpage><?supplied-pmid 19668183?><pub-id pub-id-type="pmid">19668183</pub-id></element-citation></ref><ref id="CR44"><label>44</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yurkovich</surname><given-names>JT</given-names></name><name><surname>Yurkovich</surname><given-names>BJ</given-names></name><name><surname>Dr&#x000e4;ger</surname><given-names>A</given-names></name><name><surname>Palsson</surname><given-names>BO</given-names></name><name><surname>King</surname><given-names>ZA</given-names></name></person-group><article-title>A padawan programmer&#x02019;s guide to developing software libraries</article-title><source>Cell Syst</source><year>2017</year><volume>5</volume><issue>5</issue><fpage>431</fpage><lpage>7</lpage><?supplied-pmid 28988801?><pub-id pub-id-type="pmid">28988801</pub-id></element-citation></ref><ref id="CR45"><label>45</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Whillier</surname><given-names>S</given-names></name><name><surname>Raftos</surname><given-names>JE</given-names></name><name><surname>Sparrow</surname><given-names>RL</given-names></name><name><surname>Kuchel</surname><given-names>PW</given-names></name></person-group><article-title>The effects of long-term storage of human red blood cells on the glutathione synthesis rate and steady-state concentration</article-title><source>Transfusion</source><year>2011</year><volume>51</volume><issue>7</issue><fpage>1450</fpage><lpage>9</lpage><?supplied-pmid 21251007?><pub-id pub-id-type="pmid">21251007</pub-id></element-citation></ref><ref id="CR46"><label>46</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>D&#x02019;Alessandro</surname><given-names>A</given-names></name><name><surname>D&#x02019;Amici</surname><given-names>GM</given-names></name><name><surname>Vaglio</surname><given-names>S</given-names></name><name><surname>Zolla</surname><given-names>L</given-names></name></person-group><article-title>Time-course investigation of SAGM-stored leukocyte-filtered red bood cell concentrates: from metabolism to proteomics</article-title><source>Haematologica</source><year>2012</year><volume>97</volume><issue>1</issue><fpage>107</fpage><lpage>15</lpage><?supplied-pmid 21993682?><pub-id pub-id-type="pmid">21993682</pub-id></element-citation></ref><ref id="CR47"><label>47</label><mixed-citation publication-type="other">Nemkov T, Sun K, Reisz JA, Song A, Yoshida T, Dunham A, Wither MJ, Francis RO, Roach RC, Dzieciatkowska M, Rogers SC, Doctor A, Kriebardis A, Antonelou M, Papassideri I, Young C, Thomas T, Hansen KC, Spitalnik SL, Xia Y, Zimring JC, Hod EA, D&#x02019;Alessandro A. Hypoxia modulates the purine salvage pathway and decreases red blood cell and supernatant levels of hypoxanthine during refrigerated storage. Haematologica. 2017. 10.3324/haematol.2017.178608.</mixed-citation></ref><ref id="CR48"><label>48</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>D&#x000f6;rr</surname><given-names>A</given-names></name><name><surname>Keller</surname><given-names>R</given-names></name><name><surname>Zell</surname><given-names>A</given-names></name><name><surname>Dr&#x000e4;ger</surname><given-names>A</given-names></name></person-group><article-title>SBMLsimulator: A Java Tool for Model Simulation and Parameter Estimation in Systems Biology</article-title><source>Computation</source><year>2014</year><volume>2</volume><issue>4</issue><fpage>246</fpage><lpage>57</lpage><?supplied-pmid 32211200?><pub-id pub-id-type="pmid">32211200</pub-id></element-citation></ref></ref-list></back></article>