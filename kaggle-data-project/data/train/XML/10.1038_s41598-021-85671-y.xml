<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Sci Rep</journal-id><journal-id journal-id-type="iso-abbrev">Sci Rep</journal-id><journal-title-group><journal-title>Scientific Reports</journal-title></journal-title-group><issn pub-type="epub">2045-2322</issn><publisher><publisher-name>Nature Publishing Group UK</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7979766</article-id><article-id pub-id-type="publisher-id">85671</article-id><article-id pub-id-type="doi">10.1038/s41598-021-85671-y</article-id><article-categories><subj-group subj-group-type="heading"><subject>Article</subject></subj-group></article-categories><title-group><article-title>Distant metastasis time to event analysis with CNNs in independent head and neck cancer cohorts</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Lombardo</surname><given-names>Elia</given-names></name><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Kurz</surname><given-names>Christopher</given-names></name><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Marschner</surname><given-names>Sebastian</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Avanzo</surname><given-names>Michele</given-names></name><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Gagliardi</surname><given-names>Vito</given-names></name><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Fanetti</surname><given-names>Giuseppe</given-names></name><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Franchin</surname><given-names>Giovanni</given-names></name><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Stancanello</surname><given-names>Joseph</given-names></name><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Corradini</surname><given-names>Stefanie</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Niyazi</surname><given-names>Maximilian</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Belka</surname><given-names>Claus</given-names></name><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Parodi</surname><given-names>Katia</given-names></name><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" equal-contrib="yes"><name><surname>Riboldi</surname><given-names>Marco</given-names></name><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" corresp="yes" equal-contrib="yes"><name><surname>Landry</surname><given-names>Guillaume</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="GRID">grid.411095.8</institution-id><institution-id institution-id-type="ISNI">0000 0004 0477 2585</institution-id><institution>Department of Radiation Oncology, </institution><institution>University Hospital, </institution></institution-wrap>LMU Munich, Munich, 81377 Germany </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="GRID">grid.5252.0</institution-id><institution-id institution-id-type="ISNI">0000 0004 1936 973X</institution-id><institution>Department of Medical Physics, Faculty of Physics, </institution><institution>Ludwig-Maximilians-Universit&#x000e4;t M&#x000fc;nchen, </institution></institution-wrap>Garching, 85748 Germany </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="GRID">grid.7497.d</institution-id><institution-id institution-id-type="ISNI">0000 0004 0492 0584</institution-id><institution>German Cancer Consortium (DKTK), </institution></institution-wrap>Munich, 81377 Germany </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="GRID">grid.418321.d</institution-id><institution-id institution-id-type="ISNI">0000 0004 1757 9741</institution-id><institution>Medical Physics Department, Centro di Riferimento Oncologico di Aviano (CRO) IRCCS, </institution></institution-wrap>33081 Aviano, Italy </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="GRID">grid.418321.d</institution-id><institution-id institution-id-type="ISNI">0000 0004 1757 9741</institution-id><institution>Radiation Oncology Department, Centro di Riferimento Oncologico di Aviano (CRO) IRCCS, </institution></institution-wrap>33081 Aviano, Italy </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="GRID">grid.476410.0</institution-id><institution-id institution-id-type="ISNI">0000 0004 0608 7258</institution-id><institution>Guerbet SA, </institution></institution-wrap>Villepinte, France </aff></contrib-group><pub-date pub-type="epub"><day>19</day><month>3</month><year>2021</year></pub-date><pub-date pub-type="pmc-release"><day>19</day><month>3</month><year>2021</year></pub-date><pub-date pub-type="collection"><year>2021</year></pub-date><volume>11</volume><elocation-id>6418</elocation-id><history><date date-type="received"><day>17</day><month>9</month><year>2020</year></date><date date-type="accepted"><day>28</day><month>2</month><year>2021</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2021</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is licensed under a Creative Commons Attribution 4.0 International License, which permits use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons licence, and indicate if changes were made. The images or other third party material in this article are included in the article's Creative Commons licence, unless indicated otherwise in a credit line to the material. If material is not included in the article's Creative Commons licence and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this licence, visit <ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>.</license-p></license></permissions><abstract id="Abs1"><p id="Par1">Deep learning models based on medical images play an increasingly important role for cancer outcome prediction. The standard approach involves usage of convolutional neural networks (CNNs) to automatically extract relevant features from the patient&#x02019;s image and perform a binary classification of the occurrence of a given clinical endpoint. In this work, a 2D-CNN and a 3D-CNN for the binary classification of distant metastasis (DM) occurrence in head and neck cancer patients were extended to perform time-to-event analysis. The newly built CNNs incorporate censoring information and output DM-free probability curves as a function of time for every patient. In total, 1037 patients were used to build and assess the performance of the time-to-event model. Training and validation was based on 294 patients also used in a previous benchmark classification study while for testing 743 patients from three independent cohorts were used. The best network could reproduce the good results from 3-fold cross validation [Harrell&#x02019;s concordance indices (HCIs) of 0.78, 0.74 and 0.80] in two out of three testing cohorts (HCIs of 0.88, 0.67 and 0.77). Additionally, the capability of the models for patient stratification into high and low-risk groups was investigated, the CNNs being able to significantly stratify all three testing cohorts. Results suggest that image-based deep learning models show good reliability for DM time-to-event analysis and could be used for treatment personalisation.</p></abstract><kwd-group kwd-group-type="npg-subject"><title>Subject terms</title><kwd>Cancer</kwd><kwd>Biomarkers</kwd><kwd>Medical research</kwd><kwd>Oncology</kwd></kwd-group><funding-group><award-group><funding-source><institution>Universit&#x000e4;tsklinik M&#x000fc;nchen (6933)</institution></funding-source></award-group><open-access><p>Open Access funding enabled and organized by Projekt DEAL.</p></open-access></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2021</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Introduction</title><p id="Par2">Biology-driven personalised treatment is a landmark in the development of precision radiation oncology. Over the past years, several biomarkers (e.g. human papilloma virus (HPV) status or positron emission tomography based hypoxia levels) have been proposed to help clinical decision making for improved management of certain cancers<sup><xref ref-type="bibr" rid="CR1">1</xref></sup>.</p><p id="Par3">Radiomics relies on non-invasive biomarkers based on advanced imaging analytics<sup><xref ref-type="bibr" rid="CR2">2</xref></sup> and has been shown to be able to unravel tumor phenotype in multiple studies<sup><xref ref-type="bibr" rid="CR3">3</xref>&#x02013;<xref ref-type="bibr" rid="CR6">6</xref></sup>. The typical radiomics workflow involves imaging of the patient, identification of the gross tumor volume (GTV), conversion of images to higher dimensional data (i.e. radiomic features) and the subsequent integration and mining of these data for model building. Thus, radiomics allows to build diagnostic, prognostic and predictive models for clinical outcomes using imaging data which is acquired as a part of clinical routine<sup><xref ref-type="bibr" rid="CR7">7</xref></sup>. However, several challenges have to be faced during radiomic signature development such as issues in reproducibility, standardisation in both the image acquisition, the handcrafted feature extraction and the statistical model building, and other limitations and pitfalls<sup><xref ref-type="bibr" rid="CR8">8</xref></sup>. For instance, Welch et al.<sup><xref ref-type="bibr" rid="CR9">9</xref></sup> have shown that a previously developed set of radiomic features was a surrogate for tumor volume, highlighting the need for simple baseline models to be compared with advanced radiomic signatures.</p><p id="Par4">Conventional radiomic models are built using machine learning algorithms: for binary classification tasks (e.g. whether the patient survives or not) typically random forests, support vector machines or artificial neural networks (ANNs) are used, while for survival analysis (i.e. predicting time-to-events or risks for clinical outcomes) Cox proportional hazards regression, random survival forests, and support vector survival methods are commonly used<sup><xref ref-type="bibr" rid="CR10">10</xref></sup>. In the past few years, a sub-field of machine learning called deep learning<sup><xref ref-type="bibr" rid="CR11">11</xref></sup> has been widely and successfully adopted in a variety of fields. For medical applications, convolutional neural networks (CNNs) have been extensively used as they take spatial information into account and are therefore the preferred architecture for image-related tasks<sup><xref ref-type="bibr" rid="CR12">12</xref></sup>. Compared to traditional radiomics, deep learning based radiomics approaches exploit the inherent non-linearity of deep neural networks to learn relevant features automatically<sup><xref ref-type="bibr" rid="CR13">13</xref></sup>. This enables end-to-end analysis as the contoured or cropped images are given to the algorithm which outputs directly the predictions, therefore skipping the step of handcrafted feature extraction and the efforts connected with it.</p><p id="Par5">Deep learning based models have been proven successful in a wide range of medical applications including classification of skin cancer<sup><xref ref-type="bibr" rid="CR14">14</xref></sup> or extranodal extensions in head and neck squamous cell carcinoma<sup><xref ref-type="bibr" rid="CR15">15</xref></sup>, fully-automated localisation and segmentation of rectal cancer<sup><xref ref-type="bibr" rid="CR16">16</xref></sup> and mortality risk stratification for lung cancer patients<sup><xref ref-type="bibr" rid="CR17">17</xref></sup> to name a few. Within radiology and radiotherapy, examples are detection of mammographic lesions<sup><xref ref-type="bibr" rid="CR18">18</xref></sup>, cone-beam computed tomography (CT) intensity correction<sup><xref ref-type="bibr" rid="CR19">19</xref></sup> or synthetic CT generation from magnetic resonance images<sup><xref ref-type="bibr" rid="CR20">20</xref>,<xref ref-type="bibr" rid="CR21">21</xref></sup>. Also they have been shown to either equal or outperform their engineered features counterparts in many classification tasks. For instance Diamant et al.<sup><xref ref-type="bibr" rid="CR22">22</xref></sup> could show that a CNN trained from scratch on pre-treatment CT images of head and neck cancer patients outperformed a traditional radiomics model developed in a previous study<sup><xref ref-type="bibr" rid="CR5">5</xref></sup> using exactly the same CT images. In both studies distant metastasis (DM), loco-regional failure and overall survival were used as endpoints for 300 patients available on the cancer imaging archive (TCIA)<sup><xref ref-type="bibr" rid="CR23">23</xref>,<xref ref-type="bibr" rid="CR24">24</xref></sup> and coming from four different hospitals in Quebec, Canada. Compared to dichotomised classification, only few deep learning based survival analysis models have been applied to the field of medical imaging, although several models have already been proposed<sup><xref ref-type="bibr" rid="CR25">25</xref>&#x02013;<xref ref-type="bibr" rid="CR27">27</xref></sup>. Katzman et al.<sup><xref ref-type="bibr" rid="CR26">26</xref></sup> implemented DeepSurv, a deep neural network which outputs a single number, that is, the log-risk function of a Cox proportional hazards model allowing for personalized treatment recommendations. Gensheimer et al.<sup><xref ref-type="bibr" rid="CR27">27</xref></sup> proposed Nnet-survival, a scalable discrete-time survival model for neural networks capable of outputting survival curves for every patient in a given time span.</p><p id="Par6">Head and neck cancers are a set of very heterogeneous malignancies which are diagnosed worldwide more than 830,000 times and lead to more than 430,000 deaths every year<sup><xref ref-type="bibr" rid="CR28">28</xref></sup>. Prognosis of these cancers depends on several factors including tumour site, TNM-stage, extracapsular nodal extension and HPV status, with 5-year survival rates that have been shown to vary from 90% for HPV<inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^{-}$$\end{document}</tex-math><mml:math id="M2"><mml:msup><mml:mrow/><mml:mo>-</mml:mo></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq1.gif"/></alternatives></inline-formula> early-stage tumors, to 80&#x02013;87% for HPV<inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^{+}$$\end{document}</tex-math><mml:math id="M4"><mml:msup><mml:mrow/><mml:mo>+</mml:mo></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq2.gif"/></alternatives></inline-formula> and 37&#x02013;58% for HPV<inline-formula id="IEq3"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^{-}$$\end{document}</tex-math><mml:math id="M6"><mml:msup><mml:mrow/><mml:mo>-</mml:mo></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq3.gif"/></alternatives></inline-formula> tumours presenting cervical lymph node metastasis<sup><xref ref-type="bibr" rid="CR29">29</xref></sup>. Therefore, models that are able to identify high and low risk patients and that can support clinical decision making are highly desirable and could for instance be used for dose de-escalation in radiotherapy, with the potential of decreasing long-term toxicity.</p><p id="Par7">With this work we wanted to assess the ability of CNN based approaches to accurately predict the occurrence of DM on several independent testing cohorts. To do so, we first downloaded the same 300 patients available on TCIA<sup><xref ref-type="bibr" rid="CR23">23</xref></sup> and replicated the results obtained by Diamant et al. on this cohort using cross validation (CV) on 294 patients with a 2D-CNN. Additionally, we performed CV with a 3D-CNN, in order to exploit the full volumetric information of the tumour. Then, we downloaded two additional head and neck cancer cohorts from TCIA, 136 patients from MAASTRO clinic in the Netherlands<sup><xref ref-type="bibr" rid="CR30">30</xref></sup> and 497 patients from Princess Margaret Cancer Centre (PMH) in Canada<sup><xref ref-type="bibr" rid="CR31">31</xref></sup> and obtained an additional cohort of 110 patients from Centro di Riferimento Oncologico (CRO) in Italy as a third independent testing cohort. This allowed us to perform a retrospective multicentric study with 1037 CTs of different head and neck cancer patients in total. An overview of the workflow and the cohort subdivision for training, validation and testing is shown in Figure <xref rid="Fig1" ref-type="fig">1</xref>.<fig id="Fig1"><label>Figure 1</label><caption><p>Cohorts training, validation and testing subdivision and analysis workflow. Firstly, the Canadian dataset used by Diamant et al.<sup><xref ref-type="bibr" rid="CR22">22</xref></sup> was used for 3-fold CV to find the hyper-parameters leading to the 3 best validation models. Then, the best validation models were applied to 1000 bootstrap samples of each of the 3 independent testing sets. The predictions of the 3 validation models for each bootstrap sample were averaged to obtain one model averaged prediction per bootstrap replicate. Prediction performance on one cohort was evaluated in terms of area under the curve (AUC) for binary classification and Harrell&#x02019;s concordance index (HCI) for time-to-event analysis.</p></caption><graphic xlink:href="41598_2021_85671_Fig1_HTML" id="MO1"/></fig></p><p id="Par8">Additionally, we extended the task of the network from binary classification to time-to-event analysis as no CNN being able to predict DM risk was found in literature. By combining Diamant&#x02019;s network in 2D and 3D with the survival model by Gensheimer et al.<sup><xref ref-type="bibr" rid="CR27">27</xref></sup> we have therefore constructed a deep learning framework which incorporates censoring information and is capable to output in an end-to-end fashion DM-free probability curves as function of time for every patient given the contoured pre-treatment CT image of the corresponding patient. Throughout this study, we also compared the performance of the CNNs with the performance of an ANN based on multiple clinical variables such as overall TNM stage, tumor site and volume to assess whether the CNN outperforms more classical prognostic models when it comes to discriminative performance or patient stratification. We also combined the image based CNNs with the clinical covariates based ANN in what we call CNN+Clinical, to see if a combined input could improve overall performance. Finally, to gain a better understanding of the importance of texture for the CNN prediction we performed a binary masking experiment with the input GTV and compared the performance on the testing sets with the performance achieved when using the standard image input.</p></sec><sec id="Sec2"><title>Results</title><sec id="Sec3"><title>Benchmark study comparison</title><p id="Par9">To verify that a CNN similar to the one built in the benchmark study<sup><xref ref-type="bibr" rid="CR22">22</xref></sup> is able to transfer its predictive performance to independent testing sets we first had to replicate the results obtained for the validation. For this, we built a 2D-CNN as well as 3D-CNN for classification and performed 3-fold CV on the same patient cohort used in the benchmark study. In general, the area under the curves (AUCs) obtained with the CNNs were smaller or in line with the ones obtained by Diamant et al. (see Table <xref rid="Tab1" ref-type="table">1</xref>). We also performed 3-fold CV with an ANN based on clinical variables and obtained CV AUCs which were higher than for our 2D-CNN and comparable to the ones of the 3D-CNN. For the combined CNN+Clinical models, we achieved validation AUCs which were on average higher than for the CNNs alone.<table-wrap id="Tab1"><label>Table 1</label><caption><p>Comparison of AUCs for CV in Diamant&#x02019;s study<sup><xref ref-type="bibr" rid="CR22">22</xref></sup> and CV and testing in our study.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Model</th><th align="left">CV diamant<sup><xref ref-type="bibr" rid="CR22">22</xref></sup></th><th align="left">3-fold CV</th><th align="left">Test MAASTRO</th><th align="left">Test PMH</th><th align="left">Test CRO</th></tr></thead><tbody><tr><td align="left">2D-CNN</td><td align="left">0.80&#x02013;0.88</td><td align="left">0.75; 0.83; 0.67</td><td align="left">0.81 (0.73&#x02013;0.89)</td><td align="left">0.62 (0.57&#x02013;0.67)</td><td align="left">0.80 (0.68&#x02013;0.89)</td></tr><tr><td align="left">2D-CNN+Clinical</td><td align="left">&#x02013;</td><td align="left">0.86; 0.74; 0.80</td><td align="left">0.89 (0.83&#x02013;0.94)</td><td align="left">0.66 (0.61&#x02013;0.71)</td><td align="left">0.71 (0.56&#x02013;0.83)</td></tr><tr><td align="left">3D-CNN</td><td align="left">&#x02013;</td><td align="left">0.83; 0.77; 0.79</td><td align="left">0.82 (0.74&#x02013;0.90)</td><td align="left">0.63 (0.58&#x02013;0.68)</td><td align="left">0.65 (0.47&#x02013;0.82)</td></tr><tr><td align="left">3D-CNN+Clinical</td><td align="left">&#x02013;</td><td align="left">0.82; 0.80; 0.90</td><td align="left">0.87 (0.80&#x02013;0.94)</td><td align="left">0.66 (0.61&#x02013;0.71)</td><td align="left">0.67 (0.55&#x02013;0.79)</td></tr><tr><td align="left">ANN</td><td align="left">&#x02013;</td><td align="left">0.83; 0.73; 0.86</td><td align="left">0.86 (0.79&#x02013;0.92)</td><td align="left">0.66 (0.61&#x02013;0.71)</td><td align="left">0.74 (0.62&#x02013;0.84)</td></tr></tbody></table><table-wrap-foot><p>The numbers shown for the 3-fold CV are the AUCs obtained on the CV subsets. Numbers for the testing sets represent the median AUC and in brackets the 83% confidence intervals obtained via bootstrap re-sampling. Note that the AUCs in columns 2 and 3 can be compared directly as the CV was performed on the same cohort<sup><xref ref-type="bibr" rid="CR23">23</xref></sup>.</p></table-wrap-foot></table-wrap></p></sec><sec id="Sec4"><title>Adding independent testing cohorts</title><p id="Par10">Without changing any hyper-parameter we applied the three best models from validation on a large number of patients never seen by the networks and coming from different institutions than the train and validation cohort, i.e. on three independent testing sets (Fig. <xref rid="Fig1" ref-type="fig">1</xref>). To be able to statistically compare results among different cohorts we performed bootstrap re-sampling on all the testing cohorts. The median AUC with 83% confidence interval resulting from bootstrapping (see "<xref rid="Sec12" ref-type="sec">Statistical analysis</xref>" subsection under "<xref rid="Sec7" ref-type="sec">Methods</xref>" for the reason behind using 83% confidence) is given for each testing cohort in Table <xref rid="Tab1" ref-type="table">1</xref>. The model which most consistently transferred good validation into independent testing was the 2D-CNN, being able to achieve good AUCs of around 0.8 for two out of three testing cohorts, the PMH cohort being statistically worse than the other two, with a median AUC of 0.62. The 3D-CNN, the 2D and 3D CNN+Clinical and the ANN achieved very good results on the MAASTRO cohort, the other two cohorts being worse. In general, models were always able to transfer good validation prediction performance to the MAASTRO testing cohort, while for the CRO cohort the performance depended on the architecture. For the PMH cohort no network architecture yielded a median AUC above 0.66.</p></sec><sec id="Sec5"><title>Performing time-to-event analysis</title><p id="Par11">Several authors<sup><xref ref-type="bibr" rid="CR25">25</xref>&#x02013;<xref ref-type="bibr" rid="CR27">27</xref></sup> have underlined the issue of censored data when performing binary outcome classification and have proposed extensions to deep learning models that are able to incorporate time-to-event information and thus censoring. Therefore, we extended our CNNs based on Diamant&#x02019;s study<sup><xref ref-type="bibr" rid="CR22">22</xref></sup> with the discrete-time survival model by Gensheimer et al.<sup><xref ref-type="bibr" rid="CR27">27</xref></sup>. The same extension was also applied to the CNN+Clinical and the ANN. These networks were trained from scratch following the same workflow used for binary classification, additionally incorporating the time-to-event/follow-up times for every patient. The output of such networks is for every patient a DM-free probability curve, as shown in Fig. <xref rid="Fig2" ref-type="fig">2</xref> for two selected patients with and without DM occurrence.<fig id="Fig2"><label>Figure 2</label><caption><p>Time-to-event network output. The DM-free probability curves shown are taken from two exemplary patients of the CRO testing cohort using the 3D-CNN as prognostic model. Note that the network output is at pre-defined discrete time points, the lines being drawn for visualisation purposes.</p></caption><graphic xlink:href="41598_2021_85671_Fig2_HTML" id="MO2"/></fig></p><p id="Par12">We first performed time-to-event analysis using the same image input as for the binary classification task, i.e. the CT masked with the primary and lymph node GTVs. Additionally, to analyse the importance of texture for the CNNs we performed an experiment: instead of giving the network the GTV with re-scaled Hounsfield Units inside (standard image input), we gave the networks a binary image, i.e. the GTV with all values inside set to <inline-formula id="IEq4"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$+1$$\end{document}</tex-math><mml:math id="M8"><mml:mrow><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq4.gif"/></alternatives></inline-formula> and everything outside the GTV set to <inline-formula id="IEq5"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$-1$$\end{document}</tex-math><mml:math id="M10"><mml:mrow><mml:mo>-</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq5.gif"/></alternatives></inline-formula> (see Supplementary Fig. <xref rid="MOESM1" ref-type="media">S1</xref>). In this way we can exclude that during the learning process the networks are looking at anything else except from the tumor volume and 3D shape (for the 3D-CNN) or the tumor area and 2D shape (for the 2D-CNN). For the binary masking experiment we took the best hyper-parameters obtained with standard image input and then followed again the exact same data splitting and workflow as depicted in Fig. <xref rid="Fig1" ref-type="fig">1</xref>, to ensure that no information flows from the testing cohorts into the hyper-parameters used for training.</p><p id="Par13">To evaluate the performance of time-to-event models on different cohorts we used HCI<sup><xref ref-type="bibr" rid="CR32">32</xref></sup> as it incorporates censoring in the computation of the metric and is therefore well suited to assess performance for this task.</p><p id="Par14">Table <xref rid="Tab2" ref-type="table">2</xref> shows the results for CV and testing for both the standard image input and the binary masked image input. In general, trends from binary classification have been confirmed for the MAASTRO and the PMH cohort while we observed an overall improvement for the CRO cohort. Both the 2D/3D CNNs and the 2D/3D CNNs+Clinical were able to transfer good validation results to two out of three cohorts. The same holds for the ANN, although results were on average slightly worse than for the CNNs and the CNNs+Clinical. Again, the PMH dataset was found to be worst for all models.</p><p id="Par15">Of interest is also the fact that when performing the binary masking experiment the HCI for the 3D-CNN and for the 3D-CNN+Clinical decreased by no more than 0.03 (i.e. not significantly). A more substantial drop (yet still not significant in terms of confidence intervals) was observed for the 2D-CNN and the 2D-CNN+Clinical.<table-wrap id="Tab2"><label>Table 2</label><caption><p>Comparison of HCIs for CV and testing for different time-to-event models and the two different image input scenarios.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Model</th><th align="left">Image input</th><th align="left">3-fold CV</th><th align="left">Test MAASTRO</th><th align="left">Test PMH</th><th align="left">Test CRO</th></tr></thead><tbody><tr><td align="left">2D-CNN</td><td align="left">Standard</td><td align="left">0.74; 0.75; 0.71</td><td align="left">0.81 (0.70&#x02013;0.91)</td><td align="left">0.64 (0.59&#x02013;0.69)</td><td align="left">0.86 (0.76&#x02013;0.93)</td></tr><tr><td align="left">2D-CNN+Clinical</td><td align="left">Standard</td><td align="left">0.78; 0.88; 0.80</td><td align="left">0.88 (0.82&#x02013;0.94)</td><td align="left">0.67 (0.61&#x02013;0.71)</td><td align="left">0.77 (0.66&#x02013;0.87)</td></tr><tr><td align="left">3D-CNN</td><td align="left">Standard</td><td align="left">0.78; 0.74; 0.80</td><td align="left">0.88 (0.80&#x02013;0.93)</td><td align="left">0.67 (0.62&#x02013;0.71)</td><td align="left">0.77 (0.60&#x02013;0.90)</td></tr><tr><td align="left">3D-CNN+Clinical</td><td align="left">Standard</td><td align="left">0.82; 0.84; 0.88</td><td align="left">0.88 (0.80&#x02013;0.94)</td><td align="left">0.66 (0.61&#x02013;0.71)</td><td align="left">0.74 (0.64&#x02013;0.85)</td></tr><tr><td align="left">2D-CNN</td><td align="left">Binary</td><td align="left">0.67; 0.76; 0.64</td><td align="left">0.75 (0.61&#x02013;0.86)</td><td align="left">0.69 (0.64&#x02013;0.73)</td><td align="left">0.76 (0.63&#x02013;0.89)</td></tr><tr><td align="left">2D-CNN+Clinical</td><td align="left">Binary</td><td align="left">0.90; 0.74; 0.78</td><td align="left">0.87 (0.81&#x02013;0.93)</td><td align="left">0.66 (0.61&#x02013;0.70)</td><td align="left">0.74 (0.64&#x02013;0.83)</td></tr><tr><td align="left">3D-CNN</td><td align="left">Binary</td><td align="left">0.77; 0.78; 0.80</td><td align="left">0.87 (0.80&#x02013;0.92)</td><td align="left">0.67 (0.62&#x02013;0.72)</td><td align="left">0.77 (0.65&#x02013;0.87)</td></tr><tr><td align="left">3D-CNN+Clinical</td><td align="left">Binary</td><td align="left">0.90; 0.80; 0.77</td><td align="left">0.85 (0.75&#x02013;0.94)</td><td align="left">0.67 (0.62&#x02013;0.72)</td><td align="left">0.72 (0.61&#x02013;0.82)</td></tr><tr><td align="left">ANN</td><td align="left">&#x02013;</td><td align="left">0.78; 0.82; 0.76</td><td align="left">0.87 (0.81&#x02013;0.92)</td><td align="left">0.66 (0.61&#x02013;0.71)</td><td align="left">0.74 (0.62&#x02013;0.84)</td></tr></tbody></table><table-wrap-foot><p>The numbers shown for the 3-fold CV are HCIs obtained for the CV subsets. Numbers for the testing sets represent the median HCI and in brackets the 83% confidence intervals obtained via bootstrap re-sampling. Note that HCIs for the ANN are shown only once because the only input are clinical variables of the corresponding patients.</p></table-wrap-foot></table-wrap></p><p id="Par16">In addition to the assessment of the discriminative power according to HCI, we also analysed patient stratification capability of the time-to-event networks. To do so, we found an optimal threshold to split the CV cohorts into high- and low-risk patients and then applied this threshold to the three independent testing cohorts. To infer whether the stratification on the testing cohorts was significant we applied the log rank test on the two patient groups. The resulting p value and corresponding Kaplan&#x02013;Meier plots for the two groups are shown in Fig. <xref rid="Fig3" ref-type="fig">3</xref> for the 3D-CNN and the 2D-CNN+Clinical with standard image input (because the average performance over all testing cohorts was the best and equal for these two models) and for the ANN as baseline. A complete list containing the patient stratification p values for all tested models can be found as Supplementary Table <xref rid="MOESM1" ref-type="media">S1</xref>.</p><p id="Par17">As can be seen in Fig. <xref rid="Fig3" ref-type="fig">3</xref>a,b, the CNNs were able to significantly stratify all three testing cohorts into high- and low-risk patient subgroups. Even though also the ANN significantly stratified all three testing cohorts (Fig. <xref rid="Fig3" ref-type="fig">3</xref>c), it should be noted that, as visible in the plots, the obtained difference between the two risk groups is less pronounced if compared to the 2D CNN+Clinical and the 3D CNN in two (MAASTRO and CRO) out of three testing cohorts.<fig id="Fig3"><label>Figure 3</label><caption><p>Kaplan&#x02013;Meier curves obtained when applying time-to-event models on the 3 testing cohorts. The testing patients were stratified into two groups by using an optimised threshold obtained during CV. The displayed p values were computed using the log rank test. (<bold>a</bold>) Kaplan&#x02013;Meier curves resulting from 2D-CNN+Clinical. (<bold>b</bold>) Kaplan&#x02013;Meier curves resulting from 3D-CNN. (<bold>c</bold>) Kaplan&#x02013;Meier curves resulting from ANN based on clinical variables.</p></caption><graphic xlink:href="41598_2021_85671_Fig3_HTML" id="MO3"/></fig></p></sec></sec><sec id="Sec6"><title>Discussion</title><p id="Par18">In general, it was shown that CT based 2D and 3D CNNs are well performing and reliable models for DM time-to-event analysis in head and neck cancer patients. The binary classification 2D-CNN built de novo in the benchmark study by Diamant et al.<sup><xref ref-type="bibr" rid="CR22">22</xref></sup> was chosen as a starting point, also making use of public data on the TCIA<sup><xref ref-type="bibr" rid="CR23">23</xref>,<xref ref-type="bibr" rid="CR24">24</xref></sup>. Compared to Diamant et al. we used a 3D network in addition to a 2D network and both the primary and the lymph node GTVs instead of only the primary GTV. When assessing whether this increase in complexity was justified based on the CV cohort, we observed an average increase in performance when going from the 2D-CNN to the 3D-CNN (Table <xref rid="Tab1" ref-type="table">1</xref>, 3rd column). This result suggested that some relevant information for DM outcome prediction is contained in the full 3D volume.</p><p id="Par19">The benchmark study did not evaluate model performance on an independent test set. To infer whether results obtained on the validation set (on which the hyper-parameter tuning is performed) can be transferred to independent testing sets we gathered three additional data sets from three different institutions in North America (PMH) and Europe (MAASTRO, CRO) and applied our trained models to them (Fig. <xref rid="Fig1" ref-type="fig">1</xref>). As can be seen in Table <xref rid="Tab1" ref-type="table">1</xref>, only the 2D-CNN was able to transfer good average CV AUCs to two out of three testing sets, contradicting the results obtained for CV alone and thus underlining the importance of using independent testing sets. To analyse whether a simpler model would be able to achieve similar results we also built an ANN based solely on clinical variables and obtained on average similar results to the 2D-CNN, although the values varied more between the different testing cohorts.</p><p id="Par20">To see if combined clinical and image information leads to better performance we extended the 2D and 3D CNNs to include clinical covariates but obtained no improvement in performance when looking at testing cohorts. It should be noted that we only used 7 clinical variables as these were the only ones available in all 4 cohorts. Using more covariates might increase the performance of the ANN and the CNN+Clinical.</p><p id="Par21">To overcome the issue of patients loss during follow-up, we extended all architectures with the time-to-event model by Gensheimer et al.<sup><xref ref-type="bibr" rid="CR27">27</xref></sup>. This allowed for incorporating censoring information in the training process and enabled output of DM-free probability curves at different time points for every patient (see Fig. <xref rid="Fig2" ref-type="fig">2</xref>). This could allow for better personalised treatments as the additional information on how fast an event occurs is available (which is particularly relevant for e.g. older patients). When looking at discriminative performance in terms of HCI we found that both the 2D and the 3D CNNs were able to transfer good validation results to two (MAASTRO and CRO) out of three testing cohorts (Table <xref rid="Tab2" ref-type="table">2</xref>), unlike the binary classification CNNs. This suggests that incorporating censoring information may lead to a more consistent performance. The 2D-CNN+Clinical achieved exactly the same testing performance as the 3D-CNN while the average testing HCI of the 2D-CNN, the 3D-CNN+Clinical and the ANN was slightly worse.</p><p id="Par22">Although results were significantly better than 0.5, the PMH cohort was the worst for both tasks, leading at best to an AUC of 0.66 (83% CI 0.61&#x02013;0.71) and a HCI of 0.69 (83% CI 0.64&#x02013;0.73). Considering the substantial GTV volume difference between the other cohorts and PMH (see Table <xref rid="Tab3" ref-type="table">3</xref>) we exploited the high number of patients in this cohort (497) to tackle the performance problem by training time-to-event CNNs and ANNs from scratch using only the PMH cohort. The best hyper-parameters were found by applying 3-fold CV several times on the first 50% of the cohort. Again, the three CV models leading to the best validation HCI were then applied on the second 50% of the cohort (using model averaging and bootstrap re-sampling). However, we achieved no improvement on average, the best result being a HCI of 0.69 (see Supplementary Table <xref rid="MOESM1" ref-type="media">S2</xref>). In a previously published study, Kwan et al.<sup><xref ref-type="bibr" rid="CR4">4</xref></sup> had also used a subset of PMH with a traditional radiomics model to discriminate DM risk. Their best HCI of 0.71 is in good agreement with our results, hinting towards the fact that no matter which models are used, some cohorts might be more challenging than others, at least in terms of discriminative power.</p><p id="Par23">In this work, we also performed a binary masking experiment on the CNN input image. Instead of giving the network the GTV contours with re-scaled Hounsfield Units inside, we set all values inside the GTV to <inline-formula id="IEq6"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$+1$$\end{document}</tex-math><mml:math id="M12"><mml:mrow><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq6.gif"/></alternatives></inline-formula> and then fed the resulting image to the time-to-event CNNs (see Supplementary Fig. <xref rid="MOESM1" ref-type="media">S1</xref>). The performance of the networks did not change significantly for both the 2D-CNN and the 3D-CNN, although a more notable drop was observed for the 2D-CNN (Table <xref rid="Tab2" ref-type="table">2</xref>). These results suggest that while tumor texture might increase performance if the CNN is limited to 2D, it is of less relevance for the 3D-CNN, the volume and 3D shape of the GTVp and GTVn being sufficient to achieve good testing performances. The latter finding is in agreement with the result obtained by Welch et al.<sup><xref ref-type="bibr" rid="CR9">9</xref></sup> using a traditional radiomics model.</p><p id="Par24">Finally, we also assessed the performance of our models in terms patient stratification capability. To prevent information flow from testing to training/validation we found a threshold to split patients in high- and low-risk groups using the validation cohort and then applied this threshold to the three testing cohorts. Our best performing models in terms of HCI, that is, the 2D-CNN+Clinical and the 3D-CNN with standard image as input, were also found to achieve good results for patient stratification, being able to significantly separate all three testing cohorts (log-rank test p values <inline-formula id="IEq7"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$&#x0003c; 0.05$$\end{document}</tex-math><mml:math id="M14"><mml:mrow><mml:mo>&#x0003c;</mml:mo><mml:mn>0.05</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq7.gif"/></alternatives></inline-formula>). Clinically speaking, this result might be of relevance as CNNs could be used to determine risk groups that might not respond well, or respond better. This information could then be used for treatment (or dose level) adaptation. The ANN was slightly worse than the CNNs, the separation between the high-risk and the low-risk groups being less pronounced for the MAASTRO and CRO cohorts, as can be seen in Fig. <xref rid="Fig3" ref-type="fig">3</xref>.</p><p id="Par25">Recently, a study with 291 patients investigating different deep learning approaches for predicting loco-regional head and neck tumor control was published<sup><xref ref-type="bibr" rid="CR33">33</xref></sup>. Censoring information was incorporated in all models using a different time-to-event model than in our study<sup><xref ref-type="bibr" rid="CR26">26</xref></sup>. Similarly to this work, the authors found a CT based time-to-event 3D-CNN to be the best performing model, both in terms of discriminative power as measured with HCI and in terms of patient stratification capability. Regarding the performance of their baseline clinical model in terms of HCI, a more considerable drop with respect to the 3D-CNN was found. In contrast to our study, they found the 2D-CNNs to be substantially worse than the 3D-CNNs, although it should be kept in mind that the prognosis endpoint was a different one.</p><p id="Par26">The main drawback of this study consists in its retrospective nature. Consequently, not all clinical background information was available. For example, for many patients there is uncertainty on whether they received surgery after the primary radiotherapy treatment (see &#x0201c;<xref rid="Sec8" ref-type="sec">Patient cohorts</xref>&#x0201d; subsection), a fact which might change the outcome of the treatment. For the PMH cohort this information is missing for all patients which might be one of the reasons for its underperformance. Therefore, large prospective studies could be important in the assessment of outcome prediction models. Additionally, as a consequence of our findings it is even more crucial to reduce inter-physician variability in the contouring stage, as the binary masking experiment underlined the importance of tumor volume and shape. Future studies could address whether more advanced CNN architectures like ResNet<sup><xref ref-type="bibr" rid="CR34">34</xref></sup> or DenseNet<sup><xref ref-type="bibr" rid="CR35">35</xref></sup> improve DM outcome prediction. It also remains to be seen if additional imaging (e.g. positron emission tomography or magnetic resonance imaging) or genomic biomarkers can enhance the performance of deep learning algorithms for binary classification or time-to-event prediction.</p><p id="Par27">In conclusion, this report highlighted via a large number of independent testing patients the efficacy of image based deep learning models for DM binary classification and time-to-event analysis.</p></sec><sec id="Sec7"><title>Methods</title><sec id="Sec8"><title>Patient cohorts</title><p id="Par28">For this study, 4 different cohorts totalling 1037 patients with head and neck cancer were used. All patients selected for this study received either radiation alone or chemo-radiation as main treatment and have a follow-up time larger than 2 years. All patients which had a metastasis at time of diagnosis were excluded.<list list-type="bullet"><list-item><p id="Par29">Canadian benchmark cohort: consists of 298 patients treated at 4 different hospitals in Quebec, Canada. Out of 298 patients, 294 were used for this study as few patients had to be excluded due to data corruption or non-clear identification of the RTSTRUCT corresponding to the CT. This cohort was used in the benchmark study by Diamant et al.<sup><xref ref-type="bibr" rid="CR22">22</xref></sup> and in a traditional radiomics study<sup><xref ref-type="bibr" rid="CR5">5</xref></sup> on which the former was based. All patients received either radiation alone or chemo-radiation as main treatment modality. For three out of four hospitals information on surgery was not available. Out of the 88 patients for whom surgery information was available, 10 received it. More detailed information on this cohort can be found on TCIA<sup><xref ref-type="bibr" rid="CR23">23</xref></sup>.</p></list-item><list-item><p id="Par30">MAASTRO cohort: consists of 137 patients treated at Maastricht Radiation Oncology clinic in the Netherlands. Out of 137 patients, 1 had to be excluded due to metastasis at first diagnosis. This cohort was used for survival analysis by Aerts et al.<sup><xref ref-type="bibr" rid="CR3">3</xref></sup> in one of the first traditional radiomics studies, but to our knowledge it was never used to predict DM outcome. All patients were treated with radiotherapy and for all patients information on surgery was available: on 6 out of 136 cancer surgery was performed. More information on this cohort can be found on TCIA<sup><xref ref-type="bibr" rid="CR30">30</xref></sup>.</p></list-item><list-item><p id="Par31">PMH cohort: consists of 606 patients treated at Princess Margaret Cancer Centre in Canada. Out of 606, 497 were used for this study as the remaining ones had to be exluded due to short follow-up time, missing RTSTRUCT or missing GTV for instance due to surgical resection. Although information on surgery was not available in the clinical data sheet, contour names for some patients suggested that a surgery or a resection was performed. All patients were treated with either radiotherapy alone or chemo-radiotherapy as primary treatment. Subsets of this cohort were used in several studies<sup><xref ref-type="bibr" rid="CR4">4</xref>,<xref ref-type="bibr" rid="CR9">9</xref>,<xref ref-type="bibr" rid="CR36">36</xref></sup>, although only Kwan et al. applied a traditional radiomics model on it to discriminate DM risk. More detailed information on this cohort can be found on TCIA<sup><xref ref-type="bibr" rid="CR31">31</xref></sup>.</p></list-item><list-item><p id="Par32">CRO cohort: consist of 110 patients treated at Centro di Riferimento Oncologico Aviano in Italy. Only those patients were selected who were concordant with the other cohorts in terms of clinical specifications (e.g. tumor site, treatment modality, etc.). All patients were treated with either radiation alone or with chemo-radiation as primary treatment and no patient received surgery. This cohort was obtained via a collaboration and can be shared upon request.</p></list-item></list>For all patients, the pre-treatment CT and primary (GTVp) and lymph node (GTVn) gross tumor volumes contoured by expert physicians of the corresponding hospitals were available. For all cohorts but PMH the GTVns were clearly labeled in the RTSTRUCT. For PMH every region of interest was contoured, thus to select the GTVn we looked into every patient manually and labeled as GTVns those nodes which were inside the clinical target volume. After that, under consultation of an expert physician in our department, we excluded all previously selected lymph nodes which had a volume smaller than 2 cm<inline-formula id="IEq8"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^3$$\end{document}</tex-math><mml:math id="M16"><mml:msup><mml:mrow/><mml:mn>3</mml:mn></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq8.gif"/></alternatives></inline-formula>. Table <xref rid="Tab3" ref-type="table">3</xref> compares some relevant clinical variables and specifics among the different cohorts used in this study.</p><p id="Par33">The Canadian benchmark cohort, MAASTRO and PMH are publicly available datasets and were retrieved from TCIA in anonymised form. The CRO patient data was analysed retrospectively, in anonymised form and are part of two studies approved by the Unique Regional Ethics Committee, with following approval numbers: CRO-2017-50 and CRO-2019-66. All methods were carried out in accordance with relevant guidelines and regulations. Informed consent was obtained from all patients.<table-wrap id="Tab3"><label>Table 3</label><caption><p>Comparison of clinical variables and percentage of patients having a DM among the different cohorts used in this study.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Cohort</th><th align="left">Male/female</th><th align="left">Median age (years)</th><th align="left">Overall stage I/II/III/IV</th><th align="left">Median GTVp + GTVn</th><th align="left">DM</th></tr></thead><tbody><tr><td align="left">Benchmark</td><td align="left">76/24%</td><td align="left">63</td><td align="left">1/9/20/68%</td><td align="left">31.9 cm<inline-formula id="IEq9"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^3$$\end{document}</tex-math><mml:math id="M18"><mml:msup><mml:mrow/><mml:mn>3</mml:mn></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq9.gif"/></alternatives></inline-formula></td><td align="left">13.3%</td></tr><tr><td align="left">MAASTRO</td><td align="left">81/19%</td><td align="left">61</td><td align="left">17/8/16/57%</td><td align="left">19.0 cm<inline-formula id="IEq10"><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^3$$\end{document}</tex-math><mml:math id="M20"><mml:msup><mml:mrow/><mml:mn>3</mml:mn></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq10.gif"/></alternatives></inline-formula></td><td align="left">5.8%</td></tr><tr><td align="left">PMH</td><td align="left">80/20%</td><td align="left">61</td><td align="left">1/6/14/78%</td><td align="left">39.4 cm<inline-formula id="IEq11"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^3$$\end{document}</tex-math><mml:math id="M22"><mml:msup><mml:mrow/><mml:mn>3</mml:mn></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq11.gif"/></alternatives></inline-formula></td><td align="left">14.3%</td></tr><tr><td align="left">CRO</td><td align="left">63/37%</td><td align="left">57</td><td align="left">20/21/10/46%</td><td align="left">23.0 cm<inline-formula id="IEq12"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^3$$\end{document}</tex-math><mml:math id="M24"><mml:msup><mml:mrow/><mml:mn>3</mml:mn></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq12.gif"/></alternatives></inline-formula></td><td align="left">7.9%</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec9"><title>Network architectures</title><p id="Par34">Both our 2D-CNN and our 3D-CNN architectures are inspired by the 2D-CNN built by Diamant et al.<sup><xref ref-type="bibr" rid="CR22">22</xref></sup>. As depicted in Fig. <xref rid="Fig4" ref-type="fig">4</xref>, our 3D-CNN (and 2D-CNN) comprises 3 convolutional blocks followed by two fully connected layers, one dropout layer and the final output layer. Every convolutional block is formed by a convolutional layer, which allows to change the representation of the input data while taking into account spatial information, a max-pooling layer, which introduces invariance of the network to small translations of the input<sup><xref ref-type="bibr" rid="CR11">11</xref></sup> and drastically reduces the number of parameters, and a parametric rectified linear unit (PReLu) as non-linear activation function (not shown in Fig. <xref rid="Fig4" ref-type="fig">4</xref>). After the convolutional blocks, the input image has been translated into a set of features with shape (128, 1, 1, 1) which are flattened to a vector of shape (128, ) which in turn is given as input to the two following fully connected layers (again followed by a PReLu activation function which is not shown in Fig. <xref rid="Fig4" ref-type="fig">4</xref>). After that follows a dropout layer, in which a number of neurons given by the <italic>dropout_rate</italic> hyper-parameter is set to zero to prevent the network from over-fitting, and finally the last fully connected layer which, together with a sigmoid activation function (not shown), forms the output of the network For the binary classification network this is a single number between 0 and <inline-formula id="IEq13"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$+1$$\end{document}</tex-math><mml:math id="M26"><mml:mrow><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq13.gif"/></alternatives></inline-formula>.</p><p id="Par35">The main differences of our network with respect to the benchmark study<sup><xref ref-type="bibr" rid="CR22">22</xref></sup> are that we used both a 3D-CNN and a 2D-CNN and that we extended the CNNs with the survival model by Gensheimer et al.<sup><xref ref-type="bibr" rid="CR27">27</xref></sup> to be able to incorporate time-to-event information.</p><p id="Par36">To extend the network from 2D to 3D we used 3D Keras built-in functions for the convolutions and the max-pooling instead of the corresponding 2D functions. The image input for our 2D-CNN was the <inline-formula id="IEq14"><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$256 \times 256$$\end{document}</tex-math><mml:math id="M28"><mml:mrow><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq14.gif"/></alternatives></inline-formula> axial slice with the highest number of tumor pixels as in Diamant&#x02019;s study<sup><xref ref-type="bibr" rid="CR22">22</xref></sup>. The input for the 3D-CNN was the full 3D image of <inline-formula id="IEq15"><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$256 \times 256 \times 256$$\end{document}</tex-math><mml:math id="M30"><mml:mrow><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq15.gif"/></alternatives></inline-formula>. To avoid memory issues both the 2D and the 3D input image were reduced to <inline-formula id="IEq16"><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$128 \times 128 (\times 128$$\end{document}</tex-math><mml:math id="M32"><mml:mrow><mml:mn>128</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>128</mml:mn><mml:mo stretchy="false">(</mml:mo><mml:mo>&#x000d7;</mml:mo><mml:mn>128</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq16.gif"/></alternatives></inline-formula>) with the random cropping augmentation. To adapt the binary classification CNNs to time-to-event analysis the only change which had to be done at architecture level was to increase the number of neurons in the final layer from one to <italic>n_time_intervals</italic>, i.e. to the number of time intervals for which the network outputs an event probability (see Fig. <xref rid="Fig4" ref-type="fig">4</xref>). More details on the implementation of the discrete-time survival model<sup><xref ref-type="bibr" rid="CR27">27</xref></sup> within our framework can be found in the next subsection.</p><p id="Par37">To compare the performance of the CNN with a simple baseline model based solely on clinical variables we opted for a shallow artificial neural network. To be specific, our ANN has seven clinical variables as input for every patient, that is, patient&#x02019;s age, gender, tumor site, overall stage, T-stage, N-stage and primary plus lymph node tumor volume. Age in years and total tumor volume in cm<inline-formula id="IEq17"><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^3$$\end{document}</tex-math><mml:math id="M34"><mml:msup><mml:mrow/><mml:mn>3</mml:mn></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq17.gif"/></alternatives></inline-formula> were divided by 100 and given directly to the ANN while the other five variables, being of categorical nature, were one-hot encoded and then given to the ANN. The input layer of the network was followed by one fully connected hidden layer with 14 neurons, a ReLu activation function, a dropout layer and the final fully connected layer with sigmoid activation function. Exactly as for the CNN, the last layer of the ANN has 1 neuron for the binary classification task and <italic>n_time_intervals</italic> neurons for the time-to-event task.</p><p id="Par38">Finally, in an attempt to get a better performance by combining the two previous models, we also constructed what we call the CNN+Clinical. This network has the same architecture as the CNN but has as an additional input to the masked CT also the same seven clinical variables which were used for the ANN. The clinical variables are given to the CNN+Clinical at the level of the flattening layer by concatenating the 128 features from the convolutional blocks with the vector containing the seven (partly) one-hot encoded clinical covariates. The resulting vector is then fed to the remaining architecture with fully connected layers exactly as for the normal CNN (Fig. <xref rid="Fig4" ref-type="fig">4</xref>).<fig id="Fig4"><label>Figure 4</label><caption><p>Time-to-event 3D convolutional neural network architecture. The general network architecture is based on the 2D CNN implemented by Diamant et al.<sup><xref ref-type="bibr" rid="CR22">22</xref></sup> plus the discrete time-to-event model by Gensheimer et al.<sup><xref ref-type="bibr" rid="CR27">27</xref></sup>. Numbers for the convolutional layers represent the number of kernels, the kernel size and the stride. Numbers for the max-pooling layers represent the pool size and the stride.</p></caption><graphic xlink:href="41598_2021_85671_Fig4_HTML" id="MO4"/></fig></p></sec><sec id="Sec10"><title>Time-to-event analysis</title><p id="Par39">Although binary classification models as the ones built in<sup><xref ref-type="bibr" rid="CR5">5</xref>,<xref ref-type="bibr" rid="CR17">17</xref>,<xref ref-type="bibr" rid="CR22">22</xref></sup> achieve good prediction results, they are limited by the fact that time-to-event information is discarded during model training. Especially in some situations, for instance when predicting death or metastasis occurrence for older patients, it is fundamental for treatment customisation to know how fast the event would occur. Additionally, binary classification models usually are trained on a specific time point (e.g. 2-year OS) so they would need to be re-trained to make predictions for a different one<sup><xref ref-type="bibr" rid="CR27">27</xref></sup>.To overcome these issues we extended Diamant&#x02019;s network to include time-to-event information by incorporating the survival model by Gensheimer et al.<sup><xref ref-type="bibr" rid="CR27">27</xref></sup>. Their discrete-time survival model has been implemented in Keras<sup><xref ref-type="bibr" rid="CR37">37</xref></sup> (<ext-link ext-link-type="uri" xlink:href="https://github.com/MGensheimer/nnet-survival">https://github.com/MGensheimer/nnet-survival</ext-link>) and allows to train deep neural networks while taking into account patient follow-up times and outputs discrete event probability/survival curves for each patient. To our knowledge, the only applications of Gensheimer&#x02019;s model to the field of medical imaging are the study by Zhang et al.<sup><xref ref-type="bibr" rid="CR38">38</xref></sup>, who recently showed that their CT-based CNN survival model was able to outperform a Cox proportional hazards model based on radiomic features for pancreatic ductal adenocarcinoma and the study by Kim et al.<sup><xref ref-type="bibr" rid="CR39">39</xref></sup>, who developed a CT-based deep learning model which successfully predicts disease-free survival for lung adenocarcinoma patients.</p><p id="Par40">To train our time-to-event CNN, the custom loss function <italic>surv_likelihood</italic> provided by the authors is used. At architecture level, the number of neurons in the final connected layer has to be changed in order to match the desired number of output time intervals (Fig. <xref rid="Fig4" ref-type="fig">4</xref>). To be specific, every neuron in the final layer outputs the conditional probability that the patient does not get a DM in the corresponding time interval. To obtain a &#x0201c;survival curve&#x0201d; as displayed in Fig. <xref rid="Fig4" ref-type="fig">4</xref> the conditional probabilities up to a certain time interval have to be cumulatively multiplied. For our experiments we used 10 time intervals with a spacing of half a year.</p><p id="Par41">To evaluate the performance by taking into account time-to-event information (conversely to the AUC) we used HCI<sup><xref ref-type="bibr" rid="CR32">32</xref></sup> which is defined as the fraction of patients for which the predictions and the outcomes are concordant. For our purposes we adapted Lifelines&#x02019; concordance index (<ext-link ext-link-type="uri" xlink:href="https://lifelines.readthedocs.io/en/latest/lifelines.utils.html#lifelines.utils.concordance_index">https://lifelines.readthedocs.io/en/latest/lifelines.utils.html#lifelines.utils.concordance_index</ext-link>) function to compute the concordance between a patient&#x02019;s metastasis-free probability after 3 years and the patient&#x02019;s ground truth event time. Following this definition, a perfect HCI for our model would be 1.0, as patients with e.g. a low value for metastasis-free probability should be patients with a short time-to-event.</p></sec><sec id="Sec11"><title>Implementation details</title><p id="Par42">All the code needed for data pre-processing and running the models was written in Python 3.6. The networks were built and optimized with Tensorflow 2.2.0<sup><xref ref-type="bibr" rid="CR40">40</xref></sup> using the high-level library Keras<sup><xref ref-type="bibr" rid="CR37">37</xref></sup>. Training and testing was carried out on three different graphical processing units: a NVIDIA P6000 with 24 GB, a NVIDIA P5000 with 16 GB and a NVIDIA Titan V with 12 GB of memory. The input shape of the 3D CT of <inline-formula id="IEq18"><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$256 \times 256 \times 256$$\end{document}</tex-math><mml:math id="M36"><mml:mrow><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq18.gif"/></alternatives></inline-formula> and the batchsize of four were chosen so that the networks could fit also on the smallest graphical processing unit available. For the 2D-CNNs a batchsize of 32 was used. The weights and biases of the networks were optimized using the Adam algorithm<sup><xref ref-type="bibr" rid="CR41">41</xref></sup> with a constant learning rate of <inline-formula id="IEq19"><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$3 \times 10^{-4}$$\end{document}</tex-math><mml:math id="M38"><mml:mrow><mml:mn>3</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mrow><mml:mo>-</mml:mo><mml:mn>4</mml:mn></mml:mrow></mml:msup></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq19.gif"/></alternatives></inline-formula> and <inline-formula id="IEq20"><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$2 \times 10^{-4}$$\end{document}</tex-math><mml:math id="M40"><mml:mrow><mml:mn>2</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mrow><mml:mo>-</mml:mo><mml:mn>4</mml:mn></mml:mrow></mml:msup></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq20.gif"/></alternatives></inline-formula> for the 2D and 3D binary classification CNNs and of <inline-formula id="IEq21"><alternatives><tex-math id="M41">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$6 \times 10^{-4}$$\end{document}</tex-math><mml:math id="M42"><mml:mrow><mml:mn>6</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mrow><mml:mo>-</mml:mo><mml:mn>4</mml:mn></mml:mrow></mml:msup></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq21.gif"/></alternatives></inline-formula> and <inline-formula id="IEq22"><alternatives><tex-math id="M43">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$3 \times 10^{-4}$$\end{document}</tex-math><mml:math id="M44"><mml:mrow><mml:mn>3</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mrow><mml:mo>-</mml:mo><mml:mn>4</mml:mn></mml:mrow></mml:msup></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq22.gif"/></alternatives></inline-formula> for the 2D and 3D time-to-event CNNs. The respective loss functions used were binary cross-entropy and the custom loss function <italic>surv_likelihood</italic> by Gensheimer et al.<sup><xref ref-type="bibr" rid="CR27">27</xref></sup>.</p><p id="Par43">Prior to the augmentations, all images were isotropically re-sampled to a <inline-formula id="IEq23"><alternatives><tex-math id="M45">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$1 \times 1 \times 1$$\end{document}</tex-math><mml:math id="M46"><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>1</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq23.gif"/></alternatives></inline-formula> mm<inline-formula id="IEq24"><alternatives><tex-math id="M47">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^3$$\end{document}</tex-math><mml:math id="M48"><mml:msup><mml:mrow/><mml:mn>3</mml:mn></mml:msup></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq24.gif"/></alternatives></inline-formula> grid. Specifically, the binary masks where re-sampled in 3D using in-house code for shape-based interpolation<sup><xref ref-type="bibr" rid="CR42">42</xref></sup>. The full CTs where interpolated in 3D using linear interpolation. Moreover, all CT values were windowed to <inline-formula id="IEq25"><alternatives><tex-math id="M49">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$-500$$\end{document}</tex-math><mml:math id="M50"><mml:mrow><mml:mo>-</mml:mo><mml:mn>500</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq25.gif"/></alternatives></inline-formula> and <inline-formula id="IEq26"><alternatives><tex-math id="M51">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$+500$$\end{document}</tex-math><mml:math id="M52"><mml:mrow><mml:mo>+</mml:mo><mml:mn>500</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq26.gif"/></alternatives></inline-formula> Hounsfield Units and then re-scaled from <inline-formula id="IEq27"><alternatives><tex-math id="M53">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$-1$$\end{document}</tex-math><mml:math id="M54"><mml:mrow><mml:mo>-</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq27.gif"/></alternatives></inline-formula> to <inline-formula id="IEq28"><alternatives><tex-math id="M55">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$+1$$\end{document}</tex-math><mml:math id="M56"><mml:mrow><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq28.gif"/></alternatives></inline-formula> (standard image input). The linearly interpolated CTs were then masked with the shape-based interpolated binary masks to create the re-sampled masked CTs which were used as input to the networks. For the binary masking experiment, we set all values inside the GTV which were bigger than <inline-formula id="IEq29"><alternatives><tex-math id="M57">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$-1$$\end{document}</tex-math><mml:math id="M58"><mml:mrow><mml:mo>-</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq29.gif"/></alternatives></inline-formula> to <inline-formula id="IEq30"><alternatives><tex-math id="M59">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$+1$$\end{document}</tex-math><mml:math id="M60"><mml:mrow><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq30.gif"/></alternatives></inline-formula>, i.e. we used re-sampled binary masks as input (see Supplementary Fig. <xref rid="MOESM1" ref-type="media">S1</xref>).</p><p id="Par44">To decrease over-fitting, a weight decay term of <inline-formula id="IEq31"><alternatives><tex-math id="M61">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$1 \times 10^{-4}$$\end{document}</tex-math><mml:math id="M62"><mml:mrow><mml:mn>1</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:msup><mml:mn>10</mml:mn><mml:mrow><mml:mo>-</mml:mo><mml:mn>4</mml:mn></mml:mrow></mml:msup></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq31.gif"/></alternatives></inline-formula> was used for all models. Additionally, for the 2D and 3D CNNs and the 2D and 3D CNNs+Clinical several augmentations were implemented by adapting a multithreaded augmentation pipeline which is freely available online<sup><xref ref-type="bibr" rid="CR43">43</xref></sup> (<ext-link ext-link-type="uri" xlink:href="https://github.com/MIC-DKFZ/batchgenerators">https://github.com/MIC-DKFZ/batchgenerators</ext-link>) to our workflow. This allowed us to use a slightly wider range of augmentations and especially to perform the augmentations online (during the training), in contrast to Diamant&#x02019;s study. To be specific, we used random cropping to 128 pixels in each dimension with a central random shift of maximal 20% of the original <inline-formula id="IEq32"><alternatives><tex-math id="M63">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$256 \times 256 \times 256$$\end{document}</tex-math><mml:math id="M64"><mml:mrow><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn><mml:mo>&#x000d7;</mml:mo><mml:mn>256</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq32.gif"/></alternatives></inline-formula> image. We also used mirroring of the image with 50% probability, rotations of maximal 60 degrees and elastic deformations of the image of up to 25% of the size of the cropped image, both with a probability of 10% for the binary classification 3D-CNN, 20% for the time-to-event 3D-CNN and 50% for all 2D-CNNs. Finally, for all our models we also used dropout with a <italic>dropout_rate</italic> of 25%. For the CNNs all these hyper-parameters were found by performing a manual grid search meaning that we repeated the 3-fold CV on the Canadian benchmark data set until we were satisfied with the mean validation AUC or HCI which was achieved with given parameters. On the other hand, training for the ANN took much less, so after manually finding a range of parameters leading to good results we performed an automatic grid search over 16 different combinations of hyper-parameters (number of neurons in the hidden layer, weight decay, dropout probability, learning rate) to find the ones performing best on average for the 3-fold CV. For all CNN+Clinical models we manually explored some hyper-parameters around the best hyper-parameters for the CNNs and found that the ones leading to best average CV were the same as for the CNN.</p><p id="Par45">For all our network architectures we took the 3 models achieving the best average CV performance and used them for testing without any further change. To get a single prediction out of three we applied the validation models on test data and then averaged the resulting three predictions to obtain a single model averaged prediction per patient. A schematic view of the workflow is shown in Fig. <xref rid="Fig1" ref-type="fig">1</xref>.</p><p id="Par46">When doing binary classification the 3D-CNN and 3D-CNN+Clinical were trained for 500 epochs, using Keras callbacks to save the model&#x02019;s weights when an improvement in the AUC was observed. One 3-fold CV took on average 11 h. The 2D-CNN and 2D-CNN+Clinical were trained for 500 epochs, one 3-fold CV taking on average 30 minutes. The ANN was trained for 5000 epochs, one 3-fold CV taking on average 10 minutes. When performing time-to-event analysis the 2D and 3D CNNs and CNNs+Clinical were trained for 300 epochs while the ANN for 3000, taking on average 8 h for the 3D-CNNs, 20 min for the 2D-CNNs and around 10 min for the ANN. For all the networks and the tasks, we used early stopping with a patience of 200 for all CNNs and 2000 for the ANN to shorten the overall training time.</p></sec><sec id="Sec12"><title>Statistical analysis</title><p id="Par47">The performance of the different models on the different testing cohorts was investigated in a two-fold way: by looking at the discriminative power using the area under the receiver operating characteristic curve<sup><xref ref-type="bibr" rid="CR44">44</xref></sup> (AUC) for binary classification tasks or Harrell&#x02019;s concordance index<sup><xref ref-type="bibr" rid="CR32">32</xref></sup> (HCI) for time-to-event tasks and by looking at patient stratification capability using a threshold optimised on the validation cohort. To find the threshold we first averaged over the risk of all validation patients who got a DM, then we averaged over the risk of all the validation patients which did not get a DM and finally we took the mean of these two thresholds for all three CV models and obtained a final model averaged threshold to be used for stratification of the testing sets.</p><p id="Par48">Following the suggestions by<sup><xref ref-type="bibr" rid="CR45">45</xref>,<xref ref-type="bibr" rid="CR46">46</xref></sup> we assessed significance of difference by using both estimation statistics and statistical tests. We applied estimation statistics<sup><xref ref-type="bibr" rid="CR45">45</xref></sup>, i.e. we focused on the sizes of effects at level of data, by computing the median AUC or HCI with 83% confidence intervals from bootstrap re-sampling (consisting in generating many variants of a given dataset by repeatedly taking samples with replacement from the original set<sup><xref ref-type="bibr" rid="CR32">32</xref></sup>). We used 83% confidence as it can be shown<sup><xref ref-type="bibr" rid="CR47">47</xref>,<xref ref-type="bibr" rid="CR48">48</xref></sup> that two non-overlapping 83% confidence intervals mean that the two corresponding means/medians differ significantly with a significance level of 0.05. To be in line with literature<sup><xref ref-type="bibr" rid="CR5">5</xref>,<xref ref-type="bibr" rid="CR9">9</xref>,<xref ref-type="bibr" rid="CR17">17</xref></sup>, we also split the testing patients into a high- and low-risk group and checked whether the stratification is significant by computing the p value for the log-rank test. We consider results with p values <inline-formula id="IEq33"><alternatives><tex-math id="M65">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$&#x0003c; 0.05$$\end{document}</tex-math><mml:math id="M66"><mml:mrow><mml:mo>&#x0003c;</mml:mo><mml:mn>0.05</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="41598_2021_85671_Article_IEq33.gif"/></alternatives></inline-formula> to be statistically significant.</p></sec></sec><sec sec-type="supplementary-material"><title>Supplementary Information</title><sec id="Sec13"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="41598_2021_85671_MOESM1_ESM.pdf"><caption><p>Supplementary Information 1.</p></caption></media></supplementary-material></p></sec></sec></body><back><fn-group><fn><p><bold>Publisher's note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn><fn><p>These authors contributed equally: Marco Riboldi and Guillaume Landry.</p></fn></fn-group><sec><title>Supplementary Information</title><p>The online version contains supplementary material available at 10.1038/s41598-021-85671-y.</p></sec><ack><title>Acknowledgements</title><p>We would like to thank Philipp Wesp, Balthasar Schachtner and Micheal Ingrisch from the Department of Radiology of the University Hospital of the LMU Munich for the help in setting up the MIC-DKFZ augmentation pipeline. Special thanks go to Leonard Wee and Andre Dekker from MAASTRO, and to Scott Bratman from PMH for answering our inquiries about the datasets. Elia Lombardo acknowledges Moritz Rabe for the support for the figures shown in this work and Sophie Duque for the precious help and contribution to the shape-based interpolation code.</p></ack><notes notes-type="author-contribution"><title>Author contributions</title><p>G.L. and M.R. conceived the project. G.L., M.R., C.K. and E.L. pre-processed the data, analysed the results and wrote the manuscript. E.L. built the networks and analysed the data. M.A., V.G., G.F., G.F. and J.S. collected and curated the CRO data. S.M. guided the contour selection for the PMH cohort. S.M., S.C., M.N. and C.B. provided expert knowledge for clinical questions. K.P. provided the computational framework and critically reviewed the methodological approach. All authors read and approved the manuscript.</p></notes><notes notes-type="funding-information"><title>Funding</title><p>Open Access funding enabled and organized by Projekt DEAL.</p></notes><notes notes-type="data-availability"><title>Code availability</title><p>All the code needed to build the models and perform the analysis is freely available on GitLab: <ext-link ext-link-type="uri" xlink:href="https://gitlab.physik.uni-muenchen.de/********************/dl_based_prognosis">https://gitlab.physik.uni-muenchen.de/********************/dl_based_prognosis</ext-link>.</p></notes><notes notes-type="data-availability"><title>Data availability</title><p>Three out of four cohorts used in this study can be found on The Cancer Imaging Archive (TCIA)<sup><xref ref-type="bibr" rid="CR24">24</xref></sup>: Canadian benchmark dataset<sup><xref ref-type="bibr" rid="CR23">23</xref></sup>: 10.7937/K9/TCIA.2017.8oje5q00. MAASTRO dataset<sup><xref ref-type="bibr" rid="CR30">30</xref></sup>: 10.7937/tcia.2019.8kap372n. PMH dataset<sup><xref ref-type="bibr" rid="CR31">31</xref></sup>: 10.7937/tcia.2019.8dho2gls. The CRO dataset is the result of a collaboration and can be obtained upon request.</p></notes><notes id="FPar1" notes-type="COI-statement"><title>Competing interests</title><p id="Par51">The authors declare no competing interests.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Baumann</surname><given-names>M</given-names></name><etal/></person-group><article-title>Radiation oncology in the era of precision medicine</article-title><source>Nat. Rev. Cancer</source><year>2016</year><volume>16</volume><issue>4</issue><fpage>234</fpage><lpage>249</lpage><pub-id pub-id-type="doi">10.1038/nrc.2016.18</pub-id><?supplied-pmid 27009394?><pub-id pub-id-type="pmid">27009394</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Morin</surname><given-names>O</given-names></name><etal/></person-group><article-title>A deep look into the future of quantitative imaging in oncology: A statement of working principles and proposal for change</article-title><source>Int. J. Radiat. Oncol. Biol. Phys.</source><year>2018</year><volume>102</volume><issue>4</issue><fpage>1074</fpage><lpage>1082</lpage><pub-id pub-id-type="doi">10.1016/j.ijrobp.2018.08.032</pub-id><?supplied-pmid 30170101?><pub-id pub-id-type="pmid">30170101</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Aerts</surname><given-names>H</given-names></name><etal/></person-group><article-title>Decoding tumour phenotype by noninvasive imaging using a quantitative radiomics approach</article-title><source>Nat. Commun.</source><year>2014</year><volume>5</volume><fpage>4006</fpage><pub-id pub-id-type="doi">10.1038/ncomms5006</pub-id><?supplied-pmid 24892406?><pub-id pub-id-type="pmid">24892406</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kwan</surname><given-names>JYY</given-names></name><etal/></person-group><article-title>Radiomic biomarkers to refine risk models for distant metastasis in HPV-related oropharyngeal carcinoma</article-title><source>Int. J. Radiat. Oncol. Biol. Phys.</source><year>2018</year><volume>102</volume><issue>4</issue><fpage>1107</fpage><lpage>1116</lpage><pub-id pub-id-type="doi">10.1016/j.ijrobp.2018.01.057</pub-id><?supplied-pmid 29506884?><pub-id pub-id-type="pmid">29506884</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vallieres</surname><given-names>M</given-names></name><etal/></person-group><article-title>Radiomics strategies for risk assessment of tumour failure in head-and-neck cancer</article-title><source>Sci. Rep.</source><year>2017</year><volume>7</volume><fpage>1</fpage><lpage>33</lpage><pub-id pub-id-type="doi">10.1038/s41598-017-10371-5</pub-id><pub-id pub-id-type="pmid">28127051</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Buizza</surname><given-names>G</given-names></name><etal/></person-group><article-title>Early tumor response prediction for lung cancer patients using novel longitudinal pattern features from sequential pet/ct image scans</article-title><source>Phys. Med.</source><year>2018</year><volume>54</volume><fpage>21</fpage><lpage>29</lpage><pub-id pub-id-type="doi">10.1016/j.ejmp.2018.09.003</pub-id><?supplied-pmid 30337006?><pub-id pub-id-type="pmid">30337006</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gilies</surname><given-names>RJ</given-names></name><etal/></person-group><article-title>Radiomics: Images are more than pictures, they are data</article-title><source>Radiology</source><year>2016</year><volume>278</volume><issue>2</issue><fpage>563</fpage><lpage>577</lpage><pub-id pub-id-type="doi">10.1148/radiol.**********</pub-id><pub-id pub-id-type="pmid">26579733</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Avanzo</surname><given-names>M</given-names></name><name><surname>Stancanello</surname><given-names>J</given-names></name><name><surname>El Naqa</surname><given-names>I</given-names></name></person-group><article-title>Beyond imaging: The promise of radiomics</article-title><source>Phys. Med.</source><year>2017</year><volume>38</volume><fpage>122</fpage><lpage>139</lpage><pub-id pub-id-type="doi">10.1016/j.ejmp.2017.05.071</pub-id><?supplied-pmid 28595812?><pub-id pub-id-type="pmid">28595812</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Welch</surname><given-names>ML</given-names></name><etal/></person-group><article-title>Vulnerabilities of radiomic signature development: The need for safeguards</article-title><source>Radiother. Oncol.</source><year>2019</year><volume>130</volume><fpage>2</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1016/j.radonc.2018.10.027</pub-id><?supplied-pmid 30416044?><pub-id pub-id-type="pmid">30416044</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Avanzo</surname><given-names>M</given-names></name><etal/></person-group><article-title>Machine and deep learning methods for radiomics</article-title><source>Med. Phys.</source><year>2020</year><volume>47</volume><issue>5</issue><fpage>e185</fpage><lpage>e202</lpage><pub-id pub-id-type="doi">10.1002/mp.13678</pub-id><?supplied-pmid 32418336?><pub-id pub-id-type="pmid">32418336</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Goodfellow</surname><given-names>I</given-names></name><name><surname>Bengio</surname><given-names>Y</given-names></name><name><surname>Courville</surname><given-names>A</given-names></name></person-group><source>Deep Learning</source><year>2016</year><publisher-name>MIT Press</publisher-name></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shen</surname><given-names>C</given-names></name><etal/></person-group><article-title>An introduction to deep learning in medical physics: Advantages, potential, and challenges</article-title><source>Phys. Med. Biol.</source><year>2020</year><pub-id pub-id-type="doi">10.1088/1361-6560/ab6f51</pub-id><?supplied-pmid 33152716?><pub-id pub-id-type="pmid">33152716</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>LeCun</surname><given-names>Y</given-names></name><name><surname>Bengio</surname><given-names>Y</given-names></name><name><surname>Hinton</surname><given-names>G</given-names></name></person-group><article-title>Deep learning</article-title><source>Nature</source><year>2015</year><volume>521</volume><issue>7553</issue><fpage>436</fpage><lpage>44</lpage><pub-id pub-id-type="doi">10.1038/nature14539</pub-id><?supplied-pmid 26017442?><pub-id pub-id-type="pmid">26017442</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Esteva</surname><given-names>A</given-names></name><etal/></person-group><article-title>Dermatologist-level classification of skin cancer with deep neural networks</article-title><source>Nature</source><year>2017</year><volume>542</volume><fpage>115</fpage><lpage>118</lpage><pub-id pub-id-type="doi">10.1038/nature21056</pub-id><?supplied-pmid 28117445?><pub-id pub-id-type="pmid">28117445</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kann</surname><given-names>BH</given-names></name><etal/></person-group><article-title>Multi-institutional validation of deep learning for pretreatment identification of extranodal extension in head and neck squamous cell carcinoma</article-title><source>J. Clin. Oncol.</source><year>2020</year><volume>38</volume><issue>12</issue><fpage>1304</fpage><lpage>1311</lpage><pub-id pub-id-type="doi">10.1200/JCO.19.02031</pub-id><?supplied-pmid 31815574?><pub-id pub-id-type="pmid">31815574</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Trebeschi</surname><given-names>S</given-names></name><etal/></person-group><article-title>Deep learning for fully-automated localization and segmentation of rectal cancer on multiparametric mr</article-title><source>Sci. Rep.</source><year>2017</year><volume>7</volume><fpage>5301</fpage><pub-id pub-id-type="doi">10.1038/s41598-017-05728-9</pub-id><?supplied-pmid 28706185?><pub-id pub-id-type="pmid">28706185</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hosny</surname><given-names>A</given-names></name><etal/></person-group><article-title>Deep learning for lung cancer prognostication: A retrospective multi-cohort radiomics study</article-title><source>PLoS Med.</source><year>2018</year><volume>15</volume><issue>11</issue><fpage>e1002711</fpage><pub-id pub-id-type="doi">10.1371/journal.pmed.1002711</pub-id><?supplied-pmid 30500819?><pub-id pub-id-type="pmid">30500819</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kooi</surname><given-names>T</given-names></name><etal/></person-group><article-title>Large scale deep learning for computer aided detection of mammographic lesions</article-title><source>Med. Image Anal.</source><year>2017</year><volume>35</volume><fpage>303</fpage><lpage>312</lpage><pub-id pub-id-type="doi">10.1016/j.media.2016.07.007</pub-id><?supplied-pmid 27497072?><pub-id pub-id-type="pmid">27497072</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hansen</surname><given-names>DC</given-names></name><etal/></person-group><article-title>Scatternet: A convolutional neural network for cone-beam CT intensity correction</article-title><source>Med. Phys.</source><year>2019</year><volume>46</volume><issue>5</issue><fpage>2538</fpage><pub-id pub-id-type="doi">10.1002/mp.13175</pub-id><?supplied-pmid 31074519?><pub-id pub-id-type="pmid">31074519</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Han</surname><given-names>X</given-names></name></person-group><article-title>MR-based synthetic CT generation using a deep convolutional neural network method</article-title><source>Med. Phys.</source><year>2017</year><pub-id pub-id-type="doi">10.1002/mp.12155</pub-id><?supplied-pmid 29148586?><pub-id pub-id-type="pmid">29148586</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Spadea</surname><given-names>MF</given-names></name><etal/></person-group><article-title>Deep convolution neural network (DCNN) multiplane approach to synthetic CT generation from MR images-application in brain proton therapy</article-title><source>Int. J. Radiat. Oncol. Biol. Phys.</source><year>2019</year><volume>105</volume><issue>3</issue><fpage>495</fpage><lpage>503</lpage><pub-id pub-id-type="doi">10.1016/j.ijrobp.2019.06.2535</pub-id><?supplied-pmid 31271823?><pub-id pub-id-type="pmid">31271823</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Diamant</surname><given-names>A</given-names></name><name><surname>Chatterjee</surname><given-names>A</given-names></name><name><surname>Valli&#x000e8;res</surname><given-names>M</given-names></name><name><surname>Shenouda</surname><given-names>G</given-names></name><name><surname>Seuntjens</surname><given-names>J</given-names></name></person-group><article-title>Deep learning in head and neck cancer outcome prediction</article-title><source>Sci. Rep.</source><year>2019</year><volume>9</volume><issue>1</issue><fpage>2764</fpage><pub-id pub-id-type="doi">10.1038/s41598-019-39206-1</pub-id><?supplied-pmid 30809047?><pub-id pub-id-type="pmid">30809047</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vallieres</surname><given-names>M</given-names></name><etal/></person-group><article-title>Data from head-neck-PET-CT</article-title><source>Cancer Imaging Arch.</source><year>2017</year><pub-id pub-id-type="doi">10.7937/K9/TCIA.2017.8oje5q00</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Clark</surname><given-names>K</given-names></name><etal/></person-group><article-title>The cancer imaging archive (TCIA): Maintaining and operating a public information repository</article-title><source>J. Digit. Imaging</source><year>2013</year><volume>26</volume><issue>6</issue><fpage>1045</fpage><lpage>1057</lpage><pub-id pub-id-type="doi">10.1007/s10278-013-9622-7</pub-id><?supplied-pmid 23884657?><pub-id pub-id-type="pmid">23884657</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ching</surname><given-names>T</given-names></name><name><surname>Zhu</surname><given-names>X</given-names></name><name><surname>Garmire</surname><given-names>LX</given-names></name></person-group><article-title>Cox-nnet: An artificial neural network method for prognosis prediction of high-throughput omics data</article-title><source>PLoS Comput. Biol.</source><year>2018</year><volume>14</volume><issue>4</issue><fpage>e10006076</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1006076</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Katzman</surname><given-names>JL</given-names></name><etal/></person-group><article-title>Deepsurv: Personalized treatment recommender system using a cox proportional hazards deep neural network</article-title><source>BMC Med. Res. Methodol.</source><year>2018</year><volume>18</volume><issue>1</issue><fpage>24</fpage><pub-id pub-id-type="doi">10.1186/s12874-018-0482-1</pub-id><?supplied-pmid 29482517?><pub-id pub-id-type="pmid">29482517</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gensheimer</surname><given-names>MF</given-names></name><name><surname>Narasimhan</surname><given-names>B</given-names></name></person-group><article-title>A scalable discrete-time survival model for neural networks</article-title><source>PeerJ</source><year>2019</year><volume>7</volume><fpage>e6257</fpage><pub-id pub-id-type="doi">10.7717/peerj.6257</pub-id><?supplied-pmid 30701130?><pub-id pub-id-type="pmid">30701130</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bray</surname><given-names>F</given-names></name><etal/></person-group><article-title>Global cancer statistics 2018: GLOBOCAN estimates of incidence and mortality worldwide for 36 cancers in 185 countries</article-title><source>CA Cancer J. Clin.</source><year>2018</year><volume>68</volume><issue>6</issue><fpage>394</fpage><lpage>424</lpage><pub-id pub-id-type="doi">10.3322/caac.21492</pub-id><pub-id pub-id-type="pmid">30207593</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cramer</surname><given-names>JD</given-names></name><etal/></person-group><article-title>The changing therapeutic landscape of head and neck cancer</article-title><source>Nat. Rev. Clin. Oncol.</source><year>2019</year><volume>16</volume><fpage>669</fpage><lpage>683</lpage><pub-id pub-id-type="doi">10.1038/s41571-019-0227-z</pub-id><?supplied-pmid 31189965?><pub-id pub-id-type="pmid">31189965</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wee</surname><given-names>L</given-names></name><name><surname>Dekker</surname><given-names>A</given-names></name></person-group><article-title>Data from head-neck-radiomics-HN1</article-title><source>Cancer Imaging Arch.</source><year>2019</year><pub-id pub-id-type="doi">10.7937/tcia.2019.8kap372n</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kwan</surname><given-names>JYY</given-names></name><etal/></person-group><article-title>Data from radiomic biomarkers to refine risk models for distant metastasis in oropharyngeal carcinoma</article-title><source>Cancer Imaging Arch.</source><year>2019</year><pub-id pub-id-type="doi">10.7937/tcia.2019.8dho2gls</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Harrell</surname><given-names>FEJ</given-names></name><name><surname>Lee</surname><given-names>KL</given-names></name><name><surname>Mark</surname><given-names>D</given-names></name></person-group><article-title>Multivariable prognostic models: Issues in developing models, evaluating assumptions and adequacy, and measuring and reducing errors</article-title><source>Stat. Med.</source><year>1996</year><volume>15</volume><issue>4</issue><fpage>361</fpage><lpage>387</lpage><pub-id pub-id-type="doi">10.1002/(SICI)1097-0258(19960229)15:4&#x0003c;361::AID-SIM168&#x0003e;3.0.CO;2-4</pub-id><?supplied-pmid 8668867?><pub-id pub-id-type="pmid">8668867</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Starke</surname><given-names>S</given-names></name><etal/></person-group><article-title>2D and 3D convolutional neural networks for outcome modelling of locally advanced head and neck squamous cell carcinoma</article-title><source>Sci. Rep.</source><year>2020</year><volume>10</volume><fpage>15625</fpage><pub-id pub-id-type="doi">10.1038/s41598-020-70542-9</pub-id><?supplied-pmid 32973220?><pub-id pub-id-type="pmid">32973220</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><mixed-citation publication-type="other">He, K., Zhang, X., Ren, S. &#x00026; Sun, J. Deep residual learning for image recognition. In <italic>2016 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)</italic> 770&#x02013;778. 10.1109/CVPR.2016.90 (2016).</mixed-citation></ref><ref id="CR35"><label>35.</label><mixed-citation publication-type="other">Huang, G., Liu, Z., Van Der Maaten, L. &#x00026; Weinberger, K.&#x000a0;Q. Densely connected convolutional networks. <italic>2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)</italic> 2261&#x02013;2269. 10.1109/CVPR.2017.243 (2017).</mixed-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Leijenaar</surname><given-names>RTH</given-names></name><etal/></person-group><article-title>External validation of a prognostic CT-based radiomic signature in oropharyngeal squamous cell carcinoma</article-title><source>Acta Oncol.</source><year>2015</year><volume>54</volume><issue>9</issue><fpage>1423</fpage><lpage>1429</lpage><pub-id pub-id-type="doi">10.3109/0284186X.2015.1061214</pub-id><?supplied-pmid 26264429?><pub-id pub-id-type="pmid">26264429</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><mixed-citation publication-type="other">Chollet, F. keras. <ext-link ext-link-type="uri" xlink:href="https://github.com/fchollet/keras">https://github.com/fchollet/keras</ext-link> (2015).</mixed-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>Y</given-names></name><etal/></person-group><article-title>CNN-based survival model for pancreatic ductal adenocarcinoma in medical imaging</article-title><source>BMC Med. Imaging</source><year>2020</year><volume>20</volume><fpage>11</fpage><pub-id pub-id-type="doi">10.1186/s12880-020-0418-1</pub-id><?supplied-pmid 32013871?><pub-id pub-id-type="pmid">32013871</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kim</surname><given-names>H</given-names></name><name><surname>Goo</surname><given-names>JM</given-names></name><name><surname>Lee</surname><given-names>KH</given-names></name><name><surname>Kim</surname><given-names>YT</given-names></name><name><surname>Park</surname><given-names>CM</given-names></name></person-group><article-title>Preoperative CT-based deep learning model for predicting disease-free survival in patients with lung adenocarcinomas</article-title><source>Radiology</source><year>2020</year><volume>00</volume><fpage>1</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1148/radiol.**********</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><mixed-citation publication-type="other">Abadi, M. <italic>et&#x000a0;al.</italic> Tensorflow: Large-scale machine learning on heterogeneous distributed systems. In <italic>12th USENIX Symposium on Operating Systems Design and Implementation</italic> (2016).</mixed-citation></ref><ref id="CR41"><label>41.</label><mixed-citation publication-type="other">Kingma, D. &#x00026; Ba, J. Adam: A method for stochastic optimization. In <italic>International Conference on Learning Representations</italic> (2014).</mixed-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Herman</surname><given-names>GT</given-names></name><name><surname>Zheng</surname><given-names>J</given-names></name><name><surname>Bucholtz</surname><given-names>CA</given-names></name></person-group><article-title>Shape-based interpolation</article-title><source>IEEE Comput. Graph. Appl.</source><year>1992</year><volume>12</volume><fpage>69</fpage><lpage>79</lpage><pub-id pub-id-type="doi">10.1109/38.135915</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><mixed-citation publication-type="other">Isensee, F. <italic>et&#x000a0;al.</italic> batchgenerators&#x02014;a python framework for data augmentation. 10.5281/zenodo.3632567.</mixed-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bradley</surname><given-names>AP</given-names></name></person-group><article-title>The use of the area under the ROC curve in the evaluation of machine learning algorithms</article-title><source>Pattern Recogn.</source><year>1997</year><volume>30</volume><issue>7</issue><fpage>175</fpage><lpage>177</lpage><pub-id pub-id-type="doi">10.1016/S0031-3203(96)00142-2</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Claridge-Chang</surname><given-names>A</given-names></name><name><surname>Assam</surname><given-names>PN</given-names></name></person-group><article-title>Estimation statistics should replace significance testing</article-title><source>Nat. Methods</source><year>2016</year><volume>13</volume><issue>2</issue><fpage>108</fpage><lpage>109</lpage><pub-id pub-id-type="doi">10.1038/nmeth.3729</pub-id><?supplied-pmid 26820542?><pub-id pub-id-type="pmid">26820542</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Du-Prel</surname><given-names>J</given-names></name><name><surname>Hommel</surname><given-names>G</given-names></name><name><surname>Roehrig</surname><given-names>B</given-names></name><name><surname>Blettner</surname><given-names>M</given-names></name></person-group><article-title>Confidence interval or p value? Part 4 of a series on evaluation of scientific publications</article-title><source>Dtsch. Arztebl. Int.</source><year>2009</year><volume>106</volume><issue>19</issue><fpage>335</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.3238/arztebl.2009.0335</pub-id><?supplied-pmid 19547734?><pub-id pub-id-type="pmid">19547734</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Austin</surname><given-names>PC</given-names></name><name><surname>Hux</surname><given-names>JE</given-names></name></person-group><article-title>A brief note on overlapping confidence intervals</article-title><source>J. Vasc. Surg.</source><year>2002</year><volume>36</volume><fpage>194</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1067/mva.2002.125015</pub-id><?supplied-pmid 12096281?><pub-id pub-id-type="pmid">12096281</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Goldstein</surname><given-names>H</given-names></name><name><surname>Healy</surname><given-names>MJR</given-names></name></person-group><article-title>The graphical presentation of a collection of means</article-title><source>J. R. Stat. Soc. Ser. A</source><year>1995</year><volume>158</volume><issue>1</issue><fpage>175</fpage><lpage>177</lpage><pub-id pub-id-type="doi">10.2307/2983411</pub-id></element-citation></ref></ref-list></back></article>