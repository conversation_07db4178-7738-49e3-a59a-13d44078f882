<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.3" xml:lang="en" article-type="research-article"><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.1d1 20130915//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-journalpublishing1.dtd?><?SourceDTD.Version 39.96?><?ConverterInfo.XSLTName jats2jats3.xsl?><?ConverterInfo.Version 1?><?properties open_access?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">Hum Factors</journal-id><journal-id journal-id-type="iso-abbrev">Hum Factors</journal-id><journal-id journal-id-type="hwp">sphfs</journal-id><journal-id journal-id-type="publisher-id">HFS</journal-id><journal-title-group><journal-title>Human Factors</journal-title></journal-title-group><issn pub-type="ppub">0018-7208</issn><issn pub-type="epub">1547-8181</issn><publisher><publisher-name>SAGE Publications</publisher-name><publisher-loc>Sage CA: Los Angeles, CA</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">9421345</article-id><article-id pub-id-type="pmid">33242999</article-id><article-id pub-id-type="publisher-id">10.1177_0018720820970751</article-id><article-id pub-id-type="doi">10.1177/0018720820970751</article-id><article-categories><subj-group subj-group-type="heading"><subject>Simulation and Virtual Reality</subject></subj-group></article-categories><title-group><article-title>External Human&#x02013;Machine Interfaces Can Be Misleading: An Examination of Trust Development and Misuse in a CAVE-Based Pedestrian Simulation Environment</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Kaleefathullah</surname><given-names>Anees Ahamed</given-names></name><xref rid="aff1-0018720820970751" ref-type="aff">1</xref><xref rid="aff2-0018720820970751" ref-type="aff">2</xref></contrib><contrib contrib-type="author"><name><surname>Merat</surname><given-names>Natasha</given-names></name><xref rid="aff2-0018720820970751" ref-type="aff">2</xref></contrib><contrib contrib-type="author"><name><surname>Lee</surname><given-names>Yee Mun</given-names></name><xref rid="aff2-0018720820970751" ref-type="aff">2</xref></contrib><contrib contrib-type="author"><name><surname>Eisma</surname><given-names>Yke Bauke</given-names></name><xref rid="aff1-0018720820970751" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><name><surname>Madigan</surname><given-names>Ruth</given-names></name><xref rid="aff2-0018720820970751" ref-type="aff">2</xref></contrib><contrib contrib-type="author"><name><surname>Garcia</surname><given-names>Jorge</given-names></name><xref rid="aff2-0018720820970751" ref-type="aff">2</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">https://orcid.org/0000-0002-1281-8200</contrib-id><name><surname>de Winter</surname><given-names>Joost</given-names></name><xref rid="aff1-0018720820970751" ref-type="aff">1</xref><xref rid="corresp1-0018720820970751" ref-type="corresp"/></contrib></contrib-group><aff id="aff1-0018720820970751"><label>1</label><institution-wrap><institution-id institution-id-type="Ringgold">2860</institution-id></institution-wrap> Delft University of Technology, The Netherlands</aff><aff id="aff2-0018720820970751"><label>2</label><institution-wrap><institution-id institution-id-type="Ringgold">414566</institution-id><institution-id institution-id-type="Ringgold">4468</institution-id></institution-wrap> University of Leeds, United Kingdom</aff><author-notes><corresp id="corresp1-0018720820970751">Joost de Winter, Cognitive Robotics Department, Delft University of Technology, 2628 CD Delft, The Netherlands; <email xlink:href="mailto:<EMAIL>"><EMAIL></email></corresp></author-notes><pub-date pub-type="epub"><day>26</day><month>11</month><year>2020</year></pub-date><pub-date pub-type="ppub"><month>9</month><year>2022</year></pub-date><volume>64</volume><issue>6</issue><fpage>1070</fpage><lpage>1085</lpage><history><date date-type="accepted"><day>29</day><month>9</month><year>2020</year></date><date date-type="received"><day>25</day><month>4</month><year>2020</year></date></history><permissions><copyright-statement>Copyright &#x000a9; 2020, The Author(s)</copyright-statement><copyright-year>2020</copyright-year><copyright-holder content-type="sage">Human Factors and Ergonomics Society</copyright-holder><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This article is distributed under the terms of the Creative Commons Attribution 4.0 License (<ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">https://creativecommons.org/licenses/by/4.0/</ext-link>) which permits any use, reproduction and distribution of the work without further permission provided the original work is attributed as specified on the SAGE and Open Access pages (<ext-link ext-link-type="uri" xlink:href="https://us.sagepub.com/en-us/nam/open-access-at-sage">https://us.sagepub.com/en-us/nam/open-access-at-sage</ext-link>).</license-p></license></permissions><abstract><sec><title>Objective</title><p>To investigate pedestrians&#x02019; misuse of an automated vehicle (AV) equipped with an external human&#x02013;machine interface (eHMI). Misuse occurs when a pedestrian enters the road because of uncritically following the eHMI&#x02019;s message.</p></sec><sec><title>Background</title><p>Human factors research indicates that automation misuse is a concern. However, there is no consensus regarding misuse of eHMIs.</p></sec><sec><title>Methods</title><p>Sixty participants each experienced 50 crossing trials in a Cave Automatic Virtual Environment (CAVE) simulator. The three independent variables were as follows: (1) behavior of the approaching AV (within-subject: yielding at 33 or 43 m distance, no yielding), (2) eHMI presence (within-subject: eHMI on upon yielding, off), and (3) eHMI onset timing (between-subjects: eHMI turned on 1 s before or 1 s after the vehicle started to decelerate). Two failure trials were included where the eHMI turned on, yet the AV did not yield. Dependent measures were the moment of entering the road and perceived risk, comprehension, and trust.</p></sec><sec><title>Results</title><p>Trust was higher with eHMI than without, and the &#x02212;1 Group crossed earlier than the +1 Group. In the failure trials, perceived risk increased to high levels, whereas trust and comprehension decreased. Thirty-five percent of the participants in the &#x02212;1 and +1 Groups walked onto the road when the eHMI failed for the first time, but there were no significant differences between the two groups.</p></sec><sec><title>Conclusion</title><p>eHMIs that provide anticipatory information stimulate early crossing. eHMIs may cause people to over-rely on the eHMI and under-rely on the vehicle-intrinsic cues.</p></sec><sec><title>Application</title><p>eHMI have adverse consequences, and education of eHMI capability is required.</p></sec></abstract><kwd-group><kwd>automated driving</kwd><kwd>pedestrians</kwd><kwd>external human&#x02013;machine interfaces</kwd><kwd>trust</kwd><kwd>misuse</kwd><kwd>risk perception</kwd></kwd-group><funding-group specific-use="FundRef"><award-group id="award1-0018720820970751"><funding-source id="funding1-0018720820970751">
<institution-wrap><institution>Horizon 2020 Framework Programme</institution><institution-id institution-id-type="FundRef">https://doi.org/10.13039/100010661</institution-id></institution-wrap>
</funding-source><award-id rid="funding1-0018720820970751">Grant agreement No. 723395</award-id></award-group><award-group id="award2-0018720820970751"><funding-source id="funding2-0018720820970751">
<institution-wrap><institution>Stichting voor de Technische Wetenschappen</institution><institution-id institution-id-type="FundRef">https://doi.org/10.13039/501100003958</institution-id></institution-wrap>
</funding-source><award-id rid="funding2-0018720820970751">016.Vidi.178.047</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>typesetter</meta-name><meta-value>ts10</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec sec-type="intro" id="sec1-0018720820970751"><title>Introduction</title><p>Pedestrian deaths constitute 16% of all traffic fatalities, and the vast majority of these crashes are due to human error (<xref rid="bibr27-0018720820970751" ref-type="bibr">National Highway Traffic Safety Administration [NHTSA], 2019</xref>). Automated vehicles (AVs) have the potential to improve road safety by excluding the human driver from the control loop (<xref rid="bibr13-0018720820970751" ref-type="bibr">Fagnant &#x00026; Kockelman, 2015</xref>). In current traffic, when pedestrians decide to cross the road in front of an approaching vehicle, they rely on both implicit and explicit cues. Implicit cues are regular vehicle behaviors, such as speed and deceleration. Explicit cues, on the other hand, are not part of vehicle behavior, and include amongst others, eye contact, posture, and hand gestures of the driver (<xref rid="bibr33-0018720820970751" ref-type="bibr">Sucha et al., 2017</xref>). One of the challenges in future AVs will be that the driver may be inattentive or even absent, which implies that explicit communication will become cumbersome or impossible.</p><p>External human&#x02013;machine interfaces (eHMIs) may be a viable substitute for explicit communication in current traffic. More specifically, an eHMI could indicate the yielding intention of an AV through lights, symbols, text messages, or even sound (<xref rid="bibr1-0018720820970751" ref-type="bibr">Ackermann et al., 2019</xref>; <xref rid="bibr2-0018720820970751" ref-type="bibr">Bazilinskyy et al., 2019</xref>; <xref rid="bibr3-0018720820970751" ref-type="bibr">Bengler et al., 2020</xref>; <xref rid="bibr5-0018720820970751" ref-type="bibr">Burns et al., 2019</xref>; <xref rid="bibr14-0018720820970751" ref-type="bibr">Habibovic et al., 2018</xref>; <xref rid="bibr19-0018720820970751" ref-type="bibr">Lagstr&#x000f6;m &#x00026; Lundgren, 2015</xref>; <xref rid="bibr31-0018720820970751" ref-type="bibr">Schieben et al., 2019</xref>). Although eHMIs have been found to affect pedestrians&#x02019; road-crossing decisions (e.g., <xref rid="bibr18-0018720820970751" ref-type="bibr">Kooijman et al., 2019</xref>), other studies suggest that pedestrians rely predominantly on implicit cues, such as the AV&#x02019;s approach speed and closing distance. eHMIs may, therefore, only provide a secondary source of information for pedestrians, which could be employed in ambiguous situations, for example (<xref rid="bibr7-0018720820970751" ref-type="bibr">Clamann et al., 2017</xref>; <xref rid="bibr9-0018720820970751" ref-type="bibr">Dey &#x00026; Terken, 2017</xref>; <xref rid="bibr25-0018720820970751" ref-type="bibr">Moore et al., 2019</xref>; <xref rid="bibr29-0018720820970751" ref-type="bibr">Rodr&#x000ed;guez Palmeiro et al., 2018</xref>; <xref rid="bibr30-0018720820970751" ref-type="bibr">Rothenbucher et al., 2016</xref>). The extent to which pedestrians benefit from explicit communication in the form of eHMIs versus implicit communication in the form of the vehicle movement itself (e.g., <xref rid="bibr25-0018720820970751" ref-type="bibr">Moore et al., 2019</xref>) is not yet well understood and the subject of some debate.</p><p>An evaluation of various eHMIs by <xref rid="bibr22-0018720820970751" ref-type="bibr">L&#x000f6;cken et al. (2019)</xref> has shown that eHMIs that were deemed clear, supportive, and easy to use (i.e., high pragmatic quality) yielded the highest levels of self-reported trust. A possible concern after prolonged exposure to reliable AVs and clear eHMIs is that pedestrians may start to trust and rely strongly on the eHMI, while ignoring implicit cues from the AV, that is, its actual yielding behavior. This, in turn, may cause misuse, defined by <xref rid="bibr28-0018720820970751" ref-type="bibr">Parasuraman and Riley (1997)</xref> as relying uncritically on automation without recognizing its limitations or failing to monitor the automation&#x02019;s behavior. In the context of eHMIs, we define misuse as a situation where the pedestrian walks onto the road without critically evaluating the eHMI&#x02019;s instructions. <xref rid="bibr12-0018720820970751" ref-type="bibr">Faas et al. (2020</xref>, p. 181) studied an eHMI that showed the status of automation and found that, after the experiment, some pedestrians expressed concerns about automation misuse: &#x0201c;informing about the vehicle&#x02019;s automated driving mode might lead to overtrust in the vehicle&#x02019;s capabilities among pedestrians, who might then be less attentive when encountering such a vehicle.&#x0201d; These concerns of misuse can be related to other human factors phenomena, such as errors of commission, defined as doing what an automated aid tells one to do, even when other available data suggest that the automation aid is not recommending a proper course of action (<xref rid="bibr32-0018720820970751" ref-type="bibr">Skitka et al., 1999</xref>), or compliance, defined as the tendency to perform an action cued by an automation alert (<xref rid="bibr10-0018720820970751" ref-type="bibr">Dixon et al., 2007</xref>; <xref rid="bibr23-0018720820970751" ref-type="bibr">Meyer et al., 2014</xref>).</p><p>A seminal study on trust development in general was performed by <xref rid="bibr20-0018720820970751" ref-type="bibr">Lee and Moray (1992)</xref>. They investigated changes in operators&#x02019; task performance and self-reported trust when interacting with a semi-automatic pasteurization plant. Results indicated that the participants&#x02019; trust and performance increased as they became familiar with the system. However, after experiencing a fault, trust declined considerably. More recently, <xref rid="bibr16-0018720820970751" ref-type="bibr">Holl&#x000e4;nder et al. (2019)</xref> investigated the effects of an eHMI conveying incorrect information; that is, the AV stopped, but a &#x0201c;halt&#x0201d; symbol was presented, or the AV maintained speed while a green walking symbol was presented. Results showed that the incorrect information caused a sharp decline in perceived trust and safety. Perceived trust and safety recovered directly afterwards, indicating that the incorrect information had no clear lasting effect in subsequent trials with a properly functioning eHMI.</p><p>Based on the above, it appears essential to assess the development of trust and possible misuse during repeated exposure to eHMIs. One factor that has not been studied so far concerns the interplay between explicit cues (i.e., the eHMI signal) and implicit cues (i.e., AV speed and distance). If pedestrians are relying predominantly on implicit cues, then eHMI misuse should be unlikely. Conversely, if participants rely strongly on the information provided by the eHMI while ignoring implicit cues, then there is a risk of misuse. More specifically, the risk is that the pedestrian crosses if the eHMI signals so, even when the AV does not yield for the pedestrian.</p><p>One critical variable in the examination of reliance on explicit communication versus implicit communication is the eHMI onset timing. <xref rid="bibr8-0018720820970751" ref-type="bibr">de Clercq et al. (2019)</xref> showed that participants felt more inclined to cross when the eHMI (e.g., a text &#x0201c;WALK&#x0201d; or a front brake light) switched on before the AV started braking, compared with a condition without the eHMI. Although early onset timing stimulates pedestrians to cross early, it could also cause misuse if pedestrians do not line up the information provided by the eHMI with the AVs implicit cues (i.e., is the AV indeed slowing down?).</p><p>This study aimed to examine pedestrians&#x02019; trust development and potential automation misuse during repeated encounters with an AV equipped with an eHMI, with a specific focus on the effects of eHMI onset timing. It was hypothesized that participants would trust a vehicle with an eHMI more than a vehicle without an eHMI, but would lose that trust if the system failed (i.e., eHMI turned on, but the AV did not yield). Yet participants were expected to regain trust quickly (see <xref rid="bibr16-0018720820970751" ref-type="bibr">Holl&#x000e4;nder et al., 2019</xref>). In real traffic, pedestrians could experience this type of &#x0201c;failure&#x0201d; if an AV stops for another pedestrian or object further down the road. We further expected that pedestrians would cross earlier when the eHMI provided information &#x0201c;early,&#x0201d; that is, when the eHMI onset occurred before the AV started braking. We compared this to an eHMI that provided &#x0201c;late&#x0201d; information, which means that the eHMI turned on after the AV started braking. Finally, we expected that pedestrians who have been repeatedly exposed to an early-onset eHMI would be more likely to initiate crossing when the eHMI failed, as compared with pedestrians who have repeatedly encountered a late-onset eHMI.</p><p>Previous research found that for an AV that aims to communicate &#x0201c;I am giving way,&#x0201d; a pulsing light band and conventional flashing headlights were more preferred than a pulsing lamp or a light band that filled from front to back (<xref rid="bibr21-0018720820970751" ref-type="bibr">Lee et al., 2019</xref>). It has further been recommended that, for better visibility, eHMIs need to be positioned on the front and the sides of the car instead of only on the front (<xref rid="bibr11-0018720820970751" ref-type="bibr">Eisma et al., 2020</xref>). Our study featured an AV with a 360&#x000b0; pulsing light band located on the front and sides of the vehicle as well as on the grill. This positioning should enable the pedestrian to be aware of the light band from any direction. The experiment was conducted in a Cave Automatic Virtual Environment (CAVE) simulator at the Institute for Transport Studies, University of Leeds. A realistic simulation environment was regarded as important for accurate risk perception and the assessment of potential misuse.</p></sec><sec sec-type="methods" id="sec2-0018720820970751"><title>Methods</title><sec id="sec3-0018720820970751"><title>Participants</title><p>Sixty participants (30 males and 30 females) aged between 18 and 35 years (<italic toggle="yes">M</italic> = 24.4, <italic toggle="yes">SD</italic> = 4.0) took part in the study. All participants were recruited via posters at the University of Leeds student union, through acquaintances, or via posts on social media. The 60 participants comprised of 15 different nationalities, mostly British (28), Chinese (6), Malaysian (5), Lithuanian (3), French (3), Polish (3), and Spanish (3). The participants were either students or university employees.</p><p>Twenty-nine participants were used to left-hand traffic, 15 participants were used to right-hand traffic, and the remaining 16 participants were used to both left- and right-hand traffic. Twenty-one participants wore their glasses during the experiment. Thirty-seven participants had experience with head-mounted virtual reality.</p><p>This research complied with the American Psychological Association Code of Ethics and was approved by the University of Leeds Research Ethics Committee (Ref: LTTRAN-097). All participants provided written informed consent.</p></sec><sec id="sec4-0018720820970751"><title>Pedestrian Simulator</title><p>The study was conducted in the Highly Immersive Kinematic Experimental Research (HIKER) pedestrian simulator (<xref rid="fig1-0018720820970751" ref-type="fig">Figure 1</xref>) at the University of Leeds, which is a 9-m long by 4-m wide CAVE-type simulator. Participants wore stereoscopic motion-tracking glasses and could move around freely. The virtual environment featured a single-lane, 4.19-m-wide road in a city environment during daytime (<xref rid="fig1-0018720820970751" ref-type="fig">Figure 1</xref>). A fence was placed on the other side of the road, to prevent the participant from crossing beyond that point. The simulator was programmed to alert the participant with a warning sound when they were too close to the wall beyond the fence.</p><fig position="float" fig-type="figure" id="fig1-0018720820970751"><label>Figure 1</label><caption><p>Participant&#x02019;s view of the pedestrian simulator.</p></caption><graphic xlink:href="10.1177_0018720820970751-fig1" position="float"/></fig><p>The experiment ran on a computer rack of eight machines, each one of them containing an Intel<sup>&#x000ae;</sup> Core&#x02122; i9-7900X CPU @ 3.30 GHz processor, 128 GB RAM, and 8 GB Nvidia Quadro P4000. The scenario was generated in Unity 2017.4.17 with a Middle VR ******* licensed plugin and ran in stereo mode at a resolution of 2560 &#x000d7; 1600 pixels per projector. Ten IR motion trackers connected to the software Vicon Tracker 3.7 tracked the position and head angle of the participant.</p></sec><sec id="sec5-0018720820970751"><title>Experimental Design</title><p>The experiment had three independent variables: eHMI onset, yielding behavior of the AV, and eHMI presence.</p><sec id="sec6-0018720820970751"><title>eHMI onset</title><p>eHMI onset was a between-subjects variable with two levels: &#x02212;1 s and +1 s. Participants were alternately assigned to the Group + 1 and Group &#x02212;1. For the +1 Group, the eHMI turned on 1 s after the vehicle started to decelerate. For the &#x02212;1 Group, the eHMI turned on 1 s before the vehicle started to decelerate. The eHMI onset timing offsets were based on <xref rid="bibr8-0018720820970751" ref-type="bibr">de Clercq et al. (2019)</xref>, who found that a &#x02212;1 s timing yielded substantial benefits to pedestrians, in the sense that pedestrians were willing to cross the road earlier with the eHMI compared with without the eHMI. The +1 timing offset also had benefits, although smaller as compared with the &#x02212;1 s timing.</p></sec><sec id="sec7-0018720820970751"><title>Yielding behavior</title><p>Yielding behavior was a within-subject variable with three levels: (1) yielding while starting to decelerate at a 33 m distance, (2) yielding while starting to decelerate at a 43 m, and (3) no yielding, as illustrated in <xref rid="fig2-0018720820970751" ref-type="fig">Figure 2</xref>. The interactions with the eHMI occurred during yielding trials only. The nonyielding trials were included to make participants aware that they could not cross the road in all trials but would be required to look at the AV before crossing. During nonyielding trials, the AV maintained a speed of 30 mph (~48 kph) without stopping. For the yielding trials, the AV decelerated at 2.24 m/s<sup>2</sup> and 2.99 m/s<sup>2</sup> for stopping distances of 43 m and 33 m, respectively. We used two stopping distances instead of one, to introduce variability and prevent participants from recognizing that the AV always behaved in the same way. During yielding trials, the vehicle came to a stop at a distance of 3 m from the participant and waited until the pedestrian had crossed; the car then drove off again. If the participants crossed before the vehicle came to a complete stop, the car accelerated again without stopping. <xref rid="fig3-0018720820970751" ref-type="fig">Figure 3</xref> shows the speed versus distance relationships for the two stopping distances together with markers that indicate the eHMI onsets.</p><fig position="float" fig-type="figure" id="fig2-0018720820970751"><label>Figure 2</label><caption><p>The lines display the distances where the vehicle started to decelerate (33 m and 43 m) and the complete stop at 3 m from the participant. The white circle indicates the participant&#x02019;s initial position. The white lines and circles were not visible during the experiment.</p></caption><graphic xlink:href="10.1177_0018720820970751-fig2" position="float"/></fig><fig position="float" fig-type="figure" id="fig3-0018720820970751"><label>Figure 3</label><caption><p>Vehicle speed as a function of the distance between the automated vehicle and the pedestrian. The distance between the pedestrian and the approaching vehicle is defined along the x axis (i.e., parallel to the direction of the road). Note that the automated vehicle drives past the pedestrian when the distance is 0 m. eHMI = external human&#x02013;machine interface.</p></caption><graphic xlink:href="10.1177_0018720820970751-fig3" position="float"/></fig></sec><sec id="sec8-0018720820970751"><title>eHMI presence</title><p>The eHMI presence was a within-subject variable, with two levels: eHMI off and eHMI on. In all trials, the vehicle arrived around the corner in an identical manner with the eHMI turned off. In yielding trials, the eHMI turned on in 75% of the cases (i.e., eHMI on trials) and remained off in 25% of the cases (i.e., eHMI off trials). In nonyielding trials, the eHMI remained off. Accordingly, the experiment mimicked a mixed-traffic situation with some vehicles having no eHMI and some vehicles having an eHMI to indicate when the vehicle is yielding.</p><p>In pilot tests, we noticed that our initial light band design, as proposed by <xref rid="bibr21-0018720820970751" ref-type="bibr">Lee et al. (2019)</xref>, was sometimes not noticed by participants. Because our aim was to evaluate possible misuse of the eHMI, we increased the brightness of the eHMI so that participants would not be likely to overlook it. Accordingly, we designed an eHMI consisting of a thick white light band around the top edges of the car and the front grill (<xref rid="fig4-0018720820970751" ref-type="fig">Figure 4</xref>). The light band was pulsating to attract attention. More specifically, when the light band was on, its intensity varied in a zigzag-like manner between 30% and 100%, with an intensity peak-to-peak interval of 0.80 s. That is, the intensity was 100% at the onset of the eHMI, intensity decreased linearly to 30% in 0.28 s, stayed at a constant level of 30% for 0.24 s, and increased linearly to 100% in 0.28 s again. The light band remained on until the moment the participant finished crossing.</p><fig position="float" fig-type="figure" id="fig4-0018720820970751"><label>Figure 4</label><caption><p>Automated vehicle with the eHMI at 100% intensity. The vehicle had a width of 1.90 m and a length of 4.96 m. eHMI = external human&#x02013;machine interface.</p></caption><graphic xlink:href="10.1177_0018720820970751-fig4" position="float"/></fig><p>Each participant completed 50 trials. In each trial, the participant encountered a blue AV approaching from the right. Once the participant had triggered the start of the trial, the vehicle appeared around the corner, initially out of sight, at a longitudinal and lateral distance from the pedestrian of 73 m and 22 m, respectively, and driving at a speed of 30 mph (48 kph).</p><p>Participants completed four blocks of trials, consisting of 12 trials each. There was one failure trial after Block 3, to examine initial misuse, and another one after Block 4, to evaluate whether misuse persisted (<xref rid="table1-0018720820970751" ref-type="table">Table 1</xref>). In the failure trial, the eHMI turned on, but the AV continued to drive at a constant speed. <xref rid="table2-0018720820970751" ref-type="table">Table 2</xref> shows the number of trials per yielding condition within each block. The order of trials was random within each block, for all blocks, and different for each participant.</p><table-wrap position="float" id="table1-0018720820970751"><label>Table 1</label><caption><p>Experimental Blocks</p></caption><alternatives><graphic xlink:href="10.1177_0018720820970751-table1" specific-use="table1-0018720820970751" position="float"/><table frame="hsides" rules="groups"><thead><tr><th align="left" valign="bottom" rowspan="1" colspan="1">Block 1</th><th align="center" valign="bottom" rowspan="1" colspan="1">Block 2</th><th align="center" valign="bottom" rowspan="1" colspan="1">Block 3</th><th align="center" valign="bottom" rowspan="1" colspan="1">Failure Trial 1</th><th align="center" valign="bottom" rowspan="1" colspan="1">Block 4</th><th align="center" valign="bottom" rowspan="1" colspan="1">Failure Trial 2</th></tr></thead><tbody><tr><td align="left" valign="top" rowspan="1" colspan="1">Trials 1&#x02013;12</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;Trials 13&#x02013;24</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;Trials 25&#x02013;36</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;&#x02003;Trial 37</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;Trial 38&#x02013;49</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;&#x02003;Trial 50</td></tr></tbody></table></alternatives></table-wrap><table-wrap position="float" id="table2-0018720820970751"><label>Table 2</label><caption><p>Number of Trials Per Yielding Condition and eHMI Presence Condition, Per 12-Trial Block</p></caption><alternatives><graphic xlink:href="10.1177_0018720820970751-table2" specific-use="table2-0018720820970751" position="float"/><table frame="hsides" rules="groups"><thead><tr><th align="left" valign="bottom" rowspan="1" colspan="1">Distance Between Pedestrian and Vehicle at the Onset of Deceleration</th><th align="center" valign="bottom" rowspan="1" colspan="1">eHMI Presence</th><th align="center" valign="bottom" rowspan="1" colspan="1">Number of Trials per Block</th></tr></thead><tbody><tr><td align="left" valign="top" rowspan="1" colspan="1">33 m</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;&#x02003;On</td><td align="center" valign="top" rowspan="1" colspan="1">3</td></tr><tr><td align="left" valign="top" rowspan="1" colspan="1">43 m</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;&#x02003;On</td><td align="center" valign="top" rowspan="1" colspan="1">3</td></tr><tr><td align="left" valign="top" rowspan="1" colspan="1">33 m</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;&#x02003;Off</td><td align="center" valign="top" rowspan="1" colspan="1">1</td></tr><tr><td align="left" valign="top" rowspan="1" colspan="1">43 m</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;&#x02003;Off</td><td align="center" valign="top" rowspan="1" colspan="1">1</td></tr><tr><td align="left" valign="top" rowspan="1" colspan="1">No yielding</td><td align="left" valign="top" rowspan="1" colspan="1">&#x02003;&#x02003;Off</td><td align="center" valign="top" rowspan="1" colspan="1">4</td></tr></tbody></table></alternatives><table-wrap-foot><fn id="table-fn2-1-0018720820970751"><p><italic toggle="yes">Note</italic>. eHMI = external human&#x02013;machine interface.</p></fn></table-wrap-foot></table-wrap><p>In the two failure trials, the eHMI switched on at 38 m (average onset distance of the two groups) from the participant, but the vehicle did not yield. The eHMI timing for the failure trial was the same for Groups +1 and -1.</p></sec></sec><sec id="sec9-0018720820970751"><title>Dependent Measures</title><p>The dependent measures included measures assessing perceived risk, comprehension, and trust. More specifically, the following three questions were displayed to participants on the HIKER lab screen, after each trial (<xref rid="fig5-0018720820970751" ref-type="fig">Figure 5</xref>):</p><list list-type="order"><list-item><p>I experienced the situation as risky.</p></list-item><list-item><p>I could comprehend the behavior and appearance of the approaching vehicle.</p></list-item><list-item><p>I trust the behavior and appearance of the automated vehicle.</p></list-item></list><fig position="float" fig-type="figure" id="fig5-0018720820970751"><label>Figure 5</label><caption><p>Post-trial questions displayed on the participant&#x02019;s left. Each question was answered verbally on a 10-point Likert scale from 1 (<italic toggle="yes">Strongly Disagree</italic>) to 10 (<italic toggle="yes">Strongly Agree</italic>).</p></caption><graphic xlink:href="10.1177_0018720820970751-fig5" position="float"/></fig><p>Response to these was verbal and recorded by the experimenter.</p><p>Additionally, we recorded the pedestrian&#x02019;s position along an imaginary axis that runs perpendicular to the road as a function of elapsed time since the start of the trial. Positions of 0 m, 2.05 m, and 6.24 m correspond to the starting point, the nearest edge of the road, and the farthest edge of the road, respectively.</p><p>The following dependent measures were computed:</p><list list-type="bullet"><list-item><p>Moment of entering the road in seconds since the start of the trial (s). This measure was computed only for trials in which the AV was yielding for the participant, because in nonyielding trials, the participants almost never entered the road.</p></list-item><list-item><p>Pedestrian position when the car passed (<italic toggle="yes">m</italic>). For each trial in which the AV maintained speed, we recorded the pedestrian&#x02019;s position at the moment the car passed the pedestrian. From this measure, we computed the percentage of participants who were on the road at the moment the car passed.</p></list-item></list></sec><sec id="sec10-0018720820970751"><title>Procedure</title><p>After the participants entered the HIKER room, the researcher explained that the experiment was aimed at studying the crossing behavior of pedestrians when interacting with AVs. Next, the participants read and signed the informed consent form. The form mentioned that if the eHMI is on, it means that the car is yielding, and if the eHMI is off, the car sometimes yields and sometimes continues driving without yielding. The task instructions were described as follows: &#x0201c;With the glasses on, you will look to the corner on the right and take one or two steps forward. The car will then appear from the right corner. You will then move forward along the pavement and decide to cross, or not, depending on the yielding behavior of the vehicle.&#x0201d;</p><p>Next, participants were asked to complete a demographic questionnaire and a practice session. They were verbally briefed by the researcher in the simulator on how to trigger the onset of the car, to cross the road, and answer the post-trial questions The practice trials consisted of five trials: three trials with a nonyielding vehicle, one trial with the eHMI on with a yielding vehicle (&#x02212;1 s, 33 m), and one trial with the eHMI off, with a yielding vehicle (33 m).</p><p>Participants initially stood on the edge of the pavement in the simulator. The approach of the AV was automatically triggered based on the participant&#x02019;s head angle and position in the simulator. The participant then moved forward and decided whether or not to cross in front of the approaching AV. After crossing, the participants returned to the starting position and verbally answered the three post-trial questions that appeared on their left, as shown in <xref rid="fig5-0018720820970751" ref-type="fig">Figure 5</xref>. Once they provided a response to the questions, they were ready to start with the next trial.</p><p>After the experiment, the participants completed a post-experiment questionnaire. The post-experiment questionnaire consisted of questions regarding the experiment in general and a set of virtual presence questions (<xref rid="bibr34-0018720820970751" ref-type="bibr">Witmer &#x00026; Singer, 1998</xref>). They were then reimbursed with &#x000a3;10. The experiment lasted approximately 1 hr per participant.</p></sec><sec id="sec11-0018720820970751"><title>Participant Exclusion and Statistical Analyses</title><p>All data were post-processed in MATLAB R2019b. Tables were constructed containing the participants&#x02019; means and standard deviations of the scores of the dependent measures, separated per block, eHMI presence, and yielding behavior.</p><p>Comparisons for four dependent measures (perceived risk, comprehension, trust, and moment of entering the road) were performed for the &#x02212;1 Group versus the +1 Group (averaged across all yielding trials of Blocks 1&#x02013;3), using independent-samples <italic toggle="yes">t</italic>-tests. We used Fisher&#x02019;s exact test for comparing the &#x02212;1 Group with the +1 Group, regarding the number of participants who were on the road when the car passed during Failure trials 1 and 2. Additionally, we compared the eHMI on and eHMI off conditions (averaged across all yielding trials of Blocks 1&#x02013;3), using paired <italic toggle="yes">t</italic>-tests. The scores for Block 3 versus Block 4 were also compared, using paired <italic toggle="yes">t</italic>-tests. For all statistical tests, we used an alpha level of .05. Selecting an alpha value always involves a trade-off between preventing false positives and false negatives (e.g., <xref rid="bibr26-0018720820970751" ref-type="bibr">Mudge et al., 2012</xref>). The reason for choosing .05 instead of a more conservative number is that we wanted to prevent false negatives regarding the effects of eHMIs. We used <italic toggle="yes">t</italic>-tests, as opposed to more complex (multivariate) tests, because the use of a <italic toggle="yes">t</italic>-test is consistent with our hypotheses, which address main effects and not interactions.</p></sec></sec><sec sec-type="results" id="sec12-0018720820970751"><title>Results</title><p>In each trial, the AV approached from a blind curve. This road layout ensured that participants could not see the AV arrive from a distance and was intended to prevent them from crossing the road in front of a nonyielding AV or before a yielding AV started to decelerate. An inspection of the results showed that some participants were on the road or had already crossed the road at the moment the nonyielding AV passed. According to the experimenter&#x02019;s notes and inspection of the raw data, these participants walked quickly, sometimes without slowing down. They often crossed directly in front of the nonyielding AV, and in some cases were run over by it. These behaviors were regarded as unrealistic (in real traffic, pedestrians do not cross that hazardously or walk under cars) and against the intention of the experiment (the experiment was designed so that participants would not cross in front of nonyielding AVs). For these reasons, it was decided to screen out participants. More specifically, participants who were on the road or had already crossed the road at the moment the nonyielding AV passed in more than 6 out of 18 nonyielding trials (i.e., four trials per block plus two failure trials) were excluded from all analyses. Accordingly, five participants from the +1 Group, and two participants from the &#x02212;1 Group were excluded. They had walked onto the road 17, 10, 10, 14, and 15 out of 18 times (+1 Group), and 7 and 10 out of 18 times (&#x02212;1 Group). The +1 Group consisted of 25 participants (12 males and 13 females), aged between 19 and 34 years (<italic toggle="yes">M</italic> = 24.9, <italic toggle="yes">SD</italic> = 3.8). The &#x02212;1 Group consisted of 28 participants (13 males and 15 females), aged between 19 and 35 years (<italic toggle="yes">M</italic> = 24.0, <italic toggle="yes">SD</italic> = 4.2). For completeness, the <ext-link xlink:href="https://journals.sagepub.com/doi/suppl/10.1177/0018720820970751/suppl_file/sj-pdf-1-hfs-10.1177_0018720820970751.pdf" ext-link-type="uri">Supplemental materials</ext-link> contain the results of all statistical tests for the full sample (30 participants in the +1 Group and 30 participants in the &#x02212;1 Group). The results are generally the same, except for the higher standard deviations of the road entering times for the full sample as compared with the reduced sample (see <ext-link xlink:href="https://journals.sagepub.com/doi/suppl/10.1177/0018720820970751/suppl_file/sj-pdf-1-hfs-10.1177_0018720820970751.pdf" ext-link-type="uri">Supplemental materials</ext-link>).</p><sec id="sec13-0018720820970751"><title>Comparisons Between eHMI on (&#x02212;1 Group &#x00026; +1 Group) and eHMI Off</title><p><xref rid="fig6-0018720820970751" ref-type="fig">Figure 6</xref> shows the mean scores of the dependent measures, separated per block (1&#x02013;4), eHMI presence (on, off), and yielding behavior (33 m, 44 m, or no yielding). For yielding AVs, perceived risk was lower, and perceived comprehension and trust higher, when the eHMI was on compared with off. The differences in risk, comprehension, and trust between the eHMI on and off conditions (averaged across all yielding trials of Blocks 1&#x02013;3) were significant for the &#x02212;1 Group (<italic toggle="yes">t</italic>(27) = &#x02212;6.44, <italic toggle="yes">p</italic> &#x0003c; .001, <italic toggle="yes">t</italic>(27) = 7.11, <italic toggle="yes">p</italic> &#x0003c; .001, <italic toggle="yes">t</italic>(27) = 5.80, <italic toggle="yes">p</italic> &#x0003c; .001, respectively) and for the +1 Group (<italic toggle="yes">t</italic>(24) = &#x02212;2.81, <italic toggle="yes">p</italic> = .010, <italic toggle="yes">t</italic>(24) = 4.83, <italic toggle="yes">p</italic> &#x0003c; .001, <italic toggle="yes">t</italic>(24) = 3.68, <italic toggle="yes">p</italic> = .001, respectively). Furthermore, <xref rid="fig6-0018720820970751" ref-type="fig">Figure 6</xref> shows that the perceived risk was generally lower, and the comprehension and trust higher, in the &#x02212;1 Group as compared with the +1 Group. The results of independent-samples <italic toggle="yes">t</italic>-tests for the three respective measures (risk, comprehension, and trust) between the +1 Group and &#x02212;1 Group were as follows: <italic toggle="yes">t</italic>(51) = 1.80, <italic toggle="yes">p</italic> = .078, <italic toggle="yes">t</italic>(51) = &#x02212;2.47, <italic toggle="yes">p</italic> = .017, <italic toggle="yes">t</italic>(51) = &#x02212;1.69, <italic toggle="yes">p</italic> = .098 (averaged across all eHMI on trials of Blocks 1&#x02013;3).</p><fig position="float" fig-type="figure" id="fig6-0018720820970751"><label>Figure 6</label><caption><p>Means of the participants&#x02019; results per dependent measure and experimental condition. The cells for Risk, Comprehension, and Trust are filled linearly according to the depicted mean value on a scale from 1 (no orange in the cell) to 10 (entire cell orange). The cells for the road entering time are filled linearly according to the depicted mean value on a scale from 5 s to 8 s. Standard deviations, reflecting the magnitude of individual differences, are available in the Supplemental materials. NY = no yielding.</p></caption><graphic xlink:href="10.1177_0018720820970751-fig6" position="float"/></fig><p><xref rid="fig6-0018720820970751" ref-type="fig">Figure 6</xref> further shows that pedestrians entered the road earlier for the eHMI on as compared with the eHMI off condition. The differences between eHMI on and off conditions (averaged across all yielding trials in Blocks 1&#x02013;3) were significant for both the &#x02212;1 Group (<italic toggle="yes">t</italic>(27) = &#x02212;7.26, <italic toggle="yes">p</italic> &#x0003c; .001) and the +1 Group (<italic toggle="yes">t</italic>(24) = &#x02212;7.46, <italic toggle="yes">p</italic> &#x0003c; .001). Furthermore, according to an independent-samples <italic toggle="yes">t</italic>-test, the road entering time was earlier for the &#x02212;1 Group than for the +1 Group, <italic toggle="yes">t</italic>(51) = 3.07, <italic toggle="yes">p</italic> = .003 (averaged across all eHMI on Trials of Blocks 1&#x02013;3). These effects are illustrated in <xref rid="fig7-0018720820970751" ref-type="fig">Figure 7</xref>, showing the mean pedestrian position as a function of elapsed time for all yielding conditions.</p><fig position="float" fig-type="figure" id="fig7-0018720820970751"><label>Figure 7</label><caption><p>Mean pedestrian position perpendicular to the road as a function of elapsed time per group, yielding condition, and eHMI condition (Blocks 1&#x02013;3 only). Also shown are the moments the eHMI turned on, the moments the vehicle came to a stop, and the moment the vehicle passed in the eHMI off trials. AV = automated vehicle; eHMI = external human&#x02013;machine interface.</p></caption><graphic xlink:href="10.1177_0018720820970751-fig7" position="float"/></fig></sec><sec id="sec14-0018720820970751"><title>Dynamics of Risk, Comprehension, Trust, and Time of Entering the Road (eHMI on Trials)</title><p><xref rid="fig8-0018720820970751" ref-type="fig">Figure 8</xref> illustrates that participants learned the functioning of the eHMI during the first three blocks, as indicated by a slight decrease of perceived risk and a decreased time for entering the road.</p><fig position="float" fig-type="figure" id="fig8-0018720820970751"><label>Figure 8</label><caption><p>Mean values of perceived risk of the situation (left top), comprehension of the behavior and appearance of the vehicle (right top), trust in the behavior and appearance of the vehicle (left bottom), and the moment the pedestrian entered the road (right bottom). The horizontal axis is the trial number, only counting the trials where the eHMI was on. At trial numbers 19 and 26, the eHMI failure occurred. The shaded areas represent the mean &#x000b1; standard deviation, depicted in gray for the +1 Group and in light magenta for the &#x02212;1 Group (overlap shows up as a darker magenta shading). eHMI = external human&#x02013;machine interface; FT 1 = Failure Trial 1; FT 2 = Failure Trial 2.</p></caption><graphic xlink:href="10.1177_0018720820970751-fig8" position="float"/></fig><p><xref rid="fig6-0018720820970751" ref-type="fig">Figures 6</xref> and <xref rid="fig8-0018720820970751" ref-type="fig">8</xref> show that, for both groups, in Failure Trial 1 (post Block 3) and Failure Trial 2 (post Block 4), perceived risk showed a substantial increase, and comprehension and trust a considerable decrease, as compared with the previous trials. The participants&#x02019; mean risk levels for failure trials were as high as 7.79&#x02013;8.88 on a scale of 1 to 10. Trust, on the other hand, was low (between 1.64 and 2.38). Independent-samples <italic toggle="yes">t</italic>-tests of the perceived risk, comprehension, and trust for the failure trials showed no significant differences between the +1 Group and the &#x02212;1 Group for Failure Trial 1 (<italic toggle="yes">t</italic>(51) = .75, <italic toggle="yes">p</italic> = .456, <italic toggle="yes">t</italic>(51) = 1.50, <italic toggle="yes">p</italic> = .139, <italic toggle="yes">t</italic>(51) = 1.13, <italic toggle="yes">p</italic> = .265, respectively) and Failure Trial 2 (<italic toggle="yes">t</italic>(51) = 1.47, <italic toggle="yes">p</italic> = .147, <italic toggle="yes">t</italic>(51) = 1.68, <italic toggle="yes">p</italic> = .099, <italic toggle="yes">t</italic>(51) = .63, <italic toggle="yes">p</italic> = .530, respectively).</p><p><xref rid="fig6-0018720820970751" ref-type="fig">Figures 6</xref> and <xref rid="fig8-0018720820970751" ref-type="fig">8</xref> show that the scores on the dependent measures recovered immediately after the first failure trial. We examined the differences between block 3 (before Failure Trial 1) and Block 4 (after Failure Trial 1). <xref rid="fig6-0018720820970751" ref-type="fig">Figure 6</xref> shows that in block 4, perceived risk was generally higher, and comprehension and trust lower, as compared to Block 3 (averaged over the eHMI On trials). The participants in the &#x02212;1 group entered the road later in block 4 as compared to block 3. according to a paired-samples <italic toggle="yes">t</italic>-test, the effects between Block 3 and 4 were as follows for the &#x02212;1 group: <italic toggle="yes">t</italic>(27) = &#x02212;3.15, <italic toggle="yes">p</italic> = .004, <italic toggle="yes">t</italic>(27) = 3.22, <italic toggle="yes">p</italic> = .003, <italic toggle="yes">t</italic>(27) = 3.85, <italic toggle="yes">p</italic> &#x0003c; .001, and <italic toggle="yes">t</italic>(27) = &#x02212;3.84, <italic toggle="yes">p</italic> &#x0003c; .001, for risk, comprehension, trust, and time of entering the road, respectively). For the +1 Group, the Block 3 vs. Block 4 effects were: <italic toggle="yes">t</italic>(24) = &#x02212;1.26, <italic toggle="yes">p</italic> = .220, <italic toggle="yes">t</italic>(24) = 0.52, <italic toggle="yes">p</italic> = .609, <italic toggle="yes">t</italic>(24) = 2.00, <italic toggle="yes">p</italic> = .057, and <italic toggle="yes">t</italic>(25) = &#x02212;3.20, <italic toggle="yes">p</italic> = .004, respectively). To summarize, as can also be seen from <xref rid="fig8-0018720820970751" ref-type="fig">Figure 8</xref>, participants of the &#x02212;1 group in particular lost trust after experiencing the first failure trial.</p></sec><sec id="sec15-0018720820970751"><title>Walking Onto the Road in the Failure Trial</title><p>From <xref rid="fig9-0018720820970751" ref-type="fig">Figure 9</xref>, it can be seen that, before Failure Trial 1, three participants from the &#x02212;1 Group (nonyielding trial no. 5 and 6) and one participant from the +1 Group (nonyielding trial no. 12) were on the road at the moment the AV passed. Thirty-six percent of the participants were on the road during the first failure trial. In the second failure trial, no participants from the &#x02212;1 Group were on the road. In the +1 Group, 20% of the participants in Failure Trial 2 were standing/walking on the road while the vehicle passed.</p><fig position="float" fig-type="figure" id="fig9-0018720820970751"><label>Figure 9</label><caption><p>Pedestrian location at the moment the front of the car passed. The top of the figure shows the percentage of participants in the +1 Group (25 participants) and &#x02212;1 Group (28 participants) who were on the road at the moment the car passed. The markers are transparent so that overlap can be distinguished. FT 1 = Failure Trial 1; FT 2 = Failure Trial 2.</p></caption><graphic xlink:href="10.1177_0018720820970751-fig9" position="float"/></fig><p>We hypothesized that the +1 Group would yield lower misuse of the eHMI when compared with the &#x02212;1 Group. For Failure Trial 1, nine of 25 participants in the +1 Group and 10 of 28 participants in the &#x02212;1 Group who were on the road (<italic toggle="yes">p</italic> = 1.000 according to a Fisher&#x02019;s exact test). For Failure Trial 2, five of 25 participants in the +1 Group and zero of 28 participants in the &#x02212;1 Group were on the road, a significant effect that was contrary to our hypothesis (<italic toggle="yes">p</italic> = .019 according to a Fisher&#x02019;s exact test).</p><p><xref rid="fig10-0018720820970751" ref-type="fig">Figure 10</xref> provides further insight into the behavior of participants in Failure Trial 1. Almost all participants walked up to the edge of the road and stopped, as can be seen from the horizontally running lines at an elapsed time of 3 s. After the eHMI turned on at 3.15 s, a portion of participants walked onto the road. Some participants noticed that the car was not stopping for them and stepped back, as indicated by the negatively slowing lines from an elapsed time of 5 s onward.</p><fig position="float" fig-type="figure" id="fig10-0018720820970751"><label>Figure 10</label><caption><p>Mean pedestrian position perpendicular to the road as a function of elapsed time in Failure Trial 1. A distinction is made between pedestrians who were on the road when the car passed and pedestrians who were not on the road when the car passed. AV = automated vehicle; eHMI = external human&#x02013;machine interface.</p></caption><graphic xlink:href="10.1177_0018720820970751-fig10" position="float"/></fig></sec></sec><sec sec-type="discussion" id="sec16-0018720820970751"><title>Discussion</title><p><xref rid="bibr24-0018720820970751" ref-type="bibr">Millard-Ball (2018)</xref> argued that AVs will always be risk-averse, which may cause pedestrians to misuse the AVs by crossing the road in a careless manner. In the present study, we focused on misuse caused by AVs that are equipped with a salient eHMI in the form of a light band. We hypothesized that participants would trust the AV with an eHMI more than the AV without an eHMI, but would lose trust if the eHMI failed, yet trust would recover quickly. We further expected that pedestrians would cross earlier when the eHMI onset was early, as compared with late eHMI onset.</p><p>The hypotheses were all confirmed. The results showed that if the eHMI was on, participants in the &#x02212;1 Group entered the road earlier, and reported higher trust, as compared with participants in the +1 Group. In turn, participants entered the road earlier and reported higher trust, higher comprehension, and lower risk, when the eHMI was on compared with when it was off. These results replicate earlier research (<xref rid="bibr8-0018720820970751" ref-type="bibr">de Clercq et al., 2019</xref>; <xref rid="bibr12-0018720820970751" ref-type="bibr">Faas et al., 2020</xref>) that showed that after a short instruction/training, eHMIs induce trust and stimulate pedestrians to cross, especially if the eHMI turns on before the vehicle starts to decelerate. Earlier studies suggest that a light-based eHMI is initially incomprehensible to users, but that people can learn its meaning with only a few trials of practice (<xref rid="bibr8-0018720820970751" ref-type="bibr">de Clercq et al., 2019</xref>; <xref rid="bibr14-0018720820970751" ref-type="bibr">Habibovic et al., 2018</xref>; <xref rid="bibr15-0018720820970751" ref-type="bibr">Hensch et al., 2020</xref>). The high comprehension ratings from the very start of the experiment (<xref rid="fig8-0018720820970751" ref-type="fig">Figure 8</xref>, right top) indicate that the light band eHMI was indeed easily understood.</p><p>The first failure trials had strong effects, in the sense that in as much as 35% of the trials, participants inadvertently entered the road, and the majority of participants expressed feeling a high level of risk. The results for perceived trust are consistent with <xref rid="bibr16-0018720820970751" ref-type="bibr">Holl&#x000e4;nder et al. (2019)</xref> and <xref rid="bibr20-0018720820970751" ref-type="bibr">Lee and Moray (1992)</xref>, who found that when participants experienced a failure of the automated system, their trust declined strongly. As shown in <xref rid="fig9-0018720820970751" ref-type="fig">Figure 9</xref>, some participants walked more than 1 m onto the road when the AV drove by, which meant the AV crashed into them. <xref rid="fig10-0018720820970751" ref-type="fig">Figure 10</xref> illustrates that these participants started walking immediately after the eHMI turned on and realized too late that the AV would not slow down. In other words, roughly one-third of the participants relied on the explicit communication of the eHMI while initially ignoring the vehicle-intrinsic information, whereas the remaining participants appeared to rely more strongly on the AV&#x02019;s implicit communication for deciding whether to step onto the road. The perceived risk, comprehension, trust, and time of entering the road recovered immediately after the failure trial. However, compared with the crossing trials before the failure trial, significant differences remained, especially for the &#x02212;1 Group. In other words, participants of the &#x02212;1 Group had lower trust after the first failure trial, as compared with before it.</p><p>We hypothesized that pedestrians who had previously encountered an eHMI that provided &#x0201c;early&#x0201d; information (&#x02212;1 Group) would be more likely to enter the road during failure trials as compared with those who were exposed to the late eHMI onset timing (+1 Group). However, our results showed that there were no significant differences between the two groups during Failure Trial 1. During Failure Trial 2, participants of the +1 Group were more likely to enter the road as compared with participants from the &#x02212;1 Group, which was contrary to our hypothesis. While this effect needs to be replicated, we speculate that the reason why nobody in the &#x02212;1 Group crashed in Failure Trial 2 is that participants of the &#x02212;1 Group had learned from Failure Trial 1 that they should wait a little to verify from implicit cues that the AV is indeed slowing down to a stop. This speculation is supported by <xref rid="fig8-0018720820970751" ref-type="fig">Figure 8</xref>, showing that participants in the &#x02212;1 Group entered the road later after Failure Trial 1 as compared with before. For the +1 Group, on the other hand, the eHMI during the non-failure-trials turned on relatively late, and participants may have had too little time to (learn to) wait in order to verify the validity of the eHMI signal and benefit from the eHMI at the same time. To summarize, it is possible that, after having experienced a failure trial, the &#x02212;1 Group started to rely more prominently on the implicit communication of the AV, while some participants of the +1 Group continued to rely on the explicit eHMI signal.</p></sec><sec id="sec17-0018720820970751"><title>Strengths and Limitations</title><p>This study was conducted in a CAVE simulator, where participants were able to move freely and could also see their own body in the environment, something that is not possible in most simulators that use head-mounted displays. This is a likely explanation for the high presence ratings regarding &#x0201c;proficiency in moving and interacting with the virtual environment&#x0201d; (see the <ext-link xlink:href="https://journals.sagepub.com/doi/suppl/10.1177/0018720820970751/suppl_file/sj-pdf-1-hfs-10.1177_0018720820970751.pdf" ext-link-type="uri">Supplemental Materials</ext-link>). A limitation of our study is that the simulator did not include sound. Furthermore, some participants verbally commented that the display resolution and quality were not great. This limitation can be explained by the stereo glasses themselves, and the fact that the stereo simulation could be only run at a slightly reduced resolution than the achievable 4K resolution by all eight projectors. Another limitation is that our study involved only one AV in a single lane, and therefore did not examine eHMI misuse in cases where pedestrians have to distribute their visual attention among multiple actors.</p></sec><sec id="sec18-0018720820970751"><title>Conclusions and Recommendations</title><p>The results from this study clearly show that pedestrians are prone to misusing an eHMI after repeated exposure to that eHMI. In other words, there is a risk that pedestrians will &#x0201c;blindly&#x0201d; follow-up the eHMI&#x02019;s message and ignore the implicit communication of the AV. However, contrary to our expectations, the eHMI onset timing did not appear to cause much difference when it comes to the degree of misuse during the first failure trial. This study also showed that when an eHMI signals the intent of the AV before the AV starts to decelerate, the eHMI causes the pedestrians to cross earlier, as compared with an eHMI that turns on after the vehicle starts to decelerate.</p><p>For future research, a more fine-grained understanding is required about whether pedestrians base their crossing decisions on implicit cues from the AV or explicit cues from the eHMI. Such a study could be conducted by using, for example, eye-gaze-contingent methods or psychophysics techniques. Furthermore, we recommend that pedestrians in future traffic should be made aware of the capabilities of the automated driving system, especially if the automation is prone to malfunction. For example, pedestrians could be made aware that an eHMI might turn on when the AV detects a pedestrian but will not yield for that pedestrian (but for another pedestrian instead). The present study showed that this type of failure caused some pedestrians to crash with the AV. It may be expected that the autonomous emergency braking (AEB) systems of future AVs will be able to alleviate the impact, although the extent to which this is possible would depend on how early the AV can detect and predict the pedestrian&#x02019;s crossing intentions (<xref rid="bibr17-0018720820970751" ref-type="bibr">Kooij et al., 2019</xref>). Education could help pedestrians maintain a calibrated amount of trust and could prevent misuse and disuse of the automated driving system. For example, pedestrians may be taught that eHMIs do not necessarily address the pedestrian himself, but could also address other pedestrians or road elements, such as a zebra crossing further down the road (and see <xref rid="bibr4-0018720820970751" ref-type="bibr">Boelhouwer et al., 2020</xref>, for relevant recommendations regarding information-provision for in-vehicle automation systems). Finally, whether the present findings generalize to real traffic environments with a more diverse pool of participants remains unknown. One concern is that in real traffic, where pedestrians are not instructed or trained, they might simply overlook the led strips on the AV (<xref rid="bibr6-0018720820970751" ref-type="bibr">Cefkin et al., 2019</xref>). It is recommended that future studies examine long-term trust development as well as traffic with multiple cars and multiple pedestrians of different age groups.</p></sec><sec id="sec19-0018720820970751"><title>Key Points</title><list list-type="bullet"><list-item><p>This study examined pedestrians&#x02019; trust development and misuse during repeated encounters with an AV equipped with an eHMI that signaled the AV&#x02019;s yielding intention.</p></list-item><list-item><p>Pedestrian crossed the road earlier and exhibited higher perceived trust when the eHMI was on, compared with when it was off.</p></list-item><list-item><p>An eHMI variant that turned on before the vehicle started to decelerate stimulated early crossing.</p></list-item><list-item><p>Misuse was evaluated in failure trials, in which the eHMI turned on but the AV did not yield.</p></list-item><list-item><p>Misuse occurred among 35% of participants in the first failure trial.</p></list-item></list></sec><sec sec-type="supplementary-material" id="sec20-0018720820970751" specific-use="figshare"><title>Supplemental Material</title><supplementary-material id="suppl1-0018720820970751" position="float" content-type="local-data"><caption><title>Supplementary data - Supplemental material for External Human&#x02013;Machine Interfaces Can Be Misleading: An Examination of Trust Development and Misuse in a CAVE-Based Pedestrian Simulation Environment</title></caption><media xlink:href="sj-pdf-1-hfs-10.1177_0018720820970751.pdf"><caption><p>Click here for additional data file.</p></caption></media><p>Supplemental material, Supplementary data, for External Human&#x02013;Machine Interfaces Can Be Misleading: An Examination of Trust Development and Misuse in a CAVE-Based Pedestrian Simulation Environment by Anees Ahamed Kaleefathullah, Natasha Merat, Yee Mun Lee, Yke Bauke Eisma, Ruth Madigan, Jorge Garcia and Joost de Winter in Human Factors: The Journal of Human Factors and Ergonomics Society</p></supplementary-material></sec></body><back><ack><title>Acknowledgment</title><p>This research was supported by project TTW 016.Vidi.178.047 &#x0201c;How should automated vehicles communicate with other road users?&#x0201d; financed by the Netherlands Organization for Scientific Research (NWO), and the project &#x0201c;interACT: Designing cooperative interaction of automated vehicles with other road users in mixed traffic environments,&#x0201d; which received funding from the European Union&#x02019;s Horizon 2020 research and innovation program under grant agreement No. 723395.</p></ack><fn-group><fn fn-type="supplementary-material"><p><bold>Supplemental Material:</bold> The online supplemental material is available with the manuscript on the <italic toggle="yes">HF</italic> website. Raw data and scripts are available at: <ext-link xlink:href="https://doi.org/10.4121/13077797" ext-link-type="uri">https://doi.org/10.4121/13077797</ext-link></p></fn></fn-group><bio id="d64e932"><title>Author Biographies</title><p>Anees Kaleefathullah received his MSc degree in Mechanical Engineering from Delft University of Technology in 2020.</p><p>Natasha Merat is a professor at the Institute for Transport Studies, University of Leeds, chair in Human Factors of Transport Systems, and leader of the Human Factors and Safety Group. She obtained her PhD degree from the University of Leeds in 1999.</p><p>Yee Mun Lee is a research fellow at the Institute for Transport Studies, University of Leeds. She obtained her PhD degree from the University of Nottingham Malaysia Campus in 2016.</p><p>Yke Bauke Eisma is a PhD student at the Department of Cognitive Robotics, Faculty of Mechanical, Maritime and Materials Engineering, Delft University of Technology. He received his MSc degree from Delft University of Technology in 2017.</p><p>Ruth Madigan is a research fellow at the Institute for Transport Studies, University of Leeds. She obtained her PhD from the University College Cork in 2013.</p><p>Jorge Garcia is a research assistant and programmer at the Institute for Transport Studies, University of Leeds.</p><p>Joost de Winter is an associate professor at the Department of Cognitive Robotics, Faculty of Mechanical, Maritime and Materials Engineering, Delft University of Technology. He received his PhD degree from Delft University of Technology in 2009.</p></bio><sec id="sec21-0018720820970751"><title>ORCID iD</title><p>Joost de Winter <ext-link xlink:href="https://orcid.org/0000-0002-1281-8200" ext-link-type="uri">https://orcid.org/0000-0002-1281-8200</ext-link></p></sec><ref-list><title>References</title><ref id="bibr1-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Ackermann</surname><given-names>C.</given-names></name>
<name><surname>Beggiato</surname><given-names>M.</given-names></name>
<name><surname>Schubert</surname><given-names>S.</given-names></name>
<name><surname>Krems</surname><given-names>J. F</given-names></name>
</person-group>. (<year>2019</year>). <article-title>An experimental study to investigate design and assessment criteria: What is important for communication between pedestrians and automated vehicles?</article-title>
<source>Applied Ergonomics</source>, <volume>75</volume>, <fpage>272</fpage>&#x02013;<lpage>282</lpage>.<pub-id pub-id-type="doi">10.1016/j.apergo.2018.11.002</pub-id><?supplied-pmid 30509537?><pub-id pub-id-type="pmid">30509537</pub-id></mixed-citation></ref><ref id="bibr2-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Bazilinskyy</surname><given-names>P.</given-names></name>
<name><surname>Dodou</surname><given-names>D.</given-names></name>
<name><surname>de Winter</surname><given-names>J</given-names></name>
</person-group>. (<year>2019</year>). <article-title>Survey on eHMI concepts: The effect of text, color, and perspective</article-title>. <source>Transportation Research Part F: Traffic Psychology and Behaviour</source>, <volume>67</volume>, <fpage>175</fpage>&#x02013;<lpage>194</lpage>.<pub-id pub-id-type="doi">10.1016/j.trf.2019.10.013</pub-id></mixed-citation></ref><ref id="bibr3-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Bengler</surname><given-names>K.</given-names></name>
<name><surname>Rettenmaier</surname><given-names>M.</given-names></name>
<name><surname>Fritz</surname><given-names>N.</given-names></name>
<name><surname>Feierle</surname><given-names>A</given-names></name>
</person-group>. (<year>2020</year>). <article-title>From HMI to HMIs: Towards an HMI framework for automated driving</article-title>. <source>Information</source>, <volume>11</volume>, <fpage>61</fpage>.<pub-id pub-id-type="doi">10.3390/info11020061</pub-id></mixed-citation></ref><ref id="bibr4-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Boelhouwer</surname><given-names>A.</given-names></name>
<name><surname>van den Beukel</surname><given-names>A. P.</given-names></name>
<name><surname>van der Voort</surname><given-names>M. C.</given-names></name>
<name><surname>Hottentot</surname><given-names>C.</given-names></name>
<name><surname>de Wit</surname><given-names>R. Q.</given-names></name>
<name><surname>Martens</surname><given-names>M. H</given-names></name>
</person-group>. (<year>2020</year>). <article-title>How are CAR buyers and CAR sellers currently informed about ADAS? An investigation among drivers and CAR sellers in the Netherlands</article-title>. <source>Transportation Research Interdisciplinary Perspectives</source>, <volume>4</volume>, 100103.<pub-id pub-id-type="doi">10.1016/j.trip.2020.100103</pub-id></mixed-citation></ref><ref id="bibr5-0018720820970751"><mixed-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Burns</surname><given-names>C. G.</given-names></name>
<name><surname>Oliveira</surname><given-names>L.</given-names></name>
<name><surname>Thomas</surname><given-names>P.</given-names></name>
<name><surname>Iyer</surname><given-names>S.</given-names></name>
<name><surname>Birrell</surname><given-names>S</given-names></name>
</person-group>. (<year>2019</year>). <article-title>Pedestrian decision-making responses to external human-machine interface designs for autonomous vehicles</article-title>
<conf-name>[Symposium]. IEEE Intelligent Vehicles Symposium (IV),</conf-name>
<fpage>70</fpage>&#x02013;<lpage>75</lpage>.</mixed-citation></ref><ref id="bibr6-0018720820970751"><mixed-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Cefkin</surname><given-names>M.</given-names></name>
<name><surname>Zhang</surname><given-names>J.</given-names></name>
<name><surname>Stayton</surname><given-names>E.</given-names></name>
<name><surname>Vinkhuyzen</surname><given-names>E</given-names></name>
</person-group>. (<year>2019</year>). <article-title>Multi-methods research to examine external HMI for highly automated vehicles</article-title>
<conf-name>[Conference session]. International Conference on Human-Computer Interaction</conf-name>, <conf-loc>Copenhagen, Denmark,</conf-loc>
<fpage>46</fpage>&#x02013;<lpage>64</lpage>.</mixed-citation></ref><ref id="bibr7-0018720820970751"><mixed-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Clamann</surname><given-names>M.</given-names></name>
<name><surname>Aubert</surname><given-names>M.</given-names></name>
<name><surname>Cummings</surname><given-names>M. L</given-names></name>
</person-group>. (<year>2017</year>). <article-title>Evaluation of vehicle-to-pedestrian communication displays for autonomous vehicles</article-title>
<conf-name>[Conference session]. Proceedings of the Transportation Research Board 96th Annual Meeting</conf-name>, <conf-loc>Washington DC</conf-loc>.</mixed-citation></ref><ref id="bibr8-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>de Clercq</surname><given-names>K.</given-names></name>
<name><surname>Dietrich</surname><given-names>A.</given-names></name>
<name><surname>N&#x000fa;&#x000f1;ez Velasco</surname><given-names>J. P.</given-names></name>
<name><surname>de Winter</surname><given-names>J.</given-names></name>
<name><surname>Happee</surname><given-names>R</given-names></name>
</person-group>. (<year>2019</year>). <article-title>External human-machine interfaces on automated vehicles: Effects on pedestrian crossing decisions</article-title>. <source>Human Factors: The Journal of the Human Factors and Ergonomics Society</source>, <volume>61</volume>, <fpage>1353</fpage>&#x02013;<lpage>1370</lpage>.<pub-id pub-id-type="doi">10.1177/0018720819836343</pub-id><?supplied-pmid 30912985?><pub-id pub-id-type="pmid">30912985</pub-id></mixed-citation></ref><ref id="bibr9-0018720820970751"><mixed-citation publication-type="book"><person-group person-group-type="author">
<name><surname>Dey</surname><given-names>D.</given-names></name>
<name><surname>Terken</surname><given-names>J</given-names></name>
</person-group>. (<year>2017</year>). <part-title>Pedestrian interaction with vehicles: Roles of explicit and implicit communication</part-title>. In <source>Proceedings of the 9th ACM International Conference on Automotive User Interfaces and Interactive Vehicular Applications (Automotive UI &#x02019;17)</source> (pp. <fpage>109</fpage>&#x02013;<lpage>113</lpage>). <publisher-name>Oldenburg, Germany</publisher-name>.</mixed-citation></ref><ref id="bibr10-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Dixon</surname><given-names>S. R.</given-names></name>
<name><surname>Wickens</surname><given-names>C. D.</given-names></name>
<name><surname>McCarley</surname><given-names>J. S</given-names></name>
</person-group>. (<year>2007</year>). <article-title>On the independence of compliance and reliance: Are automation false alarms worse than misses?</article-title>
<source>Human Factors: The Journal of the Human Factors and Ergonomics Society</source>, <volume>49</volume>, <fpage>564</fpage>&#x02013;<lpage>572</lpage>.<pub-id pub-id-type="doi">10.1518/001872007X215656</pub-id><?supplied-pmid 17702209?><pub-id pub-id-type="pmid">17702209</pub-id></mixed-citation></ref><ref id="bibr11-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Eisma</surname><given-names>Y. B.</given-names></name>
<name><surname>van Bergen</surname><given-names>S.</given-names></name>
<name><surname>ter Brake</surname><given-names>S. M.</given-names></name>
<name><surname>Hensen</surname><given-names>M. T. T.</given-names></name>
<name><surname>Tempelaar</surname><given-names>W. J.</given-names></name>
<name><surname>de Winter</surname><given-names>J. C. F</given-names></name>
</person-group>. (<year>2020</year>). <article-title>External human&#x02013;machine interfaces: The effect of display location on crossing intentions and eye movements</article-title>. <source>Information</source>, <volume>11</volume>, <fpage>13</fpage>.<pub-id pub-id-type="doi">10.3390/info11010013</pub-id></mixed-citation></ref><ref id="bibr12-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Faas</surname><given-names>S. M.</given-names></name>
<name><surname>Mathis</surname><given-names>L.-A.</given-names></name>
<name><surname>Baumann</surname><given-names>M</given-names></name>
</person-group>. (<year>2020</year>). <article-title>External HMI for self-driving vehicles: Which information shall be displayed?</article-title>
<source>Transportation Research Part F: Traffic Psychology and Behaviour</source>, <volume>68</volume>, <fpage>171</fpage>&#x02013;<lpage>186</lpage>.<pub-id pub-id-type="doi">10.1016/j.trf.2019.12.009</pub-id></mixed-citation></ref><ref id="bibr13-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Fagnant</surname><given-names>D. J.</given-names></name>
<name><surname>Kockelman</surname><given-names>K</given-names></name>
</person-group>. (<year>2015</year>). <article-title>Preparing a nation for autonomous vehicles: Opportunities, barriers and policy recommendations</article-title>. <source>Transportation Research Part A: Policy and Practice</source>, <volume>77</volume>, <fpage>167</fpage>&#x02013;<lpage>181</lpage>.<pub-id pub-id-type="doi">10.1016/j.tra.2015.04.003</pub-id></mixed-citation></ref><ref id="bibr14-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Habibovic</surname><given-names>A.</given-names></name>
<name><surname>Lundgren</surname><given-names>V. M.</given-names></name>
<name><surname>Andersson</surname><given-names>J.</given-names></name>
<name><surname>Klingeg&#x000e5;rd</surname><given-names>M.</given-names></name>
<name><surname>Lagstr&#x000f6;m</surname><given-names>T.</given-names></name>
<name><surname>Sirkka</surname><given-names>A.</given-names></name>
<name><surname>Fagerl&#x000f6;nn</surname><given-names>J.</given-names></name>
<name><surname>Edgren</surname><given-names>C.</given-names></name>
<name><surname>Fredriksson</surname><given-names>R.</given-names></name>
<name><surname>Krupenia</surname><given-names>S.</given-names></name>
<name><surname>Salu&#x000e4;&#x000e4;r</surname><given-names>D.</given-names></name>
<name><surname>Larsson</surname><given-names>P</given-names></name>
</person-group>. (<year>2018</year>). <article-title>Communicating intent of automated vehicles to pedestrians</article-title>. <source>Frontiers in Psychology</source>, <volume>9</volume>, 1336.<pub-id pub-id-type="doi">10.3389/fpsyg.2018.01336</pub-id><?supplied-pmid 30131737?><pub-id pub-id-type="pmid">30131737</pub-id></mixed-citation></ref><ref id="bibr15-0018720820970751"><mixed-citation publication-type="book"><person-group person-group-type="author">
<name><surname>Hensch</surname><given-names>A. C.</given-names></name>
<name><surname>Neumann</surname><given-names>I.</given-names></name>
<name><surname>Beggiato</surname><given-names>M.</given-names></name>
<name><surname>Halama</surname><given-names>J.</given-names></name>
<name><surname>Krems</surname><given-names>J. F</given-names></name>
</person-group>. (<year>2020</year>). <part-title>How should automated vehicles communicate? Effects of a light-based communication approach in a Wizard-of-Oz study</part-title>. In <source>Proceedings of the International Conference on applied human factors and ergonomics</source> (pp. <fpage>79</fpage>&#x02013;<lpage>91</lpage>). <publisher-name>New York, NY</publisher-name>.</mixed-citation></ref><ref id="bibr16-0018720820970751"><mixed-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Holl&#x000e4;nder</surname><given-names>K.</given-names></name>
<name><surname>Wintersberger</surname><given-names>P.</given-names></name>
<name><surname>Butz</surname><given-names>A</given-names></name>
</person-group>. (<year>2019</year>). <article-title>Overtrust in external cues of automated vehicles: an experimental investigation</article-title>
<conf-name>[Conference session]. The 11th International Conference Automotive User Interfaces</conf-name>, <conf-loc>Utrecht, the Netherlands</conf-loc>, <fpage>211</fpage>&#x02013;<lpage>222</lpage>.</mixed-citation></ref><ref id="bibr17-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kooij</surname><given-names>J. F. P.</given-names></name>
<name><surname>Flohr</surname><given-names>F.</given-names></name>
<name><surname>Pool</surname><given-names>E. A. I.</given-names></name>
<name><surname>Gavrila</surname><given-names>D. M</given-names></name>
</person-group>. (<year>2019</year>). <article-title>Context-based path prediction for targets with switching dynamics</article-title>. <source>International Journal of Computer Vision</source>, <volume>127</volume>, <fpage>239</fpage>&#x02013;<lpage>262</lpage>.<pub-id pub-id-type="doi">10.1007/s11263-018-1104-4</pub-id></mixed-citation></ref><ref id="bibr18-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kooijman</surname><given-names>L.</given-names></name>
<name><surname>Happee</surname><given-names>R.</given-names></name>
<name><surname>de Winter</surname><given-names>J</given-names></name>
</person-group>. (<year>2019</year>). <article-title>How do eHMIs affect pedestrians&#x02019; crossing behavior? A study using a head-mounted display combined with a motion suit</article-title>. <source>Information</source>, <volume>10</volume>, <fpage>386</fpage>.<pub-id pub-id-type="doi">10.3390/info10120386</pub-id></mixed-citation></ref><ref id="bibr19-0018720820970751"><mixed-citation publication-type="thesis"><person-group person-group-type="author">
<name><surname>Lagstr&#x000f6;m</surname><given-names>T.</given-names></name>
<name><surname>Lundgren</surname><given-names>V. M</given-names></name>
</person-group>. (<year>2015</year>) <article-title>AVIP-Autonomous vehicles interaction with pedestrians</article-title>
<source>[MSc thesis, Chalmers University of Technology]</source>.</mixed-citation></ref><ref id="bibr20-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Lee</surname><given-names>J.</given-names></name>
<name><surname>Moray</surname><given-names>N</given-names></name>
</person-group>. (<year>1992</year>). <article-title>Trust, control strategies and allocation of function in human-machine systems</article-title>. <source>Ergonomics</source>, <volume>35</volume>, <fpage>1243</fpage>&#x02013;<lpage>1270</lpage>.<pub-id pub-id-type="doi">10.1080/00140139208967392</pub-id><?supplied-pmid 1516577?><pub-id pub-id-type="pmid">1516577</pub-id></mixed-citation></ref><ref id="bibr21-0018720820970751"><mixed-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Lee</surname><given-names>Y. M.</given-names></name>
<name><surname>Uttley</surname><given-names>J.</given-names></name>
<name><surname>Madigan</surname><given-names>R.</given-names></name>
<name><surname>Garcia</surname><given-names>J.</given-names></name>
<name><surname>Tomlinson</surname><given-names>A.</given-names></name>
<name><surname>Solernou</surname><given-names>A.</given-names></name>
<name><surname>Romano</surname><given-names>R.</given-names></name>
<name><surname>Markkula</surname><given-names>G.</given-names></name>
<name><surname>Merat</surname><given-names>N</given-names></name>
</person-group>. (<year>2019</year>). <article-title>Understanding the messages conveyed by automated vehicles</article-title>
<conf-name>[Conference session]. The 11th International Conference Automotive User Interfaces</conf-name>, <conf-loc>Utrecht, the Netherlands</conf-loc>, <fpage>134</fpage>&#x02013;<lpage>143</lpage>.</mixed-citation></ref><ref id="bibr22-0018720820970751"><mixed-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>L&#x000f6;cken</surname><given-names>A.</given-names></name>
<name><surname>Golling</surname><given-names>C.</given-names></name>
<name><surname>Riener</surname><given-names>A</given-names></name>
</person-group>. (<year>2019</year>). <article-title>How should automated vehicles interact with pedestrians? A comparative analysis of interaction concepts in virtual reality</article-title>
<conf-name>[Conference session]. The 11th International Conference Automotive User Interfaces</conf-name>, <conf-loc>Utrecht, the Netherlands</conf-loc>, <fpage>262</fpage>&#x02013;<lpage>274</lpage>.</mixed-citation></ref><ref id="bibr23-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Meyer</surname><given-names>J.</given-names></name>
<name><surname>Wiczorek</surname><given-names>R.</given-names></name>
<name><surname>G&#x000fc;nzler</surname><given-names>T</given-names></name>
</person-group>. (<year>2014</year>). <article-title>Measures of reliance and compliance in aided visual scanning</article-title>. <source>Human Factors: The Journal of the Human Factors and Ergonomics Society</source>, <volume>56</volume>, <fpage>840</fpage>&#x02013;<lpage>849</lpage>.<pub-id pub-id-type="doi">10.1177/0018720813512865</pub-id><?supplied-pmid 25141592?><pub-id pub-id-type="pmid">25141592</pub-id></mixed-citation></ref><ref id="bibr24-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Millard-Ball</surname><given-names>A</given-names></name>
</person-group>. (<year>2018</year>). <article-title>Pedestrians, autonomous vehicles, and cities</article-title>. <source>Journal of Planning Education and Research</source>, <volume>38</volume>, <fpage>6</fpage>&#x02013;<lpage>12</lpage>.<pub-id pub-id-type="doi">10.1177/0739456X16675674</pub-id></mixed-citation></ref><ref id="bibr25-0018720820970751"><mixed-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Moore</surname><given-names>D.</given-names></name>
<name><surname>Currano</surname><given-names>R.</given-names></name>
<name><surname>Strack</surname><given-names>G. E.</given-names></name>
<name><surname>Sirkin</surname><given-names>D</given-names></name>
</person-group>. (<year>2019</year>). <article-title>The case for implicit external human-machine interfaces for autonomous vehicles</article-title>
<conf-name>[Conference session]. The 11th International Conference Automotive User Interfaces</conf-name>, <conf-loc>Utrecht, the Netherlands</conf-loc>, <fpage>295</fpage>&#x02013;<lpage>307</lpage>.</mixed-citation></ref><ref id="bibr26-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Mudge</surname><given-names>J. F.</given-names></name>
<name><surname>Baker</surname><given-names>L. F.</given-names></name>
<name><surname>Edge</surname><given-names>C. B.</given-names></name>
<name><surname>Houlahan</surname><given-names>J. E</given-names></name>
</person-group>. (<year>2012</year>). <article-title>Setting an optimal &#x003b1; that minimizes errors in null hypothesis significance tests</article-title>. <source>PLoS ONE</source>, <volume>7</volume>, e32734.<pub-id pub-id-type="doi">10.1371/journal.pone.0032734</pub-id><?supplied-pmid 22389720?><pub-id pub-id-type="pmid">22389720</pub-id></mixed-citation></ref><ref id="bibr27-0018720820970751"><mixed-citation publication-type="webpage"><person-group>
<collab>National Highway Traffic Safety Administration (NHTSA)</collab>
</person-group>. (<year>2019</year>). <article-title>Traffic safety facts: 2017 data</article-title>. <comment>Retrieved March 2019, from</comment><ext-link xlink:href="https://crashstats.nhtsa.dot.gov/Api/Public/ViewPublication/812681" ext-link-type="uri">https://crashstats.nhtsa.dot.gov/Api/Public/ViewPublication/812681</ext-link></mixed-citation></ref><ref id="bibr28-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Parasuraman</surname><given-names>R.</given-names></name>
<name><surname>Riley</surname><given-names>V</given-names></name>
</person-group>. (<year>1997</year>). <article-title>Humans and automation: Use, misuse, disuse, abuse</article-title>. <source>Human Factors: The Journal of the Human Factors and Ergonomics Society</source>, <volume>39</volume>, <fpage>230</fpage>&#x02013;<lpage>253</lpage>.<pub-id pub-id-type="doi">10.1518/001872097778543886</pub-id></mixed-citation></ref><ref id="bibr29-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Rodr&#x000ed;guez Palmeiro</surname><given-names>A.</given-names></name>
<name><surname>van der Kint</surname><given-names>S.</given-names></name>
<name><surname>Vissers</surname><given-names>L.</given-names></name>
<name><surname>Farah</surname><given-names>H.</given-names></name>
<name><surname>de Winter</surname><given-names>J. C. F.</given-names></name>
<name><surname>Hagenzieker</surname><given-names>M</given-names></name>
</person-group>. (<year>2018</year>). <article-title>Interaction between pedestrians and automated vehicles: A wizard of Oz experiment</article-title>. <source>Transportation Research Part F: Traffic Psychology and Behaviour</source>, <volume>58</volume>, <fpage>1005</fpage>&#x02013;<lpage>1020</lpage>.<pub-id pub-id-type="doi">10.1016/j.trf.2018.07.020</pub-id></mixed-citation></ref><ref id="bibr30-0018720820970751"><mixed-citation publication-type="confproc"><person-group person-group-type="author">
<name><surname>Rothenbucher</surname><given-names>D.</given-names></name>
<name><surname>Li</surname><given-names>J.</given-names></name>
<name><surname>Sirkin</surname><given-names>D.</given-names></name>
<name><surname>Mok</surname><given-names>B.</given-names></name>
<name><surname>Ju</surname><given-names>W</given-names></name>
</person-group>. (<year>2016</year>). <article-title>Ghost driver: A field study investigating the interaction between pedestrians and driverless vehicles</article-title>
<conf-name>[Conference session]. 25th IEEE International Conference on Robot and Human Interactive Communication,</conf-name>
<fpage>795</fpage>&#x02013;<lpage>802</lpage>.</mixed-citation></ref><ref id="bibr31-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Schieben</surname><given-names>A.</given-names></name>
<name><surname>Wilbrink</surname><given-names>M.</given-names></name>
<name><surname>Kettwich</surname><given-names>C.</given-names></name>
<name><surname>Madigan</surname><given-names>R.</given-names></name>
<name><surname>Louw</surname><given-names>T.</given-names></name>
<name><surname>Merat</surname><given-names>N</given-names></name>
</person-group>. (<year>2019</year>). <article-title>Designing the interaction of automated vehicles with other traffic participants: Design considerations based on human needs and expectations</article-title>. <source>Cognition, Technology &#x00026; Work</source>, <volume>21</volume>, <fpage>69</fpage>&#x02013;<lpage>85</lpage>.<pub-id pub-id-type="doi">10.1007/s10111-018-0521-z</pub-id></mixed-citation></ref><ref id="bibr32-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Skitka</surname><given-names>L. J.</given-names></name>
<name><surname>Mosier</surname><given-names>K. L.</given-names></name>
<name><surname>Burdick</surname><given-names>M</given-names></name>
</person-group>. (<year>1999</year>). <article-title>Does automation bias decision-making?</article-title>
<source>International Journal of Human-Computer Studies</source>, <volume>51</volume>, <fpage>991</fpage>&#x02013;<lpage>1006</lpage>.<pub-id pub-id-type="doi">10.1006/ijhc.1999.0252</pub-id></mixed-citation></ref><ref id="bibr33-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Sucha</surname><given-names>M.</given-names></name>
<name><surname>Dostal</surname><given-names>D.</given-names></name>
<name><surname>Risser</surname><given-names>R</given-names></name>
</person-group>. (<year>2017</year>). <article-title>Pedestrian-driver communication and decision strategies at marked crossings</article-title>. <source>Accident Analysis &#x00026; Prevention</source>, <volume>102</volume>, <fpage>41</fpage>&#x02013;<lpage>50</lpage>.<pub-id pub-id-type="doi">10.1016/j.aap.2017.02.018</pub-id><?supplied-pmid 28259827?><pub-id pub-id-type="pmid">28259827</pub-id></mixed-citation></ref><ref id="bibr34-0018720820970751"><mixed-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Witmer</surname><given-names>B. G.</given-names></name>
<name><surname>Singer</surname><given-names>M. J</given-names></name>
</person-group>. (<year>1998</year>). <article-title>Measuring presence in virtual environments: A presence questionnaire</article-title>. <source>Presence: Teleoperators and Virtual Environments</source>, <volume>7</volume>, <fpage>225</fpage>&#x02013;<lpage>240</lpage>.<pub-id pub-id-type="doi">10.1162/105474698565686</pub-id></mixed-citation></ref></ref-list></back></article>