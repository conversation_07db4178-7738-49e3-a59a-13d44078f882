<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.3" xml:lang="en" article-type="research-article"><?DTDIdentifier.IdentifierValue -//NLM//DTD Journal Publishing DTD v2.3 20070202//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName journalpublishing.dtd?><?SourceDTD.Version 2.3?><?ConverterInfo.XSLTName nlm2jats3.xsl?><?ConverterInfo.Version 1?><?properties open_access?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">Front Plant Sci</journal-id><journal-id journal-id-type="iso-abbrev">Front Plant Sci</journal-id><journal-id journal-id-type="publisher-id">Front. Plant Sci.</journal-id><journal-title-group><journal-title>Frontiers in Plant Science</journal-title></journal-title-group><issn pub-type="epub">1664-462X</issn><publisher><publisher-name>Frontiers Media S.A.</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">10075323</article-id><article-id pub-id-type="doi">10.3389/fpls.2023.1160645</article-id><article-categories><subj-group subj-group-type="heading"><subject>Plant Science</subject><subj-group><subject>Original Research</subject></subj-group></subj-group></article-categories><title-group><article-title>&#x0201c;How sweet are your strawberries?&#x0201d;: Predicting sugariness using non-destructive and affordable hardware</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Wen</surname><given-names>Junhan</given-names></name><xref rid="aff1" ref-type="aff">
<sup>1</sup>
</xref><xref rid="aff2" ref-type="aff">
<sup>2</sup>
</xref><uri xlink:href="https://loop.frontiersin.org/people/1746637"/></contrib><contrib contrib-type="author"><name><surname>Abeel</surname><given-names>Thomas</given-names></name><xref rid="aff2" ref-type="aff">
<sup>2</sup>
</xref><uri xlink:href="https://loop.frontiersin.org/people/919093"/></contrib><contrib contrib-type="author" corresp="yes"><name><surname>de Weerdt</surname><given-names>Mathijs</given-names></name><xref rid="aff1" ref-type="aff">
<sup>1</sup>
</xref><xref rid="fn001" ref-type="author-notes">
<sup>*</sup>
</xref></contrib></contrib-group><aff id="aff1">
<sup>1</sup>
<institution>Algorithmics Group, Faculty of Electrical Engineering, Mathematics and Computer Science, Delft University of Technology</institution>, <addr-line>Delft</addr-line>, <country>Netherlands</country>
</aff><aff id="aff2">
<sup>2</sup>
<institution>Delft Bioinformatics Lab, Faculty of Electrical Engineering, Mathematics and Computer Science, Delft University of Technology</institution>, <addr-line>Delft</addr-line>, <country>Netherlands</country>
</aff><author-notes><fn fn-type="edited-by"><p>Edited by: Tiziana Amoriello, Council for Agricultural Research and Economics, Italy</p></fn><fn fn-type="edited-by"><p>Reviewed by: Leiqing Pan, Nanjing Agricultural University, China; Jing Zhou, University of Wisconsin-Madison, United States; Alireza Sanaeifar, Zhejiang University, China</p></fn><corresp id="fn001">*Correspondence: Mathijs de Weerdt, <email xlink:href="mailto:<EMAIL>"><EMAIL></email>
</corresp><fn fn-type="other" id="fn002"><p>This article was submitted to Technical Advances in Plant Science, a section of the journal Frontiers in Plant Science</p></fn></author-notes><pub-date pub-type="epub"><day>22</day><month>3</month><year>2023</year></pub-date><pub-date pub-type="collection"><year>2023</year></pub-date><volume>14</volume><elocation-id>1160645</elocation-id><history><date date-type="received"><day>07</day><month>2</month><year>2023</year></date><date date-type="accepted"><day>01</day><month>3</month><year>2023</year></date></history><permissions><copyright-statement>Copyright &#x000a9; 2023 Wen, Abeel and de Weerdt</copyright-statement><copyright-year>2023</copyright-year><copyright-holder>Wen, Abeel and de Weerdt</copyright-holder><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/" specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p>This is an open-access article distributed under the terms of the Creative Commons Attribution License (CC BY). The use, distribution or reproduction in other forums is permitted, provided the original author(s) and the copyright owner(s) are credited and that the original publication in this journal is cited, in accordance with accepted academic practice. No use, distribution or reproduction is permitted which does not comply with these terms.</license-p></license></permissions><abstract><p>Global soft fruit supply chains rely on trustworthy descriptions of product quality. However, crucial criteria such as sweetness and firmness cannot be accurately established without destroying the fruit. Since traditional alternatives are subjective assessments by human experts, it is desirable to obtain quality estimations in a consistent and non-destructive manner. The majority of research on fruit quality measurements analyzed fruits in the lab with uniform data collection. However, it is laborious and expensive to scale up to the level of the whole yield. The &#x0201c;harvest-first, analysis-second&#x0201d; method also comes too late to decide to adjust harvesting schedules. In this research, we validated our hypothesis of using in-field data acquirable <italic>via</italic> commodity hardware to obtain acceptable accuracies. The primary instance that the research concerns is the sugariness of strawberries, described by the juice&#x02019;s total soluble solid (TSS) content (unit: &#x000b0;Brix or Brix). We benchmarked the accuracy of strawberry Brix prediction using convolutional neural networks (CNN), variational autoencoders (VAE), principal component analysis (PCA), kernelized ridge regression (KRR), support vector regression (SVR), and multilayer perceptron (MLP), based on fusions of image data, environmental records, and plant load information, etc. Our results suggest that: (i) models trained by environment and plant load data can perform reliable prediction of aggregated Brix values, with the lowest RMSE at 0.59; (ii) using image data can further supplement the Brix predictions of individual fruits from (i), from 1.27 to as low up to 1.10, but they by themselves are not sufficiently reliable.</p></abstract><kwd-group><kwd>non-destructive analysis</kwd><kwd>in-field test</kwd><kwd>machine learning</kwd><kwd>computer vision</kwd><kwd>data fusion</kwd><kwd>feature selection</kwd><kwd>total soluble solid</kwd><kwd>crop management</kwd></kwd-group><funding-group><funding-statement>This project is fully funded by Topsector Tuinbouw and Uitgangsmaterialen, the Netherlands.</funding-statement></funding-group><counts><fig-count count="7"/><table-count count="1"/><equation-count count="0"/><ref-count count="62"/><page-count count="12"/><word-count count="6093"/></counts></article-meta></front><body><sec sec-type="intro" id="s1"><label>1</label><title>Introduction</title><p>Soft fruits such as strawberries, raspberries, blueberries, etc. are popular and profitable fruit varieties. The annual consumption of strawberries in Europe is estimated to be more than 1.2 million tonnes, which leads the market share of horticultural crops (<xref rid="B9" ref-type="bibr">Calleja et&#x000a0;al., 2012</xref>; <xref rid="B38" ref-type="bibr">Ministry of Foreign Affairs (CBI), 2021a</xref>; <xref rid="B39" ref-type="bibr">Ministry of Foreign Affairs (CBI), 2021b</xref>). Worldwide production of strawberries is stable with increasing demands and prices and is continuously growing even through the COVID-19 pandemic (<xref rid="B10" ref-type="bibr">Chandler et&#x000a0;al., 2012</xref>; <xref rid="B8" ref-type="bibr">Bos-Brouwers et&#x000a0;al., 2015</xref>; <xref rid="B51" ref-type="bibr">Simpson, 2018</xref>; <xref rid="B39" ref-type="bibr">Ministry of Foreign Affairs (CBI), 2021b</xref>). However, without the protection of hard skins, soft fruits are vulnerable during production and post-harvest activities. This results in significant food waste and economic loss (<xref rid="B22" ref-type="bibr">Fruitteelt, 1991</xref>; <xref rid="B21" ref-type="bibr">Food and Agriculture Organization of the United Nations, 2011</xref>; 31 <xref rid="B53" ref-type="bibr">Stenmarck et&#x000a0;al., 2016</xref>). The food loss and waste comprise up to 50% loss along the supply chain in some countries (<xref rid="B36" ref-type="bibr">Macnish, 2012</xref>; <xref rid="B30" ref-type="bibr">Kelly et&#x000a0;al., 2019</xref>), among which the production loss is the majority, which consists of up to 20% (<xref rid="B55" ref-type="bibr">Terry et&#x000a0;al., 2011</xref>; <xref rid="B45" ref-type="bibr">Porat et&#x000a0;al., 2018</xref>). It has been estimated that for every ton of food waste, &#x020ac;1,900 of production and processing costs are lost. Moreover, it is argued that 50% of the waste could be edible (<xref rid="B53" ref-type="bibr">Stenmarck et&#x000a0;al., 2016</xref>).</p><p>The nutritional and economic value of crops is influenced by the harvesting strategy. However, subjective assessments and inappropriate maintenance of fruit quality could bring conflicts in logistics planning between suppliers and distributors, which results in even further post-harvest loss (<xref rid="B46" ref-type="bibr">Ramana et&#x000a0;al., 1981</xref>; <xref rid="B20" ref-type="bibr">Elik et&#x000a0;al., 2019</xref>). Therefore, early decision-making supports both ecological and economic interests. To make logistic and harvesting decisions as early as possible, it is highly desirable to predict the quality of ready-to-harvest strawberries in the field (<xref rid="B1" ref-type="bibr">Abasi et&#x000a0;al., 2018</xref>; <xref rid="B15" ref-type="bibr">Corallo et&#x000a0;al., 2018</xref>; <xref rid="B52" ref-type="bibr">Soosay and Kannusam, 2018</xref>; <xref rid="B32" ref-type="bibr">Lezoche et&#x000a0;al., 2020</xref>).</p><p>Multiple variables determine the quality of a strawberry, including maturity, shape, sweetness, and firmness (<xref rid="B40" ref-type="bibr">Montero et&#x000a0;al., 1996</xref>; <xref rid="B61" ref-type="bibr">Xu and Zhao, 2010</xref>; <xref rid="B33" ref-type="bibr">Liu et&#x000a0;al., 2014</xref>). As the majority of strawberry products are consumed fresh, the taste is the highest priority for most European consumers of strawberries (<xref rid="B10" ref-type="bibr">Chandler et&#x000a0;al., 2012</xref>; <xref rid="B39" ref-type="bibr">Ministry of Foreign Affairs (CBI), 2021b</xref>). Therefore, we narrow our research scope of this paper to concern the interior quality of the fruit, which is not directly told by their appearances: this paper explores the assessment of the level of sweetness of strawberries, which is quantitatively described by total soluble solid (TSS) content in the juice of freshly harvested fruits, using informatics and machine learning (ML) approaches.</p><p>Traditionally, the TSS content is measured by a refractometer, quantified by the degree Brix (&#x000b0;Brix or Brix) (<xref rid="B6" ref-type="bibr">Azodanlou et&#x000a0;al., 2003</xref>). The measurement is expensive in both labor cost and capital because the samples that are sent to destructive measurements can no longer be sold (<xref rid="B26" ref-type="bibr">G&#x000f3;mez et&#x000a0;al., 2006</xref>; <xref rid="B3" ref-type="bibr">Agulheiro-Santos et&#x000a0;al., 2022</xref>). To reduce errors and optimize the supply chain, there is a desire for more accurate, quantitative, and non-destructive tools to assess the quality of each fruit (<xref rid="B57" ref-type="bibr">Ventura et&#x000a0;al., 1998</xref>; <xref rid="B37" ref-type="bibr">Mancini et&#x000a0;al., 2020</xref>). Therefore, we explore the feasibility of Brix prediction with easily-acquirable data, such that the prediction can be carried out on-site without specific fruit preparation.</p><p>Related research has demonstrated the feasibility of applying computer vision (CV) in grading the quality of fruits (<xref rid="B62" ref-type="bibr">Zhang et&#x000a0;al., 2016</xref>; <xref rid="B34" ref-type="bibr">Liu et&#x000a0;al., 2017</xref>; <xref rid="B42" ref-type="bibr">Munera et&#x000a0;al., 2017</xref>; <xref rid="B31" ref-type="bibr">Klinbumrung and Teerachaichayut, 2018</xref>) and in assessing specific quality attributes (<xref rid="B40" ref-type="bibr">Montero et&#x000a0;al., 1996</xref>; <xref rid="B6" ref-type="bibr">Azodanlou et&#x000a0;al., 2003</xref>; <xref rid="B2" ref-type="bibr">Abeytilakarathna et&#x000a0;al., 2013</xref>; <xref rid="B56" ref-type="bibr">Vandendriessche et&#x000a0;al., 2013</xref>). CV and spectral analysis from hyperspectral imaging (HSI) are popular techniques that have often been applied in investigating the intrinsic properties (<xref rid="B4" ref-type="bibr">Amodio et&#x000a0;al., 2019</xref>; <xref rid="B35" ref-type="bibr">Liu et&#x000a0;al., 2019</xref>; <xref rid="B23" ref-type="bibr">Gao et&#x000a0;al., 2020</xref>; <xref rid="B3" ref-type="bibr">Agulheiro-Santos et&#x000a0;al., 2022</xref>). High prediction accuracy could be was achieved when fruit photos were acquired under a (mostly-)uniform experiment setup (<xref rid="B61" ref-type="bibr">Xu and Zhao, 2010</xref>; <xref rid="B43" ref-type="bibr">Nandi et&#x000a0;al., 2016</xref>; <xref rid="B37" ref-type="bibr">Mancini et&#x000a0;al., 2020</xref>; <xref rid="B59" ref-type="bibr">Weng et&#x000a0;al., 2020</xref>; <xref rid="B49" ref-type="bibr">Shao et&#x000a0;al., 2021</xref>). Such setup requires delicate devices which hinder the applications in a real-world setting and on an enormous number of samples. Moreover, the &#x0201c;harvest first, analysis second&#x0201d; methodology limits the possibility of adjusting the harvest strategy for supply chain optimizations because strawberries stop growing after being harvested. Hence, our study concerns the implication of the fruit&#x02019;s intrinsic characteristics by its appearance under natural light, when the fruit is still on the plant.</p><p>Meanwhile, the micro-climate in the greenhouse and the horticultural treatments strongly influence the harvest quality and pace of growing (<xref rid="B13" ref-type="bibr">Choi et&#x000a0;al., 2015</xref>; <xref rid="B50" ref-type="bibr">Sim et&#x000a0;al., 2020</xref>; <xref rid="B19" ref-type="bibr">D&#x000ed;az-Gali&#x000e1;n et&#x000a0;al., 2021</xref>). The temperature, humidity, CO2 level, lighting conditions, and irrigation are proven to be crucial factors (<xref rid="B29" ref-type="bibr">Hidaka et&#x000a0;al., 2016</xref>; <xref rid="B5" ref-type="bibr">Avsar et&#x000a0;al., 2018</xref>; <xref rid="B15" ref-type="bibr">Corallo et&#x000a0;al., 2018</xref>; <xref rid="B41" ref-type="bibr">Muangprathub et&#x000a0;al., 2019</xref>; <xref rid="B50" ref-type="bibr">Sim et&#x000a0;al., 2020</xref>). The crop load is also argued to influence the quality of fruits (<xref rid="B58" ref-type="bibr">Verrelst et&#x000a0;al., 2013</xref>; <xref rid="B7" ref-type="bibr">Belda et&#x000a0;al., 2020</xref>; 76 <xref rid="B16" ref-type="bibr">Correia et&#x000a0;al., 2011</xref>). In modern horticulture, environmental data is readily collected by field sensors or climate computers in most greenhouses (<xref rid="B27" ref-type="bibr">Hayashi et&#x000a0;al., 2013</xref>; <xref rid="B47" ref-type="bibr">Samykanno et&#x000a0;al., 2013</xref>; <xref rid="B41" ref-type="bibr">Muangprathub et&#x000a0;al., 2019</xref>; <xref rid="B50" ref-type="bibr">Sim et&#x000a0;al., 2020</xref>). Nevertheless, these point measurements cannot provide distinctive information to specify the quality of individual fruits. Thus, our research introduces approaches to integrate in-the-wild fruit images with environmental and plant-load data in predicting the Brix values of individual fruits.</p><p>By investigating the performances of Brix prediction models, we aim at providing insights in answering two main questions: i) how accurately can the models estimate the Brix values by different sets of inputs? and ii) which data are valuable for training the Brix prediction models? The research contributes from four perspectives: i) we collected and labeled a dataset of strawberry images and quality measurements, using commodity hardware; ii) we designed a conceptual methodology of non-destructive quality estimation; iii) we shaped and implemented our methodology to predict the strawberry sugariness; iv) by comparing the model performances, we suggest how to develop reliable prediction models by CV and ML techniques.</p></sec><sec sec-type="materials|methods" id="s2"><label>2</label><title>Materials and methods</title><sec id="s2_1"><label>2.1</label><title>Data collection</title><p>Data were collected from May 2021 to November 2021. This was carried out on overwintered trays of <italic>Favori</italic> strawberry plants in a greenhouse at the Delphy Improvement Centre B.V. (Delphy) in Bleiswijk, the Netherlands. Strawberries were cultivated in baskets that were hung from the ceiling in the greenhouse. For the plants monitored by the cameras, the harvesting frequency is mostly once per week, or twice per week when the strawberries grow faster in warmer periods. There is exactly one harvest round per day, so we use &#x0201c;from a harvest&#x0201d; to describe the data collected from the same date.</p><p>The data collection setup consisted of the following parts: i) static cameras facing the planting baskets to take periodic photos; ii) Brix measurements of the strawberries by the horticulturalists from Delphy; iii) physical labels on the branches to identify the measurement results of a strawberry with its appearance in images; iv) climate sensors to record the environment in the greenhouse and the outside weather; v) plant loads, represented by the average number of <italic>Favori</italic> fruits and/or flowersper unit area; vi) other logs about the plant cultivation.</p><p>Representations of individual strawberries were the major inputs to train the Brix prediction models. We considered image data because they are objective and distinct. The images were collected hourly with a time-lapse setting. The same sections of six example images are shown in <xref rid="f1" ref-type="fig">
<bold>Figure&#x000a0;1</bold>
</xref>. As is shown in the figures, we stuck a yellow label to indicate the ID of a strawberry a few hours before the harvest</p><fig position="float" id="f1"><label>Figure&#x000a0;1</label><caption><p>Illustration of the time-lapse images. The same parts of six images are selected. The time stamps of data collection are indicated above the images. According to the images, by 9 am on 2021-08-20, the yellow physical label is stuck onto the branch. The strawberry <italic>20.8.1.1</italic> was harvested between 3 pm and 4 pm of the same day, so the last time when it was observable on images was 3 pm, 2021-08-20.</p></caption><graphic xlink:href="fpls-14-1160645-g001" position="float"/></fig><p>(namely the &#x0201c;ID label&#x0201d;), such that the strawberry&#x02019;s appearance in the images can be connected to the measurement results. The measurement data that are assigned to identified strawberries are called the &#x0201c;connected measurements&#x0201d; in the following text.</p><p>Based on previous research on influencing factors of strawberry qualities (<xref rid="B11" ref-type="bibr">Chen et&#x000a0;al., 2011</xref>; <xref rid="B16" ref-type="bibr">Correia et&#x000a0;al., 2011</xref>; <xref rid="B5" ref-type="bibr">Avsar et&#x000a0;al., 2018</xref>) and the expertise of our collaborating horticulturalists, temperature, humidity, radiation level, CO2 density, and relevant plant treatment records (additional lighting, watering were all considered as the environment data. The number of fruits and/or flowers per unit area was counted weekly and noted as the &#x0201c;plant load&#x0201d;. Both the environment and plant load data were collected by Delphy.</p><p>The strawberries with the ID labels were stored separately. On the same day of the harvest, researchers from Delphy measured the Brix value and the firmness of those strawberries, with a refractometer and a penetrometer respectively. The size category is defined by a ring test, and the ripeness level is evaluated according to the experience of the greenhouse researchers.</p></sec><sec id="s2_2"><label>2.2</label><title>Methodology of experiment implementations</title><p>We segmented the strawberries from the in-field images, such that only the pixels that describe the sample strawberry were analyzed. We trained a Mask R-CNN model (<xref rid="B28" ref-type="bibr">He et&#x000a0;al., 2017</xref>) with a ResNet101 backbone for semantic segmentation. We used the Detectron2 platform (<xref rid="B60" ref-type="bibr">Wu et&#x000a0;al., 2019</xref>) to build the model. The ResNet101 backbone was pre-trained on the <italic>ImageNet</italic> dataset. We resized the image segments to 200*200*3 pixels. They were the raw inputs for Brix prediction and feature extraction in the pa, the <italic>image-with-env experiment</italic>, and the <italic>image-with-Brix experiment</italic>. We considered only the last available observations, e.g. the strawberry segment from the 5 image in <xref rid="f1" ref-type="fig">
<bold>Figure&#x000a0;1</bold>
</xref>. In this way, we limited the quality changes between when it was in the image and when it was measured. We also normalized the colors of the images to reduce the distraction from the changing lighting conditions during the day by applying elastic-net regressions at the red, green, and blue channels respectively.</p><p>To analyze the images in the <italic>image-only experiment</italic>, we built Convolutional Neural Networks (CNNs) and Variational Auto-Encoders (VAEs) to analyze and encode the image segments of individual strawberries with Multi-Layer Perceptrons (MLPs). The models were either trained from scratch or with weights pre-trained by other popular datasets such as the ImageNet (<xref rid="B18" ref-type="bibr">Deng et&#x000a0;al., 2009</xref>). Details of model architectures can be found in the supplementary materials. We also introduced principal component analysis (PCA) in the experiments for feature dimensionality reduction and model regularization (<xref rid="B24" ref-type="bibr">Geladi et&#x000a0;al., 1989</xref>; <xref rid="B48" ref-type="bibr">Shafizadeh-Moghadam, 2021</xref>). By taking the largest differences among the pixel data, PCA helps to exclude disturbance from the shared information of strawberry images to some extent. Hereafter, we use the word &#x0201c;encode&#x0201d; to represent the process of dimensionality reduction by the encoder parts of the VAEs and/or PCA. We use &#x0201c;attribute&#x0201d; to describe the content of information that our model concerns. &#x0201c;Feature&#x0201d; or &#x0201c;input&#x0201d; represents what goes directly to the models, such as information from the latent space of the VAEs and/or after PCA.</p><p>We trained the CNNs, MLPs, the predictor part of the VAEs, and the PCA models by the strawberry observations with connected measurements, which are 178 out of 304 Brix measurements. We trained the encoder and decoder parts of the VAEs by all the segmentation outputs of the Mask R-CNN model. Hence, this dataset includes images that were taken over the life cycles and of more strawberries. The <italic>image-only experiment</italic> and the <italic>image-with-env experiment</italic> applied the same encoders.</p><p>We designed the <italic>env-only experiment</italic> to analyze the relationship between the environment data and the Brix. We used rolling averages of the environment data over different periods. Since the environment data does not include specific information about individual strawberries, we took all of the 304 Brix measurements into account and grouped them by each harvest. They are called the &#x0201c;aggregated Brix&#x0201d;. The reliability of the aggregated Brix could also be better ensured by introducing more sample measurements. We not only trained machine learning models to predict the value expectation, but also the standard deviation (std.) and the percentiles from 10% to 90% (with intervals of 10%). The representations of the Brix distribution were considered in supporting further experiments of individual Brix prediction.</p><p>Since the amount of data points was reduced to the same as the days of harvests after the aggregation, the volume of the dataset became too small to support the training of deep neural networks. Hence, we applied linear regression (LR), support vector regression (SVR), and kernelized ridge regression (KRR) models. In addition, leave-one-out experiments were considered to enlarge the training sets of the <italic>env-only experiment</italic>. That means we split only one data point as the validation set in each experiment run, instead of proportionally splitting. Under this setting, we ensured all the data was used once in performance validation so that we could get a predicted value at every data point. The performance of individual Brix prediction in the <italic>env-only experiment</italic> is discussed based on the results from the leave-one-out experiments, by considering the predicted value expectation as the Brix predictions of all harvests on the same day.</p><p>In the <italic>image-with-env experiment</italic>, we stacked the features of images and the environment data according to the object strawberries to train models. By the encoder parts of the VAEs and the PCAs fitting to the training set, we encoded the images to image features. We trained the models of the <italic>image-with-Brix experiment</italic> by the same image features but with the outputs from the <italic>env-only experiment</italic>&#x02013; predictions of the mean, std., and percentiles, etc. We established four neural network architectures to fit the various size of features in both the <italic>image-with-env experiment</italic> and the xpd, including three three-layer MLPs and one four-layer MLP.</p><p>We used the Keras library (<xref rid="B14" ref-type="bibr">Chollet et&#x000a0;al., 2015</xref>) to build and train the CNNs, VAEs, and MLPs in the experiments. All model training used the Adam optimizer (beta1 = 0.9, beta2 = 0.999) and a learning rate of 0.0003. We considered random rotation, mirroring, and flipping to augment the image data. When training the VAE, we also considered random scaling up to &#x000b1;10%. We used the Scikit-Learn library (<xref rid="B44" ref-type="bibr">Pedregosa et&#x000a0;al., 2011</xref>) to conduct PCA and to construct LR, SVR, and KRR models in the <italic>env-only experiment</italic>. The KRR used polynomial kernels of degrees up to 3 and penalty terms of 1 and 10. These are all state-of-the-art implementations in data analytics.</p><p>For all experiments except with specific definitions, we split the data into 7:1:2 for training: testing: performance validation. We run each experiment 15 times with a fixed series of data splits. All the deep learning models were trained on a Geforce GTX 1080 GPU under a maximum of 300 epochs.</p></sec></sec><sec sec-type="results" id="s3"><label>3</label><title>Results</title><p>This chapter describes our research findings in four steps: i) the exploration of the dataset that we collected; ii) our conceptual methodology of designing the experiments; iii) the model performance of each series of experiments respectively; iv) two influencing feature selections: whether to use the plant load data or not and which image encoder to choose. The last section gives a comparison among the experiment series and states our suggestions for developing a reliable Brix prediction model.</p><sec id="s3_1"><label>3.1</label><title>An integrated dataset describing the growth and harvest quality of strawberries</title><p>In order to predict Brix from non-destructive in-field data, we collected observations of the fruits and related environmental records in a greenhouse. The observations were in the form of images, and the environmental records are time-series and single-value measurements. All relevant data were linked with the observations of individual fruits. As such, we could implement machine learning techniques to discover the mapping from the collected data to the Brix values.</p><p>From April 2021 to November 2021, we recorded the growth of strawberries by 13,400 images from three RGB cameras and collected environmental records during this period. We measured the Brix of 304 ready-to-harvest strawberries, which were selected from 28harvests in 22 weeks. The overall statistics of the measurement data set are shown in <xref rid="f2" ref-type="fig">
<bold>Figure&#x000a0;2</bold>
</xref>. According to the box plots and the line plot, the Brix at each harvest usually has a median value lower than the mean. It is implied that using the average sample measurements to estimate the Brix of every fruit has a higher probability to overestimate the quality.</p><fig position="float" id="f2"><label>Figure&#x000a0;2</label><caption><p>Statistics of the Brix measurements, grouped by harvests per week. On the left, the x-axis indicates the calendar week number of the harvests. The green y-axis presents the number of tested samples. The blue line and its contour indicate the averaged Brix value and the standard deviation (std.) of the measurements of the week respectively. The box plots illustrate the distribution of the measurement for the week. On the right, the histogram gives an overview of the distribution of all Brix measurements in 2021.</p></caption><graphic xlink:href="fpls-14-1160645-g002" position="float"/></fig><p>The environmental records during the data collection period were archived hourly and were grouped by rolling averaging over periods. As a preliminary analysis, we computed the correlations of the environmental data under different averaging periods and the aggregated Brix values of each harvest. The results indicate a strong correlation between temperatures (measured on the leaves, plants, and in the air), radiation levels, watering, and cyclic lighting strengths with the mean Brix of each harvest. The correlations of the Brix with humidity and CO2 density are weaker. Details are shown in <xref rid="SM1" ref-type="supplementary-material">
<bold>Figure S2</bold>
</xref> in <xref rid="SM1" ref-type="supplementary-material">
<bold>Supplementary Materials</bold>
</xref>.</p></sec><sec id="s3_2"><label>3.2</label><title>Conceptual experiment design</title><p>We designed four series of experiments to study the effectiveness of using these data, shown in <xref rid="f3" ref-type="fig">
<bold>Figure&#x000a0;3</bold>
</xref>: we first analyzed whether the images (section 3.3) or the environment data (section 3.4) could work alone in Brix prediction, and then we considered two ways of data fusion (section 3.5).</p><fig position="float" id="f3"><label>Figure&#x000a0;3</label><caption><p>The methodology of the four experiment series in this research. They are described by the data flow, consisting of the input attributes, the output objects, and the models to map the corresponding inputs and outputs. The line colors and the short notes indicate different experiment series: red represents the <italic>image-only experiment</italic>(<bold>&#x0201c;</bold>Exp1<bold>&#x0201d;</bold>), yellow is for the <italic>env-only experiment</italic>(<bold>&#x0201c;</bold>Exp2<bold>&#x0201d;</bold>), blue is for the <italic>image-with-env experiment</italic>(<bold>&#x0201c;</bold>Exp3<bold>&#x0201d;</bold>), and green is for the <italic>image-with-Brix experiment</italic>(<bold>&#x0201c;</bold>Exp4<bold>&#x0201d;</bold>). All the models are evaluated by comparing the outputs with the ground truth.</p></caption><graphic xlink:href="fpls-14-1160645-g003" position="float"/></fig><p>In the <italic>image-only experiment</italic>, the Brix prediction model was trained solely by the images of strawberries. We considered both supervised learning (SL) and semi-supervised (SSL) in training the models in this experiment series. A challenge in this experiment was that the inclusion of non-relevant pixel data lowered the learning process and even reduced the prediction accuracy. To reduce this effect, some of the models were accompanied by additional regularization procedures, such as conducting principal component analysis (PCA) on the training dataset and using the principal components as the features for learning.</p><p>We considered environmental records and/or plant loads as the input in the <italic>env-only experiment</italic>. Together we call these the environment data. In the primary step, we conducted correlation analysis to classify the importance of each sort of attribute and to define sets of features. Since the environment data cannot express information about individual strawberries, we trained regression models to predict the expectation and the distribution of Brix value aggregations of each harvest.</p><p>We established the <italic>image-with-env experiment</italic> and the <italic>image-with-Brix experiment</italic> respectively as two ways of integrating the image data and environmental records in training. We encoded the image of each strawberry to comprise the image features. These features were combined directly with the environmental records to train the neural networks in the <italic>image-with-env experiment</italic>. We considered the image features and the aggregated Brix predictions from the <italic>env-only experiment</italic> as the inputs in the <italic>image-with-Brix experiment</italic>. The setup was chosen based on two assumptions: i) the predictions from the <italic>env-only experiment</italic> are good indications of the overall quality of harvests; ii) compared to predicting the absolute Brix, the appearance information might be more helpful in terms of estimating the relative position out of value distribution of Brix.</p><p>We set up two exam baselines to evaluate the experiment outcomes. Firstly, we used the average value of all the Brix measurements as the expectation of the <italic>Favori</italic> species. It represents the empirical Brix value that members of the soft fruit supply chain usually believe, so it is named the <italic>empirical baseline</italic>. It is the baseline of this Brix prediction study. Secondly, we considered the average Brix of each harvest as the expected value. As it represents the traditional way of sugariness assessment, which is anticipated by sample measurements, it is called the <italic>conventional baseline</italic>. According to the experiment setup, the <italic>conventional baseline</italic> is essentially the optimal situation of models from the <italic>env-only experiment</italic>.</p><p>We used root mean squared error (RMSE) and mean absolute error (MAE) to represent the model accuracy. The RMSE is regarded as the main indicator of model performance. It gives increasingly more punishments if the predicted value is further from the ground truth. After running the experiments over different dataset splits, we used the standard deviation of the RMSEs (RMSE-std.) to indicate the robustness of model performances. The coefficient of determination (also called the R2 score) is considered a quantitative assessment of the level of model fitting. It is the proportion of the variation in the dependent variable, i.e. the individual or the aggregated Brix in this case, that is predictable from the input data. Higher R2 scores indicate better correlations between the inputs and outputs in the mapping.</p></sec><sec id="s3_3"><label>3.3</label><title>Practical Brix prediction models cannot be trained with images alone</title><p>By the <italic>image-only experiment</italic>, we inspect the feasibility to train a Brix predictor with only images. We trained CNNs from scratch, with transfer learning (TL), and with semi-supervised learning (SSL) methods. The best-performing model of the entire experiment series has an averaged RMSE of ca. 1.33 over different validation splits.</p><p>As the horizontal lines in <xref rid="f4" ref-type="fig">
<bold>Figure&#x000a0;4</bold>
</xref> indicate, the selected model outperforms <italic>empirical baseline</italic>, while it is slightly worse than <italic>conventional baseline</italic>. It is implied that the appearances of strawberries provide hints of the Brix to a limited extent, whereas the time of harvest has more predictive power. We hence conducted further experiments to unravel the other attributes for Brix prediction.</p><fig position="float" id="f4"><label>Figure&#x000a0;4</label><caption><p>Performance comparison of Brix prediction accuracies among the four experiment sets, using RMSE as an indicator. The error bars indicate the standard deviation of RMSEs (RMSE-std) across different splits of validation sets. The models are grouped by the ending point of the periods of the environmental records. The y-axis shows the minimum RMSE of models from the same group. The colors indicate the input attributes of the experiment sets. The best performance of models using only image data is presented by a horizontal line. The contour around it indicates the corresponding RMSE-std. The horizontal line in gray and brown indicates the two benchmarks that are mentioned in the methodology section.</p></caption><graphic xlink:href="fpls-14-1160645-g004" position="float"/></fig><p>Among the experiment results, we noticed that the involvement of feature dimensionality reduction facilitates the model performance. A possible mechanism would be that a large proportion of overlapping features were condensed in the latent space of VAEs or the orthonormal bases of PCA (<xref rid="B25" ref-type="bibr">Goharian et&#x000a0;al., 2007</xref>). Meanwhile, the model fitting might also be regularized with the help of PCA, particularly when the model was trained with a small data set in our situation (<xref rid="B24" ref-type="bibr">Geladi et&#x000a0;al., 1989</xref>; <xref rid="B17" ref-type="bibr">Delac et&#x000a0;al., 2005</xref>). These findings also motivated us to encode the images in the data fusion steps of further experiments.</p></sec><sec id="s3_4"><label>3.4</label><title>Models reveal significant dependencies of aggregated Brix on environment data</title><sec id="s3_4_1"><label>3.4.1</label><title>The performance in predicting aggregated values</title><p>In the <italic>env-only experiment</italic>, we trained LR, SVR, and KRR models to assess how well the collective Brix value can be predicted with only the environment data. When aggregating the data points, overfitting was an indispensable issue. Particularly, when the data are very few whilst the inputs have a large dimension. To assess the level of model-fitting, we calculated the R2 score of models using different subsets of features, hyper-parameters, and train-test splits to predict the representations of value aggregations on the testing data set. When we grouped the scores by the algorithms of models to evaluate the level of model determination, we found more than half of the LR models have a negative R2 score, which indicates that simple linear models cannot fit this mapping. With a stronger regularizer, or with higher outlier flexibility, the R2 scores of KRR (alpha=10) and SVR models are more condensed to 0.5-0.6. The generally higher R2 scores also indicate they are more practical models in tackling this circumstance.</p></sec><sec id="s3_4_2"><label>3.4.2</label><title>The performance in predicting individual values</title><p>To make the results comparable, the predictions of the averaged Brix were regarded as the estimation of all the strawberry measurements at each harvest. The RMSEs were hence calculated on the same validation splits as the other experiment sets take. <xref rid="f4" ref-type="fig">
<bold>Figure&#x000a0;4</bold>
</xref> compares the effectiveness of using various periods of environment data with other experiments, of which the time spans are grouped by the ending time.</p><p>As the bars in <xref rid="f4" ref-type="fig">
<bold>Figure&#x000a0;4</bold>
</xref> demonstrate, when models use features from the periods closer to the harvest time, they obtain lower and less diverged RMSEs in general. The RMSE-std of the models in the <italic>env-only experiment</italic> is lower than the best-performing model from the <italic>image-only experiment</italic>. The result argues that even using only the environment data in Brix prediction could train more reliable and stable models. Hence, it is strongly suggested to involve the environment data in training further comprehensive models.</p></sec></sec><sec id="s3_5"><label>3.5</label><title>Images give the power to perform individual prediction with environment data</title><p>Results from the <italic>env-only experiment</italic> indicate that we need specific information to distinguish fruit-to-fruit differences from each harvest. Since the environment data are all point measurements, we encoded the images into 200, 50, 10, and 5 features by four combinations of VAEs and PCA respectively to fit the dimension differences between the two types of data. The <italic>image-with-env experiment</italic> and the <italic>image-with-Brix experiment</italic> introduce two ways of fusing the image feature and environment data.</p><sec id="s3_5_1"><label>3.5.1</label><title>Combining image features with direct environmental information</title><p>The <italic>image-with-env experiment</italic> straightforwardly combined the two types of data to train the MLPs for the individual Brix prediction. Unsurprisingly, the lowest RMSEs from all the groups outperformed the best models from the <italic>image-only experiment</italic> and the <italic>env-only experiment</italic>, as is illustrated in <xref rid="f4" ref-type="fig">
<bold>Figure&#x000a0;4</bold>
</xref>.</p><p>Curiously, the performance difference caused by the collection time span of environment data was remarkably reduced in this experiment. A possible reason would be that the MLPs also learn the trend of changes within the time-series data &#x02013; such that the performance did not reduce as much as in the <italic>env-only experiment</italic>. Meanwhile, the nonlinearity and regularization performed by the neural network also ensured the robustness of the model performances.</p></sec><sec id="s3_5_2"><label>3.5.2</label><title>Combining image features with predicted Brix distribution of a harvest</title><p>The fourth experiment, the <italic>image-with-Brix experiment</italic>, allows us to explore another way of integrating the knowledge from the two sorts of data: to use the image features to predict the relative quality within the distribution of Brix values. We used the predictions of Brix aggregations<xref rid="fn1" ref-type="fn">
<sup>1</sup>
</xref> from the leave-one-out experiments from the <italic>env-only experiment</italic>. Among all the experiment series, the models from the <italic>image-with-Brix experiment</italic> resulted in the lowest RMSEs, as illustrated in <xref rid="f4" ref-type="fig">
<bold>Figure&#x000a0;4</bold>
</xref>. Among the different features of the aggregated Brix, models that were trained by Brix percentiles slightly outperform the models that assumed a Gaussian-distribution fit, i.e. using the mean and std. as inputs.</p></sec></sec><sec id="s3_6"><label>3.6</label><title>Plant load is crucial as part of the indirect environmental information</title><p>As is illustrated in <xref rid="f5" ref-type="fig">
<bold>Figure&#x000a0;5</bold>
</xref>, introducing the plant load as part of the input attributes has a positive effect on the model performances, which is more outstanding on the models from the <italic>env-only experiment</italic>. In the <italic>image-with-env experiment</italic>, the upper limit of model accuracy was slightly improved. But more importantly, there were notable decreases in the std. of RMSEs over different data splits. Both changes were limited in the <italic>image-with-Brix experiment</italic>. In all, we suggest that plant load is a crucial feature when the raw environmental information comprises the input data.</p><fig position="float" id="f5"><label>Figure&#x000a0;5</label><caption><p>Performance comparison of Brix prediction using different attributes of environmental information, using RMSE as an accuracy indicator. The colors indicate the involvement of the plant load data. The y-values indicate the minimum RMSEs of models from the same group.</p></caption><graphic xlink:href="fpls-14-1160645-g005" position="float"/></fig><p>Moreover, since our plant load data was averaged over different branches of strawberries, they did not directly reflect the division of nutrition on the camera-monitored plants as the literature suggests. Hence, we suppose that the data could reveal the general influence of the growing environment on strawberries in this greenhouse compartment in an indirect and deferred way.</p></sec><sec id="s3_7"><label>3.7</label><title>Image encoders have a noteworthy influence on the model performances</title><p>The best-performing models of each family are considered in the previous result discussions. However, the number of image features also influenced the model accuracy. The information from different latent spaces is illustrated in <xref rid="f6" ref-type="fig">
<bold>Figure&#x000a0;6</bold>
</xref>. <xref rid="f7" ref-type="fig">
<bold>Figure&#x000a0;7</bold>
</xref> discusses the effects when the image features are utilized with different representations of environment data. When we used only the images in the prediction, it is still important to keep as many features as possible. Referring to the illustrations in <xref rid="f6" ref-type="fig">
<bold>Figure&#x000a0;6</bold>
</xref>, it is indicated that considering the texture and the shape of strawberries could have a positive influence on the intrinsic quality representation. When using image features together with the raw environment data, we cannot see much difference in the best performances. Nevertheless, we observe an increase in the RMSEs when using larger dimensions of image features with the aggregated Brix. Overall, it is suggested that similar dimensions of image features and the other source of data could generally achieve better RMSEs.</p><fig position="float" id="f6"><label>Figure&#x000a0;6</label><caption><p>Examples of an image segment and its latent features from the four VAEs, plotted in a monologue style. The first column is the original image segment uniformed into a size of 200x200 pixels. The segment background is saved as black and transparent pixels. The level of dimensionality reduction from each encoder is shown on top of the latent space illustrations.</p></caption><graphic xlink:href="fpls-14-1160645-g006" position="float"/></fig><fig position="float" id="f7"><label>Figure&#x000a0;7</label><caption><p>Performance comparison of Brix prediction using different image encoders, using RMSE as an accuracy indicator. The x-axis indicates the input attributes of the experiment sets. The colors indicate the dimensionality of the image features involved in the experiments. The y-values show the minimum RMSEs of all models from the same group.</p></caption><graphic xlink:href="fpls-14-1160645-g007" position="float"/></fig></sec></sec><sec sec-type="discussion" id="s4"><label>4</label><title>Discussion</title><p>With this paper, we propose and evaluate a practical methodology for estimating the sugariness of individual strawberries, starting from planning the data collection setups. This approach uses affordable devices to collect relevant observations in the field and does not require harvesting or destroying the fruit. The experiment results demonstrate that it is feasible to anticipate the quality of strawberries when they are still growing. Such information could support the decision-making of harvesting and supply-chain strategies of greenhouse managers.</p><p>According to <xref rid="f4" ref-type="fig">
<bold>Figure&#x000a0;4</bold>
</xref> and <xref rid="T1" ref-type="table">
<bold>Table&#x000a0;1</bold>
</xref>, the models using image features with aggregated Brix information are the optimal choices among all the attribute combinations. The models could reduce the RMSE by up to 28.8% and 18.9% from the <italic>empirical baseline</italic> and the <italic>conventional baseline</italic> respectively. Compare to the image data, the environmental information has shown to be more relevant for the models to learn from, yet they lack the capability to tell fruit-to-fruit variances. Compared to using data from a sole source, a mixed-use of both could lead to an accuracy improvement of 10.0% and 6.2%, respectively.</p><table-wrap position="float" id="T1"><label>Table&#x000a0;1</label><caption><p>Detailed accuracy indicators of the best-performing models using different sets of input attributes.</p></caption><table frame="hsides" rules="groups"><thead><tr><th valign="top" align="left" rowspan="1" colspan="1">Image Feature</th><th valign="top" colspan="2" align="center" rowspan="1">Env. Data</th><th valign="top" colspan="2" align="center" rowspan="1">Plant Load</th><th valign="top" align="center" rowspan="1" colspan="1">Brix Agg.</th><th valign="top" align="center" rowspan="1" colspan="1">MAE</th><th valign="top" align="center" rowspan="1" colspan="1">RMSE</th><th valign="top" align="center" rowspan="1" colspan="1">RMSE-std.</th></tr></thead><tbody><tr><td valign="top" align="left" rowspan="1" colspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">In Agg. Pred.</td><td valign="top" colspan="2" align="center" rowspan="1">In Agg. Pred</td><td valign="top" align="center" rowspan="1" colspan="1">Percentiles</td><td valign="top" align="center" rowspan="1" colspan="1">0.81</td><td valign="top" align="center" rowspan="1" colspan="1">1.10</td><td valign="top" align="center" rowspan="1" colspan="1">0.158</td></tr><tr><td valign="top" align="left" rowspan="1" colspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">In Agg. Pred.</td><td valign="top" colspan="2" align="center" rowspan="1">In Agg. Pred</td><td valign="top" align="center" rowspan="1" colspan="1">Mean + std.</td><td valign="top" align="center" rowspan="1" colspan="1">0.86</td><td valign="top" align="center" rowspan="1" colspan="1">1.12</td><td valign="top" align="center" rowspan="1" colspan="1">0.139</td></tr><tr><td valign="top" align="left" rowspan="1" colspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">In Agg. Pred.</td><td valign="top" colspan="2" align="center" rowspan="1">In Agg. Pred</td><td valign="top" align="center" rowspan="1" colspan="1">Mean</td><td valign="top" align="center" rowspan="1" colspan="1">0.90</td><td valign="top" align="center" rowspan="1" colspan="1">1.15</td><td valign="top" align="center" rowspan="1" colspan="1">0.118</td></tr><tr><td valign="top" align="left" rowspan="1" colspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">Included</td><td valign="top" align="center" rowspan="1" colspan="1">N/A</td><td valign="top" align="center" rowspan="1" colspan="1">0.90</td><td valign="top" align="center" rowspan="1" colspan="1">1.18</td><td valign="top" align="center" rowspan="1" colspan="1">0.103</td></tr><tr><td valign="top" align="left" rowspan="1" colspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">Not included</td><td valign="top" align="center" rowspan="1" colspan="1">N/A</td><td valign="top" align="center" rowspan="1" colspan="1">0.90</td><td valign="top" align="center" rowspan="1" colspan="1">1.22</td><td valign="top" align="center" rowspan="1" colspan="1">0.119</td></tr><tr><td valign="top" colspan="6" align="center" rowspan="1">
<italic>the conventional baseline</italic>
</td><td valign="top" align="center" rowspan="1" colspan="1">
<italic>0.91</italic>
</td><td valign="top" align="center" rowspan="1" colspan="1">
<italic>1.22</italic>
</td><td valign="top" align="center" rowspan="1" colspan="1">
<italic>0.151</italic>
</td></tr><tr><td valign="top" align="left" rowspan="1" colspan="1">N/A</td><td valign="top" colspan="2" align="center" rowspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">Included</td><td valign="top" align="center" rowspan="1" colspan="1">N/A</td><td valign="top" align="center" rowspan="1" colspan="1">0.96</td><td valign="top" align="center" rowspan="1" colspan="1">1.24</td><td valign="top" align="center" rowspan="1" colspan="1">0.128</td></tr><tr><td valign="top" align="left" rowspan="1" colspan="1">N/A</td><td valign="top" colspan="2" align="center" rowspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">Not included</td><td valign="top" align="center" rowspan="1" colspan="1">N/A</td><td valign="top" align="center" rowspan="1" colspan="1">1.00</td><td valign="top" align="center" rowspan="1" colspan="1">1.27</td><td valign="top" align="center" rowspan="1" colspan="1">0.146</td></tr><tr><td valign="top" align="left" rowspan="1" colspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">Included</td><td valign="top" align="center" rowspan="1" colspan="1">N/A</td><td valign="top" align="center" rowspan="1" colspan="1">1.04</td><td valign="top" align="center" rowspan="1" colspan="1">1.32</td><td valign="top" align="center" rowspan="1" colspan="1">0.134</td></tr><tr><td valign="top" align="left" rowspan="1" colspan="1">Included</td><td valign="top" colspan="2" align="center" rowspan="1">Not included</td><td valign="top" colspan="2" align="center" rowspan="1">Not included</td><td valign="top" align="center" rowspan="1" colspan="1">N/A</td><td valign="top" align="center" rowspan="1" colspan="1">1.00</td><td valign="top" align="center" rowspan="1" colspan="1">1.33</td><td valign="top" align="center" rowspan="1" colspan="1">0.189</td></tr><tr><td valign="top" colspan="6" align="center" rowspan="1">
<italic>the empirical baseline</italic>
</td><td valign="top" align="center" rowspan="1" colspan="1">
<italic>1.21</italic>
</td><td valign="top" align="center" rowspan="1" colspan="1">
<italic>1.56</italic>
</td><td valign="top" align="center" rowspan="1" colspan="1">
<italic>0.312</italic>
</td></tr></tbody></table><table-wrap-foot><fn><p>The models are ranked according to the <bold>&#x0201c;</bold>RMSE<bold>&#x0201d;</bold> column. the <italic>empirical baseline</italic> is calculated by using the Brix expectation of the strawberry variety as all the predicted values. the <italic>conventional baseline</italic> is calculated by taking the average Brix of each harvest as the individual predictions. The MAE and RMSE of all models and benchmarks are calculated by averaging over 15 random validation splits. The std. of the RMSE on each validation split is presented in the <bold>&#x0201c;</bold>RMSE-std<bold>&#x0201d;</bold> column.</p></fn></table-wrap-foot></table-wrap><p>Compared to other research in the field, we included multiple types of data to build machine-learning models. Our models show competitive performances in the sweetness prediction of strawberries [RMSE 1.2 from <xref rid="B54" ref-type="bibr">Sun et&#x000a0;al. (2017)</xref>, MSE 0.95 from <xref rid="B12" ref-type="bibr">Cho et&#x000a0;al. (2019)</xref>] while using in-field data collected more easily-acquired devices. On top of that, the dataset that we collected for pursuing this research is also useful for more research in this field.</p><p>In the above-mentioned experiments, we performed all the procedures step-by-step, yet we see the possibility to exploit higher levels of model integration. Nevertheless, as state-of-the-art computer vision technologies allow detection models to be faster and more portable, expanding the capability of real-time assessments of fruit quality could also be an interesting topic.</p><p>The research primarily studies in-field and non-destructive data that are worth to be considered in training Brix prediction models. The images, which the prediction models were trained with, are essentially a subset of the time-lapse image dataset. With the entire dataset, further research is suggested to include temporal information for refining the quality prediction models. It is also an interesting topic to explore the practicability of using earlier images in forecasting future Brix values.</p><p>Our results suggest that environmental information plays a vital role in training a reliable model. Particularly, the environmental information from up to fourteen days before the harvest is crucial to ensure the model&#x02019;s accuracy. Nevertheless, we did not discuss the detailed influence of specific sources of climate data on our model accuracies. It is therefore recommended to conduct subsequent studies on the effectiveness of learning with different environmental factors.</p></sec><sec sec-type="data-availability" id="s5"><title>Data availability statement</title><p>The original contributions presented in the study are included in the article/<xref rid="SM1" ref-type="supplementary-material">
<bold>Supplementary Material</bold>
</xref>. Further inquiries can be directed to the corresponding author. The datasets that are collected for this study can be found in the 4TU Data Repository: doi:10.4121/********.</p></sec><sec sec-type="author-contributions" id="s6"><title>Author contributions</title><p>JW confirms her contribution to the paper as follows: research conception and design, data (pre-) processing, analysis and interpretation of results, and manuscript preparation. All the work was done under the supervision of MW and TA. All authors reviewed the results and approved the final version of the manuscript. All authors agree to be accountable for all aspects of the work in ensuring that questions related to the accuracy or integrity of any part of the work are appropriately investigated and resolved.</p></sec></body><back><ack><title>Acknowledgments</title><p>The authors are grateful to Delphy Improvement Centre B.V. (Delphy) for providing strawberry plants to be monitored and measured. We thank Lisanne Schuddebeurs, Stijn Jochems, Klaas Walraven, and Vera Theelen from Delphy for their data collection advice. The environmental records, the plant load data, and the destructive quality measurement tests that comprise the dataset are all provided by Delphy. We are also thankful to Camiel R. Verschoor and Jan Erik van Woerden from Birds.ai B.V. for collecting, annotating, and pre-processing the image data. Thanks to Lucas van Dijk from TU Delft, Erin Noel Jordan from TU Dortmund, and Stijn Jochems from Delphy, for giving great and thorough suggestions on the paper.</p></ack><fn-group><fn id="fn1"><label>1</label><p>To limit the variables, we took only results from the KRR model with alpha=10 and polynomial degree=3.</p></fn></fn-group><sec sec-type="COI-statement" id="s8"><title>Conflict of interest</title><p>The authors declare that the research was conducted in the absence of any commercial or financial relationships that could be construed as a potential conflict of interest.</p></sec><sec sec-type="disclaimer" id="s9"><title>Publisher&#x02019;s note</title><p>All claims expressed in this article are solely those of the authors and do not necessarily represent those of their affiliated organizations, or those of the publisher, the editors and the reviewers. Any product that may be evaluated in this article, or claim that may be made by its manufacturer, is not guaranteed or endorsed by the publisher.</p></sec><sec sec-type="supplementary-material" id="s10"><title>Supplementary material</title><p>The Supplementary Material for this article can be found online at: <ext-link xlink:href="https://www.frontiersin.org/articles/10.3389/fpls.2023.1160645/full#supplementary-material" ext-link-type="uri">https://www.frontiersin.org/articles/10.3389/fpls.2023.1160645/full#supplementary-material</ext-link>
</p><supplementary-material id="SM1" position="float" content-type="local-data"><media xlink:href="Presentation_1.pdf"><caption><p>Click here for additional data file.</p></caption></media></supplementary-material></sec><ref-list><title>References</title><ref id="B1"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Abasi</surname><given-names>S.</given-names></name><name><surname>Minaei</surname><given-names>S.</given-names></name><name><surname>Jamshidi</surname><given-names>B.</given-names></name><name><surname>Fathi</surname><given-names>D.</given-names></name></person-group> (<year>2018</year>). <article-title>Dedicated non-destructive devices for food quality measurement: A review</article-title>. <source>Trends Food Sci. Technol.</source>
<volume>78</volume>, <fpage>197</fpage>&#x02013;<lpage>205</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.tifs.2018.05.009</pub-id>
</mixed-citation></ref><ref id="B2"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Abeytilakarathna</surname><given-names>P.</given-names></name><name><surname>Fonseka</surname><given-names>R.</given-names></name><name><surname>Eswara</surname><given-names>J.</given-names></name><name><surname>Wijethunga</surname><given-names>K.</given-names></name></person-group> (<year>2013</year>). <article-title>Relationship between total solid content and red, green and blue colour intensity of strawberry (Fragaria x ananassa duch.) fruits</article-title>. <source>J. Agric. Sci.</source>
<volume>8</volume>, <fpage>82</fpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.4038/jas.v8i2.5743</pub-id>
</mixed-citation></ref><ref id="B3"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Agulheiro-Santos</surname><given-names>A. C.</given-names></name><name><surname>Ricardo-Rodrigues</surname><given-names>S.</given-names></name><name><surname>Laranjo</surname><given-names>M.</given-names></name><name><surname>Melg&#x000e3;o</surname><given-names>C.</given-names></name><name><surname>Vel&#x000e1;zquez</surname><given-names>R.</given-names></name></person-group> (<year>2022</year>). <article-title>Non-destructive prediction of total soluble solids in strawberry using near infrared spectroscopy</article-title>. <source>J. Sci. Food Agric.</source>
<volume>102</volume>, <fpage>4866</fpage>&#x02013;<lpage>4872</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1002/jsfa.11849</pub-id>
<pub-id pub-id-type="pmid">35244203</pub-id></mixed-citation></ref><ref id="B4"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Amodio</surname><given-names>M. L.</given-names></name><name><surname>Chaudhry</surname><given-names>M. M. A.</given-names></name><name><surname>Colelli</surname><given-names>G.</given-names></name></person-group> (<year>2019</year>). <article-title>Spectral and hyperspectral technologies as an additional tool to increase information on quality and origin of horticultural crops</article-title>. <source>Agronomy</source>
<volume>10</volume>, <elocation-id>7</elocation-id>. doi:&#x000a0;<pub-id pub-id-type="doi">10.3390/agronomy10010007</pub-id>
</mixed-citation></ref><ref id="B5"><mixed-citation publication-type="confproc">
<person-group person-group-type="author"><name><surname>Avsar</surname><given-names>E.</given-names></name><name><surname>Bulus</surname><given-names>K.</given-names></name><name><surname>Saridas</surname><given-names>M. A.</given-names></name><name><surname>Kapur</surname><given-names>B.</given-names></name></person-group> (<year>2018</year>). &#x0201c;<article-title>Development of a cloud-based automatic irrigation system: A case study on strawberry cultivation</article-title>,&#x0201d; in <conf-name>2018 7th International Conference on Modern Circuits and Systems Technologies (MOCAST)</conf-name>. (<conf-loc>Thessaloniki, Greece</conf-loc>: IEEE) <fpage>1</fpage>&#x02013;<lpage>4</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1109/MOCAST.2018.8376641</pub-id>
</mixed-citation></ref><ref id="B6"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Azodanlou</surname><given-names>R.</given-names></name><name><surname>Darbellay</surname><given-names>C.</given-names></name><name><surname>Luisier</surname><given-names>J.-L.</given-names></name><name><surname>Villettaz</surname><given-names>J.-C.</given-names></name><name><surname>Amad&#x000f2;</surname><given-names>R.</given-names></name></person-group> (<year>2003</year>). <article-title>Quality assessment of strawberries ( fragaria species)</article-title>. <source>J. Agric. Food Chem.</source>
<volume>51</volume>, <fpage>715</fpage>&#x02013;<lpage>721</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1021/jf0200467</pub-id>
<pub-id pub-id-type="pmid">12537447</pub-id></mixed-citation></ref><ref id="B7"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Belda</surname><given-names>S.</given-names></name><name><surname>Pipia</surname><given-names>L.</given-names></name><name><surname>Morcillo-Pallar&#x000e9;s</surname><given-names>P.</given-names></name><name><surname>Verrelst</surname><given-names>J.</given-names></name></person-group> (<year>2020</year>). <article-title>Optimizing Gaussian process regression for image time series gap-filling and crop monitoring</article-title>. <source>Agronomy</source>
<volume>10</volume>, <elocation-id>618</elocation-id>. doi:&#x000a0;<pub-id pub-id-type="doi">10.3390/agronomy10050618</pub-id>
<pub-id pub-id-type="pmid">36081839</pub-id></mixed-citation></ref><ref id="B8"><mixed-citation publication-type="book">
<person-group person-group-type="author"><name><surname>Bos-Brouwers</surname><given-names>H. E. J.</given-names></name><name><surname>Soethoudt</surname><given-names>J. M.</given-names></name><name><surname>Vollebregt</surname><given-names>H. M.</given-names></name><name><surname>Burgh</surname><given-names>M.</given-names></name></person-group> (<year>2015</year>). <source>Monitor voedselverspilling: update monitor voedselverspilling 2009-2013 &#x00026; mogelijkheden tot (zelf) monitoring van voedselverspilling door de keten heen. tech. </source> (<publisher-loc>Wageningen, The Netherlands</publisher-loc>: <publisher-name>Wageningen Bos</publisher-name>).</mixed-citation></ref><ref id="B9"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Calleja</surname><given-names>E.</given-names></name><name><surname>Ilbery</surname><given-names>B.</given-names></name><name><surname>Mills</surname><given-names>P.</given-names></name></person-group> (<year>2012</year>). <article-title>Agricultural change and the rise of the British strawberry industry 1920&#x02013;2009</article-title>. <source>J. Rural Stud.</source>
<volume>28</volume>, <fpage>603</fpage>&#x02013;<lpage>611</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.jrurstud.2012.07.005</pub-id>
</mixed-citation></ref><ref id="B10"><mixed-citation publication-type="book">
<person-group person-group-type="author"><name><surname>Chandler</surname><given-names>C. K.</given-names></name><name><surname>Folta</surname><given-names>K.</given-names></name><name><surname>Dale</surname><given-names>A.</given-names></name><name><surname>Whitaker</surname><given-names>V. M.</given-names></name><name><surname>Herrington</surname><given-names>M.</given-names></name></person-group> (<year>2012</year>). &#x0201c;<article-title>Strawberry</article-title>,&#x0201d; in <source>Fruit breeding</source> (<publisher-loc>Boston, MA</publisher-loc>: <publisher-name>Springer US</publisher-name>), <fpage>305</fpage>&#x02013;<lpage>325</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1007/978-1-4419-0763-9_9</pub-id>
</mixed-citation></ref><ref id="B11"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Chen</surname><given-names>F.</given-names></name><name><surname>Tang</surname><given-names>Y.-N.</given-names></name><name><surname>Shen</surname><given-names>M.-Y.</given-names></name></person-group> (<year>2011</year>). <article-title>Coordination control of greenhouse environmental factors</article-title>. <source>Int. J. Automation Computing</source>
<volume>8</volume>, <fpage>147</fpage>&#x02013;<lpage>153</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1007/s11633-011-0567-3</pub-id>
</mixed-citation></ref><ref id="B12"><mixed-citation publication-type="confproc">
<person-group person-group-type="author"><name><surname>Cho</surname><given-names>W.</given-names></name><name><surname>Na</surname><given-names>M.</given-names></name><name><surname>Kim</surname><given-names>S.</given-names></name><name><surname>Jeon</surname><given-names>W.</given-names></name></person-group> (<year>2019</year>). &#x0201c;<article-title>Automatic prediction of brix and acidity in stages of ripeness of strawberries using image processing techniques</article-title>,&#x0201d; in <conf-name>2019 34th International Technical Conference on Circuits/Systems, Computers and Communications (ITC-CSCC)</conf-name>, <conf-loc>JeJu, Korea (South)</conf-loc>. <fpage>1</fpage>&#x02013;<lpage>4</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1109/ITC-CSCC.2019.8793349</pub-id>
</mixed-citation></ref><ref id="B13"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Choi</surname><given-names>H. G.</given-names></name><name><surname>Moon</surname><given-names>B. Y.</given-names></name><name><surname>Kang</surname><given-names>N. J.</given-names></name></person-group> (<year>2015</year>). <article-title>Effects of LED light on the production of strawberry during cultivation in a plastic greenhouse and in a growth chamber</article-title>. <source>Scientia Hortic.</source>
<volume>189</volume>, <fpage>22</fpage>&#x02013;<lpage>31</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.scienta.2015.03.022</pub-id>
</mixed-citation></ref><ref id="B14"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Chollet</surname><given-names>F.</given-names></name><etal/></person-group>. (<year>2015</year>). <article-title>Keras</article-title>. GitHub. Available at: <uri xlink:href="https://github.com/fchollet/keras">https://github.com/fchollet/keras</uri>.</mixed-citation></ref><ref id="B15"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Corallo</surname><given-names>A.</given-names></name><name><surname>Latino</surname><given-names>M. E.</given-names></name><name><surname>Menegoli</surname><given-names>M.</given-names></name></person-group> (<year>2018</year>). <article-title>From industry 4.0 to agriculture 4.0: A framework to manage product data in agri-food supply chain for voluntary traceability, a framework proposed</article-title>. <source>Int. J. Nutr. Food Eng.</source>
<volume>11</volume>, <fpage>126</fpage>&#x02013;<lpage>130</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.5281/zenodo.1316618</pub-id>
</mixed-citation></ref><ref id="B16"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Correia</surname><given-names>P.</given-names></name><name><surname>Pestana</surname><given-names>M.</given-names></name><name><surname>Martinez</surname><given-names>F.</given-names></name><name><surname>Ribeiro</surname><given-names>E.</given-names></name><name><surname>Gama</surname><given-names>F.</given-names></name><name><surname>Saavedra</surname><given-names>T.</given-names></name><etal/></person-group>. (<year>2011</year>). <article-title>Relationships between strawberry fruit quality attributes and crop load</article-title>. <source>Scientia Hortic.</source>
<volume>130</volume>, <fpage>398</fpage>&#x02013;<lpage>403</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.scienta.2011.06.039</pub-id>
</mixed-citation></ref><ref id="B17"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Delac</surname><given-names>K.</given-names></name><name><surname>Grgic</surname><given-names>M.</given-names></name><name><surname>Grgic</surname><given-names>S.</given-names></name></person-group> (<year>2005</year>). <article-title>Independent comparative study of PCA, ICA, and LDA on the FERET data set</article-title>. <source>Int. J. Imaging Syst. Technol.</source>
<volume>15</volume>, <fpage>252</fpage>&#x02013;<lpage>260</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1002/ima.20059</pub-id>
</mixed-citation></ref><ref id="B18"><mixed-citation publication-type="confproc">
<person-group person-group-type="author"><name><surname>Deng</surname><given-names>J.</given-names></name><name><surname>Dong</surname><given-names>W.</given-names></name><name><surname>Socher</surname><given-names>R.</given-names></name><name><surname>Li</surname><given-names>L.-J.</given-names></name><name><surname>Li</surname><given-names>K.</given-names></name><name><surname>Fei-Fei</surname><given-names>L.</given-names></name></person-group> (<year>2009</year>). &#x0201c;<article-title>Imagenet: A large-scale hierarchical image database</article-title>,&#x0201d; in <conf-name>2009 IEEE conference on computer vision and pattern recognition</conf-name>. (<conf-loc>Miami, FL, USA</conf-loc>: IEEE), <fpage>248</fpage>&#x02013;<lpage>255</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1109/CVPR.2009.5206848</pub-id>
</mixed-citation></ref><ref id="B19"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>D&#x000ed;az-Gali&#x000e1;n</surname><given-names>M. V.</given-names></name><name><surname>Torres</surname><given-names>M.</given-names></name><name><surname>Sanchez-Pag&#x000e1;n</surname><given-names>J. D.</given-names></name><name><surname>Navarro</surname><given-names>P. J.</given-names></name><name><surname>Weiss</surname><given-names>J.</given-names></name><name><surname>Egea-Cortines</surname><given-names>M.</given-names></name></person-group> (<year>2021</year>). <article-title>Enhancement of strawberry production and fruit quality by blue and red LED lights in research and commercial greenhouses</article-title>. <source>South Afr. J. Bot.</source>
<volume>140</volume>, <fpage>269</fpage>&#x02013;<lpage>275</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.sajb.2020.05.004</pub-id>
</mixed-citation></ref><ref id="B20"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Elik</surname><given-names>A.</given-names></name><name><surname>Yanik</surname><given-names>D. K.</given-names></name><name><surname>Guzelsoy</surname><given-names>N. A.</given-names></name><name><surname>Yavuz</surname><given-names>A.</given-names></name><name><surname>Gogus</surname><given-names>F.</given-names></name></person-group> (<year>2019</year>). <article-title>Strategies to reduce post-harvest losses for fruits and vegetables</article-title>. <source>Int. J. Sci. Technological Res</source>
<volume>5</volume> (<issue>3</issue>), <fpage>29</fpage>&#x02013;<lpage>39</lpage> doi:&#x000a0;<pub-id pub-id-type="doi">10.7176/JSTR/5-3-04</pub-id>
</mixed-citation></ref><ref id="B21"><mixed-citation publication-type="book">
<person-group person-group-type="author"><collab>Food and Agriculture Organization of the United Nations</collab></person-group> (<year>2011</year>). <source>&#x0201c;Global food losses and food waste&#x02013;Extent, causes and prevention.&#x0201d; SAVE FOOD: An initiative on food loss and waste reduction 9, 2011</source>.</mixed-citation></ref><ref id="B22"><mixed-citation publication-type="book">
<person-group person-group-type="author"><name><surname>Fruitteelt</surname><given-names>V. D. E.</given-names></name></person-group> (<year>1991</year>). <source>Jaarverslag 1991</source> (<publisher-loc>Wilhelminadorp, The Netherlands</publisher-loc>: <publisher-name>Proefstation voor de Fruitteelt Brugstraat 51 4475AN</publisher-name>).</mixed-citation></ref><ref id="B23"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Gao</surname><given-names>J.</given-names></name><name><surname>Li</surname><given-names>P.</given-names></name><name><surname>Chen</surname><given-names>Z.</given-names></name><name><surname>Zhang</surname><given-names>J.</given-names></name></person-group> (<year>2020</year>). <article-title>A survey on deep learning for multimodal data fusion</article-title>. <source>Neural Comput.</source>
<volume>32</volume>, <fpage>829</fpage>&#x02013;<lpage>864</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1162/neco_a_01273</pub-id>
<pub-id pub-id-type="pmid">32186998</pub-id></mixed-citation></ref><ref id="B24"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Geladi</surname><given-names>P.</given-names></name><name><surname>Isaksson</surname><given-names>H.</given-names></name><name><surname>Lindqvist</surname><given-names>L.</given-names></name><name><surname>Wold</surname><given-names>S.</given-names></name><name><surname>Esbensen</surname><given-names>K.</given-names></name></person-group> (<year>1989</year>). <article-title>Principal component analysis of multivariate images</article-title>. <source>Chemometrics Intelligent Lab. Syst.</source>
<volume>5</volume>, <fpage>209</fpage>&#x02013;<lpage>220</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/0169-7439(89)80049-8</pub-id>
</mixed-citation></ref><ref id="B25"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Goharian</surname><given-names>M.</given-names></name><name><surname>Bruwer</surname><given-names>M.-J.</given-names></name><name><surname>Jegatheesan</surname><given-names>A.</given-names></name><name><surname>Moran</surname><given-names>G. R.</given-names></name><name><surname>MacGregor</surname><given-names>J. F.</given-names></name></person-group> (<year>2007</year>). <article-title>A novel approach for EIT regularization <italic>via</italic> spatial and spectral principal component analysis</article-title>. <source>Physiol. Measurement</source>
<volume>28</volume>, <fpage>1001</fpage>&#x02013;<lpage>1016</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1088/0967-3334/28/9/003</pub-id>
</mixed-citation></ref><ref id="B26"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>G&#x000f3;mez</surname><given-names>A. H.</given-names></name><name><surname>He</surname><given-names>Y.</given-names></name><name><surname>Pereira</surname><given-names>A. G.</given-names></name></person-group> (<year>2006</year>). <article-title>Non-destructive measurement of acidity, soluble solids and firmness of Satsuma mandarin using Vis/NIR-spectroscopy techniques</article-title>. <source>J. Food Eng.</source>
<volume>77</volume>, <fpage>313</fpage>&#x02013;<lpage>319</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.jfoodeng.2005.06.036</pub-id>
</mixed-citation></ref><ref id="B27"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Hayashi</surname><given-names>S.</given-names></name><name><surname>Yamamoto</surname><given-names>S.</given-names></name><name><surname>Saito</surname><given-names>S.</given-names></name><name><surname>Ochiai</surname><given-names>Y.</given-names></name><name><surname>Nagasaki</surname><given-names>Y.</given-names></name><name><surname>Kohno</surname><given-names>Y.</given-names></name></person-group> (<year>2013</year>). <article-title>Structural environment suited to the operation of a strawberry-harvesting robot mounted on a travelling platform</article-title>. <source>Eng. Agriculture Environ. Food</source>
<volume>6</volume>, <fpage>34</fpage>&#x02013;<lpage>40</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/S1881-8366(13)80015-8</pub-id>
</mixed-citation></ref><ref id="B28"><mixed-citation publication-type="confproc">
<person-group person-group-type="author"><name><surname>He</surname><given-names>K.</given-names></name><name><surname>Gkioxari</surname><given-names>G.</given-names></name><name><surname>Dollar</surname><given-names>P.</given-names></name><name><surname>Girshick</surname><given-names>R.</given-names></name></person-group> (<year>2017</year>). &#x0201c;<article-title>Mask r-CNN</article-title>,&#x0201d; in <conf-name>2017 IEEE International Conference on Computer Vision (ICCV)</conf-name>, <conf-loc>Venice, Italy</conf-loc>. <fpage>2980</fpage>&#x02013;<lpage>2988</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1109/ICCV.2017.322</pub-id>
</mixed-citation></ref><ref id="B29"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Hidaka</surname><given-names>K.</given-names></name><name><surname>Dan</surname><given-names>K.</given-names></name><name><surname>Miyoshi</surname><given-names>Y.</given-names></name><name><surname>Imamura</surname><given-names>H.</given-names></name><name><surname>Takayama</surname><given-names>T.</given-names></name><name><surname>Kitano</surname><given-names>M.</given-names></name><etal/></person-group>. (<year>2016</year>). <article-title>Twofold increase in strawberry productivity by integration of environmental control and movable beds in a Large-scale greenhouse</article-title>. <source>Environ. Control Biol.</source>
<volume>54</volume>, <fpage>79</fpage>&#x02013;<lpage>92</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.2525/ecb.54.79</pub-id>
</mixed-citation></ref><ref id="B30"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Kelly</surname><given-names>K.</given-names></name><name><surname>Madden</surname><given-names>R.</given-names></name><name><surname>Emond</surname><given-names>J. P.</given-names></name><name><surname>do Nascimento Nunes</surname><given-names>M. C.</given-names></name></person-group> (<year>2019</year>). <article-title>A novel approach to determine the impact level of each step along the supply chain on strawberry quality</article-title>. <source>Postharvest Biol. Technol.</source>
<volume>147</volume>, <fpage>78</fpage>&#x02013;<lpage>88</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.postharvbio.2018.09.012</pub-id>
</mixed-citation></ref><ref id="B31"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Klinbumrung</surname><given-names>N.</given-names></name><name><surname>Teerachaichayut</surname><given-names>S.</given-names></name></person-group> (<year>2018</year>). <article-title>Quantification of acidity and total soluble solids in guavas by near infrared hyperspectral imaging</article-title>. <source>AIP Conference Proceedings</source>
<volume>2030</volume> (<issue>1</issue>), <fpage>020209</fpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1063/1.5066850</pub-id>
</mixed-citation></ref><ref id="B32"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Lezoche</surname><given-names>M.</given-names></name><name><surname>Hernandez</surname><given-names>J. E.</given-names></name><name><surname>Alemany D&#x000ed;az</surname><given-names>M.</given-names></name><name><surname>d.</surname><given-names>M. E.</given-names></name><name><surname>Panetto</surname><given-names>H.</given-names></name><name><surname>Kacprzyk</surname><given-names>J.</given-names></name></person-group> (<year>2020</year>). <article-title>Agri-food 4.0: A survey of the supply chains and technologies for the future agriculture</article-title>. <source>Comput. Industry</source>
<volume>117</volume>, <elocation-id>103187</elocation-id>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.compind.2020.103187</pub-id>
</mixed-citation></ref><ref id="B33"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Liu</surname><given-names>C.</given-names></name><name><surname>Liu</surname><given-names>W.</given-names></name><name><surname>Lu</surname><given-names>X.</given-names></name><name><surname>Ma</surname><given-names>F.</given-names></name><name><surname>Chen</surname><given-names>W.</given-names></name><name><surname>Yang</surname><given-names>J.</given-names></name><etal/></person-group>. (<year>2014</year>). <article-title>Application of multispectral imaging to determine quality attributes and ripeness stage in strawberry fruit</article-title>. <source>PloS One</source>
<volume>9</volume>, <elocation-id>e87818</elocation-id>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1371/journal.pone.0087818</pub-id>
<pub-id pub-id-type="pmid">24505317</pub-id></mixed-citation></ref><ref id="B34"><mixed-citation publication-type="confproc">
<person-group person-group-type="author"><name><surname>Liu</surname><given-names>F.</given-names></name><name><surname>Snetkov</surname><given-names>L.</given-names></name><name><surname>Lima</surname><given-names>D.</given-names></name></person-group> (<year>2017</year>). &#x0201c;<article-title>Summary on fruit identification methods: A literature review</article-title>,&#x0201d; in <conf-name>2017 3rd International Conference on Economics, Social Science, Arts, Education and Management Engineering (ESSAEME 2017)</conf-name>. (<conf-loc>Huhhot, China</conf-loc>: Atlantis Press) <volume>119</volume>, <fpage>1629</fpage>&#x02013;<lpage>1633</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.2991/essaeme-17.2017.338</pub-id>
</mixed-citation></ref><ref id="B35"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Liu</surname><given-names>Q.</given-names></name><name><surname>Wei</surname><given-names>K.</given-names></name><name><surname>Xiao</surname><given-names>H.</given-names></name><name><surname>Tu</surname><given-names>S.</given-names></name><name><surname>Sun</surname><given-names>K.</given-names></name><name><surname>Sun</surname><given-names>Y.</given-names></name><etal/></person-group>. (<year>2019</year>). <article-title>Near-infrared hyperspectral imaging rapidly detects the decay of postharvest strawberry based on water-soluble sugar analysis</article-title>. <source>Food Analytical Methods</source>
<volume>12</volume>, <fpage>936</fpage>&#x02013;<lpage>946</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1007/s12161-018-01430-2</pub-id>
</mixed-citation></ref><ref id="B36"><mixed-citation publication-type="confproc">
<person-group person-group-type="author"><name><surname>Macnish</surname><given-names>A. J.</given-names></name></person-group> (<year>2012</year>). <article-title>Crop Post-Harvest: Science and technology&#x02013;perishables</article-title>. Eds.<person-group person-group-type="editor"><name><surname>Rees</surname><given-names>D.</given-names></name><name><surname>Farrell</surname><given-names>G.</given-names></name><name><surname>Orchard</surname><given-names>J.</given-names></name></person-group>. <source>Exp. Agric.</source> (<conf-loc>Chichester, UK</conf-loc>: Wiley-Blackwell), <volume>48</volume>
<issue>(4)</issue>, <fpage>601</fpage>&#x02013;<lpage>602</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1002/9781444354652</pub-id>
</mixed-citation></ref><ref id="B37"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Mancini</surname><given-names>M.</given-names></name><name><surname>Mazzoni</surname><given-names>L.</given-names></name><name><surname>Gagliardi</surname><given-names>F.</given-names></name><name><surname>Balducci</surname><given-names>F.</given-names></name><name><surname>Duca</surname><given-names>D.</given-names></name><name><surname>Toscano</surname><given-names>G.</given-names></name><etal/></person-group>. (<year>2020</year>). <article-title>Application of the non-destructive NIR technique for the evaluation of strawberry fruits quality parameters</article-title>. <source>Foods</source>
<volume>9</volume>, <elocation-id>441</elocation-id>. doi:&#x000a0;<pub-id pub-id-type="doi">10.3390/foods9040441</pub-id>
<pub-id pub-id-type="pmid">32268548</pub-id></mixed-citation></ref><ref id="B38"><mixed-citation publication-type="book">
<person-group person-group-type="author"><collab>Ministry of Foreign Affairs (CBI)</collab></person-group>. (<year>2021</year>a). <source>Entering the European market for fresh strawberries</source>. Available at: <uri xlink:href="https://www.cbi.eu/market-information/fresh-fruit-vegetables/strawberries/market-entry">https://www.cbi.eu/market-information/fresh-fruit-vegetables/strawberries/market-entry</uri>.</mixed-citation></ref><ref id="B39"><mixed-citation publication-type="book">
<person-group person-group-type="author"><collab>Ministry of Foreign Affairs (CBI)</collab></person-group>. (<year>2021</year>b). <source>The European market potential for strawberries</source>. Available at: <uri xlink:href="https://www.cbi.eu/market-information/fresh-fruit-vegetables/strawberries/market-potential">https://www.cbi.eu/market-information/fresh-fruit-vegetables/strawberries/market-potential</uri>.</mixed-citation></ref><ref id="B40"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Montero</surname><given-names>T. M.</given-names></name><name><surname>Moll&#x000e1;</surname><given-names>E. M.</given-names></name><name><surname>Esteban</surname><given-names>R. M.</given-names></name><name><surname>L&#x000f3;pez-Andr&#x000e9;u</surname><given-names>F. J.</given-names></name></person-group> (<year>1996</year>). <article-title>Quality attributes of strawberry during ripening</article-title>. <source>Scientia Hortic.</source>
<volume>65</volume>, <fpage>239</fpage>&#x02013;<lpage>250</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/0304-4238(96)00892-8</pub-id>
</mixed-citation></ref><ref id="B41"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Muangprathub</surname><given-names>J.</given-names></name><name><surname>Boonnam</surname><given-names>N.</given-names></name><name><surname>Kajornkasirat</surname><given-names>S.</given-names></name><name><surname>Lekbangpong</surname><given-names>N.</given-names></name><name><surname>Wanichsombat</surname><given-names>A.</given-names></name><name><surname>Nillaor</surname><given-names>P.</given-names></name></person-group> (<year>2019</year>). <article-title>IoT and agriculture data analysis for smart farm</article-title>. <source>Comput. Electron. Agric.</source>
<volume>156</volume>, <fpage>467</fpage>&#x02013;<lpage>474</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.compag.2018.12.011</pub-id>
</mixed-citation></ref><ref id="B42"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Munera</surname><given-names>S.</given-names></name><name><surname>Amigo</surname><given-names>J. M.</given-names></name><name><surname>Blasco</surname><given-names>J.</given-names></name><name><surname>Cubero</surname><given-names>S.</given-names></name><name><surname>Talens</surname><given-names>P.</given-names></name><name><surname>Aleixos</surname><given-names>N.</given-names></name></person-group> (<year>2017</year>). <article-title>Ripeness monitoring of two cultivars of nectarine using VIS-NIR hyperspectral reflectance imaging</article-title>. <source>J. Food Eng.</source>
<volume>214</volume>, <fpage>29</fpage>&#x02013;<lpage>39</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.jfoodeng.2017.06.031</pub-id>
</mixed-citation></ref><ref id="B43"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Nandi</surname><given-names>C. S.</given-names></name><name><surname>Tudu</surname><given-names>B.</given-names></name><name><surname>Koley</surname><given-names>C.</given-names></name></person-group> (<year>2016</year>). <article-title>A machine vision technique for grading of harvested mangoes based on maturity and quality</article-title>. <source>IEEE Sensors J.</source>
<volume>16</volume>, <fpage>6387</fpage>&#x02013;<lpage>6396</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1109/JSEN.2016</pub-id>
</mixed-citation></ref><ref id="B44"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Pedregosa</surname><given-names>F.</given-names></name><name><surname>Michel</surname><given-names>V.</given-names></name><name><surname>Grisel</surname><given-names>O.</given-names></name><name><surname>Blondel</surname><given-names>M.</given-names></name><name><surname>Prettenhofer</surname><given-names>P.</given-names></name><name><surname>Weiss</surname><given-names>R.</given-names></name><etal/></person-group>. (<year>2011</year>). <article-title>Scikit-learn: Machine learning in Python</article-title>. <source>J. Mach. Learn. Res.</source>
<volume>12</volume>, <fpage>2825</fpage>&#x02013;<lpage>2830</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.48550/arXiv.1201</pub-id>
</mixed-citation></ref><ref id="B45"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Porat</surname><given-names>R.</given-names></name><name><surname>Lichter</surname><given-names>A.</given-names></name><name><surname>Terry</surname><given-names>L. A.</given-names></name><name><surname>Harker</surname><given-names>R.</given-names></name><name><surname>Buzby</surname><given-names>J.</given-names></name></person-group> (<year>2018</year>). <article-title>Postharvest losses of fruit and vegetables during retail and in consumers&#x02019; homes: Quantifications, causes, and means of prevention</article-title>. <source>Postharvest Biol. Technol.</source>
<volume>139</volume>, <fpage>135</fpage>&#x02013;<lpage>149</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.postharvbio.2017.11.019</pub-id>
</mixed-citation></ref><ref id="B46"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Ramana</surname><given-names>K. V. R.</given-names></name><name><surname>Govindarajan</surname><given-names>V. S.</given-names></name><name><surname>Ranganna</surname><given-names>S.</given-names></name><name><surname>Kefford</surname><given-names>J. F.</given-names></name></person-group> (<year>1981</year>). <article-title>Citrus fruits &#x02014; varieties, chemistry, technology, and quality evaluation. part I: Varieties, production, handling, and storage</article-title>. <source>C R C Crit. Rev. Food Sci. Nutr.</source>
<volume>15</volume>, <fpage>353</fpage>&#x02013;<lpage>431</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1080/10408398109527321</pub-id>
</mixed-citation></ref><ref id="B47"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Samykanno</surname><given-names>K.</given-names></name><name><surname>Pang</surname><given-names>E.</given-names></name><name><surname>Marriott</surname><given-names>P. J.</given-names></name></person-group> (<year>2013</year>). <article-title>Genotypic and environmental effects on flavor attributes of &#x02018;Albion&#x02019; and &#x02018;Juliette&#x02019; strawberry fruits</article-title>. <source>Scientia Hortic.</source>
<volume>164</volume>, <fpage>633</fpage>&#x02013;<lpage>642</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.scienta.2013.09.001</pub-id>
</mixed-citation></ref><ref id="B48"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Shafizadeh-Moghadam</surname><given-names>H.</given-names></name></person-group> (<year>2021</year>). <article-title>Fully component selection: An efficient combination of feature selection and principal component analysis to increase model performance</article-title>. <source>Expert Syst. Appl.</source>
<volume>186</volume>, <elocation-id>115678</elocation-id>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.eswa.2021.115678</pub-id>
</mixed-citation></ref><ref id="B49"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Shao</surname><given-names>Y.</given-names></name><name><surname>Wang</surname><given-names>Y.</given-names></name><name><surname>Xuan</surname><given-names>G.</given-names></name><name><surname>Gao</surname><given-names>Z.</given-names></name><name><surname>Hu</surname><given-names>Z.</given-names></name><name><surname>Gao</surname><given-names>C.</given-names></name><etal/></person-group>. (<year>2021</year>). <article-title>Assessment of strawberry ripeness using hyperspectral imaging</article-title>. <source>Analytical Lett.</source>
<volume>54</volume>, <fpage>1547</fpage>&#x02013;<lpage>1560</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1080/00032719.2020.1812622</pub-id>
</mixed-citation></ref><ref id="B50"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Sim</surname><given-names>H. S.</given-names></name><name><surname>Kim</surname><given-names>D. S.</given-names></name><name><surname>Ahn</surname><given-names>M. G.</given-names></name><name><surname>Ahn</surname><given-names>S. R.</given-names></name><name><surname>Kim</surname><given-names>S. K.</given-names></name></person-group> (<year>2020</year>). <article-title>Prediction of strawberry growth and fruit yield based on environmental and growth data in a greenhouse for soil cultivation with applied autonomous facilities</article-title>. <source>Hortic. Sci. Technol.</source>
<volume>38</volume>, <fpage>840</fpage>&#x02013;<lpage>849</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.7235/HORT.20200076</pub-id>
</mixed-citation></ref><ref id="B51"><mixed-citation publication-type="book">
<person-group person-group-type="author"><name><surname>Simpson</surname><given-names>D.</given-names></name></person-group> (<year>2018</year>). &#x0201c;<article-title>The economic importance of strawberry crops</article-title>,&#x0201d; in <source>The genomes of rosaceous berries and their wild relatives</source>. Eds. <person-group person-group-type="editor"><name><surname>Hyt&#x000f6;nen</surname><given-names>T.</given-names></name><name><surname>Graham</surname><given-names>J.</given-names></name><name><surname>Harrison</surname><given-names>R.</given-names></name></person-group> (<publisher-loc>Cham</publisher-loc>: <publisher-name>Springer International Publishing</publisher-name>), <fpage>1</fpage>&#x02013;<lpage>7</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1007/978-3-319-76020-9_1</pub-id>
</mixed-citation></ref><ref id="B52"><mixed-citation publication-type="confproc">
<person-group person-group-type="author"><name><surname>Soosay</surname><given-names>C.</given-names></name><name><surname>Kannusam</surname><given-names>R.</given-names></name></person-group> (<year>2018</year>). &#x0201c;<article-title>Scope for industry 4.0 in agri-food supply chains</article-title>,&#x0201d; in <conf-name>Proceedings of the Hamburg International Conference of Logistics (HICL)</conf-name>, (<conf-loc>Hamburg, Germany</conf-loc>), <fpage>1</fpage>&#x02013;<lpage>22</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.15480/882.1784</pub-id>
</mixed-citation></ref><ref id="B53"><mixed-citation publication-type="book">
<person-group person-group-type="author"><name><surname>Stenmarck</surname><given-names>A.</given-names></name><name><surname>Jansen</surname><given-names>C.</given-names></name><name><surname>Quested</surname><given-names>T.</given-names></name><name><surname>Moates</surname><given-names>G.</given-names></name></person-group> (<year>2016</year>). <article-title>Estimates of European food waste levels</article-title>. <publisher-name>IVL Swedish Environmental Research Institute</publisher-name>.</mixed-citation></ref><ref id="B54"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Sun</surname><given-names>M.</given-names></name><name><surname>Zhang</surname><given-names>D.</given-names></name><name><surname>Liu</surname><given-names>L.</given-names></name><name><surname>Wang</surname><given-names>Z.</given-names></name></person-group> (<year>2017</year>). <article-title>How to predict the sugariness and hardness of melons: A near-infrared hyperspectral imaging method</article-title>. <source>Food Chem.</source>
<volume>218</volume>, <fpage>413</fpage>&#x02013;<lpage>421</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.foodchem.2016.09.023</pub-id>
<pub-id pub-id-type="pmid">27719929</pub-id></mixed-citation></ref><ref id="B55"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Terry</surname><given-names>L. A.</given-names></name><name><surname>Mena</surname><given-names>C.</given-names></name><name><surname>Williams</surname><given-names>A.</given-names></name><name><surname>Jenney</surname><given-names>N.</given-names></name><name><surname>Whitehead</surname><given-names>P.</given-names></name></person-group> (<year>2011</year>). <article-title>Fruit and vegetable resource maps: Mapping fruit and vegetable waste through the retail and wholesale supply chain</article-title>. <source>WRAP</source>, <fpage>RC008</fpage>.
</mixed-citation></ref><ref id="B56"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Vandendriessche</surname><given-names>T.</given-names></name><name><surname>Vermeir</surname><given-names>S.</given-names></name><name><surname>Mayayo Martinez</surname><given-names>C.</given-names></name><name><surname>Hendrickx</surname><given-names>Y.</given-names></name><name><surname>Lammertyn</surname><given-names>J.</given-names></name><name><surname>Nicola&#x000ef;</surname><given-names>B.</given-names></name><etal/></person-group>. (<year>2013</year>). <article-title>Effect of ripening and inter-cultivar differences on strawberry quality</article-title>. <source>LWT - Food Sci. Technol.</source>
<volume>52</volume>, <fpage>62</fpage>&#x02013;<lpage>70</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.lwt.2011.12.037</pub-id>
</mixed-citation></ref><ref id="B57"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Ventura</surname><given-names>M.</given-names></name><name><surname>de Jager</surname><given-names>A.</given-names></name><name><surname>de Putter</surname><given-names>H.</given-names></name><name><surname>Roelofs</surname><given-names>F. P.</given-names></name></person-group> (<year>1998</year>). <article-title>Non-destructive determination of soluble solids in apple fruit by near infrared spectroscopy (NIRS)</article-title>. <source>Postharvest Biol. Technol.</source>
<volume>14</volume>, <fpage>21</fpage>&#x02013;<lpage>27</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/S0925-5214(98)00030-1</pub-id>
</mixed-citation></ref><ref id="B58"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Verrelst</surname><given-names>J.</given-names></name><name><surname>Rivera</surname><given-names>J. P.</given-names></name><name><surname>Moreno</surname><given-names>J.</given-names></name><name><surname>Camps-Valls</surname><given-names>G.</given-names></name></person-group> (<year>2013</year>). <article-title>Gaussian Processes uncertainty estimates in experimental sentinel-2 LAI and leaf chlorophyll content retrieval</article-title>. <source>ISPRS J. Photogrammetry Remote Sens.</source>
<volume>86</volume>, <fpage>157</fpage>&#x02013;<lpage>167</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.isprsjprs.2013.09.012</pub-id>
</mixed-citation></ref><ref id="B59"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Weng</surname><given-names>S.</given-names></name><name><surname>Yu</surname><given-names>S.</given-names></name><name><surname>Guo</surname><given-names>B.</given-names></name><name><surname>Tang</surname><given-names>P.</given-names></name><name><surname>Liang</surname><given-names>D.</given-names></name></person-group> (<year>2020</year>). <article-title>Non-destructive detection of strawberry quality using multi-features of hyperspectral imaging and multivariate methods</article-title>. <source>Sensors</source>
<volume>20</volume>, <elocation-id>3074</elocation-id>. doi:&#x000a0;<pub-id pub-id-type="doi">10.3390/s20113074</pub-id>
<pub-id pub-id-type="pmid">32485900</pub-id></mixed-citation></ref><ref id="B60"><mixed-citation publication-type="book">
<person-group person-group-type="author"><name><surname>Wu</surname><given-names>Y.</given-names></name><name><surname>Kirillov</surname><given-names>A.</given-names></name><name><surname>Massa</surname><given-names>F.</given-names></name><name><surname>Lo</surname><given-names>W.-Y.</given-names></name><name><surname>Girshick</surname><given-names>R.</given-names></name></person-group> (<year>2019</year>). <source>Detectron2</source>. Available at: <uri xlink:href="https://github.com/facebookresearch/detectron2">https://github.com/facebookresearch/detectron2</uri>.</mixed-citation></ref><ref id="B61"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Xu</surname><given-names>L.</given-names></name><name><surname>Zhao</surname><given-names>Y.</given-names></name></person-group> (<year>2010</year>). <article-title>Automated strawberry grading system based on image processing</article-title>. <source>Comput. Electron. Agric.</source>
<volume>71</volume>, <fpage>S32</fpage>&#x02013;<lpage>S39</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.compag.2009.09.013</pub-id>
</mixed-citation></ref><ref id="B62"><mixed-citation publication-type="journal">
<person-group person-group-type="author"><name><surname>Zhang</surname><given-names>C.</given-names></name><name><surname>Guo</surname><given-names>C.</given-names></name><name><surname>Liu</surname><given-names>F.</given-names></name><name><surname>Kong</surname><given-names>W.</given-names></name><name><surname>He</surname><given-names>Y.</given-names></name><name><surname>Lou</surname><given-names>B.</given-names></name></person-group> (<year>2016</year>). <article-title>Hyperspectral imaging analysis for ripeness evaluation of strawberry with support vector machine</article-title>. <source>J. Food Eng.</source>
<volume>179</volume>, <fpage>11</fpage>&#x02013;<lpage>18</lpage>. doi:&#x000a0;<pub-id pub-id-type="doi">10.1016/j.jfoodeng.2016.01.002</pub-id>
</mixed-citation></ref></ref-list></back></article>