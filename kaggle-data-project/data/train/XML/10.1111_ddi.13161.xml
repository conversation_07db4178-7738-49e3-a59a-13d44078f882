<?xml version="1.0" encoding="UTF-8"?><component xmlns="http://www.wiley.com/namespaces/wiley" xmlns:wiley="http://www.wiley.com/namespaces/wiley/wiley" type="serialArticle" version="2.0" xml:id="ddi13161" xml:lang="en"><header><publicationMeta level="product"><doi origin="wiley" registered="yes">10.1111/(ISSN)1472-4642</doi><issn type="print">1366-9516</issn><issn type="electronic">1472-4642</issn><idGroup><id type="product" value="DDI"/></idGroup><titleGroup><title sort="DIVERSITY AND DISTRIBUTIONS" type="main">Diversity and Distributions</title><title type="short">Divers Distrib</title></titleGroup></publicationMeta><publicationMeta level="part" position="120"><doi origin="wiley">10.1111/ddi.v26.12</doi><copyright ownership="publisher">Copyright © 2020 John Wiley &amp; Sons Ltd</copyright><numberingGroup><numbering type="journalVolume" number="26">26</numbering><numbering type="journalIssue">12</numbering></numberingGroup><coverDate startDate="2020-12">December 2020</coverDate></publicationMeta><publicationMeta level="unit" position="110" status="forIssue" type="article" accessType="open"><doi>10.1111/ddi.13161</doi><idGroup><id type="unit" value="DDI13161"/></idGroup><countGroup><count number="13" type="pageTotal"/></countGroup><titleGroup><title type="articleCategory">BIODIVERSITY RESEARCH</title><title type="tocHeading1">BIODIVERSITY RESEARCH</title></titleGroup><copyright ownership="thirdParty">© 2020 The Authors. <i>Diversity and Distributions</i> published by John Wiley &amp; Sons Ltd.</copyright><legalStatement type="creativeCommonsBy"><p>This is an open access article under the terms of the <link href="http://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution</link> License, which permits use, distribution and reproduction in any medium, provided the original work is properly cited.</p></legalStatement><eventGroup><event date="2019-12-18" type="manuscriptReceived"/><event date="2020-08-19" type="manuscriptRevised"/><event date="2020-08-25" type="manuscriptAccepted"/><event agent="SPS" date="2020-09-10" type="xmlCreated"/><event type="firstOnline" date="2020-10-01"/><event type="publishedOnlineEarlyUnpaginated" date="2020-10-01"/><event type="xmlConverted" agent="Converter:WML3G_To_WML3G version:5.9.3 mode:FullText" date="2020-11-11"/></eventGroup><numberingGroup><numbering type="pageFirst">1767</numbering><numbering type="pageLast">1779</numbering></numberingGroup><correspondenceTo><lineatedText><line><b>Correspondence</b></line><line>Thomas A. Lake and David A. Moeller, Department of Plant and Microbial Biology, University of Minnesota, 1479 Gortner Ave, St. Paul, MN 55108, USA.</line><line>Emails: <email><EMAIL></email>; <email><EMAIL></email></line></lineatedText></correspondenceTo><selfCitationGroup><citation type="self" xml:id="ddi13161-cit-1001"><author><familyName>Lake</familyName> <givenNames>TA</givenNames></author>, <author><familyName>Briscoe Runquist</familyName> <givenNames>RD</givenNames></author>, <author><familyName>Moeller</familyName> <givenNames>DA</givenNames></author>. <articleTitle>Predicting range expansion of invasive species: Pitfalls and best practices for obtaining biologically realistic projections</articleTitle>. <journalTitle>Divers Distrib</journalTitle>. <pubYear year="2020">2020</pubYear>;<vol>26</vol>:<pageFirst>1767</pageFirst>–<pageLast>1779</pageLast>. <url href="https://doi.org/10.1111/ddi.13161">https://doi.org/10.1111/ddi.13161</url></citation></selfCitationGroup><objectNameGroup><objectName elementName="figure">FIGURE</objectName></objectNameGroup><linkGroup><link type="toTypesetVersion" href="file:ddi.13161.pdf"/><link type="toAuthorManuscriptVersion" href="file:ddi.13161.am.pdf"/></linkGroup></publicationMeta><contentMeta><countGroup><count number="4" type="figureTotal"/><count number="0" type="tableTotal"/><count number="8326" type="wordTotal"/></countGroup><titleGroup><title type="main">Predicting range expansion of invasive species: Pitfalls and best practices for obtaining biologically realistic projections</title><title type="shortAuthors">LAKE et al.</title></titleGroup><creators><creator affiliationRef="#ddi13161-aff-0001" corresponding="yes" creatorRole="author" xml:id="ddi13161-cr-0001"><idGroup><id type="orcid" value="https://orcid.org/0000-0002-8836-5164"/></idGroup><personName><givenNames>Thomas A.</givenNames> <familyName>Lake</familyName></personName><contactDetails><email><EMAIL></email></contactDetails></creator><creator affiliationRef="#ddi13161-aff-0001" creatorRole="author" xml:id="ddi13161-cr-0002"><idGroup><id type="orcid" value="https://orcid.org/0000-0001-7160-9110"/></idGroup><personName><givenNames>Ryan D.</givenNames> <familyName>Briscoe Runquist</familyName></personName></creator><creator affiliationRef="#ddi13161-aff-0001" corresponding="yes" creatorRole="author" xml:id="ddi13161-cr-0003"><idGroup><id type="orcid" value="https://orcid.org/0000-0002-6202-9912"/></idGroup><personName><givenNames>David A.</givenNames> <familyName>Moeller</familyName></personName><contactDetails><email><EMAIL></email></contactDetails></creator><creator creatorRole="editor" xml:id="ddi13161-cr-0004"><personName><givenNames>Céline</givenNames> <familyName>Bellard</familyName></personName></creator></creators><affiliationGroup><affiliation countryCode="US" type="organization" xml:id="ddi13161-aff-0001"><orgDiv>Department of Plant and Microbial Biology</orgDiv> <orgName>University of Minnesota</orgName> <address><city>St. Paul</city> <countryPart>MN</countryPart> <country>USA</country></address></affiliation></affiliationGroup><keywordGroup xml:lang="en"><keyword xml:id="ddi13161-kwd-0001">Boyce index</keyword><keyword xml:id="ddi13161-kwd-0002">distribution shifts</keyword><keyword xml:id="ddi13161-kwd-0003">environmental niche model</keyword><keyword xml:id="ddi13161-kwd-0004">exotic species</keyword><keyword xml:id="ddi13161-kwd-0005">gaussian kernel distributions</keyword><keyword xml:id="ddi13161-kwd-0006">Maxent</keyword><keyword xml:id="ddi13161-kwd-0007">range expansion</keyword><keyword xml:id="ddi13161-kwd-0008">sensitivity</keyword><keyword xml:id="ddi13161-kwd-0009">spatial bias</keyword><keyword xml:id="ddi13161-kwd-0010">transferability</keyword></keywordGroup><fundingInfo><fundingAgency fundRefName="Legislative-Citizen Commission on Minnesota Resources" funderDoi="10.13039/100013423">Legislative‐Citizen Commission on Minnesota Resources</fundingAgency><fundingNumber>CON000000051140</fundingNumber></fundingInfo><supportingInformation><supportingInfoItem xml:id="ddi13161-sup-0001"><mediaResource alt="supporting" mimeType="application/msword" href="suppl/ddi13161-sup-0001-Supinfo.docx"/><caption>Supplementary Material</caption></supportingInfoItem></supportingInformation><abstractGroup><abstract type="main" xml:id="ddi13161-abs-0001" xml:lang="en"><title type="main">Abstract</title><section xml:id="ddi13161-sec-0001"><title type="main">Aim</title><p>Species distribution models (SDMs) are widely used to forecast potential range expansion of invasive species. However, invasive species occurrence datasets often have spatial biases that may violate key SDM assumptions. In this study, we examined alternative methods of spatial bias correction and multiple methods for model evaluation for seven invasive plant species.</p></section><section xml:id="ddi13161-sec-0002"><title type="main">Location</title><p>North America.</p></section><section xml:id="ddi13161-sec-0003"><title type="main">Taxon</title><p>Common Tansy (<i>Tanacetum vulgare</i>), Wild Parsnip (<i>Pastinaca sativa</i>), Leafy Spurge (<i>Euphorbia virgata</i>), Common Teasel (<i>Dipsacus fullonum</i>), Brown Knapweed (<i>Centaurea jacea</i>), Black Swallowwort (<i>Vincetoxicum nigrum</i>) and Dalmatian Toadflax (<i>Linaria dalmatica</i>).</p></section><section xml:id="ddi13161-sec-0004"><title type="main">Methods</title><p>We employed bias‐correction measures for both occurrence sampling and background sampling inputs in a factorial design for Maxent resulting in six potential models for each species. We evaluated our models for complexity, model fit and using commonly employed evaluation metrics: AUC, partial AUC, the continuous Boyce index and sensitivity. We then developed a structured process for model selection.</p></section><section xml:id="ddi13161-sec-0005"><title type="main">Results</title><p>Models developed without occurrence or background bias correction often were overly complex and did not transfer well to expanding range fronts. Conversely, models that employed occurrence and/or background bias‐correction measures were less complex, had better AICc scores and had greater projection into incipient areas. These simpler models were also more likely to be selected when evaluated using a process that integrated multiple evaluation metrics. We found that invasion history (e.g. established versus incipient) was associated with the effectiveness of spatial bias correction techniques.</p></section><section xml:id="ddi13161-sec-0006"><title type="main">Main Conclusions</title><p>While challenges exist in building climate‐based correlative species distribution models for invasive species, we found that methods relying on maximizing AUC performed poorly for invasive species. We advocate for the use of multiple and diverse metrics for model evaluation. Users of species distribution models need to incorporate explicit consideration of model discrimination, model fit and model complexity into their decision‐making processes if they are to build biologically realistic models.</p></section></abstract></abstractGroup></contentMeta></header><body sectionsNumbered="yes" xml:id="ddi13161-body-0001"><section xml:id="ddi13161-sec-0007"><title type="main">INTRODUCTION</title><p>The geographic ranges of invasive species are shifting, both because they are often in disequilibrium with the environment (i.e. limited range filling) and because climate change has altered the distribution of suitable habitat. Early detection and prevention increasingly rely on species distribution models (SDMs) that forecast suitable habitats where surveillance and eradication efforts can be focused (Lodge et al., <link href="#ddi13161-bib-0053">2016</link>; Marbuah, Gren, &amp; McKie, <link href="#ddi13161-bib-0056">2014</link>). However, invasive species pose unique challenges that stymie the model building, evaluation and selection process, as invasive species commonly violate underlying model assumptions and contain multiple sources of spatial bias (Elith, Kearney, &amp; Phillips, <link href="#ddi13161-bib-0021">2010</link>). Although a variety of approaches have been used to correct these biases, it is poorly understood how alternative methods perform on a common set of invasive species.</p><p>Building SDMs of invasive species have been challenging for two main reasons. First, invasive species inherently violate the model assumption that species are in equilibrium with the environment and occupy all regions suitable for their persistence. Second, modelled relationships are correlative and only hold over the range of climate variables included in the model‐building process; therefore, projecting into uninvaded or incipient regions outside of the current invasive range may be problematic (Elith et al., <link href="#ddi13161-bib-0021">2010</link>; Elith &amp; Leathwick, <link href="#ddi13161-bib-0022">2009</link>; Hastie &amp; Fithian, <link href="#ddi13161-bib-0035">2013</link>; Lodge et al., <link href="#ddi13161-bib-0053">2016</link>). Additionally, niche shifts are common between native and invaded ranges, which may lead to differences in the underlying environmental relationships in different regions of the global range (Le Maitre, Thuiller, &amp; Schonegevel, <link href="#ddi13161-bib-0049">2008</link>). Many researchers have attempted to address model violations (Broennimann et al., <link href="#ddi13161-bib-0014">2007</link>; Fourcade, Besnard, &amp; Secondi, <link href="#ddi13161-bib-0029">2018</link>; Mainali et al., <link href="#ddi13161-bib-0055">2015</link>; Petitpierre et al., <link href="#ddi13161-bib-0068">2012</link>). Nevertheless, these methods have not completely addressed spatial biases in invasive species SDMs.</p><p>Uneven species sampling is a common source of bias that can impede accurate modelling (Bradley, Blumenthal, Wilcove, &amp; Ziska, <link href="#ddi13161-bib-0011">2010</link>; Elith &amp; Leathwick, <link href="#ddi13161-bib-0022">2009</link>; Fourcade, Engler, Rödder, &amp; Secondi, <link href="#ddi13161-bib-0030">2014</link>; Merckx, Steyaert, Vanreusel, Vincx, &amp; Vanaverbeke, <link href="#ddi13161-bib-0057">2011</link>). Sampling bias can arise if species are challenging to detect or not a priority for detection, which results in occupied regions having no documented occurrence records. In addition, spatial biases unique to invasive species may further complicate the modelling approach, such as recurrent eradication efforts, highly uneven sampling programs across political units, introduction into unsuitable habitats (Elith et al., <link href="#ddi13161-bib-0021">2010</link>; Lodge et al., <link href="#ddi13161-bib-0053">2016</link>) and introduction via stochastic or anthropogenic long‐distance dispersal events (e.g. ship ballast) (von der Lippe &amp; Kowarik, <link href="#ddi13161-bib-0083">2007</link>). These biases manifest in oversampling in the established portion of the invaded range and undersampling in regions where the invasive species is incipient, leading to uneven prevalence across the invaded range. Identifying the sources of spatial bias in invasive species, and understanding how they interact with different modelling methods, may help to build more robust and reliable SDMs.</p><p>Among the many tools available for building SDMs, Maxent is the most widely used, as it is found to perform well for most species (Hao, Elith, Guillera‐Arroita, &amp; Lahoz‐Monfort, <link href="#ddi13161-bib-0034">2019</link>). Maxent contrasts environments of occurrence points with those sampled from background locations to determine which combinations of variables best predict occurrences (Elith et al., <link href="#ddi13161-bib-0021">2010</link>, <link href="#ddi13161-bib-0023">2011</link>; Merow, Smith, &amp; Silander, <link href="#ddi13161-bib-0058">2013</link>). One key assumption in SDMs is that sample prevalence (the frequency of sampled sites in the total study area) accurately represents species prevalence (the frequency of species over the total study area). Maxent uses a constant species prevalence value across the species’ range, despite the fact that many species vary in prevalence geographically or have been documented with differing levels of sampling effort in different regions. While a number of methods have been developed to mitigate the potential effects of spatially biased occurrence records, two methods have shown promise: downsampling occurrence points and modifying the sampling distribution of background points (Kramer‐Schadt et al., <link href="#ddi13161-bib-0047">2013</link>; Phillips &amp; Dudík, <link href="#ddi13161-bib-0070">2008</link>).</p><p>Downsampling addresses spatial bias by thinning dense clusters of occurrences where oversampling may have occurred (Fourcade et al., <link href="#ddi13161-bib-0030">2014</link>). In model building, this effectively reduces the environmental importance placed on oversampled regions (Fourcade et al., <link href="#ddi13161-bib-0030">2014</link>). Alternatively, one can modify the distribution of background point sampling to match the spatial bias that exists in the occurrence points (Barbet‐Massin, Jiguet, Hélène Albert, &amp; Thuiller, <link href="#ddi13161-bib-0006">2012</link>; Kramer‐Schadt et al., <link href="#ddi13161-bib-0047">2013</link>; Phillips &amp; Dudík, <link href="#ddi13161-bib-0070">2008</link>). Background sampling with a Gaussian Kernel Density (GKD) distribution can be used to sample a greater proportion of background points in areas of highly sampled occurrence points (Elith et al., <link href="#ddi13161-bib-0021">2010</link>). GKD sampling draws more background points in areas with more occurrences, which can enable greater discriminating power in areas that are heavily sampled.</p><p>Model discrimination metrics, such as those relying on a confusion matrix, are commonly used to evaluate model performance. The area under the curve (AUC) is most frequently used due to its threshold‐independence, but it has been criticized due to its equal weighting of commission and omission errors and dependence on prevalence (Leroy et al., <link href="#ddi13161-bib-0051">2018</link>). Alternative metrics such as the partial AUC and sensitivity (TP/TP + FN) have been proposed in order to mitigate problems caused by assuming background points in presence‐only modelling are errors of commission (Allouche, Tsoar, &amp; Kadmon, <link href="#ddi13161-bib-0003">2006</link>; Boyce, Vernier, Nielsen, &amp; Schmiegelow, <link href="#ddi13161-bib-0010">2002</link>; Jiménez‐Valverde et al., <link href="#ddi13161-bib-0044">2011</link>; Lobo, Jiménez‐Valverde, &amp; Real, <link href="#ddi13161-bib-0052">2008</link>; Merckx et al., <link href="#ddi13161-bib-0057">2011</link>; Radosavljevic &amp; Anderson, <link href="#ddi13161-bib-0075">2014</link>). Others have suggested that the Continuous Boyce Index (CBI) may be useful as a spatially explicit metric to account for correlation between a models’ prediction and occurrences (Boyce et al., <link href="#ddi13161-bib-0010">2002</link>; Hirzel, Le Lay, Helfer, Randin, &amp; Guisan, <link href="#ddi13161-bib-0039">2006</link>). Last, model complexity should be considered in the model evaluation process; complex models tend to be overfit (i.e. high performance on training datasets, low performance on testing datasets) and often lack generalization (Yates et al., <link href="#ddi13161-bib-0090">2018</link>; Brun, Thuiller, Chauvier, Pellissier &amp; West et al., <link href="#ddi13161-bib-0015">2019</link>). Model complexity may be assessed by recording the number of model parameters or using information criteria, such as AICc, which balances model gains with penalties for added parameters (Akaike, <link href="#ddi13161-bib-0001">1974</link>).</p><p>For invasive species, model selection needs to explicitly consider transferability because one goal of invasive species SDMs is to transfer predictions into incipient or unoccupied habitats (Morey &amp; Venette, <link href="#ddi13161-bib-0060">2020</link>; Wenger &amp; Olden, <link href="#ddi13161-bib-0086">2012</link>; Yates et al., <link href="#ddi13161-bib-0090">2018</link>). One approach partitions cross‐validation datasets using a spatially explicit block method, where the modelled area is divided into geographic regions that contain roughly the same number of occurrences (Wenger &amp; Olden, <link href="#ddi13161-bib-0086">2012</link>; Yates et al., <link href="#ddi13161-bib-0090">2018</link>). Modellers can then estimate performance based on spatially explicit cross‐validation and the overall model performance is a useful indicator of how well SDMs transfer between geographic regions (Finnoff, Shogren, Leung, &amp; Lodge, <link href="#ddi13161-bib-0028">2007</link>; Lodge et al., <link href="#ddi13161-bib-0053">2016</link>; Yates et al., <link href="#ddi13161-bib-0090">2018</link>). Additionally, the coefficient of variation (CoV) among cross‐validated data partitions may provide insight into how well SDMs generalize among geographic regions, where greater CoV indicates that model performance varies depending on the geographic region.</p><p>For this study, we developed SDMs for seven invasive species with different invasion histories. We refer to these models as SDMs throughout the text, as we use climate variables to determine potential areas of occupancy, but do not attempt to model the niche globally. Our seven invasive species are of special interest in prevention and eradication efforts in the Upper Midwest of the United States. Our seven species represent three different invasion histories: (a) long established, widespread species that are likely in equilibrium with the environment, (b) incipient species with recent range expansion into the Upper Midwest that are diffusely distributed across the continental United States, and (c) incipient species with recent range expansion into the Upper Midwest that have irregular and clustered distributions. Using these species, we explored differences in model performance, model variability and model complexity and examined if these model characteristics interacted with different invasion history (e.g. established versus incipient invasions). The goal of the study was to determine how to build and select SDMs to make biologically sound predictions about habitat suitability that can be used by land managers for decision‐making, especially for regions that may be challenging to model but are of utmost interest for surveillance and eradication efforts. We used a factorial design to systematically explore three general themes that affect the model building and selection process: (a) how did methods that reduce the effects of spatial bias affect measures of model complexity and amount of predicted suitable habitat? (b) how were model performance and variability affected by differences in model inputs? and (c) how did model selection affect projections of habitat suitability?</p></section><section type="methods" xml:id="ddi13161-sec-0008"><title type="main">METHODS</title><section xml:id="ddi13161-sec-0009"><title type="main">Study species</title><p>We modelled seven terrestrial invasive plant species in North America with different invasion histories. Common Tansy (<i>Tanacetum vulgare</i> L.), Wild Parsnip (<i>Pastinaca sativa</i> L.) and Leafy Spurge (<i>Euphorbia virgata</i> L.) are historically established and common species throughout North America and the Upper Midwest. Common Teasel (<i>Dipsacus fullonum</i> L.) and Brown Knapweed (<i>Centaurea jacea</i> L.) are diffuse throughout North America but incipient to the Upper Midwest. Black Swallowwort (<i>Vincetoxicum nigrum</i> (L.) Moench 1802) and Dalmatian Toadflax (<i>Linaria dalmatica</i> L.) have clustered distributions throughout North America and are likewise incipient to the Upper Midwest. For detailed information on the species’ native and invasive distributions, ecology and invasion history, see Table <link href="#ddi13161-sup-0001">S1</link>. All species presented in this paper are native to Europe and portions of western Asia, are considered invasive across the continental United States and are of special concern in the Upper Midwest (Minnesota, Wisconsin, Illinois, Iowa, North Dakota and South Dakota).</p></section><section xml:id="ddi13161-sec-0010"><title type="main">Data sources</title><section xml:id="ddi13161-sec-0011"><title type="main">Spatial data</title><p>We downloaded occurrence records from two publicly available databases: (a) Global Biodiversity Information Facility (GBIF; <url href="http://www.gbif.org">www.gbif.org</url>), and (b) Early Detection &amp; Distribution Mapping System (EDDMapS; <url href="http://www.eddmaps.org">www.eddmaps.org</url>) (Table <link href="#ddi13161-sup-0001">S2</link>). We processed occurrence records to remove duplicate, erroneous and/or imprecise (e.g. gridded) coordinates. Data points with less than 0.1 decimal degrees of spatial precision were removed. Spatial analyses occurred in the R statistical environment (v3.5.1; R Core Team, <link href="#ddi13161-bib-0074">2018</link>) using the Geospatial Data Abstraction Library (“rgdal”; Bivand, Keitt, &amp; Rowlingson, <link href="#ddi13161-bib-0008">2019</link>), Geometry Engine Open Source (“rgeos”; Bivand &amp; Rundel, <link href="#ddi13161-bib-0009">2015</link>), Spatial Points (“sp”; Pebesma &amp; Bivand., <link href="#ddi13161-bib-0064">2005</link>) and Raster packages (“raster”; Hijmans &amp; vanEtten, <link href="#ddi13161-bib-0038">2012</link>).</p></section><section xml:id="ddi13161-sec-0012"><title type="main">Spatial data processing</title><p>Datasets were trimmed to include only the invaded range of North America. We used occurrence records from only the invaded range to capture unique niche shifts that may have occurred during invasion (Broennimann et al., <link href="#ddi13161-bib-0014">2007</link>). Additionally, we produced a set of models containing the occurrences from both the native and invaded range, the results of which did not appreciably change modelling outcomes (See Supplementary Materials). To reduce pseudoreplication among the invaded‐only occurrence records, we resampled occurrences to a baseline resolution of 1 km, the resolution of the climate data (<i>hereafter</i>: filtered datasets).</p></section><section xml:id="ddi13161-sec-0013"><title type="main">Environmental data</title><p>We used bioclimatic variables available from WorldClim [30 arcsecond (~1 km) resolution; <url href="http://worldclim.org/version2">http://worldclim.org/version2</url>] because they provide biologically meaningful axes of climate variation and are commonly used in SDMs (Table <link href="#ddi13161-sup-0001">S3</link>; Fick &amp; Hijmans, <link href="#ddi13161-bib-0027">2017</link>; Hijmans, Cameron, Parra, Jones, &amp; Jarvis, <link href="#ddi13161-bib-0037">2005</link>). We selected 3–5 bioclimatic variables per species that have been shown to encapsulate relevant axes of climate variation that affect plant establishment and growth as well as enhance model transferability for invasive species (Petitpierre, Broenniman, Kueffer, Daehler, &amp; Guisan, <link href="#ddi13161-bib-0067">2017</link>). We further eliminated variables that had &gt;0.7 correlation across the modelled environmental space (Table <link href="#ddi13161-sup-0001">S3</link>).</p></section></section><section xml:id="ddi13161-sec-0014"><title type="main">Species distribution modelling</title><section xml:id="ddi13161-sec-0015"><title type="main">Occurrence record sampling and background point selection</title><p>For each species, we developed two occurrence datasets and three background datasets as inputs for model construction in a 2 × 3 factorial design. For the occurrence datasets, our filtered datasets were downsampled to one occurrence per 1 km raster grid cell to match the resolution of the environmental data. We generated an additional dataset downsampled to one occurrence per 20 km raster grid cell, which effectively reduced prevalence in very densely sampled clusters without removing overall patterns of landscape occurrence. We then generated three different forms of background point selection: randomly selected background points and two different Gaussian kernel density distributions (GKD). For the random background points, we used a polygon object with 200 km buffers centred on occurrence points and dissolving overlaps. We then selected the default 10,000 background points from the polygon. GKD background point selection aligns the background point density more closely to the density of occurrence points, so we generated two GKD background point datasets for both occurrence datasets. The probability of choosing a background point was based on a surface generated from summing Gaussian kernels (with dispersion parameter λ) at each occurrence point. For the GKD background points, we varied the dispersion parameter λ to create both highly clustered background (λ = 1) and moderately clustered background (λ = 3) points. All manipulations were conducted using ArcGIS Pro 2.3 (ESRI, <link href="#ddi13161-bib-0026">2019</link>).</p></section><section xml:id="ddi13161-sec-0016"><title type="main">Model construction</title><p>We constructed SDMs using Maxent v. 3.4.1 (Phillips, Anderson, Dudík, Schapire, &amp; Blair, <link href="#ddi13161-bib-0069">2017</link>) implemented through the ENMEval package (Muscarella et al., <link href="#ddi13161-bib-0061">2014</link>). We developed six Maxent models per species using each combination of downsampled occurrence data (1 and 20 km) and GKD background point selection (random; λ = 1: highly clustered; λ = 3: moderately clustered). For all models, we set the regularization beta multiplier = 2 and allowed for all feature classes. In preliminary model building, we tested four (<i>n</i> = 4) beta multipliers from 1.0 to 4.0 with 1.0 increments. Differences among beta multipliers were minimal. Models were projected in North America using the “project” function in package “rmaxent” (Baumgartner &amp; Wilson, <link href="#ddi13161-bib-0007">2019</link>) and visualized using the “rasterVis v0.45” package (Lamigueriro &amp; Hijmans, <link href="#ddi13161-bib-0048">2018</link>).</p></section><section xml:id="ddi13161-sec-0017"><title type="main">Model evaluation</title><p>We evaluated models by partitioning occurrences into four geographically structured blocks containing an approximately equal number of occurrence records. We assessed performance with fourfold cross‐validation using three blocks as training data and retaining one block for testing. For each model, we calculated AUC (Area under the curve), the partial AUC (pAUC) ratio, the Continuous Boyce Index (CBI) and sensitivity (True Positives/ (True Positives + False Negatives)). AUC characterizes model prediction and discrimination ability relative to random chance (Phillips &amp; Dudík, <link href="#ddi13161-bib-0070">2008</link>), with values ranging from 0 to 1 where higher values indicating greater model performance and 0.5 is indicative of model predictive discrimination that is approximately random. The use of AUC has received criticism due to the equal weighting of omission and commission error and dependence on prevalence (Lobo et al., <link href="#ddi13161-bib-0052">2008</link>). As commission error and true negatives are difficult to estimate in presence‐only SDMs and may be of particular issue for invasive species, we explored model evaluation metrics that less reliant on commission error. We calculated the pAUC as the area under the portion of the curve (Peterson, Papeş, &amp; Soberón, <link href="#ddi13161-bib-0066">2008</link>) where omission errors are less than or equal to 10%. To assess whether pAUC was larger and significantly different from random expectation, we calculated the pAUC ratio, defined as the pAUC divided by the area under the 1:1 line that indicates random expectation for the same portion of the AUC curve. The pAUC ratio ranges from 0 to 2 and has a random expectation of 1 with values above 1 indicating better than random performance (Escobar, Qiao, Cabello, &amp; Townsend Peterson, <link href="#ddi13161-bib-0025">2018</link>). pAUC ratios were calculated and their significance assessed using the ‘pROC’ function in the package “NicheToolbox” (Osorio‐Olivera, <link href="#ddi13161-bib-0063">2016</link>) with 50% random withholding and 500 bootstrap iterations. Additionally, we calculated CBI, which measures the correlation between occurrence probability in the data and predicted probability (Boyce et al., <link href="#ddi13161-bib-0010">2002</link>; Hirzel et al., <link href="#ddi13161-bib-0039">2006</link>). CBI ranges from −1 to 1, where positive values indicate an accurate model that correlates with the data, zero indicates no difference from random, and negative values indicate a negative correlation. Sensitivity quantifies the correctly predicted positive fraction of occurrences and was evaluated at the threshold of 0.5 for all models.</p></section><section xml:id="ddi13161-sec-0018"><title type="main">Model predicted area</title><p>To quantify how occurrence point downsampling and GKD background points affected the quantity of predicted suitable habitat, we computed the area of suitable habitat across North America using thresholds varying from 0.01 to 0.99 at 0.01 intervals. We then plotted the relationship between proportion of suitable habitat against threshold value for each projection (<i>hereafter:</i> “threshold‐area curves”). We tested for differences in the effects of model input modifications on projected suitable area at a 50% threshold (i.e. total area of predicted habitat suitability at or above 0.5; <i>N</i> = 36 models) with ANOVA where species grouping (e.g. common and historically established, incipient and diffuse, and incipient and clustered), occurrence dataset (filtered or downsampled), and background dataset (random, moderately clustered or highly clustered) and all second‐order interaction terms were included as fixed, independent variables and area predicted to be suitable habitat at a 50% probability of occurrence threshold as the dependent variable.</p></section><section xml:id="ddi13161-sec-0019"><title type="main">Assessing model performance, variability and complexity</title><p>For each model, we differentiated between a model's performance (mean cross‐validated AUC, mean cross‐validated pAUC ratio, mean cross‐validated sensitivity and mean cross‐validated CBI), variability among the four cross‐validation datasets (AUC coefficient of variation and sensitivity coefficient of variation), and complexity (number of model parameters and AICc—although we note that AICc does not strictly assess only model complexity and instead balances model complexity with model fit). For performance, a higher mean cross‐validated statistic indicated greater performance. We also calculated the coefficient of variation for AUC and sensitivity among the four cross‐validation datasets, where models with greater variability exhibited a higher coefficient of variation among data partitions. To quantify complexity, we report the number of model parameters and AICc values. Maxent model parameters are transformations of environmental covariates that allow complex relationships to be modelled (Elith et al., <link href="#ddi13161-bib-0021">2010</link>; Merow et al., <link href="#ddi13161-bib-0058">2013</link>). More complex models had a greater number of parameters and generally higher AICc scores. We tested for significant differences in model performance (mean cross‐validated AUC, mean cross‐validated pAUC ratio, mean cross‐validated sensitivity and mean cross‐validated CBI), variability (AUC coefficient of variation and sensitivity coefficient of variation) and complexity (number of parameters) using ANOVA by including species, occurrence dataset (1 or 20 km) and background sampling (λ = 0: random; λ = 1: highly clustered; λ = 3: moderately clustered) and all second‐order interactions as independent variables. We corrected for multiple tests using a sequential Bonferroni (Holm) correction.</p></section><section xml:id="ddi13161-sec-0020"><title type="main">Structured decision process for model selection</title><p>We designed a model selection process based on measures of model performance, variability and complexity (Figure <link href="#ddi13161-fig-0001">1</link>). We used this process to assess each of the six models per species and choose the model that optimally balances performance, variability and complexity. For each performance, variability and complexity score, we ranked each model and assigned it a scalar value from one to six, where higher values indicate better evaluation metrics. For example, one model per species with the highest mean cross‐validated AUC received the highest score of six, and models with lower AUC performance received descending scores. Similarly, models with the lowest coefficient of variation received the highest score of six. For model complexity, we assigned two scores, where models with the fewest parameters received a score of six, and the lowest AICc score also received the highest score of six. Once all model metrics were scored, the model with the highest overall summed score was selected as the optimum model among the six for each species.</p><figure xml:id="ddi13161-fig-0001"><label>1</label><title type="figureName">FIGURE</title><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:13669516:media:ddi13161:ddi13161-fig-0001"/><mediaResource alt="image" href="graphic/ddi13161-fig-0001-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Model construction, evaluation and structured decision‐making process flowchart. A total of 36 models for six species were constructed and evaluated, resulting in one selected model per species</caption></figure></section></section></section><section type="results" xml:id="ddi13161-sec-0021"><title type="main">RESULTS</title><section xml:id="ddi13161-sec-0022"><title type="main">Theme 1: How did methods that reduce the effects of spatial bias affect measures of model complexity and amount of predicted suitable habitat?</title><section xml:id="ddi13161-sec-0023"><title type="main">Model complexity</title><p>Models constructed with modified background sampling and/or occurrence data downsampling were less complex. This was most apparent for number of model features; across all species, models with GKD (clustered) background sampling had fewer features relative to random background sampling (<i>F<sub>df</sub></i><sub>=2,25</sub> = 16.81, <i>p</i> &lt; .001; Figure <link href="#ddi13161-fig-0002">2</link>; Table <link href="#ddi13161-sup-0001">S9</link>), suggesting the latter are highly parameterized and more complex. Similarly, models with 20 km occurrence downsampling had fewer model features (<i>F<sub>df</sub></i><sub>=2,12</sub> = 71.07, <i>p</i> &lt; .001; Figure <link href="#ddi13161-fig-0002">2</link>; Table <link href="#ddi13161-sup-0001">S9</link>). Additionally, these models had lower AICc scores (Table <link href="#ddi13161-sup-0001">S15</link>), suggesting models with 20 km occurrence downsampling are more likely to balance model fit with model complexity.</p><figure xml:id="ddi13161-fig-0002"><label>2</label><title type="figureName">FIGURE</title><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:13669516:media:ddi13161:ddi13161-fig-0002"/><mediaResource alt="image" href="graphic/ddi13161-fig-0002-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Interaction plot of occurrence downsampling by background point clustering effects for number of model parameters. Models were constructed with either 1 km filtered datasets (circles) or 20 km downsampled datasets (triangles) and either random (GKD 0, blue), highly clustered (GKD 1, grey) or moderately clustered (GKD 3, green) background sampling</caption></figure></section><section xml:id="ddi13161-sec-0024"><title type="main">Response curves</title><p>Upon visual inspection, model response curves for all species were smoother in models constructed with occurrence downsampling and GKD background sampling (Figures <link href="#ddi13161-sup-0001">S8–S14</link>).</p></section><section xml:id="ddi13161-sec-0025"><title type="main">Threshold‐area curves</title><p>Regardless of the occurrence dataset used (1 km—filtered; 20 km—downsampled), models using random background sampling predicted substantially less suitable habitat than models that used background points sampled using a GKD of 1 or 3 (highly or moderately clustered sampling) at nearly every possible threshold value (Figure <link href="#ddi13161-fig-0003">3</link>). Among the differing invasion histories, there were distinct patterns in their threshold‐area curves. For the common and historically established species (Common Tansy, Wild Parsnip, and Leafy Spurge), downsampling (to 20 km) led to substantially greater predicted habitat suitability across all thresholds, whereas GKD background sampling had minimal effect. For incipient and diffuse species (Brown Knapweed and Common Teasel), models with occurrence downsampling or GKD background sampling led to similar increases in predicted habitat suitability across all thresholds. For incipient and clustered species (Black Swallowwort and Dalmatian Toadflax), the effect was opposite to the common and historically established species: GKD background sampling led to greater predicted habitat suitability but occurrence downsampling had minimal effects (Figure <link href="#ddi13161-fig-0003">3</link>).</p><figure xml:id="ddi13161-fig-0003"><label>3</label><title type="figureName">FIGURE</title><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:13669516:media:ddi13161:ddi13161-fig-0003"/><mediaResource alt="image" href="graphic/ddi13161-fig-0003-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Species threshold‐area curves and proportion of predicted suitable habitat at 50% threshold. For each species and species grouping: (a) common and historically established species including (b) Common Tansy, (c) Wild Parsnip, (d) Leafy Spurge, (e) incipient and diffuse species including (f) Common Teasel, (g) Brown Knapweed, and incipient and clustered species including (i) Black Swallowwort, (j) Dalmatian Toadflax. The curves depict the relationships between proportion of predicted suitable habitat (<i>y</i>‐axis) and the threshold used to determine suitability (<i>x</i>‐axis). Each threshold‐area curve panel has six curves that depend on the combination of occurrence dataset used (1 km filtered dataset = solid lines; 20 km downsampled dataset = dashed lines) and background sampling (random = blue; highly clustered/GKD 1 = grey; moderately clustered/GKD 3 = green). In the left column, the interaction between GKD and downsampling is shown at the 50% threshold; error bars represent ± 1 <i>SE</i></caption></figure><p>When suitable habitat was assessed using a threshold probability of occurrence of 50% (i.e. all area with predicted probability of occurrence greater than or equal to 50%), models using GKD background sampling (highly or moderately clustered) had greater predicted suitable habitat area relative to models built using random background sampling (<i>F<sub>df</sub></i><sub>=2,28</sub> = 6.58, <i>p</i> = .005; Table <link href="#ddi13161-sup-0001">S10</link>). Further, using that same threshold, models using occurrence downsampling (to 20 km) had significantly greater predicted suitable habitat area relative to models using the filtered (downsampling only to 1 km) occurrence datasets (<i>F<sub>df</sub></i><sub>=2,28</sub> = 10.49, <i>p</i> = .003; Table <link href="#ddi13161-sup-0001">S10</link>).</p></section></section><section xml:id="ddi13161-sec-0026"><title type="main">Theme 2: How were model performance and model variability affected by differences in model inputs?</title><section xml:id="ddi13161-sec-0027"><title type="main">Performance</title><p>Downsampling occurrence points to 20 km increased model performance when measured by sensitivity and pAUC ratio (sensitivity: <i>F</i><sub>1,12</sub> = 5.38, <i>p</i> = .039; pAUC ratio: <i>F</i><sub>1,12</sub> = 91.7, <i>p</i> &lt; .001; Tables <link href="#ddi13161-sup-0001">S11 and S12</link>). Conversely, AUC scores were greater for 1 km filtered datasets (AUC: <i>F</i><sub>1,12</sub> = 79.9, <i>p</i> &lt; .001; Tables <link href="#ddi13161-sup-0001">S11 and S12</link>). The effects of background sampling design were more consistent across performance metrics. On average, AUC, CBI and sensitivity were higher for models that used random background sampling relative to models using either GKD background sampling (AUC: <i>F<sub>df</sub></i><sub>=2,12</sub> = 218.64, <i>p</i> &lt; .001; CBI: <i>F<sub>df</sub></i><sub>=2,12</sub> = 42.86, <i>p</i> &lt; .001; sensitivity: <i>F<sub>df</sub></i><sub>=2,12</sub> = 15.38, <i>p</i> &lt; .001; Tables <link href="#ddi13161-sup-0001">S11 and S13</link>). Among the models that used GKD background sampling, species differed in which model had better evaluation metrics (Species*Background Sampling: AUC: <i>F<sub>df</sub></i><sub>=12,12</sub> = 6.07, <i>p</i> = .002; CBI: <i>F<sub>df</sub></i><sub>=12,12</sub> = 3.65, <i>p</i> = .017; Sens: <i>F<sub>df</sub></i><sub>=12,12</sub> = 4.56, <i>p</i> = .007; Table <link href="#ddi13161-sup-0001">S11</link>).</p></section><section xml:id="ddi13161-sec-0028"><title type="main">Variability</title><p>The coefficients of variation (among the four cross‐validation datasets) for both AUC and sensitivity were significantly greater when models were built using either GKD background sampling relative to random sampling (AUC: <i>F</i><sub>2,12</sub> = 9.49, <i>p</i> = .003; sensitivity: <i>F</i><sub>2,12</sub> = 20.07, <i>p</i> &lt; .001; Table <link href="#ddi13161-sup-0001">S14</link>).</p></section></section><section xml:id="ddi13161-sec-0029"><title type="main">Theme 3: How did model selection affect projections of habitat suitability, especially in currently unoccupied areas?</title><section xml:id="ddi13161-sec-0030"><title type="main">Model projections</title><p>The results for theme 1 and 2 were agnostic to model selection and were meant to explore common patterns in model outputs generated by changes in data modifications. Here, we focus on the models that were retained following our structured decision process (Figures <link href="#ddi13161-fig-0001">1</link> and <link href="#ddi13161-fig-0004">4</link>). Six of seven selected models used the 20 km occurrence downsampling and three of seven models used GKD background sampling (Figure <link href="#ddi13161-fig-0004">4</link>; Tables <link href="#ddi13161-sup-0001">S15 and S16</link>). Overall, projections of selected models using current climate aligned well with current species distributions both across the continent and in the region of interest (Figures <link href="#ddi13161-sup-0001">S15–S21</link>).</p><figure xml:id="ddi13161-fig-0004"><label>4</label><title type="figureName">FIGURE</title><mediaResourceGroup><mediaResource alt="image" href="urn:x-wiley:13669516:media:ddi13161:ddi13161-fig-0004"/><mediaResource alt="image" href="graphic/ddi13161-fig-0004-m.png" mimeType="image/png" rendition="webHiRes"/></mediaResourceGroup><caption>Projections of habitat suitability for set of selected models for each of the seven species. (a) Common Tansy, (b) Wild Parsnip, (c) Leafy Spurge, (d) Common Teasel, (e) Brown Knapweed, (g) Black Swallowwort and (h) Dalmatian Toadflax. Darker purple/blue colour values indicate low predicted habitat suitability and brighter yellow/green colour values indicate high predicted habitat suitability. Occurrence and background sampling used to construct models are summarized in (f), and amount of predicted suitable habitat in square kilometres at the 50% threshold are summarized in (i)</caption></figure><p>For the common and historically established species (Common Tansy, Wild Parsnip, and Leafy Spurge), selected models used 20 km occurrence downsampling and either highly clustered (GKD λ = 1) or random background sampling (Table <link href="#ddi13161-sup-0001">S16</link>). Occurrence downsampling reduced bias associated with uneven sampling effort at local scales. The projected suitable but unoccupied area primarily represents range filling, rather than range expansion.</p><p>For incipient but diffusely distributed species (Brown Knapweed and Common Teasel), the selected models used 20 km occurrence downsampling and highly clustered (GKD λ = 1) background sampling (Table <link href="#ddi13161-sup-0001">S16</link>). These occurrence and background point modifications reduced bias associated with uneven sampling effort at both local and regional levels. The projected suitable but unoccupied area primarily represented range expansion, not range filling.</p><p>For incipient but highly clustered species (Black Swallowwort and Dalmatian Toadflax), there were no clear patterns (Table <link href="#ddi13161-sup-0001">S16</link>). For Dalmatian Toadflax, the selected model was built using the 1 km filtered dataset and random background sampling, suggesting this dataset may not have readily identifiable spatial bias patterns in the occurrence records. Conversely, for Black Swallowwort, the selected model was built using 20 km occurrence downsampling and highly clustered background sampling. The suitable but unoccupied area for both species primarily represented range expansion into the Upper and Central Midwest.</p></section></section></section><section type="discussion" xml:id="ddi13161-sec-0031"><title type="main">DISCUSSION</title><p>Building climate‐based SDMs that can successfully predict suitable habitat for invasive species remains a fundamental challenge despite its widespread adoption for management purposes. We implemented a factorial design of data modifications to comprehensively evaluate and reduce spatial bias for multiple invasive species. Our results suggest that models constructed with downsampled occurrence data and GKD background sampling had fewer parameters (i.e. were less complex) and were more regularized. We found that model performance and model complexity metrics were often not in agreement and that models that were overly complex often failed to predict occurrences in a region at the advancing front of geographic ranges (low transferability). Using our full matrix of models, we designed a decision‐making process that considered model complexity, performance and geographic variability for multiple model evaluation metrics. Overall, our results suggest that careful consideration of spatial bias in model inputs and evaluation criteria are essential for identifying SDMs that have the capacity to predict suitable habitat at expanding range fronts of invasive species.</p><p>Our approach is uncommon in that we used a factorial design to independently assess the effects of different data modifications on the capacity of models to predict invasive species distributions and transferability to expanding range fronts. Both occurrence downsampling and GKD background selection had significant and independent effects on model complexity (e.g. number of model parameters) and the amount of projected suitable habitat. The independent effects of both modifications likely occurred because they correct for spatial bias at differing scales: downsampling of occurrence data reduces bias introduced by local oversampling whereas modification of background sampling reduces the effects of spatial biases at continental scales (e.g. incomplete sampling in some regions and/or dispersal lags). These modifications also had different effects depending on species invasion history and the spatial biases that may potentially be associated with invasion histories. For common and historically established species, downsampling had much larger effects; whereas, for the incipient and clustered species, GKD background selection had much larger effects. For the incipient and diffuse species, both had roughly equivalent effects.</p><p>There has been growing recognition that model input for SDMs may require modification to mitigate the effects of spatial bias. For both native and invasive species, downsampling of occurrence data (e.g. Briscoe Runquist, Lake, Tiffin, &amp; Moeller, <link href="#ddi13161-bib-0013">2019</link>; Kramer‐Schadt et al., <link href="#ddi13161-bib-0047">2013</link>) and background bias correction (Clements et al., <link href="#ddi13161-bib-0017">2012</link>; Kramer‐Schadt et al., <link href="#ddi13161-bib-0047">2013</link>; Phillips et al., <link href="#ddi13161-bib-0071">2009</link>; Syfert, Smith, &amp; Coomes, <link href="#ddi13161-bib-0081">2013</link>) have been implemented separately. However, systematic investigation of the interaction between the two techniques and exploration of multiple levels of each (e.g. downsampling at different spatial scales) is uncommon. Sampling bias in occurrence records may be particularly pronounced in invasive species because of the geographically uneven sampling and the relatively shortened timeframes over which they have been tracked. Therefore, when building invasive species SDMs, incorporation of downsampling and background point manipulation should be explored for all species. While occurrence and background data manipulation may improve the quality of models and their projections of habitat suitability, reducing the number of input records or manipulating background sampling to achieve a balanced design may reduce model performance measures (Hernandez, Graham, Master, &amp; Albert, <link href="#ddi13161-bib-0036">2006</link>). Due to this known tradeoff in improving model quality and decreasing model performance, the most commonly used metric to evaluate model performance (test AUC) may be misleading (Lobo et al., <link href="#ddi13161-bib-0052">2008</link>; Warren &amp; Seifert, <link href="#ddi13161-bib-0085">2011</link>). Overall, maximizing AUC will often not result in SDMs that are successful at predicting suitable but unoccupied territory. Rather, they reflect success in predicting occurrences in the oldest regions of the invaded range where records are numerous and dense across the landscape. These SDMs also often have sharp transitions between habitat predicted to be highly suitable and highly unsuitable, which indicates that the model is likely overly complex and not appropriately regularized (Figures <link href="#ddi13161-sup-0001">S15–S21</link>).</p><p>One of the overarching patterns for our set of selected models was that they consistently had low AUC scores despite high pAUC ratio, CBI and sensitivity scores. Additionally, AUC did not agree well with model fit statistics, such as AICc (which balanced model complexity with model information); this result is common across model‐building efforts (Lobo et al., <link href="#ddi13161-bib-0052">2008</link>). AUC favoured models built using standard datasets (i.e. filtered to 1 km with random background points); however, these models generally performed poorly when evaluated using alternative methods. Perhaps because of this, we found no obvious relationship between maximizing AUC and model selection. AUC equally weights errors of omission and commission (Lobo et al., <link href="#ddi13161-bib-0052">2008</link>). When AUC fails, it often does so because validation statistics assume errors of omission and commission are well characterized (Oreskes, Shrader‐Frechette, &amp; Belitz, <link href="#ddi13161-bib-0062">1994</link>). Because background points are not true absences, true species prevalence and true omission and commission errors are not well captured, and AUC may be misleading (Leroy et al., <link href="#ddi13161-bib-0051">2018</link>). Indeed, a recent simulation study of AUC found that discrimination accuracy metrics were only informative about model quality when the models were simple, and the input data closely mimicked the true ecological niche (e.g. nearly complete range filling where background points were more similar to absences; Warren, Matzke, &amp; Iglesias, <link href="#ddi13161-bib-0084">2019</link>). In our models, we did see a pattern that AUC more likely to partially agree with other metrics for our common, historically established species, which are the most likely to have filled much of their available ecological niche in their invaded range.</p><p>Due to the known challenges of AUC, we advocate that modellers use multiple evaluation metrics and integrate that information during model selection. In particular, modellers should incorporate metrics that do not equally weight commission and omission errors such as pAUC ratio and sensitivity (Anderson, Lew, &amp; Peterson, <link href="#ddi13161-bib-0004">2003</link>; Lobo et al., <link href="#ddi13161-bib-0052">2008</link>; Peterson, <link href="#ddi13161-bib-0065">2006</link>). The pAUC ratio limits evaluation of models to regions of the AUC curve with low omission (Peterson, <link href="#ddi13161-bib-0065">2006</link>). Likewise, sensitivity evaluates only the proportion of true positives (the omission rate) and does not rely on commission errors. CBI is valuable because it is spatially explicit and useful for incorporating knowledge about variation in prevalence across a landscape, but still may be prone to errors of commission. In the end, because all metrics have potentially complementary pitfalls, we used a structured process to incorporate information from them all during model selection.</p><section xml:id="ddi13161-sec-0032"><title type="main">Invasive species distributions</title><p>Our selected models predicted variable but increased habitat suitability at and beyond species' range margins (in the Upper Midwest) under current climate. For common and historically established species (Common Tansy, Wild Parsnip and Leafy Spurge), predicted suitable habitat mostly reflected range infilling, with some geographic expansion to the north and west. For the other four species, all showed potential for range expansion to the north and west. Similarly, other invasive species have also been shown to have greater potential for range infilling and northward expansion (e.g. Jarnevich &amp; Stohlgren, <link href="#ddi13161-bib-0043">2009</link>; Mainali et al., <link href="#ddi13161-bib-0055">2015</link>). Similarly, non‐native plant species of North America occupied a smaller fraction of their potential range than native species, suggesting greater potential for range infilling (Bradley, Early, &amp; Sorte, <link href="#ddi13161-bib-0012">2014</link>). Last, non‐native species have also been found to have potential for east‐west range expansion, which was also common among our species (Bradley et al., <link href="#ddi13161-bib-0012">2014</link>).</p></section></section><section type="conclusions" xml:id="ddi13161-sec-0033"><title type="main">CONCLUSIONS</title><p>Invasive species test the limits of SDM capabilities. Navigating these challenges requires developing and selecting models using data modifications to understand model behaviour and guard against common model pitfalls. It is important that SDMs are evaluated holistically with a diverse set of model diagnostics to avoid selecting and relying on models that do not generalize beyond the oldest regions of invaded ranges and cannot predict suitable habitat in incipient areas of invasion. Our study suggests that modifications of occurrence and background data should be implemented when modelling invasive species to minimize model overfit and spatial biases. Finally, interpretation of these models as the description of a species' ecological niche should be done with caution and include additional sources of evidence beyond SDMs.</p></section><section numbered="no" type="acknowledgments" xml:id="ddi13161-sec-0034"><title type="main">ACKNOWLEDGEMENTS</title><p>We thank R. Venette for thoughtful discussion and comments and Z. Radford and K. Wilson for help gathering occurrence record data. Funding for this project was provided by the Minnesota Invasive Terrestrial Plants and Pests Center through the Environment and Natural Resources Trust Fund as recommended by the Legislative‐Citizen Commission on Minnesota Resources (LCCMR).</p></section><section numbered="no" type="openResearch" xml:id="ddi13161-sec-0035"><section numbered="no" type="transparentPeerReview" xml:id="ddi13161-sec-0036"><title type="main">PEER REVIEW</title><p>The peer review history for this article is available at <url href="https://publons.com/publon/10.1111/ddi.13161">https://publons.com/publon/10.1111/ddi.13161</url>.</p></section><section numbered="no" type="dataAvailability" xml:id="ddi13161-sec-0037"><title type="main">DATA AVAILABILITY STATEMENT</title><p>All data will be made publicly available through Dryad (<url href="https://doi.org/10.5061/dryad.73n5tb2v8">https://doi.org/10.5061/dryad.73n5tb2v8</url>) the Data Repository for the University of Minnesota (<url href="https://doi.org/10.13020/z71w-jx69">https://doi.org/10.13020/z71w‐jx69</url>).</p></section></section><bibliography cited="yes" style="nameDate" xml:id="ddi13161-bibl-0001"><title type="main">REFERENCES</title><bib xml:id="ddi13161-bib-0001"><citation type="journal" xml:id="ddi13161-cit-0001"><author><familyName>Akaike</familyName>, <givenNames>H.</givenNames></author> (<pubYear year="1974">1974</pubYear>). <articleTitle>A new look at the statistical model identification</articleTitle>. <journalTitle>IEEE Transactions on Automatic Control</journalTitle>, <vol>19</vol>(<issue>6</issue>), <pageFirst>716</pageFirst>–<pageLast>723</pageLast>.</citation></bib><bib xml:id="ddi13161-bib-0003"><citation type="journal" xml:id="ddi13161-cit-0003"><author><familyName>Allouche</familyName>, <givenNames>O.</givenNames></author>, <author><familyName>Tsoar</familyName>, <givenNames>A.</givenNames></author>, &amp; <author><familyName>Kadmon</familyName>, <givenNames>K.</givenNames></author> (<pubYear year="2006">2006</pubYear>). <articleTitle>Assessing the accuracy of species distribution models: Prevalence, kappa and the true skill statistic (TSS)</articleTitle>. <journalTitle>Journal of Applied Ecology</journalTitle>, <vol>43</vol>, <pageFirst>1223</pageFirst>–<pageLast>1232</pageLast>. <url href="https://doi.org/10.1111/j.1365-2664.2006.01214.x">https://doi.org/10.1111/j.1365‐2664.2006.01214.x</url></citation></bib><bib xml:id="ddi13161-bib-0004"><citation type="journal" xml:id="ddi13161-cit-0004"><author><familyName>Anderson</familyName>, <givenNames>R. P.</givenNames></author>, <author><familyName>Lew</familyName>, <givenNames>D.</givenNames></author>, &amp; <author><familyName>Peterson</familyName>, <givenNames>A. T.</givenNames></author> (<pubYear year="2003">2003</pubYear>). <articleTitle>Evaluating predictive models of species’ distributions: Criteria for selecting optimal models</articleTitle>. <journalTitle>Ecological Modelling</journalTitle>, <vol>162</vol>, <pageFirst>211</pageFirst>–<pageLast>232</pageLast>. <url href="https://doi.org/10.1016/S0304-3800(02)00349-6">https://doi.org/10.1016/S0304‐3800(02)00349‐6</url></citation></bib><bib xml:id="ddi13161-bib-0006"><citation type="journal" xml:id="ddi13161-cit-0006"><author><familyName>Barbet‐Massin</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Jiguet</familyName>, <givenNames>F.</givenNames></author>, <author><familyName>Hélène Albert</familyName>, <givenNames>C.</givenNames></author>, &amp; <author><familyName>Thuiller</familyName>, <givenNames>W.</givenNames></author> (<pubYear year="2012">2012</pubYear>). <articleTitle>Selecting pseudo‐absences for species distribution models: How, where and how many?</articleTitle> <journalTitle>Methods in Ecology and Evolution</journalTitle>, <vol>3</vol>, <pageFirst>327</pageFirst>–<pageLast>338</pageLast>. <url href="https://doi.org/10.1111/j.2041-210X.2011.00172.x">https://doi.org/10.1111/j.2041‐210X.2011.00172.x</url></citation></bib><bib xml:id="ddi13161-bib-0007"><citation type="book" xml:id="ddi13161-cit-0007"><author><familyName>Baumgartner</familyName>, <givenNames>J.</givenNames></author>, &amp; <author><familyName>Wilson</familyName>, <givenNames>P.</givenNames></author> (<pubYear year="2019">2019</pubYear>). <bookTitle>Rmaxent: Tools for working with Maxent in R. R package version 0.803.9000</bookTitle>. Retrieved from <url href="https://github.com/johnbaums/rmaxent">https://github.com/johnbaums/rmaxent</url></citation></bib><bib xml:id="ddi13161-bib-0008"><citation type="book" xml:id="ddi13161-cit-0008"><author><familyName>Bivand</familyName>, <givenNames>R.</givenNames></author>, <author><familyName>Keitt</familyName>, <givenNames>T.</givenNames></author>, &amp; <author><familyName>Rowlingson</familyName>, <givenNames>B.</givenNames></author> (<pubYear year="2019">2019</pubYear>). <bookTitle>Rgdal: bindings for the “geospatial”data abstraction library. R package version 1.2‐16</bookTitle>. in press.</citation></bib><bib xml:id="ddi13161-bib-0009"><citation type="book" xml:id="ddi13161-cit-0009"><author><familyName>Bivand</familyName>, <givenNames>R.</givenNames></author>, &amp; <author><familyName>Rundel</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2015">2015</pubYear>). <bookTitle>rgeos: Interface to Geometry Engine—Open Source (GEOS). R package version 3–19</bookTitle>. in press.</citation></bib><bib xml:id="ddi13161-bib-0010"><citation type="journal" xml:id="ddi13161-cit-0010"><author><familyName>Boyce</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Vernier</familyName>, <givenNames>P.</givenNames></author>, <author><familyName>Nielsen</familyName>, <givenNames>S.</givenNames></author>, &amp; <author><familyName>Schmiegelow</familyName>, <givenNames>F.</givenNames></author> (<pubYear year="2002">2002</pubYear>). <articleTitle>Evaluating resource selection functions</articleTitle>. <journalTitle>Ecological Modelling</journalTitle>, <vol>157</vol>, <pageFirst>281</pageFirst>–<pageLast>300</pageLast>. <url href="https://doi.org/10.1016/S0304-3800(02)00200-4">https://doi.org/10.1016/S0304‐3800(02)00200‐4</url></citation></bib><bib xml:id="ddi13161-bib-0011"><citation type="journal" xml:id="ddi13161-cit-0011"><author><familyName>Bradley</familyName>, <givenNames>B. A.</givenNames></author>, <author><familyName>Blumenthal</familyName>, <givenNames>D. M.</givenNames></author>, <author><familyName>Wilcove</familyName>, <givenNames>D. S.</givenNames></author>, &amp; <author><familyName>Ziska</familyName>, <givenNames>L. H.</givenNames></author> (<pubYear year="2010">2010</pubYear>). <articleTitle>Predicting plant invasions in an era of global change</articleTitle>. <journalTitle>Trends in Ecology and Evolution</journalTitle>, <vol>25</vol>, <pageFirst>310</pageFirst>–<pageLast>318</pageLast>. <url href="https://doi.org/10.1016/j.tree.2009.12.003">https://doi.org/10.1016/j.tree.2009.12.003</url></citation></bib><bib xml:id="ddi13161-bib-0012"><citation type="journal" xml:id="ddi13161-cit-0012"><author><familyName>Bradley</familyName>, <givenNames>B. A.</givenNames></author>, <author><familyName>Early</familyName>, <givenNames>R.</givenNames></author>, &amp; <author><familyName>Sorte</familyName>, <givenNames>C. J. B.</givenNames></author> (<pubYear year="2014">2014</pubYear>). <articleTitle>Space to invade? Comparative range infilling and potential range of invasive and native plants</articleTitle>. <journalTitle>Global Ecology and Biogeography</journalTitle>, <vol>24</vol>, <pageFirst>348</pageFirst>–<pageLast>359</pageLast>. <url href="https://doi.org/10.1111/geb.12275">https://doi.org/10.1111/geb.12275</url></citation></bib><bib xml:id="ddi13161-bib-0013"><citation type="journal" xml:id="ddi13161-cit-0013"><author><familyName>Briscoe Runquist</familyName>, <givenNames>R. D.</givenNames></author>, <author><familyName>Lake</familyName>, <givenNames>T.</givenNames></author>, <author><familyName>Tiffin</familyName>, <givenNames>P.</givenNames></author>, &amp; <author><familyName>Moeller</familyName>, <givenNames>D. A.</givenNames></author> (<pubYear year="2019">2019</pubYear>). <articleTitle>Species distribution models throughout the invasion history of <i>Palmer amaranth</i> predict regions at risk of future invasion and reveal challenges with modeling rapidly shifting geographic ranges</articleTitle>. <journalTitle>Scientific Reports</journalTitle>, <vol>9</vol>, <pageFirst>2426</pageFirst>.</citation></bib><bib xml:id="ddi13161-bib-0014"><citation type="journal" xml:id="ddi13161-cit-0014"><author><familyName>Broennimann</familyName>, <givenNames>O.</givenNames></author>, <author><familyName>Treier</familyName>, <givenNames>U. A.</givenNames></author>, <author><familyName>Müller‐Schärer</familyName>, <givenNames>H.</givenNames></author>, <author><familyName>Thuiller</familyName>, <givenNames>W.</givenNames></author>, <author><familyName>Peterson</familyName>, <givenNames>A. T.</givenNames></author>, &amp; <author><familyName>Guisan</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2007">2007</pubYear>). <articleTitle>Evidence of climatic niche shift during biological invasion</articleTitle>. <journalTitle>Ecology Letters</journalTitle>, <vol>10</vol>, <pageFirst>701</pageFirst>–<pageLast>709</pageLast>. <url href="https://doi.org/10.1111/j.1461-0248.2007.01060.x">https://doi.org/10.1111/j.1461‐0248.2007.01060.x</url></citation></bib><bib xml:id="ddi13161-bib-0015"><citation type="journal" xml:id="ddi13161-cit-0015"><author><familyName>Brun</familyName>, <givenNames>P.</givenNames></author>, <author><familyName>Thuiller</familyName>, <givenNames>W.</givenNames></author>, <author><familyName>Chauvier</familyName>, <givenNames>Y.</givenNames></author>, <author><familyName>Pellissier</familyName>, <givenNames>L.</givenNames></author>, <author><familyName>Wüest</familyName>, <givenNames>R. O.</givenNames></author>, <author><familyName>Wang</familyName>, <givenNames>Z.</givenNames></author>, &amp; <author><familyName>Zimmermann</familyName>, <givenNames>N. E.</givenNames></author> (<pubYear year="2019">2019</pubYear>). <articleTitle>Model complexity affects species distribution projections under climate change</articleTitle>. <journalTitle>Journal of Biogeography</journalTitle>, <vol>47</vol>, <pageFirst>130</pageFirst>–<pageLast>142</pageLast>. <url href="https://doi.org/10.1111/jbi.13734">https://doi.org/10.1111/jbi.13734</url></citation></bib><bib xml:id="ddi13161-bib-0017"><citation type="journal" xml:id="ddi13161-cit-0017"><author><familyName>Clements</familyName>, <givenNames>G. R.</givenNames></author>, <author><familyName>Rayan</familyName>, <givenNames>D. M.</givenNames></author>, <author><familyName>Aziz</familyName>, <givenNames>S. A.</givenNames></author>, <author><familyName>Kawanishi</familyName>, <givenNames>K.</givenNames></author>, <author><familyName>Traeholt</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>Magintan</familyName>, <givenNames>D.</givenNames></author>, … <author><familyName>Tingley</familyName>, <givenNames>R.</givenNames></author> (<pubYear year="2012">2012</pubYear>). <articleTitle>Predicting the distribution of the Asian tapir in Peninsular Malaysia using maximum entropy modeling</articleTitle>. <journalTitle>Integrative Zoology</journalTitle>, <vol>7</vol>, <pageFirst>400</pageFirst>–<pageLast>406</pageLast>.</citation></bib><bib xml:id="ddi13161-bib-0020"><citation type="book" xml:id="ddi13161-cit-0020"><author><familyName>Douglass</familyName>, <givenNames>C. H.</givenNames></author>, <author><familyName>Weston</familyName>, <givenNames>L. A.</givenNames></author>, &amp; <author><familyName>DiTommaso</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2009">2009</pubYear>). <chapterTitle>Black and Pale Swallow‐Wort (<i>Vincetoxicum nigrum</i> and <i>V. rossicum</i>): The biology and ecology of two perennial, exotic and invasive vines</chapterTitle>. In <groupName>Inderjit</groupName> (ed.), <bookTitle>Management of invasive weeds. invading nature – Springer series in invasion ecology</bookTitle> (Vol. <vol>5</vol>, pp. <pageFirst>261</pageFirst>–<pageLast>277</pageLast>). <publisherLoc>Dordrecht, the Netherlands</publisherLoc>: <publisherName>Springer</publisherName>.</citation></bib><bib xml:id="ddi13161-bib-0021"><citation type="journal" xml:id="ddi13161-cit-0021"><author><familyName>Elith</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Kearney</familyName>, <givenNames>M.</givenNames></author>, &amp; <author><familyName>Phillips</familyName>, <givenNames>S.</givenNames></author> (<pubYear year="2010">2010</pubYear>). <articleTitle>The art of modelling range‐shifting species</articleTitle>. <journalTitle>Methods in Ecology and Evolution</journalTitle>, <vol>1</vol>, <pageFirst>330</pageFirst>–<pageLast>342</pageLast>. <url href="https://doi.org/10.1111/j.2041-210X.2010.00036.x">https://doi.org/10.1111/j.2041‐210X.2010.00036.x</url></citation></bib><bib xml:id="ddi13161-bib-0022"><citation type="journal" xml:id="ddi13161-cit-0022"><author><familyName>Elith</familyName>, <givenNames>J.</givenNames></author>, &amp; <author><familyName>Leathwick</familyName>, <givenNames>J. R.</givenNames></author> (<pubYear year="2009">2009</pubYear>). <articleTitle>Species distribution models: Ecological explanation and prediction across space and time</articleTitle>. <journalTitle>Annual Review of Ecology, Evolution, and Systematics</journalTitle>, <vol>40</vol>, <pageFirst>677</pageFirst>–<pageLast>697</pageLast>. <url href="https://doi.org/10.1146/annurev.ecolsys.110308.120159">https://doi.org/10.1146/annurev.ecolsys.110308.120159</url></citation></bib><bib xml:id="ddi13161-bib-0023"><citation type="journal" xml:id="ddi13161-cit-0023"><author><familyName>Elith</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Phillips</familyName>, <givenNames>S.</givenNames></author>, <author><familyName>Hastie</familyName>, <givenNames>T.</givenNames></author>, <author><familyName>Dudík</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>En Chee</familyName>, <givenNames>Y.</givenNames></author>, &amp; <author><familyName>Yates</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2011">2011</pubYear>). <articleTitle>A statistical explanation of MaxEnt for ecologists</articleTitle>. <journalTitle>Diversity and Distributions</journalTitle>, <vol>17</vol>, <pageFirst>43</pageFirst>–<pageLast>57</pageLast>. <url href="https://doi.org/10.1111/j.1472-4642.2010.00725.x">https://doi.org/10.1111/j.1472‐4642.2010.00725.x</url></citation></bib><bib xml:id="ddi13161-bib-0025"><citation type="journal" xml:id="ddi13161-cit-0025"><author><familyName>Escobar</familyName>, <givenNames>L. E.</givenNames></author>, <author><familyName>Qiao</familyName>, <givenNames>H.</givenNames></author>, <author><familyName>Cabello</familyName>, <givenNames>J.</givenNames></author>, &amp; <author><familyName>Townsend Peterson</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2018">2018</pubYear>). <articleTitle>Ecological niche modeling re‐examined: A case study with the Darwin’s Fox</articleTitle>. <journalTitle>Ecology and Evolution</journalTitle>, <vol>8</vol>(<issue>10</issue>), <pageFirst>4757</pageFirst>–<pageLast>4770</pageLast>. <url href="https://doi.org/10.1002/ece3.4014">https://doi.org/10.1002/ece3.4014</url></citation></bib><bib xml:id="ddi13161-bib-0026"><citation type="book" xml:id="ddi13161-cit-0026"><groupName>ESRI</groupName>. (<pubYear year="2019">2019</pubYear>). <bookTitle>ArcGIS Pro: Release 2.3</bookTitle>. <publisherLoc>Redlands, CA</publisherLoc>: <publisherName>Environmental Systems Research Institute</publisherName>.</citation></bib><bib xml:id="ddi13161-bib-0027"><citation type="journal" xml:id="ddi13161-cit-0027"><author><familyName>Fick</familyName>, <givenNames>S. E.</givenNames></author>, &amp; <author><familyName>Hijmans</familyName>, <givenNames>R. J.</givenNames></author> (<pubYear year="2017">2017</pubYear>). <articleTitle>WorldClim 2: New 1‐km spatial resolution climate surfaces for global land areas</articleTitle>. <journalTitle>International Journal of Climatology</journalTitle>, <vol>37</vol>, <pageFirst>4302</pageFirst>–<pageLast>4315</pageLast>. <url href="https://doi.org/10.1002/joc.5086">https://doi.org/10.1002/joc.5086</url></citation></bib><bib xml:id="ddi13161-bib-0028"><citation type="journal" xml:id="ddi13161-cit-0028"><author><familyName>Finnoff</familyName>, <givenNames>D.</givenNames></author>, <author><familyName>Shogren</familyName>, <givenNames>J. F.</givenNames></author>, <author><familyName>Leung</familyName>, <givenNames>B.</givenNames></author>, &amp; <author><familyName>Lodge</familyName>, <givenNames>D.</givenNames></author> (<pubYear year="2007">2007</pubYear>). <articleTitle>Take a risk: Preferring prevention over control of biological invaders</articleTitle>. <journalTitle>Ecological Economics</journalTitle>, <vol>62</vol>, <pageFirst>216</pageFirst>–<pageLast>222</pageLast>. <url href="https://doi.org/10.1016/j.ecolecon.2006.03.025">https://doi.org/10.1016/j.ecolecon.2006.03.025</url></citation></bib><bib xml:id="ddi13161-bib-0029"><citation type="journal" xml:id="ddi13161-cit-0029"><author><familyName>Fourcade</familyName>, <givenNames>Y.</givenNames></author>, <author><familyName>Besnard</familyName>, <givenNames>A. G.</givenNames></author>, &amp; <author><familyName>Secondi</familyName>, <givenNames>J.</givenNames></author> (<pubYear year="2018">2018</pubYear>). <articleTitle>Paintings predict the distribution of species, or the challenge of selecting environmental predictors and evaluation statistics</articleTitle>. <journalTitle>Global Ecology and Biogeography</journalTitle>, <vol>27</vol>, <pageFirst>245</pageFirst>–<pageLast>256</pageLast>. <url href="https://doi.org/10.1111/geb.12684">https://doi.org/10.1111/geb.12684</url></citation></bib><bib xml:id="ddi13161-bib-0030"><citation type="journal" xml:id="ddi13161-cit-0030"><author><familyName>Fourcade</familyName>, <givenNames>Y.</givenNames></author>, <author><familyName>Engler</familyName>, <givenNames>J. O.</givenNames></author>, <author><familyName>Rödder</familyName>, <givenNames>D.</givenNames></author>, &amp; <author><familyName>Secondi</familyName>, <givenNames>J.</givenNames></author> (<pubYear year="2014">2014</pubYear>). <articleTitle>Mapping species distributions with MAXENT using a geographically biased sample of presence data: A performance assessment of methods for correcting sampling bias</articleTitle>. <journalTitle>PLoS One</journalTitle>, <vol>9</vol>, <eLocator>e97122</eLocator>. <url href="https://doi.org/10.1371/journal.pone.0097122">https://doi.org/10.1371/journal.pone.0097122</url></citation></bib><bib xml:id="ddi13161-bib-0034"><citation type="journal" xml:id="ddi13161-cit-0034"><author><familyName>Hao</familyName>, <givenNames>T.</givenNames></author>, <author><familyName>Elith</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Guillera‐Arroita</familyName>, <givenNames>G.</givenNames></author>, &amp; <author><familyName>Lahoz‐Monfort</familyName>, <givenNames>J. J.</givenNames></author> (<pubYear year="2019">2019</pubYear>). <articleTitle>A review of evidence about use and performance of species distribution modelling ensembles like BIOMOD</articleTitle>. <journalTitle>Diversity and Distributions</journalTitle>, <vol>25</vol>, <pageFirst>839</pageFirst>–<pageLast>852</pageLast>. <url href="https://doi.org/10.1111/ddi.12892">https://doi.org/10.1111/ddi.12892</url></citation></bib><bib xml:id="ddi13161-bib-0035"><citation type="journal" xml:id="ddi13161-cit-0035"><author><familyName>Hastie</familyName>, <givenNames>T.</givenNames></author>, &amp; <author><familyName>Fithian</familyName>, <givenNames>W.</givenNames></author> (<pubYear year="2013">2013</pubYear>). <articleTitle>Inference from presence‐only data; the ongoing controversy</articleTitle>. <journalTitle>Ecography</journalTitle>, <vol>36</vol>, <pageFirst>864</pageFirst>–<pageLast>867</pageLast>. <url href="https://doi.org/10.1111/j.1600-0587.2013.00321.x">https://doi.org/10.1111/j.1600‐0587.2013.00321.x</url></citation></bib><bib xml:id="ddi13161-bib-0036"><citation type="journal" xml:id="ddi13161-cit-0036"><author><familyName>Hernandez</familyName>, <givenNames>P. A.</givenNames></author>, <author><familyName>Graham</familyName>, <givenNames>C. H.</givenNames></author>, <author><familyName>Master</familyName>, <givenNames>L. L.</givenNames></author>, &amp; <author><familyName>Albert</familyName>, <givenNames>D. L.</givenNames></author> (<pubYear year="2006">2006</pubYear>). <articleTitle>The effect of sample size and species characteristics on performance of different species distribution modeling methods</articleTitle>. <journalTitle>Ecography</journalTitle>, <vol>29</vol>, <pageFirst>733</pageFirst>–<pageLast>785</pageLast>. <url href="https://doi.org/10.1111/j.0906-7590.2006.04700.x">https://doi.org/10.1111/j.0906‐7590.2006.04700.x</url></citation></bib><bib xml:id="ddi13161-bib-0037"><citation type="journal" xml:id="ddi13161-cit-0037"><author><familyName>Hijmans</familyName>, <givenNames>R. J.</givenNames></author>, <author><familyName>Cameron</familyName>, <givenNames>S. E.</givenNames></author>, <author><familyName>Parra</familyName>, <givenNames>J. L.</givenNames></author>, <author><familyName>Jones</familyName>, <givenNames>P. G.</givenNames></author>, &amp; <author><familyName>Jarvis</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2005">2005</pubYear>). <articleTitle>Very high resolution interpolated climate surfaces for global land areas</articleTitle>. <journalTitle>International Journal of Climatology</journalTitle>, <vol>25</vol>, <pageFirst>1965</pageFirst>–<pageLast>1978</pageLast>. <url href="https://doi.org/10.1002/joc.1276">https://doi.org/10.1002/joc.1276</url></citation></bib><bib xml:id="ddi13161-bib-0038"><citation type="book" xml:id="ddi13161-cit-0038"><author><familyName>Hijmans</familyName>, <givenNames>R. J.</givenNames></author>, &amp; <author><familyName>vanEtten</familyName>, <givenNames>J.</givenNames></author> (<pubYear year="2012">2012</pubYear>). <bookTitle>raster: Geographic analysis and modelling with raster data. R package version 20‐08</bookTitle>. in press.</citation></bib><bib xml:id="ddi13161-bib-0039"><citation type="journal" xml:id="ddi13161-cit-0039"><author><familyName>Hirzel</familyName>, <givenNames>A. H.</givenNames></author>, <author><familyName>Le Lay</familyName>, <givenNames>G.</givenNames></author>, <author><familyName>Helfer</familyName>, <givenNames>V.</givenNames></author>, <author><familyName>Randin</familyName>, <givenNames>C.</givenNames></author>, &amp; <author><familyName>Guisan</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2006">2006</pubYear>). <articleTitle>Evaluating the ability of habitat suitability models to predict species presences</articleTitle>. <journalTitle>Ecological Modelling</journalTitle>, <vol>199</vol>, <pageFirst>142</pageFirst>–<pageLast>152</pageLast>. <url href="https://doi.org/10.1016/j.ecolmodel.2006.05.017">https://doi.org/10.1016/j.ecolmodel.2006.05.017</url></citation></bib><bib xml:id="ddi13161-bib-0043"><citation type="journal" xml:id="ddi13161-cit-0043"><author><familyName>Jarnevich</familyName>, <givenNames>C. S.</givenNames></author>, &amp; <author><familyName>Stohlgren</familyName>, <givenNames>T. J.</givenNames></author> (<pubYear year="2009">2009</pubYear>). <articleTitle>Near term climate projections for invasive species distributions</articleTitle>. <journalTitle>Biological Invasions</journalTitle>, <vol>11</vol>, <pageFirst>1373</pageFirst>–<pageLast>1379</pageLast>. <url href="https://doi.org/10.1007/s10530-008-9345-8">https://doi.org/10.1007/s10530‐008‐9345‐8</url></citation></bib><bib xml:id="ddi13161-bib-0044"><citation type="journal" xml:id="ddi13161-cit-0044"><author><familyName>Jiménez‐Valverde</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Peterson</familyName>, <givenNames>A. T.</givenNames></author>, <author><familyName>Soberón</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Overton</familyName>, <givenNames>J. M.</givenNames></author>, <author><familyName>Aragón</familyName>, <givenNames>P.</givenNames></author>, &amp; <author><familyName>Lobo</familyName>, <givenNames>J. M.</givenNames></author> (<pubYear year="2011">2011</pubYear>). <articleTitle>Use of niche models in invasive species risk assessments</articleTitle>. <journalTitle>Biological Invasions</journalTitle>, <vol>13</vol>, <pageFirst>2785</pageFirst>–<pageLast>2797</pageLast>. <url href="https://doi.org/10.1007/s10530-011-9963-4">https://doi.org/10.1007/s10530‐011‐9963‐4</url></citation></bib><bib xml:id="ddi13161-bib-0047"><citation type="journal" xml:id="ddi13161-cit-0047"><author><familyName>Kramer‐Schadt</familyName>, <givenNames>S.</givenNames></author>, <author><familyName>Niedballa</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Pilgrim</familyName>, <givenNames>J. D.</givenNames></author>, <author><familyName>Schröder</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>Lindenborn</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Reinfelder</familyName>, <givenNames>V.</givenNames></author>, … <author><familyName>Wilting</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2013">2013</pubYear>). <articleTitle>The importance of correcting for sampling bias in MaxEnt species distribution models</articleTitle>. <journalTitle>Diversity and Distributions</journalTitle>, <vol>19</vol>, <pageFirst>1366</pageFirst>–<pageLast>1379</pageLast>. <url href="https://doi.org/10.1111/ddi.12096">https://doi.org/10.1111/ddi.12096</url></citation></bib><bib xml:id="ddi13161-bib-0048"><citation type="book" xml:id="ddi13161-cit-0048"><author><familyName>Lamigueriro</familyName>, <givenNames>O. P.</givenNames></author>, &amp; <author><familyName>Hijmans</familyName>, <givenNames>R.</givenNames></author> (<pubYear year="2018">2018</pubYear>). <bookTitle>rasterVis. R package version 0.45</bookTitle>. in press.</citation></bib><bib xml:id="ddi13161-bib-0049"><citation type="journal" xml:id="ddi13161-cit-0049"><author><familyName>Le Maitre</familyName>, <givenNames>D. C.</givenNames></author>, <author><familyName>Thuiller</familyName>, <givenNames>W.</givenNames></author>, &amp; <author><familyName>Schonegevel</familyName>, <givenNames>L.</givenNames></author> (<pubYear year="2008">2008</pubYear>). <articleTitle>Developing an approach to defining the potential distributions of invasive plant species: A case study of Hakea species in South Africa</articleTitle>. <journalTitle>Global Ecology and Biogeography</journalTitle>, <vol>17</vol>(<issue>5</issue>), <pageFirst>569</pageFirst>–<pageLast>584</pageLast>.</citation></bib><bib xml:id="ddi13161-bib-0051"><citation type="journal" xml:id="ddi13161-cit-0051"><author><familyName>Leroy</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>Delsol</familyName>, <givenNames>R.</givenNames></author>, <author><familyName>Hugueny</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>Meynard</familyName>, <givenNames>C. N.</givenNames></author>, <author><familyName>Barhoumi</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>Barbet‐Massin</familyName>, <givenNames>M.</givenNames></author>, &amp; <author><familyName>Bellard</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2018">2018</pubYear>). <articleTitle>Without quality presence‐absence data, discrimination metrics such as TSS can be misleading measures of model performance</articleTitle>. <journalTitle>Journal of Biogeography</journalTitle>, <vol>45</vol>(<issue>9</issue>), <pageFirst>1994</pageFirst>–<pageLast>2002</pageLast>.</citation></bib><bib xml:id="ddi13161-bib-0052"><citation type="journal" xml:id="ddi13161-cit-0052"><author><familyName>Lobo</familyName>, <givenNames>J. M.</givenNames></author>, <author><familyName>Jiménez‐Valverde</familyName>, <givenNames>A.</givenNames></author>, &amp; <author><familyName>Real</familyName>, <givenNames>R.</givenNames></author> (<pubYear year="2008">2008</pubYear>). <articleTitle>AUC: A misleading measure of the performance of predictive distribution models</articleTitle>. <journalTitle>Global Ecology and Biogeography</journalTitle>, <vol>17</vol>, <pageFirst>145</pageFirst>–<pageLast>151</pageLast>. <url href="https://doi.org/10.1111/j.1466-8238.2007.00358.x">https://doi.org/10.1111/j.1466‐8238.2007.00358.x</url></citation></bib><bib xml:id="ddi13161-bib-0053"><citation type="journal" xml:id="ddi13161-cit-0053"><author><familyName>Lodge</familyName>, <givenNames>D. M.</givenNames></author>, <author><familyName>Simonin</familyName>, <givenNames>P. W.</givenNames></author>, <author><familyName>Burgiel</familyName>, <givenNames>S. W.</givenNames></author>, <author><familyName>Keller</familyName>, <givenNames>R. P.</givenNames></author>, <author><familyName>Bossenbroek</familyName>, <givenNames>J. M.</givenNames></author>, <author><familyName>Jerde</familyName>, <givenNames>C. L.</givenNames></author>, … <author><familyName>Zhang</familyName>, <givenNames>H.</givenNames></author> (<pubYear year="2016">2016</pubYear>). <articleTitle>Risk analysis and bioeconomics of invasive species to inform policy and management</articleTitle>. <journalTitle>Annual Review of Environment and Resources</journalTitle>, <vol>41</vol>, <pageFirst>453</pageFirst>–<pageLast>488</pageLast>. <url href="https://doi.org/10.1146/annurev-environ-110615-085532">https://doi.org/10.1146/annurev‐environ‐110615‐085532</url></citation></bib><bib xml:id="ddi13161-bib-0055"><citation type="journal" xml:id="ddi13161-cit-0055"><author><familyName>Mainali</familyName>, <givenNames>K. P.</givenNames></author>, <author><familyName>Warren</familyName>, <givenNames>D. L.</givenNames></author>, <author><familyName>Dhileepan</familyName>, <givenNames>K.</givenNames></author>, <author><familyName>McConnachie</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Strathie</familyName>, <givenNames>L.</givenNames></author>, <author><familyName>Hassan</familyName>, <givenNames>G.</givenNames></author>, … <author><familyName>Parmesan</familyName>, <givenNames>C.</givenNames></author> (<pubYear year="2015">2015</pubYear>). <articleTitle>Projecting future expansion of invasive species: Comparing and improving methodologies for species distribution modeling</articleTitle>. <journalTitle>Global Change Biology</journalTitle>, <vol>21</vol>, <pageFirst>4464</pageFirst>–<pageLast>4480</pageLast>. <url href="https://doi.org/10.1111/gcb.13038">https://doi.org/10.1111/gcb.13038</url></citation></bib><bib xml:id="ddi13161-bib-0056"><citation type="journal" xml:id="ddi13161-cit-0056"><author><familyName>Marbuah</familyName>, <givenNames>G.</givenNames></author>, <author><familyName>Gren</familyName>, <givenNames>I.</givenNames></author>, &amp; <author><familyName>McKie</familyName>, <givenNames>B.</givenNames></author> (<pubYear year="2014">2014</pubYear>). <articleTitle>Economics of Harmful Invasive Species: A Review</articleTitle>. <journalTitle>Diversity</journalTitle>, <vol>6</vol>, <pageFirst>500</pageFirst>–<pageLast>523</pageLast>. <url href="https://doi.org/10.3390/d6030500">https://doi.org/10.3390/d6030500</url></citation></bib><bib xml:id="ddi13161-bib-0057"><citation type="journal" xml:id="ddi13161-cit-0057"><author><familyName>Merckx</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>Steyaert</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Vanreusel</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Vincx</familyName>, <givenNames>M.</givenNames></author>, &amp; <author><familyName>Vanaverbeke</familyName>, <givenNames>J.</givenNames></author> (<pubYear year="2011">2011</pubYear>). <articleTitle>Null models reveal preferential sampling, spatial autocorrelation and overfitting in habitat suitability modelling</articleTitle>. <journalTitle>Ecological Modelling</journalTitle>, <vol>222</vol>, <pageFirst>588</pageFirst>–<pageLast>597</pageLast>. <url href="https://doi.org/10.1016/j.ecolmodel.2010.11.016">https://doi.org/10.1016/j.ecolmodel.2010.11.016</url></citation></bib><bib xml:id="ddi13161-bib-0058"><citation type="journal" xml:id="ddi13161-cit-0058"><author><familyName>Merow</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>Smith</familyName>, <givenNames>M. J.</givenNames></author>, &amp; <author><familyName>Silander</familyName>, <givenNames>J. A.</givenNames></author> (<pubYear year="2013">2013</pubYear>). <articleTitle>A practical guide to MaxEnt for modeling species’ distributions: What it does, and why inputs and settings matter</articleTitle>. <journalTitle>Ecography</journalTitle>, <vol>36</vol>, <pageFirst>1058</pageFirst>–<pageLast>1069</pageLast>. <url href="https://doi.org/10.1111/j.1600-0587.2013.07872.x">https://doi.org/10.1111/j.1600‐0587.2013.07872.x</url></citation></bib><bib xml:id="ddi13161-bib-0060"><citation type="journal" xml:id="ddi13161-cit-0060"><author><familyName>Morey</familyName>, <givenNames>A. C.</givenNames></author>, &amp; <author><familyName>Venette</familyName>, <givenNames>R. C.</givenNames></author> (<pubYear year="2020">2020</pubYear>). <articleTitle>Minimizing risk and maximizing spatial transferability: Challenges in constructing a useful model of potential suitability for an invasive insect</articleTitle>. <journalTitle>Annals of the Entomological Society of America</journalTitle>, <vol>113</vol>(<issue>2</issue>), <pageFirst>100</pageFirst>–<pageLast>113</pageLast>. <url href="https://doi.org/10.1093/aesa/saz049">https://doi.org/10.1093/aesa/saz049</url></citation></bib><bib xml:id="ddi13161-bib-0061"><citation type="journal" xml:id="ddi13161-cit-0061"><author><familyName>Muscarella</familyName>, <givenNames>R.</givenNames></author>, <author><familyName>Galante</familyName>, <givenNames>P. J.</givenNames></author>, <author><familyName>Soley‐Guardia</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Boria</familyName>, <givenNames>R. A.</givenNames></author>, <author><familyName>Kass</familyName>, <givenNames>J. M.</givenNames></author>, <author><familyName>Uriarte</familyName>, <givenNames>M.</givenNames></author>, &amp; <author><familyName>Anderson</familyName>, <givenNames>R. P.</givenNames></author> (<pubYear year="2014">2014</pubYear>). <articleTitle>ENMeval: An R package for conducting spatially independent evaluations and estimating optimal model complexity for Maxent ecological niche models</articleTitle>. <journalTitle>Methods in Ecology and Evolution</journalTitle>, <vol>5</vol>, <pageFirst>1198</pageFirst>–<pageLast>1205</pageLast>.</citation></bib><bib xml:id="ddi13161-bib-0062"><citation type="journal" xml:id="ddi13161-cit-0062"><author><familyName>Oreskes</familyName>, <givenNames>N.</givenNames></author>, <author><familyName>Shrader‐Frechette</familyName>, <givenNames>K.</givenNames></author>, &amp; <author><familyName>Belitz</familyName>, <givenNames>K.</givenNames></author> (<pubYear year="1994">1994</pubYear>). <articleTitle>Verification, validation, and confirmation of numerical models in the earth sciences</articleTitle>. <journalTitle>Science</journalTitle>, <vol>263</vol>, <pageFirst>641</pageFirst>–<pageLast>646</pageLast>. <url href="https://doi.org/10.1126/science.263.5147.641">https://doi.org/10.1126/science.263.5147.641</url></citation></bib><bib xml:id="ddi13161-bib-0063"><citation type="book" xml:id="ddi13161-cit-0063"><author><familyName>Osorio‐Olivera</familyName>, <givenNames>L.</givenNames></author> (<pubYear year="2016">2016</pubYear>). <bookTitle>NicheToolbox: A web tool for exploratory data analysis and niche modeling</bookTitle>. Retrieved from <url href="http://shiny.conabio.gob.mx:3838/nichetoolb2/">http://shiny.conabio.gob.mx:3838/nichetoolb2/</url></citation></bib><bib xml:id="ddi13161-bib-0064"><citation type="journal" xml:id="ddi13161-cit-0064"><author><familyName>Pebesma</familyName>, <givenNames>E.</givenNames></author>, &amp; <author><familyName>Bivand</familyName>, <givenNames>R. S.</givenNames></author> (<pubYear year="2005">2005</pubYear>). <articleTitle>S classes and methods for spatial data: The sp package</articleTitle>. <journalTitle>R News</journalTitle>, <vol>5</vol>, <pageFirst>9</pageFirst>–<pageLast>13</pageLast>.</citation></bib><bib xml:id="ddi13161-bib-0065"><citation type="journal" xml:id="ddi13161-cit-0065"><author><familyName>Peterson</familyName>, <givenNames>A. T.</givenNames></author> (<pubYear year="2006">2006</pubYear>). <articleTitle>Uses and requirements of ecological niche models and related distributional models</articleTitle>. <journalTitle>Biodiversity Informatics</journalTitle>, <vol>3</vol>, <pageFirst>59</pageFirst>–<pageLast>72</pageLast>. <url href="https://doi.org/10.17161/bi.v3i0.29">https://doi.org/10.17161/bi.v3i0.29</url></citation></bib><bib xml:id="ddi13161-bib-0066"><citation type="journal" xml:id="ddi13161-cit-0066"><author><familyName>Peterson</familyName>, <givenNames>A. T.</givenNames></author>, <author><familyName>Papeş</familyName>, <givenNames>M.</givenNames></author>, &amp; <author><familyName>Soberón</familyName>, <givenNames>J.</givenNames></author> (<pubYear year="2008">2008</pubYear>). <articleTitle>Rethinking receiver operating characteristic analysis applications in ecological niche modeling</articleTitle>. <journalTitle>Ecological Modelling</journalTitle>, <vol>213</vol>(<issue>1</issue>), <pageFirst>63</pageFirst>–<pageLast>72</pageLast>.</citation></bib><bib xml:id="ddi13161-bib-0067"><citation type="journal" xml:id="ddi13161-cit-0067"><author><familyName>Petitpierre</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>Broenniman</familyName>, <givenNames>O.</givenNames></author>, <author><familyName>Kueffer</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>Daehler</familyName>, <givenNames>C.</givenNames></author>, &amp; <author><familyName>Guisan</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2017">2017</pubYear>). <articleTitle>Selecting predictors to maximize the transferability of species distribution models: Lessons from cross‐continental plant invasions</articleTitle>. <journalTitle>Global Ecology and Biogeography</journalTitle>, <vol>26</vol>, <pageFirst>275</pageFirst>–<pageLast>287</pageLast>. <url href="https://doi.org/10.1111/geb.12530">https://doi.org/10.1111/geb.12530</url></citation></bib><bib xml:id="ddi13161-bib-0068"><citation type="journal" xml:id="ddi13161-cit-0068"><author><familyName>Petitpierre</familyName>, <givenNames>B.</givenNames></author>, <author><familyName>Kueffer</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>Broenniman</familyName>, <givenNames>O.</givenNames></author>, <author><familyName>Randin</familyName>, <givenNames>C.</givenNames></author>, <author><familyName>Daehler</familyName>, <givenNames>C.</givenNames></author>, &amp; <author><familyName>Guisan</familyName>, <givenNames>A.</givenNames></author> (<pubYear year="2012">2012</pubYear>). <articleTitle>Climatic niche shifts are rare among terrestrial plant invaders</articleTitle>. <journalTitle>Science</journalTitle>, <vol>335</vol>, <pageFirst>1344</pageFirst>–<pageLast>1348</pageLast>. <url href="https://doi.org/10.1126/science.1215933">https://doi.org/10.1126/science.1215933</url></citation></bib><bib xml:id="ddi13161-bib-0069"><citation type="journal" xml:id="ddi13161-cit-0069"><author><familyName>Phillips</familyName>, <givenNames>S. J.</givenNames></author>, <author><familyName>Anderson</familyName>, <givenNames>R. P.</givenNames></author>, <author><familyName>Dudík</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Schapire</familyName>, <givenNames>R. E.</givenNames></author>, &amp; <author><familyName>Blair</familyName>, <givenNames>M. E.</givenNames></author> (<pubYear year="2017">2017</pubYear>). <articleTitle>Opening the black box: An open‐source release of Maxent</articleTitle>. <journalTitle>Ecography</journalTitle>, <vol>40</vol>, <pageFirst>887</pageFirst>–<pageLast>893</pageLast>. <url href="https://doi.org/10.1111/ecog.03049">https://doi.org/10.1111/ecog.03049</url></citation></bib><bib xml:id="ddi13161-bib-0070"><citation type="journal" xml:id="ddi13161-cit-0070"><author><familyName>Phillips</familyName>, <givenNames>S. J.</givenNames></author>, &amp; <author><familyName>Dudík</familyName>, <givenNames>M.</givenNames></author> (<pubYear year="2008">2008</pubYear>). <articleTitle>Modeling of species distributions with Maxent: New extensions and a comprehensive evaluation</articleTitle>. <journalTitle>Ecography</journalTitle>, <vol>31</vol>, <pageFirst>161</pageFirst>–<pageLast>175</pageLast>. <url href="https://doi.org/10.1111/j.0906-7590.2008.5203.x">https://doi.org/10.1111/j.0906‐7590.2008.5203.x</url></citation></bib><bib xml:id="ddi13161-bib-0071"><citation type="journal" xml:id="ddi13161-cit-0071"><author><familyName>Phillips</familyName>, <givenNames>S. J.</givenNames></author>, <author><familyName>Dudík</familyName>, <givenNames>M.</givenNames></author>, <author><familyName>Elith</familyName>, <givenNames>J.</givenNames></author>, <author><familyName>Graham</familyName>, <givenNames>C. H.</givenNames></author>, <author><familyName>Lehmann</familyName>, <givenNames>A.</givenNames></author>, <author><familyName>Leathwick</familyName>, <givenNames>J.</givenNames></author>, &amp; <author><familyName>Ferrier</familyName>, <givenNames>S.</givenNames></author> (<pubYear year="2009">2009</pubYear>). <articleTitle>Sample selection bias and presence‐only distribution models: Implications for background and pseudo‐absence data</articleTitle>. <journalTitle>Ecological Applications</journalTitle>, <vol>19</vol>, <pageFirst>181</pageFirst>–<pageLast>197</pageLast>. <url href="https://doi.org/10.1890/07-2153.1">https://doi.org/10.1890/07‐2153.1</url></citation></bib><bib xml:id="ddi13161-bib-0072"><citation type="book" xml:id="ddi13161-cit-0072"><author><familyName>Phillips</familyName>, <givenNames>S. J.</givenNames></author>, <author><familyName>Dudík</familyName>, <givenNames>M.</givenNames></author>, &amp; <author><familyName>Schapire</familyName>, <givenNames>R. E.</givenNames></author> (<pubYear year="2018">2018</pubYear>). <bookTitle>Maxent software for modeling species niches and distributions (Version 3.4.1)</bookTitle> [Internet]. Retrieved from <url href="http://biodiversityinformatics.amnh.org/open_source/maxent/">http://biodiversityinformatics.amnh.org/open_source/maxent/</url></citation></bib><bib xml:id="ddi13161-bib-0074"><citation type="book" xml:id="ddi13161-cit-0074"><groupName>R Core Team</groupName>. (<pubYear year="2018">2018</pubYear>). <bookTitle>R: A language and environment for statistical computing</bookTitle>. <publisherLoc>Vienna, Austria</publisherLoc>: <publisherName>R Foundation for Statistical Computing</publisherName>. Retrieved from <url href="https://www.R-project.org/">https://www.R‐project.org/</url></citation></bib><bib xml:id="ddi13161-bib-0075"><citation type="journal" xml:id="ddi13161-cit-0075"><author><familyName>Radosavljevic</familyName>, <givenNames>A.</givenNames></author>, &amp; <author><familyName>Anderson</familyName>, <givenNames>R. P.</givenNames></author> (<pubYear year="2014">2014</pubYear>). <articleTitle>Making better Maxent models of species distributions: Complexity, overfitting and evaluation</articleTitle>. <journalTitle>Journal of Biogeography</journalTitle>, <vol>41</vol>, <pageFirst>629</pageFirst>–<pageLast>643</pageLast>.</citation></bib><bib xml:id="ddi13161-bib-0081"><citation type="journal" xml:id="ddi13161-cit-0081"><author><familyName>Syfert</familyName>, <givenNames>M. M.</givenNames></author>, <author><familyName>Smith</familyName>, <givenNames>M. J.</givenNames></author>, &amp; <author><familyName>Coomes</familyName>, <givenNames>D. A.</givenNames></author> (<pubYear year="2013">2013</pubYear>). <articleTitle>The effects of sampling bias and model complexity on the predictive performance of MaxEnt species distribution models</articleTitle>. <journalTitle>PLoS One</journalTitle>, <vol>8</vol>, <eLocator>e0055158</eLocator>.</citation></bib><bib xml:id="ddi13161-bib-0083"><citation type="journal" xml:id="ddi13161-cit-0083"><author><familyNamePrefix>von der</familyNamePrefix> <familyName>Lippe</familyName>, <givenNames>M.</givenNames></author>, &amp; <author><familyName>Kowarik</familyName>, <givenNames>I.</givenNames></author> (<pubYear year="2007">2007</pubYear>). <articleTitle>Long‐distance dispersal of plants by vehicles as a driver of plant invasions</articleTitle>. <journalTitle>Conservation Biology</journalTitle>, <vol>21</vol>, <pageFirst>986</pageFirst>–<pageLast>996</pageLast>. <url href="https://doi.org/10.1111/j.1523-1739.2007.00722.x">https://doi.org/10.1111/j.1523‐1739.2007.00722.x</url></citation></bib><bib xml:id="ddi13161-bib-0084"><citation type="journal" xml:id="ddi13161-cit-0084"><author><familyName>Warren</familyName>, <givenNames>D. L.</givenNames></author>, <author><familyName>Matzke</familyName>, <givenNames>N. J.</givenNames></author>, &amp; <author><familyName>Iglesias</familyName>, <givenNames>T. L.</givenNames></author> (<pubYear year="2019">2019</pubYear>). <articleTitle>Evaluating species distribution models with discrimination accuracy is uninformative for many applications</articleTitle>. <journalTitle>bioRxiv</journalTitle>, <eLocator>684399</eLocator>.</citation></bib><bib xml:id="ddi13161-bib-0085"><citation type="journal" xml:id="ddi13161-cit-0085"><author><familyName>Warren</familyName>, <givenNames>D. L.</givenNames></author>, &amp; <author><familyName>Seifert</familyName>, <givenNames>S. N.</givenNames></author> (<pubYear year="2011">2011</pubYear>). <articleTitle>Ecological niche modeling in Maxent: The importance of model complexity and the performance of model selection criteria</articleTitle>. <journalTitle>Ecological Applications</journalTitle>, <vol>21</vol>, <pageFirst>335</pageFirst>–<pageLast>342</pageLast>. <url href="https://doi.org/10.1890/10-1171.1">https://doi.org/10.1890/10‐1171.1</url></citation></bib><bib xml:id="ddi13161-bib-0086"><citation type="journal" xml:id="ddi13161-cit-0086"><author><familyName>Wenger</familyName>, <givenNames>S. J.</givenNames></author>, &amp; <author><familyName>Olden</familyName>, <givenNames>J. D.</givenNames></author> (<pubYear year="2012">2012</pubYear>). <articleTitle>Assessing transferability of ecological models: An underappreciated aspect of statistical validation</articleTitle>. <journalTitle>Methods in Ecology and Evolution</journalTitle>, <vol>3</vol>, <pageFirst>260</pageFirst>–<pageLast>267</pageLast>. <url href="https://doi.org/10.1111/j.2041-210X.2011.00170.x">https://doi.org/10.1111/j.2041‐210X.2011.00170.x</url></citation></bib><bib xml:id="ddi13161-bib-0090"><citation type="journal" xml:id="ddi13161-cit-0090"><author><familyName>Yates</familyName>, <givenNames>K. L.</givenNames></author>, <author><familyName>Bouchet</familyName>, <givenNames>P. J.</givenNames></author>, <author><familyName>Caley</familyName>, <givenNames>M. J.</givenNames></author>, <author><familyName>Mengersen</familyName>, <givenNames>K.</givenNames></author>, <author><familyName>Randin</familyName>, <givenNames>C. F.</givenNames></author>, <author><familyName>Parnell</familyName>, <givenNames>S.</givenNames></author>, … <author><familyName>Sequeira</familyName>, <givenNames>A. M. M.</givenNames></author> (<pubYear year="2018">2018</pubYear>). <articleTitle>Outstanding Challenges in the transferability of ecological models</articleTitle>. <journalTitle>Trends in Ecology and Evolution</journalTitle>, <vol>33</vol>, <pageFirst>790</pageFirst>–<pageLast>802</pageLast>. <url href="https://doi.org/10.1016/j.tree.2018.08.001">https://doi.org/10.1016/j.tree.2018.08.001</url></citation></bib></bibliography><section numbered="no" xml:id="ddi13161-sec-0038"><title type="main">BIOSKETCH</title><p>Thomas Lake is a Ph.D. student with Dr. D.A. Moeller in the Plant and Microbial Biology Program at the University of Minnesota studying the biogeography and landscape genomics of invasive species. D.A. Moeller is an Associate Professor at UMN whose research focuses on the ecological and evolutionary drivers of geographic range limits, plant mating system evolution and plant speciation. R.D. Briscoe Runquist is a Postdoctoral Research Associate with D.A.M., whose research focuses on the biogeography and landscape genomics of invasive plants.</p><p><b>Author contributions</b>: All authors conceived of the work. T.L. performed analyses. T.L. and R.D.B.R. led writing of the manuscript. All authors contributed to substantial editing of the manuscript.</p></section></body></component>