<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" dtd-version="1.3" xml:lang="en" article-type="research-article"><?DTDIdentifier.IdentifierValue -//NLM//DTD JATS (Z39.96) Journal Publishing DTD v1.2 20190208//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName JATS-journalpublishing1.dtd?><?SourceDTD.Version 1.2?><?ConverterInfo.XSLTName jats2jats3.xsl?><?ConverterInfo.Version 1?><?all-math-mml yes?><?use-mml?><?properties open_access?><?properties manuscript?><?origin ukpmcpa?><?ManuscriptPrefix new?><?iso-abbr Nat Hum Behav?><?submitter-system ukmss?><?submitter-canonical-name Nature Publishing Group?><?submitter-canonical-id NATURE-STRUCTUR?><?submitter-userid 0?><?submitter-authority publisher?><?submitter-login NPG?><?submitter-name Nature Publishing Group?><?domain wtpa?><?pmc-id-preallocated 7612428?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-journal-id">101697750</journal-id><journal-id journal-id-type="nlm-ta">Nat Hum Behav</journal-id><journal-id journal-id-type="iso-abbrev">Nat Hum Behav</journal-id><journal-title-group><journal-title>Nature human behaviour</journal-title></journal-title-group><issn pub-type="epub">2397-3374</issn></journal-meta><article-meta><article-id pub-id-type="pmcid">7612428</article-id><article-id pub-id-type="manuscript">ems137960</article-id><article-id pub-id-type="pmid">35058641</article-id><article-id pub-id-type="doi">10.1038/s41562-021-01247-w</article-id><article-categories><subj-group subj-group-type="heading"><subject>Article</subject></subj-group></article-categories><title-group><article-title>Subjective confidence reflects representation of Bayesian probability in cortex</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Geurts</surname><given-names>Laura S.</given-names></name><xref rid="A1" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><name><surname>Cooke</surname><given-names>James R. H.</given-names></name><xref rid="A1" ref-type="aff">1</xref></contrib><contrib contrib-type="author"><name><surname>van Bergen</surname><given-names>Ruben S.</given-names></name><xref rid="A1" ref-type="aff">1</xref><xref rid="A2" ref-type="aff">2</xref></contrib><contrib contrib-type="author"><name><surname>Jehee</surname><given-names>Janneke F. M.</given-names></name><xref rid="A1" ref-type="aff">1</xref><xref rid="CR1" ref-type="corresp">*</xref></contrib></contrib-group><aff id="A1">
<label>1</label>Donders Institute for Brain, Cognition and Behavior, Radboud University, Nijmegen, Netherlands</aff><aff id="A2">
<label>2</label>Zuckerman Mind Brain Behavior Institute, Columbia University, New York, United States</aff><author-notes><corresp id="CR1">
<label>*</label>Corresponding author: <email><EMAIL></email>
</corresp></author-notes><pub-date pub-type="nihms-submitted"><day>03</day><month>11</month><year>2021</year></pub-date><pub-date pub-type="ppub"><day>01</day><month>2</month><year>2022</year></pub-date><pub-date pub-type="epub"><day>20</day><month>1</month><year>2022</year></pub-date><pub-date pub-type="pmc-release"><day>20</day><month>7</month><year>2022</year></pub-date><volume>6</volume><issue>2</issue><fpage>294</fpage><lpage>305</lpage><permissions><ali:free_to_read xmlns:ali="http://www.niso.org/schemas/ali/1.0/"/><license><ali:license_ref xmlns:ali="http://www.niso.org/schemas/ali/1.0/">https://www.springernature.com/gp/open-research/policies/accepted-manuscript-terms</ali:license_ref><license-p>Users may view, print, copy, and download text and data-mine the content in such documents, for the purposes of academic research, subject always to the full Conditions of use: <ext-link ext-link-type="uri" xlink:href="https://www.springernature.com/gp/open-research/policies/accepted-manuscript-terms">https://www.springernature.com/gp/open-research/policies/accepted-manuscript-terms</ext-link>
</license-p></license></permissions><abstract><p id="P1">What gives rise to the human sense of confidence? Here, we tested the Bayesian hypothesis that confidence is based on a probability distribution represented in neural population activity. We implemented several computational models of confidence, and tested their predictions using psychophysics and fMRI. Using a generative model-based fMRI decoding approach, we extracted probability distributions from neural population activity in human visual cortex. We found that subjective confidence tracks the shape of the decoded distribution. That is, when sensory evidence was more precise, as indicated by the decoded distribution, observers reported higher levels of confidence. We furthermore found that neural activity in the insula, anterior cingulate, and prefrontal cortex was linked to both the shape of the decoded distribution and reported confidence, in ways consistent with the Bayesian model. Altogether, our findings support recent statistical theories of confidence and suggest that probabilistic information guides the computation of one&#x02019;s sense of confidence.</p></abstract></article-meta></front><body><p id="P2">Virtually any decision comes with a sense of confidence &#x02013; a subjective feeling that clearly affects our everyday choices. For example, we reduce speed when driving at night because we feel less confident about our estimates of distance to surrounding traffic, we hesitate to try a piece of food when unsure about its taste, and resist investing in stocks unless convinced of their likely future profit. But what is this sense of confidence that accompanies almost all of our decisions?</p><p id="P3">Recent Bayesian decision theories<sup>
<xref rid="R1" ref-type="bibr">1</xref>&#x02013;<xref rid="R5" ref-type="bibr">5</xref>
</sup> propose that confidence corresponds to the degree of belief, or probability, that a choice is correct based on the evidence. More specifically, these theories propose that confidence is a function of the posterior probability of being correct, which links confidence directly to the quality of the evidence on which the decision is based. Thus, greater imprecision in evidence reduces the probability that the choice is correct, which should result in lower levels of confidence. The agent&#x02019;s evidence is similarly described as a degree of belief in an event, or more formally, as a probability distribution over a latent variable. For example, the evidence could be a probability distribution over perceived distance to surrounding traffic. The width of the distribution (range of probable distances) is broader in the dark than on a clear day, thereby signaling greater imprecision or uncertainty. Although central to the Bayesian confidence hypothesis, whether such probabilistic representations play a role in confidence is currently unclear.</p><p id="P4">Results from behavioral studies<sup>
<xref rid="R6" ref-type="bibr">6</xref>&#x02013;<xref rid="R9" ref-type="bibr">9</xref>
</sup> are consistent with the notion that confidence is computed from the degree of imprecision in sensory information. However, a major limitation of this work has been the use of physical sources of noise, such as a variation in image brightness or contrast, to manipulate uncertainty. This is problematic because it could be that observers simply monitor such stimulus properties as external cues to uncertainty and confidence<sup>
<xref rid="R7" ref-type="bibr">7</xref>,<xref rid="R10" ref-type="bibr">10</xref>&#x02013;<xref rid="R13" ref-type="bibr">13</xref>
</sup>. While physiological studies have found neural correlates of statistical confidence in the orbitofrontal<sup>
<xref rid="R14" ref-type="bibr">14</xref>,<xref rid="R15" ref-type="bibr">15</xref>
</sup> and lateral intraparietal cortex<sup>
<xref rid="R16" ref-type="bibr">16</xref>
</sup>, these studies used a two-alternative forced choice (2AFC) task, so that the animal could simply rely on the distance between stimulus estimates (i.e. point estimates) and category boundary to compute confidence<sup>
<xref rid="R17" ref-type="bibr">17</xref>
</sup>, and need not use a representation of probability. Thus, one of the most fundamental assumptions of normative theories of decision-making &#x02013; that confidence is derived from a probabilistic representation of information &#x02013; has yet to be tested in cortex.</p><p id="P5">Here, we use a combination of functional Magnetic Resonance Imaging (fMRI), psychophysics, and computational modeling to address two fundamental questions. 1) Is confidence based on a probabilistic representation of sensory information? And if so, 2) what neural mechanisms extract confidence from this cortical representation of uncertainty? Human participants viewed random orientation stimuli, and reported both the orientation of the stimulus and their level of confidence in this judgment. Critically, no physical noise was added to the stimuli. We quantified the degree of uncertainty associated with stimulus representations in visual cortex using a probabilistic decoding approach<sup>
<xref rid="R10" ref-type="bibr">10</xref>,<xref rid="R18" ref-type="bibr">18</xref>
</sup>, relying on trial-by-trial fluctuations in internal noise to render the evidence more or less reliable to the observer. We used the decoded probability distributions to compare between human data and simulated data from a Bayesian observer, as well as two alternative models implementing heuristic strategies to confidence. Corroborating the Bayesian model, we discovered that human confidence judgments track the degree of uncertainty contained in visual cortical activity. That is, when the cortical representation of the stimulus was more precise (as indicated by a narrower decoded probability distribution), participants reported higher levels of confidence. In addition, activity in the dorsal Anterior Insula (dAI), dorsal Anterior Cingulate (dACC) and rostrolateral Prefrontal Cortex (rlPFC) reflected both this sensory uncertainty and reported confidence, in ways predicted by the Bayesian observer model. Taken together, these results support normative theories of decision-making, and suggest that probabilistic sensory information guides the computation of one&#x02019;s sense of confidence.</p><sec sec-type="results" id="S1"><title>Results</title><sec id="S2"><title>Ideal observer models</title><p id="P6">The observer&#x02019;s task is to infer the orientation of a stimulus from a noisy sensory measurement, and report both this estimate and their level of confidence in this judgment. We consider three model observers for this task. The decision process is identical for all three observers, but they use different strategies to confidence.</p><p id="P7">The observer&#x02019;s measurement <italic toggle="yes">m</italic> of the sensory stimulus <italic toggle="yes">s</italic> is corrupted by noise: even when the physical stimulus is held constant, the measurement varies from trial to trial. Thus, the relationship between stimulus and measurement on each trial is given by a probability distribution, <italic toggle="yes">p</italic>(<italic toggle="yes">m</italic>|<italic toggle="yes">s</italic>) which we model as a circular Gaussian centered on the stimulus and with variance <italic toggle="yes">&#x003c3;<sub>m</sub>
</italic>
<sup>2</sup>(<italic toggle="yes">s</italic>). This variability in the measurements stems from various sources of noise that are of both sensory and non-sensory origin. Specifically, we consider three sources of noise: two sensory and one non-sensory. The first source depends on stimulus orientation, with larger noise levels for oblique than cardinal stimulus orientations. This pattern captures the well-established &#x02018;oblique effect&#x02019; in orientation perception<sup>
<xref rid="R19" ref-type="bibr">19</xref>,<xref rid="R20" ref-type="bibr">20</xref>
</sup>. The second source varies in magnitude from trial to trial, and captures, for example, random fluctuations in neural response gain in sensory areas<sup>
<xref rid="R21" ref-type="bibr">21</xref>
</sup>. Finally, non-sensory noise refers to those sources of variance that affect, for example, the stimulus representation while held in working memory, or task-related processes in areas downstream of sensory cortex.</p><p id="P8">To infer the orientation of the stimulus from the measurement, all three observers invert the generative model to compute the posterior probability distribution <italic toggle="yes">p</italic>(<italic toggle="yes">s</italic>|<italic toggle="yes">m</italic>) (<xref rid="FD12" ref-type="disp-formula">Equation 12</xref>). This distribution quantifies the degree to which different stimulus values are consistent with the measurement. The mean of the posterior distribution is the model observer&#x02019;s estimate of the stimulus &#x0015d;. We take the (circular) variance of the distribution as a measure of the degree of uncertainty in this estimate. The observer&#x02019;s internal estimate of orientation is subsequently translated into an overt (behavioral) response, <italic toggle="yes">r</italic>. This transformation from internal estimate into motor response is noisy. Thus, across trials, the response fluctuates around &#x0015d;, where (motor) noise is drawn from a circular Gaussian (<xref rid="FD13" ref-type="disp-formula">Equation 13</xref>).</p><p id="P9">How does each of the observer models compute confidence? The ideal strategy is to consider the degree of imprecision in the observer&#x02019;s decision, which depends on all sources of variance that affect their reports. Specifically, for the estimation task used here, it is statistically reasonable to compute confidence as a function of the expected magnitude of the error in the observer&#x02019;s response. We quantified this as follows: <disp-formula id="FD1">
<label>#(1)</label>
<mml:math id="M1" display="block" overflow="scroll"><mml:msub><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:msup><mml:mstyle displaystyle="true"><mml:mo>&#x0222b;</mml:mo></mml:mstyle><mml:mtext>&#x0200b;</mml:mtext></mml:msup><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02223;</mml:mo><mml:mi>m</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mtext>&#x000a0;angle&#x000a0;</mml:mtext><mml:msup><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>r</mml:mi><mml:mo>,</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mi>d</mml:mi><mml:mi>s</mml:mi></mml:mrow></mml:mfrac></mml:math>
</disp-formula> where <italic toggle="yes">c<sub>B</sub>
</italic> refers to the reported level of confidence, and angle(<italic toggle="yes">r</italic>, <italic toggle="yes">s</italic>)<sup>2</sup> represents the magnitude of the response error (i.e., the squared acute-angle distance between response and (latent) stimulus). In words, when uncertainty in evidence is higher, the expected decision error tends to be larger, and reported confidence will be lower. However, our predictions do not strongly depend on the particular function assumed here, as long as confidence monotonically decreases when overall uncertainty increases. We refer to this model as the Bayesian or Probabilistic observer, as confidence is based (in part) on the posterior probability distribution &#x02013; a probabilistic notion of uncertainty.</p><p id="P10">The second model observer uses certain properties of the stimulus, such as its orientation, as a cue to confidence. This observer has learned through experience that behavioral precision is usually better for cardinal than for oblique orientations. The observer utilizes this learned relationship as a heuristic, and simply reports lower levels of confidence for those orientations that generally result in reduced levels of performance. We refer to this model as the Stimulus heuristics observer, and formally define their confidence as:
<disp-formula id="FD2">
<label>#(2)</label>
<mml:math id="M2" display="block" overflow="scroll"><mml:msub><mml:mi>c</mml:mi><mml:mi>S</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mi>f</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mfrac></mml:math>
</disp-formula> where <italic toggle="yes">f</italic>(<italic toggle="yes">&#x0015d;</italic>) is a function that rises for oblique orientations (see <xref rid="FD14" ref-type="disp-formula">Equation 14</xref> in <xref rid="S7" ref-type="sec">Methods</xref>). As the strategy ignores many sources of noise that create uncertainty, it is clearly suboptimal, but it could potentially explain human behavior, which is why we include the strategy here.</p><p id="P11">The third and final model observer ignores the imprecision in internal estimates altogether, and computes confidence exclusively from the noise in their motor response. We refer to this model as the Response heuristics observer. That is, on a given trial the observer simply notices a large offset between their internal orientation estimate and overt (motor) response. Observing that their response is off, they report lower levels of confidence. This is not an ideal strategy, but it is nonetheless a strategy that could result in a reliable link between confidence and behavioral performance, as we will show in our simulations below. We define confidence for this observer model as:
<disp-formula id="FD3">
<label>#(3)</label>
<mml:math id="M3" display="block" overflow="scroll"><mml:msub><mml:mi>c</mml:mi><mml:mi>R</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mtext>angle</mml:mtext><mml:msup><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>r</mml:mi><mml:mo>,</mml:mo><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfrac></mml:math>
</disp-formula>
</p><p id="P12">Where angle(<italic toggle="yes">r</italic>, <italic toggle="yes">&#x0015d;</italic>)<sup>2</sup> is the squared acute-angle distance between orientation estimate &#x0015d; and response <italic toggle="yes">r</italic>. <xref rid="F1" ref-type="fig">Fig. 1</xref> summarizes the three observer models.</p></sec><sec id="S3"><title>Model predictions</title><p id="P13">What behavioral patterns should one observe for the different strategies to confidence? To address this question, we simulated the behavioral orientation estimates and associated confidence reports of the three model observers. As we will show below, this leads to a set of concrete predictions that we can then test in psychophysical and neuroimaging experiments.</p><p id="P14">Does confidence predict behavioral performance? To address this question, we binned the simulated data according to reported level of confidence, and calculated the across-trial variance in behavioral orientation estimates for each of the bins. We first did this irrespective of the orientation of the stimulus. We found that the orientation judgments of the model observers were generally more precise when confidence was higher, regardless of the strategy to confidence employed by the observer (<xref rid="F2" ref-type="fig">Fig. 2a</xref>). Thus, a predictive link between confidence and behavioral precision is consistent with several strategies, and does not necessarily imply that confidence is based on a probabilistic representation of the degree of uncertainty in one&#x02019;s evidence.</p><p id="P15">We next turned to the relationship between confidence and behavioral performance for a constant stimulus. Closely replicating the experimental analysis procedures (see below), we first removed the effect of stimulus orientation from confidence, binned the data according to residual level of confidence, and calculated the variance in behavioral orientation estimates for each of the bins. We found that higher levels of confidence again predicted greater behavioral precision for both the Probabilistic and Response heuristics model (<xref rid="F2" ref-type="fig">Fig. 2b</xref>). For the Stimulus heuristics observer, in contrast, we observed no clear link between confidence and behavioral performance. This makes sense, as this observer uses orientation as a cue to confidence, so an identical orientation stimulus should, when averaged across repeated presentations, always result in the same level of confidence, irrespective of any stimulus-independent sources of variance. Thus, this analysis could potentially enable us to differentiate between some, though not all, strategies to confidence.</p><p id="P16">We next considered the relationship between confidence and the quality of the observer&#x02019;s evidence. Specifically, we determined the extent to which the degree of uncertainty in their sensory evidence predicted reported levels of confidence. Sensory uncertainty was quantified as the width of a probability distribution (see <xref rid="S7" ref-type="sec">Methods</xref>), similar to the empirical conditions. For practical reasons, we here disregard the contribution of non-sensory sources of variance and focus on sensory uncertainty alone, so as to closely match the empirical analyses. Data were binned for visualization only, and mean levels of confidence and uncertainty were computed for each of the bins. When analyzed across stimulus orientation, and for both the Stimulus heuristics and Bayesian observer, reported levels of confidence consistently decreased as sensory uncertainty increased. However, we observed no such relationship between confidence and uncertainty for the Response heuristics observer (<xref rid="F2" ref-type="fig">Fig. 2c</xref>). When holding the stimulus constant, the results were even more distinct between confidence strategies. That is, after we removed the contribution of stimulus orientation (see <xref rid="S7" ref-type="sec">Methods</xref>), the relationship between sensory uncertainty and confidence still held for the Bayesian observer, but no such link between the fidelity of the observer&#x02019;s sensory representation and confidence was observed for the two remaining models (<xref rid="F2" ref-type="fig">Fig. 2d</xref>). This illustrates the importance of considering internal levels of uncertainty when studying confidence, and moreover indicates that these analyses, when combined, should enable us to adjudicate between strategies to confidence.</p><p id="P17">In sum, if human confidence estimates are based on probabilistic computations, then 1) behavioral variance in an orientation judgment task should be higher with reduced levels of confidence for a constant stimulus; 2) there should be an inverse relationship between sensory uncertainty in cortex and reported confidence; and 3) this inverse relationship should hold both across orientations and when holding the stimulus constant. With these predictions in hand, we now turn to the experimental data to see which strategy best describes human confidence judgments.</p></sec><sec id="S4"><title>Human observers</title><p id="P18">Do human observers use a probabilistic representation of evidence quality when reporting confidence? To address this question, we presented 32 human participants with oriented gratings while we measured their brain activity using fMRI. Observers reported the orientation of the grating, as well as their confidence in this judgment (see <xref rid="F5" ref-type="fig">Extended Data Fig. 1</xref> for trial structure). They generally performed well on this task, with a mean absolute behavioral estimation error of 4.34&#x000b0; &#x000b1; 0.212&#x000b0; (mean &#x000b1; SEM across subjects).</p><p id="P19">We first focused on the link between behavioral performance and confidence. For each observer, we divided all trials, regardless of presented orientation, into ten bins of increasing confidence, and computed and compared behavioral variability and mean level of confidence in each bin. We found a significant inverse relationship between confidence and behavioral variability (t(287) = -16.79, p &#x0003c; 0.001, r = -0.70, 95% CI = [-0.76, -0.64]; <xref rid="F3" ref-type="fig">Fig. 3a</xref>, left). Thus, the observers&#x02019; orientation judgments were more precise when confidence was high. This indicates that the participants were able to meaningfully estimate their own level of confidence in the task.</p><p id="P20">We next turned to the relationship between confidence and behavioral precision for repeated presentations of the same stimulus. For each observer, we again sorted trials into ten bins of increasing confidence, calculated the mean level of confidence and behavioral variability across all trials in each bin, and computed the partial correlation coefficient between the two (while controlling for stimulus orientation, see <xref rid="S7" ref-type="sec">Methods</xref>). We considered two possible outcomes. If observers account for trial-by-trial fluctuations in internal noise when estimating confidence, as suggested by both the Probabilistic and Response heuristics model, then higher levels of confidence should predict improved behavioral performance. If, on the other hand, observers rely on orientation heuristics to confidence, then we should observe no systematic relationship at all between confidence and behavioral variability. The results revealed that behavior was more precise when confidence was high (<xref rid="F3" ref-type="fig">Fig. 3a</xref>, right; t(286) = - 11.02, p &#x0003c; 0.001, r = -0.55, 95% CI = [-0.62, -.046]). This is consistent with both the Probabilistic and Response heuristics model, and argues against an explanation of confidence in terms of orientation heuristics.</p><p id="P21">To adjudicate between the two remaining hypotheses, we then turned to the brain data. Specifically, we used a probabilistic decoding algorithm<sup>
<xref rid="R10" ref-type="bibr">10</xref>,<xref rid="R18" ref-type="bibr">18</xref>
</sup> to characterize the degree of uncertainty in perceptual evidence from cortical activity patterns in areas V1-V3. Uncertainty in the cortical stimulus representation (&#x02018;decoded uncertainty&#x02019;) was quantified on a trial-by-trial basis as the width (variance) of a decoded probability distribution (see <xref rid="S7" ref-type="sec">Methods</xref>). Benchmark analyses verified that 1) orientation decoding performance was well above chance levels (<xref rid="F6" ref-type="fig">Extended Data Fig. 2a</xref>, see also <sup>
<xref rid="R10" ref-type="bibr">10</xref>
</sup>), 2) decoded uncertainty was lower for cardinal compared to oblique stimuli (<xref rid="F6" ref-type="fig">Extended Data Fig. 2b</xref>, see also <sup>
<xref rid="R10" ref-type="bibr">10</xref>
</sup>), and 3) decoded uncertainty predicted behavioral variability, both within and across stimulus orientations (<xref rid="F6" ref-type="fig">Extended Data Fig. 2c-d</xref>, see also <sup>
<xref rid="R10" ref-type="bibr">10</xref>
</sup>). Altogether, this confirms that the precision of the observer&#x02019;s internal sensory evidence was reliably extracted from the patterns of fMRI activity on a trial-by-trial basis.</p><p id="P22">Do human observers rely on the quality of their internal visual evidence when estimating confidence? To address this question, we computed, for each individual observer, the trial-by-trial rank correlation coefficient between reported confidence and decoded uncertainty (see <xref rid="F3" ref-type="fig">Fig. 3b</xref> for an example observer). The obtained correlation coefficients were subsequently averaged across observers. Per our simulations, we predicted that if confidence is based on sensory uncertainty, then the imprecision in the observer&#x02019;s sensory evidence, as assessed by the decoder, should predict the confidence judgments of the observer. If, however, confidence is consistent with heuristic computations based on non-sensory sources of noise, then we should observe no relationship between decoded uncertainty and reported confidence at all. Corroborating the Probabilistic model, there was a reliable inverse relationship between decoded uncertainty and behavioral confidence (z = - 2.17, p = 0.015, &#x003c1; = -0.018, 95% CI = [-0.035, -0.0018]; <xref rid="F3" ref-type="fig">Fig. 3c</xref>, left). To further substantiate this result, we repeated the analysis while controlling for stimulus orientation (see <xref rid="S7" ref-type="sec">Methods</xref>).</p><p id="P23">This did not significantly reduce the strength of the observed relationship between the fidelity of the cortical stimulus representation and reported confidence (z = 0.45, p = 0.33, q = 0.0054, 95% CI = [-0.018, 0.029]; Fig. 3c, right), and again reached significance when using smaller numbers of voxels, which respond more strongly to the visual stimulus (<xref rid="F7" ref-type="fig">Extended Data Fig. 3</xref>). Thus, when the cortical representation of a stimulus is more precise, observers consistently report higher levels of confidence, as predicted by the Probabilistic model (and none of the other models). Control analyses verified that these results were robust to variations in the number of voxels selected for analysis (<xref rid="F7" ref-type="fig">Extended Data Fig. 3</xref>), and moreover, could not be explained by eye movement, position or blinks, nor by mean BOLD amplitude (<xref rid="F8" ref-type="fig">Extended Data Fig. 4</xref>). Taken together, these results suggest that human observers rely on a probabilistic representation of the quality of their sensory evidence when judging confidence.</p></sec><sec id="S5"><title>Sensory uncertainty and confidence in downstream areas</title><p id="P24">To further test the probabilistic confidence hypothesis, we next asked which downstream regions might read out the uncertainty contained in visual cortical activity so as to compute confidence. Based on our modeling work, we reasoned that if confidence is based on a probabilistic representation of the evidence, then we should be able to find downstream areas whose activity reflects sensory uncertainty, and predicts reported confidence, on a trial-by-trial basis. Specifically, we predicted an inverse relationship in activity between sensory uncertainty and confidence for these regions (cf. <xref rid="F2" ref-type="fig">Fig. 2d</xref>). Thus, under the probabilistic confidence hypothesis, cortical activity should not only increase (decrease) with reduced reliability of the observer&#x02019;s perceptual evidence, but also decrease (increase) when the observer reports greater levels of decision confidence.</p><p id="P25">We first focused on those areas that are driven by internal fluctuations in perceptual uncertainty. To identify candidate areas, we performed a whole-brain search. Specifically, we ran a general linear model (GLM) analysis in which we modeled the BOLD signal as a function of the degree of uncertainty decoded from visual cortical representations (in areas V1-V3), while controlling for differences in stimulus orientation (see <xref rid="S7" ref-type="sec">Methods</xref> for further details). We found several clusters downstream of visual cortex where neural activity reliably co-fluctuated with trial-by trial changes in decoded sensory uncertainty (see <xref rid="F4" ref-type="fig">Fig. 4a</xref> for an overview, <xref rid="SD4" ref-type="supplementary-material">Supplementary Data</xref> for whole-brain maps, and <xref rid="SD5" ref-type="supplementary-material">Supplementary Table 1</xref> for a list of clusters). This included the dorsal anterior insula (dAI), dorsal anterior cingulate cortex (dACC), and left rostrolateral prefrontal cortex (rlPFC) &#x02013; regions that are commonly associated with uncertainty<sup>
<xref rid="R22" ref-type="bibr">22</xref>
</sup> (dAI), volatility<sup>
<xref rid="R23" ref-type="bibr">23</xref>,<xref rid="R24" ref-type="bibr">24</xref>
</sup> (dACC) and metacognition<sup>
<xref rid="R25" ref-type="bibr">25</xref>
</sup> (rlPFC).</p><p id="P26">We next asked whether these uncertainty-tracking regions would also show a reliable opposite relationship to confidence in their activity, as predicted by the Probabilistic observer model. To address this question, we performed region-of-interest (ROI) analyses within the candidate regions identified by the above whole-brain analysis. First, individual ROIs were created by selecting all uncertainty-driven voxels within predefined anatomical masks corresponding to dAI<sup>
<xref rid="R26" ref-type="bibr">26</xref>
</sup>, dACC<sup>
<xref rid="R27" ref-type="bibr">27</xref>
</sup>, and left rlPFC<sup>
<xref rid="R28" ref-type="bibr">28</xref>
</sup>, using a leave-one-subject-out cross-validation procedure to avoid double dipping<sup>
<xref rid="R29" ref-type="bibr">29</xref>,<xref rid="R30" ref-type="bibr">30</xref>
</sup> (see <xref rid="S7" ref-type="sec">Methods</xref> for details, and <xref rid="F9" ref-type="fig">Extended Data Fig. 5</xref> for remaining clusters). For each subject, we then averaged the BOLD signal across all voxels within the ROI. To test whether BOLD activity was reliably modulated by the level of confidence reported by the observer, we performed a GLM analysis (see <xref rid="S7" ref-type="sec">Methods</xref> for model details). This revealed a significant effect of confidence on BOLD activity in all three regions (dAI: F(3,93) = 25.94, p &#x0003c; 0.001, R<sup>2</sup> = 0.26, 95% CI = [0.12, 0.41]; dACC: F(3,93) = 27.33, p &#x0003c; 0.001, R<sup>2</sup> = 0.25, 95% CI = [0.10, 0.39]; rlPFC: F(3,90) = 2.88, p = 0.040, R<sup>2</sup> = 0.01, 95% CI = [-0.03, 0.05]). Thus, it appears that neural activity in dAI, dACC, and rlPFC is affected by both the trial-by-trial imprecision in sensory evidence and the level of confidence reported by the observers.</p><p id="P27">Having established that activity in dAI, dACC and rlPFC is modulated by confidence, we next investigated the hypothesized inverse relationship between sensory uncertainty and reported confidence on the cortical response in these regions. To illustrate our approach, we first focus on a single ROI (dAI; <xref rid="F4" ref-type="fig">Fig. 4b</xref>). We computed, for each observer, the trial-by-trial correlation coefficient between decoded uncertainty and mean BOLD amplitude within the ROI (after removing the effect of stimulus orientation, see <xref rid="S7" ref-type="sec">Methods</xref>; see <xref rid="F4" ref-type="fig">Fig. 4c</xref> for an example observer), averaged the coefficients across observers (<xref rid="F4" ref-type="fig">Fig. 4d</xref>), and repeated the analysis over time (<xref rid="F4" ref-type="fig">Fig. 4e</xref>, left panel). We also performed the same analysis for reported confidence (<xref rid="F4" ref-type="fig">Fig. 4b-e</xref>), and dACC and rlPFC (<xref rid="F4" ref-type="fig">Fig. 4e</xref>). We discovered, in all three ROIs, a significant positive relationship between decoded uncertainty and cortical activity that was sustained over an extended period of time (<xref rid="F4" ref-type="fig">Fig. 4e</xref>; all p &#x0003c; 0.05, FWER-controlled). Critically, the effect on the cortical response was reversed for confidence (<xref rid="F4" ref-type="fig">Fig. 4e</xref>), and similarly held up over time (all p &#x0003c; 0.05, FWER-controlled). Thus, while the cortical response in dAI, dACC and rlPFC reliably increased with decoded uncertainty, activity in these regions consistently decreased with reported confidence, further corroborating the Bayesian confidence hypothesis. These effects could not be explained by trial-by-trial fluctuations in the participant&#x02019;s response time (see <xref rid="F10" ref-type="fig">Extended Data Fig. 6</xref> for control analyses). Especially interesting is that (in dACC and dAI) the positive correlation with uncertainty temporally preceded the negative correlation with reported confidence (dAI: t(31) = -3.05, p = 0.005, d = -0.54, 95% CI = [-0.90, -0.17]; dACC: t(31) = -2.72, p = 0.011, d = -0.39, 95% CI = [-0.75, - 0.032]; rlPFC: t(30) = -1.09, p = 0.29, d = -0.20, 95% CI = [-0.57, 0.17]); factoring in the (approximately 4-second) hemodynamic delay inherent in the BOLD response, the effect of sensory uncertainty appears to be roughly time-locked to the presentation (and neural processing) of the stimulus, while the correlation with reported confidence coincides with the time when subjects had to estimate their confidence. These distinct latencies furthermore suggest that decoded uncertainty and reported confidence exert (partially) independent effects on activity in these regions, which was confirmed by a partial correlation analysis (<xref rid="F11" ref-type="fig">Extended Data Fig. 7</xref>). Moreover, cortical activity in all three regions was additionally found to mediate the trial-by-trial relationship between decoded uncertainty and reported confidence (<xref rid="F12" ref-type="fig">Extended Data Fig. 8</xref>). This indicates that some of the variance in cortical activity is shared between sensory uncertainty and subjective confidence, and alludes to a direct functional role of these regions in the computation of confidence from sensory uncertainty.</p><p id="P28">Taken together, these results are consistent with the Bayesian confidence hypothesis, and suggest that dAI, dACC, and rlPFC are involved in the computation of confidence from a probabilistic representation of the quality of the observer&#x02019;s sensory evidence.</p></sec></sec><sec sec-type="discussion" id="S6"><title>Discussion</title><p id="P29">What computations give rise to the subjective sense of confidence? Here, we tested the Bayesian hypothesis that confidence is computed from a probabilistic representation of information in cortex. We first implemented a Bayesian (Probabilistic) observer model as well as two models using alternative strategies to confidence. This resulted in a set of predictions that we tested using psychophysics and fMRI. Corroborating the Bayesian model, we found that reported confidence reflects behavioral precision, even when stimulus properties such as orientation are held constant. Moreover, probability distributions decoded from population activity in visual cortex predict the level of confidence reported by the participant on a trial-by-trial basis. We furthermore identified three downstream regions, dACC, dAI and rlPFC, where BOLD activity is linked to both the width of the decoded distributions and reported confidence in ways consistent with the Bayesian observer model. Taken together, these findings support recent normative theories, and suggest that probabilistic information guides the computation of one&#x02019;s sense of confidence.</p><p id="P30">Earlier work on statistical confidence has manipulated evidence reliability by varying physical properties of the stimulus, such as its contrast. This left open the possibility that observers simply monitor these image features as a proxy for uncertainty<sup>
<xref rid="R7" ref-type="bibr">7</xref>,<xref rid="R10" ref-type="bibr">10</xref>&#x02013;<xref rid="R12" ref-type="bibr">12</xref>
</sup>, without considering an internal belief distribution over the latent variable. For this reason, we held stimulus properties constant, relied on fluctuations in internal noise, and extracted probability distributions directly from cortical activity. Our work shows that the uncertainty decoded from visual activity predicts the level of confidence reported by the observer. No less important, we find that downstream regions commonly associated with volatility in the environment<sup>
<xref rid="R23" ref-type="bibr">23</xref>,<xref rid="R24" ref-type="bibr">24</xref>
</sup>, decision-making<sup>
<xref rid="R22" ref-type="bibr">22</xref>,<xref rid="R31" ref-type="bibr">31</xref>
</sup>, and confidence<sup>
<xref rid="R25" ref-type="bibr">25</xref>,<xref rid="R32" ref-type="bibr">32</xref>,<xref rid="R33" ref-type="bibr">33</xref>
</sup>, represent trial-by-trial fluctuations in both this decoded uncertainty and reported confidence. Altogether, this strongly suggests that not stimulus heuristics or point estimates, but rather a probabilistic representation of information drives human confidence reports.</p><p id="P31">While decision confidence is usually studied in the context of binary decisions, we here focused on a continuous estimation task, which requires observers to reproduce a feature of the stimulus. For binary decisions, confidence is normatively defined as a function of the observer&#x02019;s measurement and decision boundary, in addition to sensory uncertainty, and each of these parameters can vary on a trial-by-trial basis due to internal noise. For the continuous estimation task used here, on the other hand, confidence is more straightforwardly defined as a function of sensory uncertainty, without many additional parameters. This definition makes this task ideally suited for addressing the probabilistic confidence hypothesis. While we specifically focused on uncertainty in continuous estimation, it seems nonetheless likely that the probabilistic nature of the representation will extend to binary choices and other decisions of increasing complexity.</p><p id="P32">Our findings are also important for understanding how uncertainty is represented in cortex. Previous work has shown that the width of the decoded probability distribution predicts the magnitude of behavioral orientation biases<sup>
<xref rid="R10" ref-type="bibr">10</xref>
</sup>, serial dependence effects in perception<sup>
<xref rid="R34" ref-type="bibr">34</xref>
</sup>, and classification decisions<sup>
<xref rid="R35" ref-type="bibr">35</xref>
</sup>. The current work extends these earlier findings by linking the decoded distributions directly to activity in downstream decision areas and the subjective level of confidence reported by the observer. Taken together, these findings suggest that probability distributions are not only represented in neural population activity, but also used in the brain&#x02019;s computations.</p><p id="P33">Earlier work has implicated the involvement of the dACC, dAI, and rlPFC in experimental (objective) manipulations of evidence reliability<sup>
<xref rid="R23" ref-type="bibr">23</xref>,<xref rid="R33" ref-type="bibr">33</xref>,<xref rid="R36" ref-type="bibr">36</xref>&#x02013;<xref rid="R38" ref-type="bibr">38</xref>
</sup>. Our results suggest that these regions similarly track spontaneous (internal) fluctuations in uncertainty and moreover mediate the link between sensory uncertainty and reported confidence, further elucidating their functional role in human decision-making. Thus, it appears that a more general notion of uncertainty is represented in these regions, albeit for different functional purposes. While the representation in dACC may serve to inform internal models and response selection<sup>
<xref rid="R39" ref-type="bibr">39</xref>&#x02013;<xref rid="R42" ref-type="bibr">42</xref>
</sup>, it seems likely that dAI integrates uncertainty with interoceptive and affective information to form a general subjective feeling state<sup>
<xref rid="R22" ref-type="bibr">22</xref>
</sup>. rlPFC, on the other hand, likely plays a key role in the integration of internal uncertainty with contextual information to compute confidence<sup>
<xref rid="R32" ref-type="bibr">32</xref>,<xref rid="R36" ref-type="bibr">36</xref>,<xref rid="R43" ref-type="bibr">43</xref>&#x02013;<xref rid="R45" ref-type="bibr">45</xref>
</sup>.</p><p id="P34">While we here focused on the link between subjective confidence and the precision of early sensory evidence in visual areas, confidence should additionally reflect uncertainty added by later stages of processing; for instance, when the item is held in visual working memory before observers make their judgment (although the impact of memory imprecision might be relatively small<sup>
<xref rid="R46" ref-type="bibr">46</xref>
</sup>). Indeed, our ideal observer model incorporates these sources of variance in its estimates of confidence (cf. equations 1, 8 and 9, &#x003c3;<sub>
<italic toggle="yes">n</italic>
</sub>
<sup>2</sup>). Given the involvement of early visual areas in visual working memory<sup>
<xref rid="R47" ref-type="bibr">47</xref>,<xref rid="R48" ref-type="bibr">48</xref>
</sup>, this predicts that the imprecision in the visual cortical representation during the delay period between stimulus presentation and response should predict the level of confidence reported by the observer, as well. Interestingly, additional analysis of the empirical data revealed that uncertainty decoded from signals during the retention interval indeed reliably predicted subjective levels of confidence (<xref rid="F13" ref-type="fig">Extended Data Fig. 9</xref>). Although our design does not warrant strong conclusions regarding the nature of these signals (see <xref rid="F13" ref-type="fig">Extended Data Fig. 9</xref> for discussion), these findings are consistent with a model that considers additional sources of variance when judging confidence. Corroborating this interpretation, a very recent study used our probabilistic decoding techniques and found that decoded uncertainty predicted the observer&#x02019;s judgments of uncertainty in a visual working memory task<sup>
<xref rid="R49" ref-type="bibr">49</xref>
</sup>. It will be interesting for future work to further disentangle these and other sources of noise that affect the observer&#x02019;s decisions and associated levels of confidence.</p><p id="P35">In conclusion, we showed that behavioral confidence tracks the degree of uncertainty contained in neural population activity in visual cortex, suggesting that human observers have access to and can report about the degree of imprecision in their visual cortical representations of the stimulus. Furthermore, activity in the dACC, dAI and rlPFC is modulated by both this uncertainty and reported confidence in ways predicted by the Bayesian model, suggesting that these regions are involved in the computation of confidence from sensory uncertainty. Taken together, the current results support recent normative theories of confidence and suggest that the subjective feeling of confidence is based on a statistical measure of the quality of one&#x02019;s evidence.</p></sec><sec sec-type="methods" id="S7"><title>Methods</title><sec sec-type="subjects" id="S8"><title>Participants</title><p id="P36">32 healthy adult volunteers (age range 19-31, 20 female, 12 male) with normal or corrected-to-normal vision participated in this study. Sample size (N=32) was based on a power calculation (power = 0.8; &#x003b1; = 0.05). All participants gave informed written consent prior to, and received monetary compensation for, their participation (8 and 10 euros per hour for behavioral and fMRI sessions, respectively). The study was approved by the local ethics committee (CMO Arnhem-Nijmegen, the Netherlands). Participants were included based on their ability to perform the task, which was assessed in a separate behavioral training session prior to the experimental sessions.</p></sec><sec id="S9"><title>Imaging data acquisition</title><p id="P37">MRI data were acquired on a Siemens 3T MAGNETOM PrismaFit scanner at the Donders Center for Cognitive Neuroimaging, using a 32-channel head coil. For anatomical reference, a high-resolution T1-weighted image was collected at the start of each session (3D MPRAGE, TR: 2300 ms, TI: 1100 ms, TE: 3 ms, flip angle: 8 degrees, FOV: 256 x 256 mm, 192 saggital slices, 1-mm isotropic voxels). B0 field inhomogeneity maps (TR: 653 ms, TE: 4.92 ms, flip angle: 60 degrees, FOV: 256 x 256 mm, 68 transversal slices, 2-mm isotropic voxels, interleaved slice acquisition) were acquired. Functional data were acquired using a multi-band accelerated gradient-echo EPI protocol, in 68 transversal slices covering the whole brain (TR: 1500 ms, TE: 38.60 ms, flip angle: 75 degrees, FOV: 210 x 210 mm, 2-mm isotropic voxels, multiband acceleration factor: 4, interleaved slice acquisition).</p></sec><sec id="S10"><title>Experimental design and stimuli</title><p id="P38">Participants performed an orientation estimation task while their cortical activity was measured with fMRI. They completed a total of 22-26 task runs, divided over two scan sessions on separate days. Prior to the experimental sessions, participants extensively practiced the task (2-4 hours) in a separate behavioral session.</p><p id="P39">Throughout each task run, participants fixated a bull&#x02019;s eye target (radius: 0.375 degrees) presented at the center of the screen. Each run consisted of 20 trials (16.5 <italic toggle="yes">s</italic> each), separated by an inter-trial interval of 1.5 <italic toggle="yes">s</italic>, and started and ended with a fixation period (duration at start: 4.5 <italic toggle="yes">s</italic>; at end: 15 <italic toggle="yes">s</italic>). Each trial started with the presentation of the orientation stimulus, which remained on the screen for 1.5 <italic toggle="yes">s</italic>. This was followed by a 6-<italic toggle="yes">s</italic> fixation interval, and then two successive 4.5-<italic toggle="yes">s</italic> response windows (<xref rid="F5" ref-type="fig">Extended Data Fig. 1</xref>). Orientation stimuli were counterphasing sinusoidal gratings (contrast: 10%, spatial frequency: 1 cycle per degree, randomized spatial phase, 2-Hz sinusoidal contrast modulation) presented in an annulus around fixation (inner radius: 1.5 degrees, outer radius: 7.5 degrees, grating contrast decreased linearly to 0 over the inner and outer 0.5 degrees of the radius of the annulus). Stimulus orientations were drawn (pseudo)randomly from a uniform distribution covering the full orientation space (0-179 degrees) to ensure an approximately even sampling of orientations within each run. At the start of the first response window, a black bar (length: 2.8 degrees, width: 0.1 degrees, contrast: 40%) appeared at the center of the screen at an initially random orientation. Subjects reported the orientation of the previously seen grating by rotating this bar, using separate buttons for clockwise and counterclockwise rotation on an MRI-compatible button box. At the start of the second response window, a black bar of increasing width (contrast: 40%, bar width: 0.1-0.5 degrees, linearly increasing) and wrapped around fixation (radius 1.4 degrees) became visible at the center of the screen. Participants indicated their confidence in their orientation judgement by moving a white dot (contrast: 40%, radius: 0.05 degrees) on this continuous confidence scale, using the same buttons for clockwise and counterclockwise as for their orientation response. The mapping of confidence level to scale width (i.e. whether the narrow end of the scale indicated high or low confidence) was counterbalanced across participants. The scale&#x02019;s orientation and direction (i.e. width increasing in clockwise or counterclockwise direction), as well as the starting position of the dot, were randomized across trials. For both response windows, the bar (scale) disappeared gradually over the last 1 <italic toggle="yes">s</italic> of the response window to indicate the approaching end of this window. Shortly before trial onset (0.5 s), the fixation bull&#x02019;s eye briefly turned black (duration: 0.1 s) to indicate the start of the trial. Because we were interested in the effects of sensory uncertainty on cortical activity and confidence, rather than the cortical representation of confidence <italic toggle="yes">per se</italic>, and moreover, reward-related signals might contaminate the representation of sensory information in visual areas<sup>
<xref rid="R50" ref-type="bibr">50</xref>
</sup>, participants received no trial-by-trial feedback about the accuracy of their judgments.</p><p id="P40">Each scan session also included 1 or 2 functional localizer runs, during which flickering checkerboard stimuli were presented in seven 12-<italic toggle="yes">s</italic> blocks interleaved with fixation blocks of equal duration. The checkerboard stimuli were presented within the same aperture as the grating stimuli (contrast: 100%, flicker frequency: 10 Hz, check size: 0.5 degrees). Retinotopic maps of the visual cortex were acquired in a separate scan session using standard retinotopic mapping procedures<sup>
<xref rid="R51" ref-type="bibr">51</xref>&#x02013;<xref rid="R53" ref-type="bibr">53</xref>
</sup>.</p><p id="P41">All visual stimuli were generated on a Macbook Pro computer using Matlab and the Psychophysics Toolbox<sup>
<xref rid="R54" ref-type="bibr">54</xref>
</sup>, and were presented on a rear-projection screen using a luminance-calibrated EIKI LC-XL100 projector (screen resolution: 1024 x 768 pixels, refresh rate: 60 Hz). Participants viewed the screen through a mirror mounted on the head coil.</p></sec><sec id="S11"><title>Behavioral data analysis</title><p id="P42">In general, participants finished adjusting their orientation and confidence responses well before the end of the response windows (4.5 <italic toggle="yes">s</italic> each), taking on average 2761 &#x000b1; 378 ms (mean &#x000b1; S.D. across observers) for the orientation response and 2587 &#x000b1; 313 ms for the confidence response. Trials on which participants did not finish their response by the end of the response window were excluded from further analyses (0-43 out of 440-520 trials). The error in the observer&#x02019;s behavioral orientation response was computed as the acute-angle difference between the reported and the presented orientation on a given trial. Orientation-dependent shifts (biases) in mean behavioral error were removed by fitting two fourth-degree polynomials to each observer&#x02019;s behavioral errors as a function of stimulus orientation (see <sup>
<xref rid="R10" ref-type="bibr">10</xref>
</sup> for a similar procedure). One polynomial was fit to trials for which the presented stimulus orientation was between 90 and 179 degrees, and the second polynomial was fit to trials on which the presented stimulus orientation was between 0 and 89 degrees. We used the bias-corrected behavioral errors, i.e. the residuals of this fit, in subsequent analyses. Behavioral errors that were more than three standard deviations away from the mean of each participant (after bias correction) were marked as guesses and excluded from further analysis (1-7 out of 440-520 trials). To remove potential session- and subject-specific differences in usage of the confidence scale, confidence ratings were z-scored within sessions.</p></sec><sec id="S12"><title>Preprocessing of MRI data</title><p id="P43">The raw functional imaging data were motion-corrected with respect to the middle volume of the middle run of the session, using FSL&#x02019;s MCFLIRT<sup>
<xref rid="R55" ref-type="bibr">55</xref>
</sup>. The functional data were corrected for distortion using the within-session B<sub>0</sub> fieldmap, and aligned to the T1-weighted image obtained during the same scan session. This anatomical (T1-weighted) image was aligned with a subject-specific unbiased template image, created by combining the T1-weighted images from the two sessions, using Freesurfer&#x02019;s mri_robust_template<sup>
<xref rid="R56" ref-type="bibr">56</xref>
</sup>. Slow drifts in the BOLD signal were removed using FSL&#x02019;s nonlinear high-pass temporal filter with a sigma of 24 TRs (two trials), corresponding to a cut-off period of approximately 83 seconds.</p><p id="P44">For all univariate analyses, additional preprocessing steps were performed prior to high-pass filtering. Specifically, non-brain structures were removed using FSL&#x02019;s BET<sup>
<xref rid="R57" ref-type="bibr">57</xref>
</sup>, and the data were spatially smoothed with a 6-mm Gaussian kernel using FSL&#x02019;s SUSAN<sup>
<xref rid="R58" ref-type="bibr">58</xref>
</sup>. As the univariate analyses required combining data across subjects, each subject&#x02019;s anatomical template image was non-linearly registered to MNI152 space using FSL&#x02019;s FNIRT with a warp resolution of 10 mm isotropic<sup>
<xref rid="R59" ref-type="bibr">59</xref>
</sup>.</p><p id="P45">A set of nuisance regressors was used to remove residual motion effects and global fluctuations in the BOLD signal. Per session, we defined an intercept regressor per run, 24 motion regressors based on the motion parameters estimated by MCFLIRT (all analyses), and two regressors reflecting the average signal in cerebrospinal fluid (CSF) and white matter (WM) (univariate analyses only). The CSF and WM regressors served to capture global fluctuations in signal intensity and were obtained by first creating WM and CSF masks based on the subject&#x02019;s anatomical scan data using FSL&#x02019;s FAST <sup>
<xref rid="R60" ref-type="bibr">60</xref>
</sup>, and then removing the outer edges from these masks to exclude voxels at the tissue boundaries. For the multivariate and ROI-based univariate analyses, nuisance signals were removed from the BOLD signal prior to further analyses. For the whole-brain univariate analysis, motion, CSF/WM, and intercept regressors were included as covariates in the general linear model (see Whole-brain analysis).</p><p id="P46">For the multivariate analyses, the ROI (consisting of V1, V2, and V3) was identified on the reconstructed cortical surface. Within this ROI, and in the native space of each participant, we selected for further analysis the 2000 voxels that were activated most strongly by the functional localizer stimulus while surviving a lenient statistical threshold (p &#x0003c; 0.01, uncorrected). Control analyses verified that our results were not strongly affected by the number of voxels selected for analysis (<xref rid="F7" ref-type="fig">Extended Data Fig. 3</xref>). The time series of each selected voxel was subsequently z-normalized with respect to corresponding trial time points in the same run. Activation patterns for each trial were obtained by averaging over the first 3 <italic toggle="yes">s</italic> of each trial, after adding a 4.5-<italic toggle="yes">s</italic> temporal shift to account for hemodynamic delay. This relatively short time window was chosen so as to ensure that activity from the behavioral response window was excluded from analysis. For the control analyses of <xref rid="F8" ref-type="fig">Extended Data Fig. 4</xref>, mean BOLD intensity values were calculated by averaging the z-normalized activation values across the selected voxels and time window. The results of <xref rid="F13" ref-type="fig">Extended Data Fig. 9</xref> were obtained using a sliding window of size 3 <italic toggle="yes">s</italic> (2 TRs), chosen so as to match the window size of the main analysis. For each window of analysis, the z-normalized activation values were averaged and subsequently fed to the decoding algorithm.</p></sec><sec id="S13"><title>Multivariate analysis (visual cortex)</title><sec id="S14"><title>Decoding algorithm</title><p id="P47">Trial-by-trial uncertainty in cortical stimulus representations was computed using a generative model-based, probabilistic decoding algorithm<sup>
<xref rid="R10" ref-type="bibr">10</xref>,<xref rid="R18" ref-type="bibr">18</xref>
</sup>, applied to selected voxels in visual cortex (see previous section for voxel selection criteria). The model describes the generative distribution of the voxel activity patterns given a certain stimulus, <italic toggle="yes">p</italic>(<bold>b</bold>|<italic toggle="yes">s</italic>); in other words, the probability that stimulus <italic toggle="yes">s</italic> will evoke activation pattern <bold>b</bold>. The model assumes that, across trials, voxel activity follows a multivariate Normal distribution around the voxel&#x02019;s tuning curve for orientation. Voxel tuning curves are defined as a linear combination <italic toggle="yes">
<bold>Wf</bold>
</italic>(<italic toggle="yes">s</italic>) of 8 bell-shaped basis functions, each centered on a different orientation (cf. <sup>
<xref rid="R61" ref-type="bibr">61</xref>
</sup>):
<disp-formula id="FD4">
<label>#(4)</label>
<mml:math id="M4" display="block" overflow="scroll"><mml:msub><mml:mi>f</mml:mi><mml:mi>k</mml:mi></mml:msub><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi>max</mml:mi><mml:msup><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mi>cos</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mn>2</mml:mn><mml:mi>&#x003c0;</mml:mi><mml:mfrac><mml:mrow><mml:mi>s</mml:mi><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>&#x003c6;</mml:mi><mml:mi>k</mml:mi></mml:msub></mml:mrow><mml:mrow><mml:mn>180</mml:mn></mml:mrow></mml:mfrac></mml:mrow><mml:mo>)</mml:mo></mml:mrow></mml:mrow><mml:mo>)</mml:mo></mml:mrow><mml:mn>5</mml:mn></mml:msup></mml:math>
</disp-formula> where <italic toggle="yes">s</italic> is the orientation of the presented stimulus and <italic toggle="yes">&#x003c6;<sub>k</sub>
</italic> is the preferred orientation of the <italic toggle="yes">k</italic>-th population. Basis functions were spaced equally across the full orientation space (0-179 degrees) with the first centered at zero degrees. <italic toggle="yes">W<sub>ik</sub>
</italic> is the contribution of the <italic toggle="yes">k</italic>-th basis function to the response of the <italic toggle="yes">i</italic>-th voxel.</p><p id="P48">The covariance around the voxel tuning curves is described by noise covariance matrix <bold>&#x003a9;</bold>:
<disp-formula id="FD5">
<label>#(5)</label>
<mml:math id="M5" display="block" overflow="scroll"><mml:mi mathvariant="bold">&#x003a9;</mml:mi><mml:mo>=</mml:mo><mml:mi>&#x003c1;</mml:mi><mml:mi mathvariant="bold">&#x003c4;</mml:mi><mml:msup><mml:mi mathvariant="bold">&#x003c4;</mml:mi><mml:mtext>T</mml:mtext></mml:msup><mml:mo>+</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:mi>&#x003c1;</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mstyle mathvariant="bold" mathsize="normal"><mml:mi>I</mml:mi></mml:mstyle><mml:mo>&#x02218;</mml:mo><mml:mi mathvariant="bold">&#x003c4;</mml:mi><mml:msup><mml:mi mathvariant="bold">&#x003c4;</mml:mi><mml:mtext>T</mml:mtext></mml:msup><mml:mo>+</mml:mo><mml:msup><mml:mi>&#x003c3;</mml:mi><mml:mn>2</mml:mn></mml:msup><mml:mstyle mathvariant="bold" mathsize="normal"><mml:mi>W</mml:mi></mml:mstyle><mml:msup><mml:mstyle mathvariant="bold" mathsize="normal"><mml:mi>W</mml:mi></mml:mstyle><mml:mtext>T</mml:mtext></mml:msup></mml:math>
</disp-formula>
</p><p id="P49">The first term of this covariance matrix describes noise shared globally between all voxels in the ROI, and the second term refers to noise specific to individual voxels (with variance <italic toggle="yes">&#x003c4;</italic>
<sub>i</sub>
<sup>2</sup> for voxel <italic toggle="yes">i</italic>). The relative contribution of each of these types of noise is reflected in <italic toggle="yes">&#x003c1;</italic>. The third term models tuning-dependent noise, i.e. noise, with variance <italic toggle="yes">&#x003c3;</italic>
<sup>2</sup>, shared between voxels with similar orientation preference.</p><p id="P50">Thus, the generative distribution of voxel responses is given by a multivariate Normal with mean <italic toggle="yes">
<bold>Wf</bold>
</italic>(<italic toggle="yes">s</italic>) and covariance <bold>&#x003a9;</bold>: <disp-formula id="FD6">
<label>#(6)</label>
<mml:math id="M6" display="block" overflow="scroll"><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mstyle mathvariant="bold" mathsize="normal"><mml:mi>b</mml:mi></mml:mstyle><mml:mo>&#x02223;</mml:mo><mml:mi>s</mml:mi><mml:mo>;</mml:mo><mml:mtext>&#x003b8;</mml:mtext><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi mathvariant="script">N</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mstyle mathvariant="bold" mathsize="normal"><mml:mtext>W</mml:mtext><mml:mi>f</mml:mi></mml:mstyle><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>,</mml:mo><mml:mstyle mathvariant="bold" mathsize="normal"><mml:mi>&#x003a9;</mml:mi></mml:mstyle><mml:mo stretchy="false">)</mml:mo></mml:math>
</disp-formula> where <bold>&#x003b8;</bold> = {<bold>W</bold>, <bold>&#x003c4;</bold>,<italic toggle="yes">p</italic>, <italic toggle="yes">&#x003c3;</italic>} are the model&#x02019;s parameters. The model&#x02019;s parameters were estimated in a two-step procedure (see <sup>
<xref rid="R10" ref-type="bibr">10</xref>
</sup> for further details). First, the tuning weights <bold>W</bold> were estimated by ordinary least squares regression. In the second step, the noise covariance parameters (<italic toggle="yes">&#x003c1;</italic>, <italic toggle="yes">&#x003c3;</italic>, <bold>&#x003c4;</bold>) were estimated by numerical maximization of their likelihood.</p><p id="P51">Model training and testing (&#x02018;decoding&#x02019;) was performed following a leave-one-run-out cross-validation procedure to prevent double-dipping<sup>
<xref rid="R29" ref-type="bibr">29</xref>
</sup>. That is, model parameters were first fit to a training dataset consisting of all but one fMRI run, and the model was then tested on the data from the remaining run. This procedure was repeated until all runs had served as a test set once.</p><p id="P52">Using the fitted parameters, a posterior distribution over stimulus orientation was computed for each trial in the test set. The posterior distribution is given by Bayes&#x02019; rule: <disp-formula id="FD7">
<label>#(7)</label>
<mml:math id="M7" display="block" overflow="scroll"><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02223;</mml:mo><mml:mstyle mathvariant="bold" mathsize="normal"><mml:mi>b</mml:mi></mml:mstyle><mml:mo>;</mml:mo><mml:mover accent="true"><mml:mi mathvariant="bold">&#x003b8;</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mstyle mathvariant="bold" mathsize="normal"><mml:mi>b</mml:mi></mml:mstyle><mml:mo>&#x02223;</mml:mo><mml:mi>s</mml:mi><mml:mo>;</mml:mo><mml:mover accent="true"><mml:mi mathvariant="bold">&#x003b8;</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mo stretchy="false">)</mml:mo><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mrow><mml:msup><mml:mstyle displaystyle="true"><mml:mo>&#x0222b;</mml:mo></mml:mstyle><mml:mtext>&#x0200b;</mml:mtext></mml:msup><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mstyle mathvariant="bold" mathsize="normal"><mml:mi>b</mml:mi></mml:mstyle><mml:mo>&#x02223;</mml:mo><mml:mi>s</mml:mi><mml:mo>;</mml:mo><mml:mover accent="true"><mml:mi mathvariant="bold">&#x003b8;</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mo stretchy="false">)</mml:mo><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mi>d</mml:mi><mml:mi>s</mml:mi></mml:mrow></mml:mfrac></mml:math>
</disp-formula> where <inline-formula>
<mml:math id="M8" display="inline" overflow="scroll"><mml:mover accent="true"><mml:mi mathvariant="bold">&#x003b8;</mml:mi><mml:mo>^</mml:mo></mml:mover></mml:math>
</inline-formula> are the estimated model parameters. The stimulus prior <italic toggle="yes">p</italic>(<italic toggle="yes">s</italic>) was flat, given that the stimuli presented in the experiment were uniformly distributed, and the normalizing constant in the denominator was calculated numerically. The circular mean of the posterior distribution was taken as the estimate of the presented orientation on that test trial, and the squared circular standard deviation was used as a measure of the amount of uncertainty in this estimate.</p></sec></sec><sec id="S15"><title>Statistical procedures</title><p id="P53">Most of our analyses relied on the computation of a correlation coefficient between two variables. These coefficients were calculated for each individual participant, and then averaged across observers (see below). Based on the assumed relationship between the two variables (linear or monotonic), either Pearson&#x02019;s or Spearman&#x02019;s (rank) correlation coefficient was computed. For the analyses using Pearson&#x02019;s correlation coefficient (<xref rid="F3" ref-type="fig">Fig. 3a</xref> and <xref rid="F6" ref-type="fig">Extended Data Fig. 2c-d</xref>), the data were visually inspected and appeared to be normally distributed, although this was not formally tested. Decoding accuracy was quantified by computing the circular analog of the Pearson correlation coefficient between the presented and decoded stimulus orientation. To test for an oblique effect in decoded uncertainty, we first calculated for each presented stimulus orientation its distance to the nearest cardinal (i.e. horizontal or vertical) orientation, and then computed the Spearman correlation coefficient between this measure and decoded uncertainty. To test the relationship between reported confidence and decoded uncertainty (independent of stimulus orientation), we first removed orientation-dependent shifts in decoded uncertainty and confidence by modeling confidence (decoded uncertainty) as a quadratic (linear) function of distance to cardinal (see <xref rid="F14" ref-type="fig">Extended Data Fig. 10</xref> for fitted functions); the rank correlation coefficient between confidence and decoded uncertainty was subsequently computed on the residuals of these fitted functions. After obtaining correlation coefficients for each individual observer <italic toggle="yes">i</italic>, the coefficients were Fisher transformed and a weighted average was computed across observers. Specifically, the weight of the <italic toggle="yes">i</italic>-th correlation coefficient was calculated as <italic toggle="yes">w<sub>i</sub>
</italic> = 1/<italic toggle="yes">v<sub>i</sub>
</italic>, where <italic toggle="yes">v</italic>
<sub>
<italic toggle="yes">i</italic>
</sub> is the variance of the Fisher transformed correlation coefficient<sup>
<xref rid="R62" ref-type="bibr">62</xref>
</sup>. For the Pearson correlation, <italic toggle="yes">v</italic>
<sub>
<italic toggle="yes">i</italic>
</sub> is given by 1/(<italic toggle="yes">n<sub>i</sub>
</italic> - 3) (where n<sub>
<italic toggle="yes">i</italic>
</sub> is the number of trials), and for the Spearman correlation <italic toggle="yes">v</italic>
<sub>
<italic toggle="yes">i</italic>
</sub> = 1.06/(<italic toggle="yes">n<sub>i</sub>
</italic> - 3)<sup>
<xref rid="R63" ref-type="bibr">63</xref>
</sup>. Weights were adjusted for the additional degrees of freedom lost due to stepwise correction for the oblique effect in decoded uncertainty or reported confidence by subtracting 1 (for linear correction) or 2 (for quadratic correction) from the denominator in the variance term. The significance of the coefficients was assessed using a Z-test, testing specifically for effects in the direction predicted by the ideal observer models. The average of the Z-transformed values was translated back to the correlation scale for reporting purposes. To compare correlation coefficients between confidence and decoded uncertainty with and without correction for stimulus orientation, we computed Cohen&#x02019;s q and used a Z-test to assess statistical significance. Similar procedures were used for the control analyses of <xref rid="F8" ref-type="fig">Extended Data Fig. 4</xref>. For these control analyses, we additionally performed equivalence tests (using the two one-sided tests procedure<sup>
<xref rid="R64" ref-type="bibr">64</xref>
</sup>), comparing against a smallest effect size of interest of &#x003c1; = 0.1 (see <sup>
<xref rid="R65" ref-type="bibr">65</xref>
</sup> for further rationale).</p><p id="P54">Some of our analyses required the computation of a dispersion measure (i.e., behavioral variability). For these analyses, each participant&#x02019;s data were first divided into ten equal-size bins, based on either reported level of confidence or decoded uncertainty, and summary statistics were computed across all trials in a given bin. Behavioral variability was computed as the squared circular standard deviation of (bias-corrected) estimation errors across all trials in each bin, and the average level of confidence or decoded uncertainty was quantified by computing the statistical mean. To test the relationship between behavioral variability and confidence (or decoded uncertainty), we used a multiple linear regression analysis. Independent variables were level of confidence (or decoded uncertainty), and the absolute distance between the stimulus and the nearest cardinal axis (mean across trials in each bin). We also included subject-specific intercepts. The dependent variable was behavioral variability. Partial correlation coefficients were computed from the binned data and significance was assessed using t-tests, testing for effects in the direction predicted by the ideal observer models. Control analyses verified that our results did not strongly depend on the number of used bins, nor on the specific shape of the function used to model the effect of stimulus orientation on confidence (or decoded uncertainty).</p></sec><sec id="S16"><title>Univariate analyses (whole-brain and ROI-based)</title><sec id="S17"><title>Whole-brain analysis</title><p id="P55">To identify brain regions that are modulated by sensory uncertainty, we used a whole-brain general linear model (GLM) approach. A GLM can be written as <italic toggle="yes">
<bold>y</bold> = <bold>X&#x003b2;</bold>
</italic> + <italic toggle="yes">
<bold>&#x003b5;</bold>
</italic>, where <italic toggle="yes">
<bold>y</bold>
</italic> represents the timeseries of a single voxel, <bold>X</bold> is referred to as the design matrix (or model), <italic toggle="yes">
<bold>&#x003b2;</bold>
</italic> is a vector of model parameters, and <italic toggle="yes">
<bold>&#x003b5;</bold>
</italic> represents the residuals.</p><p id="P56">We constructed a model of task-related activity based on three components: 1) a 1.5-<italic toggle="yes">s</italic> boxcar function time-locked to the stimulus onsets of all excluded trials, with height one, 2) a 1.5-<italic toggle="yes">s</italic> boxcar function time-locked to the stimulus onsets of all included trials, with height one, 3) a 1.5-<italic toggle="yes">s</italic> boxcar function time-locked to the stimulus onsets of all included trials, with its height equal to the decoded uncertainty on that trial (linearly corrected for trial-by-trial differences in stimulus orientation, cf. Multivariate analysis). Each boxcar function was convolved with a canonical hemodynamic response function (HRF) and temporal and dispersion derivatives of the HRF (SPM&#x02019;s informed basis set), yielding a total of nine regressors to include in the design matrix. The derivatives were added for additional model flexibility regarding the shape and latency of the BOLD response. In addition to the task-related regressors, we further included nuisance regressors (24 motion regressors and 2 CSF/WM regressors per session) and run-specific intercepts (see Preprocessing of fMRI data) to improve overall model fit.</p><p id="P58">The model <bold>X</bold> was fit to each subject&#x02019;s timeseries, separately for each voxel, to obtain a set of parameter estimates <inline-formula>
<mml:math id="M9" display="inline" overflow="scroll"><mml:mover accent="true"><mml:mi mathvariant="bold">&#x003b2;</mml:mi><mml:mo>^</mml:mo></mml:mover></mml:math>
</inline-formula>. Subject-level analyses were performed using SPM12, because of its increased efficiency (relative to FSL) when performing the GLM analysis on concatenated data rather than individual runs. The resulting subject-level <inline-formula>
<mml:math id="M10" display="inline" overflow="scroll"><mml:mover accent="true"><mml:mi mathvariant="bold">&#x003b2;</mml:mi><mml:mo>^</mml:mo></mml:mover></mml:math>
</inline-formula> maps were then transformed from subject-specific to standard space (MNI152) to allow for comparison and combination of estimates across subjects. We were specifically interested in the effect of decoded uncertainty on the BOLD response, which was modeled by the three regressors corresponding to the third boxcar function. The combined explanatory power of the three regressors was quantified by computing an F-statistic over the corresponding <italic toggle="yes">
<bold>&#x003b2;</bold>
</italic> estimates (across subjects). To calculate p-values, a sign-flip test (5000 permutations) was performed in combination with threshold-free cluster enhancement (TFCE)<sup>
<xref rid="R66" ref-type="bibr">66</xref>
</sup>, using FSL&#x02019;s randomise<sup>
<xref rid="R67" ref-type="bibr">67</xref>
</sup>. The family-wise error rate (FWER) was controlled by comparing the true voxel-wise TFCE scores against the null distribution of the maximum TFCE score across voxels<sup>
<xref rid="R66" ref-type="bibr">66</xref>,<xref rid="R68" ref-type="bibr">68</xref>
</sup>.</p></sec><sec id="S18"><title>ROI analysis</title><p id="P59">Brain regions modulated by perceptual uncertainty were selected and further investigated as follows. ROIs were defined using existing anatomical atlases, combined with a functional parcellation based on the whole-brain GLM analysis (as described in more detail above). Specifically, within a given (anatomical) ROI, we selected voxels modulated by decoded uncertainty using the GLM analysis, while applying a leave-one-subject-out procedure<sup>
<xref rid="R30" ref-type="bibr">30</xref>
</sup> to avoid double-dipping<sup>
<xref rid="R29" ref-type="bibr">29</xref>
</sup>. This led to the definition of eight ROIs, for each participant individually: 1) dorsal anterior insula (using the functional parcellation by Chang et al.<sup>
<xref rid="R26" ref-type="bibr">26</xref>
</sup>, mirrored to obtain bilateral labels, retrieved from Neurovault: <ext-link xlink:href="https://identifiers.org/neurovault.collection:13" ext-link-type="uri">https://identifiers.org/neurovault.collection:13</ext-link>), 2) left rostrolateral prefrontal cortex (frontal pole label, Harvard-Oxford cortical atlas<sup>
<xref rid="R28" ref-type="bibr">28</xref>
</sup>, trimmed to include the left hemisphere only), 3) dorsal anterior cingulate cortex (bilateral RCZa and RCZp labels, Neubert cingulate orbitofrontal connectivity-based parcellation<sup>
<xref rid="R27" ref-type="bibr">27</xref>
</sup>), 4) precuneus (precuneus label, Harvard-Oxford cortical atlas<sup>
<xref rid="R28" ref-type="bibr">28</xref>
</sup>), 5) supplementary motor area (SMA label, Sallet dorsal frontal connectivity-based parcellation<sup>
<xref rid="R69" ref-type="bibr">69</xref>
</sup>, mirrored to obtain bilateral labels), 6) dorsal perigenual anterior cingulate cortex (bilateral area 32d, Neubert cingulate orbitofrontal connectivity-based parcellation<sup>
<xref rid="R27" ref-type="bibr">27</xref>
</sup>), 7) ventral posterior cingulate cortex (bilateral area 23ab labels, Neubert cingulate orbitofrontal connectivity-based parcellation<sup>
<xref rid="R27" ref-type="bibr">27</xref>
</sup>), 8) dorsal posterior cingulate cortex (bilateral CCZ labels, Neubert cingulate orbitofrontal connectivity-based parcellation<sup>
<xref rid="R27" ref-type="bibr">27</xref>
</sup>). For ROIs 2 and 8, some of the leave-one-out, GLM-based masks did not contain any voxels. The corresponding data were excluded from further analyses for the respective ROIs (for ROI 2: 1 subject, ROI 8: 2 subjects). The BOLD signal was averaged over all voxels within a given ROI.</p><p id="P60">Having defined our ROIs, we then proceeded to investigate the effects of confidence in these regions. We did this in two different analyses. To assess the degree to which confidence modulated the BOLD response in each ROIs, we performed a GLM analysis. The model structure was similar to the whole-brain univariate analysis, including three 1.5-<italic toggle="yes">s</italic> boxcar functions time-locked to stimulus onset: one for excluded trials (height one), one for included trials (height one), and one to model the effect of confidence (included trials only; height equal to confidence value on that trial, quadratically corrected for trial-by-trial differences in stimulus orientation, cf. Multivariate analysis). These boxcar functions were each convolved with SPM&#x02019;s informed basis set (canonical HRF and its temporal and dispersion derivatives), and nuisance regressors (24 motion and 2 CSF/WM regressors per session). Run intercepts were also added.</p><p id="P61">To further investigate the magnitude and directionality of effects of reported confidence and decoded uncertainty over the course of a trial (without a priori assumptions regarding the shape or timing of the BOLD response), we also performed a trial-by-trial correlation analysis. Specifically, we computed the Spearman correlation coefficient between BOLD intensity and decoded uncertainty or reported confidence for each TR in the trial. Orientation-dependent changes in decoded uncertainty and confidence were first removed by modeling confidence (decoded uncertainty) as a quadratic (linear) function of distance to cardinal (cf. Multivariate analysis), and the correlation coefficient was computed using the residuals of this fit. For the control analyses presented in <xref rid="F10" ref-type="fig">Extended Data Fig. 6</xref>, response time effects in the BOLD signal were removed by modeling BOLD intensity at each timepoint (relative to stimulus onset) as a linear function of the time it took for the observer to 1) respond to the presented orientation and 2) report confidence on that trial, and the correlation coefficient between BOLD intensity and decoded uncertainty or confidence was computed on the residuals of this fit. For the partial correlation analyses reported in <xref rid="F11" ref-type="fig">Extended Data Fig. 7</xref>, both reported confidence (or decoded uncertainty) and BOLD intensity at each timepoint were modeled as a linear function of uncertainty (or confidence). The residuals of these fits were used to compute the (Spearman) correlation coefficient between confidence (or uncertainty) and the BOLD signal for each timepoint. The single-subject correlation coefficients were Fisher transformed, and a weighted average was computed across observers (cf. Multivariate analysis). Statistical significance was assessed using two-tailed permutation tests, in which uncertainty (or confidence) values were permuted across trials (1000 permutations). To control for multiple comparisons (FWER) we compared against the null distribution of the maximum correlation coefficient across timepoints (cf. Whole-brain analysis). Finally, we tested whether there was a significant difference in latency between the effects of confidence and uncertainty on the BOLD signal in each ROI. To this end we determined, for each subject individually, the (within-trial) timepoint at which the correlation coefficient between BOLD and uncertainty (confidence) was most strongly positive (negative). We then performed a paired t-test on these values, comparing between uncertainty and confidence.</p><p id="P62">To investigate whether activity in downstream areas mediates the relationship between decoded uncertainty and reported confidence, we performed the following analysis. We first modeled both confidence and uncertainty as a linear function of the BOLD signal in a given ROI and at a given (within-trial) timepoint. We then took the residuals of these model fits and computed the Spearman correlation coefficient between the (residual) uncertainty and confidence values. If the selected ROIs mediate the relationship between uncertainty and confidence, the residual correlation coefficient should be smaller in magnitude than the baseline correlation coefficient between uncertainty and confidence (i.e., not controlled for activity in downstream areas; as reported in <xref rid="F3" ref-type="fig">Fig. 3c</xref>). To quantify the mediating effect of the BOLD signal in each ROI at each timepoint, the single-subject correlation coefficients were Fisher transformed, and we subtracted from these values the (Fisher transformed) baseline correlation coefficient between uncertainty and confidence. Finally, a weighted average was computed across observers (cf. Multivariate analyses). Statistical significance of the predicted reduction in correlation strength was assessed using a permutation test, in which BOLD values were permuted across all trials (following otherwise similar procedures as for the whole-brain and main ROI-based analyses).</p></sec><sec id="S19"><title>Eye tracking data</title><p id="P63">Eye tracking data were acquired using an SR Research Eyelink 1000 system for 62 out of 64 sessions. For 11 of these sessions, data were collected for 4-12 runs (out of a total of 10-13) due to technical difficulties with the eye-tracking system. Gaze position was sampled at 1 kHz. Blinks and saccades were identified using the Eyelink software and removed. Eye fixations shorter than 100 ms in duration were similarly identified and removed. Any blinks of duration &#x0003e;1000 ms were considered to be artifacts and removed. For some trials, the quality of eye tracking data was of insufficient quality (as indicated by a high proportion of missing data points). This was identified by computing the percentage of missing (gaze) data points in a time window starting 4.5 seconds before stimulus onset and ending 4.5 seconds after stimulus offset, but excluding the stimulus window itself. Trials were excluded from further analysis if the percentage of missing data points within this pre- and post-stimulus window exceeded 50%. Based on this criterion, 3.84 % &#x000b1; 1.69 % (mean &#x000b1; S.D.) of trials were excluded from further analysis. Data were band-pass filtered using upper and lower period cutoffs of 36 <italic toggle="yes">s</italic> and 100 ms, respectively. The median gaze position per run was computed and subtracted from all data points within that run. All measures of interest were computed during stimulus presentation only, i.e. over the first 1.5 seconds of each trial. Mean eye position was obtained by first computing the mean x- and y-coordinate of the gaze data, and then taking the absolute distance from this position to the central fixation target. The proportion of blinks was computed as the fraction of time labeled as blinks; this included saccades immediately preceding or following a blink. A break from fixation occurred when the absolute distance between gaze position and the central fixation target was more than 1.5 degrees of visual angle. The proportion of fixation breaks was computed as the fraction of time labeled as such.</p></sec></sec><sec id="S20"><title>Ideal observer models</title><sec id="S21"><title>Model description</title><p id="P64">We implemented three different observer models, which make identical decisions but differ in how they compute confidence from internal signals. Model 1 takes a statistical approach and computes confidence from the degree of imprecision in the orientation judgment. Models 2 and 3 use heuristic strategies; model 2 uses features of the stimulus as a cue to confidence, and model 3 bases confidence on the magnitude of the observed error in the response. We call these the Probabilistic (Bayesian), Stimulus heuristics and Response heuristics observer, respectively (see also <xref rid="F1" ref-type="fig">Fig. 1</xref>).</p><p id="P65">The observer&#x02019;s task is to infer the stimulus from incoming sensory signals. These signals are noisy, so that there is no one-to-one mapping between a given stimulus <italic toggle="yes">s</italic> and its measurement <italic toggle="yes">m</italic>. Rather, the relationship between stimulus and measurements is described by a probability distribution <italic toggle="yes">p</italic>(<italic toggle="yes">m</italic>|<italic toggle="yes">s</italic>). We assume that across trials, the sensory measurements follow a (circular) Gaussian distribution centered on the true stimulus <italic toggle="yes">s</italic>, with variance <italic toggle="yes">&#x003c3;<sub>m</sub>
</italic>
<sup>2</sup>(<italic toggle="yes">s</italic>): <disp-formula id="FD8">
<label>#(8)</label>
<mml:math id="M11" display="block" overflow="scroll"><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02223;</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mi>Z</mml:mi></mml:mfrac><mml:mi>exp</mml:mi><mml:mrow><mml:mo>(</mml:mo><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mn>2</mml:mn><mml:mrow><mml:mrow><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mi>m</mml:mi></mml:msub><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mo>|</mml:mo></mml:mrow></mml:mrow></mml:mfrac><mml:mtext>&#x000a0;angle&#x000a0;</mml:mtext><mml:msup><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>m</mml:mi><mml:mo>,</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mo>)</mml:mo></mml:mrow></mml:math>
</disp-formula> where <italic toggle="yes">Z</italic> is a normalization constant.</p><p id="P66">We make a distinction between three sources of measurement noise: stimulus-dependent sensory noise (<italic toggle="yes">&#x003c3;<sub>s<sub>o</sub>
</sub>
</italic>
<sup>2</sup>), stimulus-independent sensory noise (<italic toggle="yes">&#x003c3;<sub>s<sub>i</sub>
</sub>
</italic>
<sup>2</sup>), and non-sensory (downstream) noise (<italic toggle="yes">&#x003c3;</italic>
<sub>
<italic toggle="yes">n</italic>
</sub>
<sup>2</sup>). The total amount of measurement noise equals the sum of the three noise components:
<disp-formula id="FD9">
<label>#(9)</label>
<mml:math id="M12" display="block" overflow="scroll"><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mi>m</mml:mi></mml:msub><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mi>o</mml:mi></mml:msub></mml:mrow></mml:msub><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>+</mml:mo><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow><mml:mn>2</mml:mn></mml:msubsup><mml:mo>+</mml:mo><mml:msubsup><mml:mi>&#x003c3;</mml:mi><mml:mi>n</mml:mi><mml:mn>2</mml:mn></mml:msubsup></mml:math>
</disp-formula>
</p><p id="P67">The stimulus-dependent component <italic toggle="yes">&#x003c3;<sub>s<sub>o</sub>
</sub>
</italic>
<sup>2</sup> represents Gaussian noise that varies in magnitude as a function of stimulus orientation. Specifically, human behavioral orientation judgments tend to be more precise for cardinal than oblique orientations<sup>
<xref rid="R19" ref-type="bibr">19</xref>,<xref rid="R70" ref-type="bibr">70</xref>
</sup>, and we model this oblique effect in orientation perception as a rectified sine function<sup>
<xref rid="R20" ref-type="bibr">20</xref>
</sup>:
<disp-formula id="FD10">
<label>#(10)</label>
<mml:math id="M13" display="block" overflow="scroll"><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mi>o</mml:mi></mml:msub></mml:mrow></mml:msub><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x022c5;</mml:mo><mml:mrow><mml:mo>|</mml:mo><mml:mrow><mml:mi>sin</mml:mi><mml:mi>s</mml:mi><mml:mfrac><mml:mrow><mml:mn>2</mml:mn><mml:mi>&#x003c0;</mml:mi></mml:mrow><mml:mrow><mml:mn>180</mml:mn></mml:mrow></mml:mfrac></mml:mrow><mml:mo>|</mml:mo></mml:mrow></mml:math>
</disp-formula> where <italic toggle="yes">a</italic> is the amplitude of the oblique effect. The stimulus-independent component <italic toggle="yes">&#x003c3;<sub>s<sub>i</sub>
</sub>
</italic>
<sup>2</sup> models any remaining sources of (Gaussian) sensory noise. Its magnitude varies randomly over trials, and we model the across-trial distribution of <italic toggle="yes">&#x003c3;<sub>s<sub>i</sub>
</sub>
</italic>
<sup>2</sup> as a gamma distribution: <disp-formula id="FD11">
<label>#(11)</label>
<mml:math id="M14" display="block" overflow="scroll"><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:msub><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup><mml:mo>&#x0223c;</mml:mo><mml:mtext>&#x00393;</mml:mtext><mml:mo stretchy="false">(</mml:mo><mml:mi>&#x003b1;</mml:mi><mml:mo>,</mml:mo><mml:mi>&#x003b2;</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:math>
</disp-formula>where <italic toggle="yes">&#x003b1;</italic> represents the shape parameter and <italic toggle="yes">&#x003b2;</italic> represents the rate parameter. Finally, the non-sensory (downstream) noise component, <italic toggle="yes">&#x003c3;<sub>n</sub>
</italic>
<sup>2</sup>, is of constant magnitude and captures (Gaussian) noise that arises beyond the initial stages of sensory processing, but prior to the decision, for example when the item is held in working memory or in processing steps downstream of sensory areas V1-V3.</p><p id="P68">To infer which stimulus likely caused their sensory measurement, the observers use full knowledge of the generative model. Specifically, each observer inverts the generative model using Bayes&#x02019; rule. Assuming a flat stimulus prior, the posterior distribution is proportional to the likelihood function:
<disp-formula id="FD12">
<label>#(12)</label>
<mml:math id="M15" display="block" overflow="scroll"><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02223;</mml:mo><mml:mi>m</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mo>&#x0221d;</mml:mo><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>m</mml:mi><mml:mo>&#x02223;</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:math>
</disp-formula>
</p><p id="P69">All three observer models take the mean of the posterior distribution as their internal sensory estimate <italic toggle="yes">&#x0015d;</italic> of the presented stimulus. This is the optimal solution for a squared-error loss function<sup>
<xref rid="R71" ref-type="bibr">71</xref>
</sup>. The observer&#x02019;s internal estimate of orientation is subsequently translated into an overt behavioral (motor) response <italic toggle="yes">r</italic>. The transformation from internal estimate into a motor response is noisy. Thus, the behavioral response <italic toggle="yes">r</italic> for the observer models is given by: <disp-formula id="FD13">
<label>#(13)</label>
<mml:math id="M16" display="block" overflow="scroll"><mml:mi>r</mml:mi><mml:mo>=</mml:mo><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003b5;</mml:mi><mml:mi>r</mml:mi></mml:msub></mml:math>
</disp-formula>where <italic toggle="yes">&#x003b5;<sub>r</sub>
</italic> is a zero-mean (circular) Gaussian noise variable with variance <italic toggle="yes">&#x003c3;<sub>r</sub>
</italic>
<sup>2</sup>.</p><p id="P70">The three model observers differ in how they compute confidence. The Bayesian or Probabilistic observer computes confidence as a function of the expected response error. Specifically, this observer assumes a (circular) squared-error loss function and computes confidence as the inverse of the expected loss (<xref rid="FD1" ref-type="disp-formula">Equation 1</xref>):
<disp-formula id="FD14">
<mml:math id="M17" display="block" overflow="scroll"><mml:msub><mml:mi>c</mml:mi><mml:mi>B</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:msup><mml:mstyle displaystyle="true"><mml:mo>&#x0222b;</mml:mo></mml:mstyle><mml:mtext>&#x0200b;</mml:mtext></mml:msup><mml:mi>p</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mi>s</mml:mi><mml:mo>&#x02223;</mml:mo><mml:mi>m</mml:mi><mml:mo stretchy="false">)</mml:mo><mml:mtext>angle</mml:mtext><mml:msup><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>r</mml:mi><mml:mo>,</mml:mo><mml:mi>s</mml:mi><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup><mml:mi>d</mml:mi><mml:mi>s</mml:mi></mml:mrow></mml:mfrac></mml:math>
</disp-formula>
</p><p id="P71">Replacing this direct mapping with any other monotonically decreasing function does not qualitatively change any of the predictions for this model. Thus, for the Bayesian observer, confidence is based (in part) on the posterior probability distribution over the stimulus.</p><p id="P72">The Stimulus heuristics observer uses the estimated orientation of the stimulus as a cue to uncertainty and confidence. That is, this observer knows that behavior tends to be more precise for cardinal than oblique orientations, and simply exploits this knowledge in their confidence judgments (<xref rid="FD2" ref-type="disp-formula">Equation 2</xref>):
<disp-formula id="FD15">
<mml:math id="M18" display="block" overflow="scroll"><mml:msub><mml:mi>c</mml:mi><mml:mi>s</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mi>f</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:mfrac></mml:math>
</disp-formula>
</p><p id="P73">where the function <italic toggle="yes">f</italic>(<italic toggle="yes">&#x0015d;</italic>) takes the shape of the oblique effect (cf. <xref rid="FD10" ref-type="disp-formula">Equation 10</xref>):
<disp-formula id="FD16">
<label>#(14)</label>
<mml:math id="M19" display="block" overflow="scroll"><mml:mi>f</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mo stretchy="false">)</mml:mo><mml:mo>=</mml:mo><mml:mi>a</mml:mi><mml:mo>&#x022c5;</mml:mo><mml:mrow><mml:mo>|</mml:mo><mml:mrow><mml:mi>sin</mml:mi><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mfrac><mml:mrow><mml:mn>2</mml:mn><mml:mi>&#x003c0;</mml:mi></mml:mrow><mml:mrow><mml:mn>180</mml:mn></mml:mrow></mml:mfrac></mml:mrow><mml:mo>|</mml:mo></mml:mrow><mml:mo>+</mml:mo><mml:mtext>E</mml:mtext><mml:mrow><mml:mo>[</mml:mo><mml:mrow><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:msub><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup></mml:mrow><mml:mo>]</mml:mo></mml:mrow></mml:math>
</disp-formula>
</p><p id="P74">The Response heuristics observer bases confidence on the observed error in the motor response. Specifically, the observer simply notices the difference between the overt response <italic toggle="yes">r</italic> and internal estimate &#x0015d;, and adjusts confidence accordingly. We quantified confidence for this model observer as the inverse of the squared acute-angle distance between the internal orientation estimate and the external response (<xref rid="FD3" ref-type="disp-formula">Equation 3</xref>):
<disp-formula id="FD17">
<mml:math id="M20" display="block" overflow="scroll"><mml:msub><mml:mi>c</mml:mi><mml:mi>R</mml:mi></mml:msub><mml:mo>=</mml:mo><mml:mfrac><mml:mn>1</mml:mn><mml:mrow><mml:mtext>angle</mml:mtext><mml:msup><mml:mrow><mml:mo stretchy="false">(</mml:mo><mml:mi>r</mml:mi><mml:mo>,</mml:mo><mml:mover accent="true"><mml:mi>s</mml:mi><mml:mo>^</mml:mo></mml:mover><mml:mo stretchy="false">)</mml:mo></mml:mrow><mml:mn>2</mml:mn></mml:msup></mml:mrow></mml:mfrac></mml:math>
</disp-formula>
</p></sec><sec id="S22"><title>Simulations</title><p id="P75">We simulated 50,000 trials for each of the three model observers. Stimulus orientations were drawn from a uniform distribution on the interval [0-179&#x000b0;]. Sensory measurements were randomly sampled from the generative model as described above (<xref rid="FD8" ref-type="disp-formula">Equations 8</xref>-<xref rid="FD11" ref-type="disp-formula">11</xref>), with <italic toggle="yes">a</italic> = 20, <italic toggle="yes">&#x003c3;<sub>d</sub>
</italic>
<sup>2</sup> = 5, <italic toggle="yes">&#x003b1;</italic> = 10, and <italic toggle="yes">&#x003b2;</italic> = 1. The normalization constant <italic toggle="yes">Z</italic> was computed numerically.</p><p id="P76">Probabilistic inference proceeded with full knowledge of the parameter values and according to <xref rid="FD12" ref-type="disp-formula">Equation 12</xref>. Behavioral responses were obtained using <xref rid="FD13" ref-type="disp-formula">Equation 13</xref> and with <italic toggle="yes">&#x003c3;<sub>r</sub>
</italic>
<sup>2</sup> = 5. Confidence judgments were obtained using <xref rid="FD1" ref-type="disp-formula">Equations 1</xref>-<xref rid="FD3" ref-type="disp-formula">3</xref> and <xref rid="FD14" ref-type="disp-formula">14</xref>. To obtain a reasonable range of confidence values, a constant (of value 1) was added to the denominator of <xref rid="FD3" ref-type="disp-formula">Equation 3</xref>. Confidence ratings were z-scored per observer to ensure that they would all fall on the same scale. Sensory uncertainty was quantified as:
<disp-formula id="FD18">
<label>#(15)</label>
<mml:math id="M21" display="block" overflow="scroll"><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mi>s</mml:mi></mml:msub><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup><mml:mo>=</mml:mo><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mi>i</mml:mi></mml:msub></mml:mrow></mml:msub><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup><mml:mo>+</mml:mo><mml:msub><mml:mi>&#x003c3;</mml:mi><mml:mrow><mml:msub><mml:mi>s</mml:mi><mml:mi>o</mml:mi></mml:msub></mml:mrow></mml:msub><mml:msup><mml:mrow/><mml:mn>2</mml:mn></mml:msup></mml:math>
</disp-formula>
</p><p id="P77">Data were preprocessed following the procedures described in Behavioral data analysis. Similar to the empirical analyses, orientation-dependent shifts in confidence judgments, behavioral variability or sensory uncertainty were removed. For data visualization, simulated data were divided over 10 equal-sized bins of increasing confidence (<xref rid="F2" ref-type="fig">Fig. 2a-b</xref>) or sensory uncertainty (<xref rid="F2" ref-type="fig">Fig. 2c-d</xref>), and the mean confidence level, variance of behavioral errors (<xref rid="F2" ref-type="fig">Fig. 2a-b</xref>), and mean level of sensory uncertainty (<xref rid="F2" ref-type="fig">Fig. 2c-d</xref>) were computed across all trials in each bin.</p></sec></sec></sec><sec sec-type="extended-data" id="S23"><label>1</label><title>Extended Data</title><fig position="anchor" id="F5"><label>Extended Data Fig. 1</label><caption><title>Trial structure.</title><p id="P78">Each trial started with the presentation of an oriented grating (1500 ms) followed by a 6000-ms fixation interval and two 4500-ms response intervals, during which the participant first reported the orientation of the previously seen stimulus by rotating a bar, and then indicated their level of confidence in this judgment on a continuous scale. Trials were separated by a 1500-ms intertrial interval. Stimulus, response bar and confidence scale are not drawn to their true scale and contrast.</p></caption><graphic xlink:href="EMS137960-f005" position="float"/></fig><fig position="anchor" id="F6"><label>Extended Data Fig. 2</label><caption><title>Orientation and uncertainty decoding performance.</title><p id="P79">The orientation of the presented stimulus, and associated uncertainty, decoded from activity patterns in areas V1-V3. (a) Orientation decoding performance was quantified by means of the circular equivalent of the Pearson correlation coefficient between presented and decoded orientations. Correlation coefficients were computed for each subject individually and then averaged across subjects (N = 32). Presented and decoded orientations were significantly correlated (z = 83.58, p &#x0003c; 0.001, r = 0.60, 95% CI = [0.58, 0.61]). (b-d) To assess the degree to which the decoder captured uncertainty contained in neural population activity, we compared decoded uncertainty to behavioral variability, the rationale being that a more precise representation in cortex should also result in more precise behavioral estimates (see also <sup>
<xref rid="R10" ref-type="bibr">10</xref>
</sup>).(b) Corroborating our approach, we found that decoded uncertainty was greater for oblique compared to cardinal orientation stimuli (correlation distance-to-cardinal and decoded uncertainty: z = 2.95, p = 0.002, &#x003c1; = 0.025, 95% CI = [0.0083 0.041]). This finding was paralleled by the imprecision in observer behavior (correlation distance-to-cardinal and behavioral variability: t(287) = 13.60, p &#x0003c; 0.001, r = 0.63, 95% CI = [0.55, 0.69]). (c-d) In addition, behavioral orientation responses were more precise when the decoded probability distributions indicated greater certainty in cortex, (c) both across orientation stimuli (correlation decoded uncertainty and behavioral variability: t(287) = 2.30, p = 0.011, r = 0.13, 95% CI = [0.019, 0.25]), and (d) when controlling for orientation (t(286) = 1.68, p = 0.047, r = 0.099, 95% CI = [-0.017, 0.21]). Altogether, this further underscores the validity of the decoding approach and shows that decoded uncertainty reliably characterizes the degree of imprecision in cortical representations of the stimulus (see <sup>
<xref rid="R10" ref-type="bibr">10</xref>,<xref rid="R18" ref-type="bibr">18</xref>
</sup> for further proof of this approach). Note that these are partial residual plots, which is why the data is centered around 0. Error bars (a-b) represent &#x000b1; 1 s.e.m. (c-d) Shades of red indicate ten equal-size bins of increasing decoded uncertainty, dots represent individual observers (N = 32).</p></caption><graphic xlink:href="EMS137960-f006" position="float"/></fig><fig position="anchor" id="F7"><label>Extended Data Fig. 3</label><caption><title>Relationship between decoded uncertainty and reported confidence across different numbers of voxels.</title><p id="P80">Correlation coefficients between decoded uncertainty and reported confidence as a function of the number of voxels included in the ROI, both across all orientations (a) and after removing the effect of stimulus orientation (b). Voxels within V1-V3 were ranked and selected for multivariate analysis based on their response to the visual localizer stimulus (see <xref rid="S7" ref-type="sec">Methods</xref>), using a lenient statistical threshold of p&#x0003c;0.01, uncorrected. The results proved reasonably robust to variations in the number of voxels selected for analysis. Dark red line indicates group average correlation coefficients, error bars denote &#x000b1; 1 s.e.m.</p></caption><graphic xlink:href="EMS137960-f007" position="float"/></fig><fig position="anchor" id="F8"><label>Extended Data Fig. 4</label><caption><title>No effects of overall BOLD or eyetracking measures on confidence.</title><p id="P81">Reported confidence is not significantly correlated with the mean BOLD response to the stimulus in areas V1-V3 (z = 0.73, p = 0.47, &#x003c1; = 0.0062, 95% CI = [-0.010, 0.023]; equivalence test: z = -0.094, p &#x0003c; 0.001), nor with mean eye position (mean absolute distance to screen center; z = -1.38, p = 0.17, &#x003c1; = -0.012, 95% CI = [-0.030, 0.0051]; equivalence test: z = -0.088, p &#x0003c; 0.001), eye blinks (z = 0.99, p = 0.32, &#x003c1; = 0.0087, 95% CI = [-0.0086, 0.026]; equivalence test: z = -0.11, p &#x0003c;0.001), or the number of breaks from fixation during stimulus presentation (z = 0.57, p = 0.57, &#x003c1; = 0.0050, 95% CI = [-0.012, 0.022]; equivalence test: z= -0.11, p &#x0003c; 0.001), suggesting that participants did not rely on heuristics in terms of eye position (&#x02018;did I look at the stimulus?&#x02019;) or eye blinks (&#x02018;were my eyes closed?&#x02019;) for reporting confidence. It furthermore rules out simple heuristic explanations in terms of attentional effort (&#x02018;my mind was elsewhere&#x02019;, &#x02018;I didn&#x02019;t really try that hard&#x02019;), as the mean BOLD response to the stimulus tends to increase with attention in these areas<sup>
<xref rid="R73" ref-type="bibr">73</xref>
</sup>. Shaded blue represents &#x000b1; 1 s.e.m. Gray dots denote individual observers (N = 32).</p></caption><graphic xlink:href="EMS137960-f008" position="float"/></fig><fig position="anchor" id="F9"><label>Extended Data Fig. 5</label><caption><title>Effects of decoded uncertainty and reported confidence on the BOLD response in precuneus, supplementary motor area, dorsal perigenual anterior cingulate cortex, ventral posterior cingulate cortex, dorsal posterior cingulate cortex, and stimulus-driven voxels in V1-V3.</title><p id="P82">Group-average correlation coefficients for the relationship between decoded uncertainty and BOLD contrast, and reported confidence and BOLD contrast, in six ROIs. (a) In precuneus, the effects of both decoded sensory uncertainty and reported confidence on BOLD peaked around the same time, i.e. during the second half of the response window. This finding is consistent with previous work suggesting that precuneus may represent uncertainty in memory but not in perception<sup>
<xref rid="R74" ref-type="bibr">74</xref>&#x02013;<xref rid="R76" ref-type="bibr">76</xref>
</sup>. (b) In supplementary motor area, both decoded uncertainty and reported confidence modulated cortical activity relatively early in the response window, while the effects of confidence lingered until after observers gave their response. (c-d) In dorsal perigenual anterior cingulate cortex and ventral posterior cingulate cortex, decoded uncertainty had a moderate effect on the BOLD response. Reported confidence modulated cortical activity during as well as shortly after the response window. (e) In dorsal posterior cingulate cortex, the modulatory effect of both decoded uncertainty and reported confidence on the cortical response was largest around the onset of the response window. (f) Stimulus driven voxels in early visual cortex were modulated by both decoded uncertainty and reported confidence, most notably during the first portion of the response interval. Given the timing of the effect (and taking into account the hemodynamic delay), this likely does not reflect uncertainty in the sensory representation <italic toggle="yes">per se</italic>, but is consistent with anticipatory processes or working memory-related signals potentially influenced by the imprecision in the cortical stimulus representation<sup>
<xref rid="R77" ref-type="bibr">77</xref>&#x02013;<xref rid="R79" ref-type="bibr">79</xref>
</sup>. Please note there is no net effect of uncertainty on the overall (univariate) BOLD response during the decoding window (stimulus presentation; dashed lines). (a-f) Horizontal lines indicate statistical significance (p&#x0003c;0.05, FWER-controlled). Error bars represent &#x000b1; 1 s.e.m. Dark gray area marks stimulus presentation window, light gray area marks response window.</p></caption><graphic xlink:href="EMS137960-f009" position="float"/></fig><fig position="anchor" id="F10"><label>Extended Data Fig. 6</label><caption><title>Effects of decoded uncertainty and reported confidence on the BOLD response in dAI, dACC and rlPFC, after accounting for trial-by-trial fluctuations in behavioral response times.</title><p id="P83">Behavioral response time effects were linearly regressed out from decoded uncertainty and reported confidence, prior to computing the Spearman correlation coefficient between decoded uncertainty (reported confidence) and the BOLD response at different moments in time after stimulus presentation. The remaining analysis steps are identical to those in the main text. Removing the effect of behavioral response time did not qualitatively change the pattern of results in any of these ROIs. Horizontal lines indicate statistical significance (p&#x0003c;0.05, FWER-controlled). Dark gray area marks stimulus presentation window, light gray area denotes response window. Error bars represent &#x000b1; 1 s.e.m.</p></caption><graphic xlink:href="EMS137960-f010" position="float"/></fig><fig position="anchor" id="F11"><label>Extended Data Fig. 7</label><caption><title>Effects of decoded uncertainty (or reported confidence) on the BOLD response in dAI, dACC and rlPFC, after controlling for confidence (or decoded uncertainty).</title><p id="P84">Reported confidence (or decoded uncertainty) was linearly regressed on both decoded uncertainty (or reported confidence) and the BOLD response at different moments in time after stimulus presentation. The residuals of these fits were then used to compute the group-averaged correlation coefficient between cortical response amplitude and decoded uncertainty (red) or reported confidence (blue). For all ROIs, the results are qualitatively similar to the main results reported in <xref rid="F4" ref-type="fig">Fig. 4</xref> in the main text. Horizontal lines indicate statistical significance (p&#x0003c;0.05, FWER-controlled). Dark gray area marks stimulus presentation window, light gray area denotes response window. Error bars represent &#x000b1; 1 s.e.m.</p></caption><graphic xlink:href="EMS137960-f011" position="float"/></fig><fig position="anchor" id="F12"><label>Extended Data Fig. 8</label><caption><title>Activity in dAI, dACC, and left rlPFC mediates the relationship between decoded uncertainty and reported confidence.</title><p id="P85">To assess the degree to which the cortical activity in these regions mediates the observed relationship between decoded uncertainty and reported confidence, we performed the following analysis. We first modeled both uncertainty and confidence as a function of the overall BOLD signal in a given ROI at each timepoint, and then used the residuals of these fits to compute the Spearman correlation coefficient between decoded uncertainty and reported confidence when controlled for the BOLD signal. From the resulting correlation coefficient, we subtracted the (baseline) correlation coefficient that was obtained while we did not control for the BOLD signal (see <xref rid="F3" ref-type="fig">Fig. 3c</xref>). We observed a significant net effect at various moments in time, which indicates that there was a reliable reduction in the strength of the inverse (negative) correlation coefficient between uncertainty and confidence when we controlled for BOLD intensity. This suggests that the level of cortical activity in these windows (partially) mediates the relationship between decoded uncertainty and reported confidence. Horizontal lines indicate statistical significance (p&#x0003c;0.05, FWER-controlled). Dark gray area marks stimulus presentation window, light gray area denotes response window. Dashed lines indicate the decoding window used in the main analyses (<xref rid="F3" ref-type="fig">Fig. 3b-c</xref> and <xref rid="F6" ref-type="fig">Extended Data Fig. 2</xref>). Error bars represent &#x000b1; 1 s.e.m.</p></caption><graphic xlink:href="EMS137960-f012" position="float"/></fig><fig position="anchor" id="F13"><label>Extended Data Fig. 9</label><caption><title>Decoding results over time.</title><p id="P86">Does reported confidence similarly reflect imprecision in the cortical representation when the orientation is held in visual working memory? To address this question, the analyses of <xref rid="F3" ref-type="fig">Fig. 3b-c</xref> and <xref rid="F6" ref-type="fig">Extended Data Fig. 2</xref> were repeated over time, using a sliding window of size 3 <italic toggle="yes">s</italic> (2 TRs). We focused on successive intervals from 1.5 <italic toggle="yes">s</italic> before to 13.5 <italic toggle="yes">s</italic> after stimulus onset (which roughly corresponds to the onset of the response window after accounting for hemodynamic delay). Benchmark tests verified that the decoded probability distributions reliably predict the orientation of the presented stimulus (a), and variability in the observer&#x02019;s behavioral estimates (b-c) over extended periods of time. Having established that the decoded distributions meaningfully reflect the degree of imprecision in the cortical representation, we next investigated the extent to which decoded uncertainty predicts reported confidence during the retention interval. We found a reliable negative relationship between decoded uncertainty and reported confidence that held up well into the delay period (d). This is consistent with an imprecise working memory trace in V1-V3 that influences subjective confidence. Please note, however, that our design does not warrant strong conclusions regarding the nature of this representation: due to fMRI&#x02019;s low temporal resolution, it is difficult to say whether these signals are purely perceptual or working memory-related (see e.g. <sup>
<xref rid="R47" ref-type="bibr">47</xref>
</sup>, for similar rationale), and later TRs could simply reflect the visual presentation of the response bar, rather than memory-based signals. (a-d) Data are centered to the middle of the analysis window (of size 2 TRs). Horizontal lines indicate statistical significance (p&#x0003c;0.05, FWER-controlled). Dark gray area marks stimulus presentation window, light gray area denotes response window. Dashed lines indicate the decoding window used for the main analyses (i.e., <xref rid="F3" ref-type="fig">Fig. 3b-c</xref> and <xref rid="F6" ref-type="fig">Extended Data Fig. 2</xref>). Shaded regions represent &#x000b1; 1 s.e.m. (standard errors in (a) are too small to be visible).</p></caption><graphic xlink:href="EMS137960-f013" position="float"/></fig><fig position="anchor" id="F14"><label>Extended Data Fig. 10</label><caption><title>Oblique effect in reported confidence and decoded uncertainty.</title><p id="P87">Effect of stimulus orientation on reported confidence (a) and decoded uncertainty (b). Each participant&#x02019;s data were first binned based on the absolute distance between presented stimulus orientation and the nearest cardinal axis (equal-width bins), and then averaged across trials and finally across subjects (error bars represent &#x000b1; 1 s.e.m). Dashed lines indicate best-fitting function (least-squares; quadratic for confidence, linear for decoded uncertainty). Functions were fitted on the trial-by-trial data for each participant, and averaged across participants.</p></caption><graphic xlink:href="EMS137960-f014" position="float"/></fig></sec><sec sec-type="supplementary-material" id="SM"><title>Supplementary Material</title><supplementary-material id="SD1" position="float" content-type="local-data"><label>Source Data Figure 2</label><media xlink:href="EMS137960-supplement-Source_Data_Figure_2.xlsx" id="d64e2670" position="anchor"/></supplementary-material><supplementary-material id="SD2" position="float" content-type="local-data"><label>Source Data Figure 3</label><media xlink:href="EMS137960-supplement-Source_Data_Figure_3.xlsx" id="d64e2673" position="anchor"/></supplementary-material><supplementary-material id="SD3" position="float" content-type="local-data"><label>Source Data Figure 4</label><media xlink:href="EMS137960-supplement-Source_Data_Figure_4.xlsx" id="d64e2676" position="anchor"/></supplementary-material><supplementary-material id="SD4" position="float" content-type="local-data"><label>Supplementary Data 1</label><media xlink:href="EMS137960-supplement-Supplementary_Data_1.zip" id="d64e2679" position="anchor"/></supplementary-material><supplementary-material id="SD5" position="float" content-type="local-data"><label>Supplementary Table 1</label><media xlink:href="EMS137960-supplement-Supplementary_Table_1.pdf" id="d64e2682" position="anchor"/></supplementary-material></sec></body><back><ack id="S24"><title>Acknowledgements</title><p>We thank A. Sanfey and R. Cools for helpful discussions, C. Beckmann for advice on statistical analyses, and P. Gaalman for MRI support. This work was supported by European Research Council Starting Grant 677601 (to J.F.M.J.). The funder had no role in study design, data collection and analysis, decision to publish or preparation of the manuscript.</p></ack><fn-group><fn id="FN1" fn-type="con"><p id="P90">
<bold>Author Contributions</bold>
</p><p id="P91">L.S.G., R.S.v.B. and J.F.M.J. conceived and designed the experiments. L.S.G. collected data. L.S.G. analyzed data, with help from J.F.M.J. L.S.G. and J.R.H.C. constructed ideal observer models, with help from J.F.M.J. L.S.G., J.R.H.C., R.S.v.B. and J.F.M.J. wrote the manuscript.</p></fn><fn id="FN2" fn-type="COI-statement"><p id="P92">
<bold>Competing Interests statement</bold>
</p><p id="P93">The authors declare no competing interests.</p></fn><fn id="FN3"><p id="P94">
<bold>Citation diversity statement</bold>
</p><p id="P95">We quantified the gender balance of works cited in the main text of this paper (n = 45, excluding self-citations) by manual gender classification of the first and last authors. Among the cited works there are 44% single-author male, 73.3% male-male, 2.2% male-female, 15.6% female-male, and 4.4% female-female publications. Expected proportions computed from publications in five top neuroscience journals (as reported in <sup>
<xref rid="R72" ref-type="bibr">72</xref>
</sup>) are 55.3% male-male, 10.2% male-female, 26.2% female-male, and 8.3% female-female.</p></fn></fn-group><sec sec-type="data-availability" id="S25"><title>Data availability statement</title><p id="P88">The data used to generate the main figures in this article are provided under Source Data. Preprocessed behavioral and fMRI data for individual participants, as well as unthresholded statistical maps from the whole-brain univariate analysis, can be downloaded from: <ext-link xlink:href="https://doi.org/10.34973/983b-a047" ext-link-type="uri">https://doi.org/10.34973/983b-a047</ext-link>. To protect participant privacy, the raw data are available from the corresponding author upon request.</p></sec><sec sec-type="data-availability" id="S26"><title>Code availability statement</title><p id="P89">All custom code is available from the corresponding author upon request. Custom code for the probabilistic decoding technique can also be found at <ext-link xlink:href="https://github.com/jeheelab/" ext-link-type="uri">https://github.com/jeheelab/</ext-link>
</p></sec><ref-list><ref id="R1"><label>1</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Pouget</surname><given-names>A</given-names></name>
<name><surname>Drugowitsch</surname><given-names>J</given-names></name>
<name><surname>Kepecs</surname><given-names>A</given-names></name>
</person-group><article-title>Confidence and certainty : distinct probabilistic quantities for different goals</article-title><source>Nat Neurosci</source><year>2016</year><volume>19</volume><fpage>366</fpage><lpage>374</lpage><pub-id pub-id-type="pmid">26906503</pub-id></element-citation></ref><ref id="R2"><label>2</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Meyniel</surname><given-names>F</given-names></name>
<name><surname>Sigman</surname><given-names>M</given-names></name>
<name><surname>Mainen</surname><given-names>ZF</given-names></name>
</person-group><article-title>Confidence as Bayesian probability: from neural origins to behavior</article-title><source>Neuron</source><year>2015</year><volume>88</volume><fpage>78</fpage><lpage>92</lpage><pub-id pub-id-type="pmid">26447574</pub-id></element-citation></ref><ref id="R3"><label>3</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Hangya</surname><given-names>B</given-names></name>
<name><surname>Sanders</surname><given-names>JI</given-names></name>
<name><surname>Kepecs</surname><given-names>A</given-names></name>
</person-group><article-title>A Mathematical Framework for Statistical Decision Confidence</article-title><source>Neural Comput</source><year>2016</year><volume>28</volume><fpage>1840</fpage><lpage>1858</lpage><pub-id pub-id-type="pmid">27391683</pub-id></element-citation></ref><ref id="R4"><label>4</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Mamassian</surname><given-names>P</given-names></name>
</person-group><article-title>Visual Confidence</article-title><source>Annu Rev Vis Sci</source><year>2016</year><volume>2</volume><fpage>459</fpage><lpage>481</lpage><pub-id pub-id-type="pmid">28532359</pub-id></element-citation></ref><ref id="R5"><label>5</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kepecs</surname><given-names>A</given-names></name>
<name><surname>Mainen</surname><given-names>ZF</given-names></name>
</person-group><article-title>A computational framework for the study of confidence in humans and animals</article-title><source>Philos Trans R Soc B</source><year>2012</year><volume>367</volume><fpage>1322</fpage><lpage>1337</lpage></element-citation></ref><ref id="R6"><label>6</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Sanders</surname><given-names>JI</given-names></name>
<name><surname>Hangya</surname><given-names>B</given-names></name>
<name><surname>Kepecs</surname><given-names>A</given-names></name>
</person-group><article-title>Signatures of a Statistical Computation in the Human Sense of Confidence</article-title><source>Neuron</source><year>2016</year><volume>90</volume><fpage>499</fpage><lpage>506</lpage><pub-id pub-id-type="pmid">27151640</pub-id></element-citation></ref><ref id="R7"><label>7</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Barthelm&#x000e9;</surname><given-names>S</given-names></name>
<name><surname>Mamassian</surname><given-names>P</given-names></name>
</person-group><article-title>Flexible mechanisms underlie the evaluation of visual confidence</article-title><source>Proc Natl Acad Sci U S A</source><year>2010</year><volume>107</volume><fpage>20834</fpage><lpage>20839</lpage><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="R8"><label>8</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Adler</surname><given-names>WT</given-names></name>
<name><surname>Ma</surname><given-names>WJ</given-names></name>
</person-group><article-title>Comparing Bayesian and non-Bayesian accounts of human confidence reports</article-title><source>PLOS Comput Biol</source><year>2018</year><volume>14</volume><elocation-id>e1006572</elocation-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="R9"><label>9</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Denison</surname><given-names>RN</given-names></name>
<name><surname>Adler</surname><given-names>WT</given-names></name>
<name><surname>Carrasco</surname><given-names>M</given-names></name>
<name><surname>Ma</surname><given-names>WJ</given-names></name>
</person-group><article-title>Humans incorporate attention-dependent uncertainty into perceptual decisions and confidence</article-title><source>Proc Natl Acad Sci U S A</source><year>2018</year><volume>115</volume><fpage>11090</fpage><lpage>11095</lpage><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="R10"><label>10</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>van Bergen</surname><given-names>RS</given-names></name>
<name><surname>Ji Ma</surname><given-names>W</given-names></name>
<name><surname>Pratte</surname><given-names>MS</given-names></name>
<name><surname>Jehee</surname><given-names>JFM</given-names></name>
</person-group><article-title>Sensory uncertainty decoded from visual cortex predicts behavior</article-title><source>Nat Neurosci</source><year>2015</year><volume>18</volume><fpage>1728</fpage><lpage>1730</lpage><pub-id pub-id-type="pmid">26502262</pub-id></element-citation></ref><ref id="R11"><label>11</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Navajas</surname><given-names>J</given-names></name>
<etal/>
</person-group><article-title>The idiosyncratic nature of confidence</article-title><source>Nat Hum Behav</source><year>2017</year><volume>1</volume><fpage>810</fpage><lpage>818</lpage><pub-id pub-id-type="pmid">29152591</pub-id></element-citation></ref><ref id="R12"><label>12</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Bertana</surname><given-names>A</given-names></name>
<name><surname>Chetverikov</surname><given-names>A</given-names></name>
<name><surname>van Bergen</surname><given-names>RS</given-names></name>
<name><surname>Ling</surname><given-names>S</given-names></name>
<name><surname>Jehee</surname><given-names>JFM</given-names></name>
</person-group><article-title>Dual strategies in human confidence judgments</article-title><source>J Vis</source><year>2021</year><volume>21</volume><fpage>21</fpage></element-citation></ref><ref id="R13"><label>13</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Honig</surname><given-names>M</given-names></name>
<name><surname>Ma</surname><given-names>WJ</given-names></name>
<name><surname>Fougnie</surname><given-names>D</given-names></name>
</person-group><article-title>Humans incorporate trial-to-trial working memory uncertainty into rewarded decisions</article-title><source>Proc Natl Acad Sci</source><year>2020</year><volume>117</volume><fpage>8391</fpage><lpage>8397</lpage><pub-id pub-id-type="pmid">32229572</pub-id></element-citation></ref><ref id="R14"><label>14</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kepecs</surname><given-names>A</given-names></name>
<name><surname>Uchida</surname><given-names>N</given-names></name>
<name><surname>Zariwala</surname><given-names>HA</given-names></name>
<name><surname>Mainen</surname><given-names>ZF</given-names></name>
</person-group><article-title>Neural correlates, computation and behavioural impact of decision confidence</article-title><source>Nature</source><year>2008</year><volume>455</volume><fpage>227</fpage><lpage>231</lpage><pub-id pub-id-type="pmid">18690210</pub-id></element-citation></ref><ref id="R15"><label>15</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Masset</surname><given-names>P</given-names></name>
<name><surname>Ott</surname><given-names>T</given-names></name>
<name><surname>Lak</surname><given-names>A</given-names></name>
<name><surname>Hirokawa</surname><given-names>J</given-names></name>
<name><surname>Kepecs</surname><given-names>A</given-names></name>
</person-group><article-title>Behavior- and Modality-General Representation of Confidence in Orbitofrontal Cortex</article-title><source>Cell</source><year>2020</year><volume>182</volume><fpage>112</fpage><lpage>126</lpage><elocation-id>e18</elocation-id><pub-id pub-id-type="pmid">32504542</pub-id></element-citation></ref><ref id="R16"><label>16</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kiani</surname><given-names>R</given-names></name>
<name><surname>Shadlen</surname><given-names>MN</given-names></name>
</person-group><article-title>Representation of confidence associated with a decision by neurons in the parietal cortex</article-title><source>Science</source><year>2009</year><volume>324</volume><fpage>759</fpage><lpage>764</lpage><pub-id pub-id-type="pmid">19423820</pub-id></element-citation></ref><ref id="R17"><label>17</label><element-citation publication-type="book"><person-group person-group-type="author">
<name><surname>Green</surname><given-names>DM</given-names></name>
<name><surname>Swets</surname><given-names>JA</given-names></name>
</person-group><source>Signal detection theory and psychophysics</source><publisher-name>Wiley</publisher-name><year>1966</year></element-citation></ref><ref id="R18"><label>18</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>van Bergen</surname><given-names>RS</given-names></name>
<name><surname>Jehee</surname><given-names>JFM</given-names></name>
</person-group><article-title>Modeling correlated noise is necessary to decode uncertainty</article-title><source>NeuroImage</source><year>2018</year><volume>180</volume><fpage>78</fpage><lpage>87</lpage><pub-id pub-id-type="pmid">28801251</pub-id></element-citation></ref><ref id="R19"><label>19</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Appelle</surname><given-names>S</given-names></name>
</person-group><article-title>Perception and discrimination as a function of stimulus orientation: the &#x0201c;oblique effect&#x0201d; in man and animals</article-title><source>Psychol Bull</source><year>1972</year><volume>78</volume><fpage>266</fpage><lpage>278</lpage><pub-id pub-id-type="pmid">4562947</pub-id></element-citation></ref><ref id="R20"><label>20</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Girshick</surname><given-names>AR</given-names></name>
<name><surname>Landy</surname><given-names>MS</given-names></name>
<name><surname>Simoncelli</surname><given-names>EP</given-names></name>
</person-group><article-title>Cardinal rules: Visual orientation perception reflects knowledge of environmental statistics</article-title><source>Nat Neurosci</source><year>2011</year><volume>14</volume><fpage>926</fpage><lpage>932</lpage><pub-id pub-id-type="pmid">21642976</pub-id></element-citation></ref><ref id="R21"><label>21</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Goris</surname><given-names>RLT</given-names></name>
<name><surname>Movshon</surname><given-names>JA</given-names></name>
<name><surname>Simoncelli</surname><given-names>EP</given-names></name>
</person-group><article-title>Partitioning neuronal variability</article-title><source>Nat Neurosci</source><year>2014</year><volume>17</volume><fpage>858</fpage><lpage>865</lpage><pub-id pub-id-type="pmid">24777419</pub-id></element-citation></ref><ref id="R22"><label>22</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Singer</surname><given-names>T</given-names></name>
<name><surname>Critchley</surname><given-names>HD</given-names></name>
<name><surname>Preuschoff</surname><given-names>K</given-names></name>
</person-group><article-title>A common role of insula in feelings, empathy and uncertainty</article-title><source>Trends Cogn Sci</source><year>2009</year><volume>13</volume><fpage>334</fpage><lpage>340</lpage><pub-id pub-id-type="pmid">19643659</pub-id></element-citation></ref><ref id="R23"><label>23</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Behrens</surname><given-names>TEJ</given-names></name>
<name><surname>Woolrich</surname><given-names>MW</given-names></name>
<name><surname>Walton</surname><given-names>ME</given-names></name>
<name><surname>Rushworth</surname><given-names>MFS</given-names></name>
</person-group><article-title>Learning the value of information in an uncertain world</article-title><source>Nat Neurosci</source><year>2007</year><volume>10</volume><fpage>1214</fpage><lpage>1221</lpage><pub-id pub-id-type="pmid">17676057</pub-id></element-citation></ref><ref id="R24"><label>24</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Rushworth</surname><given-names>MFS</given-names></name>
<name><surname>Behrens</surname><given-names>TEJ</given-names></name>
</person-group><article-title>Choice, uncertainty and value in prefrontal and cingulate cortex</article-title><source>Nat Neurosci</source><year>2008</year><volume>11</volume><fpage>389</fpage><lpage>397</lpage><pub-id pub-id-type="pmid">18368045</pub-id></element-citation></ref><ref id="R25"><label>25</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Fleming</surname><given-names>SM</given-names></name>
<name><surname>Dolan</surname><given-names>RJ</given-names></name>
</person-group><article-title>The neural basis of metacognitive ability</article-title><source>Philos Trans R Soc B</source><year>2012</year><volume>367</volume><fpage>1338</fpage><lpage>1349</lpage></element-citation></ref><ref id="R26"><label>26</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Chang</surname><given-names>LJ</given-names></name>
<name><surname>Yarkoni</surname><given-names>T</given-names></name>
<name><surname>Khaw</surname><given-names>MW</given-names></name>
<name><surname>Sanfey</surname><given-names>AG</given-names></name>
</person-group><article-title>Decoding the role of the insula in human cognition: Functional parcellation and large-scale reverse inference</article-title><source>Cereb Cortex</source><year>2013</year><volume>23</volume><fpage>739</fpage><lpage>749</lpage><pub-id pub-id-type="pmid">22437053</pub-id></element-citation></ref><ref id="R27"><label>27</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Neubert</surname><given-names>F-X</given-names></name>
<name><surname>Mars</surname><given-names>RB</given-names></name>
<name><surname>Sallet</surname><given-names>J</given-names></name>
<name><surname>Rushworth</surname><given-names>MFS</given-names></name>
</person-group><article-title>Connectivity reveals relationship of brain areas for reward-guided learning and decision making in human and monkey frontal cortex</article-title><source>Proc Natl Acad Sci</source><year>2015</year><volume>112</volume><fpage>E2695</fpage><lpage>E2704</lpage><pub-id pub-id-type="pmid">25947150</pub-id></element-citation></ref><ref id="R28"><label>28</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Desikan</surname><given-names>RS</given-names></name>
<etal/>
</person-group><article-title>An automated labeling system for subdividing the human cerebral cortex on MRI scans into gyral based regions of interest</article-title><source>NeuroImage</source><year>2006</year><volume>31</volume><fpage>968</fpage><lpage>980</lpage><pub-id pub-id-type="pmid">16530430</pub-id></element-citation></ref><ref id="R29"><label>29</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kriegeskorte</surname><given-names>N</given-names></name>
<name><surname>Simmons</surname><given-names>WK</given-names></name>
<name><surname>Bellgowan</surname><given-names>PS</given-names></name>
<name><surname>Baker</surname><given-names>CI</given-names></name>
</person-group><article-title>Circular analysis in systems neuroscience: The dangers of double dipping</article-title><source>Nat Neurosci</source><year>2009</year><volume>12</volume><fpage>535</fpage><lpage>540</lpage><pub-id pub-id-type="pmid">19396166</pub-id></element-citation></ref><ref id="R30"><label>30</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Esterman</surname><given-names>M</given-names></name>
<name><surname>Tamber-Rosenau</surname><given-names>BJ</given-names></name>
<name><surname>Chiu</surname><given-names>YC</given-names></name>
<name><surname>Yantis</surname><given-names>S</given-names></name>
</person-group><article-title>Avoiding non-independence in fMRI data analysis: Leave one subject out</article-title><source>Neuroimage</source><year>2010</year><volume>50</volume><fpage>572</fpage><lpage>576</lpage><pub-id pub-id-type="pmid">20006712</pub-id></element-citation></ref><ref id="R31"><label>31</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Gold</surname><given-names>JI</given-names></name>
<name><surname>Shadlen</surname><given-names>MN</given-names></name>
</person-group><article-title>The neural basis of decision making</article-title><source>Annu Rev Neurosci</source><year>2007</year><volume>30</volume><fpage>535</fpage><lpage>574</lpage><pub-id pub-id-type="pmid">17600525</pub-id></element-citation></ref><ref id="R32"><label>32</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>De Martino</surname><given-names>B</given-names></name>
<name><surname>Fleming</surname><given-names>SM</given-names></name>
<name><surname>Garrett</surname><given-names>N</given-names></name>
<name><surname>Dolan</surname><given-names>RJ</given-names></name>
</person-group><article-title>Confidence in value-based choice</article-title><source>Nat Neurosci</source><year>2013</year><volume>16</volume><fpage>105</fpage><lpage>110</lpage><pub-id pub-id-type="pmid">23222911</pub-id></element-citation></ref><ref id="R33"><label>33</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Stolyarova</surname><given-names>A</given-names></name>
<etal/>
</person-group><article-title>Contributions of anterior cingulate cortex and basolateral amygdala to decision confidence and learning under uncertainty</article-title><source>Nat Commun</source><year>2019</year><volume>10</volume><elocation-id>4704</elocation-id><pub-id pub-id-type="pmid">31624264</pub-id></element-citation></ref><ref id="R34"><label>34</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>van Bergen</surname><given-names>RS</given-names></name>
<name><surname>Jehee</surname><given-names>JFM</given-names></name>
</person-group><article-title>Probabilistic Representation in Human Visual Cortex Reflects Uncertainty in Serial Decisions</article-title><source>J Neurosci</source><year>2019</year><volume>39</volume><fpage>8164</fpage><lpage>8176</lpage><pub-id pub-id-type="pmid">31481435</pub-id></element-citation></ref><ref id="R35"><label>35</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Walker</surname><given-names>EY</given-names></name>
<name><surname>Cotton</surname><given-names>RJ</given-names></name>
<name><surname>Ma</surname><given-names>WJ</given-names></name>
<name><surname>Tolias</surname><given-names>AS</given-names></name>
</person-group><article-title>A neural basis of probabilistic computation in visual cortex</article-title><source>Nat Neurosci</source><year>2020</year><volume>23</volume><fpage>122</fpage><lpage>129</lpage><pub-id pub-id-type="pmid">31873286</pub-id></element-citation></ref><ref id="R36"><label>36</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Fleming</surname><given-names>SM</given-names></name>
<name><surname>Huijgen</surname><given-names>J</given-names></name>
<name><surname>Dolan</surname><given-names>RJ</given-names></name>
</person-group><article-title>Prefrontal Contributions to Metacognition in Perceptual Decision Making</article-title><source>J Neurosci</source><year>2012</year><volume>32</volume><fpage>6117</fpage><lpage>6125</lpage><pub-id pub-id-type="pmid">22553018</pub-id></element-citation></ref><ref id="R37"><label>37</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Grinband</surname><given-names>J</given-names></name>
<name><surname>Hirsch</surname><given-names>J</given-names></name>
<name><surname>Ferrera</surname><given-names>VP</given-names></name>
</person-group><article-title>A neural representation of categorization uncertainty in the human brain</article-title><source>Neuron</source><year>2006</year><volume>49</volume><fpage>757</fpage><lpage>763</lpage><pub-id pub-id-type="pmid">16504950</pub-id></element-citation></ref><ref id="R38"><label>38</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Yoshida</surname><given-names>W</given-names></name>
<name><surname>Ishii</surname><given-names>S</given-names></name>
</person-group><article-title>Resolution of Uncertainty in Prefrontal Cortex</article-title><source>Neuron</source><year>2006</year><volume>50</volume><fpage>781</fpage><lpage>789</lpage><pub-id pub-id-type="pmid">16731515</pub-id></element-citation></ref><ref id="R39"><label>39</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Karlsson</surname><given-names>MP</given-names></name>
<name><surname>Tervo</surname><given-names>DGR</given-names></name>
<name><surname>Karpova</surname><given-names>AY</given-names></name>
</person-group><article-title>Network Resets in Medial Prefrontal Cortex Mark the Onset of Behavioral Uncertainty</article-title><source>Science</source><year>2012</year><volume>338</volume><fpage>135</fpage><lpage>139</lpage><pub-id pub-id-type="pmid">23042898</pub-id></element-citation></ref><ref id="R40"><label>40</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kolling</surname><given-names>N</given-names></name>
<name><surname>Behrens</surname><given-names>TEJ</given-names></name>
<name><surname>Mars</surname><given-names>RB</given-names></name>
<name><surname>Rushworth</surname><given-names>MFS</given-names></name>
</person-group><article-title>Neural mechanisms of foraging</article-title><source>Science</source><year>2012</year><volume>335</volume><fpage>95</fpage><lpage>98</lpage></element-citation></ref><ref id="R41"><label>41</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kolling</surname><given-names>N</given-names></name>
<etal/>
</person-group><article-title>Value, search, persistence and model updating in anterior cingulate cortex</article-title><source>Nat Neurosci</source><year>2016</year><volume>19</volume><fpage>1280</fpage><lpage>1285</lpage><pub-id pub-id-type="pmid">27669988</pub-id></element-citation></ref><ref id="R42"><label>42</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Shenhav</surname><given-names>A</given-names></name>
<name><surname>Cohen</surname><given-names>JD</given-names></name>
<name><surname>Botvinick</surname><given-names>MM</given-names></name>
</person-group><article-title>Dorsal anterior cingulate cortex and the value of control</article-title><source>Nat Neurosci</source><year>2016</year><volume>19</volume><fpage>1286</fpage><lpage>1291</lpage><pub-id pub-id-type="pmid">27669989</pub-id></element-citation></ref><ref id="R43"><label>43</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Bang</surname><given-names>D</given-names></name>
<name><surname>Ershadmanesh</surname><given-names>S</given-names></name>
<name><surname>Nili</surname><given-names>H</given-names></name>
<name><surname>Fleming</surname><given-names>SM</given-names></name>
</person-group><article-title>Private&#x02013;public mappings in human prefrontal cortex</article-title><source>eLife</source><year>2020</year><volume>9</volume><elocation-id>e56477</elocation-id><pub-id pub-id-type="pmid">32701449</pub-id></element-citation></ref><ref id="R44"><label>44</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Morales</surname><given-names>J</given-names></name>
<name><surname>Lau</surname><given-names>HC</given-names></name>
<name><surname>Fleming</surname><given-names>SM</given-names></name>
</person-group><article-title>Domain-general and domain-specific patterns of activity supporting metacognition in human prefrontal cortex</article-title><source>J Neurosci</source><year>2018</year><volume>38</volume><fpage>3534</fpage><lpage>3546</lpage><pub-id pub-id-type="pmid">29519851</pub-id></element-citation></ref><ref id="R45"><label>45</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Shekhar</surname><given-names>M</given-names></name>
<name><surname>Rahnev</surname><given-names>D</given-names></name>
</person-group><article-title>Distinguishing the Roles of Dorsolateral and Anterior PFC in Visual Metacognition</article-title><source>J Neurosci</source><year>2018</year><volume>38</volume><fpage>5078</fpage><lpage>5087</lpage><pub-id pub-id-type="pmid">29720553</pub-id></element-citation></ref><ref id="R46"><label>46</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Shin</surname><given-names>H</given-names></name>
<name><surname>Zou</surname><given-names>Q</given-names></name>
<name><surname>Ma</surname><given-names>WJ</given-names></name>
</person-group><article-title>The effects of delay duration on visual working memory for orientation</article-title><source>J Vis</source><year>2017</year><volume>17</volume><fpage>10</fpage></element-citation></ref><ref id="R47"><label>47</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Harrison</surname><given-names>SA</given-names></name>
<name><surname>Tong</surname><given-names>F</given-names></name>
</person-group><article-title>Decoding reveals the contents of visual working memory in early visual areas</article-title><source>Nature</source><year>2009</year><volume>458</volume><fpage>632</fpage><lpage>635</lpage><pub-id pub-id-type="pmid">19225460</pub-id></element-citation></ref><ref id="R48"><label>48</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Rademaker</surname><given-names>RL</given-names></name>
<name><surname>Chunharas</surname><given-names>C</given-names></name>
<name><surname>Serences</surname><given-names>JT</given-names></name>
</person-group><article-title>Coexisting representations of sensory and mnemonic information in human visual cortex</article-title><source>Nat Neurosci</source><year>2019</year><volume>22</volume><fpage>1336</fpage><lpage>1344</lpage><pub-id pub-id-type="pmid">31263205</pub-id></element-citation></ref><ref id="R49"><label>49</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Li</surname><given-names>H-H</given-names></name>
<name><surname>Sprague</surname><given-names>TC</given-names></name>
<name><surname>Yoo</surname><given-names>AH</given-names></name>
<name><surname>Ma</surname><given-names>WJ</given-names></name>
<name><surname>Curtis</surname><given-names>CE</given-names></name>
</person-group><article-title>Joint representation of working memory and uncertainty in human cortex</article-title><source>Neuron</source><year>2021</year><pub-id pub-id-type="doi">10.1016/j.neuron.2021.08.022</pub-id></element-citation></ref><ref id="R50"><label>50</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Serences</surname><given-names>JT</given-names></name>
</person-group><article-title>Value-Based Modulations in Human Visual Cortex</article-title><source>Neuron</source><year>2008</year><volume>60</volume><fpage>1169</fpage><lpage>1181</lpage><pub-id pub-id-type="pmid">19109919</pub-id></element-citation></ref><ref id="R51"><label>51</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Sereno</surname><given-names>MI</given-names></name>
<etal/>
</person-group><article-title>Borders of multiple visual areas in humans revealed by functional magnetic resonance imaging</article-title><source>Science</source><year>1995</year><volume>268</volume><fpage>889</fpage><lpage>893</lpage><pub-id pub-id-type="pmid">7754376</pub-id></element-citation></ref><ref id="R52"><label>52</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Deyoe</surname><given-names>EA</given-names></name>
<etal/>
</person-group><article-title>Mapping striate and extrastriate visual areas in human cerebral cortex</article-title><source>Proc Natl Acad Sci U S A</source><year>1996</year><volume>93</volume><fpage>2382</fpage><lpage>2386</lpage><pub-id pub-id-type="pmid">8637882</pub-id></element-citation></ref><ref id="R53"><label>53</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Engel</surname><given-names>SA</given-names></name>
<name><surname>Glover</surname><given-names>GH</given-names></name>
<name><surname>Wandell</surname><given-names>BA</given-names></name>
</person-group><article-title>Retinotopic organization in human visual cortex and the spatial precision of functional MRI</article-title><source>Cereb Cortex</source><year>1997</year><volume>7</volume><fpage>181</fpage><lpage>192</lpage><pub-id pub-id-type="pmid">9087826</pub-id></element-citation></ref><ref id="R54"><label>54</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kleiner</surname><given-names>M</given-names></name>
<name><surname>Brainard</surname><given-names>DH</given-names></name>
<name><surname>Pelli</surname><given-names>DG</given-names></name>
</person-group><article-title>What&#x02019;s new in Psychtoolbox-3?</article-title><source>Perception</source><year>2007</year><volume>36</volume></element-citation></ref><ref id="R55"><label>55</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Jenkinson</surname><given-names>M</given-names></name>
<name><surname>Bannister</surname><given-names>P</given-names></name>
<name><surname>Brady</surname><given-names>M</given-names></name>
<name><surname>Smith</surname><given-names>SM</given-names></name>
</person-group><article-title>Improved optimization for the robust and accurate linear registration and motion correction of brain images</article-title><source>NeuroImage</source><year>2002</year><volume>17</volume><fpage>825</fpage><lpage>841</lpage><pub-id pub-id-type="pmid">12377157</pub-id></element-citation></ref><ref id="R56"><label>56</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Reuter</surname><given-names>M</given-names></name>
<name><surname>Schmansky</surname><given-names>NJ</given-names></name>
<name><surname>Rosas</surname><given-names>HD</given-names></name>
<name><surname>Fischl</surname><given-names>B</given-names></name>
</person-group><article-title>Within-subject template estimation for unbiased longitudinal image analysis</article-title><source>NeuroImage</source><year>2012</year><volume>61</volume><fpage>1402</fpage><lpage>1418</lpage><pub-id pub-id-type="pmid">22430496</pub-id></element-citation></ref><ref id="R57"><label>57</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Smith</surname><given-names>SM</given-names></name>
</person-group><article-title>Fast robust automated brain extraction</article-title><source>Hum Brain Mapp</source><year>2002</year><volume>17</volume><fpage>143</fpage><lpage>155</lpage><pub-id pub-id-type="pmid">12391568</pub-id></element-citation></ref><ref id="R58"><label>58</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Smith</surname><given-names>SM</given-names></name>
<name><surname>Brady</surname><given-names>JM</given-names></name>
</person-group><article-title>SUSAN - a new approach to low level image processing</article-title><source>Int J Comput Vis</source><year>1997</year><volume>23</volume><fpage>45</fpage><lpage>78</lpage></element-citation></ref><ref id="R59"><label>59</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Jenkinson</surname><given-names>M</given-names></name>
<name><surname>Beckmann</surname><given-names>CF</given-names></name>
<name><surname>Behrens</surname><given-names>TEJ</given-names></name>
<name><surname>Woolrich</surname><given-names>MW</given-names></name>
<name><surname>Smith</surname><given-names>SM</given-names></name>
</person-group><article-title>FSL</article-title><source>NeuroImage</source><year>2012</year><volume>62</volume><fpage>782</fpage><lpage>790</lpage><pub-id pub-id-type="pmid">21979382</pub-id></element-citation></ref><ref id="R60"><label>60</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Zhang</surname><given-names>Y</given-names></name>
<name><surname>Brady</surname><given-names>M</given-names></name>
<name><surname>Smith</surname><given-names>SM</given-names></name>
</person-group><article-title>Segmentation of brain MR images through a hidden Markov random field model and the expectation-maximization algorithm</article-title><source>IEEE Trans Med Imaging</source><year>2001</year><volume>20</volume><fpage>45</fpage><lpage>57</lpage><pub-id pub-id-type="pmid">11293691</pub-id></element-citation></ref><ref id="R61"><label>61</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Brouwer</surname><given-names>GJ</given-names></name>
<name><surname>Heeger</surname><given-names>DJ</given-names></name>
</person-group><article-title>Cross-orientation suppression in human visual cortex</article-title><source>J Neurophysiol</source><year>2011</year><volume>106</volume><fpage>2108</fpage><lpage>2119</lpage><pub-id pub-id-type="pmid">21775720</pub-id></element-citation></ref><ref id="R62"><label>62</label><element-citation publication-type="book"><person-group person-group-type="author">
<name><surname>Hedges</surname><given-names>LV</given-names></name>
<name><surname>Olkin</surname><given-names>I</given-names></name>
</person-group><source>Statistical methods for meta-analysis</source><publisher-name>Academic Press</publisher-name><year>1985</year></element-citation></ref><ref id="R63"><label>63</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Fieller</surname><given-names>EC</given-names></name>
<name><surname>Pearson</surname><given-names>ES</given-names></name>
</person-group><article-title>Tests for Rank Correlation Coefficients: II</article-title><source>Biometrika</source><year>1961</year><volume>48</volume><fpage>29</fpage></element-citation></ref><ref id="R64"><label>64</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Schuirmann</surname><given-names>DJ</given-names></name>
</person-group><article-title>A comparison of the Two One-Sided Tests Procedure and the Power Approach for assessing the equivalence of average bioavailability</article-title><source>J Pharmacokinet Biopharm</source><year>1987</year><volume>15</volume><fpage>657</fpage><lpage>680</lpage><pub-id pub-id-type="pmid">3450848</pub-id></element-citation></ref><ref id="R65"><label>65</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Cohen</surname><given-names>J</given-names></name>
</person-group><article-title>A Power Primer</article-title><source>Psychol Bull</source><year>1992</year><volume>112</volume><fpage>155</fpage><lpage>159</lpage><pub-id pub-id-type="pmid">19565683</pub-id></element-citation></ref><ref id="R66"><label>66</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Smith</surname><given-names>SM</given-names></name>
<name><surname>Nichols</surname><given-names>TE</given-names></name>
</person-group><article-title>Threshold-free cluster enhancement: Addressing problems of smoothing, threshold dependence and localisation in cluster inference</article-title><source>NeuroImage</source><year>2009</year><volume>44</volume><fpage>83</fpage><lpage>98</lpage><pub-id pub-id-type="pmid">18501637</pub-id></element-citation></ref><ref id="R67"><label>67</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Winkler</surname><given-names>AM</given-names></name>
<name><surname>Ridgway</surname><given-names>GR</given-names></name>
<name><surname>Webster</surname><given-names>MA</given-names></name>
<name><surname>Smith</surname><given-names>SM</given-names></name>
<name><surname>Nichols</surname><given-names>TE</given-names></name>
</person-group><article-title>Permutation inference for the general linear model</article-title><source>NeuroImage</source><year>2014</year><volume>92</volume><fpage>381</fpage><lpage>397</lpage><pub-id pub-id-type="pmid">24530839</pub-id></element-citation></ref><ref id="R68"><label>68</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Nichols</surname><given-names>TE</given-names></name>
<name><surname>Hayasaka</surname><given-names>S</given-names></name>
</person-group><article-title>Controlling the familywise error rate in functional neuroimaging: A comparative review</article-title><source>Stat Methods Med Res</source><year>2003</year><volume>12</volume><fpage>419</fpage><lpage>446</lpage><pub-id pub-id-type="pmid">14599004</pub-id></element-citation></ref><ref id="R69"><label>69</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Sallet</surname><given-names>J</given-names></name>
<etal/>
</person-group><article-title>The organization of dorsal frontal cortex in humans and macaques</article-title><source>J Neurosci</source><year>2013</year><volume>33</volume><fpage>12255</fpage><lpage>12274</lpage><pub-id pub-id-type="pmid">23884933</pub-id></element-citation></ref><ref id="R70"><label>70</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Furmanski</surname><given-names>CS</given-names></name>
<name><surname>Engel</surname><given-names>SA</given-names></name>
</person-group><article-title>An oblique effect in human primary visual cortex</article-title><source>Nat Neurosci</source><year>2000</year><volume>3</volume><fpage>535</fpage><lpage>536</lpage><pub-id pub-id-type="pmid">10816307</pub-id></element-citation></ref><ref id="R71"><label>71</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Wei</surname><given-names>XX</given-names></name>
<name><surname>Stocker</surname><given-names>AA</given-names></name>
</person-group><article-title>A Bayesian observer model constrained by efficient coding can explain &#x0201c;anti-Bayesian&#x0201d; percepts</article-title><source>Nat Neurosci</source><year>2015</year><volume>18</volume><fpage>1509</fpage><lpage>1517</lpage><pub-id pub-id-type="pmid">26343249</pub-id></element-citation></ref><ref id="R72"><label>72</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Dworkin</surname><given-names>JD</given-names></name>
<etal/>
</person-group><article-title>The extent and drivers of gender imbalance in neuroscience reference lists</article-title><source>Nat Neurosci</source><year>2020</year><volume>23</volume><fpage>918</fpage><lpage>926</lpage><pub-id pub-id-type="pmid">32561883</pub-id></element-citation></ref><ref id="R73"><label>73</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Ress</surname><given-names>D</given-names></name>
<name><surname>Backus</surname><given-names>BT</given-names></name>
<name><surname>Heeger</surname><given-names>DJ</given-names></name>
</person-group><article-title>Activity in primary visual cortex predicts performance in a visual detection task</article-title><source>Nat Neurosci</source><year>2000</year><volume>3</volume><fpage>940</fpage><lpage>945</lpage><pub-id pub-id-type="pmid">10966626</pub-id></element-citation></ref><ref id="R74"><label>74</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Baird</surname><given-names>B</given-names></name>
<name><surname>Smallwood</surname><given-names>J</given-names></name>
<name><surname>Gorgolewski</surname><given-names>KJ</given-names></name>
<name><surname>Margulies</surname><given-names>DS</given-names></name>
</person-group><article-title>Medial and lateral networks in anterior prefrontal cortex support metacognitive ability for memory and perception</article-title><source>J Neurosci</source><year>2013</year><volume>33</volume><fpage>16657</fpage><lpage>16665</lpage><pub-id pub-id-type="pmid">24133268</pub-id></element-citation></ref><ref id="R75"><label>75</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>McCurdy</surname><given-names>LY</given-names></name>
<etal/>
</person-group><article-title>Anatomical coupling between distinct metacognitive systems for memory and visual perception</article-title><source>J Neurosci</source><year>2013</year><volume>33</volume><fpage>1897</fpage><lpage>1906</lpage><pub-id pub-id-type="pmid">23365229</pub-id></element-citation></ref><ref id="R76"><label>76</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Ye</surname><given-names>Q</given-names></name>
<name><surname>Zou</surname><given-names>F</given-names></name>
<name><surname>Lau</surname><given-names>H</given-names></name>
<name><surname>Hu</surname><given-names>Y</given-names></name>
<name><surname>Kwok</surname><given-names>SC</given-names></name>
</person-group><article-title>Causal evidence for mnemonic metacognition in human precuneus</article-title><source>J Neurosci</source><year>2018</year><volume>38</volume><fpage>6379</fpage><lpage>6387</lpage><pub-id pub-id-type="pmid">29921714</pub-id></element-citation></ref><ref id="R77"><label>77</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Drugowitsch</surname><given-names>J</given-names></name>
<name><surname>Mendon&#x000e7;a</surname><given-names>AG</given-names></name>
<name><surname>Mainen</surname><given-names>ZF</given-names></name>
<name><surname>Pouget</surname><given-names>A</given-names></name>
</person-group><article-title>Learning optimal decisions with confidence</article-title><source>Proc Natl Acad Sci</source><year>2019</year><volume>116</volume><fpage>24872</fpage><lpage>24880</lpage><pub-id pub-id-type="pmid">31732671</pub-id></element-citation></ref><ref id="R78"><label>78</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Kastner</surname><given-names>S</given-names></name>
<name><surname>Pinsk</surname><given-names>MA</given-names></name>
<name><surname>Weerd</surname><given-names>P</given-names></name>
<name><surname>De,Desimone</surname><given-names>R</given-names></name>
<name><surname>Ungerleider</surname><given-names>LG</given-names></name>
</person-group><article-title>Increased Activity in Human Visual Cortex during Directed Attention in the Absence of Visual Stimulation</article-title><source>Neuron</source><year>1999</year><volume>22</volume><fpage>751</fpage><lpage>761</lpage><pub-id pub-id-type="pmid">10230795</pub-id></element-citation></ref><ref id="R79"><label>79</label><element-citation publication-type="journal"><person-group person-group-type="author">
<name><surname>Sreenivasan</surname><given-names>KK</given-names></name>
<name><surname>D&#x02019;Esposito</surname><given-names>M</given-names></name>
</person-group><article-title>The what, where and how of delay activity</article-title><source>Nat Rev Neurosci</source><year>2019</year><volume>20</volume><fpage>466</fpage><lpage>481</lpage><pub-id pub-id-type="pmid">31086326</pub-id></element-citation></ref></ref-list></back><floats-group><fig position="float" id="F1"><label>Fig. 1</label><caption><title>Overview of sources of noise and three observer models.</title><p>The observers&#x02019; task is to estimate the presented stimulus orientation <italic toggle="yes">s</italic> from a noisy measurement m. Multiple sources of noise affect the perceptual decision-making process. The measurements (<italic toggle="yes">m</italic>) vary from trial to trial due to sensory sources of noise, which can be decomposed into stimulus-related (<italic toggle="yes">&#x003c3;<sub>s<sub>o</sub>
</sub>
</italic>
<sup>2</sup>) and stimulus-independent (<italic toggle="yes">&#x003c3;<sub>s<sub>i</sub>
</sub>
</italic>
<sup>2</sup>) noise, as well as (unexplained) downstream (non-sensory) noise (<italic toggle="yes">&#x003c3;</italic>
<sub>
<italic toggle="yes">n</italic>
</sub>
<sup>2</sup>). The observers compute their stimulus estimates &#x0015d; as the mean of the posterior distribution <italic toggle="yes">p</italic>(<italic toggle="yes">s</italic>|<italic toggle="yes">m</italic>). The internal orientation estimate is transformed into a behavioral (overt) response <italic toggle="yes">r</italic>, which is subject to further noise (<italic toggle="yes">&#x003c3;<sub>r</sub>
</italic>
<sup>2</sup>). The observer also gives their level of confidence in this behavioral estimate. The Bayesian observer computes confidence as a function of the expected distance between latent stimulus and response, which depends on both the response itself, and the width of the posterior <italic toggle="yes">p</italic>(<italic toggle="yes">s</italic>|<italic toggle="yes">m</italic>), which incorporates all sources of measurement noise. The Stimulus heuristics observer computes confidence as a function of their perceptual orientation estimate (<italic toggle="yes">&#x0015d;</italic>). The Response heuristics observer computes confidence as a function of the distance between internal orientation estimate (<italic toggle="yes">&#x0015d;</italic>) and overt motor response (<italic toggle="yes">r</italic>). Both Heuristics observers ignore the degree of uncertainty in their orientation estimates when computing confidence.</p></caption><graphic xlink:href="EMS137960-f001" position="float"/></fig><fig position="float" id="F2"><label>Fig. 2</label><caption><title>Ideal observer predictions.</title><p>(a) Relationship between confidence and behavioral variability for a uniform stimulus distribution (orientation range: 0-179&#x000b0;). Trials (N = 50,000) were binned into ten equal-size bins of increasing confidence. For each bin, the variance of orientation estimation errors was computed and plotted against the mean level of confidence in that bin. Lines represent best linear fits. (b) Same as (a), but holding the stimulus constant. Confidence values were z-scored per observer such that they fall in the same range for all models. (c) Relationship between sensory uncertainty and confidence for a uniform stimulus distribution (orientation range: 0-179&#x000b0;). For visualization purposes, trials were binned into ten equal-sized bins of increasing uncertainty. The mean of both confidence and sensory uncertainty was computed across all trials in each bin, and is shown in the plot. Lines represent linear best fits computed on single-trial (unbinned) data. (d) Same as (c), but controlled for stimulus orientation. (a-d) Insets indicate, for each model, whether there is a relationship between the plotted variables (&#x02713;), or not (&#x000f8;). For ease of exposition, the group mean was added to the residuals plotted in these (b, d) and subsequent <xref rid="F3" ref-type="fig">figures (3a)</xref>.</p></caption><graphic xlink:href="EMS137960-f002" position="float"/></fig><fig position="float" id="F3"><label>Fig. 3</label><caption><title>Reported confidence, behavioral variability, and decoded sensory uncertainty.</title><p>(a) Behavioral variability decreases as confidence increases (left panel: t(287) = -16.79, p &#x0003c; 0.001, r = -0.70, 95% CI = [-0.73, -0.67]); right panel: t(286) = -11.02, p &#x0003c; 0.001, r = -0.55, 95% CI = [-0.59, -.050]). Shade of blue indicates ten within-observer bins of increasing confidence. Dots represent single observers (N = 32). (b-c) Decoded sensory uncertainty reliably predicts reported confidence. (b) Example observer (S24; left panel: z = -1.67, p = 0.047, &#x003c1; = -0.078, 95% CI = [-0.17, 0.014]; right panel: z = -1.52, p = 0.064, &#x003c1; = -0.071, 95% CI = [-0.16, 0.021]). Analyses were performed on trial-by-trial data (N = 493); data were binned (ten bins) for visualization only. Error bars represent &#x000b1; 1 s.e.m. (c) Group average (red line; shaded area represents &#x000b1; 1 s.e.m.), probability density, and individual correlation coefficients (Left panel: z = -2.17, p = 0.015, &#x003c1; = -0.018, 95% CI = [0.035, -0.0018]; right panel: z = -1.53, p = 0.063, &#x003c1; = -0.013, 95% CI = [-.030, .0036]). Gray dots indicate observers (N = 32), circle denotes S24.</p></caption><graphic xlink:href="EMS137960-f003" position="float"/></fig><fig position="float" id="F4"><label>Fig. 4</label><caption><title>Activity in dorsal anterior insula (dAI), dorsal anterior cingulate cortex (dACC), and left rostrolateral prefrontal cortex (L rlPFC) over time.</title><p>(a) Downstream clusters significantly modulated by uncertainty decoded from visual cortex (p&#x0003c;0.05 FWER-controlled) (data were masked to exclude occipital cortex; <xref rid="SD5" ref-type="supplementary-material">Supplementary Table 1</xref> gives an overview of all activations and see <xref rid="SD4" ref-type="supplementary-material">Supplementary Data</xref> for whole-brain maps). (b) Cortical response in dAI for high versus low decoded uncertainty (top) and high versus low reported confidence (bottom), averaged across all observers. Trials were binned by a median split per observer. Black arrows indicate the data presented in c and d. (c) Example observer S24. The observer&#x02019;s cortical response tentatively increases with decoded uncertainty and decreases with reported confidence. Trials (N = 493) were binned (ten bins) for visualization only, correlation coefficients were computed from trial-by-trial data. (d) Group average (red/blue line) and correlation coefficients for individual observers (gray dots; N = 32). Shown is the relationship between cortical response amplitude and decoded uncertainty (top) or reported confidence (bottom). Both effects are statistically significant (permutation test; uncertainty: &#x003c1; = 0.044, p &#x0003c; 0.001; confidence: &#x003c1; = -0.047, p &#x0003c; 0.001). (e) Group-averaged correlation coefficient between cortical response amplitude and decoded uncertainty (red) or reported confidence (blue). Horizontal lines indicate statistical significance (p&#x0003c;0.05, FWER-controlled). Arrows indicate data in d. (b,e) Dark gray area marks stimulus presentation window, light gray area represents response window. (b-e) Shaded areas and error bars denote &#x000b1; 1 s.e.m.</p></caption><graphic xlink:href="EMS137960-f004" position="float"/></fig></floats-group></article>