<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Microbiome</journal-id><journal-id journal-id-type="iso-abbrev">Microbiome</journal-id><journal-title-group><journal-title>Microbiome</journal-title></journal-title-group><issn pub-type="epub">2049-2618</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6114274</article-id><article-id pub-id-type="publisher-id">532</article-id><article-id pub-id-type="doi">10.1186/s40168-018-0532-2</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>A multi-source domain annotation pipeline for quantitative metagenomic and metatranscriptomic functional profiling</article-title></title-group><contrib-group><contrib contrib-type="author" equal-contrib="yes"><name><surname>Ugarte</surname><given-names>Ari</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" equal-contrib="yes"><name><surname>Vicedomini</surname><given-names>Riccardo</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Bernardes</surname><given-names>Juliana</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0003-2098-5743</contrib-id><name><surname>Carbone</surname><given-names>Alessandra</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2112 9282</institution-id><institution-id institution-id-type="GRID">grid.4444.0</institution-id><institution>Sorbonne Universit&#x000e9;, UPMC-Univ P6, CNRS, IBPS, Laboratoire de Biologie Computationnelle et Quantitative - UMR 7238, </institution></institution-wrap>4 Place Jussieu, Paris, 75005 France </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2112 9282</institution-id><institution-id institution-id-type="GRID">grid.4444.0</institution-id><institution>Sorbonne Universit&#x000e9;, UPMC-Univ P6, CNRS, Institut des Sciences du Calcul et des Donnees, </institution></institution-wrap>4 Place Jussieu, Paris, 75005 France </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 1931 4817</institution-id><institution-id institution-id-type="GRID">grid.440891.0</institution-id><institution>Institut Universitaire de France, </institution></institution-wrap>Paris, 75005 France </aff></contrib-group><pub-date pub-type="epub"><day>28</day><month>8</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>28</day><month>8</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>6</volume><elocation-id>149</elocation-id><history><date date-type="received"><day>27</day><month>2</month><year>2018</year></date><date date-type="accepted"><day>13</day><month>8</month><year>2018</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold> This article is distributed under the terms of the Creative Commons Attribution 4.0 International License(<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver(<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p>Biochemical and regulatory pathways have until recently been thought and modelled within one cell type, one organism and one species. This vision is being dramatically changed by the advent of whole microbiome sequencing studies, revealing the role of symbiotic microbial populations in fundamental biochemical functions. The new landscape we face requires the reconstruction of biochemical and regulatory pathways at the community level in a given environment. In order to understand how environmental factors affect the genetic material and the dynamics of the expression from one environment to another, we want to evaluate the quantity of gene protein sequences or transcripts associated to a given pathway by precisely estimating the abundance of protein domains, their weak presence or absence in environmental samples.</p></sec><sec><title>Results</title><p>MetaCLADE is a novel profile-based domain annotation pipeline based on a multi-source domain annotation strategy. It applies directly to reads and improves identification of the catalog of functions in microbiomes. MetaCLADE is applied to simulated data and to more than ten metagenomic and metatranscriptomic datasets from different environments where it outperforms InterProScan in the number of annotated domains. It is compared to the state-of-the-art non-profile-based and profile-based methods, UProC and HMM-GRASPx, showing complementary predictions to UProC. A combination of MetaCLADE and UProC improves even further the functional annotation of environmental samples.</p></sec><sec><title>Conclusions</title><p>Learning about the functional activity of environmental microbial communities is a crucial step to understand microbial interactions and large-scale environmental impact. MetaCLADE has been explicitly designed for metagenomic and metatranscriptomic data and allows for the discovery of patterns in divergent sequences, thanks to its multi-source strategy. MetaCLADE highly improves current domain annotation methods and reaches a fine degree of accuracy in annotation of very different environments such as soil and marine ecosystems, ancient metagenomes and human tissues.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s40168-018-0532-2) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Domain annotation</kwd><kwd>Metagenomic</kwd><kwd>Metatranscriptomic</kwd><kwd>Functional annotation</kwd><kwd>Probabilistic model</kwd><kwd>Environment</kwd><kwd>Motif</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100006072</institution-id><institution>Commissariat G&#x000e9;n&#x000e9;ral &#x000e0; l'Investissement</institution></institution-wrap></funding-source><award-id>ANR-11-LABX-0037-01</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100006072</institution-id><institution>Commissariat G&#x000e9;n&#x000e9;ral &#x000e0; l'Investissement</institution></institution-wrap></funding-source><award-id>ANR-11-IDEX-0004-02</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100006072</institution-id><institution>Commissariat G&#x000e9;n&#x000e9;ral &#x000e0; l'Investissement</institution></institution-wrap></funding-source><award-id>ANR-10-EQPX- 29-01</award-id></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100004562</institution-id><institution>Minist&#x000e8;re de l'Education Nationale, de l'Enseignement Superieur et de la Recherche</institution></institution-wrap></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100004795</institution-id><institution>Institut Universitaire de France</institution></institution-wrap></funding-source></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>Ecosystem changes are often correlated with the presence of new communities disturbing their stability by importing new metabolic activities [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR4">4</xref>]. Very often, such communities, their functional behaviour and their mutual interactions are hard to identify and analyse [<xref ref-type="bibr" rid="CR5">5</xref>&#x02013;<xref ref-type="bibr" rid="CR9">9</xref>]. Unraveling their structure and determining what they functionally do is crucial for understanding their metabolic dynamics and activities.</p><p>Computational studies improving the detection of the functional preferences of environmental communities are important for gaining insight into ecosystem changes [<xref ref-type="bibr" rid="CR10">10</xref>&#x02013;<xref ref-type="bibr" rid="CR15">15</xref>]. Ideally, they shall quantitatively relate genetic information with environmental factors in order to understand how these factors affect the genetic material and the dynamics of the expression from one environment to another, from one community to another. Therefore, they demand the development of appropriate tools to zoom in metabolic activities and to compare environments in detail being as precise as possible in evaluating the quantity of genetic material (gene protein sequences or transcripts) associated to a given function.</p><p>Over the past years, a lot of effort has been devoted to the creation of integrated systems for the computational analysis of metagenomic (MG) and metatranscriptomic (MT) datasets. Several pipelines conducting data pre-processing, assembly, taxonomic characterisation, gene finding, protein-coding gene annotation and pathway reconstruction, such as MinPath [<xref ref-type="bibr" rid="CR16">16</xref>], ShotgunFunctionalizeR [<xref ref-type="bibr" rid="CR17">17</xref>], CAMERA [<xref ref-type="bibr" rid="CR18">18</xref>], CoMet [<xref ref-type="bibr" rid="CR19">19</xref>], IMG/M [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR21">21</xref>], MetaPath [<xref ref-type="bibr" rid="CR22">22</xref>], PICRUSt [<xref ref-type="bibr" rid="CR23">23</xref>], Genometa [<xref ref-type="bibr" rid="CR24">24</xref>], MetaPathway [<xref ref-type="bibr" rid="CR25">25</xref>], COGNIZER [<xref ref-type="bibr" rid="CR26">26</xref>], MG-RAST [<xref ref-type="bibr" rid="CR27">27</xref>, <xref ref-type="bibr" rid="CR28">28</xref>], MEGAN [<xref ref-type="bibr" rid="CR29">29</xref>] and MOCAT2 [<xref ref-type="bibr" rid="CR30">30</xref>], have been proposed [<xref ref-type="bibr" rid="CR31">31</xref>]. For functional characterisation, protein gene annotation remains a fundamentally difficult task that still needs to be improved in order to better understand the billions of sequences that remain functionally unannotated [<xref ref-type="bibr" rid="CR32">32</xref>, <xref ref-type="bibr" rid="CR33">33</xref>]. One difficulty comes from the fact that most environmental coding sequences present no or very weak similarity with known sequences and that many of them might be new genes with novel functions. In practice, they often do not match reference databases or they match with very low significance scores [<xref ref-type="bibr" rid="CR34">34</xref>], leading to a poor functional annotation [<xref ref-type="bibr" rid="CR32">32</xref>]. A second difficulty is that environmental coding sequences are fragmented and annotation of partial information becomes harder due to a much reduced sequence length. In this respect, since protein-coding sequences might be too long compared to reads, in environmental sequence classification, one can either realise a simultaneous alignment and assembly of reads using reference proteins or probabilistic protein sequence profiles [<xref ref-type="bibr" rid="CR35">35</xref>&#x02013;<xref ref-type="bibr" rid="CR37">37</xref>] hoping to improve the sensitivity to detect significant matches, or can focus on annotating protein domains directly on sequencing reads [<xref ref-type="bibr" rid="CR38">38</xref>]. Indeed, domains are functional units, much shorter than proteins: even though their sizes vary from a few tens up to several hundreds of amino acids, 90% of the known domains are smaller than 200 aa with mean size of 100 aa [<xref ref-type="bibr" rid="CR39">39</xref>&#x02013;<xref ref-type="bibr" rid="CR41">41</xref>]. Despite their short length, they are sufficiently precise to inform us about the potential functional activity of the communities. In particular, direct read annotation will become increasingly important in the future, due to its contribution in the design of computationally efficient and precise assembly algorithms [<xref ref-type="bibr" rid="CR42">42</xref>]. With the production of larger and larger MG/MT datasets and the exploration of new environments (possibly gathering many unknown species), contig reconstruction might become even more challenging if realised without the help of domain annotation.</p><p>Here, we introduce MetaCLADE, a new generation method for the annotation of protein domains in MG/MT reads. MetaCLADE uses a multi-source annotation strategy [<xref ref-type="bibr" rid="CR43">43</xref>], where each known domain is represented by a few hundred probabilistic models and an intelligent algorithmic strategy filters the high number of hits produced by the models, retaining only the most reliable ones. These models, called clade-centered models (CCMs), span regions of the protein sequence space that are usually not well represented in a model based on a global sequence consensus (SCM) [<xref ref-type="bibr" rid="CR44">44</xref>&#x02013;<xref ref-type="bibr" rid="CR47">47</xref>]. They might highlight motifs, structural or physico-chemical properties characteristic of divergent homologous sequences. Hence, if a domain is associated to many divergent homologs, CCMs are expected to describe properties that could be missed by the SCM representing a global consensus. For this reason, CCMs should help in finding diverged homologous sequences in species that might be phylogenetically distant.</p><p>The great improvement in annotation obtained with the multi-source strategy compared to the mono-source strategy, employed by the two most commonly used annotation tools HMMer [<xref ref-type="bibr" rid="CR47">47</xref>] and HHblits [<xref ref-type="bibr" rid="CR48">48</xref>, <xref ref-type="bibr" rid="CR49">49</xref>], was proven for CLADE in [<xref ref-type="bibr" rid="CR43">43</xref>, <xref ref-type="bibr" rid="CR50">50</xref>] for genomes and, more generally, for datasets of complete coding sequences. Many different validation tests have been developed in [<xref ref-type="bibr" rid="CR43">43</xref>], where, in particular, it has been shown that many of the annotations realised with CLADE based on domains present in Pfam24 and considered false positives by Pfam24 were finally validated by the Pfam27 release, this augmenting the confidence on CLADE annotation of protein sequences. With the introduction of MetaCLADE, however, we push forward this idea and we show that CCMs can also be used to successfully annotate fragmented coding sequences in MG/MT datasets, where domain divergence and species variability might be very large. Under the hypothesis that the most populated functional classes define community preferences, the high-quality quantification of domains in reads reached with MetaCLADE allows us to infer domain functional classification and the functional importance of species in a community. Differences in domain counts for specific functional classes can also be used to compare and characterise environments.</p><p>For the annotation of MG/MT sequences, the main improvement of MetaCLADE over CLADE lies in the manner clade-centered models are handled. In fact, MetaCLADE employs neither the machine-learning algorithm for domain annotation nor the algorithm constructing the most likely protein domain architecture, characterising the two main algorithmic steps in CLADE. Due to the traits of MG/MT reads compared to full protein sequences (e.g., the very short length), the design of a specific computational method was demanded. Hence, in order to annotate fragmented domains and evaluate much shorter hits, MetaCLADE was designed around two main contributions. First, it introduces a novel definition for a domain-specific bi-dimensional gathering threshold based on a probability space constructed using a naive Bayes classifier. Second, it simplifies the hit selection so that domain annotation would be less sensitive to sequencing errors and hit length.</p><p>In order to show how MetaCLADE performs with respect to known domain annotation methods, we realised multiple comparative analyses of MG/MT datasets and demonstrated that MetaCLADE improves domain annotation and provides an improved resolution of the functional activity of a community, while clarifying preferences and missing functional features. We compared MetaCLADE against InterProScan [<xref ref-type="bibr" rid="CR51">51</xref>, <xref ref-type="bibr" rid="CR52">52</xref>], a tool that combines different protein signature recognition methods and different domain databases; UProC [<xref ref-type="bibr" rid="CR38">38</xref>], a fast and sensitive algorithm that applies directly to raw reads; and the profile-based assembler HMM-GRASPx [<xref ref-type="bibr" rid="CR37">37</xref>]. These are either new or most commonly used metagenomic annotation tools and, as they are based on Pfam, we can fairly compare MetaCLADE to them. We used several datasets, based on either real or simulated MG/MT sequences, with different characteristics, such as read length (100 bp versus 200 bp) and non-uniform species relative abundance. The simulated datasets have been generated either from known complete genomes or, more realistically, from MG datasets.</p></sec><sec id="Sec2" sec-type="results"><title>Results</title><p>Protein domains found in short MG and MT reads can be used as precise markers of the functional activity of an environment. We show that MetaCLADE highly improves current annotation methods and reaches a very fine degree of accuracy in annotation.</p><p>MetaCLADE workflow is illustrated in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref><xref rid="Fig1" ref-type="fig">a</xref>. It takes a dataset of reads (with an average size between 100 and 500 nucleotides) in input and searches for domains using a library of more than two million probabilistic models (CCMs and SCMs) [<xref ref-type="bibr" rid="CR43">43</xref>] associated to almost 15,000 Pfam domains. For each domain, hundreds of CCMs have been generated from homologous sequences representing the entire phylogenetic tree of life. The spread of species whose sequences were used for CCM construction is illustrated by the phylogenetic tree in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref><xref rid="Fig1" ref-type="fig">b</xref>, where each leaf corresponds to a different species. In contrast, the distribution of models constructed from sequences coming from different clades of the phylogenetic tree is illustrated in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref><xref rid="Fig1" ref-type="fig">c</xref>. We note that the total number of models constructed from bacterial species is higher than from eukaryotes and even higher than from archaea. Indeed, a species contributes to at most one model for a domain and can be used for constructing several models for different domains. As an example, the number of species considered for Bacteria and Viridiplantae is roughly the same (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref><xref rid="Fig1" ref-type="fig">b</xref>) while the number of models constructed from bacterial sequences is an order of magnitude higher than those from Viridiplantae (1.3e + 6 vs 1e + 5, see Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref><xref rid="Fig1" ref-type="fig">c</xref>).
<fig id="Fig1"><label>Fig. 1</label><caption><p>The MetaCLADE workflow. <bold>a</bold> The MetaCLADE workflow is described in the rectangular green box: the two MetaCLADE main steps are illustrated in white boxes. MetaCLADE input data is constituted by (i) a set of reads to be annotated where ORFs have been already identified and (ii) the CLADE model library. The CLADE model library is used to identify all domain hits for a ORF. The large set of identified hits is then combined with gathering thresholds pre-computed for each domain model (pink box), to realise the second main step in MetaCLADE (right white box): overlapping domain hits are selected based on three filtering features. The output of the workflow is an annotation of the ORF, possibly constituted by several domains. The figure illustrates the best expected annotation of a ORF, that is a domain with, possibly, some domain fragments surrounding it. The rectangular pink box illustrates the pre-computed step. For each domain, the CLADE library contains several hundreds of models that are used in MetaCLADE to identify the hits. For domain <italic>D</italic><sup>1</sup>, considered in the blue cylinder on the left, the model library contains the consensus model (blue coloured line, bottom) and hundreds of CCMs generated from sequences that are spread through the phylogenetic tree of species. Coloured lines represent models constructed from sequences coming from phylogenetic clades coloured on the same colour tone. The blue box on the right illustrates the pre-computation of the domain-specific parameters for the discrimination of positive (light blue, yellow and dark red) from negative (blue, orange and red) sequences. Dots in the plots correspond to sequences. The sequence spaces defined by bit-scores and mean-bit-scores (white plots) and the probability spaces (plots where probabilities are associated to regions) obtained by the naive Bayes classifier are given. <bold>b</bold> Phylogenetic tree of species that generated the CCMs used in MetaCLADE [<xref ref-type="bibr" rid="CR43">43</xref>]. <bold>c</bold> Histogram reporting the number of CCMs available in MetaCLADE, organised by clades</p></caption><graphic xlink:href="40168_2018_532_Fig1_HTML" id="MO1"/></fig>
</p><p>Given a read, a large number of domain hits is produced from the large number of models. MetaCLADE filters them according to three main criteria, applied one after the other to obtain the most likely annotation for each read. The first selection criteria filters out all overlapping hits for the same domain by using the match best score. Note that for any domain identified in a region of the read, it keeps exactly one domain hit per region. Also, note that a read might contain more than one non-overlapping occurrence of the same domain. This filter constitutes the first rough selection eliminating redundant hits and keeping different domains. The second criteria is the heart of the selection step and filters out most hits by keeping only those having a very high probability of being true hits. Probabilities are estimated in MetaCLADE through a pre-computed step that divides the sequence space of each domain in probability regions describing whether a hit (possibly a fragment of a domain) can be accepted with a certain confidence or not. This filter might eliminate all hits associated to a given domain. Finally, the third step selects hits that might be associated to very similar domains. Often, the hit matches produced by models associated to similar domains are overlapping. MetaCLADE carefully evaluates them and selects the hit with a highest sequence identity to the consensus sequence of the model and the highest bit-score (see &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section).</p><p>A main important methodological point in MetaCLADE concerns the explicit estimation of the likelihood of a hit to be a true domain. This estimation is particularly sensitive to the length of the hit, that in MG and MT data might be very small; namely, for each domain, MetaCLADE defines a two-dimensional gathering threshold (GA) by combining bit-score and mean-bit-score of the domain hit and by identifying multiple regions in the two-dimensional sequence space that, with a high probability, contain reliable annotations for short sequences. All computational details of the approach (i.e., algorithms, statistical models and parameter thresholds) are described in the &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section. Differences between MetaCLADE and CLADE are listed in the &#x0201c;<xref rid="Sec13" ref-type="sec">Discussion</xref>&#x0201d; section.</p><p>Annotated MG and MT datasets can be explored to learn about the functional activity of the community. For this, one has to properly evaluate the performance of the methods and this was done on simulated datasets and on several published MG/MT datasets. MetaCLADE&#x02019;s improvement over HMMer was shown on a simulated dataset, generated from 56 completely sequenced genomes with the addition of sequencing errors, and on five ocean MT samples. Five more environmental samples, produced with 454 GS FLX Titanium technology, demonstrated that MetaCLADE annotation highly improves over InterProScan (run with different libraries). One more environmental sample, produced with Illumina HiSeq 2000 technology, confirmed MetaCLADE&#x02019;s good performance on datasets of much shorter reads.</p><p>To compare MetaCLADE against two state-of-the-art annotation tools designed for short sequences, HMM-GRASPx and UProC, we used environmental samples, a simulated dataset previously employed to evaluate HMM-GRASPx, and two datasets simulated directly from MG sequences. These datasets are different in terms of average read length and species coverage, and they highlight the complementarity of MetaCLADE compared to UProC with respect to reads of shorter (100 bp) and longer (200 bp) size. They help to show that a combination of the two tools can improve performances even more.</p><sec id="Sec3"><title>Comparison of MetaCLADE and HMMer on a simulated metagenomic dataset</title><p>We simulated a 454-read dataset from 56 fully sequenced genomes&#x02014;belonging to archaea and bacteria&#x02014;and accounting for a total size of 187 Mbp (NCBI&#x02019;s accession numbers and genome sizes are shown in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S2.) Genomes have been fragmented with MetaSim [<xref ref-type="bibr" rid="CR53">53</xref>] and the outcoming clones have been parsed with FlowSim [<xref ref-type="bibr" rid="CR54">54</xref>], to simulate realistic insertion and deletion errors expected during DNA sequencing. FlowSim was used with the error model of the &#x0201c;454 Titanium FLX&#x0201d; sequencing platform (error rate &#x0223c;1<italic>%</italic>). This resulted in about 500,000 reads, with an average size of 523 bp, that were given as input to MetaCLADE.</p><p>Even though 454 is becoming nowadays a sequencing platform less and less used, we considered it in order to compare MetaCLADE to HMMer on reads that are not short. Indeed, HMMer was previously shown not to perform well on short fragments [<xref ref-type="bibr" rid="CR38">38</xref>].</p><p>The performance of MetaCLADE and HMMer on the aforementioned ORFs was computed on a ground-truth defined as follows: from each genome, we considered its CoDing Sequence (CDS) in NCBI and retained only those also defined by Swiss-Prot (June 2016 release). CDS regions were further enriched with the available Pfam domain annotation (version 29). Then, the set of true positives has been defined by those ORFs overlapping such Pfam annotations.</p><p>Predictions made by MetaCLADE and HMMer have been compared on two levels by considering or not as true positives (or true negatives) the predictions which fell into the same Pfam clan [<xref ref-type="bibr" rid="CR55">55</xref>] or InterPro family [<xref ref-type="bibr" rid="CR56">56</xref>]. In the clan-oriented annotation, MetaCLADE and HMMer were able to obtain an F-score of 98.57 and 99.56 respectively. However, MetaCLADE was able to identify more domains (86.2<italic>%</italic>) than HMMer (74.8<italic>%</italic>). Instead, if the comparison does not take into account the clans, the F-scores fall down to 95.22 and 98.16 (with 83.3 and 73.8<italic>%</italic> of recovered domains) for MetaCLADE and HMMer, respectively. In both these analyses, we notice that MetaCLADE presents a higher number of false positives and false negatives with respect to HMMer. This is expected as Swiss-Prot&#x02019;s annotations&#x02014;which we used to define the ground-truth&#x02014;are based on Pfam and hence on HMMer. A synthesis of the comparison is reported in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S3.</p><p>Finally, it is interesting to point out that the 13.8<italic>%</italic> of MetaCLADE&#x02019;s missed domains belonged to very small fragments, with an average length of 38 aa. The distribution of <italic>E</italic> values for domains annotated by MetaCLADE is plotted in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S6A (a similar analysis which considers the TrEMBL annotation is available in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S6B).</p></sec><sec id="Sec4"><title>Functional annotation of large oceanic metatranscriptomic samples</title><p>Domain identification allows to highlight the main functional activities of a community through the identification of the functions supported by the most abundant domains, but also to compare communities and organisms. A more accurate zooming into functional activities hopefully leads to capture missing features of communities&#x02019; behaviour.</p><p>The functional annotation of domains in the five oceans MT datasets in [<xref ref-type="bibr" rid="CR57">57</xref>] demonstrates a sharp difference in relative abundance of domains found by MetaCLADE compared to HMMer (hmmscan) (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">a</xref>). MetaCLADE shows that the larger amount of domains it detects falls coherently in functional classes of interest for specific environments, reaching a much better resolution of sign ificant terms among all Metagenomic GO-Slim functional classes. Certain functional classes, such as &#x0201c;translation&#x0201d;, are overrepresented for both MetaCLADE and HMMer, as expected. Others are characteristic of certain environmental conditions, and they are only detected by MetaCLADE. The numerical comparison involving all Metagenomic GO-Slim functional classes (partly visualised by the heat-map in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">a</xref>) is reported in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref> (see the &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section for the normalisation procedure).
<fig id="Fig2"><label>Fig. 2</label><caption><p>Functional analysis of MT data collected on five ocean sites. <bold>a</bold> Comparative table of domain abundance classification. Rows represent the Metagenomic GO-Slim functional classes that are the most represented, in at least one of the ocean samples. The level of domain abundance (entries of the table; normalised domain abundance, <inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$N^{S}_{I}$\end{document}</tex-math><mml:math id="M2"><mml:msubsup><mml:mrow><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mi>I</mml:mi></mml:mrow><mml:mrow><mml:mi>S</mml:mi></mml:mrow></mml:msubsup></mml:math><inline-graphic xlink:href="40168_2018_532_Article_IEq1.gif"/></alternatives></inline-formula> defined in the &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section, is rescaled here in the interval [0,1]) for Antarctic (ANT), North Pacific (NPAC), Equatorial Pacific (EPAC), Arctic (ARC) and North Atlantic (NATL) samples that are described. Samples are arranged in five columns reporting, for each functional class, the level of abundance obtained with MetaCLADE and HMMer. The colour bar ranks high levels of abundance in dark red and low levels in blue. The ranking of Metagenomic GO-Slim functional classes (from top to bottom in the table) is fixed by the average abundance of a domain in the five samples detected by MetaCLADE. Note that only the most abundant subset of GO-Slim classes is reported. <bold>b</bold> MetaCLADE analysis of domains belonging to the GO-term &#x0201c;ion transport&#x0201d; (GO.0006811). Results are displayed by GO-terms association (left) and by domain name (right). Column heights correspond to the estimation <inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$N^{S}_{I}$\end{document}</tex-math><mml:math id="M4"><mml:msubsup><mml:mrow><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mi>I</mml:mi></mml:mrow><mml:mrow><mml:mi>S</mml:mi></mml:mrow></mml:msubsup></mml:math><inline-graphic xlink:href="40168_2018_532_Article_IEq2.gif"/></alternatives></inline-formula> of domain abundance relative to each sample <italic>S</italic> and functional class <italic>I</italic> (see the &#x0201c;<xref rid="Sec27" ref-type="sec">Domain abundance</xref>&#x0201d; section in the &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section). For each environmental sample, the abundance of the first five most represented domains in the sample is plotted (note that each column has five colours.) <bold>c</bold> Hierarchical tree graph of GO-terms for &#x0201c;ion transport&#x0201d; obtained with MetaCLADE and described in <bold>b</bold> for the ANT sample. The count of domains classified with a given GO-term in ANT is represented by the colour of the associated box. The colour scale represents the number of domains identified for a GO-term. Red corresponds to &#x0003e;150 domain hits. Each box in the tree graph is coloured independently of its position in the tree graph because each domain is associated to a single box. There is no cumulative effect in the counting. <bold>d</bold> Hierarchical tree graph of GO-terms for &#x0201c;ion transport&#x0201d; obtained with HMMer. The GO-terms are associated to domains identified by HMMer in the ANT sample, compared to <bold>c</bold>. <bold>e</bold> Distribution of species originating CCMs used to annotate the five MT datasets. Due to the different number of reads in the datasets (and hence, the variable number of identified domains), we report the proportion of species, organised in clades, for each dataset</p></caption><graphic xlink:href="40168_2018_532_Fig2_HTML" id="MO2"/></fig>
</p><p>A striking example is the &#x0201c;ion transport&#x0201d; functional class for the EPAC and ANT samples, where HMMer annotation completely misses the large presence of bacteriorhodopsin-like domains in EPAC, as illustrated in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">a</xref>, <xref rid="Fig2" ref-type="fig">b</xref>. In other environments, such as the ANT sample, there is a much weaker presence of these domains but their existence is nevertheless captured. In particular, MetaCLADE annotation is much finer than HMMer annotation as seen in the complexity of the tree graphs associated to the &#x0201c;ion transport&#x0201d; GO-term in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">c</xref>, <xref rid="Fig2" ref-type="fig">d</xref>. Note the red colour, representing the highest abundance, given to the node &#x0201c;ion transport&#x0201d; in the MetaCLADE tree graph of Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">c</xref> compared to the yellow colour, corresponding to a weaker abundance, in the HMMer tree graph of Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">d</xref>. MetaCLADE tree graph is much more detailed and precise in the annotation of domains: it contains six nodes (corresponding to distinct GO-terms) more than the HMMer tree graph. The set of domains for the GO-term &#x0201c;metal ion transport&#x0201d;, for instance, is represented by just one node of 44 identified domains in the HMMer tree graph (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">d</xref>), while it is detailed by a more complex MetaCLADE tree graph of 165 identified domains, associated to iron ion, nickel cation, cobalt ion and ferrous iron transport GO-terms (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">c</xref>). This association to specific functional roles of the identified domains can help biologists to better characterise the metabolic regimes of the sample. Overall, MetaCLADE uniformly annotates more domains and with a more specific functional association than HMMer. The same tree graph analysis was realised for all Metagenomic GO-Slim functional classes, and the functional variability, through which annotated domains span within each tree graph, was estimated by counting the number of nodes (corresponding to distinct GO-terms) in the MetaCLADE and HMMer tree graphs. The values of the analysis are reported in Additional file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>, and they confirm, at large scale, that MetaCLADE annotation provides a more refined functional description than HMMer.</p><p>In Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">a</xref>, some functional classes appear as the most represented in exactly one environmental sample. This is the case for the pyrophosphates in NATL, the transferrin and the ammonium transporter in NPAC. Others are shared by several samples. They might be present in the remaining samples as well, but relatively less represented (as for the bacteriorhodopsin-like domains in EPAC and ANT discussed above, for instance, illustrated in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">b</xref>). This comparative information is crucial for zooming in the functional activity of an environment.</p><p>Finally, one should notice the distribution of species providing the homologous sequences generating CCMs used by MetaCLADE to annotate domains in the five oceanic samples (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">e</xref>). These eukaryotic read sequences were mostly annotated by &#x0201c;eukaryotic&#x0201d; CCMs generated from Metazoa and Alveolata domain sequences. A large contribution from other organisms, such as Bacteria, is also present as expected. We notice a large presence of annotations from &#x0201c;bacterial&#x0201d; CCMs for EPAC. These annotations mostly concern three domains (bacteriorhodopsin-like, <italic>S</italic>-adenosyl-<sc>L</sc>-homocysteine hydrolase and cyclosome subunit 3&#x02014;Apc3 domains) covering together the 12% of all EPAC CCM annotations and the 38% of all &#x0201c;bacterial&#x0201d; CCM annotations for EPAC. These are the domains whose &#x0201c;bacterial&#x0201d; CCMs cover alone more than the 1% of all CCM annotations; note that the bacteriorhodopsin-like domain alone covers more than 25% of &#x0201c;bacterial&#x0201d; CCM annotations for EPAC. The list of domains that have been annotated by MetaCLADE with bacterial/eukaryotic/archaea/viral CCMs is given in Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>. See also Additional file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>.</p></sec><sec id="Sec5"><title>Identification of divergent domains by conserved small motifs</title><p>MetaCLADE multi-source annotation strategy is used with the purpose of identifying very divergent domain sequences lying in reads. In fact, CCMs are probabilistic models that describe closely specific sequences and they can capture conserved patterns that are specific of homologs niches and that are missed by SCMs. As a consequence, CCMs for a domain have the possibility to describe domain sequences in greater detail and span a greater space of homologous sequences, possibly very divergent. For instance, in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref><xref rid="Fig3" ref-type="fig">a</xref>, we consider the conservation profile of the sequence alignment associated to a CCM used in the annotation of the rhodopsin-like domain in MG fragments, missed by HMMer as discussed above, but whose expression is expected in the Equatorial Pacific [<xref ref-type="bibr" rid="CR58">58</xref>, <xref ref-type="bibr" rid="CR59">59</xref>]. With this and other CCMs, MetaCLADE could annotate 371 sequences in EPAC that could not be detected by HMMer in [<xref ref-type="bibr" rid="CR57">57</xref>] due to the strong sequence divergence (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref><xref rid="Fig3" ref-type="fig">d</xref>). The conservation profile of the alignment of the 371 environmental sequences is reported in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref><xref rid="Fig3" ref-type="fig">b</xref>. It is very conserved and corresponds to a portion of the rhodopsin-like domain. This conserved pattern makes the third of the length of the entire domain. The rest of the sequence is divergent and remains with no annotation. One can visually appreciate the stronger similarity of the CCM profile (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref><xref rid="Fig3" ref-type="fig">a</xref>) to the MG sequences (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref><xref rid="Fig3" ref-type="fig">b</xref>) compared to the Pfam SCM profile (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref><xref rid="Fig3" ref-type="fig">d</xref>) of the bacterial-like rhodopsin. Indeed, 48 positions of the CCM profile versus 25 of the Pfam SCM profile match the alignment of the MG sequences (that is, given a position, the most represented amino acid in the MG profile is one of the first three best represented amino acids in the CCM/SCM profile at that position).
<fig id="Fig3"><label>Fig. 3</label><caption><p>Conserved motif in bacterial rhodopsin sequences annotated by MetaCLADE in the MT dataset EPAC. <bold>a</bold> Conservation profile of the MetaCLADE CCM fragment generated by the <italic>Geodermatophilus obscurus</italic> (strain ATCC 25078/DSM 43160/JCM 3152/G-20; Actinobacteria) sequence of the rhodopsin-like domain used by MetaCLADE to annotate environmental sequences in EPAC [<xref ref-type="bibr" rid="CR57">57</xref>]. An orange dot is located above all positions in the profile when one of the three top residues with the highest frequency appears as highest frequency residue in the corresponding position of the conservation profile in <bold>b</bold>. <bold>b</bold> Profile generated from the alignment of 371 environmental sequences annotated by MetaCLADE with CCMs of the rhodopsin-like domain and missed by HHMer. The letter height in the logo is proportional to the number of sequences in the alignment that contain the letter at a specific position, and the letter thickness is proportional to the number of gaps in the alignment at that position. <bold>c</bold> Rhodopsin fragment sequence from the dinoflagellate <italic>Prorocentrum donghaiense</italic> found in the NR database and matching, with <italic>E</italic> value 3e &#x02212;22 and sequence identity 78%, the longest environmental sequence among the 371 annotated by MetaCLADE. Note that the fragment has been aligned to the profile in <bold>b</bold> for a visual inspection of conserved positions. <bold>d</bold> Conservation profile HMM of the Pfam bacterial rhodopsin domain PF01036 (fragment). As in <bold>c</bold>, positions are aligned with the profile in <bold>b</bold> for best visualisation. An orange dot is located above a position in the profile when one of the three top residues with the highest frequency appears as highest frequency residue in the corresponding position of the conservation profile in <bold>b</bold></p></caption><graphic xlink:href="40168_2018_532_Fig3_HTML" id="MO3"/></fig>
</p><p>Note that the motif identified by MetaCLADE in the eukaryotic MG sample was recently identified in the dinoflagellate <italic>Prorocentrum donghaiense</italic> [<xref ref-type="bibr" rid="CR60">60</xref>] (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref><xref rid="Fig3" ref-type="fig">c</xref>) with an alignment comprised by homologs from <italic>Oxyrrhis marina</italic> and bacteria. The conserved positions, characteristic of the dinoflagellate sequence [<xref ref-type="bibr" rid="CR60">60</xref>], are recovered in the alignment of our MG sequences, confirming MetaCLADE functional annotation.</p><p>MetaCLADE demonstrated that its algorithmic strategy allows for the identification of conserved small motifs in MG samples and opens up the possibility of a systematic characterisation of environmental motifs.</p></sec><sec id="Sec6"><title>Improved annotation of MG/MT datasets compared to InterProScan</title><p>We ran MetaCLADE and InterProScan [<xref ref-type="bibr" rid="CR61">61</xref>] with five different libraries (Pfam [<xref ref-type="bibr" rid="CR55">55</xref>, <xref ref-type="bibr" rid="CR61">61</xref>, <xref ref-type="bibr" rid="CR62">62</xref>], Gene3D [<xref ref-type="bibr" rid="CR63">63</xref>], TIGRFAM [<xref ref-type="bibr" rid="CR64">64</xref>], PRINTS [<xref ref-type="bibr" rid="CR65">65</xref>] and ProSite [<xref ref-type="bibr" rid="CR66">66</xref>]) on five publicly available MG and MT datasets (listed in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>; see Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>). For these datasets, the sequenced reads had been pre-processed with the EBI Metagenomics pipeline [<xref ref-type="bibr" rid="CR67">67</xref>] leading on which the annotation has been realised.
<table-wrap id="Tab1"><label>Table 1</label><caption><p># of ORFs annotated by five different domain annotation tools</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">MG/MT datasets</th><th align="left"># Predicted ORFs</th><th align="left">Gene3d</th><th align="left">Pfam</th><th align="left">TIGRFAM</th><th align="left">PRINTS&#x00026;Prosite</th><th align="left">MetaCLADE</th><th align="left">MetaCLADE +UProC</th></tr></thead><tbody><tr><td align="left" colspan="8"># of ORFs annotated by each tool</td></tr><tr><td align="left">&#x02003;Puerto Rico Rainforest soil metagenome</td><td align="left">520 791</td><td align="left">189 138</td><td align="left">181 777</td><td align="left">15 155</td><td align="left">27 710</td><td align="left">
<bold>260 953</bold>
</td><td align="left">
<bold>266 380</bold>
</td></tr><tr><td align="left">&#x02003;Arctic Winter marine ecosystem</td><td align="left">358 095</td><td align="left">113 307</td><td align="left">104 098</td><td align="left">6 835</td><td align="left">15 324</td><td align="left">
<bold>150 786</bold>
</td><td align="left">
<bold>158 496</bold>
</td></tr><tr><td align="left">&#x02003;Bone sample from Vindija Neanderthal</td><td align="left">74 705</td><td align="left">12 793</td><td align="left">9 717</td><td align="left">612</td><td align="left">933</td><td align="left">
<bold>18 589</bold>
</td><td align="left">
<bold>22 673</bold>
</td></tr><tr><td align="left">&#x02003;Human gut metagenome</td><td align="left">45 770</td><td align="left">17 873</td><td align="left">18 981</td><td align="left">905</td><td align="left">2 002</td><td align="left">
<bold>27 028</bold>
</td><td align="left">
<bold>29 645</bold>
</td></tr><tr><td align="left">&#x02003;Human gut metatranscriptome</td><td align="left">37 209</td><td align="left">10 297</td><td align="left">9 699</td><td align="left">183</td><td align="left">714</td><td align="left">
<bold>16 522</bold>
</td><td align="left">
<bold>20 479</bold>
</td></tr><tr><td align="left" colspan="8"># of ORFs annotated by one specific tool</td></tr><tr><td align="left">&#x02003;Puerto Rico Rainforest soil metagenome</td><td align="left">520 791</td><td align="left">16 482</td><td align="left">1 090</td><td align="left">2 473</td><td align="left">138</td><td align="left">
<bold>37 811</bold>
</td><td align="left">
<bold>43 222</bold>
</td></tr><tr><td align="left">&#x02003;Arctic Winter marine ecosystem</td><td align="left">358 095</td><td align="left">11 564</td><td align="left">580</td><td align="left">1 703</td><td align="left">159</td><td align="left">
<bold>20 531</bold>
</td><td align="left">
<bold>28 227</bold>
</td></tr><tr><td align="left">&#x02003;Bone sample from Vindija Neanderthal</td><td align="left">74 705</td><td align="left">1 824</td><td align="left">50</td><td align="left">294</td><td align="left">14</td><td align="left">
<bold>4 663</bold>
</td><td align="left">
<bold>8 742</bold>
</td></tr><tr><td align="left">&#x02003;Human gut metagenome</td><td align="left">45 770</td><td align="left">1 368</td><td align="left">55</td><td align="left">27</td><td align="left">7</td><td align="left">
<bold>4 477</bold>
</td><td align="left">
<bold>7 093</bold>
</td></tr><tr><td align="left">&#x02003;Human gut metatranscriptome</td><td align="left">37 209</td><td align="left">1 172</td><td align="left">20</td><td align="left">4</td><td align="left">7</td><td align="left">
<bold>3 983</bold>
</td><td align="left">
<bold>7 938</bold>
</td></tr></tbody></table><table-wrap-foot><p>Number of ORFs (# of ORFs) predicted in reads with FragGeneScan and annotated by different tools. Largest annotations are reported in bold. See Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> and Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figures S7-S10, S12-S15 for additional information</p></table-wrap-foot></table-wrap>
</p><p>These datasets differ in number of reads and difficulty (measured by the number of annotated domains that could be identified). The performances of InterProScan on the five domain libraries differ greatly (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>). The first observation comes from the number of annotated domains shared by the five libraries, which is very reduced, indicating the complementarity of their domain models (see Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref><xref rid="Fig4" ref-type="fig">a</xref> for the Puerto Rico Rainforest dataset and Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figures S7A-S10A for the other four datasets). One observes that Pfam and Gene3D annotate the largest number of domains, together with MetaCLADE that largely agrees with them. In Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref><xref rid="Fig4" ref-type="fig">a</xref>, for instance, over 260,000 domains annotated by MetaCLADE, only 38,811 are identified exclusively by MetaCLADE, while all others are found by at least another tool. TIGRFAM and PRINTS&#x00026;ProSite (the union was considered) annotate the least, and their annotation is largely covered by other libraries. In particular, notice that MetaCLADE annotates a large number of reads that are missed by InterProScan for all five datasets. Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> reports the number of ORFs annotated by each library (top) and those exclusively annotated by a single library (bottom). MetaCLADE shows a high number of uniquely annotated ORFs, and, in this respect, it clearly demonstrates to go far beyond InterProScan based on different domain libraries. Moreover, on all datasets, the distribution of <italic>E</italic> values associated to MetaCLADE annotation shows a higher statistical confidence compared to InterProScan with all its libraries. This is illustrated by the density curves in Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref><xref rid="Fig4" ref-type="fig">c</xref> and Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figures S7C-S10C where the peak of the MetaCLADE curve lies at the rightmost side compared to all other domain annotation tools. In particular, this is true for the Gene3D library, whose peak corresponds to the acceptance threshold for this tool. This allows to explain why MetaCLADE seems to perform poorly on the domains exclusively annotated by Gene3D. In fact, Gene3D uses the <italic>E</italic> value threshold 1e &#x02212;4 which is for most domains too permissive. Domains were exclusively identified by Gene3D with an average <italic>E</italic> value of 1e &#x02212;7, while domains exclusively identified by MetaCLADE have an average <italic>E</italic> value of 1e &#x02212;12.
<fig id="Fig4"><label>Fig. 4</label><caption><p>Read annotations of the Puerto Rico Rainforest MG dataset obtained with InterProScan and MetaCLADE. <bold>a</bold> Domain annotation of the five tools: Pfam (yellow), Gene3D (blue), TIGRFAM (purple), PRINTS&#x00026;ProSite (orange) and MetaCLADE (green). The Venn diagram representing the number of reads annotated by one or several tools is reported. <bold>b</bold> Distribution of species originating CCMs used to annotate the dataset with MetaCLADE. <bold>c</bold> Distributions of <italic>E</italic> values associated to the sets of domains identified in an exclusive manner by each tool. For instance, for MetaCLADE, we considered 37,811 domains (see <bold>a</bold>). <italic>E</italic> values are plotted on the <italic>x</italic>-axis using a &#x02212; log10 scale. <bold>d</bold> Distribution of <italic>E</italic> values associated to all domains identified by MetaCLADE. As in <bold>c</bold>, <italic>E</italic> values are plotted on the <italic>x</italic>-axis using a &#x02212; log10 scale</p></caption><graphic xlink:href="40168_2018_532_Fig4_HTML" id="MO4"/></fig>
</p><p>We note that all the 2473 domains annotated exclusively by TIGRFAM correspond to signatures/domains that are unknown to Pfam (version 27, for which CCMs have been generated) and therefore to MetaCLADE.</p></sec><sec id="Sec7"><title>Identification of motifs in short reads: the example of ABC transporters</title><p>MetaCLADE is also suitable for technologies producing very short reads like Illumina HiSeq 2000 sequencing system. We analysed the predicted ORFs left without annotation by InterPro from one run of the O&#x02019;Connor lake dataset. The dataset contains 1,315,435 input reads and 1,211,131 predicted ORFs, with an upper bound on the missed ORFs of 104,304. InterProScan annotated 273,903 ORFs, leaving unannotated 937,228 ORFs. The two sets of predicted and unannotated ORFs have a mean ORF length of 123 bp, with a minimum of 100 bp and a maximum of 135 bp. MetaCLADE analysed the 937,228 unannotated ORFs and succeeded to annotate 57,356 of them. The distribution of <italic>E</italic> values for MetaCLADE annotations is shown in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S11. The list of the most abundant identified domains is given in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S6. The domain ranked first is the ABC transporter type 1.</p><p>The presence in the MG dataset of annotated sequences presenting some sequence similarity to the known ABC transporters is an indicator of potential metabolic activities that we wish to discover. To support confidence on this identified group of sequences, we scanned them to see if we could find motifs that are known to characterise the ABC transporter domain (<ext-link ext-link-type="uri" xlink:href="https://www.ebi.ac.uk/interpro/entry/IPR000515">https://www.ebi.ac.uk/interpro/entry/IPR000515</ext-link>). For this, we considered all 1109 environmental sequences annotated as ABC transporter type 1 by MetaCLADE (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S6) and selected the ones with an <italic>E</italic> value smaller than 1e &#x02212;4. There are 945 sequences with an average length of 36.9 residues. We ran MEME [<xref ref-type="bibr" rid="CR68">68</xref>] on them to find the 10 most significant motifs. Among these motifs, we identified the EAA motif, a 20 amino acid-conserved sequence known to occur in ABC transporter type 1 (<ext-link ext-link-type="uri" xlink:href="https://www.ebi.ac.uk/interpro/entry/IPR000515">https://www.ebi.ac.uk/interpro/entry/IPR000515</ext-link>) (See motif 9 in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S7; strictly speaking, we found a portion of the known EAA motif, where the submotif EAA &#x02212;&#x02212;&#x02212;&#x02212;G occurs [<xref ref-type="bibr" rid="CR69">69</xref>].). The consensus sequences of the 10 motifs (for example, FNLLGDGLRDALDPR for motif 1 and GAILTEAALSFLGLG for motif 9 in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S7) were used as query to search the NR database. For all consensus sequences, most of the hits found (&#x0003e;&#x02009;95%) matched ABC transporters. In rare cases, BLAST [<xref ref-type="bibr" rid="CR70">70</xref>] retrieved, in addition to the great majority of ABC transporters, other transport systems permeases or hypothetical proteins.</p><p>The presence of known motifs favourably supports the finding, and MetaCLADE proves to be able to extract useful functional information even from very short reads. In this respect, Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S6 shows that MetaCLADE annotations can substantially change the estimations of domain abundance in MG samples compared to estimations realised with HMMer. This confirms what was already observed for the ocean MT datasets, where MetaCLADE allowed for a more precise functional comparison.</p></sec><sec id="Sec8"><title>Sensitivity of MetaCLADE on the distribution of species generating the models</title><p>To analyse MetaCLADE&#x02019;s sensitivity on the distribution of species from which models have been generated compared to species where reads come from, we verified the distribution of species generating models used for the annotation of nine simulated datasets of reads. These datasets contain short fragments coming from species belonging to bacteria, viruses, archaea and eukaryotes. Specifically, they are constructed by gradually incrementing (by 10%) the number of eukaryotic sequences in them (see the &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section). The nine resulting datasets have been annotated with MetaCLADE, and the origin of the CCMs used is reported in Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>. As expected, in the annotation process, MetaCLADE tends to use models close to the communities represented in the dataset; namely, the number of models generated from eukaryotic sequences used for annotation is proportional to the quantity of eukaryotic reads in the datasets. This observation holds true for real datasets as illustrated in Figs.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref><xref rid="Fig2" ref-type="fig">e</xref> and <xref rid="Fig4" ref-type="fig">4</xref><xref rid="Fig4" ref-type="fig">b</xref> (see also Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figures S7B-S10B).
<fig id="Fig5"><label>Fig. 5</label><caption><p>Distributions of species whose sequences generated models for MetaCLADE annotation. Analysis of nine simulated datasets named &#x0201c;Euk<italic>x</italic>&#x0201d; containing <italic>x</italic>% of reads coming from eukaryotic sequences. For increasing values of <italic>x</italic>, one observes a proportionally higher number of CCMs coming from eukaryotes (red) that have been used for the annotation of the dataset. The proportion of bacteria (violet), archaea (yellow) and viruses (green) is reported for each dataset</p></caption><graphic xlink:href="40168_2018_532_Fig5_HTML" id="MO5"/></fig>
</p></sec><sec id="Sec9"><title>Comparison with UProC</title><p>UProC is a very fast protein classification tool designed for large-scale sequence analysis. It is much faster than profile-based methods, like MetaCLADE, and in MG datasets was demonstrated to achieve high sensitivity [<xref ref-type="bibr" rid="CR38">38</xref>]. We tested whether MetaCLADE, on real datasets, is identifying more domains than UProC or not. The answer depends on the average length of the reads in the dataset. We took the five environmental samples considered in the section &#x0201c;Improved annotation of MG/MT datasets compared to InterProScan&#x0201d; (see Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S4), ran UProC on them and compared to MetaCLADE results. For the Rainforest MG dataset, the analysis is reported in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref><xref rid="Fig6" ref-type="fig">a</xref>. As illustrated in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref><xref rid="Fig6" ref-type="fig">b</xref>, the 5439 reads that are uniquely annotated by UProC are either of very small size, &#x0003c;50 aa, or much larger, &#x0003e;150 aa. In contrast, the 22,059 MetaCLADE exclusive annotations do not concern very small reads but rather reads with larger size &#x0003e;50 aa, and particularly &#x0003e;150 aa. The <italic>E</italic> value density distribution curve of UProC annotations (see Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref><xref rid="Fig6" ref-type="fig">c</xref> and &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section) highlights reasonably low <italic>E</italic> values showing a high confidence in most UProC domain annotations. The second best curve is MetaCLADE&#x02019;s curve, placed on its left, followed by the InterProScan curves.
<fig id="Fig6"><label>Fig. 6</label><caption><p>Read annotations of the Puerto Rico Rainforest MG dataset obtained with InterProScan, UProC and MetaCLADE. <bold>a</bold> Domain annotation of five tools: Pfam (yellow), Gene3D (blue), TIGRFAM (purple), UProC (orange) and MetaCLADE (green). The Venn diagram representing the number of reads annotated by one or several tools is reported. <bold>b</bold> Length distribution of reads annotated exclusively by either UProC or MetaCLADE. <bold>c</bold> Distributions of <italic>E</italic> values associated to the sets of domains identified in an exclusive manner by each tool. For instance, for MetaCLADE, we considered 22,059 domains (see <bold>a</bold>). <italic>E</italic> values are plotted on the x-axis using a &#x02212; log10 scale. <bold>d</bold> Distribution of probabilities associated to those exclusive UProC domain annotations that have been detected by MetaCLADE but discarded because of the probability threshold 0.9. <bold>e</bold> Venn diagram as in <bold>a</bold> of Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>, but where MetaClade is replaced by MetaCLADE+UProC. <bold>f</bold> As in <bold>c</bold>, but where MetaCLADE is replaced by MetaCLADE+UProC</p></caption><graphic xlink:href="40168_2018_532_Fig6_HTML" id="MO6"/></fig>
</p><p>A quite large number of reads exclusively predicted by UProC is also predicted by MetaCLADE but not selected by MetaCLADE because of its probability threshold set at 0.9. More precisely, they cover less than 50% of the UProC exclusively predicted reads. By looking at the confidence of these domain predictions, a large number of these predicted reads have very low probability (see Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref><xref rid="Fig6" ref-type="fig">d</xref>). This observation suggests that, for these domains, the CLADE&#x02019;s library does not cover properly the spread of evolutionary variability of the domains.</p><p>In Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figures S12-S15, we report the analysis of the other four MG and MT datasets. All these datasets confirm that most uniquely annotated UProC reads are very short (&#x0003c;50 aa) compared to reads uniquely annotated by MetaCLADE (see Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figures S12B-S15B). The density curves of the UProC&#x02019;s associated <italic>E</italic> values occupy the right-hand side of the plots (see Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figures S12C-S15C) with a peak that indicates an optimal average <italic>E</italic> value for UProC reads. Moreover, from the distribution of domain lengths for uniquely annotated reads in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S16, we observe that, depending on the dataset, for a given domain, the read multiplicity can be much larger for MetaCLADE than for UProC. The reason is anchored on the way UProC and MetaCLADE handle sequence information. Indeed, if the space of sequences in the FULL Pfam dataset associated to a domain spans well over diversified homologous sequences, then UProC, which exploits all FULL Pfam sequences, can produce highly confident predictions. If the number of MetaCLADE models is small compared to the number of diversified FULL Pfam sequences, it might represent well only a part of the diversification and fail in predicting homology on the unrepresented pool of sequences. In contrast, if the FULL Pfam dataset does not span well over the entire set of homologous sequences, then MetaCLADE might be able to reach those diverged sequences that cannot be reached by UProC by exploiting its probabilistic models.</p></sec><sec id="Sec10"><title>A first comparison with HMM-GRASPx using an assembly-based approach</title><p>Assembly algorithms can be used prior to functional annotation of a given MG dataset. We evaluated MetaCLADE&#x02019;s performance against HMM-GRASPx [<xref ref-type="bibr" rid="CR37">37</xref>], a state-of-the-art assembly-based annotation method. HMM-GRASPx is characterised by a profile-guided assembly phase in which assembled protein contigs are verified through a HMMER realignment of Pfam profiles. Hence, a domain family is assigned to the reads by mapping them back to verified contigs. In order to provide a fair comparison of the two methods, we decided to annotate the assembly generated by HMM-GRASPx with MetaCLADE and transfer this annotation on the reads by mapping them back to the assembly as done by HMM-GRASPx. Unmapped reads were also separately annotated with MetaCLADE. In parallel, we considered another assembly approach, based on the construction and annotation of a gene catalog [<xref ref-type="bibr" rid="CR71">71</xref>, <xref ref-type="bibr" rid="CR72">72</xref>], where input reads are assembled using a canonical assembly pipeline, ORFs are predicted and putative gene sequences are clustered in order to create a non-redundant set (i.e. the gene catalog). The latter was annotated with MetaCLADE. Reads were finally mapped back to the catalog and the annotation transferred accordingly. Unmapped reads were also separately annotated with MetaCLADE. We compared MetaCLADE based on both assembly approaches against HMM-GRASPx.</p><p>To assess annotation performances, the experiment was run on the dataset used in [<xref ref-type="bibr" rid="CR37">37</xref>] which was augmented in order to better reflect the size of modern metagenomic datasets. More precisely, a set of 20 million paired-end reads was generated from a simulated marine dataset with uneven coverage and read length of 100 bp. A total of 303 Pfam domain families that are involved in some important metabolic pathways were selected as input for HMM-GRASPx and MetaCLADE as in [<xref ref-type="bibr" rid="CR37">37</xref>] (details are reported in the &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section).</p><p>The results of our comparison are reported in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref> showing that MetaCLADE always outperforms HMM-GRASPx regardless of the assembly method considered. MetaCLADE, however, performed better considering the gene catalog as a reference. The same observation still holds when clans are evaluated, that is when domain hits of the same clan (with respect to the gold standard) are counted as true positives.
<table-wrap id="Tab2"><label>Table 2</label><caption><p>Comparison of HMM-GRASPx and MetaCLADE against a simulated 100-bp marine data set with uneven coverage</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Pathway</th><th align="left" colspan="6">HMM-GRASPx</th><th align="left" colspan="6">MetaCLADE/HMM-GRASPx-Assembly<sup>c</sup></th><th align="left" colspan="6">MetaCLADE/GC-Assembly<sup>d</sup></th></tr><tr><th align="left"/><th align="left">TP</th><th align="left">FP</th><th align="left">FN</th><th align="left">Recall</th><th align="left">Prec</th><th align="left">F-score</th><th align="left">TP</th><th align="left">FP</th><th align="left">FN</th><th align="left">Recall</th><th align="left">Prec</th><th align="left">F-score</th><th align="left">TP</th><th align="left">FP</th><th align="left">FN</th><th align="left">Recall</th><th align="left">Prec</th><th align="left">F-score</th></tr></thead><tbody><tr><td align="left" colspan="19">&#x0201c;Strict&#x0201d; domain annotation<sup>a</sup></td></tr><tr><td align="left">&#x02003;KO00010</td><td align="left">159 243</td><td align="left">2 216</td><td align="left">39 938</td><td align="left">79. 9</td><td align="left">98.6</td><td align="left">88.3</td><td align="left">171 168</td><td align="left">5 384</td><td align="left">24 845</td><td align="left">87.3</td><td align="left">97.0</td><td align="left">91.9</td><td align="left">179 972</td><td align="left">7 397</td><td align="left">14 028</td><td align="left">92.8</td><td align="left">96.1</td><td align="left">
<italic>94.4</italic>
</td></tr><tr><td align="left">&#x02003;KO00020</td><td align="left">182 706</td><td align="left">11 691</td><td align="left">74 704</td><td align="left">71.0</td><td align="left">94.0</td><td align="left">80.9</td><td align="left">201 808</td><td align="left">24 786</td><td align="left">42 507</td><td align="left">82.6</td><td align="left">89.1</td><td align="left">85.7</td><td align="left">223 671</td><td align="left">20 452</td><td align="left">24 978</td><td align="left">90.0</td><td align="left">91.6</td><td align="left">
<italic>90.8</italic>
</td></tr><tr><td align="left">&#x02003;KO00030</td><td align="left">101 449</td><td align="left">589</td><td align="left">17 761</td><td align="left">85.1</td><td align="left">99.4</td><td align="left">91.7</td><td align="left">107 065</td><td align="left">833</td><td align="left">11 901</td><td align="left">90.0</td><td align="left">99.2</td><td align="left">94.4</td><td align="left">113 446</td><td align="left">859</td><td align="left">5 494</td><td align="left">95.4</td><td align="left">99.2</td><td align="left">
<italic>97.3</italic>
</td></tr><tr><td align="left">&#x02003;KO00051</td><td align="left">436 432</td><td align="left">17 970</td><td align="left">192 918</td><td align="left">69.3</td><td align="left">96.0</td><td align="left">80.5</td><td align="left">482 994</td><td align="left">50 714</td><td align="left">113 612</td><td align="left">81.0</td><td align="left">90.5</td><td align="left">85.5</td><td align="left">553 728</td><td align="left">41 566</td><td align="left">52 026</td><td align="left">91.4</td><td align="left">93.0</td><td align="left">
<italic>92.2</italic>
</td></tr><tr><td align="left">&#x02003;KO00620</td><td align="left">237 743</td><td align="left">11 756</td><td align="left">100 903</td><td align="left">70.2</td><td align="left">95.3</td><td align="left">80.8</td><td align="left">263 011</td><td align="left">27 306</td><td align="left">60 085</td><td align="left">81.4</td><td align="left">90.6</td><td align="left">85.8</td><td align="left">297 162</td><td align="left">21 038</td><td align="left">32 202</td><td align="left">90.2</td><td align="left">93.4</td><td align="left">
<italic>91.8</italic>
</td></tr><tr><td align="left">&#x02003;KO00680</td><td align="left">595 304</td><td align="left">24 101</td><td align="left">229 802</td><td align="left">72.1</td><td align="left">96.1</td><td align="left">82.4</td><td align="left">633 158</td><td align="left">74 709</td><td align="left">141 340</td><td align="left">81.8</td><td align="left">89.4</td><td align="left">85.4</td><td align="left">695 625</td><td align="left">70 569</td><td align="left">83 013</td><td align="left">89.3</td><td align="left">90.8</td><td align="left">
<italic>90.1</italic>
</td></tr><tr><td align="left">&#x02003;KO00910</td><td align="left">219 501</td><td align="left">16 171</td><td align="left">86 939</td><td align="left">71.6</td><td align="left">93.1</td><td align="left">81.0</td><td align="left">238 683</td><td align="left">30 435</td><td align="left">53 493</td><td align="left">81.7</td><td align="left">88.7</td><td align="left">85.0</td><td align="left">264 438</td><td align="left">25 270</td><td align="left">32 903</td><td align="left">88.9</td><td align="left">91.3</td><td align="left">
<italic>90.1</italic>
</td></tr><tr><td align="left">&#x02003;KO00920</td><td align="left">74 851</td><td align="left">138</td><td align="left">37 662</td><td align="left">66.5</td><td align="left">99.8</td><td align="left">79.8</td><td align="left">82 977</td><td align="left">1 098</td><td align="left">28 576</td><td align="left">74.4</td><td align="left">98.7</td><td align="left">84.8</td><td align="left">105 794</td><td align="left">603</td><td align="left">6 254</td><td align="left">94.4</td><td align="left">99.4</td><td align="left">
<italic>96.9</italic>
</td></tr><tr><td align="left" colspan="19">&#x0201c;Clan-based&#x0201d; domain annotation<sup>b</sup></td></tr><tr><td align="left">&#x02003;KO00010</td><td align="left">161 459</td><td align="left">0</td><td align="left">39 938</td><td align="left">80.2</td><td align="left">100.0</td><td align="left">89.0</td><td align="left">176 369</td><td align="left">183</td><td align="left">24 845</td><td align="left">87.7</td><td align="left">99.9</td><td align="left">93.4</td><td align="left">187 308</td><td align="left">61</td><td align="left">14 028</td><td align="left">93.0</td><td align="left">100.0</td><td align="left">
<italic>96.4</italic>
</td></tr><tr><td align="left">&#x02003;KO00020</td><td align="left">194 397</td><td align="left">0</td><td align="left">74 704</td><td align="left">72.2</td><td align="left">100.0</td><td align="left">83.9</td><td align="left">226 585</td><td align="left">9</td><td align="left">42 507</td><td align="left">84.2</td><td align="left">100.0</td><td align="left">91.4</td><td align="left">244 114</td><td align="left">9</td><td align="left">24 978</td><td align="left">90.7</td><td align="left">100.0</td><td align="left">
<italic>95.1</italic>
</td></tr><tr><td align="left">&#x02003;KO00030</td><td align="left">102 038</td><td align="left">0</td><td align="left">17 761</td><td align="left">85.2</td><td align="left">100.0</td><td align="left">92.0</td><td align="left">107 867</td><td align="left">31</td><td align="left">11 901</td><td align="left">90.1</td><td align="left">100.0</td><td align="left">94.8</td><td align="left">114 275</td><td align="left">30</td><td align="left">5 494</td><td align="left">95.4</td><td align="left">100.0</td><td align="left">
<italic>97.6</italic>
</td></tr><tr><td align="left">&#x02003;KO00051</td><td align="left">454 402</td><td align="left">0</td><td align="left">192 918</td><td align="left">70.2</td><td align="left">100.0</td><td align="left">82.5</td><td align="left">533 583</td><td align="left">125</td><td align="left">113 612</td><td align="left">82.4</td><td align="left">100.0</td><td align="left">90.4</td><td align="left">594 999</td><td align="left">295</td><td align="left">52 026</td><td align="left">92.0</td><td align="left">100.0</td><td align="left">
<italic>95.8</italic>
</td></tr><tr><td align="left">&#x02003;KO00620</td><td align="left">249 492</td><td align="left">7</td><td align="left">100 903</td><td align="left">71.2</td><td align="left">100.0</td><td align="left">83.2</td><td align="left">288 167</td><td align="left">2 150</td><td align="left">60 085</td><td align="left">82.7</td><td align="left">99.3</td><td align="left">90.3</td><td align="left">317 646</td><td align="left">554</td><td align="left">32 202</td><td align="left">90.8</td><td align="left">99.8</td><td align="left">
<italic>95.1</italic>
</td></tr><tr><td align="left">&#x02003;KO00680</td><td align="left">618 689</td><td align="left">716</td><td align="left">229 802</td><td align="left">72.9</td><td align="left">99.9</td><td align="left">84.3</td><td align="left">701 356</td><td align="left">6 511</td><td align="left">141 340</td><td align="left">83.2</td><td align="left">99.1</td><td align="left">90.5</td><td align="left">756 885</td><td align="left">9 309</td><td align="left">83 013</td><td align="left">90.1</td><td align="left">98.8</td><td align="left">
<italic>94.3</italic>
</td></tr><tr><td align="left">&#x02003;KO00910</td><td align="left">235 260</td><td align="left">412</td><td align="left">86 939</td><td align="left">73.0</td><td align="left">99.8</td><td align="left">84.3</td><td align="left">267 624</td><td align="left">1 494</td><td align="left">53 493</td><td align="left">83.3</td><td align="left">99.4</td><td align="left">90.7</td><td align="left">288 068</td><td align="left">1 640</td><td align="left">32 903</td><td align="left">89.7</td><td align="left">99.4</td><td align="left">
<italic>94.3</italic>
</td></tr><tr><td align="left">&#x02003;KO00920</td><td align="left">74 989</td><td align="left">0</td><td align="left">37 662</td><td align="left">66.6</td><td align="left">100.0</td><td align="left">79.9</td><td align="left">84 053</td><td align="left">22</td><td align="left">28 576</td><td align="left">74.6</td><td align="left">100.0</td><td align="left">85.5</td><td align="left">106 392</td><td align="left">5</td><td align="left">6 254</td><td align="left">94.4</td><td align="left">100.0</td><td align="left">
<italic>97.1</italic>
</td></tr></tbody></table><table-wrap-foot><p><sup>a</sup>Only hits having same Pfam domain with respect to the ground-truth are counted as true positives.</p><p><sup>b</sup>Domain hits that belong to the same clan with respect to the ground-truth are counted as true positives.</p><p><sup>c</sup>Annotation obtained by applying MetaCLADE on the assembled contigs of HMM-GRASPx.</p><p><sup>d</sup>Annotation obtained by applying MetaCLADE on the gene catalog</p><p>Largest values are reported in italics</p></table-wrap-foot></table-wrap>
</p><p>The same comparison had also been carried out on a simulated dataset of about 10 million 200-bp paired-end reads (keeping the same average coverage and composition of the 100-bp read dataset). Here again, MetaCLADE outperformed HMM-GRASPx (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S13), especially when considering HMM-GRASPx&#x02019;s assembly as a reference for MetaCLADE. As a matter of fact, the lower number of reads led to a more fragmented gene catalog and a lower performance of MetaCLADE on these assembled sequences. However, it was not possible to increase the read number (e.g. to 20 million) due to the excessive amount of computational resources (more than 128 GB of RAM) demanded by HMM-GRASPx. In fact, the application of HMM-GRASPx seems limited to datasets of modest size (in terms of both read and profile number).</p><p>Overall, even though MetaCLADE achieved very good performances in this assembly-based scenario, we should emphasise that it had been specifically tailored to work with relatively short fragments. Nevertheless, it can definitely benefit from read assembly (possibly in combination with CLADE [<xref ref-type="bibr" rid="CR43">43</xref>] for the treatment of long sequences). For this reason, we envisage the introduction of a preliminary assembly phase in a future implementation of our tool.</p></sec><sec id="Sec11"><title>Comparison with HMM-GRASPx and UProC on datasets of 100- and 200-bp reads</title><p>To investigate further MetaCLADE performance with very short reads, characterising the growing number of Illumina sequencing MG datasets available, we generated two sets of sequences, 100 bp (1,226,882 reads) and 200 bp (682,380 reads) long, from the Guerrero Negro Hypersaline Microbial Mats dataset (GNHM; see Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>). GNHM was used in [<xref ref-type="bibr" rid="CR38">38</xref>] to demonstrate UProC performance on short reads versus profile-based methods and, indeed, in [<xref ref-type="bibr" rid="CR42">42</xref>], it was shown that profile hidden Markov models substantially lose sensitivity on shorter reads. The two datasets have been evaluated by considering the annotations obtained either with Pfam (version 27) or with CLADE domain library (also based on Pfam27).</p><p>Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref> shows a slightly better performance of UProC compared to HMM-GRASPx and MetaCLADE on GNHM for 100-bp reads with a gold standard set by Pfam annotation. The behaviour becomes less sharp when the gold standard is CLADE, characterised by a larger number of domains. In particular, when clans are considered, MetaCLADE and UProC produce comparable F1-scores, of 57.7 and 58.9 respectively. As 200-bp reads are concerned, MetaCLADE outperforms UProC and HMM-GRASPx regardless of whether clans are considered or not. Notice the high F-scores reached by MetaCLADE, 84.6 and 81.7 versus 74 and 68.9 reached by UProC on the two gold standards when clans are considered (Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>). It is also interesting to notice that for the 200-bp reads, MetaCLADE identifies a much larger number of true positives and a smaller number of false negatives than UProC, for both gold standards and independently on whether clans are considered or not. In particular, when considering the gold standard set by CLADE and clans, MetaCLADE identifies 80,000 domains more than UProC, and a smaller number of false negatives and false positives.
<table-wrap id="Tab3"><label>Table 3</label><caption><p>Comparison of UProC, HMM-GRASPx and MetaCLADE on the Guerrero Negro Hypersaline microbial Mat project (100-bp reads)</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Tool</th><th align="left">TP</th><th align="left">FP</th><th align="left">FN</th><th align="left">Recall</th><th align="left">Precision</th><th align="left">F-score</th></tr></thead><tbody><tr><td align="left" colspan="7">Pfam gold standard</td></tr><tr><td align="left">&#x02003;UProC</td><td align="left">323 659</td><td align="left">31 539</td><td align="left">357 182</td><td align="left">47.5</td><td align="left">91.1</td><td align="left">
<italic>62.5</italic>
</td></tr><tr><td align="left">&#x02003;MetaCLADE</td><td align="left">291 896</td><td align="left">57 955</td><td align="left">388 945</td><td align="left">42.9</td><td align="left">83.4</td><td align="left">56.6</td></tr><tr><td align="left">&#x02003;HMM-GRASPx</td><td align="left">325 689</td><td align="left">41 155</td><td align="left">355 152</td><td align="left">47.8</td><td align="left">88.8</td><td align="left">62.2</td></tr><tr><td align="left">&#x02003;MetaCLADE+UProC</td><td align="left">370 274</td><td align="left">72 132</td><td align="left">310 567</td><td align="left">54.4</td><td align="left">83.7</td><td align="left">65.9</td></tr><tr><td align="left">&#x02003;UProC+MetaCLADE</td><td align="left">378 449</td><td align="left">70 063</td><td align="left">302 392</td><td align="left">55.6</td><td align="left">84.4</td><td align="left">
<italic>67.0</italic>
</td></tr><tr><td align="left">&#x02003;UProC <sub>clan</sub></td><td align="left">328 910</td><td align="left">26 288</td><td align="left">351 931</td><td align="left">48.3</td><td align="left">92.6</td><td align="left">63.5</td></tr><tr><td align="left">&#x02003;MetaCLADE <sub>clan</sub></td><td align="left">309 629</td><td align="left">40 222</td><td align="left">371 212</td><td align="left">45.5</td><td align="left">88.5</td><td align="left">60.1</td></tr><tr><td align="left">&#x02003;HMM-GRASPx <sub>clan</sub></td><td align="left">334 480</td><td align="left">32 364</td><td align="left">346 361</td><td align="left">49.1</td><td align="left">91.2</td><td align="left">
<italic>63.9</italic>
</td></tr><tr><td align="left">&#x02003;MetaCLADE+UProC <sub>clan</sub></td><td align="left">389 454</td><td align="left">52 952</td><td align="left">291 387</td><td align="left">57.2</td><td align="left">88.0</td><td align="left">
<italic>69.3</italic>
</td></tr><tr><td align="left">&#x02003;UProC+MetaCLADE <sub>clan</sub></td><td align="left">390 388</td><td align="left">58 124</td><td align="left">290 453</td><td align="left">57.3</td><td align="left">87.0</td><td align="left">69.1</td></tr><tr><td align="left" colspan="7">CLADE gold standard</td></tr><tr><td align="left">&#x02003;UProC</td><td align="left">308 483</td><td align="left">48 077</td><td align="left">476 068</td><td align="left">39.3</td><td align="left">86.5</td><td align="left">
<italic>54.1</italic>
</td></tr><tr><td align="left">&#x02003;MetaCLADE</td><td align="left">286 458</td><td align="left">48 696</td><td align="left">498 093</td><td align="left">36.5</td><td align="left">85.5</td><td align="left">51.2</td></tr><tr><td align="left">&#x02003;HMM-GRASPx</td><td align="left">296 742</td><td align="left">69 211</td><td align="left">487 809</td><td align="left">37.8</td><td align="left">81.1</td><td align="left">51.6</td></tr><tr><td align="left">&#x02003;MetaCLADE+UProC</td><td align="left">361 794</td><td align="left">64 085</td><td align="left">422 757</td><td align="left">46.1</td><td align="left">85.0</td><td align="left">59.8</td></tr><tr><td align="left">&#x02003;UProC+MetaCLADE</td><td align="left">366 221</td><td align="left">66 114</td><td align="left">418 330</td><td align="left">46.7</td><td align="left">84.7</td><td align="left">
<italic>60.2</italic>
</td></tr><tr><td align="left">&#x02003;UProC <sub>clan</sub></td><td align="left">336 302</td><td align="left">20 258</td><td align="left">448 249</td><td align="left">42.9</td><td align="left">94.3</td><td align="left">
<italic>58.9</italic>
</td></tr><tr><td align="left">&#x02003;MetaCLADE <sub>clan</sub></td><td align="left">323 009</td><td align="left">12 145</td><td align="left">461 542</td><td align="left">41.2</td><td align="left">96.4</td><td align="left">57.7</td></tr><tr><td align="left">&#x02003;HMM-GRASPx <sub>clan</sub></td><td align="left">328 729</td><td align="left">37 224</td><td align="left">455 822</td><td align="left">41.9</td><td align="left">89.8</td><td align="left">57.1</td></tr><tr><td align="left">&#x02003;MetaCLADE+UProC <sub>clan</sub></td><td align="left">405 734</td><td align="left">20 145</td><td align="left">378 817</td><td align="left">51.7</td><td align="left">95.3</td><td align="left">
<italic>67.0</italic>
</td></tr><tr><td align="left">&#x02003;UProC+MetaCLADE <sub>clan</sub></td><td align="left">406 370</td><td align="left">25 965</td><td align="left">378 181</td><td align="left">51.8</td><td align="left">94.0</td><td align="left">66.8</td></tr></tbody></table><table-wrap-foot><p>The first table considers Pfam27 annotations as gold standard, while the second one uses CLADE27. Each table is made of two sub-tables where we evaluate annotation on exact domains (top) and on clans (bottom). Annotations with domains of the same clan are counted as true positives</p><p>Largest values are reported in italics</p></table-wrap-foot></table-wrap><table-wrap id="Tab4"><label>Table 4</label><caption><p>Comparison of UProC, HMM-GRASPx and MetaCLADE on the Guerrero Negro Hypersaline microbial Mat project (200-bp reads)</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Tool</th><th align="left">TP</th><th align="left">FP</th><th align="left">FN</th><th align="left">Recall</th><th align="left">Precision</th><th align="left">F-score</th></tr></thead><tbody><tr><td align="left" colspan="7">Pfam gold standard</td></tr><tr><td align="justify">&#x02003;UProC</td><td align="left">252 245</td><td align="left">28 101</td><td align="left">161 239</td><td align="left">61.0</td><td align="left">90.0</td><td align="left">72.7</td></tr><tr><td align="justify">&#x02003;MetaCLADE</td><td align="left">307 711</td><td align="left">50 380</td><td align="left">105 773</td><td align="left">74.4</td><td align="left">85.9</td><td align="left">
<italic>79.8</italic>
</td></tr><tr><td align="justify">&#x02003;HMM-GRASPx</td><td align="left">288 152</td><td align="left">38 152</td><td align="left">125 332</td><td align="left">69.7</td><td align="left">88.3</td><td align="left">77.9</td></tr><tr><td align="justify">&#x02003;MetaCLADE+UProC</td><td align="left">319 829</td><td align="left">55 189</td><td align="left">93 655</td><td align="left">77.3</td><td align="left">85.3</td><td align="left">81.1</td></tr><tr><td align="justify">&#x02003;UProC+MetaCLADE</td><td align="left">326 133</td><td align="left">56 556</td><td align="left">87 351</td><td align="left">78.9</td><td align="left">85.2</td><td align="left">
<italic>81.9</italic>
</td></tr><tr><td align="justify">&#x02003;UProC <sub>clan</sub></td><td align="left">256 847</td><td align="left">23 499</td><td align="left">156 637</td><td align="left">62.1</td><td align="left">91.6</td><td align="left">74.0</td></tr><tr><td align="justify">&#x02003;MetaCLADE <sub>clan</sub></td><td align="left">326 217</td><td align="left">31 874</td><td align="left">87 267</td><td align="left">78.9</td><td align="left">91.1</td><td align="left">
<italic>84.6</italic>
</td></tr><tr><td align="justify">&#x02003;HMM-GRASPx <sub>clan</sub></td><td align="left">293 267</td><td align="left">33 037</td><td align="left">120 217</td><td align="left">70.9</td><td align="left">89.9</td><td align="left">79.3</td></tr><tr><td align="justify">&#x02003;MetaCLADE+UProC <sub>clan</sub></td><td align="left">338 609</td><td align="left">36 409</td><td align="left">74 875</td><td align="left">81.9</td><td align="left">90.3</td><td align="left">
<italic>85.9</italic>
</td></tr><tr><td align="justify">&#x02003;UProC+MetaCLADE <sub>clan</sub></td><td align="left">339 285</td><td align="left">43 404</td><td align="left">74 199</td><td align="left">82.1</td><td align="left">88.7</td><td align="left">85.2</td></tr><tr><td align="left" colspan="7">CLADE gold standard</td></tr><tr><td align="justify">&#x02003;UProC</td><td align="left">240 762</td><td align="left">43 085</td><td align="left">244 393</td><td align="left">49.6</td><td align="left">84.8</td><td align="left">62.6</td></tr><tr><td align="justify">&#x02003;MetaCLADE</td><td align="left">302 583</td><td align="left">63 491</td><td align="left">182 572</td><td align="left">62.4</td><td align="left">82.7</td><td align="left">
<italic>71.1</italic>
</td></tr><tr><td align="justify">&#x02003;HMM-GRASPx</td><td align="left">262 511</td><td align="left">64 833</td><td align="left">222 644</td><td align="left">54.1</td><td align="left">80.2</td><td align="left">64.6</td></tr><tr><td align="justify">&#x02003;MetaCLADE+UProC</td><td align="left">316 656</td><td align="left">68 490</td><td align="left">168 499</td><td align="left">65.3</td><td align="left">82.2</td><td align="left">72.8</td></tr><tr><td align="justify">&#x02003;UProC+MetaCLADE</td><td align="left">322 760</td><td align="left">70 325</td><td align="left">162 395</td><td align="left">66.5</td><td align="left">82.1</td><td align="left">
<italic>73.5</italic>
</td></tr><tr><td align="justify">&#x02003;UProC <sub>clan</sub></td><td align="left">264 787</td><td align="left">19 060</td><td align="left">220 368</td><td align="left">54.6</td><td align="left">93.3</td><td align="left">68.9</td></tr><tr><td align="justify">&#x02003;MetaCLADE <sub>clan</sub></td><td align="left">347 936</td><td align="left">18 138</td><td align="left">137 219</td><td align="left">71.7</td><td align="left">95.0</td><td align="left">
<italic>81.7</italic>
</td></tr><tr><td align="justify">&#x02003;HMM-GRASPx <sub>clan</sub></td><td align="left">290 155</td><td align="left">37 189</td><td align="left">195 000</td><td align="left">59.8</td><td align="left">88.6</td><td align="left">71.4</td></tr><tr><td align="justify">&#x02003;MetaCLADE+UProC <sub>clan</sub></td><td align="left">363 667</td><td align="left">21 479</td><td align="left">121 488</td><td align="left">75.0</td><td align="left">94.4</td><td align="left">
<italic>83.6</italic>
</td></tr><tr><td align="justify">&#x02003;UProC+MetaCLADE <sub><italic>clan</italic></sub></td><td align="left">364 641</td><td align="left">28 444</td><td align="left">120 514</td><td align="left">75.2</td><td align="left">92.8</td><td align="left">83.0</td></tr></tbody></table><table-wrap-foot><p>The first table considers Pfam27 annotations as gold standard, while the second one uses CLADE27. Each table is made of two sub-tables where we evaluate annotation on exact domains (top) and on clans (bottom). Annotations with domains of the same clan are counted as true positives</p><p>Largest values are reported in italics</p></table-wrap-foot></table-wrap>
</p><p>The two plots in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S17 display the relation between precision and recall, in order to evaluate the performance of the two tools on the two datasets, when the gold standard is CLADE and clans are considered. MetaCLADE displays a slightly better behaviour than UProC for fixed recall values. Notice that for very small recall values and, hence, very high hit scores, UProC detects a higher number of false positives (yet quite small) compared to MetaCLADE. This is seen with the behaviour of the curves in the zoomed plots in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S17, bottom. Precision-recall curves for UProC and MetaCLADE with Pfam-based gold standard and clan-based annotation are shown in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S18.</p></sec><sec id="Sec12"><title>A measure of the improvement obtained by combining MetaCLADE and UProC</title><p>By combining MetaCLADE and UProC, we obtain an improved quality of the functional assignments of sequences of GNHM on both the 100-bp and the 200-bp datasets. Indeed, for the two datasets, we considered the annotation realised by MetaCLADE augmented with UProC annotation on those reads left unannotated by MetaCLADE, and called this approach MetaCLADE+UProC (see the &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section). Vice versa, we considered the annotation realised by UProC augmented with MetaCLADE annotation on those reads left unannotated by UProC, and called this approach UProC+MetaCLADE (see the &#x0201c;<xref rid="Sec15" ref-type="sec">Methods</xref>&#x0201d; section). On both GNHM datasets, for the 100- and 200-bp reads, UProC+MetaCLADE outperformed on exact domain annotation and MetaCLADE+UProC on clan annotation. This is expected because UProC, based on word matching, is intuitively similar to very conserved CCMs that are very close to known sequences. Most importantly, the performance of their combination is bringing a clear improvement in terms of number of correctly predicted domains independently on the gold standard and on the dataset (Tables&#x000a0;<xref rid="Tab3" ref-type="table">3</xref> and <xref rid="Tab4" ref-type="table">4</xref>). In Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S17, the precision-recall curve for MetaCLADE+UProC shows that the addition of unique UProC annotations to MetaCLADE increases the number of false positives and therefore decreases precisions, as also seen in Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>. The advantage in using a combined approach like MetaCLADE+UProC relies on the increase of the recall (since false negatives decrease) counterbalanced by a small decrease in precision (since false positives slightly increase).</p><p>The combination MetaCLADE+UProC has been tested also on the Rainforest MG dataset, where it produced a much larger number of read predictions as illustrated in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref><xref rid="Fig6" ref-type="fig">e</xref>, accompanied by a distribution of <italic>E</italic> values showing high confidence (see Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref><xref rid="Fig6" ref-type="fig">f</xref> and compare it to Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref><xref rid="Fig4" ref-type="fig">a</xref>, <xref rid="Fig4" ref-type="fig">c</xref>). MetaCLADE+UProC behaviour on the other four MG/MT samples in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> is reported in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figures S12E-S15E and shows a high improvement in performance associated to high confidence <italic>E</italic> values (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figures S12F-S15F).</p></sec></sec><sec id="Sec13" sec-type="discussion"><title>Discussion</title><p>MetaCLADE was especially designed to consider the partial information contained in domain fragments, localised in reads. For this, we defined a powerful two-dimensional domain-dependent gathering threshold and we use multiple models to represent each domain, possibly characterising small conserved motifs for the domain. In future development, we foresee to improve the tool in several ways (see also [<xref ref-type="bibr" rid="CR43">43</xref>]):</p><p><bold>More domains and new models for an improved MetaCLADE annotation.</bold> New CCMs could be added to the library with the hope to reach novel and unrepresented evolutionary solutions for a domain. An obvious improvement could be obtained by extending the library with the set of new domains included in Gene3D and TIGRFAM. The motifs represented in PRINTS and ProSite could be also considered and the associated profiles handled in MetaCLADE. Note that MetaCLADE package provides the program to pre-compute gathering thresholds for all domain models. This allows the user to compute appropriate thresholds based on new CCMs.</p><p><bold>Constructing a library of conserved small motifs.</bold> The search for sequence motifs in an environmental sample might be realised with a computationally costly &#x0201c;all against all&#x0201d; read comparison. Alternatively, starting from the most conserved patterns comprised in CCMs, we can generate a repertoire of significant motifs specific of each domain in order to improve hit selection criteria. A systematic classification of these motifs might lead to datasets of motifs that could be used as environmental signatures of metabolic activities.</p><p>These &#x0201c;environmental patterns&#x0201d; could be also used to find new domains in environmental samples with MetaCLADE. The advantage in this search approach, compared to an &#x0201c;all against all&#x0201d; strategy, is that patterns are constructed starting from domains, possibly functionally annotated and that this annotation could be used to associate a potential functional role to new domains discovered through the pattern.</p><p><bold>Annotation of longer sequences.</bold> Availability of long reads and read assembly in contigs allow reconstructing longer stretches, and possibly entire, ORF sequences. In this case, one could replace the third filter in MetaCLADE with DAMA [<xref ref-type="bibr" rid="CR73">73</xref>], to reconstruct the best domain architecture as done in CLADE.</p><p><bold>Reduction of the number of redundant models in MetaCLADE.</bold> Some of the probabilistic models in MetaCLADE library are expected to be redundant, and a suitable handling of these models, after clustering, should help to increase the speed of the method and to preserve the same predictive power. Future development of MetaCLADE will reduce the number of redundant models representing domains.</p><p><bold>New criteria to filter overlapping hits in MetaCLADE.</bold> Different domain hits could be selected by exploiting further the characteristics of the two-dimensional space of sequences pre-computed for the domains. For instance, one could privilege the domain hits with larger bit-score/mean-bit-score distance from the closest negative in the space. These filtering conditions could improve the annotation and need to be tested at large scale.</p><p><bold>MetaCLADE differences with CLADE.</bold> MetaCLADE has been designed with the purpose of annotating MG and MT reads. It exploits the multi-source annotation strategy introduced in CLADE and the CLADE model library, but it handles the models and their output in a different manner. Indeed, the CLADE pipeline combines the output of its rich database of probabilistic models with a machine-learning strategy in order to determine a set of best predictions for each domain sequence. Then, DAMA [<xref ref-type="bibr" rid="CR73">73</xref>] is used to find the best domain architecture, by using information on domain co-occurrence and by exploiting multi-objective optimisation criteria.</p><p>Neither CLADE machine-learning algorithm nor DAMA are used in MetaCLADE. In fact, the characteristics of MG and MT reads, compared to full protein sequences, are their short lengths and the presence of multiple sequencing errors in them compared to full-length ORFs. Hence, they demand the design of a special computational protocol taking into account the particular nature of the data; namely: 
<list list-type="order"><list-item><p>CCMs cannot be used with tailored GA thresholds as in CLADE. Instead, we introduce an original bi-dimensional gathering threshold that is specifically designed for evaluating short hits. For each domain, we compute a probability space on which to evaluate hits. This is done with a na&#x000ef;ve Bayes classifier. Note that the computation of such a probability space depends on an appropriate generation of positive and negative sequences on which evaluate models for a domain.</p></list-item><list-item><p>CLADE machine-learning algorithm cannot be used for protein fragment identification. Indeed, CLADE works well with the full domain annotation of known genomes. In its design, it explicitly considers <italic>E</italic> value, hit length, consensus on multiple domain hits and clade-specific hits. On the other hand, read annotation should be less sensitive to sequence errors and hit length and should disregard the species the sequence comes from. In MetaCLADE, we do not use a SVM combining the above characteristics but instead we create a simpler pipeline of hit selection.</p></list-item><list-item><p>DAMA, the tool used in CLADE to reconstruct protein architectures, cannot be used on short reads. Indeed, reads might be long enough to contain at most one adjacent pair of domains and certainly cannot provide information to evaluate the contextual annotation of a domain within a potential domain architecture. In MetaCLADE, knowledge of adjacent pairs of domains could be considered but we left it for future developments.</p></list-item></list></p></sec><sec id="Sec14" sec-type="conclusion"><title>Conclusion</title><p>MG and MT datasets have been explored mostly to learn about which and in what abundance species are present in the community. Learning about the functional activity of the community and its subcommunities is a crucial step to understand species interactions and large-scale environmental impact. Ecological questions, such as how limited availability of abiotic factors in an ocean shape most abundant genes in a community, or how temperature affects eukaryotic phytoplankton growth strategies, for example, can be approached with an accurate domain annotation and a precise functional mapping. In this respect, one might need to zoom into functional activities and metabolic pathways employed by the environmental communities that might involve non-highly expressed genes. This means searching for lowly abundant domains that, through cooperation, might imply important functional effects. In order to capture common and rare entities in a given environment, functional annotation methods need to be as precise as possible in identifying remote homology.</p><p>Nowadays, the bottleneck resides in the annotation step, directly influencing an appropriate quantitative estimation of the domains. Here, we show how MetaCLADE, based on a multi-source annotation strategy especially designed for MG/MT data, allows for the discovery of patterns in very divergent sequences and provides a way to overcome this fundamental barrier. With the ongoing generation of new MG/MT data, unknown sequences will augment in number and probabilistic models are expected to play a major role in the annotation of sequences that span unrepresented sequence spaces. This point is clearly shown in our comparison with UProC, which is based on k-mer recognition, and therefore particularly adapted to the identification of already known domain sequences. By construction, UProC approach cannot be successful on unknown diverged domain sequences, a context where probabilistic domain modelling fully reveals its predictive power.</p></sec><sec id="Sec15"><title>Methods</title><p>This section explains MetaCLADE&#x02019;s methodology and the datasets used in the analyses. The differences between MetaCLADE and CLADE [<xref ref-type="bibr" rid="CR43">43</xref>] are presented in the &#x0201c;<xref rid="Sec13" ref-type="sec">Discussion</xref>&#x0201d; section. The time complexity is explained in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>.</p><p>The testing datasets were designed to fit current technological characteristics. We considered that Illumina represents nowadays the dominant technology in most sequencing projects. Currently, the HiSeq and MiSeq platforms are able to produce pair-end reads of 150 bp and 300 bp, respectively (with a much higher throughput for the first one). Such fragments in practice might be even shorter after the required low-quality-base trimming. For these reasons, we considered testing datasets of reads of increasing lengths. More precisely, we chose 100 bp and 200 bp as read lengths. However, we also chose to test MetaCLADE on simulated 454 fragments (mean length 523 bp) in order to prove its versatility and to show the annotation improvement as read length increases. This test is particularly important in view of the efforts from current technologies to increase read length.</p><sec id="Sec16"><title>The multi-source annotation strategy and the CLADE library</title><p>Widely used search methods based on sequence-profile and profile-profile comparison, such as PSI-BLAST [<xref ref-type="bibr" rid="CR45">45</xref>], HMMer [<xref ref-type="bibr" rid="CR47">47</xref>] and HHblits [<xref ref-type="bibr" rid="CR49">49</xref>], are based on a mono-source annotation strategy, where a single probabilistic model, generated from the consensus of a set of homologous sequences, is used to represent a protein domain. The mono-source strategy typically performs well when sequences are highly conserved. In this case, the consensus model captures the most conserved features in domain sequences and it can be successfully used to find new domains in databases of sequences, sharing the same features as the original sequence. However, when sequences have highly diverged, consensus signals become too weak to generate a useful probabilistic representation and models constructed by global consensus do not characterise domain features properly.</p><p>To overcome this fundamental bottleneck, CLADE [<xref ref-type="bibr" rid="CR43">43</xref>], a domain annotation tool tailored to full genomes, introduced a multi-source annotation strategy, where several probabilistic models are used to represent a protein domain. For each Pfam domain <italic>D</italic><sup><italic>i</italic></sup>, CLADE considers the FULL set of homologous sequences <italic>S</italic><sup><italic>i</italic></sup> in Pfam [<xref ref-type="bibr" rid="CR61">61</xref>] associated to <italic>D</italic><sup><italic>i</italic></sup>, and for some representative sequences <italic>s</italic><sub><italic>j</italic></sub> in <italic>S</italic><sup><italic>i</italic></sup> (see below), it constructs a model by retrieving with PSI-BLAST [<xref ref-type="bibr" rid="CR45">45</xref>] a set of sequences similar to <italic>s</italic><sub><italic>j</italic></sub> from the NCBI NR database. The probabilistic model generated in this way displays features that are characteristic of the sequence <italic>s</italic><sub><italic>j</italic></sub> and that might be very different for other sequences <italic>s</italic><sub><italic>k</italic></sub> in <italic>S</italic><sup><italic>i</italic></sup>. The more divergent the homologous domain sequences <italic>s</italic><sub><italic>j</italic></sub> and <italic>s</italic><sub><italic>k</italic></sub> are, the more models constructed from these sequences are expected to display different features. It is therefore important for a domain <italic>D</italic><sup><italic>i</italic></sup> to be represented by several models that can characterise its different pathways of evolution within different clades. These probabilistic models are called clade-centered models (CCMs). The multi-source annotation strategy has proven more efficient than the mono-source annotation strategy when applied to full genomes [<xref ref-type="bibr" rid="CR43">43</xref>]. In particular, due to their closeness to actual protein sequences, CLADE&#x02019;s CCMs are shown to be more specific and functionally predictive than the broadly used consensus models.</p><p>MetaCLADE is based on the multi-source annotation strategy and employs the CLADE library that includes the Pfam sequence consensus models (SCM) and at most 350 clade-centered models (CCM), with an average of 161 models per domain. The representative sequences associated to these models are selected in order to span most of the tree of life, the underlying idea being that evolutionary patterns can be found in species that are very far apart in the tree. This amounts to more than 2.5 millions probabilistic models.</p></sec><sec id="Sec17"><title>The MetaCLADE&#x02019;s pipeline</title><p>MetaCLADE&#x02019;s pipeline is illustrated in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>. It is based on two main steps, dedicated to the identification of domain hits and on their selection, and on a pre-computed learning step setting domain-specific two-dimensional thresholds used in domain selection.</p><sec id="Sec18"><title>Identification of domain hits</title><p>MetaCLADE takes as input a set of MG/MT sequences to be annotated and the CLADE model library. More specifically, the input sequences coming from a dataset of reads are expected to be (subsequences of) open reading frames (ORFs). Alternatively, one can use MetaCLADE on the six reading frame translations of the reads.</p><p>Each sequence is scanned with the model library in order to identify all domain hits. Each hit is defined by a bit-score, that is the PSI-BLAST/HMMer score associated to the match, and by a mean-bit-score, that is the bit-score of the hit divided by its length. These two scores are used to evaluate the likelihood of the hit to represent a true annotation (see the &#x0201c;<xref rid="Sec19" ref-type="sec">Selection of domain hits</xref>&#x0201d; section; for the computation of the likelihood see the &#x0201c;<xref rid="Sec20" ref-type="sec">Pre-computed two-dimensional gathering thresholds for domain identification</xref>&#x0201d; section).</p><p>The output of this first step of MetaCLADE is a set of hits, each one defined by a domain family <italic>D</italic>, a probabilistic model <italic>M</italic> associated to <italic>D</italic>, a bit-score and a mean-bit-score.</p></sec><sec id="Sec19"><title>Selection of domain hits</title><p>The second step of the pipeline filters the set of hits as follows: 
<list list-type="order"><list-item><p>All pairs of overlapping hits associated to the <italic>same</italic> domain (i.e. the overlap region covers at least 85% of both hit lengths) are processed with the intention of eliminating their redundancy. Therefore, for each overlapping pair, we retain only the best hit (i.e. with the higher bit score). The filtering is realised independently for CCMs and for SCMs.</p></list-item><list-item><p>Based on the probability obtained with the naive Bayes classifier [<xref ref-type="bibr" rid="CR74">74</xref>] applied to each Pfam domain (see the &#x0201c;<xref rid="Sec22" ref-type="sec">A naive Bayes classifier sets two-dimensional thresholds for fragmented domains</xref>&#x0201d; section), MetaCLADE retains only those hits whose bit-score is greater than a domain-specific lower bound identified by the classifier and whose probability <italic>p</italic> of being a true positive is greater than 0.9. More precisely, such a lower bound is defined as the smallest bit-score of the negative sequences used by the classifier during its training (see the &#x0201c;<xref rid="Sec20" ref-type="sec">Pre-computed two-dimensional gathering thresholds for domain identification</xref>&#x0201d; section).</p></list-item><list-item><p>Hits are filtered according to a <italic>ranking</italic> function based on the bit-score and the identity percentage computed with respect to the model consensus sequence. Specifically, we associate to each hit a real number in the interval [0,1] representing the statistical significance of the bit-score. Such a value is then multiplied by the identity percentage of the hit in order to define the <italic>ranking score</italic>. Therefore, domain hits are ordered by decreasing values of their ranking scores and iteratively discarded if they share at least 10 residues with some domain with a higher scoring hit. Eventually, this allows us to provide a small architecture (usually involving up to two domains, due to read length) for each annotated sequence.</p></list-item></list></p><p>Note that in the third point, the ranking score combines the (statistical significance of the) bit-score and the percentage identity of the match in a product and that these two values are highly correlated. Indeed, one expects higher bit-scores to be associated to higher sequence identities. This means that when two matches differ strongly on their bit-scores, the respective products will not be affected by the percentage identity of the matches. On the contrary, it is when the bit-scores are close to each other that the percentage identity of the matches will play a role by favouring matches with higher sequence identity. Intuitively, MetaCLADE prioritises bit-scores while letting percentage identity play a discriminative role between very close bit-score values.</p><p>The output of this filtering step is the ORF annotation with non-overlapping domain hits. Due to the short length of the reads, one expects at most two domains per read, possibly flanked by domain fragments on the right and/or the left. Consequently and in contrast to CLADE, there is no reconstruction of the best architecture with DAMA.</p><p>Also, note that the first filter is used to reduce the size of the set of domain hits, possibly huge at the beginning due to redundant predictions of the high number of models. The second filter is used to identify hits having a high probability to be true hits, and it constitutes the core of the filtering process. The third filter is used to identify the best solution, among the ones with highest bit-score, based on motif conservation.</p><p>As a consequence of the construction of the probability space for a domain, the second filter asks for domain hits to have a bit-score greater than the smallest bit-score of the negative sequences in the space. This is because negative sequences considered by the classifier are a selected sampling of the space of negatives (see the &#x0201c;Generation of negative sequences&#x0201d; section below); namely, among all negatives generated by the algorithmic procedure, we selected those that lie further away from the origin and that, in consequence, have the highest statistical significance. These selected negative sequences tend to group together further from the origin of the space and to lie at the borderline of regions characterised by positive sequences. Hence, one should properly evaluate the acceptance threshold against this specificity.</p></sec></sec><sec id="Sec20"><title>Pre-computed two-dimensional gathering thresholds for domain identification</title><p>MG and MT samples demand to annotate domain fragments, possibly of small length. In order to explicitly distinguish small hits from long ones, MetaCLADE directly estimates the likelihood for a small hit to be a positive sequence by considering the bit-score of the hit and also its mean-bit-score; namely, it defines a two-dimensional gathering threshold (GA) for each domain by combining bit-score and mean-bit-score and by identifying multiple regions in the two-dimensional sequence space that, with a high probability, provide reliable annotations for short sequences. Probabilities are estimated with a naive Bayes classifier [<xref ref-type="bibr" rid="CR74">74</xref>], and the statistical procedure is explained below.</p><sec id="Sec21"><title>Construction of positive and negative training sets</title><p>For each domain, MetaCLADE estimates bit-score and mean-bit-score domain-sensitive thresholds. More precisely, it constructs a sequence space for each domain, by defining a set of positive sequences (i.e. actual fragments of the domain) and by generating a set of negative sequences (i.e. sequences wrongly annotated with the domain). Ideally, for each domain, one would like to have a training set comprised of a comparable number of positive and negative sequences.</p><p><bold>Definition of positive sequences.</bold> The training set of positive sequences was constructed as follows. For each domain <italic>D</italic><sup><italic>i</italic></sup> and for each sequence in the Pfam SEED set of homologous sequences for <italic>D</italic><sup><italic>i</italic></sup>, we created a set of prefixes and suffixes of the sequence to simulate small domain portions coming from the beginning or the end of the domain sequence that may be found in MG reads. The maximum size <italic>M</italic> of prefixes and suffixes was set to 30% of the entire domain sequence length and to a maximum of 100 aa. Hence, fragments were determined by increasing lengths <italic>n</italic>&#x000b7;<italic>L</italic>, where <italic>L</italic> is a constant depending on domain size and <italic>n</italic>=1,2,3&#x02026;<italic>N</italic> is a multiplicative factor where <italic>N</italic> corresponds to the smallest integer such that <italic>M</italic>&#x02264;<italic>N</italic>&#x000b7;<italic>L</italic>. For domains of length between 15 and 75 aa, the constant <italic>L</italic> was set at 5 aa, for sizes &#x0003e;75 aa it was set to 10 aa, and for sizes &#x0003c;15 aa it was set to 1 aa. For large domains, &#x0003e;270 aa (this corresponds to one standard deviation away from the mean in the distribution of domain model sizes as reported in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S1), we expect that reads may lie somewhere in the middle of the domain and therefore we extracted random sequences from the original sequence that were not already covered by small fragmentations of the extremes. Fragment positions were set by randomly choosing their first position along the middle part of the sequence, and fragment lengths were randomly picked from a normal distribution with mean 50 and standard deviation 25. The number of fragments corresponds to ten.</p><p><bold>Generation of negative sequences.</bold> In order to define a set of negative sequences for each model (CCM or SCM) associated to a domain, we generate a large amount of decoy sequences and select as negatives those where the original domain is identified by the model (with an <italic>E</italic> value &#x0003c;1 for CCMs and a positive bit-score for SCMs).</p><p>The algorithm generates first sequences with two different methods: 
<list list-type="order"><list-item><p>A random shuffling of the 2-mers of each SEED sequence</p></list-item><list-item><p>The reversal of SEED sequences and checks whether they are negatives or not. If the number of negatives reaches at least the 50% of the positive sequences, then the algorithm stops the search. Otherwise, new sequences are generated with a third method:</p></list-item><list-item><p>By constructing a Markov model of order 3 for each domain and by using it to generate random sequences with positional probabilities</p></list-item></list></p><p>Note that in 3, the space of 160,000 (20<sup>4</sup>) 4-tuples is evaluated by assigning a probability to appear in a domain sequence to each 4-tuple. This is done with a pseudo-count, by considering each 4-tuple to appear at least once and by counting the number of occurrences <italic>n</italic> of the 4-tuple in the SEED sequences of the domain. The probability of a 4-tuple is set to <inline-formula id="IEq3"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\frac {n+1}{160~000+ N}$\end{document}</tex-math><mml:math id="M6"><mml:mfrac><mml:mrow><mml:mi>n</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mn>160</mml:mn><mml:mspace width="1em"/><mml:mn>000</mml:mn><mml:mo>+</mml:mo><mml:mi>N</mml:mi></mml:mrow></mml:mfrac></mml:math><inline-graphic xlink:href="40168_2018_532_Article_IEq3.gif"/></alternatives></inline-formula>, where <italic>N</italic> is the total number of 4-tuple occurrences in the SEED sequences. The Markov model of order 3 is defined on these probability estimations.</p><p>Only generated sequences whose original domain has been correctly identified by PSI-BLAST (for CCMs) with an <italic>E</italic> value &#x0003c;1 or by HMMer (for SCMs) with a positive bit-score are considered as negative sequences for the MetaCLADE models (CCMs or SCM, respectively) and are included in their training sets. The usage of different threshold for the two tools, PSI-BLAST and HMMer, is due to the observation that it is easier to produce negatives with PSI-BLAST than with HMMer; therefore, an <italic>E</italic> value threshold &#x0003c;1, much more selective than a positive bit-score, would reduce the large number of accepted negatives for CCMs. The statistical significance and the impact of these thresholds on the space of positive and negative sequences is discussed below (see the &#x0201c;<xref rid="Sec22" ref-type="sec">A naive Bayes classifier sets two-dimensional thresholds for fragmented domains</xref>&#x0201d; section).</p><p>The algorithm estimates the number of decoy sequences that should be generated to obtain the 50% of negative sequences and stops when this estimated number of sequences is generated. For example, supposing there are 100 positive sequences for a domain, then we seek to generate at least 50 negative sequences. If random reshuffling and reversal generate only 10 negative sequences, a Markov model is expected to generate 40 sequences. Since most decoys generated by the Markov model will not be selected as negatives, one estimates the number of decoys that should be generated by the Markov model to obtain 40 negatives and stops the algorithm after such number. The estimation has been realised based on the observation that false positives are found after a very large number of decoy generations: roughly one expects to obtain 1&#x02013;10 false positives out of 10,000 decoys for SCMs and out of 1000 decoys for CCMs. CCMs lie very close to actual sequences, and for this reason, we expect them to be much more effective in recognising a domain in a random sequence generated by a Markov model of that domain than a SCM. If a domain contains only a few sequences in its SEED set, <italic>n</italic> would be too small to produce a significant bias in the 4-mer probability emission. Therefore, <italic>n</italic> is multiplied by a factor <italic>W</italic> with initial value at 10 and incremented by one until we reach the generation rate of 1&#x02013;10 negative sequences out of 10,000 decoys for the domain and its SCM. (To estimate the weight, we only use the SCM and no CCMs.) This leads to emission probabilities <inline-formula id="IEq4"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\frac {(n+1) \times W}{160,000 + N\times W}$\end{document}</tex-math><mml:math id="M8"><mml:mfrac><mml:mrow><mml:mo>(</mml:mo><mml:mi>n</mml:mi><mml:mo>+</mml:mo><mml:mn>1</mml:mn><mml:mo>)</mml:mo><mml:mo>&#x000d7;</mml:mo><mml:mi>W</mml:mi></mml:mrow><mml:mrow><mml:mn>160</mml:mn><mml:mo>,</mml:mo><mml:mn>000</mml:mn><mml:mo>+</mml:mo><mml:mi>N</mml:mi><mml:mo>&#x000d7;</mml:mo><mml:mi>W</mml:mi></mml:mrow></mml:mfrac></mml:math><inline-graphic xlink:href="40168_2018_532_Article_IEq4.gif"/></alternatives></inline-formula>. Note that each decoy is tested against both SCM and CCMs associated to the domain and that it is considered as negative if at least one model identifies the domain in it. If too many negative sequences were produced, then only those that are the most distant from the origin of the sequence space (that is the euclidean distance of the point, defined by the bit-score and the mean-bit-score of a hit, from the origin of the two-dimensional space) are retained, limiting their amount to the number of positive sequences. Note that sequences that are most distant from the origin are those with higher statistical significance. The distribution of sequences generated with Markov models is reported in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S2. They make a total of 39,241,830,000 sequences. In contrast, the first two methods generated a total of 22,816,657.</p><p>Positive and negative sequences are described by a bit-score and a mean-bit-score. To avoid an over-representation of sequences with the same bit-score and mean-bit-score, we consider only one representative per fixed values of bit-score and mean-bit-score. This makes the two sets of positive and negative sequences, all domain confounded, to be numerically comparable. The total amount of non-redundant (in terms of their bit-score and mean-bit-score) positive matches is 13,697,142 for SCMs and 7,548,890 for CCMs while the total amount of negatives is 7,569,171 and 6,342,944, for SCMs and CCMs respectively (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S3).</p><p>Globally, we ensure the full training set is comprised of roughly 50% of positive sequences and 50% of negative ones (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S4B). This proportion varies from domain to domain and depends on the difficulty to generate correctly annotated random sequences. In Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S4, we report the proportions of negative sequences for CCMs and SCMs generated by the first two methods (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S4A) and compare them to the distributions of sequences generated by all three methods (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S4B). Clearly, the third method contributes to the largest number of negative sequences for each domain and establishes the expected numerical balance between the two training sets; all domains confounded.</p></sec><sec id="Sec22"><title>A naive Bayes classifier sets two-dimensional thresholds for fragmented domains</title><p>Positive and negative sequences are put together and analysed to obtain best separation parameters for CCMs and SCMs; namely, we use a discrete version of the naive Bayes classifier [<xref ref-type="bibr" rid="CR74">74</xref>] (downloadable from <ext-link ext-link-type="uri" xlink:href="http://www.cs.waikato.ac.nz/ml/weka/citing.html">http://www.cs.waikato.ac.nz/ml/weka/citing.html</ext-link>) to construct learning models for each Pfam domain. The discrete version of the naive Bayes classifier provides a finite partition of the sequence space and an estimation of the probability for a sequence to be a positive or a negative hit. Notice that we realise two different analyses, one on CCMs (generated by PSI-BLAST) and the other on SCMs (pHMMs generated by HMMer), because we cannot immediately compare their bit-scores. By so doing, we determine two distinct separation spaces and appropriate parameters for the two model predictions. In particular, only one probability space is estimated for all CCMs of a domain. All positive and negative sequences, generated for all CCMs, are considered in the same sequence space and the associated probability space is computed.</p><p>Figure&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref><xref rid="Fig7" ref-type="fig">a</xref> illustrates an example of separation of the spaces of positive and negative hits for the CCMs and SCM of a Pfam domain (PF01036), analysed with the naive Bayes classifier. Note that short fragments have small bit-scores with a possibly large mean-bit-score and that negative sequences are characterised by small bit-scores and small mean-bit-scores. Also, the identification of fragmented coding regions (especially important for the annotation of MG/MT datasets, where ORFs that are present in MG/MT reads are fragmented) will only be realised through very low bit-scores because only parts of domains are present. These sequences can be seen in the blue region of positive hits in Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref><xref rid="Fig7" ref-type="fig">a</xref>, where successively larger fragments appear associated to successively larger scores. They are represented by trails of points in the figure, where small fragments have small scores.
<fig id="Fig7"><label>Fig. 7</label><caption><p>Naive Bayes classifier analysis of Pfam domains. <bold>a</bold> Naive Bayes classifier analysis of the training set evaluating thresholds for positive (cyan dots) and negative (orange dots) hits discrimination of the Pfam domain &#x0201c;bacteriorhodopsin-like protein&#x0201d; (PF01036). The space of positive hits is coloured blue, and the space of negative hits is the complementary one, coloured from green to dark red (see colour scaling). From dark red to blue, each coloured area is characterised by a different probability for a sequence to be a positive sequence. The space of solutions for all CCMs confounded (left) and the SCM (pHMM; right) are described. The red dots correspond to the MG sequences identified by the models in the Equatorial Pacific (EPAC) MT dataset discussed below [<xref ref-type="bibr" rid="CR57">57</xref>]. The green vertical line on the SCM plot (right) corresponds to hmmscan GA threshold (=&#x02009;24, for this domain). Note that the grid is discrete and that rectangular regions are coloured with respect to probability intervals. This means that two adjacent regions with the same colour might have associated two different probabilities. <bold>b</bold> The probability space of CCMs and SCMs generated for MetaCLADE; all domains included. Each point of the plot is the average of the corresponding points in all spaces of solutions computed for CCMs and SCMs, respectively. Examples of such spaces of solutions are reported in <bold>a</bold> for the Pfam domain PF01036. The colour scale is defined with respect to probability values associated to the regions. The green vertical line in the SCM plot (right) corresponds to the average hmmscan GA threshold; all domain confounded. Note that if we take the bit-score only as a threshold in our database, we obtain that an average bit-score at 25 in <bold>a</bold> is statistically meaningful, independently of the mean-bit-score. This value is used as a default threshold in <ext-link ext-link-type="uri" xlink:href="http://www.ebi.ac.uk/Tools/hmmer/search/phmmer">http://www.ebi.ac.uk/Tools/hmmer/search/phmmer</ext-link></p></caption><graphic xlink:href="40168_2018_532_Fig7_HTML" id="MO7"/></fig>
</p><p>A comparison between the two spaces illustrated in Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref><xref rid="Fig7" ref-type="fig">a</xref> shows a basic difference between CCM and SCM models. First, notice the much smaller number of negative sequences obtained for the SCM space compared to the CCM space (see &#x0201c;<xref rid="Sec21" ref-type="sec">Construction of positive and negative training sets</xref>&#x0201d; section). In the CCM space, negative sequences are more clearly separated by both bit-scores and mean-bit-scores from positive ones than in the SCM space. In fact, since CCMs are &#x0201c;closer&#x0201d; to sequences than SCMs, one expects their scores to be higher for positive sequences in CCMs than in SCMs. Also, the usage of a two-dimensional sequence space, determined by bit-scores and mean-bit-scores, improves the separation of positive and negative sequences in MetaCLADE compared to HMMer (hmmscan). In the plot describing the SCM space (Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref><xref rid="Fig7" ref-type="fig">a</xref>, right), the Pfam GA threshold excludes most of the Bacteriorhodopsin-like protein sequences detected by CCMs. More generally, for all Pfam domains, we computed the difference between the GA threshold associated to the SCM and the mean of the bit-scores for the five best negative sequences identified by the SCM. The distribution of the differences, displayed in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S5, shows a small standard deviation suggesting that MetaCLADE and Pfam/HMMer estimation of the (one dimensional) cutoff is similar. This control shows that naive Bayes classification produces reasonable thresholds when projected in one dimension.</p><p>Figure&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref><xref rid="Fig7" ref-type="fig">b</xref> illustrates the general behaviour of the probability spaces for all CCMs and SCMs, all domains confounded. It shows the coherence of the spaces across models of the same domain and highlights bit-scores and mean bit-scores intervals defining rejecting and accepting regions. One observes large regions associated to high probability values (&#x0003e;0.9) accepting true positives.</p></sec></sec><sec id="Sec23"><title>Comparison to InterProScan</title><p>To compare MetaCLADE to InterProScan, we used several tools and domain model libraries: Pfam [<xref ref-type="bibr" rid="CR61">61</xref>], TIGRFAM [<xref ref-type="bibr" rid="CR64">64</xref>], Gene3D [<xref ref-type="bibr" rid="CR63">63</xref>] and PRINTS &#x00026; ProSite [<xref ref-type="bibr" rid="CR65">65</xref>, <xref ref-type="bibr" rid="CR66">66</xref>]. Annotations produced by these tools were downloaded from available annotation files provided by the EBI metagenomics pipeline <ext-link ext-link-type="uri" xlink:href="http://www.ebi.ac.uk/metagenomics/pipelines/">http://www.ebi.ac.uk/metagenomics/pipelines/</ext-link> (versions 1.0 or 2.0 depending on the dataset). The EBI metagenomics pipeline uses InterProScan (v5.0 for pipeline 1.0 and 5.9-50 for pipeline 2.0) as tool to annotate predicted ORF (using FragGeneScan 1.15) using the domain libraries above. HMMer v3.0 was downloaded from <ext-link ext-link-type="uri" xlink:href="http://hmmer.org">http://hmmer.org</ext-link> and run with default parameters and curated inclusion thresholds (option &#x02013;cut_ga).</p></sec><sec id="Sec24"><title>Comparison to UProC and a combination of UProC and MetaCLADE</title><p>UProC [<xref ref-type="bibr" rid="CR38">38</xref>] (version 1.2.0, downloaded at <ext-link ext-link-type="uri" xlink:href="https://github.com/gobics/UProC">https://github.com/gobics/UProC</ext-link>) is a tool designed for large-scale sequence analysis. Given a read dataset, UProC decides whether a domain is present or not in a sequence, but it does not point out precisely where the domain is localised in the sequence by defining starting and ending positions, nor it associates an <italic>E</italic> value to the matching. The complementary performance of UProC compared to MetaCLADE suggests to combine the two tools in order to achieve best performance in terms of number of correctly predicted domains. For this reason, we define &#x0201c;MetaCLADE+UProC&#x0201d; by adding to MetaCLADE&#x02019;s annotations all those obtained by UProC on reads left unannotated by MetaCLADE. In a similar way, we define the symmetric &#x0201c;UProC+MetaCLADE&#x0201d; annotation.</p><p>In order to evaluate UProC, we looked for the position of the domain in a read and associated an <italic>E</italic> value to it as follows. For each read annotated with a domain <italic>D</italic> by UProC, we used BLAST (with default parameters) to map it against all Pfam FULL sequences representing <italic>D</italic> and we selected the best hit, if it existed. The <italic>E</italic> value associated to this hit was considered as the UProC <italic>E</italic> value score.</p></sec><sec id="Sec25"><title>Comparison to HMM-GRASPx</title><p>MetaCLADE was compared to HMM-GRASPx version 0.0.1 [<xref ref-type="bibr" rid="CR37">37</xref>] (downloaded at <ext-link ext-link-type="uri" xlink:href="http://sourceforge.net/projects/hmm-graspx">http://sourceforge.net/projects/hmm-graspx</ext-link>); other successive beta versions have been tested, but they did not provide better results. HMM-GRASPx is a profile-based method, like MetaCLADE. It considers profile HMMs of protein families as references and uses them to guide the assembly of complete or near-complete genes and protein sequences related to a particular family. The annotation is realised after the assembly with a mapping of the reads on validated contigs.</p></sec><sec id="Sec26"><title>The use of clans and InterPro families</title><p>In order to evaluate MetaCLADE performance on datasets of simulated sequences, we identify domains at the Pfam clan [<xref ref-type="bibr" rid="CR55">55</xref>] or InterPro family [<xref ref-type="bibr" rid="CR56">56</xref>] level. This is done because sequence similarity within domains in the same Pfam clan is usually high and genome annotation is often mislead by domains belonging to the same clan. This is even more true in MG datasets, where one often needs to annotate fragments of a domain displaying a weaker signal due to the reduced length.</p><p>Pfam clans are groups of proteins for which common ancestry can be inferred by similarity of sequence, structure or profile HMM [<xref ref-type="bibr" rid="CR55">55</xref>]. The list of Pfam clans was retrieved at http://ftp://ftp.ebi.ac.uk/pub/databases/Pfam/releases/Pfam27.0/Pfam-A.clans.tsv.gz.</p><p>InterPro families represent groups of evolutionarily related proteins that share common functions. Such entries tend to be near full length and typically do not undergo recombination, in contrast to domains [<xref ref-type="bibr" rid="CR56">56</xref>]. The list of InterPro families was retrieved at <ext-link ext-link-type="uri" xlink:href="https://www.ebi.ac.uk/interpro/download.html">https://www.ebi.ac.uk/interpro/download.html</ext-link>.</p></sec><sec id="Sec27"><title>Domain abundance</title><p>The functional analysis of a MG/MT sample is realised by characterising domain abundance within a functional class with a normalised value between 0 and 1. This normalisation is done by dividing the number of domains detected in a functional class by the total number of domains belonging to the most represented class in the environmental sample. We speak about &#x0201c;normalised abundance&#x0201d;.</p><p>A second kind of normalisation is realised with respect to multiple environments, and it is used for comparing domain abundance within the same functional class across these environments. A normalised domain abundance <inline-formula id="IEq5"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$N^{S}_{I}$\end{document}</tex-math><mml:math id="M10"><mml:msubsup><mml:mrow><mml:mi>N</mml:mi></mml:mrow><mml:mrow><mml:mi>I</mml:mi></mml:mrow><mml:mrow><mml:mi>S</mml:mi></mml:mrow></mml:msubsup></mml:math><inline-graphic xlink:href="40168_2018_532_Article_IEq5.gif"/></alternatives></inline-formula>, where <italic>S</italic> is the sample and <italic>I</italic> is the domain, is computed as the product of the actual domain abundance per megabase by the average size of all samples. By multiplying by the average size of all samples, we provide an indication of the expected number of domains if all environments had the same size and can compare environments with respect to such estimations.</p></sec><sec id="Sec28"><title>Functional analysis of annotated real datasets</title><p>In order to validate MetaCLADE on all real MT and MG datasets, we associated a function based on GO classification to both the domains identified with MetaCLADE and the domains identified with HMMer (hmmscan). We used Pfam2GO [<xref ref-type="bibr" rid="CR75">75</xref>] and annotated biological process terms of GO-Slim [<xref ref-type="bibr" rid="CR56">56</xref>, <xref ref-type="bibr" rid="CR76">76</xref>]. Pfam2GO was retrieved from <ext-link ext-link-type="uri" xlink:href="http://geneontology.org/external2go/pfam2go">http://geneontology.org/external2go/pfam2go</ext-link>, and GO-Slim classification for MG was retrieved from <ext-link ext-link-type="uri" xlink:href="http://geneontology.org/page/download-ontology">http://geneontology.org/page/download-ontology</ext-link>. To highlight the differences between MetaCLADE and HMMer, we compared domain abundance in all GO-term classes. For this, we normalised domain count in each MT dataset with respect to the size of the sample as described above.</p></sec><sec id="Sec29"><title>Motif validation</title><p>To validate motifs identified by MetaCLADE on the O&#x02019;Connor lake dataset, we run MEME [<xref ref-type="bibr" rid="CR68">68</xref>] with default parameters on the MEME Suite 4.11.2 server at <ext-link ext-link-type="uri" xlink:href="http://meme-suite.org/tools/meme">http://meme-suite.org/tools/meme</ext-link>.</p></sec><sec id="Sec30"><title>MG/MT datasets used in the analyses</title><sec id="Sec31"><title>A dataset of simulated reads generated from bacterial and archaeal genomes</title><p>We generated a set of fragmented sequences from a set of 11 archaeal and 44 bacterial fully sequenced genomes. The list of species, NCBI accession numbers and genome lengths is reported in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S2. We assumed all species be equally abundant.</p><p>In order to generate a set of 500,000 clones, we first used MetaSim [<xref ref-type="bibr" rid="CR53">53</xref>], according to a normal distribution with a mean of 800 bp and a standard deviation of 100 bp. Then, we applied FlowSim [<xref ref-type="bibr" rid="CR77">77</xref>] to the set of clones to obtain actual reads simulated with realistic insertion and deletion errors expected during DNA sequencing. More precisely, the simulation was performed according to the FlowSim platform 454 GS-FLX Titanium (error rate &#x0223c;1<italic>%</italic>). The read dataset, which contained sequences of &#x0223c;523 bp on average, was finally processed with FragGeneScan [<xref ref-type="bibr" rid="CR78">78</xref>] in order to predict the ORFs. This resulted in about 500,000 reads that were given as input to MetaCLADE.</p><p>In parallel, we used information available in the XML file associated to each genome in the EBI site (<ext-link ext-link-type="uri" xlink:href="http://www.ebi.ac.uk/genomes/">http://www.ebi.ac.uk/genomes/</ext-link>) to identify coding sequences included in the SwissProt database. For these coding sequences, we retrieved annotated domains and their positions from the InterPro site through the UniProtKB proteins file (<ext-link ext-link-type="uri" xlink:href="http://ftp.ebi.ac.uk/pub/databases/interpro/protein2ipr.dat.gz">http://ftp.ebi.ac.uk/pub/databases/interpro/protein2ipr.dat.gz</ext-link>). Domain annotation of coding sequences was used to evaluate MetaCLADE performance. For this simulated dataset, we selected positive domain hits with a probability threshold of 0.85 estimated by the naive Bayes classifier.</p><p>The dataset is available at <ext-link ext-link-type="uri" xlink:href="http://www.lcqb.upmc.fr/metaclade/">http://www.lcqb.upmc.fr/metaclade/</ext-link>.</p></sec><sec id="Sec32"><title>Three datasets of simulated reads to compare with UProC and HMM-GRASPx</title><p>MetaCLADE was compared to HMM-GRASPx and UProC on the Guerrero Negro Hypersaline Microbial Mats (GNHM) MG dataset [<xref ref-type="bibr" rid="CR79">79</xref>] used in [<xref ref-type="bibr" rid="CR38">38</xref>] to demonstrate UProC performance versus profile-based methods on short reads. GNHM was downloaded from <ext-link ext-link-type="uri" xlink:href="http://uproc.gobics.de">http://uproc.gobics.de</ext-link>. GNHM is a dataset presenting a large variety of species of low abundance, with archaeal and eukaryotic species much less abundant than bacterial ones. From GNHM data, following the protocol in [<xref ref-type="bibr" rid="CR38">38</xref>], we generated two sets of short reads simulating different read lengths of 100 bp and 200 bp, respectively. The only difference with the described protocol was in the latest HMMer, version 3.1b2 was used instead of the previous one (3.0) that was used in [<xref ref-type="bibr" rid="CR38">38</xref>]. For the two sets of reads, we considered two annotations as gold standard, one realised with HMMer (hmmscan with GA cutoff) and the other with CLADE [<xref ref-type="bibr" rid="CR43">43</xref>]. Both annotations are based on domains known in Pfam 27.</p><p>The comparison between MetaCLADE and HMM-GRASPx was realised on two simulated datasets of reads of length 100 bp and 200 bp, generated from a marine MG dataset containing 23 marine microbial genomes from the <italic>Alteromonas</italic>, <italic>Candidatus</italic>, <italic>Erythrobacter</italic>, <italic>Flavobacteriales</italic>, <italic>Nitrosococcus</italic>, <italic>Photobacterium</italic>, <italic>Prochlorococcus</italic>, <italic>Roseobacter</italic>, <italic>Shewanella</italic>, <italic>Synechococcus</italic> and <italic>Vibrio</italic> groups. Relative abundances of these bacteria were simulated according to their environmental composition [<xref ref-type="bibr" rid="CR37">37</xref>] with an average coverage of &#x0223c;&#x02009;27.5X (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S11). We considered these 23 bacterial genomes along with a selected number of important metabolic pathways (see Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S12). Only those Pfam (version 27) domain families involved in each one of these pathways were finally considered (family-pathway association was performed in [<xref ref-type="bibr" rid="CR37">37</xref>] using KEGG&#x02019;s release 73.0 of January 2015). The same domain families were taken into account in HMM-GRASPx and MetaCLADE. Illumina-like paired-end nucleotide reads were generated with wgsim (version 0.3.1-r13 obtained from <ext-link ext-link-type="uri" xlink:href="http://github.com/lh3/wgsim">http://github.com/lh3/wgsim</ext-link>, with error rate parameter -e 0.01) and translated into short peptide reads using FragGeneScan [<xref ref-type="bibr" rid="CR78">78</xref>] (version 1.30, with parameters -complete=0 -train=illumina_10). For each pathway, the gold standard was defined by searching the corresponding Pfam models against the complete proteomes and transferring the annotation to those reads that mapped to a domain hit for at least the 60% of their length. Finally, gene catalogs were built using MOCAT2&#x02019;s pipeline [<xref ref-type="bibr" rid="CR30">30</xref>] with default parameters.</p><p>The datasets are available at <ext-link ext-link-type="uri" xlink:href="http://www.lcqb.upmc.fr/metaclade/">http://www.lcqb.upmc.fr/metaclade/</ext-link>.</p></sec><sec id="Sec33"><title>Nine simulated datasets to test MetaCLADE&#x02019;s sensitivity to sequence origin</title><p>In order to test how MetaCLADE is sensitive to the distribution of species in a dataset, we created nine simulated datasets with increasing percentages of eukaryotic sequences, and we annotated them in order to examine the origin of the CCMs used by MetaCLADE. Specifically, each dataset is composed by 75,000 randomly chosen UniProt coding sequences that had not been picked to build any model of CLADE&#x02019;s library. The percentage of eukaryotic CDS among the datasets varies from 10 to 90% with steps of 10%. From such CDS, a set of 50 aa fragments has been uniformly extracted in order to reach a 1.5X coverage. Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S9 describes the simulated datasets (e.g. average CDS length, number of generated fragments). Overall, we employed 3128 bacterial, 9403 eukariotic, 218 archaeal and 2127 viral species and we annotated with 1,397,356 bacterial, 858,908 eukaryotic, 101,171 archaeal and 31,799 viral models (see Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S10).</p></sec><sec id="Sec34"><title>Real metagenomics and metatranscriptomic datasets</title><p>In order to validate MetaCLADE on real data, we analysed eleven MG and MT samples. The characteristics of these eleven datasets, such as number of reads, average read size and sequencing technique used to generate the dataset, whether it is a MG or a MT dataset, are provided in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S3. Available websites for download are given in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S4.</p><p>Five MT samples come from different geographic locations in the oceans, Antarctic (ANT), North Pacific (NPAC), Equatorial Pacific (EPAC), Arctic (ARC) and North Atlantic (NATL) [<xref ref-type="bibr" rid="CR57">57</xref>]. We have identified ORFs on the reads by analysing six reading frames and annotated them with HMMer and MetaCLADE. These datasets are available upon request addressed to the authors of [<xref ref-type="bibr" rid="CR57">57</xref>].</p><p>Four published MG datasets come from very different environments: soil, ocean, ancient bones and guts. For the gut environment, we also considered a MT sample. These five sets of reads were previously analysed for ORF identification by EBI with FragGeneScan [<xref ref-type="bibr" rid="CR78">78</xref>]. ORF sequences have been annotated by EBI based on five different domain databases found in InterPro [<xref ref-type="bibr" rid="CR56">56</xref>]: Pfam [<xref ref-type="bibr" rid="CR61">61</xref>], TIGRFAM [<xref ref-type="bibr" rid="CR64">64</xref>], Gene3D [<xref ref-type="bibr" rid="CR63">63</xref>] and PRINTS &#x00026; ProSite [<xref ref-type="bibr" rid="CR65">65</xref>, <xref ref-type="bibr" rid="CR66">66</xref>]. The search was realised with InterProScan [<xref ref-type="bibr" rid="CR80">80</xref>] as the final step of the EBI MG pipeline (<ext-link ext-link-type="uri" xlink:href="http://www.ebi.ac.uk/metagenomics/about">http://www.ebi.ac.uk/metagenomics/about</ext-link>). These tools are accessible from <ext-link ext-link-type="uri" xlink:href="http://www.ebi.ac.uk/services/proteins">http://www.ebi.ac.uk/services/proteins</ext-link>. Comparison with UProC was realised on these five sets.</p><p>The O&#x02019;Connor lake MG dataset (ERP009498) was downloaded from the EBI Metagenomics portal (<ext-link ext-link-type="uri" xlink:href="https://www.ebi.ac.uk/metagenomics/projects/ERP009498">https://www.ebi.ac.uk/metagenomics/projects/ERP009498</ext-link>). It was realised with Illumina HiSeq 2000 technology and contains 1,315,435 very short reads (123 nt average length). ORFs were identified by EBI.</p><p>MetaCLADE positive domain hits are selected with a probability threshold of 0.9.</p></sec></sec></sec><sec id="Sec35"><title>Time complexity</title><p>MetaCLADE was run on a High Performance Computing architecture. In all analyses reported here, parallel computation is exploited in MetaCLADE first main step and in the first two sub-steps of the second main step. Notice that MetaCLADE can be run on a desktop computer, especially when restricting the analysis to a small subset of domains. However, when considering the full domain library on a large-size dataset of sequences, MetaCLADE can take a large amount of time as highlighted in Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Table S7 for all MG/MT samples considered in the article. For further details, see Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>.</p></sec><sec id="Sec36"><title>Evaluation measures</title><p>In order to evaluate the annotation tools, we used the standard measures of precision (also named positive predictive value, PPV), accounting for how many annotations are correct and defined as <inline-formula id="IEq6"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\frac {TP}{TP+FP}$\end{document}</tex-math><mml:math id="M12"><mml:mfrac><mml:mrow><mml:mtext mathvariant="italic">TP</mml:mtext></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">TP</mml:mtext><mml:mo>+</mml:mo><mml:mtext mathvariant="italic">FP</mml:mtext></mml:mrow></mml:mfrac></mml:math><inline-graphic xlink:href="40168_2018_532_Article_IEq6.gif"/></alternatives></inline-formula>, and recall (also named sensitivity or true positive rate, TPR), accounting for how many correct annotations are selected and defined as <inline-formula id="IEq7"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$\frac {TP}{TP+FN}$\end{document}</tex-math><mml:math id="M14"><mml:mfrac><mml:mrow><mml:mtext mathvariant="italic">TP</mml:mtext></mml:mrow><mml:mrow><mml:mtext mathvariant="italic">TP</mml:mtext><mml:mo>+</mml:mo><mml:mtext mathvariant="italic">FN</mml:mtext></mml:mrow></mml:mfrac></mml:math><inline-graphic xlink:href="40168_2018_532_Article_IEq7.gif"/></alternatives></inline-formula>, where <italic>TP</italic> indicates the number of domains that have been correctly annotated, <italic>FN</italic> indicates the number of domains which are in the gold standard but were not found by the tool and <italic>FP</italic> indicates the number of domains that have been wrongly annotated (because they do not appear in the gold standard). The F-score is the harmonic mean of precision and recall, defined as <inline-formula id="IEq8"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$2\cdot \frac {\text {precision} \: \cdot \: \text {recall}}{\text {precision} \: + \: \text {recall}}$\end{document}</tex-math><mml:math id="M16"><mml:mn>2</mml:mn><mml:mo>&#x000b7;</mml:mo><mml:mfrac><mml:mrow><mml:mtext>precision</mml:mtext><mml:mspace width="2.22144pt"/><mml:mo>&#x000b7;</mml:mo><mml:mspace width="2.22144pt"/><mml:mtext>recall</mml:mtext></mml:mrow><mml:mrow><mml:mtext>precision</mml:mtext><mml:mspace width="2.22144pt"/><mml:mo>+</mml:mo><mml:mspace width="2.22144pt"/><mml:mtext>recall</mml:mtext></mml:mrow></mml:mfrac></mml:math><inline-graphic xlink:href="40168_2018_532_Article_IEq8.gif"/></alternatives></inline-formula> (<inline-formula id="IEq9"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$= \frac {2TP}{2TP+FP+FN}$\end{document}</tex-math><mml:math id="M18"><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mn>2</mml:mn><mml:mtext mathvariant="italic">TP</mml:mtext></mml:mrow><mml:mrow><mml:mn>2</mml:mn><mml:mtext mathvariant="italic">TP</mml:mtext><mml:mo>+</mml:mo><mml:mtext mathvariant="italic">FP</mml:mtext><mml:mo>+</mml:mo><mml:mtext mathvariant="italic">FN</mml:mtext></mml:mrow></mml:mfrac></mml:math><inline-graphic xlink:href="40168_2018_532_Article_IEq9.gif"/></alternatives></inline-formula>).</p><p>In order to plot the precision-recall curves (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S16), we consider those subsets of reads that are annotated by the tool at a fixed threshold. The curve is constructed by varying a threshold that in MetaCLADE and MetaCLADE+UProC runs over <italic>E</italic> values and in UProC runs over UProC scores.</p><sec id="Sec37"><title>MetaCLADE software</title><p>The pipeline is implemented in Python 2.7 and is available at <ext-link ext-link-type="uri" xlink:href="http://www.lcqb.upmc.fr/metaclade">http://www.lcqb.upmc.fr/metaclade</ext-link> or at <ext-link ext-link-type="uri" xlink:href="https://sourcesup.renater.fr/projects/metaclade/">https://sourcesup.renater.fr/projects/metaclade/</ext-link> under the CeCILL Free Software Licence. This includes the annotation tool (MetaCLADE two main steps in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>) and the program pre-computing domain-specific gathering thresholds (MetaCLADE pre-computed step in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>). The CLADE model library used in MetaCLADE was constructed based on Pfam database v27 and was released with CLADE [<xref ref-type="bibr" rid="CR43">43</xref>]. It can be found at <ext-link ext-link-type="uri" xlink:href="http://www.lcqb.upmc.fr/CLADE">http://www.lcqb.upmc.fr/CLADE</ext-link>.</p></sec></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec38"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="40168_2018_532_MOESM1_ESM.pdf"><label>Additional file 1</label><caption><p>Additional Figures and Additional Tables (PDF 3796 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="40168_2018_532_MOESM2_ESM.xls"><label>Additional file 2</label><caption><p>Normalised abundance for all GO-Slim terms/data used to build figure 2A (XLS 74 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="40168_2018_532_MOESM3_ESM.xls"><label>Additional file 3</label><caption><p>Total number of nodes in the tree-graph rooted on a specific GO-term (XLS 35 kb)</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="40168_2018_532_MOESM4_ESM.xls"><label>Additional file 4</label><caption><p>Percentage of domain hits wrt all domain annotations (% tot) and wrt bacterial CCM annotations (% bacteria) (XLS 127 kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><fn-group><fn><p><bold>Availability of data and materials</bold></p><p>All datasets generated and analysed during the current study are publicly available, and their address is provided in our Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>. All generated datasets are available at <ext-link ext-link-type="uri" xlink:href="http://www.lcqb.upmc.fr/metaclade">http://www.lcqb.upmc.fr/metaclade</ext-link>. Data from the study in [<xref ref-type="bibr" rid="CR57">57</xref>] are available from the corresponding author on reasonable request. MetaCLADE package is available at the addresses <ext-link ext-link-type="uri" xlink:href="http://www.lcqb.upmc.fr/metaclade">http://www.lcqb.upmc.fr/metaclade</ext-link>
and <ext-link ext-link-type="uri" xlink:href="http://sourcesup.renater.fr/projects/metaclade/">http://sourcesup.renater.fr/projects/metaclade/</ext-link>.</p></fn><fn><p>Ari Ugarte and Riccardo Vicedomini contributed equally to this work.</p></fn></fn-group><ack><title>Acknowledgements</title><p>The authors acknowledge Thomas Mock (University of East Anglia, UK) for providing us the MT samples from [<xref ref-type="bibr" rid="CR57">57</xref>] and Elodie Laine for a careful reading of the manuscript.</p><sec id="d29e5155"><title>Funding</title><p>The study was funded by LabEx CALSIMLAB (public grant ANR-11-LABX-0037-01 constituting a part of the &#x0201c;Investissements d&#x02019;Avenir&#x0201d; program&#x02014;reference ANR-11-IDEX-0004-02) (AU, RV), the French Ministry of Research and Higher Education for a teaching assistantship (AU), the Institut Universitaire de France (AC), access to the HPC resources of the Institute for Scientific Computing and Simulation (Equip@Meso project&#x02014;ANR-10-EQPX-29-01, Excellence Program &#x0201c;Investissement d&#x02019;Avenir&#x0201d;).</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>AU and AC conceived and designed the experiments. AU and RV performed the experiments. AU, RV and AC analysed the data. AU, JB and AC contributed the analysis tools. AC and AU wrote the paper. All authors read and approved the final manuscript.</p></notes><notes notes-type="COI-statement"><sec><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec><sec><title>Consent for publication</title><p>Not applicable.</p></sec><sec><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec><title>Publisher&#x02019;s Note</title><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jackson</surname><given-names>CR</given-names></name></person-group><article-title>Changes in community properties during microbial succession</article-title><source>Oikos</source><year>2003</year><volume>101</volume><issue>2</issue><fpage>444</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1034/j.1600-0706.2003.12254.x</pub-id></element-citation></ref><ref id="CR2"><label>2</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tyson</surname><given-names>GW</given-names></name><name><surname>Chapman</surname><given-names>J</given-names></name><name><surname>Hugenholtz</surname><given-names>P</given-names></name><name><surname>Allen</surname><given-names>EE</given-names></name><name><surname>Ram</surname><given-names>RJ</given-names></name><name><surname>Richardson</surname><given-names>PM</given-names></name><name><surname>Solovyev</surname><given-names>VV</given-names></name><name><surname>Rubin</surname><given-names>EM</given-names></name><name><surname>Rokhsar</surname><given-names>DS</given-names></name><name><surname>Banfield</surname><given-names>JF</given-names></name></person-group><article-title>Community structure and metabolism through reconstruction of microbial genomes from the environment</article-title><source>Nature</source><year>2004</year><volume>428</volume><issue>6978</issue><fpage>37</fpage><lpage>43</lpage><pub-id pub-id-type="doi">10.1038/nature02340</pub-id><?supplied-pmid 14961025?><pub-id pub-id-type="pmid">14961025</pub-id></element-citation></ref><ref id="CR3"><label>3</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Freilich</surname><given-names>S</given-names></name><name><surname>Zarecki</surname><given-names>R</given-names></name><name><surname>Eilam</surname><given-names>O</given-names></name><name><surname>Segal</surname><given-names>ES</given-names></name><name><surname>Henry</surname><given-names>CS</given-names></name><name><surname>Kupiec</surname><given-names>M</given-names></name><name><surname>Gophna</surname><given-names>U</given-names></name><name><surname>Sharan</surname><given-names>R</given-names></name><name><surname>Ruppin</surname><given-names>E</given-names></name></person-group><article-title>Competitive and cooperative metabolic interactions in bacterial communities</article-title><source>Nat Commun</source><year>2011</year><volume>2</volume><fpage>589</fpage><pub-id pub-id-type="doi">10.1038/ncomms1597</pub-id><?supplied-pmid 22158444?><pub-id pub-id-type="pmid">22158444</pub-id></element-citation></ref><ref id="CR4"><label>4</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Johnson</surname><given-names>DR</given-names></name><name><surname>Goldschmidt</surname><given-names>F</given-names></name><name><surname>Lilja</surname><given-names>EE</given-names></name><name><surname>Ackermann</surname><given-names>M</given-names></name></person-group><article-title>Metabolic specialization and the assembly of microbial communities</article-title><source>ISME J</source><year>2012</year><volume>6</volume><issue>11</issue><fpage>1985</fpage><lpage>91</lpage><pub-id pub-id-type="doi">10.1038/ismej.2012.46</pub-id><?supplied-pmid 22592822?><pub-id pub-id-type="pmid">22592822</pub-id></element-citation></ref><ref id="CR5"><label>5</label><mixed-citation publication-type="other">Thompson JN. The geographic mosaic of coevolution.University of Chicago Press; 2005.</mixed-citation></ref><ref id="CR6"><label>6</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Whitham</surname><given-names>TG</given-names></name><name><surname>Bailey</surname><given-names>JK</given-names></name><name><surname>Schweitzer</surname><given-names>JA</given-names></name><name><surname>Shuster</surname><given-names>SM</given-names></name><name><surname>Bangert</surname><given-names>RK</given-names></name><name><surname>Le Roy</surname><given-names>CJ</given-names></name><name><surname>Lonsdorf</surname><given-names>EV</given-names></name><name><surname>Allan</surname><given-names>GJ</given-names></name><name><surname>Di Fazio</surname><given-names>SP</given-names></name><name><surname>Potts</surname><given-names>BM</given-names></name><etal/></person-group><article-title>A framework for community and ecosystem genetics: from genes to ecosystems</article-title><source>Nat Rev G enet</source><year>2006</year><volume>7</volume><issue>7</issue><fpage>510</fpage><lpage>23</lpage><pub-id pub-id-type="doi">10.1038/nrg1877</pub-id></element-citation></ref><ref id="CR7"><label>7</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chisholm</surname><given-names>RA</given-names></name><name><surname>Pacala</surname><given-names>SW</given-names></name></person-group><article-title>Theory predicts a rapid transition from niche-structured to neutral biodiversity patterns across a speciation-rate gradient</article-title><source>Theor Ecol</source><year>2011</year><volume>4</volume><issue>2</issue><fpage>195</fpage><lpage>200</lpage><pub-id pub-id-type="doi">10.1007/s12080-011-0113-5</pub-id></element-citation></ref><ref id="CR8"><label>8</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dini-Andreote</surname><given-names>F</given-names></name><name><surname>Stegen</surname><given-names>JC</given-names></name><name><surname>van Elsas</surname><given-names>JD</given-names></name><name><surname>Salles</surname><given-names>JF</given-names></name></person-group><article-title>Disentangling mechanisms that mediate the balance between stochastic and deterministic processes in microbial succession</article-title><source>Proc Natl Acad Sci</source><year>2015</year><volume>112</volume><issue>11</issue><fpage>1326</fpage><lpage>32</lpage><pub-id pub-id-type="doi">10.1073/pnas.1414261112</pub-id></element-citation></ref><ref id="CR9"><label>9</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hand</surname><given-names>BK</given-names></name><name><surname>Lowe</surname><given-names>WH</given-names></name><name><surname>Kovach</surname><given-names>RP</given-names></name><name><surname>Muhlfeld</surname><given-names>CC</given-names></name><name><surname>Luikart</surname><given-names>G</given-names></name></person-group><article-title>Landscape community genomics: understanding eco-evolutionary processes in complex environments</article-title><source>Trends Ecol Evol</source><year>2015</year><volume>30</volume><issue>3</issue><fpage>161</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1016/j.tree.2015.01.005</pub-id><?supplied-pmid 25650350?><pub-id pub-id-type="pmid">25650350</pub-id></element-citation></ref><ref id="CR10"><label>10</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Handelsman</surname><given-names>J</given-names></name></person-group><article-title>Metagenomics: application of genomics to uncultured microorganisms</article-title><source>Microbiol Mol Biol Rev</source><year>2004</year><volume>68</volume><issue>4</issue><fpage>669</fpage><lpage>85</lpage><pub-id pub-id-type="doi">10.1128/MMBR.68.4.669-685.2004</pub-id><?supplied-pmid 15590779?><pub-id pub-id-type="pmid">15590779</pub-id></element-citation></ref><ref id="CR11"><label>11</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Allen</surname><given-names>EE</given-names></name><name><surname>Banfield</surname><given-names>JF</given-names></name></person-group><article-title>Community genomics in microbial ecology and evolution</article-title><source>Nat Rev Microbiol</source><year>2005</year><volume>3</volume><issue>6</issue><fpage>489</fpage><lpage>98</lpage><pub-id pub-id-type="doi">10.1038/nrmicro1157</pub-id><?supplied-pmid 15931167?><pub-id pub-id-type="pmid">15931167</pub-id></element-citation></ref><ref id="CR12"><label>12</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tyson</surname><given-names>GW</given-names></name><name><surname>Banfield</surname><given-names>JF</given-names></name></person-group><article-title>Cultivating the uncultivated: a community genomics perspective</article-title><source>Trends Microbiol</source><year>2005</year><volume>13</volume><issue>9</issue><fpage>411</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1016/j.tim.2005.07.003</pub-id><?supplied-pmid 16043355?><pub-id pub-id-type="pmid">16043355</pub-id></element-citation></ref><ref id="CR13"><label>13</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>DeLong</surname><given-names>EF</given-names></name><name><surname>Preston</surname><given-names>CM</given-names></name><name><surname>Mincer</surname><given-names>T</given-names></name><name><surname>Rich</surname><given-names>V</given-names></name><name><surname>Hallam</surname><given-names>SJ</given-names></name><name><surname>Frigaard</surname><given-names>NU</given-names></name><name><surname>Martinez</surname><given-names>A</given-names></name><name><surname>Sullivan</surname><given-names>MB</given-names></name><name><surname>Edwards</surname><given-names>R</given-names></name><name><surname>Brito</surname><given-names>BR</given-names></name><etal/></person-group><article-title>Community genomics among stratified microbial assemblages in the ocean&#x02019;s interior</article-title><source>Science</source><year>2006</year><volume>311</volume><issue>5760</issue><fpage>496</fpage><lpage>503</lpage><pub-id pub-id-type="doi">10.1126/science.1120250</pub-id><?supplied-pmid 16439655?><pub-id pub-id-type="pmid">16439655</pub-id></element-citation></ref><ref id="CR14"><label>14</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eisen</surname><given-names>JA</given-names></name></person-group><article-title>Environmental shotgun sequencing: its potential and challenges for studying the hidden world of microbes</article-title><source>PLoS Biol</source><year>2007</year><volume>5</volume><issue>3</issue><fpage>82</fpage><pub-id pub-id-type="doi">10.1371/journal.pbio.0050082</pub-id></element-citation></ref><ref id="CR15"><label>15</label><mixed-citation publication-type="other">Van Straalen NM, Roelofs D. An introduction to ecological genomics: Oxford University Press; 2012.</mixed-citation></ref><ref id="CR16"><label>16</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ye</surname><given-names>Y</given-names></name><name><surname>Doak</surname><given-names>TG</given-names></name></person-group><article-title>A parsimony approach to biological pathway reconstruction/inference for genomes and metagenomes</article-title><source>PLoS Comput Bio</source><year>2009</year><volume>5</volume><issue>8</issue><fpage>1000465</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1000465</pub-id></element-citation></ref><ref id="CR17"><label>17</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kristiansson</surname><given-names>E</given-names></name><name><surname>Hugenholtz</surname><given-names>P</given-names></name><name><surname>Dalevi</surname><given-names>D</given-names></name></person-group><article-title>Shotgunfunctionalizer: an R-package for functional comparison of metagenomes</article-title><source>Bioinformatics</source><year>2009</year><volume>25</volume><issue>20</issue><fpage>2737</fpage><lpage>738</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btp508</pub-id><?supplied-pmid 19696045?><pub-id pub-id-type="pmid">19696045</pub-id></element-citation></ref><ref id="CR18"><label>18</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sun</surname><given-names>S</given-names></name><name><surname>Chen</surname><given-names>J</given-names></name><name><surname>Li</surname><given-names>W</given-names></name><name><surname>Altintas</surname><given-names>I</given-names></name><name><surname>Lin</surname><given-names>A</given-names></name><name><surname>Peltier</surname><given-names>S</given-names></name><name><surname>Stocks</surname><given-names>K</given-names></name><name><surname>Allen</surname><given-names>EE</given-names></name><name><surname>Ellisman</surname><given-names>M</given-names></name><name><surname>Grethe</surname><given-names>J</given-names></name><etal/></person-group><article-title>Community cyberinfrastructure for advanced microbial ecology research and analysis: the camera resource</article-title><source>Nucleic Acids Res</source><year>2010</year><volume>39</volume><issue>suppl_1</issue><fpage>546</fpage><lpage>51</lpage></element-citation></ref><ref id="CR19"><label>19</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lingner</surname><given-names>T</given-names></name><name><surname>A&#x000df;hauer</surname><given-names>KP</given-names></name><name><surname>Schreiber</surname><given-names>F</given-names></name><name><surname>Meinicke</surname><given-names>P</given-names></name></person-group><article-title>Comet &#x02013; a web server for comparative functional profiling of metagenomes</article-title><source>Nucleic Acids Res</source><year>2011</year><volume>39</volume><issue>suppl_2</issue><fpage>518</fpage><lpage>23</lpage><pub-id pub-id-type="doi">10.1093/nar/gkr388</pub-id></element-citation></ref><ref id="CR20"><label>20</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Markowitz</surname><given-names>VM</given-names></name><name><surname>Chen</surname><given-names>I-MA</given-names></name><name><surname>Chu</surname><given-names>K</given-names></name><name><surname>Szeto</surname><given-names>E</given-names></name><name><surname>Palaniappan</surname><given-names>K</given-names></name><name><surname>Grechkin</surname><given-names>Y</given-names></name><name><surname>Ratner</surname><given-names>A</given-names></name><name><surname>Jacob</surname><given-names>B</given-names></name><name><surname>Pati</surname><given-names>A</given-names></name><name><surname>Huntemann</surname><given-names>M</given-names></name><etal/></person-group><article-title>IMG/M: the integrated metagenome data management and comparative analysis system</article-title><source>Nucleic Acids Res</source><year>2011</year><volume>40</volume><issue>D1</issue><fpage>123</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1093/nar/gkr975</pub-id></element-citation></ref><ref id="CR21"><label>21</label><mixed-citation publication-type="other">Chen I-MA, Markowitz VM, Chu K, Palaniappan K, Szeto E, Pillay M, Ratner A, Huang J, Andersen E, Huntemann M, et al.IMG/M: integrated genome and metagenome comparative data analysis system. Nucleic Acids Res. 2016:929.</mixed-citation></ref><ref id="CR22"><label>22</label><mixed-citation publication-type="other">Liu B, Pop M. Metapath: identifying differentially abundant metabolic pathways in metagenomic datasets. In: BMC Proceedings, vol 5. BioMed Central: 2011. p. 9.</mixed-citation></ref><ref id="CR23"><label>23</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Langille</surname><given-names>MG</given-names></name><name><surname>Zaneveld</surname><given-names>J</given-names></name><name><surname>Caporaso</surname><given-names>JG</given-names></name><name><surname>McDonald</surname><given-names>D</given-names></name><name><surname>Knights</surname><given-names>D</given-names></name><name><surname>Reyes</surname><given-names>JA</given-names></name><name><surname>Clemente</surname><given-names>JC</given-names></name><name><surname>Burkepile</surname><given-names>DE</given-names></name><name><surname>Thurber</surname><given-names>RLV</given-names></name><name><surname>Knight</surname><given-names>R</given-names></name><etal/></person-group><article-title>Predictive functional profiling of microbial communities using 16S rRNA marker gene sequences</article-title><source>Nat Biotechnol</source><year>2013</year><volume>31</volume><issue>9</issue><fpage>814</fpage><pub-id pub-id-type="doi">10.1038/nbt.2676</pub-id><?supplied-pmid 23975157?><pub-id pub-id-type="pmid">23975157</pub-id></element-citation></ref><ref id="CR24"><label>24</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Davenport</surname><given-names>CF</given-names></name><name><surname>T&#x000fc;mmler</surname><given-names>B</given-names></name></person-group><article-title>Advances in computational analysis of metagenome sequences</article-title><source>Environ Microbiol</source><year>2013</year><volume>15</volume><issue>1</issue><fpage>1</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1111/j.1462-2920.2012.02843.x</pub-id><?supplied-pmid 22882611?><pub-id pub-id-type="pmid">22882611</pub-id></element-citation></ref><ref id="CR25"><label>25</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Konwar</surname><given-names>KM</given-names></name><name><surname>Hanson</surname><given-names>NW</given-names></name><name><surname>Pag&#x000e9;</surname><given-names>AP</given-names></name><name><surname>Hallam</surname><given-names>SJ</given-names></name></person-group><article-title>Metapathways: a modular pipeline for constructing pathway/genome databases from environmental sequence information</article-title><source>BMC Bioinforma</source><year>2013</year><volume>14</volume><issue>1</issue><fpage>202</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-14-202</pub-id></element-citation></ref><ref id="CR26"><label>26</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bose</surname><given-names>T</given-names></name><name><surname>Haque</surname><given-names>MM</given-names></name><name><surname>Reddy</surname><given-names>C</given-names></name><name><surname>Mande</surname><given-names>SS</given-names></name></person-group><article-title>COGNIZER: a framework for functional annotation of metagenomic datasets</article-title><source>PLoS ONE</source><year>2015</year><volume>10</volume><issue>11</issue><fpage>0142102</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0142102</pub-id></element-citation></ref><ref id="CR27"><label>27</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wilke</surname><given-names>A</given-names></name><name><surname>Bischof</surname><given-names>J</given-names></name><name><surname>Gerlach</surname><given-names>W</given-names></name><name><surname>Glass</surname><given-names>E</given-names></name><name><surname>Harrison</surname><given-names>T</given-names></name><name><surname>Keegan</surname><given-names>KP</given-names></name><name><surname>Paczian</surname><given-names>T</given-names></name><name><surname>Trimble</surname><given-names>WL</given-names></name><name><surname>Bagchi</surname><given-names>S</given-names></name><name><surname>Grama</surname><given-names>A</given-names></name><etal/></person-group><article-title>The MG-RAST metagenomics database and portal in 2015</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>44</volume><issue>D1</issue><fpage>590</fpage><lpage>4</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1322</pub-id></element-citation></ref><ref id="CR28"><label>28</label><mixed-citation publication-type="other">Keegan KP, Glass EM, Meyer F. MG-RAST, a metagenomics service for analysis of microbial community structure and function. In: Microbial Environmental Genomics (MEG). Springer: 2016. p. 207&#x02013;33.</mixed-citation></ref><ref id="CR29"><label>29</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huson</surname><given-names>DH</given-names></name><name><surname>Beier</surname><given-names>S</given-names></name><name><surname>Flade</surname><given-names>I</given-names></name><name><surname>G&#x000f3;rska</surname><given-names>A</given-names></name><name><surname>El-Hadidi</surname><given-names>M</given-names></name><name><surname>Mitra</surname><given-names>S</given-names></name><name><surname>Ruscheweyh</surname><given-names>HJ</given-names></name><name><surname>Tappu</surname><given-names>R</given-names></name></person-group><article-title>MEGAN community edition-interactive exploration and analysis of large-scale microbiome sequencing data</article-title><source>PLoS Comput Biol</source><year>2016</year><volume>12</volume><issue>6</issue><fpage>1004957</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1004957</pub-id></element-citation></ref><ref id="CR30"><label>30</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kultima</surname><given-names>JR</given-names></name><name><surname>Coelho</surname><given-names>LP</given-names></name><name><surname>Forslund</surname><given-names>K</given-names></name><name><surname>Huerta-Cepas</surname><given-names>J</given-names></name><name><surname>Li</surname><given-names>SS</given-names></name><name><surname>Driessen</surname><given-names>M</given-names></name><name><surname>Voigt</surname><given-names>AY</given-names></name><name><surname>Zeller</surname><given-names>G</given-names></name><name><surname>Sunagawa</surname><given-names>S</given-names></name><name><surname>Bork</surname><given-names>P</given-names></name></person-group><article-title>MOCAT2: a metagenomic assembly, annotation and profiling framework</article-title><source>Bioinformatics</source><year>2016</year><volume>32</volume><issue>16</issue><fpage>2520</fpage><lpage>523</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btw183</pub-id><?supplied-pmid 27153620?><pub-id pub-id-type="pmid">27153620</pub-id></element-citation></ref><ref id="CR31"><label>31</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Escobar-Zepeda</surname><given-names>A</given-names></name><name><surname>Vera-Ponce de Le&#x000f3;n</surname><given-names>A</given-names></name><name><surname>Sanchez-Flores</surname><given-names>A</given-names></name></person-group><article-title>The road to metagenomics: from microbiology to dna sequencing technologies and bioinformatics</article-title><source>Front Genet</source><year>2015</year><volume>6</volume><fpage>348</fpage><pub-id pub-id-type="doi">10.3389/fgene.2015.00348</pub-id><?supplied-pmid 26734060?><pub-id pub-id-type="pmid">26734060</pub-id></element-citation></ref><ref id="CR32"><label>32</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Prakash</surname><given-names>T</given-names></name><name><surname>Taylor</surname><given-names>TD</given-names></name></person-group><article-title>Functional assignment of metagenomic data: challenges and applications</article-title><source>Brief Bioinform</source><year>2012</year><volume>13</volume><issue>6</issue><fpage>711</fpage><lpage>27</lpage><pub-id pub-id-type="doi">10.1093/bib/bbs033</pub-id><?supplied-pmid 22772835?><pub-id pub-id-type="pmid">22772835</pub-id></element-citation></ref><ref id="CR33"><label>33</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>De Filippo</surname><given-names>C</given-names></name><name><surname>Ramazzotti</surname><given-names>M</given-names></name><name><surname>Fontana</surname><given-names>P</given-names></name><name><surname>Cavalieri</surname><given-names>D</given-names></name></person-group><article-title>Bioinformatic approaches for functional annotation and pathway inference in metagenomics data</article-title><source>Brief Bioinform</source><year>2012</year><volume>13</volume><issue>6</issue><fpage>696</fpage><lpage>710</lpage><pub-id pub-id-type="doi">10.1093/bib/bbs070</pub-id><?supplied-pmid 23175748?><pub-id pub-id-type="pmid">23175748</pub-id></element-citation></ref><ref id="CR34"><label>34</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Raes</surname><given-names>J</given-names></name><name><surname>Foerstner</surname><given-names>KU</given-names></name><name><surname>Bork</surname><given-names>P</given-names></name></person-group><article-title>Get the most out of your metagenome: computational analysis of environmental sequence data</article-title><source>Curr Opin Microbiol</source><year>2007</year><volume>10</volume><issue>5</issue><fpage>490</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1016/j.mib.2007.09.001</pub-id><?supplied-pmid 17936679?><pub-id pub-id-type="pmid">17936679</pub-id></element-citation></ref><ref id="CR35"><label>35</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Q</given-names></name><name><surname>Fish</surname><given-names>JA</given-names></name><name><surname>Gilman</surname><given-names>M</given-names></name><name><surname>Sun</surname><given-names>Y</given-names></name><name><surname>Brown</surname><given-names>CT</given-names></name><name><surname>Tiedje</surname><given-names>JM</given-names></name><name><surname>Cole</surname><given-names>JR</given-names></name></person-group><article-title>Xander: employing a novel method for efficient gene-targeted metagenomic assembly</article-title><source>Microbiome</source><year>2015</year><volume>3</volume><issue>1</issue><fpage>32</fpage><pub-id pub-id-type="doi">10.1186/s40168-015-0093-6</pub-id><?supplied-pmid 26246894?><pub-id pub-id-type="pmid">26246894</pub-id></element-citation></ref><ref id="CR36"><label>36</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huson</surname><given-names>DH</given-names></name><name><surname>Tappu</surname><given-names>R</given-names></name><name><surname>Bazinet</surname><given-names>AL</given-names></name><name><surname>Xie</surname><given-names>C</given-names></name><name><surname>Cummings</surname><given-names>MP</given-names></name><name><surname>Nieselt</surname><given-names>K</given-names></name><name><surname>Williams</surname><given-names>R</given-names></name></person-group><article-title>Fast and simple protein-alignment-guided assembly of orthologous gene families from microbiome sequencing reads</article-title><source>Microbiome</source><year>2017</year><volume>5</volume><issue>1</issue><fpage>11</fpage><pub-id pub-id-type="doi">10.1186/s40168-017-0233-2</pub-id><?supplied-pmid 28122610?><pub-id pub-id-type="pmid">28122610</pub-id></element-citation></ref><ref id="CR37"><label>37</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhong</surname><given-names>C</given-names></name><name><surname>Edlund</surname><given-names>A</given-names></name><name><surname>Yang</surname><given-names>Y</given-names></name><name><surname>McLean</surname><given-names>JS</given-names></name><name><surname>Yooseph</surname><given-names>S</given-names></name></person-group><article-title>Metagenome and metatranscriptome analyses using protein family profiles</article-title><source>PLoS Comput Biol</source><year>2016</year><volume>12</volume><issue>7</issue><fpage>1004991</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1004991</pub-id></element-citation></ref><ref id="CR38"><label>38</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Meinicke</surname><given-names>P</given-names></name></person-group><article-title>UProC: tools for ultra-fast protein domain classification</article-title><source>Bioinformatics</source><year>2015</year><volume>31</volume><issue>9</issue><fpage>1382</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btu843</pub-id><?supplied-pmid 25540185?><pub-id pub-id-type="pmid">25540185</pub-id></element-citation></ref><ref id="CR39"><label>39</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Richardson</surname><given-names>JS</given-names></name></person-group><article-title>The anatomy and taxonomy of protein structure</article-title><source>Adv Protein Chem</source><year>1981</year><volume>34</volume><fpage>167</fpage><lpage>339</lpage><pub-id pub-id-type="doi">10.1016/S0065-3233(08)60520-3</pub-id><?supplied-pmid 7020376?><pub-id pub-id-type="pmid">7020376</pub-id></element-citation></ref><ref id="CR40"><label>40</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Janin</surname><given-names>J</given-names></name><name><surname>Wodak</surname><given-names>SJ</given-names></name></person-group><article-title>Structural domains in proteins and their role in the dynamics of protein function</article-title><source>Prog Biophys Molec Biol</source><year>1983</year><volume>42</volume><fpage>21</fpage><lpage>78</lpage><pub-id pub-id-type="doi">10.1016/0079-6107(83)90003-2</pub-id><pub-id pub-id-type="pmid">6353481</pub-id></element-citation></ref><ref id="CR41"><label>41</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>D</given-names></name><name><surname>Nussinov</surname><given-names>R</given-names></name></person-group><article-title>Favorable domain size in proteins</article-title><source>Structure</source><year>1998</year><volume>3</volume><issue>1</issue><fpage>11</fpage><lpage>17</lpage></element-citation></ref><ref id="CR42"><label>42</label><mixed-citation publication-type="other">Zhang Y, Sun Y, Cole JR. A Sensitive and Accurate protein domain cLassification Tool (SALT) for short reads. Bioinformatics. 2013;357.</mixed-citation></ref><ref id="CR43"><label>43</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bernardes</surname><given-names>J</given-names></name><name><surname>Zaverucha</surname><given-names>G</given-names></name><name><surname>Vaquero</surname><given-names>C</given-names></name><name><surname>Carbone</surname><given-names>A</given-names></name></person-group><article-title>Improvement in protein domain identification is reached by breaking consensus, with the agreement of many profiles and domain co-occurrence</article-title><source>PLoS Comput Biol</source><year>2016</year><volume>12</volume><issue>7</issue><fpage>1005038</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1005038</pub-id></element-citation></ref><ref id="CR44"><label>44</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gribskov</surname><given-names>M</given-names></name><name><surname>McLachlan</surname><given-names>AD</given-names></name><name><surname>Eisenberg</surname><given-names>D</given-names></name></person-group><article-title>Profile analysis: detection of distantly related proteins</article-title><source>Proc Natl Acad Sci</source><year>1987</year><volume>84</volume><issue>13</issue><fpage>4355</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1073/pnas.84.13.4355</pub-id><?supplied-pmid 3474607?><pub-id pub-id-type="pmid">3474607</pub-id></element-citation></ref><ref id="CR45"><label>45</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Altschul</surname><given-names>SF</given-names></name><name><surname>Madden</surname><given-names>TL</given-names></name><name><surname>Sch&#x000e4;ffer</surname><given-names>AA</given-names></name><name><surname>Zhang</surname><given-names>J</given-names></name><name><surname>Zhang</surname><given-names>Z</given-names></name><name><surname>Miller</surname><given-names>W</given-names></name><name><surname>Lipman</surname><given-names>DJ</given-names></name></person-group><article-title>Gapped BLAST and PSI-BLAST: a new generation of protein database search programs</article-title><source>Nucleic Acids Res</source><year>1997</year><volume>25</volume><issue>17</issue><fpage>3389</fpage><lpage>402</lpage><pub-id pub-id-type="doi">10.1093/nar/25.17.3389</pub-id><?supplied-pmid 9254694?><pub-id pub-id-type="pmid">9254694</pub-id></element-citation></ref><ref id="CR46"><label>46</label><mixed-citation publication-type="other">Durbin R, Eddy SR, Krogh A, Mitchison G. Biological sequence analysis: probabilistic models of proteins and nucleic acids.Cambridge University Press; 1998.</mixed-citation></ref><ref id="CR47"><label>47</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eddy</surname><given-names>SR</given-names></name></person-group><article-title>Accelerated profile HMM searches</article-title><source>PLoS Comput Biol</source><year>2011</year><volume>7</volume><fpage>1002195</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1002195</pub-id></element-citation></ref><ref id="CR48"><label>48</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Soeding</surname><given-names>J</given-names></name></person-group><article-title>Protein homology detection by HMM-HMM comparison</article-title><source>Bioinformatics</source><year>2005</year><volume>21</volume><fpage>951</fpage><lpage>60</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/bti125</pub-id><pub-id pub-id-type="pmid">15531603</pub-id></element-citation></ref><ref id="CR49"><label>49</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Remmert</surname><given-names>M</given-names></name><name><surname>Biegert</surname><given-names>A</given-names></name><name><surname>Hauser</surname><given-names>A</given-names></name><name><surname>Soeding</surname><given-names>J</given-names></name></person-group><article-title>HHblits: lightning-fast iterative protein sequence searching by HMM-HMM alignment</article-title><source>Nat Methods</source><year>2011</year><volume>9</volume><fpage>173</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1038/nmeth.1818</pub-id><?supplied-pmid 22198341?><pub-id pub-id-type="pmid">22198341</pub-id></element-citation></ref><ref id="CR50"><label>50</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bernardes</surname><given-names>J</given-names></name><name><surname>Vaquero</surname><given-names>C</given-names></name><name><surname>Carbone</surname><given-names>A</given-names></name></person-group><article-title>Plasmobase: a comparative database of predicted domain architectures for <italic>Plasmodium</italic> genomes</article-title><source>Malar J</source><year>2017</year><volume>16</volume><issue>1</issue><fpage>241</fpage><pub-id pub-id-type="doi">10.1186/s12936-017-1887-8</pub-id><?supplied-pmid 28592293?><pub-id pub-id-type="pmid">28592293</pub-id></element-citation></ref><ref id="CR51"><label>51</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zdobnov</surname><given-names>EM</given-names></name><name><surname>Apweiler</surname><given-names>R</given-names></name></person-group><article-title>Interproscan&#x02013;an integration platform for the signature-recognition methods in interpro</article-title><source>Bioinformatics</source><year>2001</year><volume>17</volume><issue>9</issue><fpage>847</fpage><lpage>8</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/17.9.847</pub-id><?supplied-pmid 11590104?><pub-id pub-id-type="pmid">11590104</pub-id></element-citation></ref><ref id="CR52"><label>52</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Quevillon</surname><given-names>E</given-names></name><name><surname>Silventoinen</surname><given-names>V</given-names></name><name><surname>Pillai</surname><given-names>S</given-names></name><name><surname>Harte</surname><given-names>N</given-names></name><name><surname>Mulder</surname><given-names>N</given-names></name><name><surname>Apweiler</surname><given-names>R</given-names></name><name><surname>Lopez</surname><given-names>R</given-names></name></person-group><article-title>Interproscan: protein domains identifier</article-title><source>Nucleic Acids Res</source><year>2005</year><volume>33</volume><issue>suppl_2</issue><fpage>116</fpage><lpage>20</lpage><pub-id pub-id-type="doi">10.1093/nar/gki442</pub-id></element-citation></ref><ref id="CR53"><label>53</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Richter</surname><given-names>DC</given-names></name><name><surname>Ott</surname><given-names>F</given-names></name><name><surname>Auch</surname><given-names>AF</given-names></name><name><surname>Schmid</surname><given-names>R</given-names></name><name><surname>Huson</surname><given-names>DH</given-names></name></person-group><article-title>MetaSim: a sequencing simulator for genomics and metagenomicsBuilt-in loops allow versatility in domain-domain interactions: lessons from self-interacting domains</article-title><source>PLoS ONE</source><year>2008</year><volume>3</volume><issue>10</issue><fpage>3373</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0003373</pub-id></element-citation></ref><ref id="CR54"><label>54</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Batzer</surname><given-names>S</given-names></name><etal/></person-group><article-title>Characteristics of 454 pyrosequencing data-enabling realistic simulation with FlowSim</article-title><source>Bioinformatics</source><year>2010</year><volume>26</volume><fpage>420</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btq365</pub-id></element-citation></ref><ref id="CR55"><label>55</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Finn</surname><given-names>RD</given-names></name><etal/></person-group><article-title>Pfam: clans, web tools and services</article-title><source>Nucleic Acids Res</source><year>2005</year><volume>34</volume><fpage>247</fpage><lpage>51</lpage><pub-id pub-id-type="doi">10.1093/nar/gkj149</pub-id></element-citation></ref><ref id="CR56"><label>56</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mitchell</surname><given-names>A</given-names></name><etal/></person-group><article-title>The InterPro protein families database: the classification resource after 15 years</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><fpage>213</fpage><lpage>21</lpage><pub-id pub-id-type="doi">10.1093/nar/gku1243</pub-id></element-citation></ref><ref id="CR57"><label>57</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Toseland</surname><given-names>A</given-names></name><name><surname>Daines</surname><given-names>SJ</given-names></name><name><surname>Clark</surname><given-names>JR</given-names></name><name><surname>Kirkham</surname><given-names>A</given-names></name><name><surname>Strauss</surname><given-names>J</given-names></name><name><surname>Uhlig</surname><given-names>C</given-names></name><name><surname>Lenton</surname><given-names>TM</given-names></name><name><surname>Valentin</surname><given-names>K</given-names></name><name><surname>Pearson</surname><given-names>GA</given-names></name><name><surname>Moulton</surname><given-names>V</given-names></name><name><surname>Mock</surname><given-names>T</given-names></name></person-group><article-title>The impact of temperature on marine phytoplankton resource allocation and metabolism</article-title><source>Nat Clim Chang</source><year>2013</year><volume>3</volume><fpage>979</fpage><lpage>84</lpage><pub-id pub-id-type="doi">10.1038/nclimate1989</pub-id></element-citation></ref><ref id="CR58"><label>58</label><mixed-citation publication-type="other">Sunda WG. Trace metal/phytoplankton interactions in the sea. In: Chemistry of aquatic systems: local and global perspectives. Springer: 1994. p. 213&#x02013;47.</mixed-citation></ref><ref id="CR59"><label>59</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tagliabue</surname><given-names>A</given-names></name><name><surname>Bowie</surname><given-names>AR</given-names></name><name><surname>Boyd</surname><given-names>PW</given-names></name><name><surname>Buck</surname><given-names>KN</given-names></name><name><surname>Johnson</surname><given-names>KS</given-names></name><name><surname>Saito</surname><given-names>MakA</given-names></name></person-group><article-title>The integral role of iron in ocean biogeochemistry</article-title><source>Nature</source><year>2017</year><volume>543</volume><fpage>51</fpage><pub-id pub-id-type="doi">10.1038/nature21058</pub-id><?supplied-pmid 28252066?><pub-id pub-id-type="pmid">28252066</pub-id></element-citation></ref><ref id="CR60"><label>60</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shi</surname><given-names>X</given-names></name><name><surname>Li</surname><given-names>L</given-names></name><name><surname>Guo</surname><given-names>C</given-names></name><name><surname>Lin</surname><given-names>X</given-names></name><name><surname>Li</surname><given-names>M</given-names></name><name><surname>Lin</surname><given-names>S</given-names></name></person-group><article-title>Rhodopsin gene expression regulated by the light dark cycle, light spectrum and light intensity in the dinoflagellate <italic>Prorocentrum</italic></article-title><source>Front Microbiol</source><year>2015</year><volume>6</volume><fpage>555</fpage><pub-id pub-id-type="doi">10.3389/fmicb.2015.00555</pub-id><?supplied-pmid 26082770?><pub-id pub-id-type="pmid">26082770</pub-id></element-citation></ref><ref id="CR61"><label>61</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Finn</surname><given-names>RD</given-names></name><name><surname>Bateman</surname><given-names>A</given-names></name><name><surname>Clements</surname><given-names>J</given-names></name><name><surname>Coggill</surname><given-names>P</given-names></name><name><surname>Eberhardt</surname><given-names>RY</given-names></name><name><surname>Eddy</surname><given-names>SR</given-names></name><name><surname>Heger</surname><given-names>A</given-names></name><name><surname>Hetherington</surname><given-names>K</given-names></name><name><surname>Holm</surname><given-names>L</given-names></name><name><surname>Mistry</surname><given-names>J</given-names></name><name><surname>Sonnhammer</surname><given-names>ELL</given-names></name><name><surname>Tate</surname><given-names>J</given-names></name><name><surname>Punta</surname><given-names>M</given-names></name></person-group><article-title>Pfam: the protein families database</article-title><source>Nucleic Acids Res</source><year>2014</year><volume>42</volume><fpage>222</fpage><lpage>30</lpage><pub-id pub-id-type="doi">10.1093/nar/gkt1223</pub-id></element-citation></ref><ref id="CR62"><label>62</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Punta</surname><given-names>M</given-names></name><name><surname>Coggill</surname><given-names>PC</given-names></name><name><surname>Eberhardt</surname><given-names>RY</given-names></name><name><surname>Mistry</surname><given-names>J</given-names></name><name><surname>Tate</surname><given-names>J</given-names></name><name><surname>Boursnell</surname><given-names>C</given-names></name><name><surname>Pang</surname><given-names>N</given-names></name><name><surname>Forslund</surname><given-names>K</given-names></name><name><surname>Ceric</surname><given-names>G</given-names></name><name><surname>Clements</surname><given-names>J</given-names></name><name><surname>Heger</surname><given-names>A</given-names></name><name><surname>Holm</surname><given-names>L</given-names></name><name><surname>Sonnhammer</surname><given-names>ALL</given-names></name><name><surname>Eddy</surname><given-names>SR</given-names></name><name><surname>Bateman</surname><given-names>A</given-names></name><name><surname>Finn</surname><given-names>RD</given-names></name></person-group><article-title>The Pfam protein families database</article-title><source>Nucleic Acids Res</source><year>2012</year><volume>40</volume><fpage>290</fpage><lpage>301</lpage><pub-id pub-id-type="doi">10.1093/nar/gkr1065</pub-id><pub-id pub-id-type="pmid">21896617</pub-id></element-citation></ref><ref id="CR63"><label>63</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lees</surname><given-names>JG</given-names></name><name><surname>Lee</surname><given-names>D</given-names></name><name><surname>Studer</surname><given-names>RA</given-names></name><name><surname>Dawson</surname><given-names>NL</given-names></name><name><surname>Sillitoe</surname><given-names>I</given-names></name><name><surname>Das</surname><given-names>S</given-names></name><name><surname>Yeats</surname><given-names>C</given-names></name><name><surname>Dessailly</surname><given-names>BH</given-names></name><name><surname>Rentzsch</surname><given-names>R</given-names></name><name><surname>Orengo</surname><given-names>CA</given-names></name></person-group><article-title>Gene3D: multi-domain annotations for protein sequence and comparative genome analysis</article-title><source>Nucleic Acids Res</source><year>2014</year><volume>42</volume><fpage>240</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1093/nar/gkt1205</pub-id></element-citation></ref><ref id="CR64"><label>64</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Haft</surname><given-names>DH</given-names></name><name><surname>Selengut</surname><given-names>JD</given-names></name><name><surname>Richter</surname><given-names>RA</given-names></name><name><surname>Harkins</surname><given-names>D</given-names></name><name><surname>Basu</surname><given-names>MK</given-names></name><name><surname>Beck</surname><given-names>E</given-names></name></person-group><article-title>TIGRFAMs and genome properties in 2013</article-title><source>Nucleic Acids Res</source><year>2013</year><volume>41</volume><fpage>387</fpage><lpage>95</lpage><pub-id pub-id-type="doi">10.1093/nar/gks1234</pub-id></element-citation></ref><ref id="CR65"><label>65</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Attwood</surname><given-names>TK</given-names></name><name><surname>Coletta</surname><given-names>A</given-names></name><name><surname>Muirhead</surname><given-names>G</given-names></name><name><surname>Pavlopoulou</surname><given-names>A</given-names></name><name><surname>Philippou</surname><given-names>PB</given-names></name><name><surname>Popov</surname><given-names>I</given-names></name><name><surname>Roma-Mateo</surname><given-names>C</given-names></name><name><surname>Theodosiou</surname><given-names>A</given-names></name><name><surname>Mitchell</surname><given-names>AL</given-names></name></person-group><article-title>The PRINTS database: a fine-grained protein sequence annotation and analysis resource - its status in 2012</article-title><source>Database</source><year>2012</year><volume>10</volume><fpage>019</fpage></element-citation></ref><ref id="CR66"><label>66</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sigrist</surname><given-names>CJA</given-names></name><name><surname>de Castro</surname><given-names>E</given-names></name><name><surname>Cerutti</surname><given-names>L</given-names></name><name><surname>Cuche</surname><given-names>BA</given-names></name><name><surname>Hulo</surname><given-names>N</given-names></name><name><surname>Bridge</surname><given-names>A</given-names></name><name><surname>Bougueleret</surname><given-names>L</given-names></name><name><surname>Xenarios</surname><given-names>I</given-names></name></person-group><article-title>New and continuing developments at PROSITE</article-title><source>Nucleic Acids Res</source><year>2013</year><volume>41</volume><fpage>344</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1093/nar/gks1067</pub-id></element-citation></ref><ref id="CR67"><label>67</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mitchell</surname><given-names>A</given-names></name><name><surname>Bucchini</surname><given-names>F</given-names></name><name><surname>Cochrane</surname><given-names>G</given-names></name><name><surname>Denise</surname><given-names>H</given-names></name><name><surname>Hoopen</surname><given-names>Pt</given-names></name><name><surname>Fraser</surname><given-names>M</given-names></name><name><surname>Pesseat</surname><given-names>S</given-names></name><name><surname>Potter</surname><given-names>S</given-names></name><name><surname>Scheremetjew</surname><given-names>M</given-names></name><name><surname>Sterk</surname><given-names>P</given-names></name><etal/></person-group><article-title>EBI metagenomics in 2016-an expanding and evolving resource for the analysis and archiving of metagenomic data</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>44</volume><issue>D1</issue><fpage>595</fpage><lpage>603</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1195</pub-id><pub-id pub-id-type="pmid">26400178</pub-id></element-citation></ref><ref id="CR68"><label>68</label><mixed-citation publication-type="other">Bailey TL, Elkan C. Fitting a mixture model by expectation maximization to discover motifs in biopolymers. In: AAAI 1994 ISMB. AAAI Press: 1994. p. 28&#x02013;36.</mixed-citation></ref><ref id="CR69"><label>69</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Saurin</surname><given-names>W</given-names></name><name><surname>K&#x000f6;ster</surname><given-names>W</given-names></name><name><surname>Dassa</surname><given-names>E</given-names></name></person-group><article-title>Bacterial binding protein-dependent permeases: characterization of distinctive signatures for functionally related integral cytoplasmic membrane proteins</article-title><source>Mol Microbiol</source><year>1994</year><volume>12</volume><fpage>993</fpage><lpage>1004</lpage><pub-id pub-id-type="doi">10.1111/j.1365-2958.1994.tb01087.x</pub-id><?supplied-pmid 7934906?><pub-id pub-id-type="pmid">7934906</pub-id></element-citation></ref><ref id="CR70"><label>70</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Altschul</surname><given-names>SF</given-names></name><name><surname>Gish</surname><given-names>W</given-names></name><name><surname>Miller</surname><given-names>W</given-names></name><name><surname>Myers</surname><given-names>EW</given-names></name><name><surname>Lipman</surname><given-names>DJ</given-names></name></person-group><article-title>Basic local alignment search tool</article-title><source>J Mol Biol</source><year>1990</year><volume>215</volume><issue>3</issue><fpage>403</fpage><lpage>10</lpage><pub-id pub-id-type="doi">10.1016/S0022-2836(05)80360-2</pub-id><?supplied-pmid 2231712?><pub-id pub-id-type="pmid">2231712</pub-id></element-citation></ref><ref id="CR71"><label>71</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Qin</surname><given-names>J</given-names></name><name><surname>Li</surname><given-names>R</given-names></name><name><surname>Raes</surname><given-names>J</given-names></name><name><surname>Arumugam</surname><given-names>M</given-names></name><name><surname>Burgdorf</surname><given-names>KS</given-names></name><name><surname>Manichanh</surname><given-names>C</given-names></name><name><surname>Nielsen</surname><given-names>T</given-names></name><name><surname>Pons</surname><given-names>N</given-names></name><name><surname>Levenez</surname><given-names>F</given-names></name><name><surname>Yamada</surname><given-names>T</given-names></name><etal/></person-group><article-title>A human gut microbial gene catalogue established by metagenomic sequencing</article-title><source>Nature</source><year>2010</year><volume>464</volume><issue>7285</issue><fpage>59</fpage><pub-id pub-id-type="doi">10.1038/nature08821</pub-id><?supplied-pmid 20203603?><pub-id pub-id-type="pmid">20203603</pub-id></element-citation></ref><ref id="CR72"><label>72</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sunagawa</surname><given-names>S</given-names></name><name><surname>Coelho</surname><given-names>LP</given-names></name><name><surname>Chaffron</surname><given-names>S</given-names></name><name><surname>Kultima</surname><given-names>JR</given-names></name><name><surname>Labadie</surname><given-names>K</given-names></name><name><surname>Salazar</surname><given-names>G</given-names></name><name><surname>Djahanschiri</surname><given-names>B</given-names></name><name><surname>Zeller</surname><given-names>G</given-names></name><name><surname>Mende</surname><given-names>DR</given-names></name><name><surname>Alberti</surname><given-names>A</given-names></name><etal/></person-group><article-title>Structure and function of the global ocean microbiome</article-title><source>Science</source><year>2015</year><volume>348</volume><issue>6237</issue><fpage>1261359</fpage><pub-id pub-id-type="doi">10.1126/science.1261359</pub-id><?supplied-pmid 25999513?><pub-id pub-id-type="pmid">25999513</pub-id></element-citation></ref><ref id="CR73"><label>73</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bernardes</surname><given-names>JS</given-names></name><name><surname>Vieira</surname><given-names>FRJ</given-names></name><name><surname>Zaverucha</surname><given-names>G</given-names></name><name><surname>Carbone</surname><given-names>A</given-names></name></person-group><article-title>A multi-objective optimisation approach accurately resolves protein domain architectures</article-title><source>Bioinformatics</source><year>2016</year><volume>32</volume><issue>3</issue><fpage>345</fpage><lpage>53</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btv582</pub-id><?supplied-pmid 26458889?><pub-id pub-id-type="pmid">26458889</pub-id></element-citation></ref><ref id="CR74"><label>74</label><mixed-citation publication-type="other">Hall M, Frank E, Holmes G, Pfahringer B, Reutemann P, Witten IH. The WEKA data mining software: an update. SIGKDD Explor. 2009;11.</mixed-citation></ref><ref id="CR75"><label>75</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Camon</surname><given-names>EB</given-names></name><etal/></person-group><article-title>An evaluation of GO annotation retrieval for BioCreAtIvE and GOA</article-title><source>BMC Bioinformatics</source><year>2005</year><volume>6</volume><fpage>1</fpage><lpage>17</lpage><pub-id pub-id-type="doi">10.1186/1471-2105-6-S1-S17</pub-id><pub-id pub-id-type="pmid">15631638</pub-id></element-citation></ref><ref id="CR76"><label>76</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hunter</surname><given-names>S</given-names></name><etal/></person-group><article-title>EBI metagenomics - a new resource for the analysis and archiving of metagenomic data</article-title><source>Nucleic Acids Res</source><year>2014</year><volume>42</volume><fpage>600</fpage><lpage>6</lpage><pub-id pub-id-type="doi">10.1093/nar/gkt961</pub-id></element-citation></ref><ref id="CR77"><label>77</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Balzer</surname><given-names>S</given-names></name><name><surname>Malde</surname><given-names>K</given-names></name><name><surname>Lanz&#x000e9;n</surname><given-names>A</given-names></name><name><surname>Sharma</surname><given-names>A</given-names></name><name><surname>Jonassen</surname><given-names>I</given-names></name></person-group><article-title>Characteristics of 454 pyrosequencing data-enabling realistic simulation with flowsim</article-title><source>Bioinformatics</source><year>2010</year><volume>26</volume><issue>18</issue><fpage>420</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btq365</pub-id></element-citation></ref><ref id="CR78"><label>78</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rho</surname><given-names>M</given-names></name><name><surname>Tang</surname><given-names>H</given-names></name><name><surname>Ye</surname><given-names>Y</given-names></name></person-group><article-title>FragGeneScan: predicting genes in short and error-prone reads</article-title><source>Nucleic Acids Res</source><year>2010</year><volume>38</volume><fpage>191</fpage><pub-id pub-id-type="doi">10.1093/nar/gkq747</pub-id></element-citation></ref><ref id="CR79"><label>79</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kunin</surname><given-names>V</given-names></name><name><surname>Raes</surname><given-names>J</given-names></name><name><surname>Harris</surname><given-names>JK</given-names></name><name><surname>Spear</surname><given-names>JR</given-names></name><name><surname>Walker</surname><given-names>JJ</given-names></name><name><surname>Ivanova</surname><given-names>N</given-names></name><name><surname>Von Mering</surname><given-names>C</given-names></name><name><surname>Bebout</surname><given-names>BM</given-names></name><name><surname>Pace</surname><given-names>NR</given-names></name><name><surname>Bork</surname><given-names>P</given-names></name><etal/></person-group><article-title>Millimeter-scale genetic gradients and community-level molecular convergence in a hypersaline microbial mat</article-title><source>Mol Syst Biol</source><year>2008</year><volume>4</volume><issue>1</issue><fpage>198</fpage><?supplied-pmid 18523433?><pub-id pub-id-type="pmid">18523433</pub-id></element-citation></ref><ref id="CR80"><label>80</label><mixed-citation publication-type="other">Mitchell A, Bucchini F, Cochrane G, Denise H, ten Hoopen P, Fraser M, Pesseat S, Potter S, Scheremetjew M, Sterk P, Finn RD. EBI metagenomics in 2016 - an expanding and evolving resource for the analysis and archiving of metagenomic data. Nucleic Acids Res. 2015.</mixed-citation></ref></ref-list></back></article>