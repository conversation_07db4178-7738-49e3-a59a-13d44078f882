<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">J Cheminform</journal-id><journal-id journal-id-type="iso-abbrev">J Cheminform</journal-id><journal-title-group><journal-title>Journal of Cheminformatics</journal-title></journal-title-group><issn pub-type="epub">1758-2946</issn><publisher><publisher-name>Springer International Publishing</publisher-name><publisher-loc>Cham</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">5471289</article-id><article-id pub-id-type="publisher-id">223</article-id><article-id pub-id-type="doi">10.1186/s13321-017-0223-1</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>Comparative evaluation of atom mapping algorithms for balanced metabolic reactions: application to Recon 3D </article-title></title-group><contrib-group><contrib contrib-type="author"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0003-4903-9515</contrib-id><name><surname>Preciat Gonzalez</surname><given-names>German A.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>El Assal</surname><given-names>Lemmer R. P.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Noronha</surname><given-names>Alberto</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Thiele</surname><given-names>Ines</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Haraldsd&#x000f3;ttir</surname><given-names>Hulda S.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Fleming</surname><given-names>Ronan M. T.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1"><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2295 9843</institution-id><institution-id institution-id-type="GRID">grid.16008.3f</institution-id><institution>Luxembourg Centre for Systems Biomedicine, </institution><institution>University of Luxembourg, </institution></institution-wrap>6, avenue du Swing, 4367 Belvaux, Luxembourg </aff></contrib-group><pub-date pub-type="epub"><day>14</day><month>6</month><year>2017</year></pub-date><pub-date pub-type="pmc-release"><day>14</day><month>6</month><year>2017</year></pub-date><pub-date pub-type="collection"><year>2017</year></pub-date><volume>9</volume><elocation-id>39</elocation-id><history><date date-type="received"><day>2</day><month>2</month><year>2017</year></date><date date-type="accepted"><day>25</day><month>5</month><year>2017</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2017</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><p>The mechanism of each chemical reaction in a metabolic network can be represented as a set of atom mappings, each of which relates an atom in a substrate metabolite to an atom of the same element in a product metabolite. Genome-scale metabolic network reconstructions typically represent biochemistry at the level of reaction stoichiometry. However, a more detailed representation at the underlying level of atom mappings opens the possibility for a broader range of biological, biomedical and biotechnological applications than with stoichiometry alone. Complete manual acquisition of atom mapping data for a genome-scale metabolic network is a laborious process. However, many algorithms exist to predict atom mappings. How do their predictions compare to each other and to manually curated atom mappings? For more than four thousand metabolic reactions in the latest human metabolic reconstruction, Recon 3D, we compared the atom mappings predicted by six atom mapping algorithms. We also compared these predictions to those obtained by manual curation of atom mappings for over five hundred reactions distributed among all top level Enzyme Commission number classes. Five of the evaluated algorithms had similarly high prediction accuracy of over 91% when compared to manually curated atom mapped reactions. On average, the accuracy of the prediction was highest for reactions catalysed by oxidoreductases and lowest for reactions catalysed by ligases. In addition to prediction accuracy, the algorithms were evaluated on their accessibility, their advanced features, such as the ability to identify equivalent atoms, and their ability to map hydrogen atoms. In addition to prediction accuracy, we found that software accessibility and advanced features were fundamental to the selection of an atom mapping algorithm in practice.</p><sec><title>Electronic supplementary material</title><p>The online version of this article (doi:10.1186/s13321-017-0223-1) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Atom mapping</kwd><kwd>Metabolic network reconstruction</kwd><kwd>Automation</kwd><kwd>RDT</kwd><kwd>DREAM</kwd><kwd>AutoMapper</kwd><kwd>CLCA</kwd><kwd>MWED</kwd><kwd>ICMAP</kwd><kwd>Recon 3D</kwd></kwd-group><funding-group><award-group><funding-source><institution>Universidad de Guadalajara (MX)</institution></funding-source><award-id>(Exp.021 V/2014/215</award-id><principal-award-recipient><name><surname>Preciat Gonzalez</surname><given-names>German A.</given-names></name></principal-award-recipient></award-group><award-group><funding-source><institution>European Union&#x02019;s Horizon 2020 research and innovation programme</institution></funding-source><award-id>No 668738</award-id><principal-award-recipient><name><surname>El Assal</surname><given-names>Lemmer R. P.</given-names></name></principal-award-recipient></award-group><award-group><funding-source><institution>Luxembourg National Research Fund (FNR)</institution></funding-source></award-group><award-group><funding-source><institution>Interagency Modeling and Analysis Group, Multi-scale Modeling Consortium U01</institution></funding-source><award-id>GM102098</award-id><principal-award-recipient><name><surname>Fleming</surname><given-names>Ronan M. T.</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2017</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>In every biochemical reaction, the total number of atoms of each element in all substrates is equal to that in all products. An atom mapping is a one-to-one correspondence (bijection) between an atom in a substrate and an atom in a product. An instance of a chemical reaction may be represented by a set of atom mappings, with one atom mapping between each substrate and product atom. Together, a set of atom mappings for a chemical reaction specify key aspects of the reaction mechanism, e.g., chemical bond change, breakage, and formation. A single chemical reaction can admit multiple chemically equivalent atom mappings when chemically equivalent atoms are present in a substrate, a product, or both. Therefore, each chemical reaction can be represented by one set, or multiple chemically equivalent sets, of atom mappings, each of which may be interpreted as a graph with a set of disconnected edges, each of which establishes a bijective relation between a substrate and product atom (Fig. <xref rid="Fig1" ref-type="fig">1</xref>).<fig id="Fig1"><label>Fig. 1</label><caption><p>An atom mapping for the enolase reaction. <bold>a</bold> Enolase (VMH ID: ENO) catalyses the hydrolysis of 2-phosphoglycerate (VMH ID: 2pg) to produce phosphoenolpyruvate (VMH ID: pep) and water (VMH ID: h2o). The atoms of the substrate are assigned with a mapping number that matches only with one atom of the same element in the product molecules; this representation describes the reaction mechanism. <bold>b</bold>, <bold>c</bold> A graphical representation of two possible atom mappings for the enolase reaction. Nodes (<italic>circles</italic>) represent atoms. Atoms can be matched to metabolite structures in (<bold>a</bold>) on their metabolite identifiers, <italic>colours</italic> and <italic>numbers</italic>. Directed edges (<italic>arrows</italic>) represent atom transitions. All hydrogen atoms are omitted to simplify the figure. Since oxygen atoms <italic>5</italic>, and <italic>6</italic> and <italic>9</italic>, <italic>10</italic>, and <italic>11</italic> are chemically equivalent twelve accurate atom mappings could be predicted for this reaction</p></caption><graphic xlink:href="13321_2017_223_Fig1_HTML" id="MO1"/></fig>
</p><p>Due to the time consuming nature of manual curation of atom mapping, it is of great importance to have reliable algorithms to predict atom mappings, especially for large sets of reactions found in metabolic databases and genome-scale metabolic network reconstructions. To our knowledge, only the BioPath [<xref ref-type="bibr" rid="CR1">1</xref>] and KEGG RPAIR [<xref ref-type="bibr" rid="CR2">2</xref>] databases disseminate manually curated atom mappings. Other metabolic databases such as EC-BLAST [<xref ref-type="bibr" rid="CR3">3</xref>] MetaCyc [<xref ref-type="bibr" rid="CR4">4</xref>] and MetRxn [<xref ref-type="bibr" rid="CR5">5</xref>] include predicted atom mappings.</p><p>A genome-scale metabolic reconstruction is a structured knowledge-base that abstracts pertinent information on the biochemical transformations taking place within an organism [<xref ref-type="bibr" rid="CR6">6</xref>]. Such reconstructions form the basis for the development of condition-specific metabolic models whose functions are simulated and validated by comparison with experimental results. These models are then used in a wide range of biological, biotechnological and biomedical research scenarios. Manual curation reconstructions involves extensive literature review [<xref ref-type="bibr" rid="CR7">7</xref>] and sometimes sufficient experimental literature is not in existence. This situation has driven the development of a range of software tools that seek to automate parts of the process to generate reconstruction content, e.g., [<xref ref-type="bibr" rid="CR8">8</xref>]. Recon 3D is the latest human metabolic reconstruction [<xref ref-type="bibr" rid="CR9">9</xref>], that adds three dimensional metabolite and protein structures to a genome-scale reconstruction for the first time. It is envisaged that this reconstruction and the models derived from it will drive deeper understanding how biochemical processes relate to mechanisms at the atomic scale.</p><p>It is fortunate that many atom mapping algorithms have been developed, but which is most suited to predict atom mappings for a genome-scale metabolic network reconstruction? Here, we evaluate six recently published atom mapping algorithms [<xref ref-type="bibr" rid="CR10">10</xref>&#x02013;<xref ref-type="bibr" rid="CR15">15</xref>]. We compare their predictions for more than five thousand metabolic reactions in the latest human metabolic reconstruction, Recon 3D [<xref ref-type="bibr" rid="CR9">9</xref>]. We also compared these predictions with manually curated atom mappings for a set of 512 human metabolic reactions. Of the manually curated atom mappings, 340 were obtained from the BioPath database [<xref ref-type="bibr" rid="CR1">1</xref>] and 172 additional reactions were manually curated to ensure that we could compare predictions with representative reaction types from all six top level EC numbers [<xref ref-type="bibr" rid="CR16">16</xref>] (see Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table 1S). The best performing algorithm was used to predict atom mappings for the latest version of the human metabolic reconstruction, Recon 3D [<xref ref-type="bibr" rid="CR9">9</xref>].</p><sec id="Sec2"><title>Atom mapping algorithms</title><p>Six academic and commercially available atom mapping algorithms were included in our evaluation <italic>Reaction Decoder Tool</italic> (RDT, [<xref ref-type="bibr" rid="CR10">10</xref>]), <italic>Determination of Reaction Mechanisms</italic> (DREAM, [<xref ref-type="bibr" rid="CR11">11</xref>]), <italic>AutoMapper 5.0.1</italic> (AutoMapper, [<xref ref-type="bibr" rid="CR12">12</xref>], ChemAxon, Budapest, Hungary), <italic>Canonical Labeling for Clique Approximation</italic> (CLCA, [<xref ref-type="bibr" rid="CR13">13</xref>]), <italic>Minimum Weighted Edit-Distance</italic> (MWED, [<xref ref-type="bibr" rid="CR14">14</xref>]) within Pathway Tools, and <italic>InfoChem-Map</italic> (ICMAP, [<xref ref-type="bibr" rid="CR15">15</xref>], InfoChem, Munich, Germany). These algorithms implement different prediction strategies or they use molecular properties, such as the bonds with hydrogen atoms or the use of the stereochemistry to predict atom mapping. They are also equipped with a variable array of advanced features (Fig. <xref rid="Fig2" ref-type="fig">2</xref>), including the ability to identify chemically equivalent atoms and reaction centres, as well as the option to map hydrogen atoms. Other distinguishing factors include ease of availability, licensing, and use of standardised data formats. Atom mappings and chemical structure data can be encoded in different atom level chemical reaction formats such as the SMILES [<xref ref-type="bibr" rid="CR17">17</xref>] or RXN [<xref ref-type="bibr" rid="CR18">18</xref>] formats. The most useful atom mapping format depends on the quality of the data and the intended application, e.g, the RXN format can hold information about chemical bond changes and stereochemistry. The SMILES format holds a canonical representation of molecules, which is independent of the application used to generate it. Examples of different chemical formats are given in the Additional file <xref rid="MOESM1" ref-type="media">1</xref>. A brief description of each atom mapping algorithm follows.<fig id="Fig2"><label>Fig. 2</label><caption><p>Atom mapping predictions for the enolase reaction. All six compared algorithms returned an accurate atom mapping but included different types of additional information. CLCA and MWED identify equivalent atoms in the reactants (<italic>blue</italic>). DREAM and AutoMapper map hydrogen atoms (<italic>yellow</italic>). RDT, CLCA, ICMAP and MWED all identify reaction centres (<italic>green</italic>). Unlike the other three algorithms, MWED does not identify reaction centres by adding information to the bonds that break and form. Instead, it assigns different colours to the molecular substructures (moieties) that break apart or bind together. The atom mapped reactions are visualised with MarvinView (ChemAxon, Budapest, Hungary), which accepts the RXN and SMILES formats as input</p></caption><graphic xlink:href="13321_2017_223_Fig2_HTML" id="MO2"/></fig>
</p><sec id="Sec3"><title>DREAM</title><p>
<italic>D</italic>etermination of <italic>REA</italic>ction <italic>M</italic>echanisms (DREAM) [<xref ref-type="bibr" rid="CR19">19</xref>] is a Web tool that identifies atom mappings using an optimisation-based approach known as Mixed Integer Linear Optimisation (MILP). This approach aims to minimise the number of bonds broken, bonds formed and bond order changes, between substrates and products. To make predictions it considers chemical properties such as stereochemistry and hydrogen bonding. The functionality of this algorithm is accessible as a web application. With DREAM, it is possible to atom map hydrogen atoms. In practice, the output of the current DREAM implementation does not designate reaction centres or assign chemically equivalent atoms in the output format, although based on the similarity to the MWED algorithm, which is able to carry out both of the aforementioned functions, this should be possible. Atom mappings are predicted from RXN or SMILES files, or a single reaction can be drawn in the web application using the Java Platform, Micro Edition (JME) Molecular Editor. The predicted atom mappings are output as RXN files. Web services and a web user interface for DREAM are available at <ext-link ext-link-type="uri" xlink:href="http://ares.tamu.edu/dream">http://ares.tamu.edu/dream</ext-link>.</p></sec><sec id="Sec4"><title>AutoMapper</title><p>AutoMapper [<xref ref-type="bibr" rid="CR20">20</xref>] (ChemAxon, Budapest, Hungary) uses two approaches to predict atom mapping: maximum common substructure (MCS) and minimum chemical distance (MCD). In MCS, substrate(s) and product(s) are represented as molecular graphs. This approach aims to identify the largest substructures of substrate graphs that are isomorphic to product graphs. For any atom that is not part of an isomorphic substructure, an atom mapping is calculated by MCD, which minimises the number of bonds that are broken and formed. AutoMapper is a tool for atom mapping a single reaction using the desktop application MarvinSketch (ChemAxon, Budapest, Hungary), or multiple reactions using Standardizer (ChemAxon, Budapest, Hungary) via command line. MarvinSketch is available free of charge whereas Standardizer requires a license that is free for academics. Automapper provides the option to map hydrogen atoms, but the tool can neither identify chemically equivalent atoms nor reaction centres. AutoMapper accepts a variety of different chemical formats, including RXN, InChI, and SMILES. It generates atom mappings in RXN or SMILES formats. AutoMapper is available from <ext-link ext-link-type="uri" xlink:href="https://www.chemaxon.com">https://www.chemaxon.com</ext-link>.</p></sec><sec id="Sec5"><title>RDT</title><p>Reaction Decoder Tool (RDT) [<xref ref-type="bibr" rid="CR10">10</xref>] is a Java-based, open-source atom mapping software tool. For each reaction, it returns the best of four atom mappings, predicted with four different algorithms: <italic>Mixture-MCS</italic>, which matches the maximum common substructure between substrates and products; <italic>Min-sub model</italic>, which matches the smallest substructures between the substrates and products; <italic>Max-sub model</italic>, which matches the largest substructures between the substrates and products; and, lastly, <italic>Assimilation model</italic>, which is triggered if a substrate or a product contains a ring system. Once an algorithm has matched a maximal number of atoms, the remaining atoms are mapped according to a similarity score for molecules, and the selection-and-elimination process is repeated until all atoms have been mapped. All four algorithms use the molecule stereochemistry to predict the atom mappings. RDT returns the atom mapping with the minimum number of modified bonds. RDT can be installed on a desktop or accessed via the web application EC-BLAST [<xref ref-type="bibr" rid="CR3">3</xref>]. RDT can identify the reaction centres but lacks the ability to map hydrogen atoms or identify chemically equivalent atoms. The user is given the choice between RXN and SMILES for both in- and output formats. Web services are available at <ext-link ext-link-type="uri" xlink:href="http://www.ebi.ac.uk/thornton-srv/software/rbl/">http://www.ebi.ac.uk/thornton-srv/software/rbl/</ext-link> and the software at <ext-link ext-link-type="uri" xlink:href="https://github.com/asad/ReactionDecoder">https://github.com/asad/ReactionDecoder</ext-link>.</p></sec><sec id="Sec6"><title>CLCA </title><p>The Canonical Labelling for Clique Approximation [<xref ref-type="bibr" rid="CR21">21</xref>] (CLCA) algorithm identifies the maximum common substructure between substrates and products using prime factorisation to generate canonical labels for bond-atoms. If a reaction has multiple reactant or product molecular graphs, many combinations of MCS exist. Thus, MCD is used to select a substructure that reduces the number of bond changes between reactants and products. It generates canonical labels using a variety of chemical properties, such as the number of non-hydrogen connections, the number of non-hydrogen bonds, atomic numbers, the sign of charge, the absolute charge, the number of connected hydrogen atoms, the atomic numbers of neighbouring atoms, R or S descriptors for chiral atoms, pro-R or pro-S for prochiral arms, and cis and trans descriptors. CLCA identifies chemically equivalent atoms by using their canonical labels; the algorithm also indicates reaction centres. CLCA can only map hydrogen atoms for reactions with fully protonated molecules. CLCA uses SMILES as its input and output format. The CLCA algorithm is available from <ext-link ext-link-type="uri" xlink:href="https://github.com/maranasgroup/MetRxn/tree/master/Alchemist">https://github.com/maranasgroup/MetRxn/tree/master/Alchemist</ext-link>.</p></sec><sec id="Sec7"><title>MWED </title><p>Similar to DREAM, Minimum Weighted Edit-Distance [<xref ref-type="bibr" rid="CR22">22</xref>] (MWED) uses an MILP approach that aims to minimise bonds changes. However, this algorithm assigns weights to bonds of the molecules in the reaction, and a specific cost when a bond is modified. The algorithm uses the bonds with hydrogen molecules and the stereochemistry to predict atom mappings. The algorithm is available within the Pathway Tools Software Suite [<xref ref-type="bibr" rid="CR23">23</xref>], which requires a license that is free of charge for academics. MWED can identify chemically equivalent atoms, as well as reaction centres. The algorithm does not map hydrogen atoms but it does take them into consideration when calculating atom mappings. It supports both RXN and SMILES as input and generates SMILES and MetaCyc output files. Pathway Tools is available from <ext-link ext-link-type="uri" xlink:href="http://biocyc.org/">http://biocyc.org/</ext-link>.</p></sec><sec id="Sec8"><title>ICMAP </title><p>InfoChem-Map [<xref ref-type="bibr" rid="CR15">15</xref>] (ICMAP, InfoChem, Munich, Germany) uses maximum common substructure and minimum chemical distance approaches. It identifies reaction centres using minimum chemical distance when a bond is formed or broken. Unmapped atoms are mapped by minimum chemical distance with two additional chemical rules applied. That is, the breakage and formation of bonds between heteroatoms is given preference over C&#x02013;C bonds, and bonds with hydrogen atoms are rated the same as C&#x02013;C bonds. ICMAP is a commercial desktop application. It identifies the reactions centres but cannot identify chemically equivalent atoms or map hydrogen atoms. One extra feature of the ICMAP algorithm is the classification of a reaction regarding the reaction centres indentified using a 15 digit numeric code. Both input and output are in RD file format, which is a container file format for RXN files. ICMAP application is available from <ext-link ext-link-type="uri" xlink:href="http://www.infochem.de/">http://www.infochem.de/</ext-link>.</p></sec></sec></sec><sec id="Sec9"><title>Results</title><sec id="Sec10"><title>Prediction accuracy</title><p>We evaluated the accuracy of the six atom mapping algorithms (Table <xref rid="Tab1" ref-type="table">1</xref>) by comparing predictions to manually curated atom mappings for 512 reactions (see Methods). The comparison of individual reactions is shown in Additional file <xref rid="MOESM2" ref-type="media">2</xref>. Table <xref rid="Tab1" ref-type="table">1</xref> shows the number of atom mapped reactions that were compared with the manually curated atom mappings.<table-wrap id="Tab1"><label>Table 1</label><caption><p>Number of evaluated reactions per algorithm</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Algorithm</th><th align="left">Number of reactions compared</th><th align="left">Unmapped reactions</th></tr></thead><tbody><tr><td align="left">RDT</td><td align="left">512</td><td align="left">0</td></tr><tr><td align="left">DREAM</td><td align="left">512</td><td align="left">0</td></tr><tr><td align="left">AutoMapper</td><td align="left">512</td><td align="left">0</td></tr><tr><td align="left">CLCA</td><td align="left">488</td><td align="left">24</td></tr><tr><td align="left">MWED</td><td align="left">477</td><td align="left">35</td></tr><tr><td align="left">ICMAP</td><td align="left">496</td><td align="left">16</td></tr></tbody></table><table-wrap-foot><p>Due to limited access to some algorithms, we could not predict atom mappings for all 512 manually curated reactions</p></table-wrap-foot></table-wrap>
</p><p>Metabolic reactions can be classified according to a four digit Enzyme Commission (EC) number assigned to the catalysing enzyme. The first digit, hereafter referred to as the top-level EC number, encodes the type of reaction that the enzyme catalyses (see Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table 1S). The prediction accuracy of the algorithms on representative reactions of all six defined reaction types and the overall accuracy is shown in Fig.&#x000a0; <xref rid="Fig3" ref-type="fig">3</xref>. Five of the 6 algorithms gave accurate predictions for more than 90% of reactions catalysed by oxidoreductases, with RDT being the most accurate. However, the accuracy of all six algorithms was low for reactions catalysed by ligases. DREAM, CLCA and ICMAP were equally accurate at prediction of atom mappings for isomerases. CLCA was the most accurate for hydrolases and lyases. Finally, DREAM was the most accurate for transferases. The predictions and manual curation for each reaction are given in Additional file <xref rid="MOESM3" ref-type="media">3</xref>.<fig id="Fig3"><label>Fig. 3</label><caption><p>Accuracy by reaction types. Percentage of reactions where predicted atom mappings agreed with the manually curated atom mappings. On each <italic>bar</italic> is shown the number of reactions compared for each algorithm and top level EC number</p></caption><graphic xlink:href="13321_2017_223_Fig3_HTML" id="MO3"/></fig>
</p></sec><sec id="Sec11"><title>Additional features</title><p>In addition to prediction accuracy, we compared the technical and advanced features of each algorithms. The technical features include the prediction approach, the user interface, the availability, and the file formats used for each algorithm (Table <xref rid="Tab2" ref-type="table">2</xref>). The main advanced features were the ability to identify chemically equivalent atoms and reaction centres, and the option to map hydrogen atoms (Table <xref rid="Tab3" ref-type="table">3</xref>). We also compared additional features, such as the ability to map all atoms in each reaction, the ability to map R groups and the consideration of reactant stereochemistry. Advanced features can be particularly important for certain applications of atom mapping [<xref ref-type="bibr" rid="CR24">24</xref>&#x02013;<xref ref-type="bibr" rid="CR26">26</xref>].<table-wrap id="Tab2"><label>Table 2</label><caption><p>Comparison of technical features</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left"/><th align="left">Approach</th><th align="left">Interface</th><th align="left">Availability</th><th align="left">Input file formats</th><th align="left">Output file formats</th></tr></thead><tbody><tr><td align="left">RDT</td><td align="left">Structure-based</td><td align="left">Web and desktop application</td><td align="left">Free</td><td align="left">RXN, SMILES</td><td align="left">RXN, SMILES</td></tr><tr><td align="left">DREAM</td><td align="left">Optimisation-based</td><td align="left">Web application</td><td align="left">Free</td><td align="left">RXN, SMILES</td><td align="left">RXN</td></tr><tr><td align="left">MWED</td><td align="left">Optimisation-based</td><td align="left">Desktop application</td><td align="left">Free for academics</td><td align="left">RXN, SMILES</td><td align="left">SMILES, MetaCyc</td></tr><tr><td align="left">CLCA</td><td align="left">Structure-based</td><td align="left">Algorithm</td><td align="left">Free</td><td align="left">SMILES</td><td align="left">SMILES</td></tr><tr><td align="left">ICMAP</td><td align="left">Structure-based</td><td align="left">Desktop application</td><td align="left">Commercial</td><td align="left">RXN</td><td align="left">RXN</td></tr><tr><td align="left">AutoMapper</td><td align="left">Structure-based</td><td align="left">Desktop application</td><td align="left">Free for academics</td><td align="left">RXN, SMILES</td><td align="left">RXN, SMILES</td></tr></tbody></table></table-wrap>
<table-wrap id="Tab3"><label>Table 3</label><caption><p>Comparison of advanced features</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left"/><th align="left">Equivalent atoms</th><th align="left">Hydrogen atoms</th><th align="left">Reaction centres</th><th align="left">Maps all atoms</th><th align="left">Maps R groups</th><th align="left">Stereo-chemistry</th><th align="left">Maps unbalanced reactions</th></tr></thead><tbody><tr><td align="left">RDT</td><td align="left">
<inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M2"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq1.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M4"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq2.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq3"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M6"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq3.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq4"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M8"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq4.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq5"><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M10"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq5.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq6"><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M12"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq6.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq7"><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M14"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq7.gif"/></alternatives></inline-formula>
</td></tr><tr><td align="left">DREAM</td><td align="left">
<inline-formula id="IEq8"><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M16"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq8.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq9"><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M18"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq9.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq10"><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M20"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq10.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq11"><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M22"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq11.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq12"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M24"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq12.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq13"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M26"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq13.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq14"><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M28"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq14.gif"/></alternatives></inline-formula>
</td></tr><tr><td align="left">MWED</td><td align="left">
<inline-formula id="IEq15"><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M30"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq15.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq16"><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M32"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq16.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq17"><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M34"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq17.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq18"><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M36"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq18.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq19"><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M38"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq19.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq20"><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M40"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq20.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq21"><alternatives><tex-math id="M41">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M42"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq21.gif"/></alternatives></inline-formula>
</td></tr><tr><td align="left">CLCA</td><td align="left">
<inline-formula id="IEq22"><alternatives><tex-math id="M43">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M44"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq22.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq23"><alternatives><tex-math id="M45">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark ^{\rm a}$$\end{document}</tex-math><mml:math id="M46"><mml:msup><mml:mo stretchy="false">&#x02713;</mml:mo><mml:mi mathvariant="normal">a</mml:mi></mml:msup></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq23.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq24"><alternatives><tex-math id="M47">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M48"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq24.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq25"><alternatives><tex-math id="M49">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M50"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq25.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq26"><alternatives><tex-math id="M51">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M52"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq26.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq27"><alternatives><tex-math id="M53">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M54"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq27.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq28"><alternatives><tex-math id="M55">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M56"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq28.gif"/></alternatives></inline-formula>
</td></tr><tr><td align="left">ICMAP</td><td align="left">
<inline-formula id="IEq29"><alternatives><tex-math id="M57">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M58"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq29.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq30"><alternatives><tex-math id="M59">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M60"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq30.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq31"><alternatives><tex-math id="M61">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M62"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq31.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq32"><alternatives><tex-math id="M63">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M64"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq32.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq33"><alternatives><tex-math id="M65">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M66"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq33.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq34"><alternatives><tex-math id="M67">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M68"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq34.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq35"><alternatives><tex-math id="M69">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M70"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq35.gif"/></alternatives></inline-formula>
</td></tr><tr><td align="left">AutoMapper 5.0.1</td><td align="left">
<inline-formula id="IEq36"><alternatives><tex-math id="M71">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M72"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq36.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq37"><alternatives><tex-math id="M73">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M74"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq37.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq38"><alternatives><tex-math id="M75">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M76"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq38.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq39"><alternatives><tex-math id="M77">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M78"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq39.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq40"><alternatives><tex-math id="M79">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M80"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq40.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq41"><alternatives><tex-math id="M81">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\varvec{\chi }$$\end{document}</tex-math><mml:math id="M82"><mml:mrow><mml:mi mathvariant="bold-italic">&#x003c7;</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq41.gif"/></alternatives></inline-formula>
</td><td align="left">
<inline-formula id="IEq42"><alternatives><tex-math id="M83">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\checkmark$$\end{document}</tex-math><mml:math id="M84"><mml:mo stretchy="false">&#x02713;</mml:mo></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq42.gif"/></alternatives></inline-formula>
</td></tr></tbody></table><table-wrap-foot><p>
<inline-formula id="IEq43"><alternatives><tex-math id="M85">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$^{\rm a}$$\end{document}</tex-math><mml:math id="M86"><mml:msup><mml:mrow/><mml:mi mathvariant="normal">a</mml:mi></mml:msup></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq43.gif"/></alternatives></inline-formula>&#x000a0;CLCA can only map hydrogen atoms for reactions with fully protonated molecules</p></table-wrap-foot></table-wrap>
</p></sec><sec id="Sec12"><title>Application to Recon 3D</title><p>To atom map reactions in a metabolic network reconstruction, one requires chemical structures, reaction stoichiometries, and an atom mapping algorithm. Chemical structures for 2369 (85%) (Fig.&#x000a0; <xref rid="Fig4" ref-type="fig">4</xref>a) of the 2797 unique metabolites in Recon 3D [<xref ref-type="bibr" rid="CR9">9</xref>] were obtained were obtained [<xref ref-type="bibr" rid="CR27">27</xref>] or drawn using information from publicly available sources, such as Recon 2 [<xref ref-type="bibr" rid="CR28">28</xref>], PubChem [<xref ref-type="bibr" rid="CR29">29</xref>], Kyoto encyclopedia of genes and genomes (KEGG) [<xref ref-type="bibr" rid="CR30">30</xref>], Chemical Entities of Biological Interest (ChEBI) [<xref ref-type="bibr" rid="CR31">31</xref>], Lipid Mass Structure Database (LMSD) [<xref ref-type="bibr" rid="CR32">32</xref>], BioPath database [<xref ref-type="bibr" rid="CR33">33</xref>], ChemSpider database [<xref ref-type="bibr" rid="CR34">34</xref>], and the Human Metabolome DataBase (HMDB) [<xref ref-type="bibr" rid="CR35">35</xref>]. No chemical structures were obtained for the remaining 428 (15%) unique metabolites due to insufficient information about the precise chemical structure (e.g., eumelanin), or because some Recon 3D reactions do not specify the nature of the reactant sufficiently, e.g., in lipid metabolism, a generic lipid substrate may correspond to a family of compounds, that may differ slightly in structure, due to the number and position of double bonds.<fig id="Fig4"><label>Fig. 4</label><caption><p>Coverage of metabolites and reactions in Recon 3D. <bold>a</bold> Coverage of unique metabolites structure data. <bold>b</bold> Coverage of reaction atom mapping data</p></caption><graphic xlink:href="13321_2017_223_Fig4_HTML" id="MO4"/></fig>
</p><p>We selected three different algorithms for the atom mapping of mass balanced Recon 3D reactions due to their high accuracy, ease of availability, and predictions without any unmapped atoms. The Reaction Decoder Tool (RDT) was selected to atom map reactions with implicit hydrogen atoms, while DREAM and CLCA were chosen to atom map reaction with explicit hydrogen atoms. Atom mappings were predicted for 7637 mass balanced reactions in Recon 3D, using both RDT and DREAM. Atom mappings were predicted for a further 272 mass imbalanced internal reactions in Recon 3D, using both RDT and CLCA. A remaining 840 internal reactions, that is 10% of all 8748 internal reactions, were not atom mapped due to missing chemical structures (Fig. <xref rid="Fig4" ref-type="fig">4</xref>b). Metabolite structures and atom mappings for Recon 3D [<xref ref-type="bibr" rid="CR9">9</xref>] are disseminated via the Virtual Metabolic Human database (VMH, <ext-link ext-link-type="uri" xlink:href="https://vmh.life/">https://vmh.life/</ext-link>). Metabolite structures are provided in MOL and SMILES formats. Atom mapping data are provided in both RXN and SMILES formats.</p></sec></sec><sec id="Sec13"><title>Discussion</title><p>The six algorithms compared in this work implement different atom mapping approaches. RDT, AutoMapper 5.0.1, CLCA, and ICMAP implement an approach based on the identification of a common molecular substructure, whereas DREAM and MWED implement an optimisation-based approach [<xref ref-type="bibr" rid="CR36">36</xref>]. Each algorithm is ideal for different purposes, e.g., the RDT, ICMAP, and MWED algorithms can be used to describe reaction mechanisms as they identify reaction centres. CLCA can be used to enumerate alternative atom mappings by identifying equivalent atoms. DREAM atom maps hydrogen atoms, and can thus be used to identify conserved moieties corresponding to hydrogen atoms in metabolic networks [<xref ref-type="bibr" rid="CR26">26</xref>] and implemented within the COBRA Toolbox [<xref ref-type="bibr" rid="CR37">37</xref>]. Finally, AutoMapper has a user-friendly interface and is part of a large suite of useful chemical informatics tools provided by ChemAxon. Due to the accuracy, ease of availability and ability to map all atoms as well as R groups, we chose RDT for atom mapping of Recon 3D reactions with implicit hydrogen atoms, DREAM for its ability to explicitly map hydrogen atoms and CLCA for its ability to map mass imbalanced reactions with explicit hydrogen atoms.</p><p>It is especially interesting that five of the six algorithms achieved a prediction accuracy of more than 90%. However, this is somewhat lower than the reported accuracy [<xref ref-type="bibr" rid="CR21">21</xref>, <xref ref-type="bibr" rid="CR22">22</xref>]. This discrepancy may be due to selection of a different set of manually curated atom mappings from the KEGG RPAIR database [<xref ref-type="bibr" rid="CR2">2</xref>]. Manual curation of more Recon 3D reactions, couple with testing new versions of existing atom mapping algorithms, will, in the future likely lead to prediction accuracy that asymptotically approaches 100%.</p><p>If EC numbers were available for all reactions in Recon 3D, a superior strategy would have been to use the prediction of the algorithm with the best accuracy for each top-level EC number. That is, instead of selecting a single algorithm to atom map all reactions in Recon 3D, we could have selected the most accurate algorithm for each reaction type (Fig. <xref rid="Fig3" ref-type="fig">3</xref>). However, this was not feasible because a large number of reactions have not yet been assigned an EC number [<xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR38">38</xref>]. Moreover, due to the high level of accuracy across all six algorithms, our choice to use RDT was also based on other features such as software availability and accessibility of the user interface.</p><p>The EC number assigned to a reaction contains information about the reaction mechanism, which can be used to identify prediction errors. The most common errors we encountered were the preference for breaking and forming C&#x02013;C <inline-formula id="IEq44"><alternatives><tex-math id="M87">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\sigma$$\end{document}</tex-math><mml:math id="M88"><mml:mi mathvariant="italic">&#x003c3;</mml:mi></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq44.gif"/></alternatives></inline-formula>-bonds instead of less stable bond types (Fig. <xref rid="Fig5" ref-type="fig">5</xref>), and the incorrect assignment of leaving groups in addition-elimination reactions (Fig. <xref rid="Fig6" ref-type="fig">6</xref>). In addition, idiosyncrasies of individual algorithms seem to result in inaccurate predictions for certain types of reactions.<fig id="Fig5"><label>Fig. 5</label><caption><p>An incorrect reaction mechanism predicted by five algorithms. Alanine-glyoxylate transaminase (VMH ID: AGTim) reaction catalyses the chemical transformation of <sc>L</sc>-alanine (VMH ID: ala_L) and glyoxylate (VMH ID: glx) into pyruvate (VMH ID: pyr) and glycine (VMH ID: gly). The known reaction mechanism of the alanine-glyoxylate transaminase reaction is represented by the manual atom mapping (<italic>top</italic>). Five algorithms predicted the same incorrect atom mapping for this reaction (<italic>bottom</italic>) [<xref ref-type="bibr" rid="CR43">43</xref>]. Only the MWED algorithm predicted correctly</p></caption><graphic xlink:href="13321_2017_223_Fig5_HTML" id="MO5"/></fig>
</p><p>Most algorithms tend to predict atom mappings and thereby reaction mechanisms with the lowest sum total number of bonds that are broken and formed, but often fail to sufficiently penalise the breakage of more stable bonds. This was the case for the alanine-glyoxylate transaminase reaction in Fig. <xref rid="Fig5" ref-type="fig">5</xref>, which has the EC number 2.6.1.44. The first number (2) indicates that the type of enzyme that catalyses the reaction is a transferase. The second number (6) indicates that it transfers nitrogen groups. The third number (1) indicates that the nitrogen group is transferred from an alanine molecule. The last number (44) indicates that a nitrogen group is transferred from alanine to glyoxylic acid. In this reaction, five algorithms predicted that the transferred group would be a methyl group, but this is incorrect due to the high energy needed to break a C&#x02013;C <inline-formula id="IEq45"><alternatives><tex-math id="M89">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\sigma$$\end{document}</tex-math><mml:math id="M90"><mml:mi mathvariant="italic">&#x003c3;</mml:mi></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq45.gif"/></alternatives></inline-formula>-bond.</p><p>Another common error was with prediction of addition-elimination mechanisms. Typically, a nucleophile will attack an electron-deficient centre, which will push electron density towards an adjacent oxygen, followed by electron density being pushed back to the nucleophilic centre, which will eliminate a leaving group. Under acidic conditions, the leaving group is reprotonated to give the resulting alcohol or thiol (see Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure 1S). Figure <xref rid="Fig6" ref-type="fig">6</xref> shows an example of a prediction error for the acetylcholinesterase reaction. The EC number of the reaction is 3.1.1.7, where (3) indicates a hydrolase reaction, (1) shows that the hydrolysis takes place on an ester bond; more specifically, carboxylic ester hydrolysis (1). The last number (7) indicates hydrolysis of a choline ester. The reaction mechanism predicted by DREAM was not consistent with this EC number.</p><p>As expected, DREAM and MWED predictions were similar (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table 2S) since they are based on a very similar MILP approach. However, although MWED is based on DREAM, the latter obtained greater accuracy when comparing its predictions with the manually-cured atom mappings. Of the 477 predictions that could be compared, the algorithms predictions differed in 69 occasions of which in 30 DREAM predicts correctly and MWED does not, 23 DREAM predicts incorrectly and MWED does not, and in 16 both predictions are wrong. Among the most important differences, MWED fails to correctly assign the leaving groups. Nevertheless, because of the weight, it gives to the bonds, it can correctly predict reaction mechanisms as indicated in Fig. <xref rid="Fig5" ref-type="fig">5</xref>.</p><p>Chemically equivalent atoms in a molecule are atoms that are interchangeable through any symmetric operation (Fig. <xref rid="Fig7" ref-type="fig">7</xref>). Reactions of molecules with equivalent atoms have multiple equivalent atom mappings. For instance, all reactions involving molecular oxygen (Fig. <xref rid="Fig7" ref-type="fig">7</xref>a) have at least two chemically equivalent atom mappings. A compact representation of all chemically equivalent atom mappings for a single reaction can be achieved by assigning the same atom mapping number to chemically equivalent atoms (Fig. <xref rid="Fig7" ref-type="fig">7</xref>). Although the DREAM should, in principle, be able to identify chemically equivalent atoms, only CLCA and MWED assigned chemically equivalent atoms in practice. However, there is room for improvement with both algorithms. CLCA often fails to identify chemically equivalent atoms in resonance structures (Fig. <xref rid="Fig7" ref-type="fig">7</xref>b) and MWED fails to identify molecular symmetry (Fig. <xref rid="Fig7" ref-type="fig">7</xref>d).<fig id="Fig6"><label>Fig. 6</label><caption><p>Incorrect addition&#x02013;elimination mechanism predicted by DREAM. Acetylcholinesterase (VMH ID: HMR_0641) reaction catalyses the breakdown of acetylcholine (VMH ID: ach) and water (VMH ID: h2o) to form acetate (VMH ID: ac) and choline (VMH ID: chol). The predicted mechanism (<italic>bottom</italic>) for the acetylcholinesterase reaction does not correspond to the mechanism described by the EC number (<italic>top</italic>). The (C<sup>3</sup>&#x02013;O<sup>5</sup>) bond is broken and the (C<sup>3</sup>&#x02013;O<sup>1</sup>) bond is formed. However, DREAM predicts that the (C<sup>6</sup>&#x02013;O<sup>5</sup>) bond is broken, followed by formation of the (C<sup>6</sup>&#x02013;O<sup>1</sup>) bond [<xref ref-type="bibr" rid="CR43">43</xref>]</p></caption><graphic xlink:href="13321_2017_223_Fig6_HTML" id="MO6"/></fig>
<fig id="Fig7"><label>Fig. 7</label><caption><p>Chemically equivalent atoms. Four molecules with chemically equivalent atoms (<italic>coloured backgrounds</italic>). <bold>a</bold> Molecular oxygen (VMH ID: o2). <bold>b</bold> Methyl phosphate where all three highlighted oxygen atoms are chemically equivalent through resonance. MWED, but not CLCA, can identify the highlighted atoms as being chemically equivalent. <bold>c</bold> 1-Amino-1,1-ethanedio. <bold>d</bold> 1,3-Diaminopropane (VMH ID: 13dampp), which shows that chemically equivalent atoms are not necessarily connected to a shared atom. CLCA, but not MWED, can identify the highlighted atoms as being chemically equivalent</p></caption><graphic xlink:href="13321_2017_223_Fig7_HTML" id="MO7"/></fig>
</p><p>An atom mapping provides an abstract mechanistic description of a chemical reaction. It describes the fate of the atoms and all the bond changes that happen during the reaction. Therefore, when an atom mapping is predicted the reaction centres are identified. RDT, CLCA, MWED, and ICMAP are all able to identify reaction centers and generate their results indicating where the reaction centre is, however, DREAM and AutoMapper do not. The identification of reaction centers is useful for a visual description of the reaction mechanism. These can potentially also be used to predict optimal pathways in a metabolic network, involving the minimum number of bond changes.</p><p>Two reactions with identical stoichiometry may occur by different reaction mechanisms depending, for example, on the catalysing enzyme. Each reaction mechanism corresponds to a distinct set of atom mappings. The optimisation-based algorithms DREAM and MWED are able to predict multiple optimal atom mappings for a single reaction. On the other hand, ICMAP represent ambiguity in a reaction mechanism by leaving some atoms unmapped. This approach may be useful to represent mechanistic ambiguity in a compact form as a single file (Fig. <xref rid="Fig8" ref-type="fig">8</xref>). However, in some cases, it remains unmapped atoms that were not assigned by the MCS process (Fig. <xref rid="Fig2" ref-type="fig">2</xref>f, on the left hand side, one oxygen atom in the 2pg molecule and on the right hand side, the h2o molecule).<fig id="Fig8"><label>Fig. 8</label><caption><p>Unmapped atoms. Occasionally, ICMAP leaves some atoms unmapped. In this ICMAP prediction (VMH ID: MVLACc), the oxygen atoms in Mevalonate (VMH ID: mev_R) that are indicated in <italic>blue</italic>, may map to the water molecule (VMH ID: h2o) or the 4-hydroxy-4-methyl-2-oxanone molecule (VMH ID: mvlac)</p></caption><graphic xlink:href="13321_2017_223_Fig8_HTML" id="MO8"/></fig>
</p><p>The optimisation based algorithms DREAM and MWED require each reaction to be mass elementally balanced in order to assign atom mappings. This requirement is consistent with chemical principles. However, it could be a limitation depending on the objective of the atom mapping. In particular, atom mapping elementally unbalanced reactions could provide an automatic approach to suggest modifications to the reconstruction that could balance the reaction (Fig. <xref rid="Fig9" ref-type="fig">9</xref>), or for the automatic assignment of EC numbers [<xref ref-type="bibr" rid="CR38">38</xref>]. This utility is especially important for reconstructions whose content is at the edge of experimental biochemistry. Nevertheless, for other applications [<xref ref-type="bibr" rid="CR24">24</xref>, <xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR39">39</xref>] it is necessary to know the fate of all the atoms in the metabolic network.<fig id="Fig9"><label>Fig. 9</label><caption><p>Unbalanced thyroid peroxidase reaction. The thyroid peroxidase reaction (VMH ID: THYPX) catalises the hydrogen peroxide molecules (VMH ID: h2o2) and two hydrogen iodides (VMH ID: i) into two water molecules (VMH ID: h2o) and two molecular iodines (VMH ID: iodine). The reaction can be balanced by adding the unmapped molecular iodine product atoms (<italic>blue background</italic>) on the <italic>left hand side</italic>
</p></caption><graphic xlink:href="13321_2017_223_Fig9_HTML" id="MO9"/></fig>
</p><p>Atom mapping algorithms use different chemical formats to input and output atom mappings. Each chemical format has unique features that distinguish it from other formats. DREAM, AutoMapper, CLCA, and MWED all used the SMILES format which is a compact string representation of a reaction with canonicalised molecules. RXN files were used by DREAM, MWED, and AutoMapper. This file format can store additional data such as bond changes and stereochemistry. ICMAP was the only algorithm to use the RD format. This format is a concatenation of multiple reactions in RXN format. The developers of MWED have also created their own format based on the data in the MetaCyc database [<xref ref-type="bibr" rid="CR4">4</xref>]. An additional feature to accept and return at least one standard format (SMILES or RXN) should be a publication criterion for any future atom mapping algorithm. Chemoinformatics applications such as <italic>molConverter</italic> from ChemAxon [<xref ref-type="bibr" rid="CR20">20</xref>], and OpenBabel [<xref ref-type="bibr" rid="CR40">40</xref>] can be used to convert from one standard format to another.</p><p>Only AutoMapper, CLCA and DREAM provided the option to predict the fate of hydrogen atoms in chemical reactions. RDT, MWED, and ICMAP do not explicitly return the mapping of hydrogen atoms. Nevertheless, bonds involving hydrogen atoms are considered for the assignment of atom mappings by all algorithms, except for AutoMapper v5.0.1. Hydrogen atoms are not as useful as carbon atoms for isotopic labelling in metabolic flux analysis [<xref ref-type="bibr" rid="CR24">24</xref>], which is currently the main application for atom mapping data. However, as we shall now discuss, there are other applications where this is important.</p><p>When reaction stoichiometry is combined with atom mapping data for an entire metabolic network, new applications become possible that are beyond the resolution of reaction stoichiometry alone. For example, given atom mapping data for a stoichiometrically consistent metabolic network, the set of <italic>conserved moieties</italic> can be efficiently computed and identified [<xref ref-type="bibr" rid="CR26">26</xref>]. Each conserved moiety corresponds to a particular identifiable molecular substructure that is invariant with respect to the chemical transformations of that network. Atom mapping of all atoms is a prerequisite for identification of all conserved moieties. The cardinality of the set of conserved moieties can be enumerated <italic>a priori </italic>as it is equivalent to the row rank deficiency of the corresponding stoichiometric matrix. As such, it is possible to easily check if the expected number of conserved moieties has been computed, given a set of atom mapping data for a stoichiometrically consistent network. In some instances, even with mapping of hydrogen atoms, one or more conserved moiety is not computed with the aforementioned approach [<xref ref-type="bibr" rid="CR26">26</xref>]. It appears as though mapping of electrons may also be necessary, but the conditions for this requirement remain to be clarified [<xref ref-type="bibr" rid="CR26">26</xref>]. Every biochemical network will contain at least one conserved moiety corresponding to a hydrogen atom, so this feature is desired for atom mapping of metabolic reconstructions.</p><p>The set of conserved moiety vectors forms a sparse non-negative integer basis for the left null space of a stoichiometrically consistent network. Constraints derived from this left null space basis are a fundamental part of kinetic modelling because the amount of each conserved moiety is time-invariant [<xref ref-type="bibr" rid="CR41">41</xref>]. The set of all conserved moieties for a chemical reaction network give rise to a biochemically intuitive non-negative integer basis for the left nullspace of the corresponding stoichiometric matrix. Of course, one can always compute a linear basis for the left nullspace of a stoichiometric matrix using various linear algebraic algorithms, but then biochemical interpretation of each basis vector is problematic. From this perspective, we advocate for explicit mapping of hydrogen atoms, or at least the option to do so.</p><p>Atom mappings are also used to identify the existence and contribution of pathways involved in the metabolism of specific biological molecules by refining carbon flux paths with atomic trace data [<xref ref-type="bibr" rid="CR25">25</xref>], which can lead to potential biomarkers for diseases. Since conserved moieties consist of a set of atoms that follow the same path through a metabolic network, in principle, it is sufficient to isotopically label a single atom within a moiety to detect the possible paths of that entire moiety through a metabolic network [<xref ref-type="bibr" rid="CR24">24</xref>]. Additionally, numerical classifiers for enzymes known as Enzyme Commission numbers (EC numbers) [<xref ref-type="bibr" rid="CR16">16</xref>] can be computationally assigned to reactions in genome-scale metabolic networks [<xref ref-type="bibr" rid="CR38">38</xref>] using atom mappings. EC numbers establish links between enzymatic reactions, enzymes, enzyme genes, and metabolic pathways. These are just some examples of the many potential applications of atom mapping in genome-scale metabolic networks.</p></sec><sec id="Sec14"><title>Conclusions</title><p>We focussed on comparing the predictive accuracy of atom mapping algorithms for elementally balanced biochemical reactions with complete structural specification of reactants. Therefore, any conclusions we obtained are specific to this particular atom mapping objective. Many of the algorithms tested have a variety of different advanced features which were not compared in detail so depending on ones objective the optimal algorithmic choice could differ.</p><p>Of the six atom mapping algorithms tested for atom mapping of elementally balanced reactions, most had an impressive prediction accuracy of 91% or higher, e.g. the DREAM, CLCA, MWED, ICMAP and RDT algorithms. However different algorithms seem to be more accurate for different types of reaction mechanisms. Selection of an algorithm also depends on factors such as ease of availability of the software, quality of the user interface, and ability to deliver advanced features beyond atom mapping <italic>per se</italic>. Objectively, from the high accuracy achieved by many atom mapping algorithms, one can conclude that atom mapping is an advanced art. To reach perfection, detailed comparison of algorithmic approaches and elucidation of systematic imperfections will be necessary.</p><p>From a network perspective, approaching perfection in atom mapping is important because if the probability of an incorrectly mapped atom is <inline-formula id="IEq50"><alternatives><tex-math id="M91">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$p\in (0,1)$$\end{document}</tex-math><mml:math id="M92"><mml:mrow><mml:mi>p</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mo stretchy="false">(</mml:mo><mml:mn>0</mml:mn><mml:mo>,</mml:mo><mml:mn>1</mml:mn><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq50.gif"/></alternatives></inline-formula> but the length of a pathway involving that atom is <italic>k</italic> then the probabilty of an incorrectly mapped atom at the end of the pathway is <inline-formula id="IEq51"><alternatives><tex-math id="M93">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$${\mathcal {O}}(p^{k})$$\end{document}</tex-math><mml:math id="M94"><mml:mrow><mml:mi mathvariant="script">O</mml:mi><mml:mo stretchy="false">(</mml:mo><mml:msup><mml:mi>p</mml:mi><mml:mi>k</mml:mi></mml:msup><mml:mo stretchy="false">)</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq51.gif"/></alternatives></inline-formula>, e.g., <inline-formula id="IEq52"><alternatives><tex-math id="M95">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$0.91^{10}\cong 0.39$$\end{document}</tex-math><mml:math id="M96"><mml:mrow><mml:mn>0</mml:mn><mml:mo>.</mml:mo><mml:msup><mml:mn>91</mml:mn><mml:mn>10</mml:mn></mml:msup><mml:mo>&#x02245;</mml:mo><mml:mn>0.39</mml:mn></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq52.gif"/></alternatives></inline-formula>. This is a worst case scenario that assumes the same atom is incorrectly mapped in each of the <italic>k</italic> sequential reactions. Nevertheless, it points out the importance for the atom mapping community of striving for ever higher levels of accuracy.</p><p>In our view, this can best be achieved by more detailed comparison of the alternate algorithms, in mathematical form, as well as in their computational implementations, via licensed source code for desktop applications. Expansion of the number of additional features would be valuable. Already, identification of chemically equivalent atoms and reaction centres or mapping of hydrogen atoms are of key importance in certain applications, e.g., identification of conserved moieties and simulation of isotope labelling experiments. The convergence of genome-scale metabolic modelling, chemoinformatics and structural bioinformatics, as illustrated in Recon 3D opens up a host of new applications for atom mapping, not to mention the potential for cross fertilisation of ideas.</p></sec><sec id="Sec15"><title>Methods</title><sec id="Sec16"><title>Recon 3D</title><p>Recon 3D is a genome-scale metabolic reconstruction of human metabolism accounting for ~12,000 metabolic reactions involving ~8000 metabolites [<xref ref-type="bibr" rid="CR9">9</xref>]. It is not cell-type specific, rather it is an amalgamation of the known metabolic capabilities ocurring in at least one human cell, regardless of type. Recon 3D is the most complete global human network model to date and the first to account for mechanisms at the atomic scale.</p></sec><sec id="Sec17"><title>RXN files</title><p>After obtaining the chemical structures of the unique metabolites [<xref ref-type="bibr" rid="CR27">27</xref>], reaction stoichiometries from Recon 3D were used to create the corresponding RXN files using a MATLAB live script (Additional files <xref rid="MOESM4" ref-type="media">4</xref> and <xref rid="MOESM5" ref-type="media">5</xref>).</p></sec><sec id="Sec18"><title>Manually curated atom mappings</title><p>Manually curated atom mappings were obtained from the BioPath database [<xref ref-type="bibr" rid="CR33">33</xref>] for the 340 Recon 3D reactions that are also on the BioPath database. An additional 196 Recon 3D reactions representative for al 6 top EC-numbers were manually atom mapped according to textbook characterisations of reaction mechanisms [<xref ref-type="bibr" rid="CR42">42</xref>, <xref ref-type="bibr" rid="CR43">43</xref>].</p></sec><sec id="Sec19"><title>Algorithms predictions</title><p>CLCA, MWED ICMAP and RDT predictions were obtained by contacting the developers of each algorithm. DREAM predictions were obtained by compressing the RXN files obtained into different ZIP files with less than 2 MB of data. Then the ZIP files were uploaded in the DREAM web application. AutoMapper 5.0.1 predictions were obtained by using the ChemAxon application <italic>Standardizer</italic>.</p></sec><sec id="Sec20"><title>Evaluation of prediction accuracy</title><p>We say that an algorithm accurately predicts the atom mappings for a reaction if each atom mapping for that reaction matches that obtained by manual curation. The accuracy of each algorithm was quantified using the percentage of reactions that were accurately predicted. Predictions that did not match the manually curated atom mappings were double checked manually.</p></sec><sec id="Sec21"><title>Standardisation process</title><p>Not all algorithms returned results in the same format. In particular, the order of reactants (Fig. <xref rid="Fig2" ref-type="fig">2</xref>e, in this example, MWED prediction switched the product molecules) and the order of atoms within reactants varied between algorithms (Fig. <xref rid="Fig2" ref-type="fig">2</xref>). Therefore, we standardised the algorithmic output to enable comparison between algorithms and with manually curated data. First, atom mapping predictions in RXN format were converted to SMILES format to obtain the canonical order of atoms in molecules. The conversion was performed using the ChemAxon application <italic>molConverter</italic>. Then, the SMILES strings for substrates and products were sorted by length. If the SMILES for two substrates (or products) were of the same length, they were sorted in alphabetical order. After that, the reactions in SMILES were converted back to RXN format with <italic>molConverter</italic>. Finally, the atom mapping numbers of each atom were re-assigned in ascending order, maintaining the equivalent atoms for the CLCA and MWED algorithms. This standardisation process enabled automatic comparison between predicted and manually curated atom mappings.</p><sec id="Sec22"><title>Unmapped atoms</title><p>In some cases, ICMAP did not assign an atom mapping number to all the atoms in a reaction (e.g., oxygen in Fig. <xref rid="Fig2" ref-type="fig">2</xref>f). In a post-processing step, atom mapping numbers were automatically assigned to all uniquely identifiable unmapped atoms. A substrate atom was deemed to be uniquely identifiable if it was the only unmapped atom of a particular element and it could therefore only map to one atom of the same element in the products.</p></sec><sec id="Sec23"><title>Chemically equivalent atoms</title><p>The identification of chemically equivalent atoms is essential prior to comparing atom mappings. Otherwise, a discrepancy between two equivalent atom mappings would be indistinguishable from a discrepancy due to an incorrect prediction. When algorithms did not identify chemically equivalent atoms, they were identified in a post-processing step using techniques from graph theory implemented in MATLAB (MathWorks, Natick, Massachusetts). Every molecule was represented as a molecular graph <inline-formula id="IEq53"><alternatives><tex-math id="M97">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$G:=[V,E]$$\end{document}</tex-math><mml:math id="M98"><mml:mrow><mml:mi>G</mml:mi><mml:mo>:</mml:mo><mml:mo>=</mml:mo><mml:mo stretchy="false">[</mml:mo><mml:mi>V</mml:mi><mml:mo>,</mml:mo><mml:mi>E</mml:mi><mml:mo stretchy="false">]</mml:mo></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq53.gif"/></alternatives></inline-formula>; an ordered pair of vertices, <inline-formula id="IEq54"><alternatives><tex-math id="M99">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$v\in V$$\end{document}</tex-math><mml:math id="M100"><mml:mrow><mml:mi>v</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi>V</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq54.gif"/></alternatives></inline-formula>, and edges, <inline-formula id="IEq55"><alternatives><tex-math id="M101">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$e\in E$$\end{document}</tex-math><mml:math id="M102"><mml:mrow><mml:mi>e</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi>E</mml:mi></mml:mrow></mml:math><inline-graphic xlink:href="13321_2017_223_Article_IEq55.gif"/></alternatives></inline-formula>. A vertex represents an atom and an undirected edge between two vertices represents a chemical bond. Two atoms <italic>a</italic> and <italic>b</italic> were said to be chemically equivalent if both were of the same element, both were connected to the same atom <italic>c</italic>, and neither was connected to any other atom. The bond type was not considered since equivalent atoms are often part of a resonance structure with delocalised electrons. The chemically equivalent atoms in each substrate were assigned the same atom mapping number. Then, the atom mapping numbers of matched product atoms were updated accordingly. This process was also then repeated in the opposite direction, from products to substrates.</p></sec></sec></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec24"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="13321_2017_223_MOESM1_ESM.pdf"><caption><p>
<bold>Additional file 1.</bold> Contains the supplementary information of the manuscript. This includes 1) Different atom mapping chemical formats for reaction cyanase; 2) <bold>Figure 1S</bold>: Ester hydrolysis under basic conditions; 3) <bold>Figure 2S</bold>: Cyanase reaction atom mapped; 4) <bold>Table 1S</bold>: Top level Enzyme Commission number classification; 5) <bold>Table 2S</bold>: Similarity between atom mapping predictions.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="13321_2017_223_MOESM2_ESM.csv"><caption><p>
<bold>Additional file 2.</bold> Contains the full comparison after the algorithmic and manual check of the of reactions. Full-atom mapping comparison table. With all standardised reactions, RXN file atom identifiers were extracted as an array and processed in MATLAB where they were compared if the reaction was present in the database. Comparisons were made with the following order: 1) cured reactions, 2) DREAM, 3) AutoMapper, 4) CLCA 5) MWED, and 6) ICMAP. In the curated reactions column, there are only two values, 1 or NaN if there was no file. DREAM columncould have 3 values 1 if equal than the curated reactions, 2 if are not equal and NaN if the le does not exist. AutoMapper column has 4 values, 1&#x02013;3 and NaN. 1 if is equal to curated files, 2 if the mappings are equal to DREAM and 3 if they are not equal to the curated DREAM reactions and NaN if not file existed. So CLCA 1&#x02013;4 and NaN, MWED 1&#x02013;5 and NaN, and NaN ICMAP 1&#x02013;6. If all reactions are equal, all columns 1 values obtained. With the matrix containing all the comparisons similarity of all algorithms was calculated.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="13321_2017_223_MOESM3_ESM.zip"><caption><p>
<bold>Additional file 3.</bold> Contains two folders, one contains the predictions obtained from each algorithm in RXN format (Folder: algorithmicPredictions), the other contains manually curated atom mappings, (Folder:standardisedPredictions).</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="13321_2017_223_MOESM4_ESM.pdf"><caption><p>
<bold>Additional file 4.</bold> A pdf version of atomMappingComparisonScript.mlx.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="13321_2017_223_MOESM5_ESM.mlx"><caption><p>
<bold>Additional file 4.</bold> A MATLAB LiveScript used to standardise the algorithmic predictions, before comparison. Requires MATLAB 2016a and above.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>RDT</term><def><p>Reaction Decoder Tool</p></def></def-item><def-item><term>DREAM</term><def><p>Determination of reaction mechanisms</p></def></def-item><def-item><term>CLCA</term><def><p>Canonical Labelling for Clique Approximation</p></def></def-item><def-item><term>MWED</term><def><p>Minimum Weighted Edit-Distance</p></def></def-item><def-item><term>ICMAP</term><def><p>InfoChem-Map</p></def></def-item><def-item><term>MILP</term><def><p>Mixed Integer Linear Optimisation</p></def></def-item><def-item><term>MCS</term><def><p>Maximum common substructure</p></def></def-item><def-item><term>MCD</term><def><p>Minimum chemical distance</p></def></def-item><def-item><term>EC</term><def><p>Enzyme Commission</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Electronic supplementary material</bold></p><p>The online version of this article (doi:10.1186/s13321-017-0223-1) contains supplementary material, which is available to authorized users.</p></fn></fn-group><ack><title>Authors' contributions</title><p>GPG computationally implemented the atom mapping comparison. LEA and IT generated manually curated metabolite structure and atom mapping files. AN and IT integrated results with the VMH database. HSH provided technical superivision. RMTF concieved the study. All authors contributed to the manuscript. All authors read and approved the final manuscript.</p><sec id="FPar1"><title>Acknowledgements</title><p>The authors would like to thank the developers of each of the tested algorithms for engaging constructively to facilitate this comparison.</p></sec><sec id="FPar2"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec id="FPar3"><title>Funding</title><p>GPG was supported by the University of Guadalajara scholarship (Exp. 021 V/2014/215). LEA was supported by funding from the European Union Horizon 2020 research and innovation programme under Grant Agreement No. 668738. HSH was supported by the Luxembourg National Research Fund (FNR) through the National Centre of Excellence in Research (NCER) on Parkinson&#x02019;s disease. RMTF was funded by the Interagency Modeling and Analysis Group, Multi-scale Modeling Consortium U01 awards from the National Institute of General Medical Sciences, award GM102098, and U.S. Department of Energy, Office of Science, Biological and Environmental Research Program, award DE-SC0010429. The funding bodies had no role in the design of the study or the collection, analysis, and interpretation of data or in writing the manuscript.</p></sec><sec id="FPar4"><title>Publisher&#x02019;s Note</title><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Forster</surname><given-names>M</given-names></name><name><surname>Pick</surname><given-names>A</given-names></name><name><surname>Raitner</surname><given-names>M</given-names></name><name><surname>Schreiber</surname><given-names>F</given-names></name><name><surname>Brandenburg</surname><given-names>FJ</given-names></name></person-group><article-title>The system architecture of the BioPath system</article-title><source>In Silico Biol</source><year>2002</year><volume>2</volume><issue>3</issue><fpage>415</fpage><lpage>426</lpage><pub-id pub-id-type="pmid">12542424</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><mixed-citation publication-type="other">Shimizu Y, Hattori M, Goto S, Kanehisa M (2008) Generalized reaction patterns for prediction of unknown enzymatic reactions. Genome informatics. international conference on genome informatics, vol 20, pp 149&#x02013;158</mixed-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rahman</surname><given-names>SA</given-names></name><name><surname>Cuesta</surname><given-names>SM</given-names></name><name><surname>Furnham</surname><given-names>N</given-names></name><name><surname>Holliday</surname><given-names>GL</given-names></name><name><surname>Thornton</surname><given-names>JM</given-names></name></person-group><article-title>EC-BLAST: a tool to automatically search and compare enzyme reactions</article-title><source>Nat Methods</source><year>2014</year><volume>11</volume><issue>2</issue><fpage>171</fpage><lpage>174</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2803</pub-id><pub-id pub-id-type="pmid">24412978</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Caspi</surname><given-names>R</given-names></name><name><surname>Altman</surname><given-names>T</given-names></name><name><surname>Dreher</surname><given-names>K</given-names></name><name><surname>Fulcher</surname><given-names>CA</given-names></name><name><surname>Subhraveti</surname><given-names>P</given-names></name><name><surname>Keseler</surname><given-names>IM</given-names></name><name><surname>Kothari</surname><given-names>A</given-names></name><name><surname>Krummenacker</surname><given-names>M</given-names></name><name><surname>Latendresse</surname><given-names>M</given-names></name><name><surname>Mueller</surname><given-names>LA</given-names></name><name><surname>Ong</surname><given-names>Q</given-names></name><name><surname>Paley</surname><given-names>S</given-names></name><name><surname>Pujar</surname><given-names>A</given-names></name><name><surname>Shearer</surname><given-names>AG</given-names></name><name><surname>Travers</surname><given-names>M</given-names></name><name><surname>Weerasinghe</surname><given-names>D</given-names></name><name><surname>Zhang</surname><given-names>P</given-names></name><name><surname>Karp</surname><given-names>PD</given-names></name></person-group><article-title>The MetaCyc database of metabolic pathways and enzymes and the BioCyc collection of pathway/genome databases</article-title><source>Nucleic Acids Res</source><year>2012</year><volume>40</volume><issue>Database issue</issue><fpage>742</fpage><lpage>753</lpage><pub-id pub-id-type="doi">10.1093/nar/gkr1014</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kumar</surname><given-names>A</given-names></name><name><surname>Suthers</surname><given-names>PF</given-names></name><name><surname>Maranas</surname><given-names>CD</given-names></name></person-group><article-title>MetRxn: a knowledgebase of metabolites and reactions spanning metabolic models and databases</article-title><source>BMC Bioinform</source><year>2012</year><volume>13</volume><fpage>6</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-13-6</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Palsson</surname><given-names>B&#x000d8;</given-names></name></person-group><source>Systems biology: constraint-based reconstruction and analysis</source><year>2015</year><publisher-loc>Cambridge</publisher-loc><publisher-name>Cambridge University Press</publisher-name></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thiele</surname><given-names>I</given-names></name><name><surname>Palsson</surname><given-names>B&#x000d8;</given-names></name></person-group><article-title>A protocol for generating a high-quality genome-scale metabolic reconstruction</article-title><source>Nat Protoc</source><year>2010</year><volume>5</volume><issue>1</issue><fpage>93</fpage><lpage>121</lpage><pub-id pub-id-type="doi">10.1038/nprot.2009.203</pub-id><pub-id pub-id-type="pmid">20057383</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Magn&#x000fa;sd&#x000f3;ttir</surname><given-names>S</given-names></name><name><surname>Heinken</surname><given-names>A</given-names></name><name><surname>Kutt</surname><given-names>L</given-names></name><name><surname>Ravcheev</surname><given-names>DA</given-names></name><name><surname>Bauer</surname><given-names>E</given-names></name><name><surname>Noronha</surname><given-names>A</given-names></name><name><surname>Greenhalgh</surname><given-names>K</given-names></name><name><surname>J&#x000e4;ger</surname><given-names>C</given-names></name><name><surname>Baginska</surname><given-names>J</given-names></name><name><surname>Wilmes</surname><given-names>P</given-names></name><name><surname>Fleming</surname><given-names>RMT</given-names></name><name><surname>Thiele</surname><given-names>I</given-names></name></person-group><article-title>Generation of genome-scale metabolic reconstructions for 773 members of the human gut microbiota</article-title><source>Nat Biotech</source><year>2017</year><volume>35</volume><issue>1</issue><fpage>81</fpage><lpage>89</lpage><pub-id pub-id-type="doi">10.1038/nbt.3703</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><mixed-citation publication-type="other">Brunk E, Sahoo S, Daniel Z, Altunkaya A, Prli&#x00107; A, Mih N, Sastry A, Preciat Gonzalez GA, Danielsdottir AD, Noronha A, Aurich M, Rose P, Fleming RMT, Thiele I, Palsson BO Recon 3D:&#x000a0;A resource enabling a three-dimensional view of gene variation in human metabolism (submitted)</mixed-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rahman</surname><given-names>SA</given-names></name><name><surname>Torrance</surname><given-names>G</given-names></name><name><surname>Baldacci</surname><given-names>L</given-names></name><name><surname>Cuesta</surname><given-names>SM</given-names></name><name><surname>Fenninger</surname><given-names>F</given-names></name><name><surname>Gopal</surname><given-names>N</given-names></name><name><surname>Choudhary</surname><given-names>S</given-names></name><name><surname>May</surname><given-names>JW</given-names></name><name><surname>Holliday</surname><given-names>GL</given-names></name><name><surname>Steinbeck</surname><given-names>C</given-names></name><name><surname>Thornton</surname><given-names>JM</given-names></name></person-group><article-title>Reaction Decoder Tool (RDT): extracting features from chemical reactions</article-title><source>Bioinformatics</source><year>2016</year><volume>32</volume><issue>13</issue><fpage>2065</fpage><lpage>2066</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btw096</pub-id><pub-id pub-id-type="pmid">27153692</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>First</surname><given-names>EL</given-names></name><name><surname>Gounaris</surname><given-names>CE</given-names></name><name><surname>Floudas</surname><given-names>CA</given-names></name></person-group><article-title>Stereochemically consistent reaction mapping and identification of multiple reaction mechanisms through integer linear optimization</article-title><source>J Chem Inf Model</source><year>2012</year><volume>52</volume><fpage>84</fpage><lpage>92</lpage><pub-id pub-id-type="doi">10.1021/ci200351b</pub-id><pub-id pub-id-type="pmid">22098204</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><mixed-citation publication-type="other">ChemAxon:Standardizer, was used for structure canonicalization and transformation. J Chem *********, 2015, ChemAxon. <ext-link ext-link-type="uri" xlink:href="http://www.chemaxon.com">http://www.chemaxon.com</ext-link> (2015)</mixed-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kumar</surname><given-names>A</given-names></name><name><surname>Maranas</surname><given-names>CD</given-names></name></person-group><article-title>CLCA: maximum common molecular substructure queries within the MetRxn database</article-title><source>J Chem Inf Model</source><year>2014</year><volume>54</volume><issue>12</issue><fpage>3417</fpage><lpage>38</lpage><pub-id pub-id-type="doi">10.1021/ci5003922</pub-id><pub-id pub-id-type="pmid">25412255</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Latendresse</surname><given-names>M</given-names></name><name><surname>Malerich</surname><given-names>JP</given-names></name><name><surname>Travers</surname><given-names>M</given-names></name><name><surname>Karp</surname><given-names>PD</given-names></name></person-group><article-title>Accurate atom-mapping computation for biochemical reactions</article-title><source>J Chem Inf Model</source><year>2012</year><volume>52</volume><issue>11</issue><fpage>2970</fpage><lpage>82</lpage><pub-id pub-id-type="doi">10.1021/ci3002217</pub-id><pub-id pub-id-type="pmid">22963657</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kraut</surname><given-names>H</given-names></name><name><surname>Eiblmaier</surname><given-names>J</given-names></name><name><surname>Grethe</surname><given-names>G</given-names></name><name><surname>L&#x000f6;w</surname><given-names>P</given-names></name><name><surname>Matuszczyk</surname><given-names>H</given-names></name><name><surname>Saller</surname><given-names>H</given-names></name></person-group><article-title>Algorithm for reaction classification</article-title><source>J Chem Inf Model</source><year>2013</year><volume>53</volume><issue>11</issue><fpage>2884</fpage><lpage>95</lpage><pub-id pub-id-type="doi">10.1021/ci400442f</pub-id><pub-id pub-id-type="pmid">24102490</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tipton</surname><given-names>KF</given-names></name></person-group><article-title>Nomenclature Committee of the International Union of Biochemistry and Molecular Biology (NC-IUBMB). Enzyme nomenclature. Recommendations 1992. Supplement: corrections and additions</article-title><source>Eur J Biochem/FEBS</source><year>1994</year><volume>223</volume><issue>1</issue><fpage>1</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1111/j.1432-1033.1994.tb18960.x</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><mixed-citation publication-type="other">Anderson E, Veith GD, Weininger D (1987).&#x000a0;SMILES: A line notation and computerized interpreter for chemical structures. Duluth, MN: U.S. EPA, Environmental Research Laboratory-Duluth. Report No. EPA/600/M-87/021</mixed-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dalby</surname><given-names>A</given-names></name><name><surname>Nourse</surname><given-names>JG</given-names></name><name><surname>Hounshell</surname><given-names>WD</given-names></name><name><surname>Gushurst</surname><given-names>AKI</given-names></name><name><surname>Grier</surname><given-names>DL</given-names></name><name><surname>Leland</surname><given-names>BA</given-names></name><name><surname>Laufer</surname><given-names>J</given-names></name></person-group><article-title>Description of several chemical structure file formats used by computer programs developed at Molecular Design Limited</article-title><source>J Chem Inf Model</source><year>1992</year><volume>32</volume><issue>3</issue><fpage>244</fpage><lpage>255</lpage><pub-id pub-id-type="doi">10.1021/ci00007a012</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>First</surname><given-names>EL</given-names></name><name><surname>Gounaris</surname><given-names>CE</given-names></name><name><surname>Floudas</surname><given-names>CA</given-names></name></person-group><article-title>Stereochemically consistent reaction mapping and identification of multiple reaction mechanisms through integer linear optimization</article-title><source>J Chem Inf Model</source><year>2012</year><volume>52</volume><issue>1</issue><fpage>84</fpage><lpage>92</lpage><pub-id pub-id-type="doi">10.1021/ci200351b</pub-id><pub-id pub-id-type="pmid">22098204</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><mixed-citation publication-type="other">ChemAxon: Standardizer, Was Used for Structure Canonicalization and Transformation. J Chem *********, 2015. ChemAxon (<ext-link ext-link-type="uri" xlink:href="http://www.chemaxon.com">http://www.chemaxon.com</ext-link>) (2015)</mixed-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kumar</surname><given-names>A</given-names></name><name><surname>Maranas</surname><given-names>CD</given-names></name></person-group><article-title>CLCA: maximum common molecular substructure queries within the MetRxn database</article-title><source>J Chem Inf Model</source><year>2014</year><volume>54</volume><issue>12</issue><fpage>3417</fpage><lpage>3438</lpage><pub-id pub-id-type="doi">10.1021/ci5003922</pub-id><pub-id pub-id-type="pmid">25412255</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Latendresse</surname><given-names>M</given-names></name><name><surname>Malerich</surname><given-names>JP</given-names></name><name><surname>Travers</surname><given-names>M</given-names></name><name><surname>Karp</surname><given-names>PD</given-names></name></person-group><article-title>Accurate atom-mapping computation for biochemical reactions</article-title><source>J Chem Inf Model</source><year>2012</year><volume>52</volume><issue>11</issue><fpage>2970</fpage><lpage>2982</lpage><pub-id pub-id-type="doi">10.1021/ci3002217</pub-id><pub-id pub-id-type="pmid">22963657</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Karp</surname><given-names>PD</given-names></name><name><surname>Paley</surname><given-names>S</given-names></name><name><surname>Romero</surname><given-names>P</given-names></name></person-group><article-title>The pathway tools software</article-title><source>Bioinformatics</source><year>2002</year><volume>18</volume><issue>Suppl 1</issue><fpage>225</fpage><lpage>232</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/18.suppl_1.S225</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wiechert</surname><given-names>W</given-names></name></person-group><article-title>13c metabolic flux analysis</article-title><source>Metab Eng</source><year>2001</year><volume>3</volume><issue>3</issue><fpage>195</fpage><lpage>206</lpage><pub-id pub-id-type="doi">10.1006/mben.2001.0187</pub-id><pub-id pub-id-type="pmid">11461141</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pey</surname><given-names>J</given-names></name><name><surname>Planes</surname><given-names>FJ</given-names></name><name><surname>Beasley</surname><given-names>JE</given-names></name></person-group><article-title>Refining carbon flux paths using atomic trace data</article-title><source>Bioinformatics</source><year>2014</year><volume>30</volume><issue>7</issue><fpage>975</fpage><lpage>980</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btt653</pub-id><pub-id pub-id-type="pmid">24273244</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Haraldsd&#x000f3;ttir</surname><given-names>HS</given-names></name><name><surname>Fleming</surname><given-names>RMT</given-names></name></person-group><article-title>Identification of Conserved Moieties in Metabolic Networks by Graph Theoretical Analysis of Atom Transition Networks</article-title><source>PLOS Computational Biology</source><year>2016</year><volume>12</volume><issue>11</issue><fpage>e1004999</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1004999</pub-id><pub-id pub-id-type="pmid">27870845</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Haraldsd&#x000f3;ttir</surname><given-names>HS</given-names></name><name><surname>Thiele</surname><given-names>I</given-names></name><name><surname>Fleming</surname><given-names>RM</given-names></name></person-group><article-title>Comparative evaluation of open source software for mapping between metabolite identifiers in metabolic network reconstructions: application to Recon 2</article-title><source>J Cheminform</source><year>2014</year><volume>6</volume><issue>1</issue><fpage>2</fpage><pub-id pub-id-type="doi">10.1186/1758-2946-6-2</pub-id><pub-id pub-id-type="pmid">24468196</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thiele</surname><given-names>I</given-names></name><name><surname>Swainston</surname><given-names>N</given-names></name><name><surname>Fleming</surname><given-names>RMT</given-names></name><name><surname>Hoppe</surname><given-names>A</given-names></name><name><surname>Sahoo</surname><given-names>S</given-names></name><name><surname>Aurich</surname><given-names>MK</given-names></name><name><surname>Haraldsdottir</surname><given-names>H</given-names></name><name><surname>Mo</surname><given-names>ML</given-names></name><name><surname>Rolfsson</surname><given-names>O</given-names></name><name><surname>Stobbe</surname><given-names>MD</given-names></name><name><surname>Thorleifsson</surname><given-names>SG</given-names></name><name><surname>Agren</surname><given-names>R</given-names></name><name><surname>B&#x000f6;lling</surname><given-names>C</given-names></name><name><surname>Bordel</surname><given-names>S</given-names></name><name><surname>Chavali</surname><given-names>AK</given-names></name><name><surname>Dobson</surname><given-names>P</given-names></name><name><surname>Dunn</surname><given-names>WB</given-names></name><name><surname>Endler</surname><given-names>L</given-names></name><name><surname>Hala</surname><given-names>D</given-names></name><name><surname>Hucka</surname><given-names>M</given-names></name><name><surname>Hull</surname><given-names>D</given-names></name><name><surname>Jameson</surname><given-names>D</given-names></name><name><surname>Jamshidi</surname><given-names>N</given-names></name><name><surname>Jonsson</surname><given-names>JJ</given-names></name><name><surname>Juty</surname><given-names>N</given-names></name><name><surname>Keating</surname><given-names>S</given-names></name><name><surname>Nookaew</surname><given-names>I</given-names></name><name><surname>Le Nov&#x000e8;re</surname><given-names>N</given-names></name><name><surname>Malys</surname><given-names>N</given-names></name><name><surname>Mazein</surname><given-names>A</given-names></name><name><surname>Papin</surname><given-names>JA</given-names></name><name><surname>Price</surname><given-names>ND</given-names></name><name><surname>Selkov</surname><given-names>E</given-names><suffix>Sr</suffix></name><name><surname>Sigurdsson</surname><given-names>MI</given-names></name><name><surname>Simeonidis</surname><given-names>E</given-names></name><name><surname>Sonnenschein</surname><given-names>N</given-names></name><name><surname>Smallbone</surname><given-names>K</given-names></name><name><surname>Sorokin</surname><given-names>A</given-names></name><name><surname>van Beek</surname><given-names>JHGM</given-names></name><name><surname>Weichart</surname><given-names>D</given-names></name><name><surname>Goryanin</surname><given-names>I</given-names></name><name><surname>Nielsen</surname><given-names>J</given-names></name><name><surname>Westerhoff</surname><given-names>HV</given-names></name><name><surname>Kell</surname><given-names>DB</given-names></name><name><surname>Mendes</surname><given-names>P</given-names></name><name><surname>Palsson</surname><given-names>B&#x000d8;</given-names></name></person-group><article-title>A community-driven global reconstruction of human metabolism</article-title><source>Nat Biotech</source><year>2013</year><volume>31</volume><issue>5</issue><fpage>419</fpage><lpage>425</lpage><pub-id pub-id-type="doi">10.1038/nbt.2488</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kim</surname><given-names>S</given-names></name><name><surname>Thiessen</surname><given-names>PA</given-names></name><name><surname>Bolton</surname><given-names>EE</given-names></name><name><surname>Chen</surname><given-names>J</given-names></name><name><surname>Fu</surname><given-names>G</given-names></name><name><surname>Gindulyte</surname><given-names>A</given-names></name><name><surname>Han</surname><given-names>L</given-names></name><name><surname>He</surname><given-names>J</given-names></name><name><surname>He</surname><given-names>S</given-names></name><name><surname>Shoemaker</surname><given-names>BA</given-names></name><name><surname>Wang</surname><given-names>J</given-names></name><name><surname>Yu</surname><given-names>B</given-names></name><name><surname>Zhang</surname><given-names>J</given-names></name><name><surname>Bryant</surname><given-names>SH</given-names></name></person-group><article-title>PubChem substance and compound databases</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>44</volume><issue>D1</issue><fpage>1202</fpage><lpage>1213</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv951</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kanehisa</surname><given-names>M</given-names></name><name><surname>Goto</surname><given-names>S</given-names></name></person-group><article-title>KEGG: kyoto encyclopedia of genes and genomes</article-title><source>Nucleic Acids Res</source><year>2000</year><volume>28</volume><issue>1</issue><fpage>27</fpage><lpage>30</lpage><pub-id pub-id-type="doi">10.1093/nar/28.1.27</pub-id><pub-id pub-id-type="pmid">10592173</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hastings</surname><given-names>J</given-names></name><name><surname>de Matos</surname><given-names>P</given-names></name><name><surname>Dekker</surname><given-names>A</given-names></name><name><surname>Ennis</surname><given-names>M</given-names></name><name><surname>Harsha</surname><given-names>B</given-names></name><name><surname>Kale</surname><given-names>N</given-names></name><name><surname>Muthukrishnan</surname><given-names>V</given-names></name><name><surname>Owen</surname><given-names>G</given-names></name><name><surname>Turner</surname><given-names>S</given-names></name><name><surname>Williams</surname><given-names>M</given-names></name><name><surname>Steinbeck</surname><given-names>C</given-names></name></person-group><article-title>The ChEBI reference database and ontology for biologically relevant chemistry: enhancements for 2013</article-title><source>Nucleic Acids Res</source><year>2013</year><volume>41</volume><issue>D1</issue><fpage>456</fpage><lpage>463</lpage><pub-id pub-id-type="doi">10.1093/nar/gks1146</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sud</surname><given-names>M</given-names></name><name><surname>Fahy</surname><given-names>E</given-names></name><name><surname>Cotter</surname><given-names>D</given-names></name><name><surname>Brown</surname><given-names>A</given-names></name><name><surname>Dennis</surname><given-names>EA</given-names></name><name><surname>Glass</surname><given-names>CK</given-names></name><name><surname>Merrill</surname><given-names>AH</given-names></name><name><surname>Murphy</surname><given-names>RC</given-names></name><name><surname>Raetz</surname><given-names>CRH</given-names></name><name><surname>Russell</surname><given-names>DW</given-names></name><name><surname>Subramaniam</surname><given-names>S</given-names></name></person-group><article-title>LMSD: LIPID MAPS structure database</article-title><source>Nucleic Acids Res</source><year>2007</year><volume>35</volume><issue>Database issue</issue><fpage>52732</fpage></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Forster</surname><given-names>M</given-names></name><name><surname>Pick</surname><given-names>A</given-names></name><name><surname>Raitner</surname><given-names>M</given-names></name><name><surname>Schreiber</surname><given-names>F</given-names></name><name><surname>Brandenburg</surname><given-names>FJ</given-names></name></person-group><article-title>The system architecture of the BioPath system</article-title><source>Silico Biol (Gedrukt)</source><year>2002</year><volume>2</volume><issue>3</issue><fpage>415</fpage><lpage>426</lpage></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Williams</surname><given-names>AJ</given-names></name><name><surname>Tkachenko</surname><given-names>V</given-names></name><name><surname>Golotvin</surname><given-names>S</given-names></name><name><surname>Kidd</surname><given-names>R</given-names></name><name><surname>McCann</surname><given-names>G</given-names></name></person-group><article-title>ChemSpider&#x02014;building a foundation for the semantic web by hosting a crowd sourced databasing platform for chemistry</article-title><source>J Cheminform</source><year>2010</year><volume>2</volume><issue>Suppl 1</issue><fpage>16</fpage><pub-id pub-id-type="doi">10.1186/1758-2946-2-S1-O16</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wishart</surname><given-names>DS</given-names></name><name><surname>Tzur</surname><given-names>D</given-names></name><name><surname>Knox</surname><given-names>C</given-names></name><name><surname>Eisner</surname><given-names>R</given-names></name><name><surname>Guo</surname><given-names>AC</given-names></name><name><surname>Young</surname><given-names>N</given-names></name><name><surname>Cheng</surname><given-names>D</given-names></name><name><surname>Jewell</surname><given-names>K</given-names></name><name><surname>Arndt</surname><given-names>D</given-names></name><name><surname>Sawhney</surname><given-names>S</given-names></name><name><surname>Fung</surname><given-names>C</given-names></name><name><surname>Nikolai</surname><given-names>L</given-names></name><name><surname>Lewis</surname><given-names>M</given-names></name><name><surname>Coutouly</surname><given-names>M-A</given-names></name><name><surname>Forsythe</surname><given-names>I</given-names></name><name><surname>Tang</surname><given-names>P</given-names></name><name><surname>Shrivastava</surname><given-names>S</given-names></name><name><surname>Jeroncic</surname><given-names>K</given-names></name><name><surname>Stothard</surname><given-names>P</given-names></name><name><surname>Amegbey</surname><given-names>G</given-names></name><name><surname>Block</surname><given-names>D</given-names></name><name><surname>Hau</surname><given-names>DD</given-names></name><name><surname>Wagner</surname><given-names>J</given-names></name><name><surname>Miniaci</surname><given-names>J</given-names></name><name><surname>Clements</surname><given-names>M</given-names></name><name><surname>Gebremedhin</surname><given-names>M</given-names></name><name><surname>Guo</surname><given-names>N</given-names></name><name><surname>Zhang</surname><given-names>Y</given-names></name><name><surname>Duggan</surname><given-names>GE</given-names></name><name><surname>MacInnis</surname><given-names>GD</given-names></name><name><surname>Weljie</surname><given-names>AM</given-names></name><name><surname>Dowlatabadi</surname><given-names>R</given-names></name><name><surname>Bamforth</surname><given-names>F</given-names></name><name><surname>Clive</surname><given-names>D</given-names></name><name><surname>Greiner</surname><given-names>R</given-names></name><name><surname>Li</surname><given-names>L</given-names></name><name><surname>Marrie</surname><given-names>T</given-names></name><name><surname>Sykes</surname><given-names>BD</given-names></name><name><surname>Vogel</surname><given-names>HJ</given-names></name><name><surname>Querengesser</surname><given-names>L</given-names></name></person-group><article-title>HMDB: the Human Metabolome DataBase</article-title><source>Nucl Acids Res</source><year>2007</year><volume>35</volume><issue>suppl 1</issue><fpage>521</fpage><lpage>526</lpage><pub-id pub-id-type="doi">10.1093/nar/gkl923</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>WL</given-names></name><name><surname>Chen</surname><given-names>DZ</given-names></name><name><surname>Taylor</surname><given-names>KT</given-names></name></person-group><article-title>Automatic reaction mapping and reaction center detection</article-title><source>Wiley Interdiscip Rev Comput Mol Sci</source><year>2013</year><volume>3</volume><issue>6</issue><fpage>560</fpage><lpage>593</lpage><pub-id pub-id-type="doi">10.1002/wcms.1140</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schellenberger</surname><given-names>J</given-names></name><name><surname>Que</surname><given-names>R</given-names></name><name><surname>Fleming</surname><given-names>RMT</given-names></name><name><surname>Thiele</surname><given-names>I</given-names></name><name><surname>Orth</surname><given-names>JD</given-names></name><name><surname>Feist</surname><given-names>AM</given-names></name><name><surname>Zielinski</surname><given-names>DC</given-names></name><name><surname>Bordbar</surname><given-names>A</given-names></name><name><surname>Lewis</surname><given-names>NE</given-names></name><name><surname>Rahmanian</surname><given-names>S</given-names></name><name><surname>Kang</surname><given-names>J</given-names></name><name><surname>Hyduke</surname><given-names>DR</given-names></name><name><surname>Palsson</surname><given-names>B&#x000d8;</given-names></name></person-group><article-title>Quantitative prediction of cellular metabolism with constraint-based models: the COBRA Toolbox v2.0</article-title><source>Nat Protoc</source><year>2011</year><volume>6</volume><issue>9</issue><fpage>1290</fpage><lpage>1307</lpage><pub-id pub-id-type="doi">10.1038/nprot.2011.308</pub-id><pub-id pub-id-type="pmid">21886097</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kotera</surname><given-names>M</given-names></name><name><surname>Okuno</surname><given-names>Y</given-names></name><name><surname>Hattori</surname><given-names>M</given-names></name><name><surname>Goto</surname><given-names>S</given-names></name><name><surname>Kanehisa</surname><given-names>M</given-names></name></person-group><article-title>Computational assignment of the EC numbers for genomic-scale analysis of enzymatic reactions</article-title><source>J Am Chem Soc</source><year>2004</year><volume>126</volume><issue>50</issue><fpage>16487</fpage><lpage>16498</lpage><pub-id pub-id-type="doi">10.1021/ja0466457</pub-id><pub-id pub-id-type="pmid">15600352</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pey</surname><given-names>J</given-names></name><name><surname>Planes</surname><given-names>FJ</given-names></name><name><surname>Beasley</surname><given-names>JE</given-names></name></person-group><article-title>Refining carbon flux paths using atomic trace data</article-title><source>Bioinformatics (Oxford, England)</source><year>2014</year><volume>30</volume><issue>7</issue><fpage>97580</fpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btt653</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><mixed-citation publication-type="other">O&#x02019;Boyle NM, Banck M, James CA, Morley C, Vandermeersch T, Hutchison GR, Weininger D, Murray-Rust P, Rzepa H, Murray-Rust P, Rzepa HS, Murray-Rust P, Rzepa H, Wright M, Murray-Rust P, Rzepa H, Holliday GL, Murray-Rust P, Rzepa HS, Fogel K, Bender A, Mussa HY, Glen RC, Reiling S, Filimonov D, Poroikov V, Borodina Y, Gloriozova T, Morgan HL, McKay BD, Gakh A, Burnett M, Trepalin SV, Yarkov AV, Pletnev IV, Gakh AA, Gakh AA, Burnett MN, Trepalin SV, Yarkov AV, Halgren T, Halgren T, Halgren T, Halgren T, Nachbar R, Halgren T, Andronico A, Randall A, Benz RW, Baldi P, Rappe A, Casewit C, Colwell K, Goddard W, Skiff WM, Wang J, Wolf RM, Caldwell JW, Kollman PA, Case DA, Wang J, Wang W, Kollman PA, Case DA, O NM, Martin K, Hoffman B, O N, Myers J, Allison T, Bittner S, Didier B, Frenklach M, Green W, Ho Y, Hewson J, Koegler W, Lansing C, Lind P, Alm M, Amini A, Shrimpton PJ, Muggleton SH, Sternberg MJE, Arbor S, Marshall GR, Huang Z, Huan J, Smalter A, Lushington GH, Cheng T, Li Q, Wang Y, Bryant SH Mihaleva VV, Verhoeven HA, de Vos RCH, Hall RD, van Ham R, Bas DC, Rogers DM, Jensen JH, Fabian L, Brock CP, Dehmer M, Barbarini N, Varmuza K, Graber A, Langham JJ, Jain AN, Fontaine F, Pastor M, Zamora I, Konyk M, Leon AD Dumontier M, Kogej T, Engkvist O, Blomberg N, Muresan S, Reyn&#x000e8;s C, Host H, Camproux A-C, Laconde G, Leroux F, Mazars A, Deprez B, Fahraeus R, Villoutreix BO, Sperandio O, Lagorce D, Pencheva T, Villoutreix BO, Miteva MA, G&#x000f3;mez MJ, Pazos F, Guijarro FJ, de Lorenzo V, Valencia A, Kazius J, Nijssen S, Kok J, B&#x000e4;ck T, IJzerman AP, O NM, Br&#x000fc;stle M, Buehler M, Dodson J, van Duin A, Bullock CW, Jacob RB, McDougal OM, Hampikian G, Andersen T, Jiang X, Kumar K, Hu X, Wallqvist A, Reifman J, Lagorce D, Sperandio O, Galons H, Miteva MA, Villoutreix BO, Maunz A, Helma C, Kramer S, Maunz A, Helma C, Kramer S, Helma C, Meineke MA, Vardeman CF, Lin T, Fennell CJ, Gezelter JD, Tosco P, Balle T, Tosco P, Balle T, Filippov IV, Nicklaus MC, Koes DR, Camacho CJ, Jacob CR, Beyhan SM, Bulo RE, Gomes ASP, G&#x000f6;tz AW, Kiewisch K, Sikkema J, Visscher L, Green WH, Allen JW, Ashcraft RW, Beran GJ, Class CA, Gao C, Goldsmith CF, Harper MR, Jalan A, Magoon GR, Matheu DM, Merchant SS, Mo JD, Petway S, Raman S, Sharma S, Song J, Geem KMV, Wen J, West RH, Wong A, Wong H-W, Yelvington PE, Yu J, Karwath A, Raedt LD, Lonie DC, Zurek E, Zonta N, Grimstead IJ, Avis NJ, Brancale A, Chen JH, Linstead E, Swamidass SJ, Wang D, Baldi P, Backman TWH, Cao Y, Girke T, Ahmed J, Worth CL, Thaben P, Matzig C, Blasse C, Dunkel M, Preissner R, Miteva MA, Guyon F, Tuffery P, Sharman JL, Mpamhanga CP, Spedding M, Germain P, Staels B, Dacquet C, Laudet V, Harmar AJ, Esposito R, Ermondi G, Caron G, Wallach I, Lilien R, Poater A, Cosenza B, Correa A, Giudice S, Ragone F, Scarano V, Cavallo L, Yan B-B, Xue M-Z, Xiong B, Liu K, Hu D-Y, Shen J-K, Rydberg P, Gloriam DE, Olsen L, Ingsriswang S, Pacharawongsakda E, Bauer RA, Bourne PE, Formella A, Frommel C, Gille C, Goede A, Guerler A, Hoppe A, Knapp EW, Poschel T, Schmidt U, Struck S, Gruening B, Hossbach J, Jaeger IS, Parol R, Lindequist U, Teuscher E, Preissner R, Bauer RA, Gunther S, Jansen D, Heeger C, Thaben PF, Preissner R, Ahmed J, Preissner S, Dunkel M, Worth CL, Eckert A, Preissner R, Kuhn M, Szklarczyk D, Franceschini A, Campillos M, von Mering C, Jensen LJ, Beyer A, Bork P, Tetko IV, Gasteiger J, Todeschini R, Mauri A, Livingstone D, Ertl P, Palyulin VA, Radchenko EV, Zefirov NS, Makarenko AS, Sperandio O, Petitjean M, Tuffery P (2011) Open babel: an open chemical toolbox. J Cheminform 3(1):33. doi:10.1186/1758-2946-3-33</mixed-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fleming</surname><given-names>RMT</given-names></name><name><surname>Maes</surname><given-names>CM</given-names></name><name><surname>Saunders</surname><given-names>MA</given-names></name><name><surname>Ye</surname><given-names>Y</given-names></name><name><surname>Palsson</surname><given-names>B&#x000d8;</given-names></name></person-group><article-title>A variational principle for computing nonequilibrium fluxes and potentials in genome-scale biochemical networks</article-title><source>J Theor Biol</source><year>2012</year><volume>292</volume><issue>71&#x02013;77</issue><fpage>2014</fpage></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Clayden</surname><given-names>J</given-names></name><name><surname>Greeves</surname><given-names>N</given-names></name><name><surname>Warren</surname><given-names>S</given-names></name><name><surname>Wothers</surname><given-names>P</given-names></name></person-group><source>Organic chemistry</source><year>2000</year><edition>1</edition><publisher-loc>Oxford</publisher-loc><publisher-name>Oxford University Press</publisher-name></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Loudon</surname><given-names>GM</given-names></name><name><surname>Parise</surname><given-names>J</given-names></name></person-group><source>Organic chemistry</source><year>2016</year><publisher-loc>Greenwood Village</publisher-loc><publisher-name>Roberts and Company Publishers</publisher-name></element-citation></ref></ref-list></back></article>