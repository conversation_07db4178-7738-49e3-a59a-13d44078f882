<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.0 20120330//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//NLM//DTD Journal Publishing DTD v2.0 20040830//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName journalpublishing.dtd?><?SourceDTD.Version 2.0?><?ConverterInfo.XSLTName jp2nlmx2.xsl?><?ConverterInfo.Version 2?><front><journal-meta><journal-id journal-id-type="nlm-ta">Evol Appl</journal-id><journal-id journal-id-type="iso-abbrev">Evol Appl</journal-id><journal-id journal-id-type="publisher-id">eva</journal-id><journal-title-group><journal-title>Evolutionary Applications</journal-title></journal-title-group><issn pub-type="ppub">1752-4571</issn><issn pub-type="epub">1752-4571</issn><publisher><publisher-name>BlackWell Publishing Ltd</publisher-name><publisher-loc>Oxford, UK</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">4055175</article-id><article-id pub-id-type="doi">10.1111/eva.12151</article-id><article-categories><subj-group subj-group-type="heading"><subject>Original Articles</subject></subj-group></article-categories><title-group><article-title>Evidence that hepatitis C virus genome partly controls infection outcome</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Hartfield</surname><given-names>Matthew</given-names></name><xref ref-type="aff" rid="au1">1</xref></contrib><contrib contrib-type="author"><name><surname>Bull</surname><given-names>Rowena</given-names></name><xref ref-type="aff" rid="au2">2</xref></contrib><contrib contrib-type="author"><name><surname>White</surname><given-names>Peter A</given-names></name><xref ref-type="aff" rid="au2">2</xref></contrib><contrib contrib-type="author"><name><surname>Lloyd</surname><given-names>Andrew</given-names></name><xref ref-type="aff" rid="au2">2</xref></contrib><contrib contrib-type="author"><name><surname>Luciani</surname><given-names>Fabio</given-names></name><xref ref-type="aff" rid="au2">2</xref><xref ref-type="author-notes" rid="fn1">*</xref></contrib><contrib contrib-type="author"><name><surname>Alizon</surname><given-names>Samuel</given-names></name><xref ref-type="aff" rid="au1">1</xref><xref ref-type="author-notes" rid="fn1">*</xref></contrib><contrib contrib-type="author"><collab>on behalf of the HITS investigators</collab></contrib><aff id="au1"><label>1</label><institution>Laboratoire MIVEGEC (UMR CNRS 5290 IRD 224 UM1 UM2)</institution><addr-line>Montpellier Cedex 5, France</addr-line></aff><aff id="au2"><label>2</label><institution>Infection and Inflammation Research Centre, School of Medical Sciences, Faculty of Medicine, University of New South Wales</institution><addr-line>Sydney, Australia</addr-line></aff></contrib-group><author-notes><corresp id="cor1">Matthew Hartfield, Laboratoire MIVEGEC (UMR CNRS 5290 IRD 224 UM1 UM2), 911 avenue Agropolis, B.P. 64501, 34394 Montpellier Cedex 5, France. Tel.: +33 *********; fax: +33 *********; e-mail: <email><EMAIL></email>; <email><EMAIL></email></corresp><fn id="fn1"><label>*</label><p>These authors contributed equally to this work.</p></fn></author-notes><pub-date pub-type="ppub"><month>5</month><year>2014</year></pub-date><pub-date pub-type="epub"><day>26</day><month>3</month><year>2014</year></pub-date><volume>7</volume><issue>5</issue><fpage>533</fpage><lpage>547</lpage><history><date date-type="received"><day>04</day><month>12</month><year>2013</year></date><date date-type="accepted"><day>07</day><month>1</month><year>2014</year></date></history><permissions><copyright-statement>&#x000a9; 2014 The Authors. <italic>Evolutionary Applications</italic> published by John Wiley &#x00026; Sons Ltd.</copyright-statement><copyright-year>2014</copyright-year><license license-type="open-access" xlink:href="http://creativecommons.org/licenses/by/3.0/"><license-p>This is an open access article under the terms of the Creative Commons Attribution License, which permits use, distribution and reproduction in any medium, provided the original work is properly cited.</license-p></license></permissions><abstract><p>Infection by hepatitis C virus (HCV) leads to one of two outcomes; either the infection resolves within approximately 6 months or the virus can persist indefinitely. Host genetics are known to affect the likelihood of clearance or persistence. By contrast, the importance of the virus genotype in determining infection outcome is unknown, as quantifying this effect traditionally requires well-characterized transmission networks, which are rare. Extending phylogenetic approaches previously developed to estimate the virus control over set-point viral load in HIV-1 infections, we simulate inheritance of a binary trait along a phylogenetic tree, use this data to quantify how infection outcomes cluster and ascertain the effect of virus genotype on these. We apply our method to the Hepatitis C Incidence and Transmission Study in prisons (HITS-p) data set from Australia, as this cohort prospectively identified incident cases including viraemic subjects who ultimately clear the virus, thus providing us with a unique collection of sequences from clearing infections. We detect significant correlations between infection outcome and virus distance in the phylogeny for viruses of Genotype 1, with estimates lying at around 67%. No statistically significant estimates were obtained for viruses of Genotype 3a.</p></abstract><kwd-group><kwd>hepatitis C virus</kwd><kwd><italic>IL28B</italic> SNPs</kwd><kwd>infection simulation</kwd><kwd>phylogenetic analysis</kwd><kwd>trait correlation</kwd></kwd-group></article-meta></front><body><sec><title>Introduction</title><p>The hepatitis C virus (HCV; family <italic>Flaviviridae</italic>, genus <italic>Hepacivirus</italic>) is estimated to infect around 170 million people worldwide, and is a major cause of chronic liver disease (Simmonds <xref rid="b44" ref-type="bibr">2004</xref>). Due to the ensuing health burden caused by HCV, there is a considerable research focus on understanding how the host and the virus genetics shape the infection outcome (Ploss and Dubuisson <xref rid="b37" ref-type="bibr">2012</xref>). There is also a vast array of new therapies being developed that target both host (e.g. cyclophilin inhibitors, microRNA antagomirs (Janssen et al. <xref rid="b20" ref-type="bibr">2013</xref>)) and viral phenotypes (direct-acting antiviral agents targeting the HCV protease and polymerase; Ploss and Dubuisson <xref rid="b37" ref-type="bibr">2012</xref>).</p><p>If untreated, acute HCV infections mostly result in one of two infection outcomes. Either the virus is eliminated within 6 months (a &#x02018;cleared&#x02019; infection) or it develops into a &#x02018;chronic&#x02019; infection that generally persists for life unless cured by antiviral therapy. Spontaneous clearance can occur after 6 months, albeit with a very low probability (much &#x0003c;1% overall; Micallef et al. <xref rid="b30" ref-type="bibr">2006</xref>; Grebely et al. <xref rid="b13" ref-type="bibr">2012</xref>, <xref rid="b14" ref-type="bibr">2014</xref>). Genetic polymorphisms in the promoter region of the host <italic>IL28B</italic> gene that correlate with an increased probability of HCV clearance have been detected (Ge et al. <xref rid="b11" ref-type="bibr">2009</xref>; Thomas et al. <xref rid="b50" ref-type="bibr">2009</xref>; Rauch et al. <xref rid="b42" ref-type="bibr">2010</xref>). It is clear that certain regions of the viral polyprotein are targeted by the host immune responses and are therefore involved in clearance. Specifically, the envelope (<italic>E</italic>) region of the HCV genome harbours a hypervariable region, which is a target for neutralizing antibody response, which in turn has been associated with driving viral evolution (Weiner et al. <xref rid="b53" ref-type="bibr">1992</xref>; Kumar et al. <xref rid="b22" ref-type="bibr">1993</xref>; Fafi-Kremer et al. <xref rid="b8" ref-type="bibr">2010</xref>; Liu et al. <xref rid="b27" ref-type="bibr">2010</xref>). There are also multiple epitopes within the nonstructural (NS) region of the genome that are targeted by cellular immune response (CD8 cytotoxic T cells), giving rise to virus escape variants (Kuntzen et al. <xref rid="b23" ref-type="bibr">2007</xref>; Osburn et al. <xref rid="b32" ref-type="bibr">2010</xref>). These findings argue that an optimal host immune response clears HCV quickly, leading to a progressive purging of virus variants during acute infection. It is currently unclear to what extent strains isolated from cleared or chronic infections differ in their evolutionary dynamics. Farci et al. (<xref rid="b9" ref-type="bibr">2000</xref>) found that HCV strains detected during the chronic phase tend to have higher genetic diversity in the E region compared with strains detected in subjects who cleared the infection, leading to clustering of similar infection outcomes along a phylogeny. However, a recent study (Liu et al. <xref rid="b28" ref-type="bibr">2012</xref>) observed the opposite result, with clearing strains exhibiting higher evolutionary rates in the E2-HVR1 region than chronic strains.</p><p>Hepatitis C virus is divided into six major genotypes (1&#x02013;6) and several closely related subtypes (for example, 1a or 1b) that differ approximately by 30% in nucleotide and amino acid sequences (Simmonds et al. <xref rid="b45" ref-type="bibr">2005</xref>). The six genotypes vary in predominance and are dependent upon geographical location (Simmonds et al. <xref rid="b45" ref-type="bibr">2005</xref>). For example, Genotype 1 is most prevalent in the USA and Europe, while Genotype 3 is more common in Asia and South America, and Australia has both genotypes 1 and 3. There also exist epidemiological differences between the genotypes, with Pybus et al. (<xref rid="b38" ref-type="bibr">2001</xref>) showing that the 1a and 1b subtypes of HCV spread faster than others.</p><p>Overall, it appears likely that specific HCV infection outcomes result from the interlinked effects of the individual virus genome, host genetics and immune response status. However, the relative effects of all of these factors, especially virus genetics, are poorly known. It is hard to investigate the relative impact of each of these effects, especially the impact of genetically similar transmitted viruses, as acute infection is predominantly asymptomatic and the incubation period may be up to several months. Hence, the transmission chain is typically poorly characterized. Recently, phylogenetic methods have been used to determine the effect of a virus genome on the value of infection traits, such as viral load. The power of these methods lies in the fact that a phylogeny built on the sequences from viruses sampled from different individuals can approximate the transmission network between these hosts (Leitner et al. <xref rid="b25" ref-type="bibr">1996</xref>; Hu&#x000e9; et al. <xref rid="b19" ref-type="bibr">2004</xref>). Even though this was only an approximation of the real transmission network, Alizon et al. (<xref rid="b1" ref-type="bibr">2010</xref>) were able to show using phylogenetic comparative methods that up to 59% of the set-point viral load in HIV infections was attributable to virus genetics effects; this estimate is higher than some estimates obtained from known transmission pairs (Tang et al. <xref rid="b48" ref-type="bibr">2004</xref>; Hollingsworth et al. <xref rid="b18" ref-type="bibr">2010</xref>), but consistent with others (Hecht et al. <xref rid="b17" ref-type="bibr">2010</xref>).</p><p>In general, phylogenetic methods have been of considerable use in elucidating the epidemiological history of HCV infections. By estimating the population size of an HCV epidemic in Egypt, it was shown that the outbreak coincided with mass parenteral antischistosomal therapy (Pybus et al. <xref rid="b39" ref-type="bibr">2003</xref>; Drummond et al. <xref rid="b6" ref-type="bibr">2005</xref>; Stadler et al. <xref rid="b47" ref-type="bibr">2013</xref>), validating earlier epidemiological evidence (Frank et al. <xref rid="b10" ref-type="bibr">2000</xref>). Similarly, a phylogenetic analysis of the recombinant HCV form 2k/1b suggests that this variant has spread via infected blood transfusions in the former Soviet Union (Raghwani et al. <xref rid="b41" ref-type="bibr">2012</xref>). However, there is very little work using the phylogenetic comparative approach to determine the genetic effects of the infection itself.</p><p>Here, we extend the methods used in Alizon et al. (<xref rid="b1" ref-type="bibr">2010</xref>) to HCV, to measure the degree of clustering of discrete infection outcomes along a phylogeny. We describe a method to measure the virus control over the infection outcome for genetically similar HCV strains, which also accounts for phylogenetic and transmission uncertainty. Our virus control value is analogous to heritability in quantitative genetics (Visscher et al. <xref rid="b52" ref-type="bibr">2008</xref>) but applied to categorical traits. That is, the control value measures to what degree a virus genotype influences the probability that a specific infection outcome will be cleared or not, and also that this trait is passed on to its recipient. Our method functions by simulating a set of phylogeny tips (each of which corresponds to an infection outcome) by assuming different virus effects on trait outcomes. We then compare the tip distribution we observe with real data to the simulated tipset, to determine whether there is a virus effect on infection outcome. We additionally describe how the method can take into account confounding effects such as host genetics. These findings have applications in elucidating the causes of an HCV infection outcome, as well as providing evolutionary insights as to the selective forces behind specific infection outcomes.</p></sec><sec sec-type="materials|methods"><title>Materials and methods</title><sec><title>Ethics statement</title><p>Ethical approval was obtained from Human Research Ethics Committees of Justice Health (reference number GEN 31/05), New South Wales Department of Corrective Services (reference number 05/0884) and the University of New South Wales (reference number 05094), all located in Sydney, Australia. The project was approved by the relevant institutional review boards. All subjects provided written informed consent.</p></sec><sec><title>Subjects analysed in study</title><p>Hepatitis C virus genome data used in this study were obtained as part of the Australian &#x02018;Hepatitis C Incidence and Transmission Study in prisons (HITS-p) cohort (Dolan et al. <xref rid="b5" ref-type="bibr">2010</xref>; Teutsch et al. <xref rid="b49" ref-type="bibr">2010</xref>). From September 2005 to May 2009, adult prison inmates were recruited at 19 of 30 correctional centres housing adult male or females in New South Wales (NSW). The conditions to join the study were to report a lifetime history of injecting drug use (IDU); to be imprisoned within the last 12 months; and to have a serological test documenting negative anti-HCV antibody status during that time. Of the 500 subjects enrolled in HITS-p, this analysis included subjects who were HCV-RNA negative at enrolment and had completed at least one prospective follow-up visit while in prison (having either been continuously imprisoned or visited upon re-imprisonment after a period of release). A venous blood sample was collected at each interview to screen for HCV antibodies and viraemia (HCV-RNA test), as previously described (Dolan et al. <xref rid="b5" ref-type="bibr">2010</xref>). The average follow-up time between two consecutive interviews was 6 months.</p><p>Natural viral clearance was defined as two RNA-negative tests, while antibody-positive status in the subsequent 12 months following the last HCV-RNA-positive test. Within the HITS-p cohort, 134 subjects were found HCV antibody-positive, 94 at baseline and the remaining during the 5 years follow-up period.</p><p>Generally, it is hard to obtain virus sequences from subjects who ultimately cleared the infection, as the acute phase of the infection is typically asymptomatic. The HITS cohort therefore provides a unique opportunity for blood sampling in the acute phase, allowing detection of early viraemia in clearing infections. The number of sequences from clearing infections used in this study is therefore one of the highest number to have been analysed. There are only a limited number of publications reporting viral sequences and known disease outcome, including a similar number of clearers (Kasprowicz et al. <xref rid="b21" ref-type="bibr">2010</xref>; Liu et al. <xref rid="b28" ref-type="bibr">2012</xref>). However, these studies have not provided a large cohort of HCV-infected subjects with a closely related transmission network. Rather, these studies match case control samples for specific analyses, which are unsuitable for the measurement of phylogenetic relatedness between viral sequences. The HITS-p cohort is a prospective cohort of individuals infected with the same transmission route in closed setting. Hence, it is likely that the IDUs sampled in this cohort are infected by related viruses, which is though to improve the accuracy of phylogenetic models to approximate the transmission network. HITS-p also collects substantial risk behaviour data on each enrolled individual at each time point, as well as their genotype at the <italic>IL28B</italic> loci.</p></sec><sec><title>Sequences of E1-HVR1</title><p>The region encoding the last 171 bp of core, E1 and HVR1 (882 bp; nucleotides 723&#x02013;1604, with reference to HCV strain H77; GenBank accession number AF009606) was amplified by nested RT-PCR as previously described (Pham et al. <xref rid="b36" ref-type="bibr">2010</xref>). In brief, viral RNA was extracted from 140 &#x003bc;L of plasma using the QIAmp Viral RNA kit according to manufacturers&#x02019; instructions (Qiagen, Venlo, Limburg, the Netherlands). Five microlitres of extracted RNA was added to a 15 &#x003bc;L reaction mixture containing 10 &#x003bc;L of RT-PCR reaction mix (iScript One-Step RT-PCR with SYBR Green; Bio-Rad, Hercules, CA, USA), 500 n<sc>m</sc> of each primer (GV32 and GV33 in Pham et al. (<xref rid="b36" ref-type="bibr">2010</xref>)) and 0.4 &#x003bc;L of 50x iScript reverse-transcriptase enzyme (Bio-Rad). Cycling conditions included a 10-min reverse transcription step at 50&#x000b0;C; reverse-transcription inactivation at 95&#x000b0;C for 5 min; 15 cycles at 95&#x000b0;C for 30 s, 55&#x000b0;C for 30 s and 72&#x000b0;C for 1 min, followed by a final extension of 72&#x000b0;C for 7 min. The second-round PCR was prepared by adding 2 &#x003bc;L of first-round product to 18 &#x003bc;L of reaction mix containing iQ SYBR Green Supermix (Bio-Rad) and 500 n<sc>m</sc> of each primer (GV34 and GV35/GV36). The second-round cycling conditions included 95&#x000b0;C for 5 min and 40 cycles of 95&#x000b0;C for 30 s, 60&#x000b0;C for 30 s and 72&#x000b0;C for 1 min. The amplified products were identified by agarose gel electrophoresis. PCR products were purified and sequenced directly on an ABI 3730 DNA Analyzer (Applied Biosystems, Carlsbad, CA, USA) using dye-terminator chemistry.</p></sec><sec><title>Initial analysis of HCV genetic data</title><p>Sequences of the E1-HVR1 region of the HCV genome were available for 92 subjects infected with genotype 1a, 1b or 3a viruses. Of these, 19 were from subjects who ultimately naturally cleared the HCV infection (sequencing was difficult in clearing infections due to lower viral loads). For the study, we only used the earliest-obtained HCV sequence for each patient and discarded subsequent sequences. Preliminary analysis showed that within-host evolution contributed minimally to the phylogenetic relatedness between subjects, as all the longitudinal samples present in the cohort clustered with the subject itself (result not shown). Due to some incomplete sequencing, subsequent phylogenies were built using a 657 bp length of sequence, covering the core and E1 regions. Although Gray et al. (<xref rid="b12" ref-type="bibr">2011</xref>) found that this segment was hypervariable within hosts, they also showed that it evolved at a similar rate to the rest of the genome at the between-host level, so phylogenies should not reflect within-host selection at this segment.</p><p>We performed an initial analysis on the complete data set. However, to prevent confounding of any detected genetic signal with the HCV genotype, we subsequently extracted the genotypes from the two major observed subtypes and performed analysis on these separate data sets, which we denoted as &#x02018;Genotype 1&#x02019; (containing viruses of subtype 1a and 1b) and &#x02018;Genotype 3&#x02019; (subtype 3a; Fig. <xref ref-type="fig" rid="fig01">1</xref>). The application of these selection criteria resulted in 31 subjects in the Genotype 1, and 42 subjects in the Genotype 3 category with confirmed sequences amenable for further analyses. We decided to combine sequences of subtype 1a and 1b into the same data set, given their similarity and also because we did not find significant association of acute infections with subtype (<italic>P</italic> = 0.0619, Fisher's exact test).</p><fig id="fig01" position="float"><label>Figure 1</label><caption><p>Phylogenies of the two HCV data sets. &#x02018;Genotype 1&#x02019; (part A) consisted of viruses of subtype 1a or 1b, as denoted on the graph. &#x02018;Genotype 3&#x02019; (part B) consisted of viruses of subtype 3a. Short-term acute outcomes are denoted with an A, chronic outcomes with a C. Grey labels indicate strains where the host had the homozygous <italic>IL28B</italic>-917 SNP corresponding to increased clearance rate. A scale bar is also included that shows the number of substitutions per site. Phylogenies were compiled for this figure using PhyML (Guindon et al. <xref rid="b16" ref-type="bibr">2010</xref>), with a GTR model, gamma-distributed site heterogeneity, empirical nucleotide frequencies and invariable sites. See main text for further details.</p></caption><graphic xlink:href="eva0007-0533-f1"/></fig></sec><sec><title>Creation of phylogenetic trees</title><p>For each data set, we used <sc>beast</sc> v1.7.3 (Drummond et al. <xref rid="b7" ref-type="bibr">2012</xref>) to create a set of phylogenetic trees for use in our Bayesian analysis, to account for sampling error in the phylogeny. As the inference of phylogenetic signal for a trait can be very sensitive to the underlying phylogeny, then by sampling from the posterior distribution and performing the analysis over several trees, we minimized the influence that phylogenetic sensitivity had on the final result (Parker et al. <xref rid="b35" ref-type="bibr">2008</xref>). The collection date of the HCV sample, as obtained from the HITS-p cohort, was used to align tree tips. Using the <sc>jmodeltest</sc> software (Guindon and Gascuel <xref rid="b15" ref-type="bibr">2003</xref>; Darriba et al. <xref rid="b4" ref-type="bibr">2012</xref>), it was found that a GTR nucleotide model with estimated base frequencies, invariable sites and a Gamma site heterogeneity model with four categories (GTR + I + &#x00393;) gave the lowest Bayesian information criterion (BIC) score.</p><p>We estimated phylogenies by comparing outputs consisting of a constant population size coalescent model; exponential population growth; or a Bayesian Skyline model with 10 groups. Using the Path-O-Gen software (version 1.4, available from <ext-link ext-link-type="uri" xlink:href="http://tree.bio.ed.ac.uk/software/pathogen/">http://tree.bio.ed.ac.uk/software/pathogen/</ext-link>), we did not find a positive correlation between the sampling date and root-to-tip divergence for either subtype. Both estimates were not significantly different from zero (correlation coefficient for Genotype 1 was &#x02212;0.220, <italic>P</italic> = 0.398; for Genotype 3, it was &#x02212;0.0863, <italic>P</italic> = 0.600). Therefore, it appeared that no molecular clock signal is present in the data. We therefore proceeded using a strict clock model as the simplest possible scenario and checked afterwards that the estimated substitution rates were in line with previous findings for HCV. The outputs seemed to make sense, as substitution rate estimates that we found lied around 10<sup>&#x02212;3</sup>, as found by Gray et al. (<xref rid="b12" ref-type="bibr">2011</xref>). The log-likelihoods of the exponential model and the Bayesian Skyline models were not significantly different when individually compared with the constant-size model, as inferred using a likelihood ratio test. Therefore, we used the outputs from the constant-size coalescent population model for subsequent analyses.</p><p>There could be a concern that since we sampled from a densely infected population, then using a coalescent model would provide a skewed phylogeny with branches that are more closely related than they actually are. As a precaution, we also produced phylogenies using a birth&#x02013;death model, as opposed to a coalescent model (Stadler et al. <xref rid="b46" ref-type="bibr">2012</xref>). We will compare results obtained from different methods where necessary; however, we generally found the same quantitative results as those obtained from a coalescent tree.</p><p>We set each MCMC to run for 10<sup>9</sup> iterations, with parameters logged every 100 000 runs. Each parameter in the MCMC analysis exceeded an effective sampling size of 600.</p></sec><sec><title>Generating simulation data of categorical trait control</title><p>In this study, we were interested in estimating how likely it is for a binary trait, such as infection outcome, to be correlated between related virus strains. There currently exists several Bayesian methods to infer genetic relatedness between different tip outcomes from a phylogenetic tree [summarized in Parker et al. (<xref rid="b35" ref-type="bibr">2008</xref>)]. While these methods can infer the likelihood of whether a trait is clustered in a phylogeny, they cannot currently quantify the degree to which categorical infection traits are &#x02018;inherited&#x02019; by the infection recipient, as well as account for confounding factors. Theoretically, if a trait of interest is more likely to be &#x02018;inherited&#x02019; from one infection to the next, then it should cluster around specific tips in the tree. To this end, we constructed a method to simulate inheritance of a trait along the phylogeny, and then link these predicted outcomes to existing statistics that determine the correlation between tip outcomes and phylogenetic distance.</p><p>A schematic of these simulations is shown in Fig. <xref ref-type="fig" rid="fig02">2</xref>. For the simulations, we chose 500 trees at random from the posterior distribution as produced by the <sc>beast</sc> analysis for a certain genotype, to make the simulations tractable. For each of these trees, we randomly simulated the trait values for the tree, based on 20 different correlation values (denoted <italic>c</italic>) that were chosen from a uniform distribution between 0 and 1. A value of <italic>c</italic> = 0 means that there is no trait correlation between related strains, so daughter infections are assigned a trait value at random. On the other hand, a value of <italic>c</italic> = 1 means that the &#x02018;offspring&#x02019; of an infection caused by a specific strain is certain to retain the same outcome as its &#x02018;parent&#x02019; infection. For each of these 20 chosen values of <italic>c</italic>, we started by either assigning the trait of interest (such as HCV infections that clear rapidly) to the root with probability <italic>p</italic>, where <italic>p</italic> is the observed frequency of the trait in the actual data set or the alternative trait (such as chronic HCV infection) with probability 1&#x02013;<italic>p</italic>. From this tip, we went through each node of the tree in turn. At each new node, we assigned a trait to this node. This trait is either that of its ancestor with control probability <italic>c</italic>. With probability 1 &#x02013; <italic>c</italic>, the outcome is unrelated to its ancestor, so instead the trait was chosen from a Bernoulli distribution with mean <italic>p</italic> (that was observed in the real data) and subsequently assigned to the node. For each branch, we repeated this process for generations, which were chosen from the following distribution:</p><p>g = 1 + Poisson (<italic>L</italic>/<italic>T</italic><sub><italic>g</italic></sub>)</p><fig id="fig02" position="float"><label>Figure 2</label><caption><p>Schematic of the virus control simulations. The root has an initial state assigned to it; this state is certainly passed on to one of the offspring genotypes with probability <italic>c</italic> (case A), or is not passed on with probability 1 &#x02013; <italic>c</italic> (case B). In this latter case, the offspring state is assigned a value drawn from a Bernoulli trial, where <italic>p</italic> is the observed frequency of the trait of interest from the data set.</p></caption><graphic xlink:href="eva0007-0533-f2"/></fig><p>Here, <italic>L</italic> is the length of the branch between a parent and daughter node in years, and <italic>T</italic><sub>g</sub> the average length of infection, which was set to 3 years, as based on estimates using surveillance data and molecular sequences (Magiorkinis et al. <xref rid="b29" ref-type="bibr">2013</xref>). Therefore, at least one generation was assumed per branch, along with an estimated number of subsequent transmissions that were not captured by the existing phylogeny (Shirreff et al. <xref rid="b43" ref-type="bibr">2013</xref>). This process was repeated until all nodes had been assigned values, including all tree tips. Simulations were executed using R (R Development Core Team <xref rid="b40" ref-type="bibr">2008</xref>).</p><p>After we simulated infection outcomes, we calculated the associated clustering statistics for a simulated outcome. To achieve this, we used the &#x02018;ace&#x02019; function, provided as part of the &#x02018;ape&#x02019; package (Analyses of Phylogenetics and Evolution) for R (Paradis et al. <xref rid="b34" ref-type="bibr">2004</xref>). This function implements Pagel's maximum-likelihood method (Pagel <xref rid="b33" ref-type="bibr">1994</xref>) for estimating the rate of correlated evolution between several discrete traits along a phylogeny. It does so by estimating the transition probabilities <italic>q</italic><sub>0,1</sub> and <italic>q</italic><sub>1,0</sub> between the two traits; the probability that a &#x02018;clearer&#x02019; genotype will switch to being a chronic genotype, and <italic>vice versa</italic>. There are two probabilities because it can be easier to switch from one state to the other, hence, <italic>q</italic><sub>0,1</sub> is not necessarily equal to <italic>q</italic><sub>1,0</sub>. After estimating these transition probabilities, the inputted control value was saved, along with estimates of the switching rates (with standard errors) and the log-likelihood for the estimates. Note that in order for the analysis to proceed, there needed to be heterogeneity in trait outcomes. That is, there has to be at least one tip of each category present at the end of the simulation, to cause transitions between the two traits. If all the simulated tips were of one type only, we artificially introduced an outcome of the opposite type at a random tip on the phylogeny.</p><p>Overall, we ended up with 500 trees &#x000d7; 20 trait control values = 10 000 simulated outcomes for each genotype of interest. Generally, the final outcome corresponded well with actual data; with simulations along the Genotype 1 coalescent phylogeny, the average final number of clearers equalled 6.99, which was not significantly different from the true value of 7 (two-sided <italic>t</italic> = &#x02212;0.298, <italic>P</italic> = 0.766). The total number of clearers appeared to fluctuate around 7 for most correlation values, but if the correlation value used as an input approached 1, then this total number could reach high values (so all tips would be assigned the infrequent trait), or more likely to go to zero. This behaviour is due to the final trait allocations being more likely to reflect the initial state assigned to the tree root (<xref ref-type="supplementary-material" rid="SD1">Fig. S1A</xref>). After removing extremely high outlier values (those that lie outside the 97.5% upper quartile), the estimated switching rates were found to be significantly negatively correlated with the input trait correlation values (for <italic>q</italic><sub>1,0</sub>, <italic>F</italic><sub>1,9748</sub> = 135.7, <italic>P</italic> &#x0003c; 2.2 &#x000d7; 10<sup>&#x02013;16</sup>; for <italic>q</italic><sub>0,1</sub>, <italic>F</italic><sub>1,9748</sub> = 177.4, <italic>P</italic> &#x0003c; 2.2 &#x000d7; 10<sup>&#x02013;16</sup>; see also <xref ref-type="supplementary-material" rid="SD1">Fig. S1B,C</xref>). This supported our intuition that with high levels of virus control on trait outcomes, similar outcomes tended to congregate around closely related tips. These results were quantitatively similar for the Genotype 3 data set, and also to results produced using a birth&#x02013;death phylogeny.</p></sec><sec><title>Using simulation data to quantify virus control over traits</title><p>To estimate the control of the HCV virus genotype over the infection trait, we estimated the switching rates for the true data set, for 200 trees sampled from the <sc>beast</sc> analysis. This gave us a range of true switching rates for the actual data. These transition values are important in their own right, as they notify to what degree infection outcomes cluster on the phylogeny. Yet they are different from the main quantity of interest, which is the extent to which the trait is controlled by the virus. Therefore, using these estimates, we then selected simulation data that jointly generated the same switching rates and then obtained the associated correlation values that were used to generate these same values (Fig. <xref ref-type="fig" rid="fig03">3</xref>). This methodology shares many similarities with the Approximate Bayesian Computing (ABC) approach. In both cases, we first simulate many response values (here, the switching rates and log-likelihoods) using many sets of input values (here the virus control values), and then, when we observe such response values in nature, work our way back to infer what the input values are. One difference though is that in ABC the precision has to be set, whereas here we use the quantiles of the real data to generate the confidence intervals for our estimated data.</p><fig id="fig03" position="float"><label>Figure 3</label><caption><p>Schematic showing how to estimate virus control <italic>c</italic> from simulation data. After estimating the switching rate (<italic>q</italic><sub>1,0</sub>) from the true data set (<italic>y</italic>-axis), the simulation points are found that also lie within this range (grey horizontal lines). The associated control values are then found from these points, so the median virus control value is then obtained along with confidence interval limits (black lines on the <italic>x</italic>-axis). Note that for this figure, we only plot a sample of all points on a log scale for clarity of presentation.</p></caption><graphic xlink:href="eva0007-0533-f3"/></fig><p>To test for significance, we randomized the tips 1000 times and obtained the estimated control value for each random tipset. We denoted a control value as being significant if more than 95% of the randomizations were lower than the true value, as randomization should break up associations between tips, thereby decreasing the expected virus control over the trait. The fact that significance was determined using maximum-likelihood estimates of the switching rates meant that these values were optimized based on the underlying phylogenetic tree. Therefore, the randomization procedure tested for clustering between similar traits, while accounting for potential confounding effects of the tree size and shape.</p></sec><sec><title>Testing for virus control over infection outcome while conditioning on host's <italic>IL28B</italic> status</title><p>Estimates of infection outcome correlation could have been confounded by the presence of SNPs at the <italic>IL28B</italic> locus that correlate with increased clearance rate. In these cases, we wanted to obtain estimates of the trait correlation while accounting for external factors. To achieve this, we calculated switching rates and log-likelihood values using the <sc>bayestraits</sc> package (available from <ext-link ext-link-type="uri" xlink:href="http://www.evolution.rdg.ac.uk">http://www.evolution.rdg.ac.uk</ext-link>), which executes the maximum-likelihood analysis of Pagel (<xref rid="b33" ref-type="bibr">1994</xref>) while accounting for secondary traits that can affect the evolution of the main trait. We first checked that the same results as the &#x02018;ace&#x02019; package for R were obtained for the switching rates if we only investigate one trait, which they did. BayesTraits can thus be used to calculate the estimated transition rates of infection outcome, given that there is a possible covariation with the host's <italic>IL28B</italic> genetic status.</p><p>From these estimates, we recalculated the estimated virus control over the trait, given the new transition probabilities, from the simulation data as outlined above. We then tested for significance by randomizing the tips 1000 times, while ensuring that the infection outcome and host <italic>IL28B</italic> factor for a specific tip remain linked together. Trait correlation estimates were then recalculated for each of these 1000 new tip sets and tested for significance as before.</p></sec></sec><sec sec-type="results"><title>Results</title><sec><title>Correlation between virus genomes and infection outcome for all genome sequences</title><p>Genetic data obtained from the HITS-p cohort mostly resolved itself into one of the two clades, specifically HCV genotypes 1 (including subtypes 1a and 1b), and subtype 3a (Fig. <xref ref-type="fig" rid="fig01">1</xref>). These data sets are labelled as &#x02018;Genotype 1&#x02019; and &#x02018;Genotype 3&#x02019;, respectively. As a first test of our method, we investigated whether we could detect evidence of virus control over infection outcome for both genotypes analysed together (a joint phylogeny is shown in <xref ref-type="supplementary-material" rid="SD1">Fig. S2</xref>). Results are presented in Fig. <xref ref-type="fig" rid="fig04">4</xref>A, which shows that a fairly high level of virus control was observed (0.63), which was significant (<italic>P</italic> = 0.044 following a randomization test). A lower value (0.59) was observed for the birth&#x02013;death model, which was also marginally significant (<italic>P</italic> = 0.046; <xref ref-type="supplementary-material" rid="SD1">Fig. S3A</xref>).</p><fig id="fig04" position="float"><label>Figure 4</label><caption><p>Estimate of control signal for a single trait, based on a coalescent phylogeny for all sequences. Control signal estimates as inferred from the actual data set of interest (black), and of the 1000 median values of virus control estimates obtained from randomized tipsets (grey). <italic>P</italic> values listed above each pair of box plots show significancy of true control value based on randomization test; bold values indicate <italic>P</italic> &#x0003c; 0.05. Data analysed were the infection outcome (A); the status of the <italic>IL28B</italic>-860 SNP (B) or <italic>IL28B</italic>-917 SNP (C); or the the infection outcome covarying with the <italic>IL28B</italic>-917 SNP (D).</p></caption><graphic xlink:href="eva0007-0533-f4"/></fig><p>The high level of virus control found for randomized tips likely reflects that, although the log-likelihood of the switching rates decreases with increased virus control, the likelihood surface can be flat for a broad range of control values. This effect can reduce the power of the method to detect low levels of signal (see <xref ref-type="supplementary-material" rid="SD1">Fig. S1D</xref> for log-likelihood values obtained over the Genotype 1 phylogeny). We will investigate the power further in the section &#x02018;Power calculations and analysis of simulated data&#x02019;.</p><p>For this first analysis, it was assumed that infection outcomes change independently of other factors. This is not quite true, as there are known host polymorphisms that can affect infection outcome. If these host effects cluster on the virus phylogeny, then this host clustering may mask any virus signal that would otherwise be expressed. Therefore, we next investigated the phylogenetic signal of host genetic effects.</p></sec><sec><title>Phylogenetic signal for host's <italic>IL28B</italic> status</title><p>It is known that several polymorphisms near the <italic>IL28B</italic> gene in humans correlate with increased clearance rate of acute HCV infection. Those with a homozygous C/C mutation at the rs12979860 SNP (hereafter <italic>IL28B</italic>-860) are much more likely to achieve clearance (Ge et al. <xref rid="b11" ref-type="bibr">2009</xref>; Thomas et al. <xref rid="b50" ref-type="bibr">2009</xref>; Tillmann et al. <xref rid="b51" ref-type="bibr">2010</xref>). Similarly, those with a T/T genotype at the rs8099917 SNP (hereafter <italic>IL28B</italic>-917) are more likely to clear the virus (Rauch et al. <xref rid="b42" ref-type="bibr">2010</xref>).</p><p>In theory, as these mutations are present in the host genome and not the virus, and we were analysing virus phylogenies, we should not have detected phylogenetic signal for the host's <italic>IL28B</italic> status. We therefore controlled whether there was evidence of clustering for specific <italic>IL28B</italic> genotypes on the virus phylogeny, as a clustering of host traits could have masked expression of virus control on infection outcome clustering.</p><p>Figure <xref ref-type="fig" rid="fig04">4</xref>B,C outlines results when using the SNP that maximized clearance rates for HCV (C/C for <italic>IL28B</italic>-860; T/T for <italic>IL28B</italic>-917) as the trait of interest instead of the chronic/clearer outcomes. No significant correlations were obtained, although <italic>p</italic> values tended to be close to significance for the <italic>IL28B</italic>-917 SNP (Fig. <xref ref-type="fig" rid="fig04">4</xref>C; phylogenetic signal value = 0.646; <italic>P</italic> = 0.054). Results were quantitatively similar if using a birth&#x02013;death phylogeny (<xref ref-type="supplementary-material" rid="SD1">Fig. S3B,C</xref>).</p><p>The near-significant result for <italic>IL28B</italic>-917 SNP highlights a possibility of nonrandom clustering of host genetic effects on the phylogeny, which could have affected our previous analysis on infection outcome status.</p></sec><sec><title>Correlation in infection outcome, accounting for host status</title><p>As there could have been potential confounding effect from the host in determining potential influence of the virus control on infection outcome, we repeated the first analysis while accounting for the host's <italic>IL28B</italic> status. To this end, we used <sc>bayestraits</sc> (available from <ext-link ext-link-type="uri" xlink:href="http://www.evolution.rdg.ac.uk">http://www.evolution.rdg.ac.uk</ext-link>) to recalculate estimated transitions rates of the infection outcome, given that they could be affected by a secondary trait (in this case, the host's <italic>IL28B</italic>-917 genotype). We did not investigate the effect of <italic>IL28B</italic>-860 SNP as a co-factor as we did not find nearly significant evidence of this trait clustering on any of the phylogenies. This second analysis essentially removed the variance in infection outcomes solely caused by host differences and focused on the virus control on the residual variance. We then used these new rates to calculate estimates of virus control from the simulated data set of infection outcomes.</p><p>The results from the repeated analysis are outlined in Fig. <xref ref-type="fig" rid="fig04">4</xref>D. A significant (<italic>P</italic> = 0.015) and high virus control value of 0.87 was obtained if infection outcome covaried with the <italic>IL28B</italic>-917 SNP. The control value obtained using the birth&#x02013;death phylogeny was slightly lower (0.84) but remained strongly significant (<xref ref-type="supplementary-material" rid="SD1">Fig. S3D</xref>).</p><p>This initial analysis demonstrates how, over our entire data set, there appears to be a strong effect of the virus genotype on infection outcome, especially after correcting for host genotype. However, this analysis does not determine whether this signal is driven by effects of one specific genotype. This could be true if the genotypes respond differently to treatment (Lauer and Walker <xref rid="b24" ref-type="bibr">2001</xref>). In addition, it could be argued that the high level of phylogenetic signal was instead caused by the large phylogenetic distance between the two genotype clades (<xref ref-type="supplementary-material" rid="SD1">Fig. S2</xref>). There may also be uncertainty about whether the two subclades are from different transmission networks. To this end, we subsequently repeated this analysis but on the separate Genotype 1 and Genotype 3 data sets.</p></sec><sec><title>Separate analysis of genotypes</title><p>For this analysis, we first tested whether a model that had different transition rates for each clade provided a better fit than one using the same transition rates for each subclade. Initially, we compared four switching rates (different <italic>q</italic><sub>0,1</sub> and <italic>q</italic><sub>1,0</sub> values for the Genotype 1 and 3 subclades), as opposed to two rates (same <italic>q</italic><sub>0,1</sub> and <italic>q</italic><sub>1,0</sub> for each subclade). We found that the four-rate model provided a better fit, as determined by a lower BIC value, irrespective of whether we used a coalescent or a birth&#x02013;death phylogeny (data not shown). This result reflects the large phylogenetic distance between Genotype 1 and 3 clades (<xref ref-type="supplementary-material" rid="SD1">Fig. S2</xref>).</p><p>We next investigated whether we could detect evidence of a significant level of virus control over infection outcome for the two data sets of interest. The results of our analysis are presented for Genotype 1 (Fig. <xref ref-type="fig" rid="fig05">5</xref>A) and Genotype 3 (Fig. <xref ref-type="fig" rid="fig06">6</xref>A). For Genotype 1, a significantly high control value of 0.67 was obtained (<italic>P</italic> = 0.012). However, for Genotype 3, a close but nonsignificant value of 0.573 (<italic>P</italic> = 0.099) was found. Phylogenetic signal for the birth&#x02013;death phylogeny remained significant for Genotype 1 (<xref ref-type="supplementary-material" rid="SD1">Figs S4A and S5A</xref>).</p><fig id="fig05" position="float"><label>Figure 5</label><caption><p>Estimate of control signal for a single trait, based on a coalescent phylogeny, for sequences from Genotype 1. Control signal estimates as inferred from the actual data set of interest (black), and of the 1000 median values of virus control estimates obtained from randomized tipsets (grey). <italic>P</italic> values listed above each pair of box plots show significancy of true control value based on randomization test; bold values indicate <italic>P</italic> &#x0003c; 0.05. Data analysed were the infection outcome (A); the status of the <italic>IL28B</italic>-860 SNP (B) or <italic>IL28B</italic>-917 SNP (C); or the the infection outcome covarying with the <italic>IL28B</italic>-917 SNP (D).</p></caption><graphic xlink:href="eva0007-0533-f5"/></fig><fig id="fig06" position="float"><label>Figure 6</label><caption><p>As Fig. <xref ref-type="fig" rid="fig05">5</xref>, but with sequences from Genotype 3.</p></caption><graphic xlink:href="eva0007-0533-f6"/></fig><p>It was then determined whether there was any potential clustering of host genotypes along these subclades (Fig. <xref ref-type="fig" rid="fig05">5</xref>B,C for Genotype 1; Fig. <xref ref-type="fig" rid="fig06">6</xref>B,C for Genotype 3). As for the complete data set, we did not obtain any significant results (Fig. <xref ref-type="fig" rid="fig05">5</xref>C; see also Fig. <xref ref-type="fig" rid="fig01">1</xref> to see clustering of the <italic>IL28B</italic>-917 SNP on the phylogenies).</p><p>Note that the degree of observed clustering does not say anything about the correlation between the infection outcome and the <italic>IL28B</italic> SNP. It instead measures whether more related viruses tend to be found in similar host types. One possible reason for signal could be due to selection acting on the hypervariable segment within hosts during infection. To test for this effect, we repeated the analysis with phylogenies built using third codon positions only, which should be largely (but not completely) free from selection. This analysis produced essentially the same estimates of phylogenetic signal as found using a phylogeny built using the whole genetic region (<xref ref-type="supplementary-material" rid="SD1">Table S1</xref>). Therefore, host clustering does not seem to arise due to selection acting on the hypervariable segment. We will come back to the phenomena of host clustering in further detail in the Discussion.</p><p>Finally, we investigated the phylogenetic signal for infection outcome over each subclade, as it covaried with the <italic>IL28B</italic>-917 SNP (Fig. <xref ref-type="fig" rid="fig05">5</xref>D for Genotype 1, 6D for Genotype 3). We found a significant virus control value of 0.63 for Genotype 1; this estimate was not significantly different from the value obtained without correcting for host effects (two-sided <italic>t</italic> = 1.66, <italic>P</italic> = 0.0961). However, despite a higher control value being obtained for Genotype 3 than before (0.64), it was nonsignificant following the randomization test. The estimated control value using the birth&#x02013;death phylogeny was higher for Genotype 1 (0.69) and remained significant. Although a similarly high value was found for the Genotype 3 birth&#x02013;death phylogeny (0.66), it was not significant due to the high estimates obtained from randomized tips (<xref ref-type="supplementary-material" rid="SD1">Figs S4D and S5D</xref>).</p><p>Overall, this analysis suggests that the phylogenetic signal obtained for the complete data set was strongly driven by viruses of Genotype 1. After correcting for <italic>IL28B</italic> status, the Genotype 1 estimate was lower than that for the total data set (0.63, compared with 0.87 for all data), implying that the level of virus control could have been inflated when we pooled all samples due to the extra phylogenetic structure caused. We further investigated this result using power simulations, to verify that our estimates for Genotype 1 are accurate.</p></sec><sec><title>Power calculations and analysis of simulated data</title><p>In the previous analysis, one of the more glaring outcomes is that even randomized tips gave rather high estimates of virus control, lying at around 40%. The randomization test determined that our data from Genotype 1 showed evidence of phylogenetic signal compared with randomized tips, but the question remained as to whether the magnitude of the estimates of virus control we obtained are meaningful.</p><p>To determine the accuracy and the potential power of our method, we simulated 100 different tip outcomes along the Genotype 1 phylogeny, for a known virus control value (<italic>c</italic>), with each outcome produced along a separate tree from the posterior distribution. We then used our method to estimate the level of virus control from these simulated data sets as before, except that we tested for significance using 100 randomized tips as these tests were computationally intensive.</p><p>Figure <xref ref-type="fig" rid="fig07">7</xref> shows the results of this analysis. We see that as the level of simulated virus control <italic>c</italic> increases, the number of significant runs, and thus the power of the method, also increases. However, the power is generally quite low (ranging from 2/100 runs being significant if simulated <italic>c</italic> equals 0.55, to 17/100 if <italic>c</italic> = 0.85). In addition, the estimated level of virus control from the significant estimates increases with the input value (the slope of the linear fit is 0.35). Although error estimates are quite large for inputted <italic>c</italic> = 0.55, we see that the estimated values are fairly accurate in estimating the actual input by comparing mean points to the <italic>y</italic> = <italic>x</italic> line. However, if the input value is very large, then the control value is underestimated (when we set <italic>c</italic> = 0.85, the estimated level of phylogenetic signal is 0.73).</p><fig id="fig07" position="float"><label>Figure 7</label><caption><p>Phylogenetic signal estimated from simulated data with known control value (<italic>c</italic>), along the Genotype 1 phylogeny. 100 tipsets were simulated per input value, with the number of estimates that are significant listed in the <italic>x</italic>-axis. Each point shows the mean value of the significant estimates, with error bars representing 95% confidence intervals. The dotted line shows the <italic>y</italic> = <italic>x</italic> line; the dashed line shows a linear regression fit, with slope 0.35 (adjusted <italic>R</italic><sup>2</sup> = 0.88, <italic>P</italic> = 0.0392).</p></caption><graphic xlink:href="eva0007-0533-f7"/></fig><p>Overall, these power simulations showed that given a result is significant, the obtained estimate of virus control is likely to lie close to the actual level of phylogenetic signal that is present. However, this method also shows that our method can be prone to noisy estimates, which reduced the power of the analysis. This result makes clear the need for future work to increase the precision of phylogenetic inference (Shirreff et al. <xref rid="b43" ref-type="bibr">2013</xref>).</p></sec></sec><sec sec-type="discussion"><title>Discussion</title><p>While there are known host factors that can determine the infection outcome of an HCV infection (Ge et al. <xref rid="b11" ref-type="bibr">2009</xref>; Thomas et al. <xref rid="b50" ref-type="bibr">2009</xref>; Rauch et al. <xref rid="b42" ref-type="bibr">2010</xref>; Tillmann et al. <xref rid="b51" ref-type="bibr">2010</xref>), there currently exists very little information on how virus genetics affect the infection outcome. Using a phylogenetic method combined with data from an Australian prisoner cohort, we showed that diversity in the virus genotype could partly explain diversity in the infection outcome.</p><p>In our initial analysis, we detected a high level of phylogenetic signal once correcting for the host's <italic>IL28B</italic>-917 SNP, indicating that a virus genomic effect could be present (Fig. <xref ref-type="fig" rid="fig04">4</xref>). We then analysed each subclade separately as the high signal level could have been caused by general differences between genotypes. When investigating only the phylogenetic distance correlation between virus sequences and infection outcomes, we found a significant signal for Genotype 1 (Fig. <xref ref-type="fig" rid="fig05">5</xref>A) but not for Genotype 3 (albeit our result was close to significance; Fig. <xref ref-type="fig" rid="fig06">6</xref>A). We then repeated this analysis while accounting for the host's <italic>IL28B</italic> genotype as a co-factor to explain non-<italic>IL28B</italic> clearance effects and found that the virus control value equalled 0.63 for Genotype 1 (Fig. <xref ref-type="fig" rid="fig05">5</xref>D), which was not statistically different to the estimate without the correction (0.68).</p><p>Therefore, our analyses suggest that for Genotype 1 of HCV, a substantial component of clearance that is not due to the <italic>IL28B</italic> genotype is likely to be explained by the similarity of viral sequences infecting the host. However, we did not detect a significant effect for Genotype 3. The most parsimonious explanation for not detecting signal is probably that it is too weak to be picked up by the current method: our power calculations suggest that the signal has to lie at about 50% to be detected (see also Shirreff et al. (<xref rid="b43" ref-type="bibr">2013</xref>)). In addition, the coalescent effective population size is much higher for Genotype 3 (&#x0223c;223 for Genotype 1, compared to &#x0223c;1398 for Genotype 3), suggesting that this clade has been undersampled so as to lose phylogenetic signal. The addition of more sequences could therefore help to detect significant signal for Genotype 3.</p><p>While the clustering of infection outcomes suggests that there is an effect of virus genetics on infection outcome for Genotype 1, caution should be taken in interpreting the magnitude of these estimates as phylogenetic signal could also arise due to the action of an unknown co-variate that has an effect on virus persistence, such as age or gender. Additionally, it could be argued that infection outcomes cluster due the effect of a different disease that is co-transmitted with HCV. With the HITS-p cohort, the overwhelming majority of transmissions are caused via contaminated injecting or tattooing equipment (Dolan et al. <xref rid="b5" ref-type="bibr">2010</xref>; Teutsch et al. <xref rid="b49" ref-type="bibr">2010</xref>), so co-transmission of other pathogens is unlikely. This is because other blood-borne pathogens that could be transmitted, notably HIV and Hepatitis B, are present at low frequencies in the prisons (&#x0003c;1% for HIV and 5% for HBV). Furthermore, entry into the cohort is only permissible to those not already infected by HIV, so outcome clustering is not due to the effect of HIV co-transmission.</p><sec><title>Host clustering on a virus phylogeny</title><p>An important discussion point is why we detected relatively high and near-significant rates of correlation amongst the host <italic>IL28B</italic> genotypes when we pooled all the data, as theoretically there should not have been any magnitude of host genetic outcomes clustering along the phylogeny, which was built based using virus genotypes only. One explanation is that host clustering arose due to a quirk of host sampling, if hosts were genetically related (for example, if they were twins) and also shared the same transmission history. In this regard, no known first-degree relatives were enrolled in the cohort, so this type of sampling bias should not exist.</p><p>A more exciting explanation is that there was an interaction between the lineage of the virus and the host genetics. This effect could have arisen if the host acts as a virus filter, so only some HCV viruses can infect hosts that have <italic>IL28B</italic> genotypes that increase clearance rate. Another explanation is that the host <italic>IL28B</italic> genotype selects for specific virus mutations from amongst the quasispecies shortly after the infection has occurred, explaining their genetic similarity. This would be a case of parallel evolution (organisms that have evolved in similar environmental conditions and face the same challenges tend to look alike). However, as phylogenetic signal was nearly significant for only one host SNP (<italic>IL28B</italic>&#x02013;917) for the complete data set only, and the same degree of signal was also present on a third-site phylogeny (where most selective effects of the virus should be absent), then these findings do not strongly suggest that there is major selection acting by the host. Importantly, this significant interaction we detected between the virus phylogeny and the host genetics should not be mixed with the known correlation between host genetics and infection outcome (Thomas et al. <xref rid="b50" ref-type="bibr">2009</xref>). However, one interesting extension would be to determine the effect of the virus phylogeny on inferring the correlation between host <italic>IL28B</italic> polymorphism and infection outcome. This has been done for HIV (Bhattacharya et al. <xref rid="b3" ref-type="bibr">2007</xref>), but to achieve this for HCV, a larger data set is required.</p></sec><sec><title>Perspectives</title><p>It is becoming increasingly recognized that the variance in viral infection outcomes is not just due to host genetics; differences in the virus also plays an important role (Alizon et al. <xref rid="b2" ref-type="bibr">2011</xref>). While there is now substantial work investigating how infection outcomes correlate amongst related strains in HIV [reviewed in M&#x000fc;ller et al. (<xref rid="b31" ref-type="bibr">2011</xref>)], little work has been undertaken for other viruses of humans, including HCV. The results of this study show that there is a correlation in HCV infection outcome between related strains, for Genotype 1 at least. These findings have important implications in the prognosis of HCV infections in general and can be used to improve and potentially generate new virus-specific or host-specific therapies.</p><p>These findings also shed some interesting light on the process of virus evolution. Superficially, it seems baffling as to why HCV infections that clear immediately after the acute stage should be maintained by the virus genotype, as their presence appears to reduce opportunities to spread the pathogen. It could be that &#x02018;clearer&#x02019; HCV genotypes are weakly deleterious, as reflected in the low frequency of clearers that are observed. These weakly deleterious genotypes could be maintained between hosts at low frequencies via recurrent mutation (Wright <xref rid="b54" ref-type="bibr">1931</xref>), which could lead to clearer HCV infections having similar genotypes, if mutation is required at specific sites to cause a clearing infection. This mechanism could also be an example of &#x02018;short-sighted&#x02019; evolution of HCV, in which clearing outcomes evolve due to a within-host benefit, but at a cost of reduced transmission (Levin and Bull <xref rid="b26" ref-type="bibr">1994</xref>). We therefore anticipate that these results will motivate further work into elucidating the interactions between virus and host genotypes, and also into how these interactions affect the evolution of virulence and pathogen transmission. A phylogenetic analysis of ancestral &#x02018;founding&#x02019; variants could be used to investigate this hypothesis; if clearing variants were deleterious in the long term, then they would become extinct over short timescales. Otherwise, they would persist and form distinct subclades.</p><p>Our method is able to elucidate correlations between virus genetics and categorical infection outcomes that may not be detectable using standard clustering statistics (Parker et al. <xref rid="b35" ref-type="bibr">2008</xref>). In contrast with earlier methods, this method also provides a quantitative estimate of virus control over a categorical trait, as well as taking into account uncertainty in phylogenetic sampling, which is a common issue (Parker et al. <xref rid="b35" ref-type="bibr">2008</xref>). However, the method can be inherently noisy, as reflected in the high signal levels detected for randomized tips and the fact that we did not detect significant signal for Genotype 3, even after correcting for the host's genotype (see also power simulations, Fig. <xref ref-type="fig" rid="fig07">7</xref>). We anticipate that if more sequences were available, then the method would be less affected by its inherent noise. Secondly, phylogenies were constructed based on genetic data of only a part of the HCV genome, as opposed to whole-genome sequences. Analysing longer sequences or different regions of the HCV genome might result in phylogenies that more accurately reflect the complete set of virus variants. Some areas of the genome have minimal host interactions, whereas other regions strongly interact with the innate and adaptive elements of the host immune response. Therefore, phylogenies based on other parts of the virus genome could also reveal different interaction effects with host genetics. Finally, we hope that future research would refine methodology, so that it would be more likely to detect low levels of phylogenetic signal.</p></sec></sec><sec><title>Conclusion</title><p>In summary, by creating a method to simulate inheritance of a binary trait along a set of phylogenetic trees, we have found that the HCV phylogeny controls 67% of the phylogenetic signal of acute HCV infection for Genotype 1. This outcome could have arisen due to an inherited or transmitted trait within the HCV genome. Overall, this finding not only suggests that the virus genotype can affect infection outcome, but this is also dependent on the specific subtype of HCV, and host genetics. These findings should motivate further research into host&#x02013;parasite interactions that affect virus evolution.</p></sec></body><back><ack><p>We would like to thank Roger Kouyos and Sebastian Bonhoeffer for discussions, and two anonymous reviewers for comments on the manuscript. The HITS investigators include Andrew Lloyd, Fabio Luciani, Michael Levy, Kate Dolan, Peter White, Bill Rawlinson, Paul Haber, Carla Treloar, Greg Dore and Lisa Maher. The HITS-p cohort was supported by an NHMRC Partnership Grant (No. 1016351). FL and RB were supported by NHMRC Early Career Fellowships (No. 510428 and 630733, respectively) and AL by a NHMRC Practitioner Fellowship (No. 510246). M.H. is funded by an ATIP-Avenir grant from CNRS and INSERM to S.A.; S.A. and M.H. acknowledge additional support from the CNRS and the IRD.</p></ack><sec><title>Data archiving statement</title><p>HCV genome samples have been uploaded to GenBank (accession IDs KJ437271 &#x02013; KJ437343). Simulation data used for analysis are available from the Dryad Digital Repository: <ext-link ext-link-type="uri" xlink:href="http://dx.doi.org/10.5061/dryad.3978f">http://dx.doi.org/10.5061/dryad.3978f</ext-link>.</p></sec><ref-list><title>Literature cited</title><ref id="b1"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alizon</surname><given-names>S</given-names></name><name><surname>von Wyl</surname><given-names>V</given-names></name><name><surname>Stadler</surname><given-names>T</given-names></name><name><surname>Kouyos</surname><given-names>RD</given-names></name><name><surname>Yerly</surname><given-names>S</given-names></name><name><surname>Hirschel</surname><given-names>B</given-names></name><name><surname>B&#x000f6;ni</surname><given-names>J</given-names></name><etal/></person-group><article-title>Phylogenetic approach reveals that virus genotype largely determines HIV set-point viral load</article-title><source>PLoS Pathogens</source><year>2010</year><volume>6</volume><fpage>e1001123</fpage><pub-id pub-id-type="pmid">20941398</pub-id></element-citation></ref><ref id="b2"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alizon</surname><given-names>S</given-names></name><name><surname>Luciani</surname><given-names>F</given-names></name><name><surname>Regoes</surname><given-names>RR</given-names></name></person-group><article-title>Epidemiological and clinical consequences of within-host evolution</article-title><source>Trends in Microbiology</source><year>2011</year><volume>19</volume><fpage>24</fpage><lpage>32</lpage><pub-id pub-id-type="pmid">21055948</pub-id></element-citation></ref><ref id="b3"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bhattacharya</surname><given-names>T</given-names></name><name><surname>Daniels</surname><given-names>M</given-names></name><name><surname>Heckerman</surname><given-names>D</given-names></name><name><surname>Foley</surname><given-names>B</given-names></name><name><surname>Frahm</surname><given-names>N</given-names></name><name><surname>Kadie</surname><given-names>C</given-names></name><name><surname>Carlson</surname><given-names>J</given-names></name><etal/></person-group><article-title>Founder effects in the assessment of HIV polymorphisms and HLA allele associations</article-title><source>Science</source><year>2007</year><volume>315</volume><fpage>1583</fpage><lpage>1586</lpage><pub-id pub-id-type="pmid">17363674</pub-id></element-citation></ref><ref id="b4"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Darriba</surname><given-names>D</given-names></name><name><surname>Taboada</surname><given-names>GL</given-names></name><name><surname>Doallo</surname><given-names>R</given-names></name><name><surname>Posada</surname><given-names>D</given-names></name></person-group><article-title>jModelTest 2: more models, new heuristics and parallel computing</article-title><source>Nature Methods</source><year>2012</year><volume>9</volume><fpage>772</fpage><pub-id pub-id-type="pmid">22847109</pub-id></element-citation></ref><ref id="b5"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dolan</surname><given-names>K</given-names></name><name><surname>Teutsch</surname><given-names>S</given-names></name><name><surname>Scheuer</surname><given-names>N</given-names></name><name><surname>Levy</surname><given-names>M</given-names></name><name><surname>Rawlinson</surname><given-names>W</given-names></name><name><surname>Kaldor</surname><given-names>J</given-names></name><name><surname>Lloyd</surname><given-names>A</given-names></name><etal/></person-group><article-title>Incidence and risk for acute hepatitis C infection during imprisonment in Australia</article-title><source>European Journal of Epidemiology</source><year>2010</year><volume>25</volume><fpage>143</fpage><lpage>148</lpage><pub-id pub-id-type="pmid">20084429</pub-id></element-citation></ref><ref id="b6"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Drummond</surname><given-names>AJ</given-names></name><name><surname>Rambaut</surname><given-names>A</given-names></name><name><surname>Shapiro</surname><given-names>B</given-names></name><name><surname>Pybus</surname><given-names>OG</given-names></name></person-group><article-title>Bayesian coalescent inference of past population dynamics from molecular sequences</article-title><source>Molecular Biology and Evolution</source><year>2005</year><volume>22</volume><fpage>1185</fpage><lpage>1192</lpage><pub-id pub-id-type="pmid">15703244</pub-id></element-citation></ref><ref id="b7"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Drummond</surname><given-names>AJ</given-names></name><name><surname>Suchard</surname><given-names>MA</given-names></name><name><surname>Xie</surname><given-names>D</given-names></name><name><surname>Rambaut</surname><given-names>A</given-names></name></person-group><article-title>Bayesian phylogenetics with BEAUti and the BEAST 1.7</article-title><source>Molecular Biology and Evolution</source><year>2012</year><volume>29</volume><fpage>1969</fpage><lpage>1973</lpage><pub-id pub-id-type="pmid">22367748</pub-id></element-citation></ref><ref id="b8"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fafi-Kremer</surname><given-names>S</given-names></name><name><surname>Fofana</surname><given-names>I</given-names></name><name><surname>Soulier</surname><given-names>E</given-names></name><name><surname>Carolla</surname><given-names>P</given-names></name><name><surname>Meuleman</surname><given-names>P</given-names></name><name><surname>Leroux-Roels</surname><given-names>G</given-names></name><name><surname>Patel</surname><given-names>AH</given-names></name><etal/></person-group><article-title>Viral entry and escape from antibody-mediated neutralization influence hepatitis C virus reinfection in liver transplantation</article-title><source>Journal of Experimental Medicine</source><year>2010</year><volume>207</volume><fpage>2019</fpage><lpage>2031</lpage><pub-id pub-id-type="pmid">20713596</pub-id></element-citation></ref><ref id="b9"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Farci</surname><given-names>P</given-names></name><name><surname>Shimoda</surname><given-names>A</given-names></name><name><surname>Coiana</surname><given-names>A</given-names></name><name><surname>Diaz</surname><given-names>G</given-names></name><name><surname>Peddis</surname><given-names>G</given-names></name><name><surname>Melpolder</surname><given-names>JC</given-names></name><name><surname>Strazzera</surname><given-names>A</given-names></name><etal/></person-group><article-title>The outcome of acute hepatitis C predicted by the evolution of the viral quasispecies</article-title><source>Science</source><year>2000</year><volume>288</volume><fpage>339</fpage><lpage>344</lpage><pub-id pub-id-type="pmid">10764648</pub-id></element-citation></ref><ref id="b10"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Frank</surname><given-names>C</given-names></name><name><surname>Mohamed</surname><given-names>MK</given-names></name><name><surname>Strickland</surname><given-names>GT</given-names></name><name><surname>Lavanchy</surname><given-names>D</given-names></name><name><surname>Arthur</surname><given-names>RR</given-names></name><name><surname>Magder</surname><given-names>LS</given-names></name><name><surname>Khoby</surname><given-names>TE</given-names></name><etal/></person-group><article-title>The role of parenteral antischistosomal therapy in the spread of hepatitis C virus in Egypt</article-title><source>The Lancet</source><year>2000</year><volume>355</volume><fpage>887</fpage><lpage>891</lpage></element-citation></ref><ref id="b11"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ge</surname><given-names>D</given-names></name><name><surname>Fellay</surname><given-names>J</given-names></name><name><surname>Thompson</surname><given-names>AJ</given-names></name><name><surname>Simon</surname><given-names>JS</given-names></name><name><surname>Shianna</surname><given-names>KV</given-names></name><name><surname>Urban</surname><given-names>TJ</given-names></name><name><surname>Heinzen</surname><given-names>EL</given-names></name><etal/></person-group><article-title>Genetic variation in <italic>IL28B</italic> predicts hepatitis C treatment-induced viral clearance</article-title><source>Nature</source><year>2009</year><volume>461</volume><fpage>399</fpage><lpage>401</lpage><pub-id pub-id-type="pmid">19684573</pub-id></element-citation></ref><ref id="b12"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gray</surname><given-names>RR</given-names></name><name><surname>Parker</surname><given-names>J</given-names></name><name><surname>Lemey</surname><given-names>P</given-names></name><name><surname>Salemi</surname><given-names>M</given-names></name><name><surname>Katzourakis</surname><given-names>A</given-names></name><name><surname>Pybus</surname><given-names>OG</given-names></name></person-group><article-title>The mode and tempo of hepatitis C virus evolution within and among hosts</article-title><source>BMC Evolutionary Biology</source><year>2011</year><volume>11</volume><fpage>00</fpage><lpage>00</lpage></element-citation></ref><ref id="b13"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grebely</surname><given-names>J</given-names></name><name><surname>Prins</surname><given-names>M</given-names></name><name><surname>Hellard</surname><given-names>M</given-names></name><name><surname>Cox</surname><given-names>AL</given-names></name><name><surname>Osburn</surname><given-names>WO</given-names></name><name><surname>Lauer</surname><given-names>G</given-names></name><name><surname>Page</surname><given-names>K</given-names></name><etal/></person-group><article-title>Hepatitis C virus clearance, reinfection, and persistence, with insights from studies of injecting drug users: towards a vaccine</article-title><source>The Lancet Infectious Diseases</source><year>2012</year><volume>12</volume><fpage>408</fpage><lpage>414</lpage><pub-id pub-id-type="pmid">22541630</pub-id></element-citation></ref><ref id="b14"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Grebely</surname><given-names>J</given-names></name><name><surname>Page</surname><given-names>K</given-names></name><name><surname>Sacks-Davis</surname><given-names>R</given-names></name><name><surname>Schim van der Loeff</surname><given-names>M</given-names></name><name><surname>Rice</surname><given-names>TM</given-names></name><name><surname>Bruneau</surname><given-names>J</given-names></name><name><surname>Morris</surname><given-names>MD</given-names></name><etal/></person-group><article-title>The effects of female sex, viral genotype, and <italic>IL28B</italic> genotype on spontaneous clearance of acute hepatitis C Virus infection</article-title><source>Hepatology</source><year>2014</year><volume>59</volume><fpage>109</fpage><lpage>120</lpage><pub-id pub-id-type="pmid">23908124</pub-id></element-citation></ref><ref id="b15"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guindon</surname><given-names>S</given-names></name><name><surname>Gascuel</surname><given-names>O</given-names></name></person-group><article-title>A simple, fast, and accurate algorithm to estimate large phylogenies by maximum likelihood</article-title><source>Systematic Biology</source><year>2003</year><volume>52</volume><fpage>696</fpage><lpage>704</lpage><pub-id pub-id-type="pmid">14530136</pub-id></element-citation></ref><ref id="b16"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guindon</surname><given-names>S</given-names></name><name><surname>Dufayard</surname><given-names>J-F</given-names></name><name><surname>Lefort</surname><given-names>V</given-names></name><name><surname>Anisimova</surname><given-names>M</given-names></name><name><surname>Hordijk</surname><given-names>W</given-names></name><name><surname>Gascuel</surname><given-names>O</given-names></name></person-group><article-title>New algorithms and methods to estimate maximum-likelihood phylogenies: assessing the performance of PhyML 3.0</article-title><source>Systematic Biology</source><year>2010</year><volume>59</volume><fpage>307</fpage><lpage>321</lpage><pub-id pub-id-type="pmid">20525638</pub-id></element-citation></ref><ref id="b17"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hecht</surname><given-names>FM</given-names></name><name><surname>Hartogensis</surname><given-names>W</given-names></name><name><surname>Bragg</surname><given-names>L</given-names></name><name><surname>Bacchetti</surname><given-names>P</given-names></name><name><surname>Atchison</surname><given-names>R</given-names></name><name><surname>Grant</surname><given-names>R</given-names></name><name><surname>Barbour</surname><given-names>J</given-names></name><etal/></person-group><article-title>HIV RNA level in early infection is predicted by viral load in the transmission source</article-title><source>AIDS</source><year>2010</year><volume>24</volume><fpage>941</fpage><lpage>945</lpage><pub-id pub-id-type="pmid">20168202</pub-id></element-citation></ref><ref id="b18"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hollingsworth</surname><given-names>TD</given-names></name><name><surname>Laeyendecker</surname><given-names>O</given-names></name><name><surname>Shirreff</surname><given-names>G</given-names></name><name><surname>Donnelly</surname><given-names>CA</given-names></name><name><surname>Serwadda</surname><given-names>D</given-names></name><name><surname>Wawer</surname><given-names>MJ</given-names></name><name><surname>Kiwanuka</surname><given-names>N</given-names></name><etal/></person-group><article-title>HIV-1 transmitting couples have similar viral load set-points in Rakai, Uganda</article-title><source>PLoS Pathogens</source><year>2010</year><volume>6</volume><fpage>e1000876 EP</fpage><pub-id pub-id-type="pmid">20463808</pub-id></element-citation></ref><ref id="b19"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hu&#x000e9;</surname><given-names>S</given-names></name><name><surname>Clewley</surname><given-names>JP</given-names></name><name><surname>Cane</surname><given-names>PA</given-names></name><name><surname>Pillay</surname><given-names>D</given-names></name></person-group><article-title>HIV-1 pol gene variation is sufficient for reconstruction of transmissions in the era of antiretroviral therapy</article-title><source>AIDS</source><year>2004</year><volume>18</volume><fpage>719</fpage><lpage>728</lpage><pub-id pub-id-type="pmid">15075506</pub-id></element-citation></ref><ref id="b20"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Janssen</surname><given-names>HLA</given-names></name><name><surname>Reesink</surname><given-names>HW</given-names></name><name><surname>Lawitz</surname><given-names>EJ</given-names></name><name><surname>Zeuzem</surname><given-names>S</given-names></name><name><surname>Rodriguez-Torres</surname><given-names>M</given-names></name><name><surname>Patel</surname><given-names>K</given-names></name><name><surname>van der Meer</surname><given-names>AJ</given-names></name><etal/></person-group><article-title>Treatment of HCV infection by targeting microRNA</article-title><source>New England Journal of Medicine</source><year>2013</year><volume>368</volume><fpage>1685</fpage><lpage>1694</lpage><pub-id pub-id-type="pmid">23534542</pub-id></element-citation></ref><ref id="b21"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kasprowicz</surname><given-names>V</given-names></name><name><surname>Kang</surname><given-names>Y-H</given-names></name><name><surname>Lucas</surname><given-names>M</given-names></name><name><surname>Schulze zur Wiesch</surname><given-names>J</given-names></name><name><surname>Kuntzen</surname><given-names>T</given-names></name><name><surname>Fleming</surname><given-names>V</given-names></name><name><surname>Nolan</surname><given-names>BE</given-names></name><etal/></person-group><article-title>Hepatitis C virus (HCV) sequence variation induces an HCV-specific T-cell phenotype analogous to spontaneous resolution</article-title><source>Journal of Virology</source><year>2010</year><volume>84</volume><fpage>1656</fpage><lpage>1663</lpage><pub-id pub-id-type="pmid">19906915</pub-id></element-citation></ref><ref id="b22"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kumar</surname><given-names>U</given-names></name><name><surname>Brown</surname><given-names>J</given-names></name><name><surname>Monjardino</surname><given-names>J</given-names></name><name><surname>Thomas</surname><given-names>HC</given-names></name></person-group><article-title>Sequence variation in the large envelope glycoprotein (E2/NS1) of hepatitis C virus during chronic infection</article-title><source>Journal of Infectious Diseases</source><year>1993</year><volume>167</volume><fpage>726</fpage><lpage>730</lpage><pub-id pub-id-type="pmid">8382721</pub-id></element-citation></ref><ref id="b23"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kuntzen</surname><given-names>T</given-names></name><name><surname>Timm</surname><given-names>J</given-names></name><name><surname>Berical</surname><given-names>A</given-names></name><name><surname>Lewis-Ximenez</surname><given-names>LL</given-names></name><name><surname>Jones</surname><given-names>A</given-names></name><name><surname>Nolan</surname><given-names>B</given-names></name><name><surname>Schulzezur Wiesch</surname><given-names>J</given-names></name><etal/></person-group><article-title>Viral sequence evolution in acute hepatitis C virus infection</article-title><source>Journal of Virology</source><year>2007</year><volume>81</volume><fpage>11658</fpage><lpage>11668</lpage><pub-id pub-id-type="pmid">17699568</pub-id></element-citation></ref><ref id="b24"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lauer</surname><given-names>GM</given-names></name><name><surname>Walker</surname><given-names>BD</given-names></name></person-group><article-title>Hepatitis C virus infection</article-title><source>New England Journal of Medicine</source><year>2001</year><volume>345</volume><fpage>41</fpage><lpage>52</lpage><pub-id pub-id-type="pmid">11439948</pub-id></element-citation></ref><ref id="b25"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Leitner</surname><given-names>T</given-names></name><name><surname>Escanilla</surname><given-names>D</given-names></name><name><surname>Franz&#x000e9;n</surname><given-names>C</given-names></name><name><surname>Uhl&#x000e9;n</surname><given-names>M</given-names></name><name><surname>Albert</surname><given-names>J</given-names></name></person-group><article-title>Accurate reconstruction of a known HIV-1 transmission history by phylogenetic tree analysis</article-title><source>Proceedings of the National Academy of Sciences of the United States of America</source><year>1996</year><volume>93</volume><fpage>10864</fpage><lpage>10869</lpage><pub-id pub-id-type="pmid">8855273</pub-id></element-citation></ref><ref id="b26"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Levin</surname><given-names>BR</given-names></name><name><surname>Bull</surname><given-names>JJ</given-names></name></person-group><article-title>Short-sighted evolution and the virulence of pathogenic microorganisms</article-title><source>Trends in Microbiology</source><year>1994</year><volume>2</volume><fpage>76</fpage><lpage>81</lpage><pub-id pub-id-type="pmid">8156275</pub-id></element-citation></ref><ref id="b27"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>L</given-names></name><name><surname>Fisher</surname><given-names>BE</given-names></name><name><surname>Dowd</surname><given-names>KA</given-names></name><name><surname>Astemborski</surname><given-names>J</given-names></name><name><surname>Cox</surname><given-names>AL</given-names></name><name><surname>Ray</surname><given-names>SC</given-names></name></person-group><article-title>Acceleration of hepatitis C virus envelope evolution in humans is consistent with progressive humoral immune selection during the transition from acute to chronic infection</article-title><source>Journal of Virology</source><year>2010</year><volume>84</volume><fpage>5067</fpage><lpage>5077</lpage><pub-id pub-id-type="pmid">20200239</pub-id></element-citation></ref><ref id="b28"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>L</given-names></name><name><surname>Fisher</surname><given-names>BE</given-names></name><name><surname>Thomas</surname><given-names>DL</given-names></name><name><surname>Cox</surname><given-names>AL</given-names></name><name><surname>Ray</surname><given-names>SC</given-names></name></person-group><article-title>Spontaneous clearance of primary acute hepatitis C virus infection correlated with high initial viral RNA level and rapid HVR1 evolution</article-title><source>Hepatology</source><year>2012</year><volume>55</volume><fpage>1684</fpage><lpage>1691</lpage><pub-id pub-id-type="pmid">22234804</pub-id></element-citation></ref><ref id="b29"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Magiorkinis</surname><given-names>G</given-names></name><name><surname>Sypsa</surname><given-names>V</given-names></name><name><surname>Magiorkinis</surname><given-names>E</given-names></name><name><surname>Paraskevis</surname><given-names>D</given-names></name><name><surname>Katsoulidou</surname><given-names>A</given-names></name><name><surname>Belshaw</surname><given-names>R</given-names></name><name><surname>Fraser</surname><given-names>C</given-names></name><etal/></person-group><article-title>Integrating phylodynamics and epidemiology to estimate transmission diversity in viral epidemics</article-title><source>PLoS Computational Biology</source><year>2013</year><volume>9</volume><fpage>e1002876 EP</fpage><pub-id pub-id-type="pmid">23382662</pub-id></element-citation></ref><ref id="b30"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Micallef</surname><given-names>JM</given-names></name><name><surname>Kaldor</surname><given-names>JM</given-names></name><name><surname>Dore</surname><given-names>GJ</given-names></name></person-group><article-title>Spontaneous viral clearance following acute hepatitis C infection: a systematic review of longitudinal studies</article-title><source>Journal of Viral Hepatitis</source><year>2006</year><volume>13</volume><fpage>34</fpage><lpage>41</lpage><pub-id pub-id-type="pmid">16364080</pub-id></element-citation></ref><ref id="b31"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>M&#x000fc;ller</surname><given-names>V</given-names></name><name><surname>Fraser</surname><given-names>C</given-names></name><name><surname>Herbeck</surname><given-names>JT</given-names></name></person-group><article-title>A strong case for viral genetic factors in HIV virulence</article-title><source>Viruses</source><year>2011</year><volume>3</volume><fpage>204</fpage><lpage>216</lpage><pub-id pub-id-type="pmid">21994727</pub-id></element-citation></ref><ref id="b32"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Osburn</surname><given-names>WO</given-names></name><name><surname>Fisher</surname><given-names>BE</given-names></name><name><surname>Dowd</surname><given-names>KA</given-names></name><name><surname>Urban</surname><given-names>G</given-names></name><name><surname>Liu</surname><given-names>L</given-names></name><name><surname>Ray</surname><given-names>SC</given-names></name><name><surname>Thomas</surname><given-names>DL</given-names></name><etal/></person-group><article-title>Spontaneous control of primary hepatitis C virus infection and immunity against persistent reinfection</article-title><source>Gastroenterology</source><year>2010</year><volume>138</volume><fpage>315</fpage><lpage>324</lpage><pub-id pub-id-type="pmid">19782080</pub-id></element-citation></ref><ref id="b33"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pagel</surname><given-names>M</given-names></name></person-group><article-title>Detecting correlated evolution on phylogenies: a general method for the comparative analysis of discrete characters</article-title><source>Proceedings of the Royal Society of London. Series B: Biological Sciences</source><year>1994</year><volume>255</volume><fpage>37</fpage><lpage>45</lpage></element-citation></ref><ref id="b34"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Paradis</surname><given-names>E</given-names></name><name><surname>Claude</surname><given-names>J</given-names></name><name><surname>Strimmer</surname><given-names>K</given-names></name></person-group><article-title>APE: analyses of phylogenetics and evolution in R language</article-title><source>Bioinformatics</source><year>2004</year><volume>20</volume><fpage>289</fpage><lpage>290</lpage><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="b35"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Parker</surname><given-names>J</given-names></name><name><surname>Rambaut</surname><given-names>A</given-names></name><name><surname>Pybus</surname><given-names>OG</given-names></name></person-group><article-title>Correlating viral phenotypes with phylogeny: accounting for phylogenetic uncertainty</article-title><source>Infection, Genetics and Evolution</source><year>2008</year><volume>8</volume><fpage>239</fpage><lpage>246</lpage></element-citation></ref><ref id="b36"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pham</surname><given-names>ST</given-names></name><name><surname>Bull</surname><given-names>RA</given-names></name><name><surname>Bennett</surname><given-names>JM</given-names></name><name><surname>Rawlinson</surname><given-names>WD</given-names></name><name><surname>Dore</surname><given-names>GJ</given-names></name><name><surname>Lloyd</surname><given-names>AR</given-names></name><name><surname>White</surname><given-names>PA</given-names></name></person-group><article-title>Frequent multiple hepatitis C virus infections among injection drug users in a prison setting</article-title><source>Hepatology</source><year>2010</year><volume>52</volume><fpage>1564</fpage><lpage>1572</lpage><pub-id pub-id-type="pmid">21038409</pub-id></element-citation></ref><ref id="b37"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ploss</surname><given-names>A</given-names></name><name><surname>Dubuisson</surname><given-names>J</given-names></name></person-group><article-title>New advances in the molecular biology of hepatitis C virus infection: towards the identification of new treatment targets</article-title><source>Gut</source><year>2012</year><volume>61</volume><fpage>i25</fpage><lpage>i35</lpage><pub-id pub-id-type="pmid">22504917</pub-id></element-citation></ref><ref id="b38"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pybus</surname><given-names>OG</given-names></name><name><surname>Charleston</surname><given-names>MA</given-names></name><name><surname>Gupta</surname><given-names>S</given-names></name><name><surname>Rambaut</surname><given-names>A</given-names></name><name><surname>Holmes</surname><given-names>EC</given-names></name><name><surname>Harvey</surname><given-names>PH</given-names></name></person-group><article-title>The epidemic behavior of the hepatitis C Virus</article-title><source>Science</source><year>2001</year><volume>292</volume><fpage>2323</fpage><lpage>2325</lpage><pub-id pub-id-type="pmid">11423661</pub-id></element-citation></ref><ref id="b39"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pybus</surname><given-names>OG</given-names></name><name><surname>Drummond</surname><given-names>AJ</given-names></name><name><surname>Nakano</surname><given-names>T</given-names></name><name><surname>Robertson</surname><given-names>BH</given-names></name><name><surname>Rambaut</surname><given-names>A</given-names></name></person-group><article-title>The epidemiology and iatrogenic transmission of hepatitis C virus in Egypt: a Bayesian coalescent approach</article-title><source>Molecular Biology and Evolution</source><year>2003</year><volume>20</volume><fpage>381</fpage><lpage>387</lpage><pub-id pub-id-type="pmid">12644558</pub-id></element-citation></ref><ref id="b40"><element-citation publication-type="book"><collab>R Development Core Team</collab><source>R: A Language and Environment for Statistical Computing</source><year>2008</year><publisher-loc>Vienna, Austria</publisher-loc><publisher-name>R Foundation for Statistical Computing</publisher-name><comment>ISBN 3-900051-07-0</comment></element-citation></ref><ref id="b41"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Raghwani</surname><given-names>J</given-names></name><name><surname>Thomas</surname><given-names>XV</given-names></name><name><surname>Koekkoek</surname><given-names>SM</given-names></name><name><surname>Schinkel</surname><given-names>J</given-names></name><name><surname>Molenkamp</surname><given-names>R</given-names></name><name><surname>van de Laar</surname><given-names>TJ</given-names></name><name><surname>Takebe</surname><given-names>Y</given-names></name><etal/></person-group><article-title>Origin and evolution of the unique hepatitis C virus circulating recombinant form 2k/1b</article-title><source>Journal of Virology</source><year>2012</year><volume>86</volume><fpage>2212</fpage><lpage>2220</lpage><pub-id pub-id-type="pmid">22114341</pub-id></element-citation></ref><ref id="b42"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rauch</surname><given-names>A</given-names></name><name><surname>Kutalik</surname><given-names>Z</given-names></name><name><surname>Descombes</surname><given-names>P</given-names></name><name><surname>Cai</surname><given-names>T</given-names></name><name><surname>Di Iulio</surname><given-names>J</given-names></name><name><surname>Mueller</surname><given-names>T</given-names></name><name><surname>Bochud</surname><given-names>M</given-names></name><etal/></person-group><article-title>Genetic variation in <italic>IL28B</italic> is associated with chronic hepatitis C and treatment failure: a genome-wide association study</article-title><source>Gastroenterology</source><year>2010</year><volume>138</volume><fpage>1338</fpage><lpage>1345</lpage><comment>.e7</comment><pub-id pub-id-type="pmid">20060832</pub-id></element-citation></ref><ref id="b43"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shirreff</surname><given-names>G</given-names></name><name><surname>Alizon</surname><given-names>S</given-names></name><name><surname>Cori</surname><given-names>A</given-names></name><name><surname>G&#x000fc;nthard</surname><given-names>HF</given-names></name><name><surname>Laeyendecker</surname><given-names>O</given-names></name><name><surname>van Sighem</surname><given-names>A</given-names></name><name><surname>Bezemer</surname><given-names>D</given-names></name><etal/></person-group><article-title>How effectively can HIV phylogenies be used to measure heritability?</article-title><source>Evolution, Medicine, and Public Health</source><year>2013</year><volume>1</volume><fpage>209</fpage><lpage>224</lpage></element-citation></ref><ref id="b44"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Simmonds</surname><given-names>P</given-names></name></person-group><article-title>Genetic diversity and evolution of hepatitis C virus &#x02013; 15 years on</article-title><source>Journal of General Virology</source><year>2004</year><volume>85</volume><fpage>3173</fpage><lpage>3188</lpage><pub-id pub-id-type="pmid">15483230</pub-id></element-citation></ref><ref id="b45"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Simmonds</surname><given-names>P</given-names></name><name><surname>Bukh</surname><given-names>J</given-names></name><name><surname>Combet</surname><given-names>C</given-names></name><name><surname>Del&#x000e9;age</surname><given-names>G</given-names></name><name><surname>Enomoto</surname><given-names>N</given-names></name><name><surname>Feinstone</surname><given-names>S</given-names></name><name><surname>Halfon</surname><given-names>P</given-names></name><etal/></person-group><article-title>Consensus proposals for a unified system of nomenclature of hepatitis C virus genotypes</article-title><source>Hepatology</source><year>2005</year><volume>42</volume><fpage>962</fpage><lpage>973</lpage><pub-id pub-id-type="pmid">16149085</pub-id></element-citation></ref><ref id="b46"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stadler</surname><given-names>T</given-names></name><name><surname>Kouyos</surname><given-names>R</given-names></name><name><surname>von Wyl</surname><given-names>V</given-names></name><name><surname>Yerly</surname><given-names>S</given-names></name><name><surname>B&#x000f6;ni</surname><given-names>J</given-names></name><name><surname>B&#x000fc;rgisser</surname><given-names>P</given-names></name><name><surname>Klimkait</surname><given-names>T</given-names></name><etal/></person-group><article-title>Estimating the basic reproductive number from viral sequence data</article-title><source>Molecular Biology and Evolution</source><year>2012</year><volume>29</volume><fpage>347</fpage><lpage>357</lpage><pub-id pub-id-type="pmid">21890480</pub-id></element-citation></ref><ref id="b47"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stadler</surname><given-names>T</given-names></name><name><surname>K&#x000fc;hnert</surname><given-names>D</given-names></name><name><surname>Bonhoeffer</surname><given-names>S</given-names></name><name><surname>Drummond</surname><given-names>AJ</given-names></name></person-group><article-title>Birth&#x02013;death skyline plot reveals temporal changes of epidemic spread in HIV and hepatitis C virus (HCV)</article-title><source>Proceedings of the National Academy of Sciences of the United States of America</source><year>2013</year><volume>110</volume><fpage>228</fpage><lpage>233</lpage><pub-id pub-id-type="pmid">23248286</pub-id></element-citation></ref><ref id="b48"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tang</surname><given-names>J</given-names></name><name><surname>Tang</surname><given-names>S</given-names></name><name><surname>Lobashevsky</surname><given-names>E</given-names></name><name><surname>Zulu</surname><given-names>I</given-names></name><name><surname>Aldrovandi</surname><given-names>G</given-names></name><name><surname>Allen</surname><given-names>S</given-names></name><name><surname>Kaslow</surname><given-names>RA</given-names></name></person-group><article-title>HLA allele sharing and HIV type 1 viremia in seroconverting Zambians with known transmitting partners</article-title><source>AIDS Research and Human Retroviruses</source><year>2004</year><volume>20</volume><fpage>19</fpage><lpage>25</lpage><pub-id pub-id-type="pmid">15000695</pub-id></element-citation></ref><ref id="b49"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Teutsch</surname><given-names>S</given-names></name><name><surname>Luciani</surname><given-names>F</given-names></name><name><surname>Scheuer</surname><given-names>N</given-names></name><name><surname>McCredie</surname><given-names>L</given-names></name><name><surname>Hosseiny</surname><given-names>P</given-names></name><name><surname>Rawlinson</surname><given-names>W</given-names></name><name><surname>Kaldor</surname><given-names>J</given-names></name><etal/></person-group><article-title>Incidence of primary hepatitis C infection and risk factors for transmission in an Australian prisoner cohort</article-title><source>BMC Public Health</source><year>2010</year><volume>10</volume><fpage>633</fpage><pub-id pub-id-type="pmid">20964864</pub-id></element-citation></ref><ref id="b50"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thomas</surname><given-names>DL</given-names></name><name><surname>Thio</surname><given-names>CL</given-names></name><name><surname>Martin</surname><given-names>MP</given-names></name><name><surname>Qi</surname><given-names>Y</given-names></name><name><surname>Ge</surname><given-names>D</given-names></name><name><surname>O'hUigin</surname><given-names>C</given-names></name><name><surname>Kidd</surname><given-names>J</given-names></name><etal/></person-group><article-title>Genetic variation in <italic>IL28B</italic> and spontaneous clearance of hepatitis C virus</article-title><source>Nature</source><year>2009</year><volume>461</volume><fpage>798</fpage><lpage>801</lpage><pub-id pub-id-type="pmid">19759533</pub-id></element-citation></ref><ref id="b51"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tillmann</surname><given-names>HL</given-names></name><name><surname>Thompson</surname><given-names>AJ</given-names></name><name><surname>Patel</surname><given-names>K</given-names></name><name><surname>Wiese</surname><given-names>M</given-names></name><name><surname>Tenckhoff</surname><given-names>H</given-names></name><name><surname>Nischalke</surname><given-names>HD</given-names></name><name><surname>Lokhnygina</surname><given-names>Y</given-names></name><etal/></person-group><article-title>A polymorphism near <italic>IL28B</italic> is associated with spontaneous clearance of acute hepatitis C virus and jaundice</article-title><source>Gastroenterology</source><year>2010</year><volume>139</volume><fpage>1586</fpage><lpage>1592</lpage><comment>.e1</comment><pub-id pub-id-type="pmid">20637200</pub-id></element-citation></ref><ref id="b52"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Visscher</surname><given-names>PM</given-names></name><name><surname>Hill</surname><given-names>WG</given-names></name><name><surname>Wray</surname><given-names>NR</given-names></name></person-group><article-title>Heritability in the genomics era &#x02013; concepts and misconceptions</article-title><source>Nature Reviews Genetics</source><year>2008</year><volume>9</volume><fpage>255</fpage><lpage>266</lpage></element-citation></ref><ref id="b53"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Weiner</surname><given-names>AJ</given-names></name><name><surname>Geysen</surname><given-names>HM</given-names></name><name><surname>Christopherson</surname><given-names>C</given-names></name><name><surname>Hall</surname><given-names>JE</given-names></name><name><surname>Mason</surname><given-names>TJ</given-names></name><name><surname>Saracco</surname><given-names>G</given-names></name><name><surname>Bonino</surname><given-names>F</given-names></name><etal/></person-group><article-title>Evidence for immune selection of hepatitis C virus (HCV) putative envelope glycoprotein variants: potential role in chronic HCV infections</article-title><source>Proceedings of the National Academy of Sciences of the United States of America</source><year>1992</year><volume>89</volume><fpage>3468</fpage><lpage>3472</lpage><pub-id pub-id-type="pmid">1314389</pub-id></element-citation></ref><ref id="b54"><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wright</surname><given-names>S</given-names></name></person-group><article-title>Evolution in Mendelian populations</article-title><source>Genetics</source><year>1931</year><volume>16</volume><fpage>97</fpage><lpage>159</lpage><pub-id pub-id-type="pmid">17246615</pub-id></element-citation></ref></ref-list><sec sec-type="supplementary-material"><title>Supporting Information</title><p>Additional Supporting Information may be found in the online version of this article:</p><p><bold>Table S1.</bold> Overview of third-site only phylogeny results.</p><p><bold>Figure S1.</bold> Plots of how various simulation statistics vary with increased virus control for our HCV &#x02018;Genotype 1&#x02019; coalescent phylogeny.</p><p><bold>Figure S2.</bold> Phylogeny of the joint Genotypes 1 and 3 data set.</p><p><bold>Figure S3.</bold> Estimate of control signal for a single trait, based on a birthdeath phylogeny for all sequences.</p><p><bold>Figure S4.</bold> Estimate of control signal for a single trait, based on a birthdeath phylogeny, for sequences from Genotype 1.</p><p><bold>Figure S5.</bold> As Fig. S4, but with sequences from Genotype 3.</p><supplementary-material content-type="local-data" id="SD1"><media mime-subtype="pdf" xlink:href="eva0007-0533-SD1.pdf" xlink:type="simple" id="d35e3455" position="anchor"/></supplementary-material></sec></back></article>