<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1d2 20140930//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 2?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Syst Biol</journal-id><journal-id journal-id-type="iso-abbrev">BMC Syst Biol</journal-id><journal-title-group><journal-title>BMC Systems Biology</journal-title></journal-title-group><issn pub-type="epub">1752-0509</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">4581490</article-id><article-id pub-id-type="publisher-id">209</article-id><article-id pub-id-type="doi">10.1186/s12918-015-0209-4</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>Analysis of a spatial gene expression database for sea anemone <italic>Nematostella vectensis</italic> during early development</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Botman</surname><given-names>Daniel</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Jansson</surname><given-names>Fredrik</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>R&#x000f6;ttinger</surname><given-names>Eric</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2"/><xref ref-type="aff" rid="Aff3"/><xref ref-type="aff" rid="Aff4"/></contrib><contrib contrib-type="author"><name><surname>Martindale</surname><given-names>Mark Q.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff5"/></contrib><contrib contrib-type="author"><name><surname>de Jong</surname><given-names>Johann</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff6"/></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Kaandorp</surname><given-names>Jaap A.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1"><label/>Computational Science, University of Amsterdam, Science Park 904, Amsterdam, The Netherlands </aff><aff id="Aff2"><label/>Universit&#x000e9; Nice Sophia Antipolis, Institute for Research on Cancer and Aging, Nice (IRCAN), UMR 7284, Nice, France </aff><aff id="Aff3"><label/>Centre National de la Recherche Scientifique (CNRS), Institute for Research on Cancer and Aging, Nice (IRCAN), UMR 7284, Nice, France </aff><aff id="Aff4"><label/>Institut National de la Sant&#x000e9; et de la Recherche M&#x000e9;dicale (INSERM), Institute for Research on Cancer and Aging, Nice (IRCAN), U1081, Nice, France </aff><aff id="Aff5"><label/>Whitney Lab for Marine Bioscience, University of Florida, St. Augustine, FL USA </aff><aff id="Aff6"><label/>Computational Cancer Biology Group, Division of Molecular Carcinogenesis, The Netherlands Cancer Institute, Amsterdam, The Netherlands </aff></contrib-group><pub-date pub-type="epub"><day>24</day><month>9</month><year>2015</year></pub-date><pub-date pub-type="pmc-release"><day>24</day><month>9</month><year>2015</year></pub-date><pub-date pub-type="collection"><year>2015</year></pub-date><volume>9</volume><elocation-id>63</elocation-id><history><date date-type="received"><day>22</day><month>4</month><year>2015</year></date><date date-type="accepted"><day>9</day><month>9</month><year>2015</year></date></history><permissions><copyright-statement>&#x000a9; Botman et al. 2015</copyright-statement><license license-type="OpenAccess"><license-p>
<bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p>The spatial distribution of many genes has been visualized during the embryonic development in the starlet sea anemone <italic>Nematostella vectensis</italic> in the last decade. <italic>In situ</italic> hybridization images are available in the Kahi Kai gene expression database, and a method has been developed to quantify spatial gene expression patterns of <italic>N. vectensis</italic>. In this paper, gene expression quantification is performed on a wide range of gene expression patterns from this database and descriptions of observed expression domains are stored in a separate database for further analysis.</p></sec><sec><title>Methods</title><p>Spatial gene expression from suitable in situ hybridization images has been quantified with the GenExp program. A correlation analysis has been performed on the resulting numerical gene expression profiles for each stage. Based on the correlated clusters of spatial gene expression and detailed descriptions of gene expression domains, various mechanisms for developmental gene expression are proposed.</p></sec><sec><title>Results</title><p>In the blastula and gastrula stages of development in <italic>N. vectensis</italic>, its continuous sheet of cells is partitioned into correlating gene expression domains. During progressing development, these regions likely correspond to different fates. A statistical analysis shows that genes generally remain expressed during the planula stages in those major regions that they occupy at the end of gastrulation.</p></sec><sec><title>Discussion</title><p>Observed shifts in gene expression domain boundaries suggest that elongation in the planula stage mainly occurs in the vegetal ring under the influence of the gene Rx. The secondary body axis in N. vectensis is proposed to be determined at the mid blastula transition.</p></sec><sec><title>Conclusions</title><p>Early gene expression domains in <italic>N. vectensis</italic> appear to maintain a positional order along the primary body axis. Early determination in <italic>N. vectensis</italic> occurs in two stages: expression in broad circles and rings in the blastula is consolidated during gastrulation, and more complex expression patterns appear in the planula within these broad regions. Quantification and comparison of gene expression patterns across a database can generate hypotheses about collective cell movements before these movements are measured directly.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (doi:10.1186/s12918-015-0209-4) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Nematostella vectensis</kwd><kwd>Gene expression quantification</kwd><kwd>Embryonic development</kwd><kwd>Cluster analysis</kwd><kwd>Gene expression database</kwd><kwd>In Situ Hybridization (ISH)</kwd></kwd-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2015</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1" sec-type="introduction"><title>Background</title><p>Spatial gene expression assays can be used as a tool for verifying predicted regulatory interactions between genes and for predicting properties of missing components in a gene regulation network [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. The largest potential of spatial gene product distribution datasets, is in verifying numerical models of regulatory interaction networks, which has been demonstrated for the embryonic development of fruit fly <italic>Drosophila melanogaster</italic> [<xref ref-type="bibr" rid="CR3">3</xref>]. Also the formation of digits in early mouse limbs has been replicated with mechanistic models with the help of gene expression patterns [<xref ref-type="bibr" rid="CR4">4</xref>].</p><p>To perform accurate simulations of such processes, the spatial gene expression patterns need to be digitally quantified and formatted to standardized profiles. A procedure for gene expression quantification has been described [<xref ref-type="bibr" rid="CR5">5</xref>] and applied [<xref ref-type="bibr" rid="CR6">6</xref>] for organisms with changing morphologies during embryonic development. Similarities among gene expression profiles can provide information about co-expression relationships [<xref ref-type="bibr" rid="CR7">7</xref>]. Similarity metrics are a common tool for classifying time series expression data to identify correlating dynamics among genes. These similarity measures can identify correlating spatial expression among genes from quantified expression patterns as well. To use quantified gene expression patterns in dynamic simulations, reliable time points for gene expression patterns are required.</p><p>For example, in <italic>Drosophila</italic>, the spatial gene expression of <italic>even skipped</italic> (<italic>eve</italic>) has been measured precisely for many time points. The <italic>eve</italic> pattern is employed as a time reference: <italic>eve</italic> is assayed together with the queried gene to establish the development time for the sample [<xref ref-type="bibr" rid="CR8">8</xref>]. For many other animal models, a time reference gene is not (yet) available and other embryo properties are applied to estimate the time of development. In these cases the subsequent stages of development can be qualitatively identified from the changing embryo morphology. These changes in morphology are caused by division and migration of cells, processes that are absent during the early cleavage cycles of flies.</p><p>In the comparative gene expression database Kahi Kai, <italic>in situ</italic> RNA hybridization assays are collected for many marine invertebrates [<xref ref-type="bibr" rid="CR9">9</xref>] and are classified according to the embryo morphologies. This database thereby allows for an analysis of spatial expression features for all gene entries.</p><p>In this study, many genes from the Kahi Kai database are compared at various stages of development, based on their expression in different embryonic regions. First, the majority of <italic>in situ</italic> hybridization images are quantified and the quantified gene expression patterns are collected in a list of digital expression profiles. Stage-specific correlation analyses are performed on these spatial gene expression profiles to discover the embryo&#x02019;s major division in expression domains.</p><p>Second, a subset of genes from the database is listed with a detailed description of the spatial expression in the stages for which data is available. This list provides an overview of the developmental stages with spatial hybridization images for each gene and allows a detailed description of expression properties beyond the general classifications. Progression of spatial expression is compared for subsequent available stages, and the main periods of gene expression dynamics are identified.</p><p>A large set of gene expression patterns in the starlet sea anemone <italic>Nematostella vectensis</italic> is analyzed. The determination of the secondary axis in <italic>N. vectensis</italic> is one aspect of gene expression that requires spatial localization. The database contains various genes that are expressed along this axis.</p><p>The change in <italic>N. vectensis</italic> morphology during development is schematically displayed in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>. The nucleus in the egg is located at the future oral pole, which means that the primary (oral-aboral) axis is already determined before fertilization [<xref ref-type="bibr" rid="CR10">10</xref>, <xref ref-type="bibr" rid="CR11">11</xref>]. The determination of the secondary axis, which is defined by the location of the syphonoglyph, is unclear. The first structures that appear along this axis are the primary mesenteries, but differential gene expression is already observed during gastrulation [<xref ref-type="bibr" rid="CR12">12</xref>, <xref ref-type="bibr" rid="CR13">13</xref>]. Based on the early symmetry break in various gene expression patterns and on early <italic>N. vectensis</italic> morphogenesis, a mechanism is proposed for secondary axis determination. Spatial gene expression patterns in early stages of development are necessary to study the determination and formation of the secondary axis.<fig id="Fig1"><label>Fig. 1</label><caption><p>Progressing embryo morphology during <italic>N. vectensis</italic> development. The table estimates the time of development at two different temperatures for the stages until the late planula. Table entries indicate the hours after fertilization derived from [<xref ref-type="bibr" rid="CR11">11</xref>, <xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR21">21</xref>]. The annotations in the schematic morphologies are guidelines for researchers to describe expression domains in their hybridization images. AnHe&#x02009;=&#x02009;animal hemisphere, VeHe&#x02009;=&#x02009;vegetal hemisphere, An&#x02009;=&#x02009;animal pole, Cd&#x02009;=&#x02009;central domain, Cr&#x02009;=&#x02009;central ring, Er&#x02009;=&#x02009;external ring, Ve&#x02009;=&#x02009;vegetal pole, pEn&#x02009;=&#x02009;presumptive endoderm, bEc&#x02009;=&#x02009;blastoporal ectoderm, Ec&#x02009;=&#x02009;ectoderm, OrHe&#x02009;=&#x02009;oral hemisphere, AbHe&#x02009;=&#x02009;aboral hemisphere, OrEc&#x02009;=&#x02009;oral ectoderm, En&#x02009;=&#x02009;endoderm, AbEc&#x02009;=&#x02009;aboral ectoderm, PhEc&#x02009;=&#x02009;pharyngeal ectoderm, PhEn&#x02009;=&#x02009;pharyngeal endoderm, AtEn&#x02009;=&#x02009;apical tuft endoderm, AtEc&#x02009;=&#x02009;apical tuft ectoderm, At&#x02009;=&#x02009;apical tuft, M&#x02009;=&#x02009;mouth, BwEc&#x02009;=&#x02009;body wall ectoderm, BwEn&#x02009;=&#x02009;body wall endoderm, MeEc&#x02009;=&#x02009;mesentery ectoderm, MeEn&#x02009;=&#x02009;mesentery endoderm, TeB&#x02009;=&#x02009;tentacle bud, TeEc&#x02009;=&#x02009;tentacle ectoderm, TeEn&#x02009;=&#x02009;tentacle endoderm, Si&#x02009;=&#x02009;siphonoglyph, TeTi&#x02009;=&#x02009;tentacle tip, TeBa&#x02009;=&#x02009;tentacle base (The original nomenclature in [<xref ref-type="bibr" rid="CR9">9</xref>] has been adapted to the more detailed denotations for the blastula stage in [<xref ref-type="bibr" rid="CR15">15</xref>].)</p></caption><graphic xlink:href="12918_2015_209_Fig1_HTML" id="MO1"/></fig></p><p>We analyzed spatial gene expression patterns in various stages of development in <italic>N. vectensis</italic>. Changes in these patterns are due to gene expression dynamics within stationary cells or due to migrating cells that retain their gene expression state. Fate mapping experiments can conclusively determine migratory behavior, but these data are not available for <italic>N. vectensis</italic>. Our solution to deal with this lack of fate map data is the assumption that the expression state in migrating cells likely remains unchanged for many genes. In this fashion, we estimate major cell movements based on the spatial gene expression data, which are available.</p><p>In conclusion we demonstrate the application of correlation analysis to quantified spatial gene expression patterns in order to identify co-expressed spatial domains.</p><p>A possible application of these correlation matrices is the selection of gene clusters for regulatory experiments, functional studies [<xref ref-type="bibr" rid="CR14">14</xref>] and computational gene regulation network models [<xref ref-type="bibr" rid="CR6">6</xref>], because co-expressed genes often share regulators or biological functions.</p></sec><sec id="Sec2" sec-type="materials|methods"><title>Methods</title><p>The order of application for the described methods is displayed in the diagram in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>. Note that the intermediate results provide new information on their own and can be subjected to additional analyses.<fig id="Fig2"><label>Fig. 2</label><caption><p>Workflow overview. The information stored in the Kahi Kai gene expression database has been processed into convenient formats for two partly overlapping sets of genes. These processing methods and the methods used for additional analyses are described in the text. While this workflow may seem to converge to a single final result, all intermediate results can be explored for multiple purposes</p></caption><graphic xlink:href="12918_2015_209_Fig2_HTML" id="MO2"/></fig></p><sec id="Sec3"><title>Cluster analysis of quantified <italic>in situ</italic> hybridizations</title><p>The genes listed in the Kahi Kai database for <italic>N. vectensis</italic> are screened for useful expression patterns. For one-dimensional quantification, suitable genes are genes that display cylindrical expression in broad domains up to the late planula stage. Other genes, such as those that are expressed on the syphonoglyph side only or in individual cells, require a two-dimensional or three-dimensional quantification method for a complete description. <italic>In situ</italic> hybridization images are imported into GenExp, a MATLAB interface designed to extract and quantify gene expression patterns [<xref ref-type="bibr" rid="CR5">5</xref>]. A continuous series of digital morphologies has been derived from a confocal microscopy study on <italic>N. vectensis</italic> gastrulation. A digital morphology is selected and overlaid with the hybridization image (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3a</xref>). To get a correct alignment, the points of the digital morphology are dragged over the observed cell layer boundaries (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3b</xref>). The cell layer is divided into segments with edges between the inner and outer cell layer boundaries (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3c</xref>). The color intensities of the pixels within each segment are averaged and plotted as a function of the segment&#x02019;s position on the cell layer (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3d</xref>). This plot is edited to compensate for artifacts from the environment, annotations and imperfections in the segmentation (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3e</xref>). The edited plot is interpolated at a hundred equidistant points and the intensity is scaled to unity to arrive at a standardized expression profile. The standardized profiles are ordered in seven groups from blastula to late planula, based on the development label of the source images. The profiles within each group are clustered with average linkage and Pearson correlation distance. The groups are divided into two main clusters, or into three clusters if the second split reduces the largest branch. The profiles within these main clusters are displayed in combined plots. Profiles in any apparent subclusters are plotted together as well.<fig id="Fig3"><label>Fig. 3</label><caption><p>Gene expression quantification pipeline. A digital morphology is overlaid with the gene expression image (<bold>a</bold>) and the points are manually dragged over the embryo&#x02019;s cell layer (<bold>b</bold>). After the cell layer is decomposed into segments (<bold>c</bold>) and the intensity is plotted as a function of cell layer position (<bold>d</bold>), the profile is manually edited to correct for artifacts (<bold>e</bold>)</p></caption><graphic xlink:href="12918_2015_209_Fig3_HTML" id="MO3"/></fig></p></sec><sec id="Sec4"><title>Overview and analysis of expression summaries</title><p>A list of all genes in the Kahi Kai database with <italic>in situs</italic> available from the blastula to the late planula stage was retrieved with the built-in search tool. From this list, those genes were selected with images available in at least two different stages of development. The selected genes are listed in a database table, with descriptions of the expression patterns in available stages. The descriptions are derived from the Kahi Kai expression summary matrix, while correcting possible inconsistencies with the <italic>in situs</italic> (illustrated for the gene <italic>FoxB</italic> in Figs.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> and <xref rid="Fig5" ref-type="fig">5</xref> as an example). Expression in the endoderm wall and ectoderm wall is specified in more detail. If a gene expression pattern exhibits noncylindrical symmetry, this is briefly indicated.<fig id="Fig4"><label>Fig. 4</label><caption><p>Hybridization images for <italic>FoxB</italic> in the Kahi Kai database. If multiple images are available for a developmental stage, these are accessed with the blue arrow buttons</p></caption><graphic xlink:href="12918_2015_209_Fig4_HTML" id="MO4"/></fig><fig id="Fig5"><label>Fig. 5</label><caption><p>Expression summary for <italic>FoxB</italic> in the Kahi Kai database. The indicated expression domains are derived from the available <italic>in situ</italic> RNA hybridizations (some hybridization images are displayed in Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>). Expression in the presumptive endoderm at the early gastrula is incorrect</p></caption><graphic xlink:href="12918_2015_209_Fig5_HTML" id="MO5"/></fig></p><p>All possible pairwise combinations of developmental stages are listed in a spreadsheet, and the expression pattern descriptions from the database table are inserted for subsequent available stages. All combinations with identical expression domains in both the first and the second stage are added up. The extent to which the expression pattern has changed is indicated in a separate column. The instances in which a pattern has remained within the same major region (minor change), shifted across major regions (major change) or vanished are catalogued for genes that start in a single major domain. For each major domain, all possible stages in which a pattern can display a minor change, display a major change or vanish are counted. The relative occurrences of these events in each stage are derived from their counts. The instances that a pattern has displayed minor or major changes with respect to the major expression regions are registered for the complete set of available genes as well, along with their possible first appearance and disappearance.</p></sec></sec><sec id="Sec5" sec-type="results"><title>Results</title><sec id="Sec6"><title>Cluster analysis of quantified <italic>in situ</italic> hybridizations</title><p>For all seven stages of development from blastula to late planula, <italic>in situs</italic> of suitable genes have been quantified. The expression profiles are provided in Additional file <xref rid="MOESM1" ref-type="media">1</xref> in MATLAB format. The quantified patterns are ordered in dendrograms and correlation matrices. The number of analyzed patterns from each stage are: 112 from the blastula (Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>); 52 from the early gastrula (Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref>); 18 from the mid gastrula (Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8</xref>); 25 from the late gastrula (Fig.&#x000a0;<xref rid="Fig9" ref-type="fig">9</xref>); 15 from the early planula (Fig.&#x000a0;<xref rid="Fig10" ref-type="fig">10</xref>); 17 from the planula (Fig.&#x000a0;<xref rid="Fig11" ref-type="fig">11</xref>) and 13 from the late planula (Fig.&#x000a0;<xref rid="Fig12" ref-type="fig">12</xref>). From the correlation matrices, major and minor blocks are identified. The profiles in these blocks are combined in separate plots for the blastula (Fig.&#x000a0;<xref rid="Fig13" ref-type="fig">13</xref>), early gastrula (Fig.&#x000a0;<xref rid="Fig14" ref-type="fig">14</xref>), mid gastrula (Fig.&#x000a0;<xref rid="Fig15" ref-type="fig">15</xref>), late gastrula (Fig.&#x000a0;<xref rid="Fig16" ref-type="fig">16</xref>), early planula (Fig.&#x000a0;<xref rid="Fig17" ref-type="fig">17</xref>), planula (Fig.&#x000a0;<xref rid="Fig18" ref-type="fig">18</xref>) and late planula (Fig.&#x000a0;<xref rid="Fig19" ref-type="fig">19</xref>) stages. Correlating expression domains from the blastula to the late gastrula stages are summarized in Fig.&#x000a0;<xref rid="Fig20" ref-type="fig">20</xref>.<fig id="Fig6"><label>Fig. 6</label><caption><p>Hierarchical clustering of gene expression profiles in the blastula stage. Tcfcle3 is labeled as cleavage, but with a large blastocoel the sample is suitable for quantification. The dendrogram is cut off at a similarity (1 minus the correlation coefficient) of 0.7. For all clusterings, Pearson correlation is used as the distance metric and unweighted average is used for linking</p></caption><graphic xlink:href="12918_2015_209_Fig6_HTML" id="MO6"/></fig><fig id="Fig7"><label>Fig. 7</label><caption><p>Hierarchical clustering of gene expression profiles in the early gastrula stage. The dendrogram is cut off at a similarity of 1.3</p></caption><graphic xlink:href="12918_2015_209_Fig7_HTML" id="MO7"/></fig><fig id="Fig8"><label>Fig. 8</label><caption><p>Hierarchical clustering of gene expression profiles in the mid gastrula stage. The dendrogram is cut off at a similarity of 1.1</p></caption><graphic xlink:href="12918_2015_209_Fig8_HTML" id="MO8"/></fig><fig id="Fig9"><label>Fig. 9</label><caption><p>Hierarchical clustering of gene expression profiles in the late gastrula stage. The dendrogram is cut off at a similarity of 0.6</p></caption><graphic xlink:href="12918_2015_209_Fig9_HTML" id="MO9"/></fig><fig id="Fig10"><label>Fig. 10</label><caption><p>Hierarchical clustering of gene expression profiles in the early planula stage. The dendrogram is cut off at a similarity of 0.9</p></caption><graphic xlink:href="12918_2015_209_Fig10_HTML" id="MO10"/></fig><fig id="Fig11"><label>Fig. 11</label><caption><p>Hierarchical clustering of gene expression profiles in the planula stage. The dendrogram is cut off at a similarity of 0.9</p></caption><graphic xlink:href="12918_2015_209_Fig11_HTML" id="MO11"/></fig><fig id="Fig12"><label>Fig. 12</label><caption><p>Hierarchical clustering of gene expression profiles in the late planula stage. The dendrogram is cut off at a similarity of 0.9</p></caption><graphic xlink:href="12918_2015_209_Fig12_HTML" id="MO12"/></fig><fig id="Fig13"><label>Fig. 13</label><caption><p>Combined plots of quantified gene expression patterns in the blastula stage. The main clusters from Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref> are plotted in large diagrams. The small diagrams are subclusters within the large plot above them. The insets are <italic>in situ</italic> hybridizations from which a profile in the subcluster is derived. The expression domains that arise from the clustering are: central domain (Cd), central ring (Cr) and external ring (Er)</p></caption><graphic xlink:href="12918_2015_209_Fig13_HTML" id="MO13"/></fig><fig id="Fig14"><label>Fig. 14</label><caption><p>Combined plots of quantified gene expression patterns in the early gastrula stage. The main clusters from Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref> are plotted in large diagrams. The small diagrams are subclusters within the large plot above them. The insets are <italic>in situ</italic> hybridizations from which a profile in the subcluster is derived. The expression domains that arise from the clustering are: presumptive endoderm (pEn), blastoporal ectoderm (bEc), vegetal ring (Vr) and vegetal pole (Ve)</p></caption><graphic xlink:href="12918_2015_209_Fig14_HTML" id="MO14"/></fig><fig id="Fig15"><label>Fig. 15</label><caption><p>Combined plots of quantified gene expression patterns in the mid gastrula stage. The main clusters from Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8</xref> are plotted in large diagrams. The small diagrams are subclusters within the large plot above them. The insets are <italic>in situ</italic> hybridizations from which a profile in the subcluster is derived. The expression domains that arise from the clustering are: endoderm (En), oral pole (Or), vegetal ring (Vr) and vegetal pole (Ve)</p></caption><graphic xlink:href="12918_2015_209_Fig15_HTML" id="MO15"/></fig><fig id="Fig16"><label>Fig. 16</label><caption><p>Combined plots of quantified gene expression patterns in the late gastrula stage. The main clusters from Fig.&#x000a0;<xref rid="Fig9" ref-type="fig">9</xref> are plotted in large diagrams. The small diagrams are subclusters within the large plot above them. The insets are <italic>in situ</italic> hybridizations from which a profile in the (sub)cluster is derived. The expression domains that arise from the clustering are: endoderm (En), pharyngeal ectoderm (PhEc), oral end (OrE) and vegetal pole (Ve)</p></caption><graphic xlink:href="12918_2015_209_Fig16_HTML" id="MO16"/></fig><fig id="Fig17"><label>Fig. 17</label><caption><p>Combined plots of quantified gene expression patterns in the early planula stage. The plots represent the clusters in Fig.&#x000a0;<xref rid="Fig10" ref-type="fig">10</xref>. The insets are <italic>in situ</italic> hybridizations from which a profile in the cluster is derived</p></caption><graphic xlink:href="12918_2015_209_Fig17_HTML" id="MO17"/></fig><fig id="Fig18"><label>Fig. 18</label><caption><p>Combined plots of quantified gene expression patterns in the planula stage. The plots represent the clusters in Fig.&#x000a0;<xref rid="Fig11" ref-type="fig">11</xref>. The insets are <italic>in situ</italic> hybridizations from which a profile in the cluster is derived</p></caption><graphic xlink:href="12918_2015_209_Fig18_HTML" id="MO18"/></fig><fig id="Fig19"><label>Fig. 19</label><caption><p>Combined plots of quantified gene expression patterns in the late planula stage. The plots represent the clusters in Fig.&#x000a0;<xref rid="Fig12" ref-type="fig">12</xref>. The insets are <italic>in situ</italic> hybridizations from which a profile in the cluster is derived</p></caption><graphic xlink:href="12918_2015_209_Fig19_HTML" id="MO19"/></fig><fig id="Fig20"><label>Fig. 20</label><caption><p>Overview of gene expression regions at various stages. The clusters and subclusters of correlating standardized expression profiles have been divided in three major regions: central domain/endoderm (<italic>red</italic>), central ring/external ring/oral ectoderm (<italic>green</italic>) and vegetal hemisphere/aboral ectoderm (<italic>blue</italic>)</p></caption><graphic xlink:href="12918_2015_209_Fig20_HTML" id="MO20"/></fig></p><p>For the blastula stage, gene expression is present in the central domain in the 101 profiles in the blue cluster, while expression is excluded from the central domain in the 11 profiles in the red cluster. From the correlation matrix in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>, two smaller blocks with strong correlation and an isolated sample are identified within the blue cluster. The profiles within each subcluster are combined in three small plots below the major blue cluster in Fig.&#x000a0;<xref rid="Fig13" ref-type="fig">13</xref>. The first subcluster contains profiles with gene expression limited to the central domain, while gene expression in the second subcluster is extended to the central ring. The isolated profile displays gene expression in a narrow spot within the central domain. The red cluster in the correlation matrix contains two smaller blocks as well. These subclusters, the small plots below the major red cluster in Fig.&#x000a0;<xref rid="Fig13" ref-type="fig">13</xref>, seem to separate the central ring and the external ring. An earlier study of whole mount <italic>in situ</italic> hybridizations in the <italic>N. vectensis</italic> blastula distinguished four co-expression domains in the animal hemisphere [<xref ref-type="bibr" rid="CR15">15</xref>]. The present cluster analysis confirms the existence of these four domains (central domain, central domain&#x02009;+&#x02009;central ring, central ring and external ring).</p><p>For the early gastrula stage, gene expression is absent in the aboral ectoderm in the 44 profiles in the blue cluster, while this region is included in the expression patterns of the 8 profiles in the red cluster. The blue cluster of the correlation matrix (Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref>) contains three smaller blocks; two outer blocks are clearly separated, while the middle block is positively correlated to both other blocks. The profiles within each subcluster are combined in three small plots below the major blue cluster in Fig.&#x000a0;<xref rid="Fig14" ref-type="fig">14</xref>. Expression in the first and third subclusters is limited to the presumptive endoderm and blastoporal ectoderm, respectively. Expression in the second subcluster covers both regions. The red cluster of the correlation matrix contains two smaller blocks; these subclusters are plotted in Fig.&#x000a0;<xref rid="Fig14" ref-type="fig">14</xref> below the major red cluster. The first subcluster exhibits expression at the vegetal pole, while genes in the second subcluster are expressed in a ring around the vegetal pole.</p><p>For the mid gastrula stage, the blue cluster consists of 13 profiles with expression in the blastopore, while gene expression appears in the aboral ectoderm in the 5 profiles in the red cluster. The blue cluster of the correlation matrix in Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8</xref> contains a block with strongly correlated profiles, while the remaining profiles are correlated somewhat weaker. The first subcluster represents endodermal expression, while expression in the second subcluster is in the oral pole at various ranges from the center. The profiles of both subclusters are displayed in Fig.&#x000a0;<xref rid="Fig15" ref-type="fig">15</xref> below the major blue cluster. The red cluster of the correlation matrix contains two blocks with strongly correlated profiles. The profiles in these blocks, combined in two small plots below the major red cluster in Fig.&#x000a0;<xref rid="Fig15" ref-type="fig">15</xref>, represent gene expression at the vegetal pole and in a ring around this pole, respectively.</p><p>For the late gastrula stage, the green cluster represents endodermal expression, the red and blue clusters represent ectodermal expression in the oral and aboral pole, respectively. Two smaller blocks are visible within the red cluster; the profiles in these subclusters are displayed in two small plots in Fig.&#x000a0;<xref rid="Fig16" ref-type="fig">16</xref>. Gene expression in the first subcluster is limited to the oral end, while expression in the second subcluster is extended inwards to the pharynx.</p><p>For the early planula stage, the three clusters represent the oral pole, the aboral ectoderm and the aboral endoderm. The profiles in these clusters are plotted in red, blue and black in Fig.&#x000a0;<xref rid="Fig17" ref-type="fig">17</xref>, respectively.</p><p>For the planula stage, the characteristic expression domains in each of the three clusters are the aboral pole ectoderm, the oral pole ectoderm and the endoderm, respectively. The profiles in these clusters are collected in the blue, green and red plots in Fig.&#x000a0;<xref rid="Fig18" ref-type="fig">18</xref>, respectively.</p><p>For the late planula stage, the first two clusters show expression in the aboral and oral ectoderm, respectively, while the profiles in the third cluster show expression in multiple locations. The clusters are displayed in the red, blue and green plots in Fig.&#x000a0;<xref rid="Fig19" ref-type="fig">19</xref>, respectively.</p></sec><sec id="Sec7"><title>Overview and analysis of expression summaries</title><p>From the Kahi Kai gene expression database, 73 genes have hybridization images available for at least two stages from blastula to late planula. These genes are listed with descriptions of their expression in a Microsoft Access database sheet (Additional file <xref rid="MOESM2" ref-type="media">2</xref>). Counts of pairwise expression domains are listed in a Microsoft Excel spreadsheet (Additional file <xref rid="MOESM3" ref-type="media">3</xref>). The central domain/endoderm, central ring/external ring/oral ectoderm and vegetal hemisphere/aboral ectoderm are selected as major expression regions.</p><p>The changes or lack of change in expression patterns starting in a single major expression region are shown in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> (central domain/endoderm), Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref> (central ring/external ring/oral ectoderm) and Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref> (vegetal hemisphere/aboral ectoderm). Sums of blocks from these matrices are displayed in Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>, Table&#x000a0;<xref rid="Tab5" ref-type="table">5</xref> and Table&#x000a0;<xref rid="Tab6" ref-type="table">6</xref>, respectively.<table-wrap id="Tab1"><label>Table 1</label><caption><p>Gene expression behavior with initial expression limited to the central domain/endoderm</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th>Initial stage</th><th>Blastula</th><th>Early gastrula</th><th>Mid gastrula</th><th>Late gastrula</th><th>Early planula</th><th>Planula</th></tr></thead><tbody><tr><td>final stage</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02002;early gastrula</td><td/><td>(21:1:1)</td><td/><td/><td/><td/><td/></tr><tr><td>&#x02002;mid gastrula</td><td/><td>
<bold>(1:0:0)</bold>
</td><td>
<bold>(4:2:0)</bold>
</td><td/><td/><td/><td/></tr><tr><td>&#x02002;late gastrula</td><td/><td>
<bold>(0:0:0)</bold>
</td><td>
<bold>(4:1:0)</bold>
</td><td>(3:0:0)</td><td/><td/><td/></tr><tr><td>&#x02002;early planula</td><td/><td>
<bold>(0:1:0)</bold>
</td><td>
<bold>(0:0:0)</bold>
</td><td>(1:0:0)</td><td>(0:0:0)</td><td/><td/></tr><tr><td>&#x02002;planula</td><td/><td>
<bold>(0:0:0)</bold>
</td><td>
<bold>(0:0:0)</bold>
</td><td>(0:0:0)</td><td>(4:1:0)</td><td>(7:0:0)</td><td/></tr><tr><td>&#x02002;late planula</td><td/><td>
<bold>(0:0:0)</bold>
</td><td>
<bold>(0:0:0)</bold>
</td><td>(0:0:0)</td><td>(0:0:0)</td><td>(0:1:0)</td><td>(9:0:0)</td></tr></tbody></table><table-wrap-foot><p>Entries from the pairwise gene expression spreadsheet (Additional file <xref rid="MOESM3" ref-type="media">3</xref>) that start with expression only in the central domain/endoderm are included in this table. For all combinations of stages, the modes of progression of gene expression (minor change:major change:vanished) are counted. The combinations in bold numbers include the period from the early gastrula to the mid gastrula</p></table-wrap-foot></table-wrap><table-wrap id="Tab2"><label>Table 2</label><caption><p>Gene expression behavior with initial expression limited to the central ring/external ring/oral ectoderm</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th>Initial stage</th><th>Blastula</th><th>Early gastrula</th><th>Mid gastrula</th><th>Late gastrula</th><th>Early planula</th><th>Planula</th></tr></thead><tbody><tr><td>Final stage</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02002;Early gastrula</td><td/><td>(8:0:0)</td><td/><td/><td/><td/><td/></tr><tr><td>&#x02002;Mid gastrula</td><td/><td>(0:0:0)</td><td>(5:0:0)</td><td/><td/><td/><td/></tr><tr><td>&#x02002;Late gastrula</td><td/><td>(1:0:0)</td><td>(2:2:0)</td><td>(8:0:0)</td><td/><td/><td/></tr><tr><td>&#x02002;Early planula</td><td/><td>(0:1:0)</td><td>(1:0:0)</td><td>(1:0:0)</td><td>(4:1:0)</td><td/><td/></tr><tr><td>&#x02002;Planula</td><td/><td>(0:0:0)</td><td>(0:0:0)</td><td>(0:0:0)</td><td>(2:1:0)</td><td>(4:0:1)</td><td/></tr><tr><td>&#x02002;Late planula</td><td/><td>(0:0:0)</td><td>(0:0:0)</td><td>(0:0:0)</td><td>(0:0:0)</td><td>(1:0:0)</td><td>(3:0:0)</td></tr></tbody></table><table-wrap-foot><p>Entries from the pairwise gene expression spreadsheet (Additional file <xref rid="MOESM3" ref-type="media">3</xref>) that start with expression only in the central ring/external ring/oral ectoderm are included in this table. For all combinations of stages, the modes of progression of gene expression (minor change:major change:vanished) are counted</p></table-wrap-foot></table-wrap><table-wrap id="Tab3"><label>Table 3</label><caption><p>Gene expression behavior with initial expression limited to the vegetal hemisphere/aboral ectoderm</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th>Initial stage</th><th>Blastula</th><th>Early gastrula</th><th>Mid gastrula</th><th>Late gastrula</th><th>Early planula</th><th>Planula</th></tr></thead><tbody><tr><td>Final stage</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02002;Early gastrula</td><td/><td>(0:0:0)</td><td/><td/><td/><td/><td/></tr><tr><td>&#x02002;Mid gastrula</td><td/><td>(0:0:0)</td><td>(2:1:0)</td><td/><td/><td/><td/></tr><tr><td>&#x02002;Late gastrula</td><td/><td>(0:0:0)</td><td>(2:0:0)</td><td>(1:0:0)</td><td/><td/><td/></tr><tr><td>&#x02002;Early planula</td><td/><td>(0:0:0)</td><td>(0:0:0)</td><td>(1:0:0)</td><td>(3:0:0)</td><td/><td/></tr><tr><td>&#x02002;Planula</td><td/><td>(0:0:0)</td><td>(0:1:0)</td><td>(1:0:0)</td><td>(1:0:0)</td><td>(2:0:0)</td><td/></tr><tr><td>&#x02002;Late planula</td><td/><td>(0:0:0)</td><td>(0:0:0)</td><td>(0:0:0)</td><td>(0:0:0)</td><td>(1:0:0)</td><td>(1:1:0)</td></tr></tbody></table><table-wrap-foot><p>For pairs of subsequent stages, the modes of progression of gene expression (minor change:major change:vanished) are counted</p></table-wrap-foot></table-wrap><table-wrap id="Tab4"><label>Table 4</label><caption><p>Sums of possible periods for gene expression behavior in the central domain/endoderm</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Two-stage period</th><th>Possible counts</th><th>Percentages</th></tr></thead><tbody><tr><td>Blastula-early gastrula</td><td>(22:2:1)</td><td>(88:8:4)</td></tr><tr><td>Early gastrula-mid gastrula</td><td>
<bold>(9:4:0)</bold>
</td><td>(69:31:0)</td></tr><tr><td>Mid gastrula-late gastrula</td><td>(8:2:0)</td><td>(80:20:0)</td></tr><tr><td>Late gastrula-early planula</td><td>(5:2:0)</td><td>(71:29:0)</td></tr><tr><td>Early planula-planula</td><td>(11:2:0)</td><td>(85:15:0)</td></tr><tr><td>Planula-late planula</td><td>(9:1:0)</td><td>(90:10:0)</td></tr></tbody></table><table-wrap-foot><p>For each period, the possible changes in gene expression (minor change:major change:vanished) are added in the second column and expressed as percentages in the third column. As an example, the period from the early gastrula to the mid gastrula (bold numbers) is included in the combinations of stages highlighted in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>
</p></table-wrap-foot></table-wrap><table-wrap id="Tab5"><label>Table 5</label><caption><p>Sums of possible periods for gene expression behavior in the central ring/external ring/oral ectoderm</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Two-stage period</th><th>Possible counts</th><th>Percentages</th></tr></thead><tbody><tr><td>Blastula-early gastrula</td><td>(9:1:0)</td><td>(90:10:0)</td></tr><tr><td>Early gastrula-mid gastrula</td><td>(9:3:0)</td><td>(75:25:0)</td></tr><tr><td>Mid gastrula-late gastrula</td><td>(13:3:0)</td><td>(81:19:0)</td></tr><tr><td>Late gastrula-early planula</td><td>(8:3:0)</td><td>(73:27:0)</td></tr><tr><td>Early planula-planula</td><td>(7:1:1)</td><td>(78:11:11)</td></tr><tr><td>Planula-late planula</td><td>(4:0:0)</td><td>(100:0:0)</td></tr></tbody></table><table-wrap-foot><p>For each period, the possible changes in gene expression (minor change:major change:vanished) are added in the second column and expressed as percentages in the third column</p></table-wrap-foot></table-wrap><table-wrap id="Tab6"><label>Table 6</label><caption><p>Sums of possible periods for gene expression behavior in the vegetal hemisphere/aboral ectoderm</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Two-stage period</th><th>Possible counts</th><th>Percentages</th></tr></thead><tbody><tr><td>Blastula-early gastrula</td><td>(0:0:0)</td><td>(0:0:0)</td></tr><tr><td>Early gastrula-mid gastrula</td><td>(4:2:0)</td><td>(67:33:0)</td></tr><tr><td>Mid gastrula-late gastrula</td><td>(5:1:0)</td><td>(83:17:0)</td></tr><tr><td>Late gastrula-early planula</td><td>(6:1:0)</td><td>(86:14:0)</td></tr><tr><td>Early planula-planula</td><td>(5:1:0)</td><td>(83:17:0)</td></tr><tr><td>Planula-late planula</td><td>(2:1:0)</td><td>(67:33:0)</td></tr></tbody></table><table-wrap-foot><p>For each period, the possible changes in gene expression (minor change:major change:vanished) are added in the second column and expressed as percentages in the third column</p></table-wrap-foot></table-wrap></p><p>A total of 25 genes for which <italic>in situs</italic> are available in the blastula stage are exclusively expressed in the central domain. From these genes, 22 are expressed in the endoderm in the next stage with available <italic>in situs</italic>. Expression of 2 genes has changed beyond the endoderm in the next available stage and 1 gene is no longer expressed at all. This means that 88&#x000a0;% of the genes expressed only in the central domain in the blastula is subsequently expressed only in the endoderm. From all combinations of subsequent available stages that include the early gastrula-mid gastrula period (highlighted in Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>), 13 genes are initially only expressed in the central domain/endoderm. From these genes, 4 expression patterns have changed beyond the endoderm, which is 31&#x000a0;%. Likewise, out of the 10 genes initially limited to the central domain/endoderm, the major change of 2 (20&#x000a0;%) could possibly occur in the mid gastrula-late gastrula period. Out of the 7 genes expressed between the late gastrula and early planula, 2 (29&#x000a0;%) could have changed beyond the endoderm during this interval.</p><p>Out of the 10 genes expressed only in the central ring or external ring in the blastula, 9 (90&#x000a0;%) are subsequently expressed only in the oral ectoderm.</p><p>No blastula <italic>in situs</italic> are available for genes expressed in the vegetal hemisphere. Out of the 6 genes expressed only in the early gastrula vegetal hemisphere, 4 (67&#x000a0;%) are subsequently expressed only in the aboral ectoderm.</p><p>The changes or lack of change in expression in all regions are shown in Table&#x000a0;<xref rid="Tab7" ref-type="table">7</xref>. Sums of blocks from this matrix are displayed in Table&#x000a0;<xref rid="Tab8" ref-type="table">8</xref>. Out of all 41 genes with images stored in the blastula stage, 31 (76&#x000a0;%) display expression in the next available stage in the same major region(s). The added percentages for major changes and first appearance of gene expression are highest in the early gastrula to early planula stages (<italic>n</italic>&#x02009;=&#x02009;214, <italic>p</italic>&#x02009;=&#x02009;0.03, two-tail Fisher&#x02019;s exact test).<table-wrap id="Tab7"><label>Table 7</label><caption><p>Gene expression behavior for all regions combined</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th>Initial stage</th><th>Blastula</th><th>Early gastrula</th><th>Mid gastrula</th><th>Late gastrula</th><th>Early planula</th><th>Planula</th></tr></thead><tbody><tr><td>Final stage</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>&#x02002;Early gastrula</td><td/><td>(29:3:1:1:1)</td><td/><td/><td/><td/><td/></tr><tr><td>&#x02002;Mid gastrula</td><td/><td>(1:0:2:0:0)</td><td>(12:3:0:0:1)</td><td/><td/><td/><td/></tr><tr><td>&#x02002;Late gastrula</td><td/><td>(1:0:0:0:0)</td><td>(8:3:2:0:0)</td><td>(12:0:1:0:0)</td><td/><td/><td/></tr><tr><td>&#x02002;Early planula</td><td/><td>(0:2:0:0:0)</td><td>(1:0:1:0:0)</td><td>(4:1:4:0:0)</td><td>(8:2:1:0:0)</td><td/><td/></tr><tr><td>&#x02002;Planula</td><td/><td>(0:0:0:0:0)</td><td>(0:3:3:0:0)</td><td>(2:0:3:0:0)</td><td>(7:2:2:0:0)</td><td>(17:0:0:1:0)</td><td/></tr><tr><td>&#x02002;Late planula</td><td/><td>(0:0:0:0:0)</td><td>(0:0:0:0:0)</td><td>(0:2:1:0:0)</td><td>(0:0:0:0:0)</td><td>(2:1:0:0:0)</td><td>(16:2:0:0:0)</td></tr></tbody></table><table-wrap-foot><p>All entries from the pairwise gene expression spreadsheet (Additional file <xref rid="MOESM3" ref-type="media">3</xref>) are included in this table. For all combinations of stages, the modes of progression of gene expression (minor change:major change:appeared:vanished:none) are counted</p></table-wrap-foot></table-wrap><table-wrap id="Tab8"><label>Table 8</label><caption><p>Sums of possible periods for gene expression behavior in all regions</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Two-stage period</th><th>Possible counts</th><th>Percentages</th></tr></thead><tbody><tr><td>Blastula-early gastrula</td><td>(31:5:3:1:1)</td><td>(76:12:7:2:2)</td></tr><tr><td>Early gastrula-mid gastrula</td><td>(23:11:8:0:1)</td><td>(53:26:19:0:2)</td></tr><tr><td>Mid gastrula-late gastrula</td><td>(28:11:15:0:0)</td><td>(52:20:28:0:0)</td></tr><tr><td>Late gastrula-early planula</td><td>(22:12:15:0:0)</td><td>(45:24:31:0:0)</td></tr><tr><td>Early planula-planula</td><td>(28:8:9:1:0)</td><td>(61:17:20:2:0)</td></tr><tr><td>Planula-late planula</td><td>(18:5:1:0:0)</td><td>(75:21:4:0:0)</td></tr></tbody></table><table-wrap-foot><p>For each period, the possible changes in gene expression (minor change:major change:appeared:vanished:none) are added in the second column and expressed as percentages in the third column</p></table-wrap-foot></table-wrap></p><p>The latter trend was also observed for the subsets of genes initially expressed exclusively in the endoderm and in the oral ectoderm, respectively. This could not be statistically confirmed though (<italic>p</italic>&#x02009;=&#x02009;0.31 and <italic>p</italic>&#x02009;=&#x02009;0.42, respectively), due to lower numbers (<italic>n</italic>&#x02009;=&#x02009;53 and <italic>n</italic>&#x02009;=&#x02009;51, respectively). The data points for genes expressed in the aboral ectoderm are too few (<italic>n</italic>&#x02009;=&#x02009;28) to observe any trend.</p></sec></sec><sec id="Sec8" sec-type="discussion"><title>Discussion</title><p>A gene with expression profiles from multiple samples at one stage can belong to more than one cluster. This may be due to the noise in gene expression among individuals. Another possible explanation is that the expression patterns are different across individual <italic>in situs</italic> at the beginning and at the end of a developmental stage. This change could be caused by cells migrating from one region to another, or by dynamic regulatory interactions. In the current study, this issue is handled by performing cluster analyses to all expression profiles for all genes within a broad time window instead of analyzing blurred averages of each gene. A solution to this uncertainty would be an increased time resolution for the expression profiles, resulting in more precise regions for narrower time windows. In <italic>Drosophila</italic> embryos, the definition of narrow time classes allowed the observation of significant domain shifts [<xref ref-type="bibr" rid="CR4">4</xref>]. In the sea urchin, precise timing resulted in a sequence of spatial regulatory states [<xref ref-type="bibr" rid="CR16">16</xref>].</p><p>Central domain expression is generally persistent in the endoderm, while expression in the central and external rings is often limited to the oral ectoderm during and after gastrulation. The first entry in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> shows that the early gastrula expression pattern is known for 23 genes that are expressed exclusively in the central domain in the blastula stage. Out of these 23 genes, 21 are expressed in the presumptive endoderm in the early gastrula stage. Similarly, all 8 genes expressed exclusively in the central or external ring in the blastula with known expression in the early gastrula, are expressed in the blastoporal ectoderm in the latter stage as summarized in the first entry in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>. Moreover, expression is observed exclusively in the same major domain in the next available stage for at least 69&#x000a0;% of all instances of expression limited to either of these two major domains, as indicated in the last column of Tables&#x000a0;<xref rid="Tab4" ref-type="table">4</xref> and <xref rid="Tab5" ref-type="table">5</xref>. These observations are a strong indication that the central domain differentiates into the later endoderm and that the central and external rings become oral ectoderm. The vegetal hemisphere likely becomes aboral ectoderm, but this is based on a small number of expression patterns (Table&#x000a0;<xref rid="Tab6" ref-type="table">6</xref>). These differentiation regions are in agreement with the locations of a dye injected into an <italic>N. vectensis</italic> egg and recorded during embryonic development [<xref ref-type="bibr" rid="CR11">11</xref>]. Injection of lineage tracers into individual cells of developing <italic>N. vectensis</italic> embryos results in contiguous clones of labeled cells without long distant migration of individual cells (Martindale, unpublished observations). Based on the statistical stability of the main gene expression regions, a regulation mechanism is proposed where genes in early regions activate genes in corresponding later regions, while genes in adjacent regions repress one another (Fig.&#x000a0;<xref rid="Fig21" ref-type="fig">21</xref>).<fig id="Fig21"><label>Fig. 21</label><caption><p>Proposed interactions between gene clusters in various expression regions. Early expression clusters activate or develop into later expression clusters in corresponding regions (<italic>green arrows</italic>). Neighboring expression clusters inhibit each other (<italic>red arrows</italic>). The blastula domains developing into the body wall ectoderm region are derived from a single gene expression pattern (see text). For this reason, the proposed interactions with the body wall ectoderm are indicated with dashed arrows</p></caption><graphic xlink:href="12918_2015_209_Fig21_HTML" id="MO21"/></fig></p><p>If changes in expression patterns occur consistently in many genes, this might indicate a collective cellular motion in the embryo. During gastrulation, expression in the region between the oral and vegetal poles is observed for the quantified profiles of <italic>Anthox1</italic>, <italic>FGFRa</italic>, <italic>FoxD.1</italic>, <italic>Sox1</italic>, <italic>Sox3</italic> and <italic>Rx</italic>. Due to intense <italic>Sox1</italic> expression in the oral pole, the <italic>Sox1</italic> profiles are outside the vegetal ring clusters. <italic>Anthox1</italic>, <italic>FGFRa</italic> and <italic>FoxD.1</italic> are likewise classified as members of the vegetal pole cluster because their strongest expression occurs in the corresponding region. For these six genes, a single <italic>in situ</italic> in the blastula stage has been stored: this <italic>Sox1</italic> image shows staining throughout the animal hemisphere. This hybridization experiment is a weak indication that genes in the body wall ectoderm are first expressed in the external ring or vegetal ring of the blastula. This is basically the null hypothesis; in the absence of any known collective cell movements in the aboral ectoderm during gastrulation, expression in this region has likely remained stationary from the blastula. The body wall has been included in the proposed regulatory interactions among clustered gene expression regions (Fig.&#x000a0;<xref rid="Fig21" ref-type="fig">21</xref>). The vegetal domains of <italic>Anthox1</italic>, <italic>FGFRa</italic> and <italic>FoxD.1</italic> become restricted to the aboral end in the planula stage, while <italic>Sox1</italic> and <italic>Sox3</italic> move towards the oral pole in the planula. Meanwhile, the <italic>Rx</italic> domain has significantly expanded in the late planula. These changes in gene expression domains may indicate that ectoderm elongation in the planula stage is most pronounced in the initially narrow <italic>Rx</italic> expression domain. Even though this hypothesis is based on few observations, it could be tested with fate mapping experiments. In the frog <italic>Xenopus laevis</italic>, the gene <italic>rax</italic> promotes cell proliferation in developing retinal tissue [<xref ref-type="bibr" rid="CR17">17</xref>]; <italic>Rx</italic> may similarly induce tissue growth in <italic>N. vectensis</italic>.</p><p>Many hierarchical clusters include profiles with strong pairwise correlations to profiles in other clusters. This is caused by the partial overlap of the expression domains that characterize the clusters and by expression of some genes in both regions. The average cutoff value for the main clusters is 0.9; this value is quite high and indicates fuzzy boundaries between many expression clusters. These fuzzy boundaries between expression clusters are likely due to dynamic changes in gene expression. Out of the 25 genes expressed only in the central domain, 3 (12&#x000a0;%) will exhibit major expression changes, or terminate their expression (Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>, first row). Out of the 10 genes expressed in the central ring or external ring, 1 (10&#x000a0;%) is not expressed in the oral ectoderm (Table&#x000a0;<xref rid="Tab5" ref-type="table">5</xref>, first row). This explains why some genes are expressed in multiple clusters at the same stage, and why some genes appear in clusters for different expression domains between developmental stages.</p><p>According to the hypothesis that the position of cells in the planula is largely determined by their position in the blastula, the change of gene expression patterns across major regions must be the result of regulatory action. The added percentages for possible major change and first appearance of gene expression in the early gastrula to early planula stages are 45, 48 and 55&#x000a0;% (Table&#x000a0;<xref rid="Tab8" ref-type="table">8</xref>). In comparison, these percentages are 37 and 25&#x000a0;% in the early planula to late planula stages. These observations suggest that more dynamic changes in expression occur during gastrulation than in the period that the planula transforms into a polyp. For the study of pattern formation in <italic>N. vectensis</italic>, recording gene expression during the relatively short period of gastrulation should therefore be more informative than monitoring the planula stages. In general, during gastrulation it is decided in which major region(s) the genes are expressed. Detailed expression patterns arise during the planula stages, generally within the bounds of the major regions determined for each gene at the end of gastrulation. The appearance of differential details explains the decrease in correlation among gene expression patterns after gastrulation (Figs.&#x000a0;<xref rid="Fig10" ref-type="fig">10</xref>, <xref rid="Fig11" ref-type="fig">11</xref>, <xref rid="Fig12" ref-type="fig">12</xref>, <xref rid="Fig17" ref-type="fig">17</xref>, <xref rid="Fig18" ref-type="fig">18</xref> and <xref rid="Fig19" ref-type="fig">19</xref>). This loss of correlation could also be caused by the lower number of quantified patterns in the planula stages; with additional gene expression quantifications in these stages of development it could be tested whether the expression domains diverge or whether they form a new set of clusters.</p><p>One aspect of gene expression that requires spatial localization, is the determination of the secondary axis in <italic>N. vectensis</italic>. Individual cells appear indistinguishable during the early cleavage cycles until the early blastula starts oscillating at the animal pole [<xref ref-type="bibr" rid="CR18">18</xref>]. This oscillation stops at the mid blastula transition, when the synchrony of cell divisions is lost and the blastula remains spherical. The spherical blastula symmetry is permanently broken at the onset of gastrulation [<xref ref-type="bibr" rid="CR19">19</xref>]. During gastrulation, Bmp ligands and antagonists are asymmetrically expressed along the secondary axis [<xref ref-type="bibr" rid="CR13">13</xref>]. Based on these observations, determination of the secondary axis may coincide with the mid blastula transition. The asynchrony in cell divisions then produces a stochastic perturbation in a morphogen concentration which would define the secondary axis.</p><p>The gene expression database contains various genes that are differentially expressed along the secondary axis, although most are not identified in the expression summaries. From the incomplete list of genes included in the expression pattern overview in this study (Additional file <xref rid="MOESM2" ref-type="media">2</xref>), various genes involved in secondary axis formation can already be identified. The genes <italic>Anthox7</italic>, <italic>Anthox8b</italic>, <italic>Bmp2/4</italic>, <italic>chordin</italic>, <italic>Gbx</italic>, <italic>Hex</italic>, <italic>Msx</italic>, <italic>Msx2</italic>, <italic>NvHD060</italic>, <italic>Smad1/5</italic> and <italic>Vent1</italic> exhibit noncylindrical expression patterns. Quantification of these patterns requires a two-dimensional or three-dimensional template and eventually a three-dimensional detection method. Especially <italic>Vent1</italic> asymmetric expression in the early gastrula stands out. In various animals, <italic>vent</italic> genes are involved in a Bmp signaling feedback loop [<xref ref-type="bibr" rid="CR20">20</xref>]. Curiously, the genes <italic>Bmp2/4</italic>, <italic>chordin</italic> and <italic>Smad1/5</italic> in this signaling pathway are still symmetrically expressed in the <italic>N. vectensis</italic> early gastrula (or at least their asymmetry is less pronounced).</p><p>The <italic>N. vectensis in situ</italic> hybridization collection contains expression data for less than half of all developmental stages for most genes stored. Despite the sparsity of this dataset, a large-scale analysis results in meaningful insights. Sparse data is common for databases that contain labor intensive measurements, and standardization allows global analysis approaches to be applied to incomplete biological databases. Developmental gene expression databases contain large sets of genes with many regulatory interactions. Our clustering-by-region approach is convenient to select genes from a large set for computational regulatory network modeling.</p></sec><sec id="Sec9" sec-type="conclusion"><title>Conclusions</title><p>Our cluster analysis indicates that early gene expression domains in <italic>N. vectensis</italic> are spatially separated in a stable sequence along the primary body axis. An additional statistical analysis indicates that precise gene expression domains in <italic>N. vectensis</italic> are generally formed by two processes. Genes that are expressed in the blastula appear in broad expression regions. During gastrulation and planula development, the expression domains are refined within the boundaries of these broad regions.</p><p>It should be stressed that no additional experiments have been performed for our study. Spatial expression data in a public gene expression database have been quantified and analysed, and these analyses resulted in new hypotheses on cellular migration in <italic>N. vectensis</italic>. Spatial gene expression patterns have been collected in extensive databases for other model animals as well, and a similar computational approach can generate hypotheses about cellular migration before fate maps are available for these animals.</p><sec id="Sec10"><title>Availability of supporting data</title><p>The quantified spatial gene expression profiles and descriptions of spatial gene expression patterns supporting the results of this article are included within Additional file <xref rid="MOESM1" ref-type="media">1</xref> and Additional file <xref rid="MOESM2" ref-type="media">2</xref>, respectively. The original set of <italic>N. vectensis in situ</italic> hybridizations is available in the Kahi Kai repository, <ext-link ext-link-type="uri" xlink:href="http://www.kahikai.org/index.php?content=list_genes&#x00026;speciesid=1">http://www.kahikai.org/index.php?content=list_genes&#x00026;speciesid=1</ext-link>.</p></sec></sec></body><back><app-group><app id="App1"><sec id="Sec11"><title>Additional files</title><p><media position="anchor" xlink:href="12918_2015_209_MOESM1_ESM.rar" id="MOESM1"><label>Additional file 1:</label><caption><p>
<bold>MATLAB files of standardized gene expression profiles derived from stored </bold>
<bold><italic>in situ</italic></bold>
<bold>hybridizations.</bold> The profiles are provided both as separate numerical arrays in labeled .mat files and as a two-column array of cells in the file &#x0201c;allarrs.mat&#x0201d;. For the separate arrays, the labels are the filenames; for the array of cells, the labels are the character arrays in the first column. The labels include the gene name, the developmental stage and, if applicable, the sequence number. For example, the file &#x0201c;admp-relatedbla1.mat&#x0201d; contains the variable &#x0201c;profile&#x0201d;, which is a 1&#x000d7;100 numerical array from the first image during the blastula of admp-related. This numerical array is also located in the second column of the 252&#x000d7;2 cell array called &#x0201c;expressiondata&#x0201d; in the file &#x0201c;allarrs.mat&#x0201d;, behind character array &#x0201c;admp-relatedbla1&#x0201d; in the first column. (cle&#x02009;=&#x02009;cleavage, bla&#x02009;=&#x02009;blastula, ega&#x02009;=&#x02009;early gastrula, mga&#x02009;=&#x02009;mid gastrula, lga&#x02009;=&#x02009;late gastrula, epl&#x02009;=&#x02009;early planula, pla&#x02009;=&#x02009;planula, lpl&#x02009;=&#x02009;late planula). The cell array has been converted to comma separated table &#x0201c;expressiontable.txt&#x0201d;, to be processed outside MATLAB and in modified MATLAB releases. The text file has been produced with the script &#x0201c;exportexpression.m&#x0201d; and can be restored to cell array &#x0201c;expressiondata0&#x0201d; in file &#x0201c;allarrs0.mat&#x0201d; with the script &#x0201c;importexpression.m&#x0201d;. (RAR 230 kb)</p></caption></media><media position="anchor" xlink:href="12918_2015_209_MOESM2_ESM.accdb" id="MOESM2"><label>Additional file 2:</label><caption><p>
<bold>Observed expression domains of genes with</bold>
<bold><italic>in situ</italic></bold>
<bold>hybridizations stored for multiple stages of embryonic development.</bold> The pattern descriptions are ordered in a Microsoft Access database spreadsheet. Shorthand notations: base&#x02009;=&#x02009;base of tentacle; cells&#x02009;=&#x02009;individual cells; syphonoglyph&#x02009;=&#x02009;syphonoglyph side; tuft&#x02009;=&#x02009;apical tuft; wall&#x02009;=&#x02009;body wall. &#x0201c;N/A&#x0201d; means no hybridization image and description are currently provided for this developmental stage. &#x0201c;?&#x0201d; means an entry in the expression summary is provided without a corresponding hybridization image. &#x0201c;none&#x0201d; means no gene expression is observed. &#x0201c;band&#x0201d; means expression is observed in a ring between the oral and aboral ends. &#x0201c;full&#x0201d; means expression is observed in the complete tissue layer. &#x0201c;biradial&#x0201d; means two disconnected expression domains are observed on opposite sides (suggesting a biradially symmetric pattern). &#x0201c;octoradial?&#x0201d; means expression is observed in eight separate domains, possibly suggesting octoradial symmetry. &#x0201c;one side&#x0201d; means expression is present in one side of the embryo, but this side (either the syphonoglyph side or the non-syphonoglyph side) is not indicated. (ACCDB 5268 kb)</p></caption></media><media position="anchor" xlink:href="12918_2015_209_MOESM3_ESM.xlsx" id="MOESM3"><label>Additional file 3:</label><caption><p>
<bold>Subsequent expression domains of genes from an expression database.</bold> Pairs of subsequently available expression domains (third and fourth columns) are ordered in a Microsoft Excel spreadsheet table by their initial and final stages of development (first and second columns, respectively). The percentages (sixth column) are the relative frequencies (from the gene counts in the fifth column) for each pair of expression domains. A change from cylindrical expression pattern symmetry to biradial or bilateral symmetry is indicated in the seventh column with &#x0201c;symmetry break&#x0201d;. Changes of expression over major regions are indicated in the eighth column (appeared&#x02009;=&#x02009;expression is observed only in the second stage, major change&#x02009;=&#x02009;expression moved to another major region, minor change&#x02009;=&#x02009;expression in the second stage is limited to the same major region(s) as in the first stage, none&#x02009;=&#x02009;no expression is observed in both stages, vanished&#x02009;=&#x02009;expression is observed only in the first stage). (XLSX 21 kb)</p></caption></media></p></sec></app></app-group><fn-group><fn><p><bold>Competing interests</bold></p><p>The authors declare that they have no competing interests.</p></fn><fn><p><bold>Authors&#x02019; contributions</bold></p><p>DB carried out the gene expression quantifications and the cluster analysis, performed the statistical analysis and drafted the manuscript. FJ helped to analyse the data and to draft the manuscript. ER designed the gene expression database, carried out the majority of <italic>in situ</italic> hybridizations and helped to draft the manuscript. MQM participated in the design of the study, helped to draft the manuscript and provided the experimental equipment and the laboratory space for the majority of the experiments. JJ designed the gene expression quantification software, conceived of the cluster analysis and helped to draft the manuscript. JAK participated in the design of the study and helped to draft the manuscript. All authors read and approved the final manuscript.</p></fn><fn><p><bold>Authors&#x02019; information</bold></p><p>Not applicable.</p></fn><fn><p><bold>Availability of data and materials</bold></p><p>Not applicable.</p></fn></fn-group><ack><title>Funding</title><p>Funding for this study is provided by the BioPreDyn (DB) and Swarm-Organ (FJ) projects as part of the Seventh Framework Program (FP7 grants 289434 and 601062).</p></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>E</given-names></name><name><surname>Davidson</surname><given-names>EH</given-names></name></person-group><article-title>Building developmental gene regulatory networks</article-title><source>Birth Defects Res C Embryo Today</source><year>2009</year><volume>87</volume><fpage>123</fpage><lpage>30</lpage><pub-id pub-id-type="doi">10.1002/bdrc.20152</pub-id><?supplied-pmid 19530131?><pub-id pub-id-type="pmid">19530131</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chan</surname><given-names>TM</given-names></name><name><surname>Longabaugh</surname><given-names>W</given-names></name><name><surname>Bolouri</surname><given-names>H</given-names></name><name><surname>Chen</surname><given-names>HL</given-names></name><name><surname>Tseng</surname><given-names>WF</given-names></name><name><surname>Chao</surname><given-names>CH</given-names></name><etal/></person-group><article-title>Developmental gene regulatory networks in the zebrafish embryo</article-title><source>Biochim Biophys Acta</source><year>1789</year><volume>2009</volume><fpage>279</fpage><lpage>98</lpage></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jaeger</surname><given-names>J</given-names></name><name><surname>Surkova</surname><given-names>S</given-names></name><name><surname>Blagov</surname><given-names>M</given-names></name><name><surname>Janssens</surname><given-names>H</given-names></name><name><surname>Kosman</surname><given-names>D</given-names></name><name><surname>Kozlov</surname><given-names>KN</given-names></name><etal/></person-group><article-title>Dynamic control of positional information in the early <italic>Drosophila</italic> embryo</article-title><source>Nature</source><year>2004</year><volume>430</volume><fpage>368</fpage><lpage>71</lpage><pub-id pub-id-type="doi">10.1038/nature02678</pub-id><?supplied-pmid 15254541?><pub-id pub-id-type="pmid">15254541</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sheth</surname><given-names>R</given-names></name><name><surname>Marcon</surname><given-names>L</given-names></name><name><surname>Bastida</surname><given-names>MF</given-names></name><name><surname>Junco</surname><given-names>M</given-names></name><name><surname>Quintana</surname><given-names>L</given-names></name><name><surname>Dahn</surname><given-names>R</given-names></name><etal/></person-group><article-title><italic>Hox</italic> genes regulate digit patterning by controlling the wavelength of a Turing-type mechanism</article-title><source>Science</source><year>2012</year><volume>338</volume><fpage>1476</fpage><lpage>80</lpage><pub-id pub-id-type="doi">10.1126/science.1226804</pub-id><?supplied-pmid 23239739?><pub-id pub-id-type="pmid">23239739</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Botman</surname><given-names>D</given-names></name><name><surname>Kaandorp</surname><given-names>JA</given-names></name></person-group><article-title>Spatial gene expression quantification: a tool for analysis of in situ hybridizations in sea anemone <italic>Nematostella vectensis</italic></article-title><source>BMC Res Notes</source><year>2012</year><volume>5</volume><fpage>555</fpage><pub-id pub-id-type="doi">10.1186/1756-0500-5-555</pub-id><?supplied-pmid 23039089?><pub-id pub-id-type="pmid">23039089</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Botman</surname><given-names>D</given-names></name><name><surname>R&#x000f6;ttinger</surname><given-names>E</given-names></name><name><surname>Martindale</surname><given-names>MQ</given-names></name><name><surname>de Jong</surname><given-names>J</given-names></name><name><surname>Kaandorp</surname><given-names>JA</given-names></name></person-group><article-title>A computational approach towards a gene regulatory network for the developing <italic>Nematostella vectensis</italic> gut</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><fpage>e103341</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0103341</pub-id><?supplied-pmid 25076223?><pub-id pub-id-type="pmid">25076223</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Song</surname><given-names>L</given-names></name><name><surname>Langfelder</surname><given-names>P</given-names></name><name><surname>Horvath</surname><given-names>S</given-names></name></person-group><article-title>Comparison of co-expression measures: mutual information, correlation, and model based indices</article-title><source>BMC Bioinformatics</source><year>2012</year><volume>13</volume><fpage>328</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-13-328</pub-id><?supplied-pmid 23217028?><pub-id pub-id-type="pmid">23217028</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Myasnikova</surname><given-names>E</given-names></name><name><surname>Samsonova</surname><given-names>A</given-names></name><name><surname>Kozlov</surname><given-names>K</given-names></name><name><surname>Samsonova</surname><given-names>M</given-names></name><name><surname>Reinitz</surname><given-names>J</given-names></name></person-group><article-title>Registration of the expression patterns of <italic>Drosophila</italic> segmentation genes by two independent methods</article-title><source>Bioinformatics</source><year>2001</year><volume>17</volume><fpage>3</fpage><lpage>12</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/17.1.3</pub-id><?supplied-pmid 11222257?><pub-id pub-id-type="pmid">11222257</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ormestad</surname><given-names>M</given-names></name><name><surname>Martindale</surname><given-names>MQ</given-names></name><name><surname>R&#x000f6;ttinger</surname><given-names>E</given-names></name></person-group><article-title>A comparative gene expression database for invertebrates</article-title><source>Evodevo</source><year>2011</year><volume>2</volume><fpage>17</fpage><pub-id pub-id-type="doi">10.1186/2041-9139-2-17</pub-id><?supplied-pmid 21861937?><pub-id pub-id-type="pmid">21861937</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Martindale</surname><given-names>MQ</given-names></name><name><surname>Hejnol</surname><given-names>A</given-names></name></person-group><article-title>A developmental perspective: changes in the position of the blastopore during bilaterian evolution</article-title><source>Dev Cell</source><year>2009</year><volume>17</volume><fpage>162</fpage><lpage>74</lpage><pub-id pub-id-type="doi">10.1016/j.devcel.2009.07.024</pub-id><?supplied-pmid 19686678?><pub-id pub-id-type="pmid">19686678</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lee</surname><given-names>PN</given-names></name><name><surname>Kumburegama</surname><given-names>S</given-names></name><name><surname>Marlow</surname><given-names>HQ</given-names></name><name><surname>Martindale</surname><given-names>MQ</given-names></name><name><surname>Wikramanayake</surname><given-names>AH</given-names></name></person-group><article-title>Asymmetric developmental potential along the animal-vegetal axis in the anthozoan cnidarian, <italic>Nematostella vectensis</italic>, is mediated by Dishevelled</article-title><source>Dev Biol</source><year>2007</year><volume>310</volume><fpage>169</fpage><lpage>86</lpage><pub-id pub-id-type="doi">10.1016/j.ydbio.2007.05.040</pub-id><?supplied-pmid 17716645?><pub-id pub-id-type="pmid">17716645</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Finnerty</surname><given-names>JR</given-names></name><name><surname>Pang</surname><given-names>K</given-names></name><name><surname>Burton</surname><given-names>P</given-names></name><name><surname>Paulson</surname><given-names>D</given-names></name><name><surname>Martindale</surname><given-names>MQ</given-names></name></person-group><article-title>Origins of bilateral symmetry: <italic>Hox</italic> and <italic>dpp</italic> expression in a sea anemone</article-title><source>Science</source><year>2004</year><volume>304</volume><fpage>1335</fpage><lpage>7</lpage><pub-id pub-id-type="doi">10.1126/science.1091946</pub-id><?supplied-pmid 15131263?><pub-id pub-id-type="pmid">15131263</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rentzsch</surname><given-names>F</given-names></name><name><surname>Anton</surname><given-names>R</given-names></name><name><surname>Saina</surname><given-names>M</given-names></name><name><surname>Hammerschmidt</surname><given-names>M</given-names></name><name><surname>Holstein</surname><given-names>TW</given-names></name><name><surname>Technau</surname><given-names>U</given-names></name></person-group><article-title>Asymmetric expression of the BMP antagonists <italic>chordin</italic> and <italic>gremlin</italic> in the sea anemone <italic>Nematostella vectensis</italic>: implications for the evolution of axial patterning</article-title><source>Dev Biol</source><year>2006</year><volume>296</volume><fpage>375</fpage><lpage>87</lpage><pub-id pub-id-type="doi">10.1016/j.ydbio.2006.06.003</pub-id><?supplied-pmid 16828077?><pub-id pub-id-type="pmid">16828077</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dittmar</surname><given-names>WJ</given-names></name><name><surname>McIver</surname><given-names>L</given-names></name><name><surname>Michalak</surname><given-names>P</given-names></name><name><surname>Garner</surname><given-names>HR</given-names></name><name><surname>Valdez</surname><given-names>G</given-names></name></person-group><article-title>EvoCor: a platform for predicting functionally related genes using phylogenetic and expression profiles</article-title><source>Nucleic Acids Res</source><year>2014</year><volume>42</volume><fpage>W72</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1093/nar/gku442</pub-id><?supplied-pmid 24848012?><pub-id pub-id-type="pmid">24848012</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>R&#x000f6;ttinger</surname><given-names>E</given-names></name><name><surname>Dahlin</surname><given-names>P</given-names></name><name><surname>Martindale</surname><given-names>MQ</given-names></name></person-group><article-title>A framework for the establishment of a cnidarian gene regulatory network for &#x0201c;endomesoderm&#x0201d; specification: the inputs of &#x000df;-catenin/TCF signaling</article-title><source>PLoS Genet</source><year>2012</year><volume>8</volume><fpage>e1003121</fpage><pub-id pub-id-type="doi">10.1371/journal.pgen.1003164</pub-id><?supplied-pmid 23271979?><pub-id pub-id-type="pmid">23271979</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>E</given-names></name><name><surname>Cui</surname><given-names>M</given-names></name><name><surname>Peter</surname><given-names>IS</given-names></name><name><surname>Davidson</surname><given-names>EH</given-names></name></person-group><article-title>Encoding regulatory state boundaries in the pregastrular oral ectoderm of the sea urchin embryo</article-title><source>Proc Natl Acad Sci U S A</source><year>2014</year><volume>111</volume><fpage>E906</fpage><lpage>13</lpage><pub-id pub-id-type="doi">10.1073/pnas.1323105111</pub-id><?supplied-pmid 24556994?><pub-id pub-id-type="pmid">24556994</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Terada</surname><given-names>K</given-names></name><name><surname>Kitayama</surname><given-names>A</given-names></name><name><surname>Kanamoto</surname><given-names>T</given-names></name><name><surname>Ueno</surname><given-names>N</given-names></name><name><surname>Furukawa</surname><given-names>T</given-names></name></person-group><article-title>Nucleosome regulator Xhmgb3 is required for cell proliferation of the eye and brain as a downstream target of <italic>Xenopus rax/Rx1</italic></article-title><source>Dev Biol</source><year>2006</year><volume>291</volume><fpage>398</fpage><lpage>412</lpage><pub-id pub-id-type="doi">10.1016/j.ydbio.2005.12.029</pub-id><?supplied-pmid 16445903?><pub-id pub-id-type="pmid">16445903</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fritzenwanker</surname><given-names>JH</given-names></name><name><surname>Genikhovich</surname><given-names>G</given-names></name><name><surname>Kraus</surname><given-names>Y</given-names></name><name><surname>Technau</surname><given-names>U</given-names></name></person-group><article-title>Early development and axis specification in the sea anemone <italic>Nematostella vectensis</italic></article-title><source>Dev Biol</source><year>2007</year><volume>310</volume><fpage>264</fpage><lpage>79</lpage><pub-id pub-id-type="doi">10.1016/j.ydbio.2007.07.029</pub-id><?supplied-pmid 17716644?><pub-id pub-id-type="pmid">17716644</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Manuel</surname><given-names>M</given-names></name></person-group><article-title>Early evolution of symmetry and polarity in metazoan body plans</article-title><source>C R Biol</source><year>2009</year><volume>332</volume><fpage>184</fpage><lpage>209</lpage><pub-id pub-id-type="doi">10.1016/j.crvi.2008.07.009</pub-id><?supplied-pmid 19281951?><pub-id pub-id-type="pmid">19281951</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kozmikova</surname><given-names>I</given-names></name><name><surname>Candiani</surname><given-names>S</given-names></name><name><surname>Fabian</surname><given-names>P</given-names></name><name><surname>Gurska</surname><given-names>D</given-names></name><name><surname>Kozmik</surname><given-names>Z</given-names></name></person-group><article-title>Essential role of Bmp signaling and its positive feedback loop in the early cell fate evolution of chordates</article-title><source>Dev Biol</source><year>2013</year><volume>382</volume><fpage>538</fpage><lpage>54</lpage><pub-id pub-id-type="doi">10.1016/j.ydbio.2013.07.021</pub-id><?supplied-pmid 23933491?><pub-id pub-id-type="pmid">23933491</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Magie</surname><given-names>CR</given-names></name><name><surname>Daly</surname><given-names>M</given-names></name><name><surname>Martindale</surname><given-names>MQ</given-names></name></person-group><article-title>Gastrulation in the cnidarian <italic>Nematostella vectensis</italic> occurs via invagination not ingression</article-title><source>Dev Biol</source><year>2007</year><volume>305</volume><fpage>483</fpage><lpage>97</lpage><pub-id pub-id-type="doi">10.1016/j.ydbio.2007.02.044</pub-id><?supplied-pmid 17397821?><pub-id pub-id-type="pmid">17397821</pub-id></element-citation></ref></ref-list></back></article>