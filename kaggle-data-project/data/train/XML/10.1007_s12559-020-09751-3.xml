<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN" "JATS-archivearticle1-3-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ali="http://www.niso.org/schemas/ali/1.0/" article-type="research-article" dtd-version="1.3"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><?covid-19-tdm?><processing-meta base-tagset="archiving" mathml-version="3.0" table-model="xhtml" tagset-family="jats"><restricted-by>pmc</restricted-by></processing-meta><front><journal-meta><journal-id journal-id-type="nlm-ta">Cognit Comput</journal-id><journal-id journal-id-type="iso-abbrev">Cognit Comput</journal-id><journal-title-group><journal-title>Cognitive Computation</journal-title></journal-title-group><issn pub-type="ppub">1866-9956</issn><issn pub-type="epub">1866-9964</issn><publisher><publisher-name>Springer US</publisher-name><publisher-loc>New York</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7429098</article-id><article-id pub-id-type="pmid">32837591</article-id><article-id pub-id-type="publisher-id">9751</article-id><article-id pub-id-type="doi">10.1007/s12559-020-09751-3</article-id><article-categories><subj-group subj-group-type="heading"><subject>Article</subject></subj-group></article-categories><title-group><article-title>Social Group Optimization&#x02013;Assisted Kapur&#x02019;s Entropy and Morphological Segmentation for Automated Detection of COVID-19 Infection from Computed Tomography Images</article-title></title-group><contrib-group><contrib contrib-type="author" corresp="yes"><name><surname>Dey</surname><given-names>Nilanjan</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Rajinikanth</surname><given-names>V.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Fong</surname><given-names>Simon James</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff3">3</xref><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Kaiser</surname><given-names>M. Shamim</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-2037-8348</contrib-id><name><surname>Mahmud</surname><given-names>Mufti</given-names></name><address><email><EMAIL></email><email><EMAIL></email></address><xref ref-type="aff" rid="Aff6">6</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="GRID">grid.440742.1</institution-id><institution-id institution-id-type="ISNI">0000 0004 1799 6713</institution-id><institution>Department of Information Technology, </institution><institution>Techno India College of Technology, </institution></institution-wrap>Kolkata, 700156 West Bengal India </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="GRID">grid.252262.3</institution-id><institution-id institution-id-type="ISNI">0000 0001 0613 6919</institution-id><institution>Department of Electronics and Instrumentation Engineering, </institution><institution>St. Joseph&#x02019;s College of Engineering, </institution></institution-wrap>Chennai, 600119 India </aff><aff id="Aff3"><label>3</label>Department of Computer and Information Science, University of Macau, Taipa, China </aff><aff id="Aff4"><label>4</label>DACC Laboratory, Zhuhai Institutes of Advanced Technology of the Chinese Academy of Sciences, Zhuhai, China </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="GRID">grid.411808.4</institution-id><institution-id institution-id-type="ISNI">0000 0001 0664 5967</institution-id><institution>Institute of Information Technology, </institution><institution>Jahangirnagar University, </institution></institution-wrap>Savar, 1342 Dhaka Bangladesh </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="GRID">grid.12361.37</institution-id><institution-id institution-id-type="ISNI">0000 0001 0727 0669</institution-id><institution>Department of Computing &#x00026; Technology, </institution><institution>Nottingham Trent University, </institution></institution-wrap>Clifton Lane, Nottingham NG11 8NS UK </aff></contrib-group><pub-date pub-type="epub"><day>15</day><month>8</month><year>2020</year></pub-date><pub-date pub-type="pmc-release"><day>15</day><month>8</month><year>2020</year></pub-date><pub-date pub-type="ppub"><year>2020</year></pub-date><volume>12</volume><issue>5</issue><fpage>1011</fpage><lpage>1023</lpage><history><date date-type="received"><day>1</day><month>5</month><year>2020</year></date><date date-type="accepted"><day>29</day><month>6</month><year>2020</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2020</copyright-statement><license><ali:license_ref specific-use="textmining" content-type="ccbylicense">https://creativecommons.org/licenses/by/4.0/</ali:license_ref><license-p><bold>Open Access</bold>This article is licensed under a Creative Commons Attribution 4.0 International License, which permits use, sharing, adaptation, distribution and reproduction in any medium or format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons licence, and indicate if changes were made. The images or other third party material in this article are included in the article's Creative Commons licence, unless indicated otherwise in a credit line to the material. If material is not included in the article's Creative Commons licence and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the copyright holder. To view a copy of this licence, visit <ext-link ext-link-type="uri" xlink:href="https://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>.</license-p></license></permissions><abstract id="Abs1"><p id="Par1">The coronavirus disease (COVID-19) caused by a novel coronavirus, SARS-CoV-2, has been declared a global pandemic. Due to its infection rate and severity, it has emerged as one of the major global threats of the current generation. To support the current combat against the disease, this research aims to propose a machine learning&#x02013;based pipeline to detect COVID-19 infection using lung computed tomography scan images (CTI). This implemented pipeline consists of a number of sub-procedures ranging from segmenting the COVID-19 infection to classifying the segmented regions. The initial part of the pipeline implements the segmentation of the COVID-19&#x02013;affected CTI using social group optimization&#x02013;based Kapur&#x02019;s entropy thresholding, followed by k-means clustering and morphology-based segmentation. The next part of the pipeline implements feature extraction, selection, and fusion to classify the infection. Principle component analysis&#x02013;based serial fusion technique is used in fusing the features and the fused feature vector is then employed to train, test, and validate four different classifiers namely Random Forest, K-Nearest Neighbors (KNN), Support Vector Machine with Radial Basis Function, and Decision Tree. Experimental results using benchmark datasets show a high accuracy (&#x0003e; 91%) for the morphology-based segmentation task; for the classification task, the KNN offers the highest accuracy among the compared classifiers (&#x0003e; 87%). However, this should be noted that this method still awaits clinical validation, and therefore should not be used to clinically diagnose ongoing COVID-19 infection.</p></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>COVID-19 infection</kwd><kwd>CT scan image</kwd><kwd>Fused feature vector</kwd><kwd>KNN classifier</kwd><kwd>Segmentation and detection accuracy</kwd></kwd-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; Springer Science+Business Media, LLC, part of Springer Nature 2020</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Introduction</title><p id="Par2">Lung infection caused by coronavirus disease (COVID-19) has emerged as one of the major diseases and has affected over 8.2 million of the population globally<xref ref-type="fn" rid="Fn1">1</xref>, irrespective of their race, gender, and age. The infection and the morbidity rates caused by this novel coronavirus are increasing rapidly [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>]. Due to its severity and progression rate, the recent report of the World Health Organization (WHO) declared it as pandemic [<xref ref-type="bibr" rid="CR3">3</xref>]. Even though an extensive number of precautionary schemes have been implemented, the occurrence rate of COVID-19 infection is rising rapidly due to various circumstances.</p><p id="Par4">The origin of COVID-19 is due to a virus called severe acute respiratory syndrome-coronavirus-2 (SARS-CoV-2) and this syndrome initially started in Wuhan, China, in December 2019 [<xref ref-type="bibr" rid="CR4">4</xref>]. The outbreak of COVID-19 has appeared as a worldwide problem and a considerable amount of research works are already in progress to determine solutions to manage the disease infection rate and spread. Furthermore, the recently proposed research works on (i) COVID-19 infection detection [<xref ref-type="bibr" rid="CR5">5</xref>&#x02013;<xref ref-type="bibr" rid="CR8">8</xref>], (ii) handling of the infection [<xref ref-type="bibr" rid="CR9">9</xref>, <xref ref-type="bibr" rid="CR10">10</xref>], and (iii) COVID-19 progression and prediction [<xref ref-type="bibr" rid="CR11">11</xref>&#x02013;<xref ref-type="bibr" rid="CR13">13</xref>] have helped get more information regarding the disease.</p><p id="Par5">The former research and the medical findings discovered that COVID-19 initiates disease in the human respiratory tract and builds severe acute pneumonia. The existing research also confirmed that the premature indications of COVID-19 are subclinical and it necessitates a committed medical practice to notice and authenticate the illness. The frequent medical-grade analysis engages in a collection of samples from infected persons and sample supported examination and confirmation of COVID-19 using reverse transcription-polymerase chain reaction (RT-PCR) test and image-guided assessment employing lung computed tomography scan images (CTI), and the chest X-ray [<xref ref-type="bibr" rid="CR14">14</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref>]. When the patient is admitted with COVID-19 infection, the doctor will initiate the treatment process to cure the patient using the prearranged treatment practice which will decrease the impact of pneumonia.</p><p id="Par6">Usually, experts recommend a chain of investigative tests to identify the cause, position, and harshness of pneumonia. The preliminary examinations, such as blood tests and pleural-fluid assessment, are performed clinically to detect the severity of the infection [<xref ref-type="bibr" rid="CR18">18</xref>&#x02013;<xref ref-type="bibr" rid="CR20">20</xref>]. The image-assisted methods are also frequently implemented to sketch the disease in the lung, which can be additionally examined by an expert physician or a computerized arrangement to recognize the severity of the pneumonia. Compared with chest X-ray, CTI is frequently considered due to its advantage and the 3-D view. The research work published on COVID-19 also confirmed the benefit of CT in detecting the disease in the respiratory tract and pneumonia [<xref ref-type="bibr" rid="CR21">21</xref>&#x02013;<xref ref-type="bibr" rid="CR23">23</xref>].</p><p id="Par7">Recently, more COVID-19 detection methods have been proposed for the progression stage identification of COVID-19 using the RT-PCR and imaging methods. Most of these existing works combined RT-PCR with the imaging procedure to confirm and treat the disease. The recent work of Rajinikanth et al. [<xref ref-type="bibr" rid="CR8">8</xref>] developed a computer-supported method to assess the COVID-19 lesion using lung CTI. This work implemented few operator-assisted steps to achieve superior outcomes during the COVID-19 evaluation.</p><p id="Par8">ML approaches are well-known for their capabilities in recognizing patterns in data. In recent years, ML has been applied to a variety of tasks including biological data mining [<xref ref-type="bibr" rid="CR24">24</xref>, <xref ref-type="bibr" rid="CR25">25</xref>], medical image analysis [<xref ref-type="bibr" rid="CR26">26</xref>], financial forecasting [<xref ref-type="bibr" rid="CR27">27</xref>], trust management [<xref ref-type="bibr" rid="CR28">28</xref>], anomaly detection [<xref ref-type="bibr" rid="CR29">29</xref>, <xref ref-type="bibr" rid="CR30">30</xref>], disease detection [<xref ref-type="bibr" rid="CR31">31</xref>, <xref ref-type="bibr" rid="CR32">32</xref>], natural language processing [<xref ref-type="bibr" rid="CR33">33</xref>], and strategic game playing [<xref ref-type="bibr" rid="CR34">34</xref>].</p><p id="Par9">The presented work aims to:
<list list-type="bullet"><list-item><p id="Par10">Propose a ML-driven pipeline to extract and detect the COVID-19 infection from lung CTI with an improved accuracy.</p></list-item><list-item><p id="Par11">Develop a procedural sequence for an automated extraction of the COVID-19 infection from a benchmark lung CTI dataset.</p></list-item><list-item><p id="Par12">Put forward an appropriate sequence of techniques, tri-level thresholding using social group optimization (SGO)-based Kapur&#x02019;s entropy (KE) or SGO-KE, K-Means Clustering (KMC)-based separation, morphology-based segmentation to accurately extract COVID-19 infection from lung CTI.</p></list-item></list></p><p id="Par13">A comparison of the extracted COVID-19 infection information from the CTI using the proposed pipeline with the ground truth (GT) images confirms the segmentation accuracy of the proposed method. The proposed pipeline achieves mean segmentation and classification accuracy of more than 91% and 87% respectively using 78 images from a benchmark dataset.</p><p id="Par14">This research is arranged as follows; Section &#x0201c;<xref rid="Sec2" ref-type="sec">Motivation</xref>&#x0201d; presents the motivation, Section &#x0201c;<xref rid="Sec3" ref-type="sec">Methodology</xref>&#x0201d; represents the methodological details of the proposed scheme. Section &#x0201c;<xref rid="Sec8" ref-type="sec">Results and Discussion</xref>&#x0201d; outlines the attained results and discussions. Section &#x0201c;<xref rid="Sec9" ref-type="sec">Conclusion</xref>&#x0201d; depicts the conclusion of the present research work.</p></sec><sec id="Sec2"><title>Motivation</title><p id="Par15">The proposed research work is motivated by the former image examination works existing in literature [<xref ref-type="bibr" rid="CR35">35</xref>&#x02013;<xref ref-type="bibr" rid="CR38">38</xref>]. During the mass disease screening operation, the existing medical data amount will gradually increase and reduce the data burden; it is essential to employ an image segregation system to categorize the existing medical data into two or multi-class, and to assign the priority during the treatment implementation. The recent works in the literature confirm that the feature-fusion&#x02013;based methods will improve the classification accuracy without employing the complex methodologies [<xref ref-type="bibr" rid="CR39">39</xref>&#x02013;<xref ref-type="bibr" rid="CR41">41</xref>]. Classification task implemented using the features of the original image and the region-of-interest (ROI) offered superior result on some image classification problems and this procedure is recommended when the similarity between the normal and the disease class images is more [<xref ref-type="bibr" rid="CR24">24</xref>, <xref ref-type="bibr" rid="CR26">26</xref>, <xref ref-type="bibr" rid="CR31">31</xref>, <xref ref-type="bibr" rid="CR42">42</xref>, <xref ref-type="bibr" rid="CR43">43</xref>]. Hence, for the identical images, it is necessary to employ a segmentation technique to extract the ROI from the disease class image with better accuracy [<xref ref-type="bibr" rid="CR26">26</xref>]. Finally, the fused features of the actual image and the ROI are fused to attain enhanced classification accuracy.</p></sec><sec id="Sec3"><title>Methodology</title><p id="Par16">This section of the work presents the methodological details of the proposed scheme. Like the former approaches, this work also implemented two different phases to improve the detection accuracy.</p><sec id="Sec4"><title>Proposed Pipeline</title><p id="Par17">This work consists of the following two stages as depicted in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>. These are:
<list list-type="bullet"><list-item><p id="Par18">Implementation of an image segmentation method to extract the COVID-19 infection,</p></list-item><list-item><p id="Par19">Execution of a ML scheme to classify the considered lung CTI database into normal/COVID-19 class.</p></list-item></list><fig id="Fig1"><label>Fig. 1</label><caption><p>The number of image processing stages implemented in the proposed work</p></caption><graphic xlink:href="12559_2020_9751_Fig1_HTML" id="MO1"/></fig></p><p id="Par20">The details of these two stages are given below:</p><sec id="FPar1"><title>Stage 1:</title><p id="Par21">Figure&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> depicts the image processing system proposed to extract the pneumonia infection in the lung due to COVID-19. Initially, the required 2D slices of the lung CTI are collected from an open-source database [<xref ref-type="bibr" rid="CR44">44</xref>]. All the collected images are resized into 256 &#x000d7; 256 &#x000d7; 1 pixels and the normalized images are then considered for evaluation. In this work, SGO-KE&#x02013;based tri-level threshold is initially applied to enhance the lung section (see &#x0201c;<xref ref-type="sec" rid="FPar4">Social Group Optimization and Kapur&#x02019;s Function</xref>&#x0201d; for details). Then, KMC is employed to segregate the thresholded image into background, artifact, and the lung segment. The unwanted lung sections are then removed using a morphological segmentation procedure and the extracted binary image of the lung is then compared with its related GT provided in the database. Finally, the essential performance measures are computed and based on which the performance of the proposed COVID-19 system is validated.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Image segmentation framework to extract COVID-19 infection from 2D lung CT scan image</p></caption><graphic xlink:href="12559_2020_9751_Fig2_HTML" id="MO2"/></fig></p></sec><sec id="FPar2"><title>Stage 2:</title><p id="Par22">Figure&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> presents the proposed ML scheme to separate the considered lung CTI into normal/COVID-19 class. This system is constructed using two different images, such as (i) the original test image (normal/COVID-19 class) and (ii) the binary form of the COVID-19 section. The various procedures existing in the proposed ML scheme are depicted in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>.
<fig id="Fig3"><label>Fig. 3</label><caption><p>Proposed ML scheme to detect COVID-19 infection</p></caption><graphic xlink:href="12559_2020_9751_Fig3_HTML" id="MO3"/></fig></p></sec><sec id="Sec5"><title>Segmentation of COVID-19 Infection</title><p id="Par23">This procedure is implemented only for the CTI associated with the COVID-19 pneumonia infection. The complete details on various stages involved in this process are depicted in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>. The series of procedures implemented in this figure are used to extract the COVID-19 infection from the chosen test image with better accuracy. The pseudo-code of the implemented procedure is depicted in Algorithm 1.
<fig id="Figa"><graphic xlink:href="12559_2020_9751_Figa_HTML" id="MO15"/></fig></p><sec id="FPar3"><title>Image Thresholding</title><p id="Par24">Initially, the enhancement of the infected pneumonia section is achieved by implementing a tri-level threshold based on SGO and the KE. In this operation, the role of the SGO is to randomly adjust the threshold value of the chosen image until KE is maximized. The threshold which offered the maximized KE is considered as the finest threshold. The related information on the SGO-KE implemented in this work can be found in [<xref ref-type="bibr" rid="CR45">45</xref>]. The SGO parameters discussed in Dey et al. [<xref ref-type="bibr" rid="CR46">46</xref>] are considered in the proposed work to threshold the considered CTI.</p></sec><sec id="FPar4"><title>Social Group Optimization and Kapur&#x02019;s Function</title><p id="Par25">SGO is a heuristic technique proposed by Satapathy and Naik [<xref ref-type="bibr" rid="CR47">47</xref>] by mimicking the knowledge sharing concepts in humans. This algorithm employs two phases, such as (i) enhancing phase to coordinate the arrangement of people (agents) in a group, and the (ii) knowledge gaining phase: which allows the agents to notice the finest solution based on the task. In this paper, an agent is considered a social population who is generated based on the features/parameters.</p><p id="Par26">The mathematical description of the SGO is defined as: let <italic>X</italic><sub><italic>I</italic></sub> denote the original knowledge of agents of a group with dimension <italic>I</italic> = 1, 2, ... , <italic>N</italic>. If the number of variables to be optimized is represented as <italic>D</italic>, then the initial knowledge can be expressed as <italic>X</italic><sub><italic>I</italic></sub> = (<italic>x</italic><sub><italic>I</italic>1</sub>, <italic>x</italic><sub><italic>I</italic>2</sub>,... <italic>x</italic><sub><italic>I</italic><italic>D</italic></sub>). For a chosen problem, the objective function can be defined as <italic>F</italic><sub><italic>J</italic></sub>, with <italic>J</italic> = 1, 2, ... , <italic>N</italic>.</p><p id="Par27">The updated function in SGO is;
<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ X_{new_{I,J}}=X_{old_{I,J}} \zeta + R (g_{best_{J}}-X_{old_{I,J}} ) $$\end{document}</tex-math><mml:math id="M2"><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mi>n</mml:mi><mml:mi>e</mml:mi><mml:msub><mml:mrow><mml:mi>w</mml:mi></mml:mrow><mml:mrow><mml:mi>I</mml:mi><mml:mo>,</mml:mo><mml:mi>J</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mi>o</mml:mi><mml:mi>l</mml:mi><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mrow><mml:mi>I</mml:mi><mml:mo>,</mml:mo><mml:mi>J</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mi>&#x003b6;</mml:mi><mml:mo>+</mml:mo><mml:mi>R</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>b</mml:mi><mml:mi>e</mml:mi><mml:mi>s</mml:mi><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mi>J</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mi>o</mml:mi><mml:mi>l</mml:mi><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mrow><mml:mi>I</mml:mi><mml:mo>,</mml:mo><mml:mi>J</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula>where <inline-formula id="IEq1"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$X_{new_{i,j}}$\end{document}</tex-math><mml:math id="M4"><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mi>n</mml:mi><mml:mi>e</mml:mi><mml:msub><mml:mrow><mml:mi>w</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12559_2020_9751_Article_IEq1.gif"/></alternatives></inline-formula> is the original knowledge, <inline-formula id="IEq2"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$X_{old_{i,j}}$\end{document}</tex-math><mml:math id="M6"><mml:msub><mml:mrow><mml:mi>X</mml:mi></mml:mrow><mml:mrow><mml:mi>o</mml:mi><mml:mi>l</mml:mi><mml:msub><mml:mrow><mml:mi>d</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>,</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12559_2020_9751_Article_IEq2.gif"/></alternatives></inline-formula> is the updated knowledge, <italic>&#x003b6;</italic> denotes self-introspection parameter (assigned as 0.2), <italic>R</italic> is the random number [0,1], and <inline-formula id="IEq3"><alternatives><tex-math id="M7">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$g_{best_{j}}$\end{document}</tex-math><mml:math id="M8"><mml:msub><mml:mrow><mml:mi>g</mml:mi></mml:mrow><mml:mrow><mml:mi>b</mml:mi><mml:mi>e</mml:mi><mml:mi>s</mml:mi><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow></mml:msub></mml:math><inline-graphic xlink:href="12559_2020_9751_Article_IEq3.gif"/></alternatives></inline-formula> is the global best knowledge.</p><p id="Par28">In this work, the SGO is employed to find the optimal threshold by maximizing the KE value and this operation is defined below:</p><p id="Par29">Entropy in an image is the measure of its irregularity and for a considered image, Kapur&#x02019;s thresholding can be used to identify the optimal threshold by maximizing its entropy value.</p><p id="Par30">Let <italic>T</italic><italic>h</italic> = [<italic>t</italic><sub>1</sub>, <italic>t</italic><sub>2</sub>, ... , <italic>t</italic><sub><italic>n</italic>&#x02212;&#x02009;1</sub>] denote the threshold vector of the chosen image of a fixed dimension and assume this image has <italic>L</italic> gray levels (0 to <italic>L</italic> &#x02212;&#x02009;1) with a total pixel value of <italic>Z</italic>. If<italic>f</italic>() represents the frequency of <italic>j</italic>-th intensity level, then the pixel distribution of the image will be:
<disp-formula id="Equ2"><label>2</label><alternatives><tex-math id="M9">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ Z=f(0)+f(1)+...+f(L-1). $$\end{document}</tex-math><mml:math id="M10"><mml:mi>Z</mml:mi><mml:mo>=</mml:mo><mml:mi>f</mml:mi><mml:mo>(</mml:mo><mml:mn>0</mml:mn><mml:mo>)</mml:mo><mml:mo>+</mml:mo><mml:mi>f</mml:mi><mml:mo>(</mml:mo><mml:mn>1</mml:mn><mml:mo>)</mml:mo><mml:mo>+</mml:mo><mml:mn>...</mml:mn><mml:mo>+</mml:mo><mml:mi>f</mml:mi><mml:mo>(</mml:mo><mml:mi>L</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn><mml:mo>)</mml:mo><mml:mn>.</mml:mn></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ2.gif" position="anchor"/></alternatives></disp-formula>If the probability of <italic>j</italic>-th intensity level is given by:
<disp-formula id="Equ3"><label>3</label><alternatives><tex-math id="M11">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ P_{j}=f(j)/Z. $$\end{document}</tex-math><mml:math id="M12"><mml:msub><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:mi>f</mml:mi><mml:mo>(</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo><mml:mo>/</mml:mo><mml:mi>Z</mml:mi><mml:mn>.</mml:mn></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ3.gif" position="anchor"/></alternatives></disp-formula>Then, during the threshold selection, the pixels of image are separated into <italic>T</italic><italic>h</italic> +&#x02009;1 groups according to the assigned threshold value. After disconnection of the images as per the selected threshold, the entropy of each cluster is separately computed and combined to get the final entropy as follows:</p><p id="Par31">The KE to be maximized is given by Eq.&#x000a0;<xref rid="Equ14" ref-type="">14</xref>:
<disp-formula id="Equ4"><label>4</label><alternatives><tex-math id="M13">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ KE_{max}=F_{KE}(Th)=\sum\limits_{i=1}^{n}{G_{i}^{C}}. $$\end{document}</tex-math><mml:math id="M14"><mml:mi>K</mml:mi><mml:msub><mml:mrow><mml:mi>E</mml:mi></mml:mrow><mml:mrow><mml:mi>m</mml:mi><mml:mi>a</mml:mi><mml:mi>x</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>F</mml:mi></mml:mrow><mml:mrow><mml:mi>K</mml:mi><mml:mi>E</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>T</mml:mi><mml:mi>h</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:munderover accent="false" accentunder="false"><mml:mrow><mml:mi>&#x02211;</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>n</mml:mi></mml:mrow></mml:munderover><mml:msubsup><mml:mrow><mml:mi>G</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup><mml:mn>.</mml:mn></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ4.gif" position="anchor"/></alternatives></disp-formula>For a tri-level thresholding problem, the expression will be given by Eq.&#x000a0;<xref rid="Equ5" ref-type="">5</xref>:
<disp-formula id="Equ5"><label>5</label><alternatives><tex-math id="M15">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ f(t_{1},t_{2},t_{3})=\sum\limits_{i=1}^{3}{G_{i}^{C}}. $$\end{document}</tex-math><mml:math id="M16"><mml:mi>f</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mn>1</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msub><mml:mo>,</mml:mo><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mn>3</mml:mn></mml:mrow></mml:msub><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:munderover accent="false" accentunder="false"><mml:mrow><mml:mi>&#x02211;</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mn>3</mml:mn></mml:mrow></mml:munderover><mml:msubsup><mml:mrow><mml:mi>G</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup><mml:mn>.</mml:mn></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ5.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par32">where <italic>G</italic><sub><italic>i</italic></sub> is the entropy given by:
<disp-formula id="Equ6"><label>6</label><alternatives><tex-math id="M17">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{@{}rcl@{}} {G_{1}^{C}}&#x00026;=&#x00026;\sum\limits_{j=1}^{t_{1}}\frac{{P_{j}^{C}}}{{w_{0}^{C}}}\ln\left( \frac{{P_{j}^{C}}}{{w_{0}^{C}}}\right), \end{array} $$\end{document}</tex-math><mml:math id="M18"><mml:msubsup><mml:mrow><mml:mi>G</mml:mi></mml:mrow><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup><mml:mo>=</mml:mo><mml:munderover accent="false" accentunder="false"><mml:mrow><mml:mi>&#x02211;</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:munderover><mml:mfrac class="tfrac"><mml:mrow><mml:msubsup><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow><mml:mrow><mml:msubsup><mml:mrow><mml:mi>w</mml:mi></mml:mrow><mml:mrow><mml:mn>0</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow></mml:mfrac><mml:mo>ln</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mfrac class="tfrac"><mml:mrow><mml:msubsup><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow><mml:mrow><mml:msubsup><mml:mrow><mml:mi>w</mml:mi></mml:mrow><mml:mrow><mml:mn>0</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced><mml:mo>,</mml:mo></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ6.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ7"><label>7</label><alternatives><tex-math id="M19">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{@{}rcl@{}} {G_{2}^{C}}&#x00026;=&#x00026;\sum\limits_{j=t_{1}}^{t_{2}}\frac{{P_{j}^{C}}}{{w_{1}^{C}}}\ln\left( \frac{{P_{j}^{C}}}{{w_{1}^{C}}}\right), \end{array} $$\end{document}</tex-math><mml:math id="M20"><mml:msubsup><mml:mrow><mml:mi>G</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup><mml:mo>=</mml:mo><mml:munderover accent="false" accentunder="false"><mml:mrow><mml:mi>&#x02211;</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mn>1</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:munderover><mml:mfrac class="tfrac"><mml:mrow><mml:msubsup><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow><mml:mrow><mml:msubsup><mml:mrow><mml:mi>w</mml:mi></mml:mrow><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow></mml:mfrac><mml:mo>ln</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mfrac class="tfrac"><mml:mrow><mml:msubsup><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow><mml:mrow><mml:msubsup><mml:mrow><mml:mi>w</mml:mi></mml:mrow><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced><mml:mo>,</mml:mo></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ7.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ8"><label>8</label><alternatives><tex-math id="M21">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{@{}rcl@{}} {G_{3}^{C}}&#x00026;=&#x00026;\sum\limits_{j=t_{2}}^{t_{3}}\frac{{P_{j}^{C}}}{{w_{2}^{C}}}\ln\left( \frac{{P_{j}^{C}}}{{w_{2}^{C}}}\right), \end{array} $$\end{document}</tex-math><mml:math id="M22"><mml:msubsup><mml:mrow><mml:mi>G</mml:mi></mml:mrow><mml:mrow><mml:mn>3</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup><mml:mo>=</mml:mo><mml:munderover accent="false" accentunder="false"><mml:mrow><mml:mi>&#x02211;</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:msub><mml:mrow><mml:mi>t</mml:mi></mml:mrow><mml:mrow><mml:mn>3</mml:mn></mml:mrow></mml:msub></mml:mrow></mml:munderover><mml:mfrac class="tfrac"><mml:mrow><mml:msubsup><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow><mml:mrow><mml:msubsup><mml:mrow><mml:mi>w</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow></mml:mfrac><mml:mo>ln</mml:mo><mml:mfenced close=")" open="("><mml:mrow><mml:mfrac class="tfrac"><mml:mrow><mml:msubsup><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow><mml:mrow><mml:msubsup><mml:mrow><mml:mi>w</mml:mi></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced><mml:mo>,</mml:mo></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ8.gif" position="anchor"/></alternatives></disp-formula>where,</p><p id="Par33"><inline-formula id="IEq4"><alternatives><tex-math id="M23">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}${P_{j}^{C}}$\end{document}</tex-math><mml:math id="M24"><mml:msubsup><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:math><inline-graphic xlink:href="12559_2020_9751_Article_IEq4.gif"/></alternatives></inline-formula> is the probability distribution for intensity, <italic>C</italic> is the image class (<italic>C</italic> = 1 for the grayscale image), and <inline-formula id="IEq5"><alternatives><tex-math id="M25">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$w_{i-1}^{C}$\end{document}</tex-math><mml:math id="M26"><mml:msubsup><mml:mrow><mml:mi>w</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msubsup></mml:math><inline-graphic xlink:href="12559_2020_9751_Article_IEq5.gif"/></alternatives></inline-formula> is the probability occurrence.</p><p id="Par34">During the tri-level thresholding, a chosen approach is employed to find the <italic>F</italic><sub><italic>K</italic><italic>E</italic></sub>(<italic>T</italic><italic>h</italic>) by randomly varying the thresholds (<italic>T</italic><italic>h</italic> = {<italic>t</italic><sub>1</sub>, <italic>t</italic><sub>2</sub>, <italic>t</italic><sub>3</sub>} ). In this research, the SGO is employed to adjust the thresholds to find the <italic>F</italic><sub><italic>K</italic><italic>E</italic></sub>(<italic>T</italic><italic>h</italic>).</p></sec><sec id="FPar5"><title>Segmentation Based on KMC and Morphological Process</title><p id="Par35">The COVID-19 infection from the enhanced CTI is then separated using the KMC technique and this approach helps segregate the image into various regions [<xref ref-type="bibr" rid="CR48">48</xref>]. In this work, the enhanced image is separated into three sections, such as the background, normal image section, and the COVID-infection. The essential information on KMC and the morphology-based segmentation can be found in [<xref ref-type="bibr" rid="CR49">49</xref>]. The extracted COVID-19 is associated with the artifacts; hence, morphological enhancement and segmentation discussed in [<xref ref-type="bibr" rid="CR49">49</xref>, <xref ref-type="bibr" rid="CR50">50</xref>] are implemented to extract the pneumonia infection, with better accuracy.</p><p id="Par36">KMC helps split <italic>u</italic>-observations into K-groups. For a given set of observations with dimension &#x0201c;<italic>d</italic>,&#x0201d; KMC will try to split them into <italic>K</italic>-groups; <italic>Q</italic>(<italic>Q</italic><sub>1</sub>, <italic>Q</italic><sub>2</sub>, ... , <italic>Q</italic><sub><italic>K</italic></sub>) for (<italic>K</italic> &#x02264; <italic>u</italic>) to shrink the within-cluster sum of squares as depicted by Eq.&#x000a0;<xref rid="Equ9" ref-type="">9</xref>:
<disp-formula id="Equ9"><label>9</label><alternatives><tex-math id="M27">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \arg \min_{Q}\sum\limits_{i=1}^{K}||O_{i}-\mu_{i}||^{2}=\arg \min_{Q}\sum\limits_{i=1}^{K}|Q_{i}|Var(Q_{i}) $$\end{document}</tex-math><mml:math id="M28"><mml:mi mathvariant="normal">arg</mml:mi><mml:munder><mml:mrow><mml:mi mathvariant="normal">min</mml:mi></mml:mrow><mml:mrow><mml:mi>Q</mml:mi></mml:mrow></mml:munder><mml:munderover accent="false" accentunder="false"><mml:mrow><mml:mi>&#x02211;</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>K</mml:mi></mml:mrow></mml:munderover><mml:mo>|</mml:mo><mml:mo>|</mml:mo><mml:msub><mml:mrow><mml:mi>O</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003bc;</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>|</mml:mo><mml:msup><mml:mrow><mml:mo>|</mml:mo></mml:mrow><mml:mrow><mml:mn>2</mml:mn></mml:mrow></mml:msup><mml:mo>=</mml:mo><mml:mi mathvariant="normal">arg</mml:mi><mml:munder><mml:mrow><mml:mi mathvariant="normal">min</mml:mi></mml:mrow><mml:mrow><mml:mi>Q</mml:mi></mml:mrow></mml:munder><mml:munderover accent="false" accentunder="false"><mml:mrow><mml:mi>&#x02211;</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:mi>K</mml:mi></mml:mrow></mml:munderover><mml:mo>|</mml:mo><mml:msub><mml:mrow><mml:mi>Q</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>|</mml:mo><mml:mi>V</mml:mi><mml:mi>a</mml:mi><mml:mi>r</mml:mi><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>Q</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ9.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par37">where <italic>O</italic> is the number of observations, <italic>Q</italic> is the number of splits, and <italic>&#x003bc;</italic><sub><italic>j</italic></sub> is the mean of points in <italic>Q</italic><sub><italic>i</italic></sub>.</p></sec><sec id="FPar6"><title>Performance Computation</title><p id="Par38">The outcome of the morphological segmentation is in the form of binary and this binary image is then compared against the binary form of the GT and then the essential performance measures, such as accuracy, precision, sensitivity, specificity, and F1-score, are computed. A similar procedure is implemented on all the 78 images existing in the benchmark COVID-19 database and the mean values of these measures are then considered to confirm the segmentation accuracy of the proposed technique. The essential information on these measures is clearly presented in [<xref ref-type="bibr" rid="CR51">51</xref>, <xref ref-type="bibr" rid="CR52">52</xref>].</p></sec></sec><sec id="Sec6"><title>Implementation of Machine Learning Scheme</title><p id="Par39">The ML procedure implemented in this research is briefed in this section. This scheme implements a series of procedures on the original CTI (normal/COVID-19 class) and the segmented binary form of the COVID-19 infection as depicted in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>. The main objective of this ML scheme is to segregate the considered CTI database into normal/COVID-19 class images. The process is shown in algorithm 2.
<graphic position="anchor" xlink:href="12559_2020_9751_Figb_HTML" id="MO4"/></p><sec id="FPar7"><title>Initial Processing</title><p id="Par40">This initial processing of the considered image dataset is individually executed for the test image and the segmented COVID-19 infection. The initial processing involves extracting the image features using a chosen methodology and formation of a one-dimensional FV using the chosen dominant features.</p></sec><sec id="FPar8"><title>Feature Vector 1 (FV1):</title><p id="Par41">The accuracy of disease detection using the ML technique depends mainly on the considered image information. In the literature, a number of image feature extraction procedures are discussed to examine a class of medical images [<xref ref-type="bibr" rid="CR35">35</xref>&#x02013;<xref ref-type="bibr" rid="CR37">37</xref>, <xref ref-type="bibr" rid="CR39">39</xref>&#x02013;<xref ref-type="bibr" rid="CR42">42</xref>]. In this work, the well-known image feature extraction methods, such as Complex-Wavelet-Transform (CWT) and Discrete-Wavelet-Transform (DWT) as well as Empirical-Wavelet-Transform (EWT) are considered in 2-D domain to extract the features of the normal/COVID-19 class grayscale images. The information on the CWT, DWT, and EWT are clearly discussed in the earlier works [<xref ref-type="bibr" rid="CR52">52</xref>]. After extracting the essential features using these methods, a statistical evaluation and Student&#x02019;s <italic>t</italic> test&#x02013;based validation is implemented to select the dominant features to create the essential FVs, such as <italic>F</italic><italic>V</italic><sub><italic>C</italic><italic>W</italic><italic>T</italic></sub> (34 features), <italic>F</italic><italic>V</italic><sub><italic>D</italic><italic>W</italic><italic>T</italic></sub> (32 features), and <italic>F</italic><italic>V</italic><sub><italic>E</italic><italic>W</italic><italic>T</italic></sub> (3 features) which are considered to get the principle FV1 set (FV1 = 69 features) by sorting and arranging these features based on its <italic>p</italic> value and <italic>t</italic> value. The feature selection process and FV1 creation are implemented as discussed in [<xref ref-type="bibr" rid="CR52">52</xref>].
<list list-type="bullet"><list-item><p id="Par42">CWT: This function was derived from the Fourier transform and is represented using complex-valued scaling function and complex-valued wavelet as defined below;
<disp-formula id="Equ10"><label>10</label><alternatives><tex-math id="M29">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \psi_{C}(t)=\psi_{R}(t)+\psi_{I}(t) $$\end{document}</tex-math><mml:math id="M30"><mml:msub><mml:mrow><mml:mi>&#x003c8;</mml:mi></mml:mrow><mml:mrow><mml:mi>C</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003c8;</mml:mi></mml:mrow><mml:mrow><mml:mi>R</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003c8;</mml:mi></mml:mrow><mml:mrow><mml:mi>I</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ10.gif" position="anchor"/></alternatives></disp-formula>where <italic>&#x003c8;</italic><sub><italic>C</italic></sub>(<italic>t</italic>), <italic>&#x003c8;</italic><sub><italic>R</italic></sub>(<italic>t</italic>), and <italic>&#x003c8;</italic><sub><italic>I</italic></sub>(<italic>t</italic>) represent the complex, real, and image parts respectively.</p></list-item><list-item><p id="Par43">DWT: This approach evaluates the non-stationary information. When a wavelet has the function <italic>&#x003c8;</italic>(<italic>t</italic>) &#x02208; <italic>W</italic><sup>2</sup>(<italic>r</italic>), then its DWT (denoted by <italic>D</italic><italic>W</italic><italic>T</italic>(<italic>a</italic>, <italic>b</italic>)) can be written as:
<disp-formula id="Equ11"><label>11</label><alternatives><tex-math id="M31">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ DWT(a,b)=\frac{1}{\sqrt{2^{a}}} {\int}_{-\infty}^{\infty}x(t)\psi^{*}\left( \frac{t-b2^{a}}{2^{a}}\right) dt $$\end{document}</tex-math><mml:math id="M32"><mml:mi>D</mml:mi><mml:mi>W</mml:mi><mml:mi>T</mml:mi><mml:mo>(</mml:mo><mml:mi>a</mml:mi><mml:mo>,</mml:mo><mml:mi>b</mml:mi><mml:mo>)</mml:mo><mml:mo>=</mml:mo><mml:mfrac class="tfrac"><mml:mrow><mml:mn>1</mml:mn></mml:mrow><mml:mrow><mml:msqrt><mml:mrow><mml:msup><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mrow><mml:mi>a</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:msqrt></mml:mrow></mml:mfrac><mml:msubsup><mml:mrow><mml:mo>&#x0222b;</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mi>&#x0221e;</mml:mi></mml:mrow><mml:mrow><mml:mi>&#x0221e;</mml:mi></mml:mrow></mml:msubsup><mml:mi>x</mml:mi><mml:mo>(</mml:mo><mml:mi>t</mml:mi><mml:mo>)</mml:mo><mml:msup><mml:mrow><mml:mi>&#x003c8;</mml:mi></mml:mrow><mml:mrow><mml:mo>&#x02217;</mml:mo></mml:mrow></mml:msup><mml:mfenced close=")" open="("><mml:mrow><mml:mfrac class="tfrac"><mml:mrow><mml:mi>t</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>b</mml:mi><mml:msup><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mrow><mml:mi>a</mml:mi></mml:mrow></mml:msup></mml:mrow><mml:mrow><mml:msup><mml:mrow><mml:mn>2</mml:mn></mml:mrow><mml:mrow><mml:mi>a</mml:mi></mml:mrow></mml:msup></mml:mrow></mml:mfrac></mml:mrow></mml:mfenced><mml:mi>d</mml:mi><mml:mi>t</mml:mi></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ11.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par44">where <italic>&#x003c8;</italic>(<italic>t</italic>) is the principle wavelet, the symbol &#x02217; denotes the complex conjugate, <italic>a</italic> and <italic>b</italic> (<italic>a</italic>, <italic>b</italic> &#x02208; <italic>R</italic>) are scaling parameters of dilation and transition respectively.</p></list-item><list-item><p id="Par45">EWT: The Fourier spectrum of EWT of range 0 to <italic>&#x003c0;</italic> is segmented into <italic>M</italic> regions. Each limit is denoted as <italic>&#x003c9;</italic><sub><italic>m</italic></sub> (where <italic>m</italic> = 1, 2, ... , <italic>M</italic>) in which the starting limit is <italic>&#x003c9;</italic><sub>0</sub> = 0 and final limit is <italic>&#x003c9;</italic><sub><italic>M</italic></sub> = <italic>&#x003c0;</italic>. The translation phase <italic>T</italic><sub><italic>m</italic></sub> centered around <italic>&#x003c9;</italic><sub><italic>m</italic></sub> has a width of 2&#x003a6;<sub><italic>m</italic></sub> where &#x003a6;<sub><italic>m</italic></sub> = <italic>&#x003bb;</italic><italic>&#x003c9;</italic><sub><italic>m</italic></sub> for 0 &#x0003c; <italic>&#x003bb;</italic> &#x0003c; 1. Other information on EWT can be found in [<xref ref-type="bibr" rid="CR53">53</xref>].</p></list-item></list></p></sec><sec id="FPar9"><title>Feature Vector 2 (FV2):</title><p id="Par46">The essential information from the binary form of COVID-19 infection image is extracted using the feature extraction procedure discussed in Bhandary et al. [<xref ref-type="bibr" rid="CR35">35</xref>] and this work helped get the essential binary features using the Haralick and Hu technique. This method helps get 27 numbers of features (<italic>F</italic><sub><italic>H</italic><italic>a</italic><italic>r</italic><italic>a</italic><italic>l</italic><italic>i</italic><italic>c</italic><italic>k</italic></sub> = 18 features and <italic>F</italic><sub><italic>H</italic><italic>u</italic></sub> = 9 features) and the combination of these features helped get the 1D FV2 (FV2 = 27 features).
<list list-type="bullet"><list-item><p id="Par47"><italic>Haralick features</italic>: Haralick features are computed using a Gray Level Co-occurrence Matrix (GLCM). GLCM is a matrix, in which the total rows and columns depend on the gray levels (<italic>G</italic>) of the image. In this, the matrix component <italic>P</italic>(<italic>i</italic>, <italic>j</italic>|&#x00394;<italic>x</italic>,&#x00394;<italic>y</italic>) is the virtual frequency alienated by a pixel space (&#x00394;<italic>x</italic>,&#x00394;<italic>y</italic>). If <italic>&#x003bc;</italic><sub><italic>x</italic></sub> and <italic>&#x003bc;</italic><sub><italic>y</italic></sub> represent the mean and <italic>&#x003c3;</italic><sub><italic>x</italic></sub> and <italic>&#x003c3;</italic><sub><italic>y</italic></sub> represent the standard deviation of <italic>P</italic><sub><italic>x</italic></sub> and <italic>P</italic><sub><italic>y</italic></sub>, then:
<disp-formula id="Equ12"><label>12</label><alternatives><tex-math id="M33">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \begin{array}{@{}rcl@{}} \mu_{x}&#x00026;=&#x00026;{\sum}_{i=0}^{G-1}iP_{x}(i),\\ \mu_{y}&#x00026;=&#x00026;{\sum}_{j=0}^{G-1}jP_{y}(j),\\ \sigma_{x}&#x00026;=&#x00026;{\sum}_{i=0}^{G-1}(P_{x}(i)-\mu_{x}(i))\\ \sigma_{y}&#x00026;=&#x00026;{\sum}_{j=0}^{G-1}(P_{y}(j)-\mu_{y}(j)). \end{array} $$\end{document}</tex-math><mml:math id="M34"><mml:mtable class="eqnarray" columnalign="right center left"><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>&#x003bc;</mml:mi></mml:mrow><mml:mrow><mml:mi>x</mml:mi></mml:mrow></mml:msub></mml:mtd><mml:mtd class="eqnarray-2"><mml:mo>=</mml:mo></mml:mtd><mml:mtd class="eqnarray-3"><mml:msubsup><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:mrow><mml:mrow><mml:mi>G</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msubsup><mml:mi>i</mml:mi><mml:msub><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>x</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>)</mml:mo><mml:mo>,</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>&#x003bc;</mml:mi></mml:mrow><mml:mrow><mml:mi>y</mml:mi></mml:mrow></mml:msub></mml:mtd><mml:mtd class="eqnarray-2"><mml:mo>=</mml:mo></mml:mtd><mml:mtd class="eqnarray-3"><mml:msubsup><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:mrow><mml:mrow><mml:mi>G</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msubsup><mml:mi>j</mml:mi><mml:msub><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>y</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo><mml:mo>,</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>&#x003c3;</mml:mi></mml:mrow><mml:mrow><mml:mi>x</mml:mi></mml:mrow></mml:msub></mml:mtd><mml:mtd class="eqnarray-2"><mml:mo>=</mml:mo></mml:mtd><mml:mtd class="eqnarray-3"><mml:msubsup><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:mrow><mml:mrow><mml:mi>G</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msubsup><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>x</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>)</mml:mo><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003bc;</mml:mi></mml:mrow><mml:mrow><mml:mi>x</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>i</mml:mi><mml:mo>)</mml:mo><mml:mo>)</mml:mo></mml:mtd></mml:mtr><mml:mtr><mml:mtd class="eqnarray-1"><mml:msub><mml:mrow><mml:mi>&#x003c3;</mml:mi></mml:mrow><mml:mrow><mml:mi>y</mml:mi></mml:mrow></mml:msub></mml:mtd><mml:mtd class="eqnarray-2"><mml:mo>=</mml:mo></mml:mtd><mml:mtd class="eqnarray-3"><mml:msubsup><mml:mrow><mml:mo>&#x02211;</mml:mo></mml:mrow><mml:mrow><mml:mi>j</mml:mi><mml:mo>=</mml:mo><mml:mn>0</mml:mn></mml:mrow><mml:mrow><mml:mi>G</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mn>1</mml:mn></mml:mrow></mml:msubsup><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>P</mml:mi></mml:mrow><mml:mrow><mml:mi>y</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mrow><mml:mi>&#x003bc;</mml:mi></mml:mrow><mml:mrow><mml:mi>y</mml:mi></mml:mrow></mml:msub><mml:mo>(</mml:mo><mml:mi>j</mml:mi><mml:mo>)</mml:mo><mml:mo>)</mml:mo><mml:mn>.</mml:mn></mml:mtd></mml:mtr></mml:mtable></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ12.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par48">where <italic>P</italic><sub><italic>x</italic></sub>(<italic>i</italic>) and <italic>P</italic><sub><italic>y</italic></sub>(<italic>j</italic>) matrix components during the <italic>i</italic>-th and <italic>j</italic>-th entries, respectively.</p><p id="Par49">These parameters can be used to extract the essential texture and shape features from the considered grayscale image.</p></list-item><list-item><p id="Par50"><italic>Hu moments</italic>: For a two-dimensional (2D) image, the 2D (<italic>i</italic> + <italic>j</italic>)-th order moments can be defined as;
<disp-formula id="Equ13"><label>13</label><alternatives><tex-math id="M35">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ M_{ij}={\int}_{-\infty}^{\infty}{\int}_{-\infty}^{\infty}x^{i}y^{j}f(x,y)dxdy $$\end{document}</tex-math><mml:math id="M36"><mml:msub><mml:mrow><mml:mi>M</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi><mml:mi>j</mml:mi></mml:mrow></mml:msub><mml:mo>=</mml:mo><mml:msubsup><mml:mrow><mml:mo>&#x0222b;</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mi>&#x0221e;</mml:mi></mml:mrow><mml:mrow><mml:mi>&#x0221e;</mml:mi></mml:mrow></mml:msubsup><mml:msubsup><mml:mrow><mml:mo>&#x0222b;</mml:mo></mml:mrow><mml:mrow><mml:mo>&#x02212;</mml:mo><mml:mi>&#x0221e;</mml:mi></mml:mrow><mml:mrow><mml:mi>&#x0221e;</mml:mi></mml:mrow></mml:msubsup><mml:msup><mml:mrow><mml:mi>x</mml:mi></mml:mrow><mml:mrow><mml:mi>i</mml:mi></mml:mrow></mml:msup><mml:msup><mml:mrow><mml:mi>y</mml:mi></mml:mrow><mml:mrow><mml:mi>j</mml:mi></mml:mrow></mml:msup><mml:mi>f</mml:mi><mml:mo>(</mml:mo><mml:mi>x</mml:mi><mml:mo>,</mml:mo><mml:mi>y</mml:mi><mml:mo>)</mml:mo><mml:mi>d</mml:mi><mml:mi>x</mml:mi><mml:mi>d</mml:mi><mml:mi>y</mml:mi></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ13.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par51">for <italic>i</italic>, <italic>j</italic> = 0, 1, 2,... If the image function <italic>f</italic>(<italic>x</italic>, <italic>y</italic>) is a piecewise continuous value, then the moments of all order exist and the moment sequence <italic>M</italic><sub><italic>i</italic><italic>j</italic></sub> is uniquely determined. Other information on Hu moments can be found in [<xref ref-type="bibr" rid="CR35">35</xref>].</p></list-item></list></p></sec><sec id="FPar10"><title>Fused Feature Vector (FFV:)</title><p id="Par52">In this work, the original test image helped get the FV1 and the binary form of the COVID-19 helps get the FV2. To implement a classifier, it is essential to have a single feature vector with a pre-defined dimension.</p><p id="Par53">In this work, the FFV based on the principle component analysis (PCA) is implemented to attain a 1D FFV (69 + 27 = 96 features) by combining the FV1 and FV2, and this feature set is then considered to train, test, and validate the classifier system implemented in this study. The complete information on the feature fusion based on the serial fusion can be found in [<xref ref-type="bibr" rid="CR35">35</xref>, <xref ref-type="bibr" rid="CR54">54</xref>].</p></sec><sec id="FPar11"><title>Classification</title><p id="Par54">Classification is one of the essential parts in a verity of ML and deep learning (DL) techniques implemented to examine a class of medical datasets. The role of the classifier is to segregate the considered medical database into two-class and multi-class information using the chosen classifier system. In the proposed work, the classifiers, such as Random-Forest (RF), Support Vector Machine-Radial Basis Function (SVM-RBF), K-Nearest Neighbors (KNN), and Decision Tree (DT), are considered. The essential information on the implemented classifier units can be found in [<xref ref-type="bibr" rid="CR35">35</xref>, <xref ref-type="bibr" rid="CR36">36</xref>, <xref ref-type="bibr" rid="CR45">45</xref>, <xref ref-type="bibr" rid="CR52">52</xref>]. A fivefold cross-validation is implemented and the best result among the trial is chosen as the final classification result.</p></sec><sec id="FPar12"><title>Validation</title><p id="Par55">From the literature, it can be noted that the performance of the ML and DL-based data analysis is normally confirmed by computing the essential performance measures [<xref ref-type="bibr" rid="CR35">35</xref>, <xref ref-type="bibr" rid="CR36">36</xref>]. In this work, the common performance measures, such as accuracy (<xref rid="Equ4" ref-type="">4</xref>), precision (<xref rid="Equ15" ref-type="">15</xref>), sensitivity (<xref rid="Equ16" ref-type="">16</xref>), specificity (<xref rid="Equ17" ref-type="">17</xref>), F1-score (<xref rid="Equ18" ref-type="">18</xref>), and negative predictive value (NPV) (<xref rid="Equ19" ref-type="">19</xref>) computed.</p><p id="Par56">The mathematical expression for these values is as follows:
<disp-formula id="Equ14"><label>14</label><alternatives><tex-math id="M37">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \text{Accuracy}=\frac{(T_{P}+T_{N})}{(T_{P}+T_{N}+F_{P}+F_{N} )} $$\end{document}</tex-math><mml:math id="M38"><mml:mtext>Accuracy</mml:mtext><mml:mo>=</mml:mo><mml:mfrac class="tfrac"><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>F</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>F</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ14.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ15"><label>15</label><alternatives><tex-math id="M39">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \text{Precision}=\frac{T_{P}}{(T_{P}+F_{P} )} $$\end{document}</tex-math><mml:math id="M40"><mml:mtext>Precision</mml:mtext><mml:mo>=</mml:mo><mml:mfrac class="tfrac"><mml:mrow><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>F</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ15.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ16"><label>16</label><alternatives><tex-math id="M41">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \text{Sensitivity}=\frac{T_{P}}{(T_{P}+F_{N})} $$\end{document}</tex-math><mml:math id="M42"><mml:mtext>Sensitivity</mml:mtext><mml:mo>=</mml:mo><mml:mfrac class="tfrac"><mml:mrow><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>F</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ16.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ17"><label>17</label><alternatives><tex-math id="M43">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \text{Specificity}=\frac{T_{N}}{(T_{N}+F_{P})} $$\end{document}</tex-math><mml:math id="M44"><mml:mtext>Specificity</mml:mtext><mml:mo>=</mml:mo><mml:mfrac class="tfrac"><mml:mrow><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>F</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ17.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ18"><label>18</label><alternatives><tex-math id="M45">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \text{F1-Score}=\frac{2T_{P}}{(2T_{P}+F_{N}+F_{P})} $$\end{document}</tex-math><mml:math id="M46"><mml:mtext>F1-Score</mml:mtext><mml:mo>=</mml:mo><mml:mfrac class="tfrac"><mml:mrow><mml:mn>2</mml:mn><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:mn>2</mml:mn><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>F</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>F</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ18.gif" position="anchor"/></alternatives></disp-formula><disp-formula id="Equ19"><label>19</label><alternatives><tex-math id="M47">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ \text{NPV}=\frac{T_{N}}{(T_{N}+F_{N})} $$\end{document}</tex-math><mml:math id="M48"><mml:mtext>NPV</mml:mtext><mml:mo>=</mml:mo><mml:mfrac class="tfrac"><mml:mrow><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mo>(</mml:mo><mml:msub><mml:mrow><mml:mi>T</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mrow><mml:mi>F</mml:mi></mml:mrow><mml:mrow><mml:mi>N</mml:mi></mml:mrow></mml:msub><mml:mo>)</mml:mo></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12559_2020_9751_Article_Equ19.gif" position="anchor"/></alternatives></disp-formula>where <italic>T</italic><sub><italic>P</italic></sub>= true positive, <italic>T</italic><sub><italic>N</italic></sub>= true negative, <italic>F</italic><sub><italic>P</italic></sub>= false positive, and <italic>F</italic><sub><italic>N</italic></sub>=false negative.</p></sec></sec></sec><sec id="Sec7"><title>COVID-19 Dataset</title><p id="Par57">The clinical-level diagnosis of the COVID-19 pneumonia infection is normally assessed using the imaging procedure. In this research, the lung CTI are considered for the examination and these images are resized into 256 &#x000d7; 256 &#x000d7; 1 pixels to reduce the computation complexity. This work considered 400 grayscale lung CTI (200 normal and 200 COVID-19 class images) for the assessment. This research initially considered the benchmark COVID-19 database of [<xref ref-type="bibr" rid="CR44">44</xref>] for the assessment. This dataset consists of 100 2D lung CTI along with its GT; and in this research, only 78 images are considered for the assessment and the remaining 22 images are discarded due to its poor resolution and the associated artifacts. The remaining COVID-19 CTI (122 images) are collected from the Radiopaedia database [<xref ref-type="bibr" rid="CR55">55</xref>] from cases 3 [<xref ref-type="bibr" rid="CR56">56</xref>], 8 [<xref ref-type="bibr" rid="CR57">57</xref>], 23 [<xref ref-type="bibr" rid="CR58">58</xref>], 10 [<xref ref-type="bibr" rid="CR59">59</xref>], 27 [<xref ref-type="bibr" rid="CR60">60</xref>] 52 [<xref ref-type="bibr" rid="CR61">61</xref>], 55 [<xref ref-type="bibr" rid="CR62">62</xref>], and 56 [<xref ref-type="bibr" rid="CR63">63</xref>].</p><p id="Par58">The normal class images of the 2D lung CTI have been collected from The Lung Image Database Consortium-Image Database Resource Initiative (LIDC-IDRI) [<xref ref-type="bibr" rid="CR64">64</xref>&#x02013;<xref ref-type="bibr" rid="CR66">66</xref>] and The Reference Image Database to Evaluate therapy Response-The Cancer Imaging Archive (RIDER-TCIA) [<xref ref-type="bibr" rid="CR66">66</xref>, <xref ref-type="bibr" rid="CR67">67</xref>] database and the sample images of the collected dataset are depicted in Figs.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> and&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>. Figure&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref> presents the test image and the related GT of the benchmark CTI. Figure&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref> depicts the images of the COVID-19 [<xref ref-type="bibr" rid="CR55">55</xref>] and normal lung [<xref ref-type="bibr" rid="CR64">64</xref>, <xref ref-type="bibr" rid="CR67">67</xref>] CTI considered for the assessment.
<fig id="Fig4"><label>Fig. 4</label><caption><p>Sample test images of COVID-19 and the GT collected from [<xref ref-type="bibr" rid="CR24">24</xref>]</p></caption><graphic xlink:href="12559_2020_9751_Fig4_HTML" id="MO5"/></fig><fig id="Fig5"><label>Fig. 5</label><caption><p>Sample test images of COVID-19 and normal group</p></caption><graphic xlink:href="12559_2020_9751_Fig5_HTML" id="MO6"/></fig></p></sec></sec><sec id="Sec8"><title>Results and Discussion</title><p id="Par59">The experimental results obtained in the proposed work are presented and discussed in this section. This developed system is executed using a workstation with the configuration: Intel i5 2.GHz processor with 8GB RAM and 2GB VRAM equipped with the MATLAB (<ext-link ext-link-type="uri" xlink:href="http://www.mathworks.com">www.mathworks.com</ext-link>). Experimental results of this study confirm that this scheme requires a mean time of 173 &#x000b1; 11 s to process the considered CTI dataset and the processing time can be improved by using a workstation with higher computational capability. The advantage of this scheme is it is a fully automated practice and will not require the operator assistance during the execution. The proposed research initially executes the COVID-19 infection segmentation task using the benchmark dataset of [<xref ref-type="bibr" rid="CR44">44</xref>]. The results attained using a chosen trial image are depicted in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>. Figure&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>a depicts the sample image of dimension 256 &#x000d7; 256 &#x000d7; 1 and Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>b and c depict the actual and the binary forms of the GT image. The result attained with the SGO-KE-based tri-level threshold is depicted in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>d. Later, the KMC is employed to segregate Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>d into three different sections and the separated images are shown in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6e&#x02013;g</xref>. Finally, a morphological segmentation technique is implemented to segment the COVID-19 infection from Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6g</xref> and the attained result is presented in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6h</xref>. After extracting the COVID-19 infection from the test image, the performance of the proposed segmentation method is confirmed by implementing a comparative examination between the binary GT existing in Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref>c with Fig.&#x000a0;<xref rid="Fig6" ref-type="fig">6h</xref> and the essential performance values are then computed based on the pixel information of the background (0) and the COVID-19 section (1). For this image, the values attained are <italic>T</italic><sub><italic>P</italic></sub> = 5865 pixels, <italic>F</italic><sub><italic>P</italic></sub> = 306, <italic>T</italic><sub><italic>N</italic></sub> = 52572, and <italic>F</italic><sub><italic>N</italic></sub> = 1949, and these values offered accuracy = 96.28%, precision = 95.04%, sensitivity = 75.06%, specificity = 99.42%, F1-score = 83.88%, and NPV = 96.43%.
<fig id="Fig6"><label>Fig. 6</label><caption><p>Results attained with the benchmark COVID-19 database. <bold>a</bold> Sample test image. <bold>b</bold> FT image. <bold>c</bold> Binary GT. <bold>d</bold> SGO-KE thresholded image. <bold>e</bold> Background. <bold>f</bold> Artifact. <bold>g</bold> Lung section. <bold>h</bold> Segmented COVID-19 infection</p></caption><graphic xlink:href="12559_2020_9751_Fig6_HTML" id="MO7"/></fig></p><p id="Par60">A similar procedure is implemented for other images of this dataset and means performance measure attained for the whole benchmark database (78 images) is depicted in Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref>. From this figure, it is evident that the segmentation accuracy attained for this dataset is higher than 91%, and in the future the performance of the proposed segmentation method can be validated against other thresholding and segmentation procedures existing in the medical imaging literature.
<fig id="Fig7"><label>Fig. 7</label><caption><p>Mean performance measure attained with the proposed COVID-19 segmentation procedure</p></caption><graphic xlink:href="12559_2020_9751_Fig7_HTML" id="MO8"/></fig></p><p id="Par61">The methodology depicted in Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> is then implemented by considering the entire database of the CTI prepared in this research work. This dataset consists of 400 grayscale images with dimension 256 &#x000d7; 256 &#x000d7; 1 pixels and the normal/COVID-19 class images have a similar dimension to confirm the performance of the proposed technique. Initially, the proposed ML scheme is implemented by considering only the grayscale image features (FV1) with a dimension 1 &#x000d7; 69 and the performance of the considered classifier units, such as RF, KNN, SVM-RBF, and DT, is computed. During this procedure, 70% of the database (140 + 140 = 280 images) are considered for training and 30% (60 + 60 = 120 images) are considered for testing. After checking its function, each classifier is separately validated by using the entire database and the attained results are recorded. Here, a fivefold cross-validation is implemented for each classifier and the best result attained is considered as the final result. The obtained results are depicted in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> (the first three rows). The results reveal that the classification accuracy attained with SVM-RBF is superior (85%) compared with the RF, KNN, and DT. Also, the RF technique helped get the better values of the sensitivity and NPV compared with other classifiers.
<table-wrap id="Tab1"><label>Table 1</label><caption><p>Disease detection performance attained with the proposed ML scheme</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Features</th><th align="left">Classifier</th><th align="left">TP</th><th align="left">FN</th><th align="left">TN</th><th align="left">FP</th><th align="left">Acc. (%)</th><th align="left">Prec. (%)</th><th align="left">Sens. (%)</th><th align="left">Spec. (%)</th><th align="left">F1-Sc. (%)</th><th align="left">NPV (%)</th></tr></thead><tbody><tr><td align="left">FV1 (1&#x000d7;69)</td><td align="left">RF</td><td align="left">163</td><td align="left">37</td><td align="left">172</td><td align="left">28</td><td align="left">83.75</td><td align="left">85.34</td><td align="left"><italic>81.50</italic></td><td align="left">86.00</td><td align="left">83.37</td><td align="left"><italic>82.30</italic></td></tr><tr><td align="left"/><td align="left">KNN</td><td align="left">159</td><td align="left">41</td><td align="left">177</td><td align="left">23</td><td align="left">84.00</td><td align="left">87.36</td><td align="left">79.50</td><td align="left">88.50</td><td align="left">83.24</td><td align="left">81.19</td></tr><tr><td align="left"/><td align="left">SVM-RBF</td><td align="left">161</td><td align="left">39</td><td align="left">179</td><td align="left">21</td><td align="left"><italic>85.00</italic></td><td align="left"><italic>88.46</italic></td><td align="left">80.50</td><td align="left"><italic>89.50</italic></td><td align="left"><italic>84.29</italic></td><td align="left">82.11</td></tr><tr><td align="left"/><td align="left">DT</td><td align="left">160</td><td align="left">40</td><td align="left">168</td><td align="left">32</td><td align="left">82.00</td><td align="left">83.33</td><td align="left">80.00</td><td align="left">84.00</td><td align="left">81.63</td><td align="left">80.77</td></tr><tr><td align="left">FFV (1&#x000d7;96)</td><td align="left">RF</td><td align="left">169</td><td align="left">31</td><td align="left">178</td><td align="left">22</td><td align="left">86.75</td><td align="left"><italic>88.48</italic></td><td align="left">84.50</td><td align="left"><italic>89.00</italic></td><td align="left">86.45</td><td align="left">85.17</td></tr><tr><td align="left"/><td align="left">KNN</td><td align="left">178</td><td align="left">22</td><td align="left">173</td><td align="left">27</td><td align="left"><italic>87.75</italic></td><td align="left">86.83</td><td align="left"><italic>89.00</italic></td><td align="left">86.50</td><td align="left"><italic>87.90</italic></td><td align="left"><italic>88.72</italic></td></tr><tr><td align="left"/><td align="left">SVM-RBF</td><td align="left">172</td><td align="left">28</td><td align="left">177</td><td align="left">23</td><td align="left">87.25</td><td align="left">88.20</td><td align="left">86.00</td><td align="left">88.50</td><td align="left">87.09</td><td align="left">86.34</td></tr><tr><td align="left"/><td align="left">DT</td><td align="left">174</td><td align="left">26</td><td align="left">172</td><td align="left">28</td><td align="left">86.50</td><td align="left">86.14</td><td align="left">87.00</td><td align="left">86.00</td><td align="left">86.57</td><td align="left">86.89</td></tr></tbody></table><table-wrap-foot><p><italic>TP</italic>, true positive; <italic>FN</italic>, false negative; <italic>TN</italic>, true negative; <italic>FP</italic>, false positive; <italic>Acc.</italic>, accuracy; <italic>Prec.</italic>, precision; <italic>Sens.</italic>, sensitivity; <italic>Spec.</italic>, specificity; <italic>F1-Sc.</italic>, F1-score; <italic>NPV</italic>, negative predictive value, italicized values indicate the best performance.</p></table-wrap-foot></table-wrap></p><p id="Par62">To improve the detection accuracy, the feature vector size is increased by considering the FFV (1 &#x000d7; 96 features) and a similar procedure is repeated. The obtained results (as in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>, bottom three rows) with the FFV confirm that the increment of features improves the detection accuracy considerably and the KNN classifier offers an improved accuracy (higher than 87%) compared with the RF, SVM-RBF, and DT. The precision and the F1-score offered by the RF are superior compared with the alternatives. The experimental results attained with the proposed ML scheme revealed that this methodology helps achieve better classification accuracy on the considered lung CTI dataset. The accuracy attained with the chosen classifiers for FV1 and FFV is depicted in Fig.&#x000a0;<xref rid="Fig8" ref-type="fig">8</xref>. The future scope of the proposed method includes (i) implementing the proposed ML scheme to test the clinically obtained CTI of COVID-19 patients; (ii) enhancing the performance of implemented ML technique by considering the other feature extraction and classification procedures existing in the literature; and (iii) implementing and validating the performance of the proposed ML with other ML techniques existing in the literature; and (iv) implementing an appropriate DL architecture to attain better detection accuracy on the benchmark as well as the clinical grade COVID-19 infected lung CTI.
<fig id="Fig8"><label>Fig. 8</label><caption><p>Detection accuracy attained in the proposed system with various classifiers</p></caption><graphic xlink:href="12559_2020_9751_Fig8_HTML" id="MO9"/></fig></p></sec><sec id="Sec9"><title>Conclusion</title><p id="Par63">The aim of this work has been to develop an automated detection pipeline to recognize the COVID-19 infection from lung CTI. This work proposes an ML-based system to achieve this task. The proposed system executed a sequence of procedures ranging from image pre-processing to the classification to develop a better COVID-19 detection tool. The initial part of the work implements an image segmentation procedure with SGO-KE thresholding, KMC-based separation, morphology-based COVID-19 infection extraction, and a relative study between the extracted COVID-19 sections with the GT. The segmentation assisted to achieve an overall accuracy higher than 91% on a benchmark CTI dataset. Later, an ML scheme with essential procedures such as feature extraction, feature selection, feature fusion, and classification is implemented on the considered data, and the proposed scheme with the KNN classifier achieved an accuracy higher than 87%.</p></sec></body><back><fn-group><fn id="Fn1"><label>1</label><p id="Par3"><ext-link ext-link-type="uri" xlink:href="https://www.worldometers.info/coronavirus/">https://www.worldometers.info/coronavirus/</ext-link>, as of June 17, 2020</p></fn><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><ack><p>The authors of this paper would like to thank Medicalsegmentation.com and Radiopaedia.org for sharing the clinical-grade COVID-19 images.</p></ack><notes notes-type="author-contribution"><title>Author Contributions</title><p>This work was carried out in close collaboration between all co-authors. ND, VR, MSK, and MM first defined the research theme and contributed an early design of the system. ND and VR further implemented and refined the system development. ND, VR, SJF, MSK, and MM wrote the paper. All authors have contributed to, seen, and approved the final manuscript.</p></notes><notes><title>Compliance with Ethical Standards</title><notes id="FPar13" notes-type="COI-statement"><title><bold>Conflict of Interest</bold></title><p id="Par64">All authors declare that they have no conflict of interest.</p></notes><notes id="FPar14"><title>Ethical Approval</title><p id="Par65">All procedures reported in this study were in accordance with the ethical standards of the institutional and/or national research committee and with the 1964 Helsinki declaration and its later amendments or comparable ethical standards.</p></notes><notes id="FPar15"><title>Informed Consent</title><p id="Par66">This study used secondary data; therefore, the informed consent does not apply.</p></notes></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><mixed-citation publication-type="other">WHO. WHO, editor. Coronavirus. WHO; 2020. Last accessed: 10th April 2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://www.who.int/emergencies/diseases/novel-coronavirus-2019">https://www.who.int/emergencies/diseases/novel-coronavirus-2019</ext-link>.</mixed-citation></ref><ref id="CR2"><label>2.</label><mixed-citation publication-type="other">(ALA) ALA. ALA, editor. Coronavirus update- Worldometer. ALA; 2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://www.worldometers.info/coronavirus/">https://www.worldometers.info/coronavirus/</ext-link>.</mixed-citation></ref><ref id="CR3"><label>3.</label><mixed-citation publication-type="other">WHO. WHO, editor. WHO/Europe | Coronavirus disease (COVID-19) outbreak - WHO announces COVID-19 outbreak a pandemic. WHO; 1948. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://bit.ly/3bvuX8S">https://bit.ly/3bvuX8S</ext-link>.</mixed-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>Q</given-names></name><etal/></person-group><article-title>Early transmission dynamics in Wuhan, China, of novel coronavirus&#x02013;infected pneumonia</article-title><source>Engl J Med</source><year>2020</year><volume>382</volume><issue>13</issue><fpage>1199</fpage><lpage>1207</lpage><pub-id pub-id-type="doi">10.1056/NEJMoa2001316</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><mixed-citation publication-type="other">Bai HX, et al. Performance of radiologists in differentiating COVID-19 from viral pneumonia on chest CT. Radiology. 2020; p. 200823. [epub ahead of print.]</mixed-citation></ref><ref id="CR6"><label>6.</label><mixed-citation publication-type="other">Chua F, Armstrong-James D, Desai SR, Barnett J, Kouranos V, Kon OM, et al. The role of CT in case ascertainment and management of COVID-19 pneumonia in the UK: insights from high-incidence regions. The Lancet Respiratory Medicine. 2020;0(0). EPub ahead of print. 10.1016/S2213-2600(20)30132-6.</mixed-citation></ref><ref id="CR7"><label>7.</label><mixed-citation publication-type="other">Santosh KC. AI-driven tools for coronavirus outbreak: need of active learning and cross-population train/test models on multitudinal/multimodal data. Journal of Medical Systems. 2020;44:93.</mixed-citation></ref><ref id="CR8"><label>8.</label><mixed-citation publication-type="other">Rajinikanth V, et al. Harmony-Search and Otsu based system for coronavirus disease (COVID-19) detection using lung CT scan images. CoRR. 2020; Available from: <ext-link ext-link-type="uri" xlink:href="https://arxiv.org/abs/2004.03431">https://arxiv.org/abs/2004.03431</ext-link>.</mixed-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liu</surname><given-names>K</given-names></name><name><surname>Xu</surname><given-names>P</given-names></name><name><surname>Lv</surname><given-names>WF</given-names></name><name><surname>Qiu</surname><given-names>XH</given-names></name><name><surname>Yao</surname><given-names>JL</given-names></name><name><surname>Gu</surname><given-names>JF</given-names></name><etal/></person-group><article-title>CT manifestations of coronavirus disease-2019: a retrospective analysis of 73 cases by disease severity</article-title><source>Eur J Radiol</source><year>2020</year><volume>126</volume><fpage>108941</fpage><pub-id pub-id-type="doi">10.1016/j.ejrad.2020.108941</pub-id><pub-id pub-id-type="pmid">32193037</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><mixed-citation publication-type="other">Yang R, Li X, Liu H, Zhen Y, Zhang X, Xiong Q, et al. Chest CT severity score: an imaging tool for assessing severe COVID-19. Radiol Cardioth Imaging. 2020;2(2):e200047.</mixed-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fong</surname><given-names>SJ</given-names></name></person-group><article-title>Finding an accurate early forecasting model from small dataset: a case of 2019-nCoV novel coronavirus outbreak</article-title><source>Int J Interact Multimed Artif Intell</source><year>2020</year><volume>6</volume><issue>1</issue><fpage>132</fpage><lpage>140</lpage></element-citation></ref><ref id="CR12"><label>12.</label><mixed-citation publication-type="other">Fong SJ, Li G, Dey N, Crespo RG, Herrera-Viedma E. Composite Monte Carlo decision making under high uncertainty of novel coronavirus epidemic using hybridized deep learning and fuzzy rule induction. Applied Soft Computing. 2020; p. 106282.</mixed-citation></ref><ref id="CR13"><label>13.</label><mixed-citation publication-type="other">Verity R, Okell LC, Dorigatti I, Winskill P, Whittaker C, Imai N, et al. Estimates of the severity of coronavirus disease 2019: a model-based analysis. The Lancet Infectious Diseases. 2020; 0(0). Publisher: Elsevier.</mixed-citation></ref><ref id="CR14"><label>14.</label><mixed-citation publication-type="other">Fang Y, et al. Sensitivity of chest CT for COVID-19: comparison to RT-PCR. Radiology. 2020;0(0):200432.</mixed-citation></ref><ref id="CR15"><label>15.</label><mixed-citation publication-type="other">Zhou Z, Guo D, Li C, Fang Z, Chen L, Yang R, et al. Coronavirus disease 2019: initial chest CT findings. European Radiology. 2020; EPub ahead of print. 10.1007/s00330-020-06816-7.</mixed-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yoon</surname><given-names>SH</given-names></name><name><surname>Lee</surname><given-names>KH</given-names></name><name><surname>Kim</surname><given-names>JY</given-names></name><name><surname>Lee</surname><given-names>YK</given-names></name><name><surname>Ko</surname><given-names>H</given-names></name><name><surname>Kim</surname><given-names>KH</given-names></name><etal/></person-group><article-title>Chest radiographic and CT findings of the 2019 novel coronavirus disease (COVID-19): analysis of nine patients treated in Korea</article-title><source>Korean J Radiol</source><year>2020</year><volume>21</volume><issue>4</issue><fpage>494</fpage><lpage>500</lpage><pub-id pub-id-type="doi">10.3348/kjr.2020.0132</pub-id><pub-id pub-id-type="pmid">32100485</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><mixed-citation publication-type="other">Li K, Fang Y, Li W, Pan C, Qin P, Zhong Y, et al. CT Image visual quantitative evaluation and clinical classification of coronavirus disease (COVID-19). European Radiology. 2020; EPub ahead of print 10.1007/s00330-020-06817-6.</mixed-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chung</surname><given-names>M</given-names></name><name><surname>Bernheim</surname><given-names>A</given-names></name><name><surname>Mei</surname><given-names>X</given-names></name><name><surname>Zhang</surname><given-names>N</given-names></name><name><surname>Huang</surname><given-names>M</given-names></name><name><surname>Zeng</surname><given-names>X</given-names></name><etal/></person-group><article-title>CT imaging features of 2019 novel coronavirus (2019-nCoV)</article-title><source>Radiology</source><year>2020</year><volume>295</volume><issue>1</issue><fpage>202</fpage><lpage>207</lpage><pub-id pub-id-type="doi">10.1148/radiol.**********</pub-id><pub-id pub-id-type="pmid">32017661</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><mixed-citation publication-type="other">RSNA. healthcare-in-europe com, editor. CT outperforms lab diagnosis for coronavirus infection. healthcare-in-europe.com; 2020. Last accessed date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://bit.ly/3aoTQBD">https://bit.ly/3aoTQBD</ext-link>.</mixed-citation></ref><ref id="CR20"><label>20.</label><mixed-citation publication-type="other">Borges do Nascimento IJ, Cacic N, Abdulazeem HM, von Groote TC, Jayarajah U, Weerasekara I, et al. Novel coronavirus infection (COVID-19) in humans: a scoping review and meta-analysis. Journal of Clinical Medicine. 2020;9(4):941. Number: 4 Publisher: Multidisciplinary Digital Publishing Institute.</mixed-citation></ref><ref id="CR21"><label>21.</label><mixed-citation publication-type="other">Bernheim A, Mei X, Huang M, Yang Y, Fayad ZA, Zhang N, et al. Chest CT findings in coronavirus disease-19 (COVID-19): relationship to duration of infection. Radiology. 2020; p. 200463.</mixed-citation></ref><ref id="CR22"><label>22.</label><mixed-citation publication-type="other">Wang Y, Dong C, Hu Y, Li C, Ren Q, Zhang X, et al. Temporal changes of CT findings in 90 patients with COVID-19 pneumonia: a longitudinal study. Radiology. 2020; p. 200843.</mixed-citation></ref><ref id="CR23"><label>23.</label><mixed-citation publication-type="other">Shan F, Gao Y, Wang J, Shi W, Shi N, Han M, et al. Lung infection quantification of COVID-19 in CT images with deep learning. CVPR. 2020.</mixed-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mahmud</surname><given-names>M</given-names></name><name><surname>Kaiser</surname><given-names>MS</given-names></name><name><surname>Hussain</surname><given-names>A</given-names></name><name><surname>Vassanelli</surname><given-names>S</given-names></name></person-group><article-title>Applications of deep learning and reinforcement learning to biological data</article-title><source>IEEE Trans Neural Netw Learn Syst</source><year>2018</year><volume>29</volume><issue>6</issue><fpage>2063</fpage><lpage>2079</lpage><pub-id pub-id-type="doi">10.1109/TNNLS.2018.2790388</pub-id><pub-id pub-id-type="pmid">29771663</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><mixed-citation publication-type="other">Mahmud M, Kaiser MS, Hussain A. Deep learning in mining biological data. [cs, q-bio, stat]. 2020; p. 1&#x02013;36. Available from: arXiv:<ext-link ext-link-type="uri" xlink:href="http://arxiv.org/abs/2003.00108">2003.00108</ext-link>2003.00108.</mixed-citation></ref><ref id="CR26"><label>26.</label><mixed-citation publication-type="other">Ali HM, Kaiser MS, Mahmud M. Application of convolutional neural network in segmenting brain regions from MRI data. Brain Informatics. Lecture Notes in Computer Science. In: Liang P, Goel V, and Shan C, editors. Cham: Springer International Publishing; 2019. p. 136&#x02013;146.</mixed-citation></ref><ref id="CR27"><label>27.</label><mixed-citation publication-type="other">Orojo O, Tepper J, McGinnity TM, Mahmud M. A multi-recurrent network for crude oil price prediction. In: 2019 IEEE Symposium Series on Computational Intelligence (SSCI); 2019. p. 2940&#x02013;2945.</mixed-citation></ref><ref id="CR28"><label>28.</label><mixed-citation publication-type="other">Arifeen MM, et al. Hidden Markov model based trust management model for underwater wireless sensor networks. Proceedings Of The International Conference On Computing Advancements; 2020. p. 1&#x02013;5. 10.1145/3377049.3377054.</mixed-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yahaya</surname><given-names>SW</given-names></name><name><surname>Lotfi</surname><given-names>A</given-names></name><name><surname>Mahmud</surname><given-names>M</given-names></name></person-group><article-title>A consensus novelty detection ensemble approach for anomaly detection in activities of daily living</article-title><source>Appl Soft Comput</source><year>2019</year><volume>105613</volume><fpage>83</fpage></element-citation></ref><ref id="CR30"><label>30.</label><mixed-citation publication-type="other">Yahaya SW, Lotfi A, Mahmud M, Machado P, Kubota N. Gesture recognition intermediary robot for abnormality detection in human activities. In: 2019 IEEE Symposium Series on Computational Intelligence (SSCI); 2019. p. 1415&#x02013;1421.</mixed-citation></ref><ref id="CR31"><label>31.</label><mixed-citation publication-type="other">Noor MBT, Zenia NZ, Kaiser MS, Mahmud M, Al Mamun S. Detecting neurodegenerative disease from MRI: a brief review on a deep learning perspective. Brain Informatics. Lecture Notes in Computer Science. In: Liang P, Goel V, and Shan C, editors. Cham: Springer International Publishing; 2019. p. 115&#x02013;125.</mixed-citation></ref><ref id="CR32"><label>32.</label><mixed-citation publication-type="other">Miah Y, Prima CNE, Seema SJ, Mahmud M, Kaiser MS. Performance comparison of machine learning techniques in identifying dementia from open access clinical datasets. In: Proceedings of ICACIN 2020. Springer; 2020. p. 69&#x02013; 78.</mixed-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rabby</surname><given-names>G</given-names></name><name><surname>Azad</surname><given-names>S</given-names></name><name><surname>Mahmud</surname><given-names>M</given-names></name><name><surname>Zamli</surname><given-names>KZ</given-names></name><name><surname>Rahman</surname><given-names>MM</given-names></name></person-group><article-title>A flexible keyphrase extraction technique for academic literature</article-title><source>Procedia Comput Sci</source><year>2018</year><volume>135</volume><fpage>553</fpage><lpage>63</lpage><pub-id pub-id-type="doi">10.1016/j.procs.2018.08.208</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Silver</surname><given-names>D</given-names></name><etal/></person-group><article-title>Mastering the game of Go with deep neural networks and tree search</article-title><source>Nature</source><year>2016</year><volume>529</volume><issue>7587</issue><fpage>484</fpage><pub-id pub-id-type="doi">10.1038/nature16961</pub-id><pub-id pub-id-type="pmid">26819042</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bhandary</surname><given-names>A</given-names></name><name><surname>Prabhu</surname><given-names>GA</given-names></name><name><surname>Rajinikanth</surname><given-names>V</given-names></name><name><surname>Thanaraj</surname><given-names>KP</given-names></name><name><surname>Satapathy</surname><given-names>SC</given-names></name><name><surname>Robbins</surname><given-names>DE</given-names></name><etal/></person-group><article-title>Deep-learning framework to detect lung abnormality &#x02013; a study with chest X-ray and lung CT scan images</article-title><source>Pattern Recogn Lett</source><year>2020</year><volume>129</volume><fpage>271</fpage><lpage>278</lpage><pub-id pub-id-type="doi">10.1016/j.patrec.2019.11.013</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><mixed-citation publication-type="other">Pugalenthi R, Rajakumar MP, Ramya J, Rajinikanth V. Evaluation and classification of the brain tumor MRI using machine learning technique. J Control Eng Appl Inf. 2019;21(4):12&#x02013;21.</mixed-citation></ref><ref id="CR37"><label>37.</label><mixed-citation publication-type="other">Celik Y, Talo M, Yildirim O, Karabatak M, Acharya UR. Automated invasive ductal carcinoma detection based using deep transfer learning with whole-slide images. Pattern Recogn Lett. 2020;133:232&#x02013;239.</mixed-citation></ref><ref id="CR38"><label>38.</label><mixed-citation publication-type="other">Sharif M, Amin J, Raza M, Anjum MA, Afzal H, Shad SA. Brain tumor detection based on extreme learning. Neural Computing and Applications. 2020; EPub ahead of print 10.1007/s00521-019-04679-8.</mixed-citation></ref><ref id="CR39"><label>39.</label><mixed-citation publication-type="other">Amin J, Sharif M, Raza M, Mussarat Y. Detection of brain tumor based on features fusion and machine learning. Journal of Ambient Intelligence and Humanized Computing. 2018.</mixed-citation></ref><ref id="CR40"><label>40.</label><mixed-citation publication-type="other">Amin J, Sharif M, Gul N, Yasmin M, Shad SA. Brain tumor classification based on DWT fusion of MRI sequences using convolutional neural network. Pattern Recogn Lett. 2020;129:115&#x02013;122.</mixed-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sharif</surname><given-names>M</given-names></name><name><surname>Amin</surname><given-names>J</given-names></name><name><surname>Nisar</surname><given-names>MW</given-names></name><name><surname>Anjum</surname><given-names>MA</given-names></name><name><surname>Nazeer</surname><given-names>M</given-names></name><name><surname>Shad</surname><given-names>SA</given-names></name></person-group><article-title>A unified patch based method for brain tumor detection using features fusion</article-title><source>Cogn Syst Res</source><year>2020</year><volume>59</volume><fpage>273</fpage><lpage>286</lpage><pub-id pub-id-type="doi">10.1016/j.cogsys.2019.10.001</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Das</surname><given-names>A</given-names></name><name><surname>Acharya</surname><given-names>RU</given-names></name><name><surname>Panda</surname><given-names>SS</given-names></name><name><surname>Sabut</surname><given-names>SK</given-names></name></person-group><article-title>Deep learning based liver cancer detection using watershed transform and Gaussian mixture model techniques</article-title><source>Cogn Syst Res</source><year>2019</year><volume>54</volume><fpage>165</fpage><lpage>175</lpage><pub-id pub-id-type="doi">10.1016/j.cogsys.2018.12.009</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><mixed-citation publication-type="other">Wu YH, Gao SH, Mei J, Xu J, Fan DP, Zhao CW, et al. JCS: an explainable COVID-19 diagnosis system by joint classification and segmentation. [cs, eess]. 2020;p. 1&#x02013;11. . Available from: arXiv:<ext-link ext-link-type="uri" xlink:href="http://arxiv.org/abs/2004.07054">2004.07054</ext-link>.</mixed-citation></ref><ref id="CR44"><label>44.</label><mixed-citation publication-type="other">Artificial Intelligence AS. MedSeg, editor. CT Dataset for COVID-19. MedSeg; 2020. Last access date 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="http://medicalsegmentation.com/covid19/">http://medicalsegmentation.com/covid19/</ext-link>.</mixed-citation></ref><ref id="CR45"><label>45.</label><mixed-citation publication-type="other">Dey N, Rajinikanth V, Shi F, Tavares JMRS, Moraru L, Karthik KA, et al. Social-group-optimization based tumor evaluation tool for clinical brain MRI of FLAIR/diffusion-weighted modality. Biocybern Biomed Eng. 2019;39(3):843&#x02013;856.</mixed-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dey</surname><given-names>N</given-names></name><name><surname>Rajinikanth</surname><given-names>V</given-names></name><name><surname>Ashour</surname><given-names>AS</given-names></name><name><surname>Tavares</surname><given-names>JMRS</given-names></name></person-group><article-title>Social group optimization supported segmentation and evaluation of skin melanoma images</article-title><source>Symmetry</source><year>2018</year><volume>10</volume><issue>2</issue><fpage>51</fpage><pub-id pub-id-type="doi">10.3390/sym10020051</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Satapathy</surname><given-names>S</given-names></name><name><surname>Naik</surname><given-names>A</given-names></name></person-group><article-title>Social group optimization (SGO): a new population evolutionary optimization technique</article-title><source>Compl Intell Syst</source><year>2016</year><volume>2</volume><issue>3</issue><fpage>173</fpage><lpage>203</lpage><pub-id pub-id-type="doi">10.1007/s40747-016-0022-8</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><mixed-citation publication-type="other">Kowsalya N, Kalyani A, Chalcedony CJ, Sivakumar R, Janani M, Rajinikanth V. An approach to extract optic-disc from retinal image using K-means clustering. In: 2018 Fourth International Conference on Biosignals, Images and Instrumentation (ICBSII); 2018. p. 206&#x02013;212.</mixed-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tian</surname><given-names>Z</given-names></name><name><surname>Dey</surname><given-names>N</given-names></name><name><surname>Ashour</surname><given-names>AS</given-names></name><name><surname>McCauley</surname><given-names>P</given-names></name><name><surname>Shi</surname><given-names>F</given-names></name></person-group><article-title>Morphological segmenting and neighborhood pixel-based locality preserving projection on brain fMRI dataset for semantic feature extraction: an affective computing study</article-title><source>Neural Comput Appl</source><year>2018</year><volume>30</volume><issue>12</issue><fpage>3733</fpage><lpage>3748</lpage><pub-id pub-id-type="doi">10.1007/s00521-017-2955-2</pub-id></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Y</given-names></name><name><surname>Shi</surname><given-names>F</given-names></name><name><surname>Cao</surname><given-names>L</given-names></name><name><surname>Dey</surname><given-names>N</given-names></name><name><surname>Wu</surname><given-names>Q</given-names></name><name><surname>Ashour</surname><given-names>AS</given-names></name><etal/></person-group><article-title>Morphological segmentation analysis and texture-based support vector machines classification on mice liver fibrosis microscopic images</article-title><source>Curr Bioinform</source><year>2019</year><volume>14</volume><issue>4</issue><fpage>282</fpage><lpage>294</lpage><pub-id pub-id-type="doi">10.2174/1574893614666190304125221</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><mixed-citation publication-type="other">Chaki J, Dey N. Texture feature extraction techniques for image recognition, Voice In Settings. SpringerBriefs in Computational Intelligence. Springer Singapore; 2020.</mixed-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Acharya</surname><given-names>UR</given-names></name><name><surname>Fernandes</surname><given-names>SL</given-names></name><name><surname>WeiKoh</surname><given-names>JE</given-names></name><name><surname>Ciaccio</surname><given-names>EJ</given-names></name><name><surname>Fabell</surname><given-names>MKM</given-names></name><name><surname>Tanik</surname><given-names>UJ</given-names></name><etal/></person-group><article-title>Automated detection of Alzheimer&#x02019;s disease using brain MRI images&#x02013; a study with various feature extraction techniques</article-title><source>J Med Syst</source><year>2019</year><volume>43</volume><issue>9</issue><fpage>302</fpage><pub-id pub-id-type="doi">10.1007/s10916-019-1428-9</pub-id><pub-id pub-id-type="pmid">31396722</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Maheshwari</surname><given-names>S</given-names></name><name><surname>Pachori</surname><given-names>RB</given-names></name><name><surname>Acharya</surname><given-names>UR</given-names></name></person-group><article-title>Automated diagnosis of glaucoma using empirical wavelet transform and correntropy features extracted from fundus images</article-title><source>IEEE J Biomed Health Inf</source><year>2017</year><volume>21</volume><issue>3</issue><fpage>803</fpage><lpage>813</lpage><pub-id pub-id-type="doi">10.1109/JBHI.2016.2544961</pub-id></element-citation></ref><ref id="CR54"><label>54.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kala</surname><given-names>S</given-names></name><name><surname>Ezhilarasi</surname><given-names>M</given-names></name></person-group><article-title>Comparative analysis of serial and parallel fusion on texture features for improved breast cancer diagnosis</article-title><source>Curr Med Imaging Rev</source><year>2018</year><volume>14</volume><issue>6</issue><fpage>957</fpage><lpage>968</lpage><pub-id pub-id-type="doi">10.2174/1573405613666170926164625</pub-id></element-citation></ref><ref id="CR55"><label>55.</label><mixed-citation publication-type="other">Moore CM, et al. Radiopaedia, editor. COVID-19 | Radiology Reference Article | Radiopaedia.org. Radiopaedia; 2020. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://radiopaedia.org/articles/covid-19-3">https://radiopaedia.org/articles/covid-19-3</ext-link>.</mixed-citation></ref><ref id="CR56"><label>56.</label><mixed-citation publication-type="other">Bahman R. Radiopaedia, editor. Cases by R. Bahman: Radiopaedia.org rID: 74560. Radiopaedia; 2020. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://radiopaedia.org/cases/covid-19-pneumonia-3">https://radiopaedia.org/cases/covid-19-pneumonia-3</ext-link>.</mixed-citation></ref><ref id="CR57"><label>57.</label><mixed-citation publication-type="other">Hosseinabadi F. Radiopaedia, editor. Case courtesy of Dr Fateme Hosseinabadi: Radiopaedia.org rID: 74868. Radiopaedia; 2020. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://radiopaedia.org/cases/covid-19-pneumonia-8">https://radiopaedia.org/cases/covid-19-pneumonia-8</ext-link>.</mixed-citation></ref><ref id="CR58"><label>58.</label><mixed-citation publication-type="other">Smith D. Radiopaedia, editor. Case courtesy of Dr Derek Smith: Radiopaedia.org rID: 75249. Radiopaedia; 2020. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://radiopaedia.org/cases/covid-19-pneumonia-23">https://radiopaedia.org/cases/covid-19-pneumonia-23</ext-link>.</mixed-citation></ref><ref id="CR59"><label>59.</label><mixed-citation publication-type="other">Bahman R. Radiopaedia, editor. Cases by R. Bahman: Radiopaedia.org rID: 74879. Radiopaedia; 2020. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://radiopaedia.org/cases/covid-19-pneumonia-10">https://radiopaedia.org/cases/covid-19-pneumonia-10</ext-link>.</mixed-citation></ref><ref id="CR60"><label>60.</label><mixed-citation publication-type="other">Cetinoglu K. Radiopaedia, editor. Case courtesy of Dr Kenan Cetinoglu: Radiopaedia.org rID: 75281. Radiopaedia; 2020. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://radiopaedia.org/cases/covid-19-pneumonia-27">https://radiopaedia.org/cases/covid-19-pneumonia-27</ext-link>.</mixed-citation></ref><ref id="CR61"><label>61.</label><mixed-citation publication-type="other">Feger J. Radiopaedia, editor. Case courtesy of Dr Joachim Feger: Radiopaedia.org rID: 75541. Radiopaedia; 2020. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://radiopaedia.org/cases/covid-19-pneumonia-52">https://radiopaedia.org/cases/covid-19-pneumonia-52</ext-link>.</mixed-citation></ref><ref id="CR62"><label>62.</label><mixed-citation publication-type="other">TaghiNiknejad M. Radiopaedia, editor. Case 55, courtesy of Dr Mohammad TaghiNiknejad: Radiopaedia.org rID: 75606. Radiopaedia; 2018. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://radiopaedia.org/cases/covid-19-pneumonia-55">https://radiopaedia.org/cases/covid-19-pneumonia-55</ext-link>.</mixed-citation></ref><ref id="CR63"><label>63.</label><mixed-citation publication-type="other">TaghiNiknejad M. Radiopaedia, editor. Case courtesy of Dr Mohammad TaghiNiknejad: Radiopaedia.org rID: 75607. Radiopaedia; 2020. Last access date: 22-04-2020. Available from: <ext-link ext-link-type="uri" xlink:href="https://radiopaedia.org/cases/covid-19-pneumonia-56">https://radiopaedia.org/cases/covid-19-pneumonia-56</ext-link>.</mixed-citation></ref><ref id="CR64"><label>64.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Clark</surname><given-names>K</given-names></name><name><surname>Vendt</surname><given-names>B</given-names></name><name><surname>Smith</surname><given-names>K</given-names></name><name><surname>Freymann</surname><given-names>J</given-names></name><name><surname>Kirby</surname><given-names>J</given-names></name><name><surname>Koppel</surname><given-names>P</given-names></name><etal/></person-group><article-title>The Cancer Imaging Archive (TCIA): maintaining and operating a public information repository</article-title><source>J Digit Imaging</source><year>2013</year><volume>26</volume><issue>6</issue><fpage>1045</fpage><lpage>1057</lpage><pub-id pub-id-type="doi">10.1007/s10278-013-9622-7</pub-id><pub-id pub-id-type="pmid">23884657</pub-id></element-citation></ref><ref id="CR65"><label>65.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Armato</surname><given-names>SG</given-names></name><etal/></person-group><article-title>The Lung Image Database Consortium (LIDC) and Image Database Resource Initiative (IDRI): a completed reference database of lung nodules on CT scans: The LIDC/IDRI thoracic CT database of lung nodules</article-title><source>Med Phys</source><year>2011</year><volume>38</volume><issue>2</issue><fpage>915</fpage><lpage>931</lpage><pub-id pub-id-type="doi">10.1118/1.3528204</pub-id><pub-id pub-id-type="pmid">21452728</pub-id></element-citation></ref><ref id="CR66"><label>66.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhao</surname><given-names>B</given-names></name><etal/></person-group><article-title>Evaluating variability in tumor measurements from same-day repeat CT scans of patients with non-small cell lung cancer</article-title><source>Radiology</source><year>2009</year><volume>252</volume><issue>1</issue><fpage>263</fpage><lpage>272</lpage><pub-id pub-id-type="doi">10.1148/radiol.**********</pub-id><pub-id pub-id-type="pmid">19561260</pub-id></element-citation></ref><ref id="CR67"><label>67.</label><mixed-citation publication-type="other">Zhao B, Schwartz LH, Kris MG. Archive TCI, editor. Data From RIDER_Lung CT. The Cancer Imaging Archive; 2015. Last access date: 17-06-2020. Available from: 10.7937/K9/TCIA.2015.U1X8A5NR.</mixed-citation></ref></ref-list></back></article>