<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Genome Biol</journal-id><journal-id journal-id-type="iso-abbrev">Genome Biol</journal-id><journal-title-group><journal-title>Genome Biology</journal-title></journal-title-group><issn pub-type="ppub">1474-7596</issn><issn pub-type="epub">1474-760X</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">7017576</article-id><article-id pub-id-type="publisher-id">1949</article-id><article-id pub-id-type="doi">10.1186/s13059-020-1949-z</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>Robustness and applicability of transcription factor and pathway analysis tools on single-cell RNA-seq data</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Holland</surname><given-names>Christian H.</given-names></name><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Tanevski</surname><given-names>Jovan</given-names></name><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Perales-Pat&#x000f3;n</surname><given-names>Javier</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Gleixner</surname><given-names>Jan</given-names></name><xref ref-type="aff" rid="Aff4">4</xref><xref ref-type="aff" rid="Aff5">5</xref></contrib><contrib contrib-type="author"><name><surname>Kumar</surname><given-names>Manu P.</given-names></name><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Mereu</surname><given-names>Elisabetta</given-names></name><xref ref-type="aff" rid="Aff7">7</xref></contrib><contrib contrib-type="author"><name><surname>Joughin</surname><given-names>Brian A.</given-names></name><xref ref-type="aff" rid="Aff6">6</xref><xref ref-type="aff" rid="Aff8">8</xref></contrib><contrib contrib-type="author"><name><surname>Stegle</surname><given-names>Oliver</given-names></name><xref ref-type="aff" rid="Aff4">4</xref><xref ref-type="aff" rid="Aff5">5</xref><xref ref-type="aff" rid="Aff9">9</xref></contrib><contrib contrib-type="author"><name><surname>Lauffenburger</surname><given-names>Douglas A.</given-names></name><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Heyn</surname><given-names>Holger</given-names></name><xref ref-type="aff" rid="Aff7">7</xref><xref ref-type="aff" rid="Aff10">10</xref></contrib><contrib contrib-type="author"><name><surname>Szalai</surname><given-names>Bence</given-names></name><xref ref-type="aff" rid="Aff11">11</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-8552-8976</contrib-id><name><surname>Saez-Rodriguez</surname><given-names>Julio</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><aff id="Aff1"><label>1</label>Institute for Computational Biomedicine, Bioquant, Heidelberg University, Faculty of Medicine, and Heidelberg University Hospital, Heidelberg, Germany </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="GRID">grid.1957.a</institution-id><institution-id institution-id-type="ISNI">0000 0001 0728 696X</institution-id><institution>Joint Research Centre for Computational Biomedicine (JRC-COMBINE), </institution><institution>RWTH Aachen University, Faculty of Medicine, </institution></institution-wrap>Aachen, Germany </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="GRID">grid.11375.31</institution-id><institution-id institution-id-type="ISNI">0000 0001 0706 0012</institution-id><institution>Department of Knowledge Technologies, </institution><institution>Jo&#x0017e;ef Stefan Institute, </institution></institution-wrap>Ljubljana, Slovenia </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="GRID">grid.7497.d</institution-id><institution-id institution-id-type="ISNI">0000 0004 0492 0584</institution-id><institution>German Cancer Research Center (DKFZ), </institution></institution-wrap>Heidelberg, Germany </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="GRID">grid.4709.a</institution-id><institution-id institution-id-type="ISNI">0000 0004 0495 846X</institution-id><institution>European Molecular Biology Laboratory (EMBL), </institution><institution>Genome Biology Unit, </institution></institution-wrap>Heidelberg, Germany </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="GRID">grid.116068.8</institution-id><institution-id institution-id-type="ISNI">0000 0001 2341 2786</institution-id><institution>Department of Biological Engineering, </institution><institution>MIT, </institution></institution-wrap>Cambridge, MA USA </aff><aff id="Aff7"><label>7</label><institution-wrap><institution-id institution-id-type="GRID">grid.473715.3</institution-id><institution>CNAG-CRG, Centre for Genomic Regulation (CRG), </institution><institution>Barcelona Institute of Science and Technology (BIST), </institution></institution-wrap>Barcelona, Spain </aff><aff id="Aff8"><label>8</label><institution-wrap><institution-id institution-id-type="GRID">grid.116068.8</institution-id><institution-id institution-id-type="ISNI">0000 0001 2341 2786</institution-id><institution>Koch Institute for Integrative Cancer Biology, MIT, </institution></institution-wrap>Cambridge, MA USA </aff><aff id="Aff9"><label>9</label><institution-wrap><institution-id institution-id-type="GRID">grid.225360.0</institution-id><institution-id institution-id-type="ISNI">0000 0000 9709 7726</institution-id><institution>European Molecular Biology Laboratory, </institution><institution>European Bioinformatics Institute, </institution></institution-wrap>Wellcome Genome Campus, Cambridge, UK </aff><aff id="Aff10"><label>10</label><institution-wrap><institution-id institution-id-type="GRID">grid.5612.0</institution-id><institution-id institution-id-type="ISNI">0000 0001 2172 2676</institution-id><institution>Universitat Pompeu Fabra (UPF), </institution></institution-wrap>Barcelona, Spain </aff><aff id="Aff11"><label>11</label><institution-wrap><institution-id institution-id-type="GRID">grid.11804.3c</institution-id><institution-id institution-id-type="ISNI">0000 0001 0942 9821</institution-id><institution>Faculty of Medicine, Department of Physiology, </institution><institution>Semmelweis University, </institution></institution-wrap>Budapest, Hungary </aff></contrib-group><pub-date pub-type="epub"><day>12</day><month>2</month><year>2020</year></pub-date><pub-date pub-type="pmc-release"><day>12</day><month>2</month><year>2020</year></pub-date><pub-date pub-type="collection"><year>2020</year></pub-date><volume>21</volume><elocation-id>36</elocation-id><history><date date-type="received"><day>3</day><month>9</month><year>2019</year></date><date date-type="accepted"><day>29</day><month>1</month><year>2020</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2020</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Many functional analysis tools have been developed to extract functional and mechanistic insight from bulk transcriptome data. With the advent of single-cell RNA sequencing (scRNA-seq), it is in principle possible to do such an analysis for single cells. However, scRNA-seq data has characteristics such as drop-out events and low library sizes. It is thus not clear if functional TF and pathway analysis tools established for bulk sequencing can be applied to scRNA-seq in a meaningful way.</p></sec><sec><title>Results</title><p id="Par2">To address this question, we perform benchmark studies on simulated and real scRNA-seq data. We include the bulk-RNA tools PROGENy, GO enrichment, and DoRothEA that estimate pathway and transcription factor (TF) activities, respectively, and compare them against the tools SCENIC/AUCell and metaVIPER, designed for scRNA-seq. For the in silico study, we simulate single cells from TF/pathway perturbation bulk RNA-seq experiments. We complement the simulated data with real scRNA-seq data upon CRISPR-mediated knock-out. Our benchmarks on simulated and real data reveal comparable performance to the original bulk data. Additionally, we show that the TF and pathway activities preserve cell type-specific variability by analyzing a mixture sample sequenced with 13 scRNA-seq protocols. We also provide the benchmark data for further use by the community.</p></sec><sec><title>Conclusions</title><p id="Par3">Our analyses suggest that bulk-based functional analysis tools that use manually curated footprint gene sets can be applied to scRNA-seq data, partially outperforming dedicated single-cell tools. Furthermore, we find that the performance of functional analysis tools is more sensitive to the gene sets than to the statistic used.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>scRNA-seq</kwd><kwd>Functional analysis</kwd><kwd>Transcription factor analysis</kwd><kwd>Pathway analysis</kwd><kwd>Benchmark</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100002347</institution-id><institution>Bundesministerium f&#x000fc;r Bildung und Forschung</institution></institution-wrap></funding-source><award-id>FKZ: 031L0049</award-id><principal-award-recipient><name><surname>Saez-Rodriguez</surname><given-names>Julio</given-names></name></principal-award-recipient></award-group></funding-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/100000002</institution-id><institution>National Institutes of Health</institution></institution-wrap></funding-source><award-id>U54-CA217377</award-id><principal-award-recipient><name><surname>Lauffenburger</surname><given-names>Douglas A.</given-names></name></principal-award-recipient></award-group></funding-group><funding-group><award-group><funding-source><institution>Ministry of Science, Innovation and Universities</institution></funding-source><award-id>SAF2017-89109-P</award-id><principal-award-recipient><name><surname>Heyn</surname><given-names>Holger</given-names></name></principal-award-recipient></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2020</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par4">Gene expression profiles provide a blueprint of the status of cells. Thanks to diverse high-throughput techniques, such as microarrays and RNA-seq, expression profiles can be collected relatively easily and are hence very common. To extract functional and mechanistic information from these profiles, many tools have been developed that can, for example, estimate the status of molecular processes such as the activity of pathways or transcription factors (TFs). These functional analysis tools are broadly used and belong to the standard toolkit to analyze expression data [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR4">4</xref>].</p><p id="Par5">Functional analysis tools typically combine prior knowledge with a statistical method to gain functional and mechanistic insights from omics data. In the case of transcriptomics, prior knowledge is typically rendered as gene sets containing genes belonging to, e.g., the same biological process or to the same Gene Ontology (GO) annotation. The Molecular Signature Database (MSigDB) is one of the largest collections of curated and annotated gene sets [<xref ref-type="bibr" rid="CR5">5</xref>]. Statistical methods are as abundant as the different types of gene sets. Among them, the most commonly used are over-representation analysis (ORA) [<xref ref-type="bibr" rid="CR6">6</xref>] and Gene Set Enrichment Analysis (GSEA) [<xref ref-type="bibr" rid="CR7">7</xref>]. Still, there is a growing number of statistical methods spanning from simple linear models to advanced machine learning methods [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR9">9</xref>].</p><p id="Par6">Recent technological advances in single-cell RNA-seq (scRNA-seq) enable the profiling of gene expression at the individual cell level [<xref ref-type="bibr" rid="CR10">10</xref>]. Multiple technologies and protocols have been developed, and they have experienced a dramatic improvement over recent years. However, single-cell data sets have a number of limitations and biases, including low library size and drop-outs. Bulk RNA-seq tools that focus on cell type identification and characterization as well as on inferring regulatory networks can be readily applied to scRNA-seq data [<xref ref-type="bibr" rid="CR11">11</xref>]. This suggests that functional analysis tools should in principle be applicable to scRNA-seq data as well. However, it has not been investigated yet whether these limitations could distort and confound the results, rendering the tools not applicable to single-cell data.</p><p id="Par7">In this paper, we benchmarked the robustness and applicability of various TF and pathway analysis tools on simulated and real scRNA-seq data. We focused on three tools for bulk and three tools for scRNA-seq data. The bulk tools were PROGENy [<xref ref-type="bibr" rid="CR12">12</xref>], DoRothEA [<xref ref-type="bibr" rid="CR13">13</xref>], and classical GO enrichment analysis, combining GO gene sets [<xref ref-type="bibr" rid="CR14">14</xref>] with GSEA. PROGENy estimates the activity of 14 signaling pathways by combining corresponding gene sets with a linear model. DoRothEA is a collection of resources of TF&#x02019;s targets (regulons) that can serve as gene sets for TF activity inference. For this study, we coupled DoRothEA with the method VIPER [<xref ref-type="bibr" rid="CR15">15</xref>] as it incorporates the mode of regulation of each TF-target interaction. Both PROGENy&#x02019;s and DoRothEA&#x02019;s gene sets are based on observing the transcriptomic consequences (the &#x0201c;footprint&#x0201d;) of the processes of interest rather than the genes composing the process as gene sets [<xref ref-type="bibr" rid="CR16">16</xref>]. This approach has been shown to be more accurate and informative in inferring the process&#x02019;s activity [<xref ref-type="bibr" rid="CR12">12</xref>, <xref ref-type="bibr" rid="CR17">17</xref>]. The tools specifically designed for application on scRNA-seq data that we considered are SCENIC/AUCell [<xref ref-type="bibr" rid="CR18">18</xref>] and metaVIPER [<xref ref-type="bibr" rid="CR19">19</xref>]. SCENIC is a computational workflow that comprises the construction of gene regulatory networks (GRNs) from scRNA-seq data that are subsequently interrogated to infer TF activity with the statistical method AUCell. In addition, we coupled AUCell with the footprint-based gene sets from DoRothEA and PROGENy that we hereafter refer to as D-AUCell and P-AUCell. Using DoRothEA with both VIPER and AUCell on scRNA-seq for TF activity inference allowed us to compare the underlying statistical methods more objectively. metaVIPER is an extension of VIPER which is based on the same statistical method but relies on multiple GRNs such as tissue-specific networks.</p><p id="Par8">We first benchmarked the tools on simulated single-cell transcriptome profiles. We found that on this in silico data the footprint-based gene sets from DoRothEA and PROGENy can functionally characterize simulated single cells. We observed that the performance of the different tools is dependent on the used statistical method and properties of the data, such as library size. We then used real scRNA-seq data upon CRISPR-mediated knock-out/knock-down of TFs [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR21">21</xref>] to assess the performance of TF analysis tools. The results of this benchmark further supported our finding that TF analysis tools can provide accurate mechanistic insights into single cells. Finally, we demonstrated the utility of the tools for pathway and TF activity estimation on recently published data profiling a complex sample with 13 different scRNA-seq technologies [<xref ref-type="bibr" rid="CR22">22</xref>]. Here, we showed that summarizing gene expression into TF and pathway activities preserves cell-type-specific information and leads to biologically interpretable results. Collectively, our results suggest that the bulk- and footprint-based TF and pathway analysis tools DoRothEA and PROGENy partially outperform the single-cell tools SCENIC, AUCell, and metaVIPER. Although on scRNA-seq data DoRothEA and PROGENy were less accurate than on bulk RNA-seq, we were still able to extract relevant functional insight from scRNA-seq data.</p></sec><sec id="Sec2"><title>Results</title><sec id="Sec3"><title>Robustness of bulk-based TF and pathway analysis tools against low gene coverage</title><p id="Par9">Single-cell RNA-seq profiling is hampered by low gene coverage due to drop-out events [<xref ref-type="bibr" rid="CR23">23</xref>]. In our first analysis, we focused solely on the low gene coverage aspect and whether tools designed for bulk RNA-seq can deal with it. Specifically, we aimed to explore how DoRothEA, PROGENy, and GO gene sets combined with GSEA (GO-GSEA) can handle low gene coverage in general, independently of other technical artifacts and characteristics from scRNA-seq protocols. Thus, we conducted this benchmark using bulk transcriptome benchmark data. In these studies, single TFs and pathways are perturbed experimentally, and the transcriptome profile is measured before and after the perturbation. These experiments can be used to benchmark tools for TF/pathway activity estimation, as they should estimate correctly the change in the perturbed TF or pathway. The use of these datasets allowed us to systematically control the gene coverage (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). The workflow consisted of four steps (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S1a). In the first step, we summarized all perturbation experiments into a matrix of contrasts (with genes in rows and contrasts in columns) by differential gene expression analysis. Subsequently, we randomly replaced, independently for each contrast, logFC values with 0 so that we obtain a predefined number of &#x0201c;covered&#x0201d; genes with a logFC unequal to zero. Accordingly, a gene with a logFC equal to 0 was considered as missing/not covered. Then, we applied DoRothEA, PROGENy, and GO-GSEA to the contrast matrix, subsetted only to those experiments which are suitable for the corresponding tool: TF perturbation for DoRothEA and pathway perturbation for PROGENy and GO-GSEA. We finally evaluate the global performance of the methods with receiver operating characteristic (ROC) and precision-recall (PR) curves (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). This process was repeated 25 times to account for stochasticity effects during inserting zeros in the contrast matrix (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section).</p><p id="Par10">DoRothEA&#x02019;s TFs are accompanied by an empirical confidence level indicating the confidence in their regulons, ranging from A (most confident) to E (less confident; see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). For this benchmark, we included only TFs with confidence levels A and B (denoted as DoRothEA (AB)) as this combination has a reasonable tradeoff between TF coverage and performance [<xref ref-type="bibr" rid="CR13">13</xref>]. In general, the performance of DoRothEA dropped as gene coverage decreased. While it showed reasonable prediction power with all available genes (AUROC of 0.690), it approached almost the performance of a random model (AUROC of 0.5) when only 500 genes were covered (mean AUROC of 0.547, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>a, and similar trend with AUPRC, Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S1b).
<fig id="Fig1"><label>Fig. 1</label><caption><p>Testing the robustness of DoRothEA (AB), PROGENy, and GO-GSEA against low gene coverage. <bold>a</bold> DoRothEA (AB) performance (area under ROC curve, AUROC) versus gene coverage. <bold>b</bold> PROGENy performance (AUROC) for different number of footprint genes per pathway versus gene coverage. <bold>c</bold> Performance (AUROC) of GO-GSEA versus gene coverage. The dashed line indicates the performance of a random model. The colors in <bold>a</bold> and <bold>c</bold> are meant only as a visual support to distinguish between the individual violin plots and jittered points</p></caption><graphic xlink:href="13059_2020_1949_Fig1_HTML" id="MO1"/></fig></p><p id="Par11">We next benchmarked pathway activities estimated by PROGENy and GO-GSEA. In the original PROGENy framework, 100 footprint genes are used per pathway to compute pathway activities by default, as it has been shown that this leads to the best performance on bulk samples [<xref ref-type="bibr" rid="CR12">12</xref>]. However, one can extend the footprint size to cover more genes of the expression profiles. We reasoned that this might counteract low gene coverage and implemented accordingly different PROGENy versions (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). With the default PROGENy version (100 footprint genes per pathway), we observed a clear drop in the global performance with decreasing gene coverage, even though less drastic than for DoRothEA (from AUROC of 0.724 to 0.636, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>b, similar trends with AUPRC, Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S1c). As expected, PROGENy performed the best with 100 footprint genes per pathway when there is complete gene coverage. The performance differences between the various PROGENy versions shrank with decreasing gene coverage. This suggests that increasing the number of footprint genes can help to counteract low gene coverage. To provide a fair comparison between PROGENy and GO-GSEA, we used only those 14 GO terms that match the 14 PROGENy pathways (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S1d). In general, GO-GSEA showed weaker performance than PROGENy. The decrease in performance was more prominent as gene coverage decreased (from AUROC of 0.662 to 0.525, Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>c, and similar trend with AUPRC, Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S1e). With a gene coverage of less than 2000 genes, GO-GSEA performance was no better than random.</p><p id="Par12">As our benchmark data set comprises multiple perturbation experiments per pathway, we also evaluated the performance of PROGENy and GO-GSEA at the pathway level (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S2a and b). The pathway-wise evaluation supported our finding that PROGENy outperforms GO-GSEA across all gene coverages, but the performance between pathways is variable.</p><p id="Par13">In summary, this first benchmark provided insight into the general robustness of the bulk-based tools DoRothEA, PROGENy, and GO-GSEA with respect to low gene coverage. DoRothEA performed reasonably well down to a gene coverage of 2000 genes. The performance of all different PROGENy versions was robust across the entire gene coverage range tested. GO-GSEA showed a worse performance than PROGENy, especially in the low gene coverage range. Since DoRothEA and PROGENy showed promising performance in low gene coverage ranges, we decided to explore them on scRNA-seq data. Due to its poor performance, we did not include GO-GSEA in the subsequent analyses.</p></sec><sec id="Sec4"><title>Benchmark on simulated single-cell RNA-seq data</title><p id="Par14">For the following analyses, we expanded the set of tools with the statistical methods AUCell that we decoupled from the SCENIC workflow [<xref ref-type="bibr" rid="CR18">18</xref>] and metaVIPER [<xref ref-type="bibr" rid="CR19">19</xref>]. Both methods were developed specifically for scRNA-seq analysis and thus allow the comparison of bulk vs single-cell based tools on scRNA-seq data. AUCell is a statistical method that is originally used with GRNs constructed by SCENIC and assesses whether gene sets are enriched in the top quantile of a ranked gene signature (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). In this study, we combined AUCell with DoRothEA&#x02019;s and PROGENy&#x02019;s gene sets (referred to as D-AUCell and P-AUCell, respectively). metaVIPER is an extension of VIPER and requires multiple gene regulatory networks instead of a single network. In our study, we coupled 27 tissue-specific gene regulatory networks with metaVIPER, which provides a single TF consensus activity score estimated across all networks (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). To benchmark all these methods on single cells, ideally, we would have scRNA-seq datasets after perturbations of TFs and pathways. However, these datasets, especially for pathways, are currently very rare. To perform a comprehensive benchmark study, we developed a strategy to simulate samples of single cells using bulk RNA-seq samples from TF and pathway perturbation experiments.</p><p id="Par15">A major cause of drop-outs in single-cell experiments is the abundance of transcripts in the process of reverse-transcription of mRNA to cDNA [<xref ref-type="bibr" rid="CR23">23</xref>]. Thus, our simulation strategy was based on the assumption that genes with low expression are more likely to result in drop-out events.</p><p id="Par16">The simulation workflow started by transforming read counts of a single bulk RNA-seq sample to transcripts per million (TPM), normalizing for gene length and library size. Subsequently, for each gene, we assigned a sampling probability by dividing the individual TPM values with the sum of all TPM values. These probabilities are proportional to the likelihood for a given gene not to &#x0201c;drop-out&#x0201d; when simulating a single cell from the bulk sample. We determined the total number of gene counts for a simulated single cell by sampling from a normal distribution with a mean equal to the desired library size which is specified as the first parameter of the simulation. We refer hereafter to this number as the library size. For every single cell, we then sampled with replacement genes from the gene probability vector up to the determined library size. The frequency of occurrence of individual genes becomes the new gene count in the single cell. The number of simulated single cells from a single bulk sample can be specified as the second parameter of the simulation. Of note, this parameter is not meant to reflect a realistic number of cells, but it is rather used to investigate the loss of information: the lower the number of simulated cells, the more information is lost from the original bulk sample (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>a; see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). This simple workflow guaranteed that the information of the original bulk perturbation is preserved and scRNA-seq characteristics, such as drop-outs, low library size, and a high number of samples/cells are introduced.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Benchmark results of TF and pathway analysis tools on simulated scRNA-seq data. <bold>a</bold> Simulation strategy of single cells from an RNA-seq bulk sample. <bold>b</bold> Example workflow of DoRothEA&#x02019;s performance evaluation on simulated single cells for a specific parameter combination (number of cells&#x02009;=&#x02009;10, mean library size&#x02009;=&#x02009;5000). 1. Step: ROC-curves of DoRothEA&#x02019;s performance on single cells (25 replicates) and on bulk data including only TFs with confidence level A. 2. Step: DoRothEA performance on single cells and bulk data summarized as AUROC vs TF coverage. TF coverage denotes the number of distinct perturbed TFs in the benchmark dataset that are also covered by the gene set resource (see Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S3a) Results are provided for different combinations of DoRothEA&#x02019;s confidence levels (A, B, C, D, E). Error bars of AUROC values depict the standard deviation and correspond to different simulation replicates. Step 3: Averaged difference across all confidence level combinations between AUROC of single cells and bulk data for all possible parameter combinations. The letters within the tiles indicates which confidence level combination performs the best on single cells. The tile marked in red corresponds to the parameter setting used for previous plots (Steps 1 and 2). <bold>c</bold> D-AUCell and <bold>d</bold> metaVIPER performance&#x000a0;on simulated single cells summarized as AUROC for a specific parameter combination (number of cells&#x02009;=&#x02009;10, mean library size&#x02009;=&#x02009;5000) and corresponding bulk data vs TF coverage. <bold>e</bold>, <bold>f</bold> Performance results of <bold>e</bold> PROGENy and <bold>f</bold> P-AUCell on simulated single cells&#x000a0;for a specific parameter combination (number of cells&#x02009;=&#x02009;10, mean library size&#x02009;=&#x02009;5000) and corresponding bulk data&#x000a0;in ROC space vs number of footprint genes per pathway. <bold>c</bold>&#x02013;<bold>f</bold> Plots revealing the change in performance for all possible parameter combinations (Step 3) are available in Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S7. <bold>b</bold>&#x02013;<bold>f</bold> The dashed line indicates the performance of a random model</p></caption><graphic xlink:href="13059_2020_1949_Fig2_HTML" id="MO2"/></fig></p><p id="Par17">Our bulk RNA-seq samples comprised 97 single TF perturbation experiments targeting 52 distinct TFs and 15 single pathway perturbation experiments targeting 7 distinct pathways (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S3a and b; see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). We repeated the simulation of single cells from each bulk sample template to account for the stochasticity of the simulation procedure. We tested our simulation strategy by comparing the characteristics of the simulated cells to real single cells. In this respect, we compared the count distribution (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S4a), the relationship of mean and variance of gene expression (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S4b), and the relationship of library size to the number of detected genes (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S4c). These comparisons suggested that our simulated single cells closely resemble real single cells and are thus suitable for benchmarking.</p><p id="Par18">Unlike in our first benchmark, we applied the TF and pathway analysis tools directly on single samples/cells and built the contrasts between perturbed and control samples at the level of pathway and TF activities (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). We compared the performance of all tools to recover the perturbed TFs/pathways. We also considered the performance on the template bulk data, especially for the bulk-based tools DoRothEA and PROGENy, as a baseline for comparison to their respective performance on the single-cell data.</p><p id="Par19">We show, as an example, the workflow of the performance evaluation for DoRothEA (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b, 1. Step). As a first step, we applied DoRothEA to single cells generated for one specific parameter combination and bulk samples, performed differential activity analysis (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section), and evaluated the performance with ROC and PR curves including only TFs with confidence level A. In this example, we set the number of cells to 10 as this reflects an observable loss of information of the original bulk sample and the mean library size to 5000 as this corresponds to a very low but still realistic sequencing depths of scRNA-seq experiments. Each repetition of the simulation is depicted by an individual ROC curve, which shows the variance in the performance of DoRothEA on simulated single-cell data (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b, 1. Step). The variance decreases as the library size and the number of cells increase (which holds true for all tested tools, Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S5a&#x02013;e). The shown ROC curves are summarized into a single AUROC value for bulk and mean AUROC value for single cells. We performed this procedure also for different TF confidence level combinations and show the performance change in these values in relation to the number of distinct perturbed TFs in the benchmark that are also covered by the gene set resources that we refer to as TF coverage (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b, 2. Step). For both bulk and single cells, we observe a tradeoff between TF coverage and performance caused by including different TF confidence level combinations in the benchmark. This result is supported by both AUROC and AUPRC (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S6a) and corresponds to our previous findings [<xref ref-type="bibr" rid="CR13">13</xref>]. The performance of DoRothEA on single cells does not reach the performance on bulk, though it can still recover TF perturbations on the simulated single cells reasonably well. This is especially evident for the most confident TFs (AUROC of 0.690 for confidence level A and 0.682 for the confidence level combination AB). Finally, we explore the effect of the simulation parameters library size and the number of cells on the performance by performing the previously described analysis for all combinations of library sizes and cell numbers. We computed the mean difference between AUROC scores of single-cell and bulk data across all confidence level combinations. A negative difference indicates that the tool of interest performs overall better on bulk data than on scRNA-seq data, and a positive difference that it performs better on scRNA-seq. We observed a gradually decreasing negative difference approaching 0 when the size of the library and the number of cells increase (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>b, 3. Step, and Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S7a). Note, however, that the number of cells and thus the amount of lost information of the original bulk sample has a stronger impact on the performance than the mean library size. In addition, we identified the best performing combination of DoRothEA&#x02019;s TF confidence levels for different library sizes and the number of single cells. Thus, the results can be used as recommendations for choosing the confidence levels on data from an experiment with comparable characteristics in terms of sequencing depths.</p><p id="Par20">Similarly to DoRothEA, we also observed for D-AUCell a tradeoff between TF coverage and performance on both single cells and bulk samples when using the same parameter combination as before (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>c, similar trend with AUPRC Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S6b). The summarized performance across all confidence level combinations of D-AUCell on single cells slightly outperformed its performance on bulk samples (AUROC of 0.601 on single cells and 0.597 on bulk). This trend becomes more evident with increasing library size and the number of cells (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S7b).</p><p id="Par21">For the benchmark of metaVIPER, we assigned confidence levels to the tissue-specific GTEx regulons based on DoRothEA&#x02019;s gene set classification. This was done for consistency with DoRothEA and D-AUCell, even if there is no difference in confidence among them. Hence, for metaVIPER, we do not observe a tradeoff between TF coverage and performance (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>d, similar trend with AUPRC Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S6c). As opposed to D-AUCell, metaVIPER performed clearly better on single cells than on bulk samples across all confidence level combinations (AUROC of 0.584 on single cells and 0.531 on bulk). This trend increased with increasing library size and number of cells (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S7c). However, the overall performance of metaVIPER is worse than the performance of DoRothEA and D-AUCell. In summary, the bulk-based tool DoRothEA performed the best on the simulated single cells followed by D-AUCell. metaVIPER performed slightly better than a random model.</p><p id="Par22">For the benchmark of pathway analysis tools, we observed that PROGENy performed well across different number of footprint genes per pathway, with a peak at 500 footprint genes for both single cells and bulk (AUROC of 0.856 for bulk and 0.831 for single cells, Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>e, similar trend with AUPRC Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S6d). A better performance for single-cell analysis with more than 100 footprint genes per pathway is in agreement with the previous general robustness study that suggested that a higher number of footprint genes can counteract low gene coverage. Similarly to the benchmark of TF analysis tools, we studied the effect of the simulation parameters on the performance of pathway analysis tools. We averaged for each parameter combination the performance difference between single cells and bulk across the different versions of PROGENy. For the parameter combination associated with Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>e (number of cells&#x02009;=&#x02009;10, mean library size&#x02009;=&#x02009;5000), the average distance is negative showing that the performance of PROGENy on bulk was, in general, better than on single-cell data. Increasing the library size and the number of cells improved the performance of PROGENy on single cells reaching almost the same performance as on bulk samples (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S7d). For most parameter combinations, PROGENy with 500 or 1000 footprint genes per pathway yields the best performance.</p><p id="Par23">For P-AUCell, we observed a different pattern than for PROGENy as it worked best with 100 footprint genes per pathway for both single cells and bulk (AUROC of 0.788 for bulk and 0.712 for single cells, Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>f, similar trends with AUPRC Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S6e). Similar to PROGENy, increasing the library size and the number of cells improved the performance, but not to the extent of its performance on bulk (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S7e). For most parameter combinations, P-AUCell with 100 or 200 footprint genes per pathway yielded the best performance.</p><p id="Par24">In summary, both PROGENy and P-AUCell performed well on the simulated single cells, and PROGENy performed slightly better. For pathway analysis, P-AUCell did not perform better on scRNA-seq than on bulk data. We then went on to perform a benchmark analysis on real scRNA-seq datasets.</p></sec><sec id="Sec5"><title>Benchmark on real single-cell RNA-seq data</title><p id="Par25">After showing that the footprint-based gene sets from DoRothEA and PROGENy can handle low gene coverage and work reasonably well on simulated scRNA-seq data with different statistical methods, we performed a benchmark on real scRNA-seq data. However, single-cell transcriptome profiles of TF and pathway perturbations are very rare. To our knowledge, there are no datasets of pathway perturbations on single-cell level comprehensive enough for a robust benchmark of pathway analysis tools. For tools inferring TF activities, the situation is better: recent studies combined CRISPR knock-outs/knock-down of TFs with scRNA-seq technologies [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR21">21</xref>] that can serve as potential benchmark data.</p><p id="Par26">The first dataset is based on the Perturb-seq technology, which contains 26 knock-out perturbations targeting 10 distinct TFs after 7 and 13&#x02009;days of perturbations (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S8a) [<xref ref-type="bibr" rid="CR20">20</xref>]. To explore the effect of perturbation time, we divided the dataset into two sub-datasets based on perturbation duration (Perturb-seq (7d) and Perturb-seq (13d)). The second dataset is based on CRISPRi protocol and contains 141 perturbation experiments targeting 50 distinct TFs [<xref ref-type="bibr" rid="CR21">21</xref>] (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S8a). The datasets showed a variation in terms of drop-out rate, the number of cells, and sequencing depths (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S8b).</p><p id="Par27">To exclude bad or unsuccessful perturbations in the case of CRISPRi experiments, we discarded experiments when the logFC of the targeted gene/TF was greater than 0 (12 out of 141, Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S8c). This quality control is important only in the case of CRISPRi, as it works on the transcriptional level. Perturb-seq (CRISPR knock-out) acts on the genomic level, so we cannot expect a clear relationship between KO efficacy and transcript level of the target. Note that the logFCs of both Perturb-seq sub-datasets are in a narrower range in comparison to the logFCs of the CRISPRi dataset (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S8d). The perturbation experiments that passed this quality check were used in the following analyses.</p><p id="Par28">We also considered the SCENIC framework for TF analysis [<xref ref-type="bibr" rid="CR18">18</xref>]. We inferred GRNs for each sub-dataset using this framework (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). We set out to evaluate the performance of DoRothEA, D-AUCell, metaVIPER, and SCENIC on each benchmark dataset individually.</p><p id="Par29">To perform a fair comparison among the tools, we pruned their gene set resources to the same set of TFs. However, the number of TFs in the dataset-specific SCENIC networks was very low (109 for Perturb-Seq (7d), 126 for Perturb-Seq (13d), and 182 TFs for CRISPRi), yielding a low overlap with the other gene set resources. Therefore, only a small fraction of the benchmark dataset was usable yielding low TF coverage. Nevertheless, we found that DoRothEA performed the best on the Perturb-seq (7d) dataset (AUROC of 0.752, Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>a) followed by D-AUCell and SCENIC with almost identical performance (AUROC of 0.629 and 0.631, respectively). metaVIPER performed just slightly better than a random model (AUROC of 0.533). Interestingly, all tools performed poorly on the Perturb-seq (13d) dataset. In the CRISPRi dataset, DoRothEA and D-AUCell performed the best with D-AUCell showing slightly better performance than DoRothEA (AUROC of 0.626 for D-AUCell and 0.608 for DoRothEA). SCENIC and metaVIPER performed slightly better than a random model. Given that we included in this analysis only shared TFs across all gene set resources, we covered only 5 and 17 distinct TFs of the Perturb-seq and CRISPRi benchmark dataset.
<fig id="Fig3"><label>Fig. 3</label><caption><p>Benchmark results of TF analysis tools on real scRNA-seq data. <bold>a</bold> Performance of DoRothEA, D-AUCell, metaVIPER, and SCENIC on all sub benchmark datasets in ROC space vs TF coverage. <bold>b</bold> Performance of DoRothEA, D-AUCell, and metaVIPER on all sub benchmark datasets in ROC vs TF coverage split up by combinations of DoRothEA&#x02019;s confidence levels (A-E). <bold>a</bold>, <bold>b</bold> In both panels, the results for each tool are based on the same but for the respective panel different set of (shared) TFs. TF coverage reflects the number of distinct perturbed TFs in the benchmark data set that are also covered by the gene sets</p></caption><graphic xlink:href="13059_2020_1949_Fig3_HTML" id="MO3"/></fig></p><p id="Par30">To make better use of the benchmark dataset, we repeated the analysis without SCENIC, which resulted in a higher number of shared TFs among the gene set resources and a higher TF coverage. The higher TF coverage allowed us to investigate the performance of the tools in terms of DoRothEA&#x02019;s confidence level. For both Perturb-seq datasets, we found consistent results with the previous study when the TF coverage increased from 5 to 10 (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>b). However, for the CRISPRi dataset, the performance of DoRothEA and metaVIPER remained comparable to the previous study while the performance of D-AUCell dropped remarkably. These trends can also be observed in PR-space (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S8e).</p><p id="Par31">In summary, these analyses suggested that the tools DoRothEA and D-AUCell, both interrogating the manually curated, high-quality regulons from DoRothEA, are the best-performing tools to recover TF perturbation at the single-cell level of real data.</p></sec><sec id="Sec6"><title>Application of TF and pathway analysis tools on samples of heterogeneous cell type populations (PBMC+HEK293T)</title><p id="Par32">In our last analysis, we wanted to test the performance of all tested tools in a more heterogeneous system that would illustrate a typical scRNA-seq data analysis scenario where multiple cell types are present. We used a dataset from the Human Cell Atlas project [<xref ref-type="bibr" rid="CR24">24</xref>] that contains scRNA-seq profiles of human peripheral blood mononuclear cells (PBMCs) and HEK 293&#x02009;T cell line with annotated cell types [<xref ref-type="bibr" rid="CR22">22</xref>]. This dataset was analyzed with 13 different scRNA-seq protocols (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). In this study, no ground truth (in contrast to the previous perturbation experiments) for TF and pathway activities was available. To evaluate the performance of all tools, we assessed the potential of TF and pathway activities to cluster cells from the same cell type together based on a priori annotated cell types. All pathway analysis tools and the TF analysis tools DoRothEA, D-AUCell, and metaVIPER were readily applicable to the dataset, except for SCENIC, where we first had to infer GRNs specific for each dataset (and thus experimental protocol) from the respective data (e.g., Drop-seq regulons inferred from the Drop-seq dataset; see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). The overlap of all protocol-specific SCENIC regulons comprised only 24 TFs (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S9a). Including regulons from DoRothEA and GTEx shrank the total overlap down to 20 (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S9b). In contrast, high-quality regulons (confidence levels A and B) from DoRothEA and GTEx alone overlapped in 113 TFs. Given the very low regulon overlap between DoRothEA, GTEx, and all protocol-specific SCENIC regulons, we decided to subset DoRothEA and GTEx to their shared TFs while using all available TFs of the protocol-specific SCENIC regulons.</p><p id="Par33">The low overlap of the SCENIC regulons motivated us to investigate the direct functional consequences of their usage. Theoretically, one would expect to retrieve highly similar regulons as they were constructed from the same biological context. We calculated the pairwise (Pearson) correlations of TF activities between the scRNA-seq&#x000a0;technologies for each tool. The distribution of correlation coefficients for each tool denotes the consistency of predicted TF activity across the protocols (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S10). The tools DoRothEA, D-AUCell, and metaVIPER had all a similar median Pearson correlation coefficient of ~&#x02009;0.63 and SCENIC of 0.34. This suggests that the predicted TF activities via SCENIC networks are less consistent across the protocols than the TF activities predicted via DoRothEA, D-AUCell, and metaVIPER.</p><p id="Par34">To assess the clustering capacity of TF and pathway activities, we performed our analysis for each scRNA-seq technology separately to identify protocol-specific and protocol-independent trends. We assumed that the cell-type-specific information should be preserved also on the reduced dimension space of TF and pathway activities if these meaningfully capture the corresponding functional processes. Hence, we assessed how well the individual clusters correspond to the annotated cell types by a two-step approach. First, we applied UMAP on different input matrices, e.g., TF/pathway activities or gene expression, and then we evaluated how well cells from the same cell type cluster together. We considered silhouette widths as a metric of cluster purity (see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). Intuitively, each cell type should form a distinct cluster. However, some cell types are closely related, such as different T cells (CD4 and CD8) or monocytes (CD14+ and FCGR3A+). Thus, we decided to evaluate the cluster purity at different levels of the cell-type hierarchy from fine-grained to coarse-grained. We started with the hierarchy level 0 where every cell type forms a distinct cluster and ended with the hierarchy level 4 where all PBMC cell types and the HEK cell line form a distinct cluster (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>a). Our main findings rely on hierarchy level 2.
<fig id="Fig4"><label>Fig. 4</label><caption><p>Application of TF and pathway analysis tools on a representative scRNA-seq dataset of PBMCs and HEK cells. <bold>a</bold> Dendrogram showing how cell lines/cell types are clustered together based on different hierarchy levels. The dashed line marks the hierarchy level 2, where CD4 T cells, CD8 T cells, and NK cells are aggregated into a single cluster. Similarly, CD14+ monocytes, FCGR3A+ monocytes, and dendritic cells are also aggregated to a single cluster. The B cells and HEK cells are represented by separate, pure clusters. <bold>b</bold>, <bold>d</bold> Comparison of cluster purity (clusters are defined by hierarchy level 2) between the top 2000 highly variable genes and <bold>b</bold> TF activity and TF expression and <bold>d</bold> pathway activities. The dashed line in <bold>b</bold> separates SCENIC as it is not directly comparable to the other TF analysis tools and controls due to a different number of considered TFs. <bold>c</bold> UMAP plots of TF activities calculated with DoRothEA and corresponding TF expression measured by SMART-Seq2 protocol. <bold>e</bold> Heatmap of selected TF activities inferred with DoRothEA from gene expression data generated via Quartz-Seq2</p></caption><graphic xlink:href="13059_2020_1949_Fig4_HTML" id="MO4"/></fig></p><p id="Par35">Silhouette widths derived from a set of highly variable genes (HVGs) set the baseline for the silhouette widths derived from pathway/TF activities. We identified the top 2000 HVGs with Seurat [<xref ref-type="bibr" rid="CR25">25</xref>] using the selection method &#x0201c;vst&#x0201d; as it worked the best in our hands at four out of five hierarchy levels (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S11). For both TF and pathway activity matrices, the number of features available for dimensionality reduction using UMAP was substantially less (113 TFs for DoRothEA/metaVIPER, up to 400 TFs for SCENIC GRNs and 14 pathways, respectively) than for a gene expression matrix containing the top 2000 HVGs. As the number of available features for dimensionality reduction is different between HVGs, TFs, and pathways, we compare the cluster purity among these input features, to a positive and negative control. The positive control is a gene expression matrix with the top <italic>n</italic> HVGs and the negative control is a gene expression matrix with randomly chosen <italic>n</italic> HVGs out of the 2000 HVGs (<italic>n</italic> equals 14 for pathway analysis and 113 for TF analysis). It should be noted that in terms of TF analysis, the positive and negative control is only applicable to DoRothEA, D-AUCell, and metaVIPER as they share the same number of features. As the protocol-specific SCENIC GRNs differ in size (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S9a), each network would require its own positive and negative control.</p><p id="Par36">To evaluate the performance of the TF activity inference methods and the utility of TF activity scores, we determined the cluster purity derived from TF activities predicted by DoRothEA, D-AUCell, metaVIPER, and SCENIC, TF expression, and positive and negative controls. scRNA-seq protocols and input matrices used for dimensionality reduction affected cluster purity significantly (two-way ANOVA <italic>p</italic> values &#x0003c;&#x02009;2.2e&#x02212;16 and 4.32e&#x02212;12, respectively, <italic>p</italic> values and estimations for corresponding linear model coefficients in Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S12a; see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). The cluster purity based on TF activities inferred using DoRothEA and D-AUCell did not differ significantly (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>b, corresponding plots for all hierarchy levels in Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S12b). In addition, the cluster purity of both tools was not significantly worse than the purity based on all 2000 HVGs, though we observed a slight trend indicating a better cluster purity based on HVGs. This trend is expected due to the large difference in available features for dimensionality reduction. Instead, a comparison to the positive and negative controls is more appropriate. Both DoRothEA and D-AUCell performed comparably to the positive control but significantly better than the negative control across all scRNA-seq protocols (TukeyHSD post-hoc-test, adj. <italic>p</italic> value of 1.26e&#x02212;4 for DoRothEA and 7.09e&#x02212;4 for D-AUCell). The cluster purity derived from metaVIPER was significantly worse than for DoRothEA (TukeyHSD post-hoc-test, adj. <italic>p</italic> value of 0.054) and tend to be worse than D-AUCell (TukeyHSD post-hoc-test, adj. <italic>p</italic> value of 0.163) as well. metaVIPER was not significantly better than the negative control. The cluster purity from SCENIC was significantly better than the negative control (TukeyHSD post-hoc-test, adj. <italic>p</italic> value of 1.11e&#x02212;6) and comparable to the positive control and thus to DoRothEA and D-AUCell. However, as mentioned above, SCENIC is only partially comparable to the controls and other tools due to the different number of TFs.</p><p id="Par37">Regardless of the underlying TF activity tool, except for metaVIPER, the cluster purity derived from TF activities outperformed significantly the purity derived from TF expression (TukeyHSD post-hoc-test, adj. <italic>p</italic> value of 5.89e&#x02212;6 for DoRothEA, 3.85&#x02212;e5 for D-AUCell, and 4.0e&#x02212;8 for SCENIC). This underlines the advantage and relevance of using TF activities over the expression of the TF itself (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>c). With a comparable performance to a similar number of HVG and also to 2000 HVGs, we concluded that TF activities serve&#x02014;independently of the underlying scRNA-seq protocol&#x02014;as a complementary approach for cluster analysis that is based on generally more interpretable cell type marker.</p><p id="Par38">To evaluate the performance of pathway inference methods and the utility of pathway activity scores, we determined cluster purity with pathway matrices generated by different PROGENy&#x000a0;versions and P-AUCell. We used 200 and 500 footprint genes per pathway for PROGENy and P-AUCell, respectively, since they provided the best performance in the previous analyses. As observed already for the TF analysis tools, scRNA-seq protocols and matrices used for dimensionality reduction affected cluster purity significantly (two-way ANOVA <italic>p</italic> values of 2.84e&#x02212;7 and 1.13e&#x02212;13, respectively, <italic>p</italic> values and estimations for corresponding linear model coefficients in Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S13a; see the &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d; section). The cluster purity derived from pathway activity matrices is not significantly different between PROGENy and P-AUCell, while worse than all HVGs (TukeyHSD post-hoc-test, adj. <italic>p</italic> value of 4.07e&#x02212;10 for PROGENy and 4.59e&#x02212;9 for P-AUCell, Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>d, corresponding plots for all hierarchy levels in Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S13b). This is expected due to the large difference in the number of available features for dimensionality reduction (2000 HVGs vs 14 pathways). The cluster purity of both approaches was comparable to the positive control but significantly better than the negative control (TukeyHSD post-hoc-test, adj. <italic>p</italic> value of 0.077 for PROGENy and 0.013 for P-AUCell vs negative control). In summary, this study indicated that the pathway activities contain relevant and cell-type-specific information, even though they do not capture enough functional differences to be used for effective clustering analysis. Overall, the cluster purity of cells represented by the estimated pathway activities is worse than the cluster purity of cells represented by the estimated TF activities.</p><p id="Par39">In addition, we observed that TF and pathway matrices derived from Quartz-Seq2 protocol yielded for hierarchy level 2 in significantly better cluster purity than all other protocols, which is in agreement with the original study of the PBMC + HEK293T data (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S12a and S13a) [<xref ref-type="bibr" rid="CR22">22</xref>].</p><p id="Par40">TF and pathway activity scores are more interpretable than the expression of single genes. Hence, we were interested to explore whether we could recover known cell-type-specific TF and pathway activities from the PBMC data. We decided to focus on the dataset measured with Quartz-Seq2 as this protocol showed in our and in the original study superior performance over all other protocols [<xref ref-type="bibr" rid="CR22">22</xref>]. We calculated mean TF and pathway activity scores for each cell type using DoRothEA, D-AUCell, metaVIPER, and SCENIC (using only TFs with confidence levels A and B, Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4</xref>e and Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S14a&#x02013;c, respectively), PROGENy with 500 and P-AUCell with 200 footprint genes per pathway (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S14d and e). In terms of TF activities, we observed high RFXAP, RFXANK, and RFX5 activity (TFs responsible for MHCII expression) in monocytes, dendritic cells, and B cells (the main antigen-presenting cells of the investigated population [<xref ref-type="bibr" rid="CR26">26</xref>]) (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S14a and b). Myeloid lineage-specific SPI1 activity [<xref ref-type="bibr" rid="CR27">27</xref>] was observed in monocytes and dendritic cells. The high activity of repressor TF (where regulation directionality is important) FOXP1 in T lymphocytes [<xref ref-type="bibr" rid="CR28">28</xref>] was only revealed by DoRothEA. Proliferative TFs like Myc and E2F4 had also high activity in HEK cells.</p><p id="Par41">Regarding pathway activities, we observed across both methods, in agreement with the literature, high activity of NFkB and TNFa in monocytes [<xref ref-type="bibr" rid="CR29">29</xref>] and elevated Trail pathway activity in B cells (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S14d and e) [<xref ref-type="bibr" rid="CR30">30</xref>]. HEK cells, as expected from dividing cell lines, had higher activity of proliferative pathways (MAPK, EGFR, and PI3K, Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S14d). These later pathway activity changes were only detected by PROGENy but not with AUCell, highlighting the importance of directionality information.</p><p id="Par42">Besides these individual examples, we analyzed the biological relevance of the identified TF activities in more detail. We assumed that the highly active TFs are regulating important cellular functions, resulting in a correlation between TF activity and essentiality. As (to our knowledge) no gene essentiality data is available for PBMCs, we used hematologic cancer (lymphoma and leukemia) gene essentiality data from the DepMap project [<xref ref-type="bibr" rid="CR31">31</xref>]. We compared the difference between the TF activities in lymphoid (B, T, and NK cells) and myeloid (monocytes and dendritic cells) PBMCs with the TF gene essentiality differences between myeloid and lymphoid hematologic cancers. SPI1, according to its higher activity in myeloid PBMCs, was more essential in myeloid leukemias (Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S15a and b, Wilcoxon-test <italic>p</italic> value&#x02009;=&#x02009;0.038). For a more comprehensive analysis, we compared the differences in TF activity (PBMCs, lymphoid - myeloid) and the differences in TF gene essentiality (hematologic cancers, lymphoid - myeloid) by calculating their Pearson correlation for all TFs. The TF activities predicted by DoRothEA correlated best with respective essentiality scores across all scRNA-seq protocols (median Pearson correlation coefficient of 0.107; 0.08 for D-AUCell; 0.04 for metaVIPER; and &#x02212;&#x02009;0.002 for SCENIC, Additional&#x000a0;file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>: Figure S15c). The difference in TF activities predicted with DoRothEA from the dataset generated by Smart-Seq2 and Quartz-Seq2 correlated significantly with the difference in essentiality (Pearson correlation, <italic>p</italic> value of 0.049 and 0.032, respectively). Thus, TF activities predicted with DoRothEA regulons correlate, albeit, weakly with gene/TF essentiality.</p><p id="Par43">In summary, the analysis of this mixture sample demonstrated that summarizing gene expression into TF activities can preserve cell type-specific information while drastically reducing the number of features. Hence, TF activities could be considered as an alternative to gene expression for clustering analysis. Furthermore, they correlate, albeit weakly, with gene/TF essentiality, suggesting the biological relevance of the identified cell-type-specific TF activities.</p><p id="Par44">We also showed that pathway activity matrices contain cell-type-specific information, too, although we do not recommend using them for clustering analysis as the number of features is too low. In addition, we recovered known pathway/TF cell-type associations showing the importance of directionality and supporting the utility and power of the functional analysis tools DoRothEA and PROGENy.</p></sec></sec><sec id="Sec7"><title>Discussion</title><p id="Par45">In this paper, we tested the robustness and applicability of functional analysis tools on scRNA-seq data. We included both bulk- and single-cell-based tools that estimate either TF or pathway activities from gene expression data and for which well-defined benchmark data exist. The bulk-based tools were DoRothEA, PROGENy, and GO gene sets analyzed with GSEA (GO-GSEA). The functional analysis tools specifically designed for the application in single cells were SCENIC, AUCell combined with DoRothEA (D-AUCell) and PROGENy (P-AUCell) gene sets, and metaVIPER.</p><p id="Par46">We first explored the effect of low gene coverage in bulk data on the performance of the bulk-based tools DoRothEA, PROGENy, and GO-GSEA. We found that for all tools the performance dropped with decreasing gene coverage but at a different rate. While PROGENy was robust down to 500 covered genes, DoRothEA&#x02019;s performance dropped markedly at 2000 covered genes. In addition, the results related to PROGENy suggested that increasing the number of footprint genes per pathway counteracted low gene coverage. GO-GSEA showed the strongest drop and did not perform better than a random guess below 2000 covered genes. Comparing the global performance across all pathways of both pathway analysis tools suggests that footprint-based gene sets are superior over gene sets containing pathway members (e.g., GO gene sets) in recovering perturbed pathways. This observation is in agreement with previous studies conducted by us and others [<xref ref-type="bibr" rid="CR12">12</xref>, <xref ref-type="bibr" rid="CR32">32</xref>]. However, both PROGENy and GO-GSEA performed poorly for some pathways, e.g., WNT pathway. We reason that this observation might be due to the quality of the corresponding benchmark data [<xref ref-type="bibr" rid="CR33">33</xref>]. Given this fact and that GO-GSEA cannot handle low gene coverage (in our hands), we concluded that this approach is not suitable for scRNA-seq analysis. Hence, we decided to focus only on PROGENy as bulk-based pathway analysis tool for the following analyses.</p><p id="Par47">Afterward, we benchmarked DoRothEA, PROGENy, D-AUCell, P-AUCell, and metaVIPER on simulated single cells that we sampled from bulk pathway/TF perturbation samples. We showed that our simulated single cells possess characteristics comparable to real single-cell data, supporting the relevance of this strategy. Different combinations of simulation parameters can be related to different scRNA-seq technologies. For each combination, we provide a recommendation of how to use DoRothEA&#x02019;s and PROGENy&#x02019;s gene sets (in terms of confidence level combination or number of footprint genes per pathway) to yield the best performance. It should be noted that our simulation approach, as it is now, allows only the simulation of a homogenous cell population. This would correspond to a single cell experiment where the transcriptome of a cell line is profiled. In future work, this simulation strategy could be adapted to account for a heterogeneous dataset that would resemble more realistic single-cell datasets [<xref ref-type="bibr" rid="CR34">34</xref>, <xref ref-type="bibr" rid="CR35">35</xref>].</p><p id="Par48">In terms of TF activity inference, DoRothEA performed best on the simulated single cells followed by D-AUCell and then metaVIPER. Both DoRothEA and D-AUCell shared DoRothEA&#x02019;s gene set collection but applied different statistics. Thus, we concluded that, in our data, VIPER is more suitable to analyze scRNA-seq data than AUCell. The tool metaVIPER performed only slightly better than a random model, and since it uses VIPER like DoRothEA, the weak performance must be caused by the selection of the gene set resource. DoRothEA&#x02019;s gene sets/TF regulons were constructed by integrating different types of evidence spanning from literature curated to predicted TF-target interactions. For metaVIPER, we used 27 tissue-specific GRNs constructed in a data-driven manner with ARACNe [<xref ref-type="bibr" rid="CR36">36</xref>] thus containing only predicted TF-target interactions. The finding that especially the high-confidence TF regulons from DoRothEA outperform pure ARACNe regulons is in agreement with previous observations [<xref ref-type="bibr" rid="CR13">13</xref>, <xref ref-type="bibr" rid="CR37">37</xref>] and emphasizes the importance of combining literature curated resources with in silico predicted resources. Moreover, we hypothesize based on the pairwise comparison that for functional analysis, the choice of gene sets is of higher relevance than the choice of the underlying statistical method.</p><p id="Par49">As one could expect, the single-cell tools D-AUCell metaVIPER performed better on single cells than on the original bulk samples. This trend becomes more pronounced with increasing library size and number of cells. However, the bulk-based tools performed even better on the simulated single cells than the scRNA specific tools.</p><p id="Par50">Related to pathway analysis, both PROGENy and P-AUCell performed well on the simulated single cells. The original framework of PROGENy uses a linear model that incorporates individual weights of the footprint genes, denoting the importance and also the sign of the contribution (positive/negative) to the pathway activity score. Those weights cannot be considered when applying AUCell with PROGENy gene sets. The slightly higher performance of PROGENy suggests that individual weights assigned to gene set members can improve the activity estimation of biological processes.</p><p id="Par51">Subsequently, we aimed to validate the functional analysis tools on real single-cell data. While we could not find suitable benchmark data of pathway perturbations, we exploited two independent datasets of TF perturbations to benchmark the TF analysis tools which we extended with SCENIC. These datasets combined CRISPR-mediated TF knock-out/knock-down (Perturb-Seq and CRISPRi) with scRNA-seq. It should be noted that pooled screenings of gene knock-outs with Perturb-seq suffer from an often faulty assignment of guide-RNA and single-cell [<xref ref-type="bibr" rid="CR38">38</xref>]. Those mislabeled data confound the benchmark as the ground-truth is not reliable. In addition, our definition of true-positives and true-negatives is commonly used for such analyses [<xref ref-type="bibr" rid="CR4">4</xref>, <xref ref-type="bibr" rid="CR13">13</xref>, <xref ref-type="bibr" rid="CR37">37</xref>], but it might be incorrect due to indirect and compensatory mechanisms [<xref ref-type="bibr" rid="CR39">39</xref>]. These phenomena can confound the results of this type of benchmarks.</p><p id="Par52">Nevertheless, we showed that DoRothEA&#x02019;s gene sets were globally effective in inferring TF activity from single-cell data with varying performance dependent on the used statistical method. As already shown in the in silico benchmark, D-AUCell showed a weaker performance than DoRothEA, supporting that VIPER performs better than AUCell. Interestingly, metaVIPER&#x02019;s performance was no better than random across all datasets. metaVIPER used the same statistical method as DoRothEA but different gene set resources. This further supports our hypothesis that the selection of gene sets is more important than the statistical method for functional analysis. This trend is also apparent when comparing the performance of SCENIC and D-AUCell as both rely on the statistical method AUCell but differ in their gene set resource. SCENICs&#x02019; performance was consistently weaker than D-AUCell. In addition, we found that the gene regulatory networks inferred with the SCENIC workflow covered only a limited number of TFs in comparison to the relatively comprehensive regulons from DoRothEA or GTEx.</p><p id="Par53">Furthermore, the perturbation time had a profound effect on the performance of the tools: while DoRothEA and D-AUCell worked well for a perturbation duration of 6 (CRISPRi) and 7&#x02009;days (Perturb-Seq (7d)), the performance dropped markedly for 13&#x02009;days. We reasoned that, within 13&#x02009;days of perturbation, compensation effects are taking place at the molecular level that confound the prediction of TF activities. In addition, it is possible that cells without a gene edit outgrow cells with a successful knock-out after 13&#x02009;days as the knock-out typically yield in a lower fitness and thus proliferation rate.</p><p id="Par54">In summary, DoRothEA subsetted to confidence levels A and B performed the best on real scRNA-seq data but at the cost of the TF coverage. The results of the in silico and in vitro benchmark are in agreement. Accordingly, we believe that it is reasonable to assume that also PROGENy works on real data given the positive benchmark results on simulated data.</p><p id="Par55">Finally, we applied our tools of interest to a mixture sample of PBMCs and HEK cells profiled with 13 different scRNA-seq protocols. We investigated to which extent pathway and TF matrices retain cell-type-specific information, by evaluating how well cells belonging to the same cell type or cell type family cluster together in reduced dimensionality space. Given the lower numbers of features available for dimensionality reduction using TF and pathway activities, cell types could be recovered equally well as when using the same number of the top highly variable genes. In addition, we showed that cell types could be recovered more precisely using TF activities than TF expression, which is in agreement with previous studies [<xref ref-type="bibr" rid="CR19">19</xref>]. This suggests that summarizing gene expression as TF and pathway activities can lead to noise filtering, particularly relevant for scRNA-seq data, though TF activities performed better than pathway activities which is again attributed to the even lower number of pathways. Specifically, TF activities computed with DoRothEA, D-AUCell, and SCENIC yielded a reasonable cluster purity. It should be noted that, while DoRothEA and D-AUCell rely on independent regulons, the SCENIC networks are constructed from the same dataset they are applied to. This poses the risk of overfitting. Across technologies, the TF activities from SCENIC correlated less well than those calculated with the other tools, which is consistent with overfitting by SCENIC, but further analysis is required.</p><p id="Par56">Our analysis suggested at different points that the performance of TF and pathway analysis tools is more sensitive to the selection of gene sets than the statistical methods. In particular, manually curated footprint gene sets seem to perform generally better. This hypothesis could be tested in the future by decoupling functional analysis tools into gene sets and statistics. Benchmarking all possible combinations of gene sets and statistics (i.e., DoRothEA gene sets with a linear model or PROGENy gene sets with VIPER) would shed light on this question which we believe is of high relevance for the community.</p></sec><sec id="Sec8"><title>Conclusions</title><p id="Par57">Our systematic and comprehensive benchmark study suggests that functional analysis tools that rely on manually curated footprint gene sets are effective in inferring TF and pathway activity from scRNA-seq data, partially outperforming tools specifically designed for scRNA-seq analysis. In particular, the performance of DoRothEA and PROGENy was consistently better than all other tools. We showed the limits of both tools with respect to low gene coverage. We also provided recommendations on how to use DoRothEA&#x02019;s and PROGENy&#x02019;s gene sets in the best way dependent on the number of cells, reflecting the amount of available information, and sequencing depths. Furthermore, we showed that TF and pathway activities are rich in cell-type-specific information with a reduced amount of noise and provide an intuitive way of interpretation and hypothesis generation. We provide our benchmark data and code to the community for further assessment of methods for functional analysis.</p></sec><sec id="Sec9"><title>Methods</title><sec id="Sec10"><title>Functional analysis tools, gene set resources, and statistical methods</title><sec id="Sec11"><title>PROGENy</title><p id="Par58">PROGENy is a tool that infers pathway activity for 14 signaling pathways (Androgen, Estrogen, EGFR, Hypoxia, JAK-STAT, MAPK, NFkB, PI3K, p53, TGFb, TNFa, Trail, VEGF, and WNT) from gene expression data [<xref ref-type="bibr" rid="CR12">12</xref>, <xref ref-type="bibr" rid="CR33">33</xref>]. By default pathway activity inference is based on gene sets comprising the top 100 most responsive genes upon corresponding pathway perturbation, which we refer to as footprint genes of a pathway. Each footprint gene is assigned a weight denoting the strength and direction of regulation upon pathway perturbation. Pathway scores are computed by a weighted sum of the product from expression and the weight of footprint genes.</p></sec><sec id="Sec12"><title>DoRothEA</title><p id="Par59">DoRothEA is a gene set resource containing signed transcription factor (TF)-target interactions [<xref ref-type="bibr" rid="CR13">13</xref>]. Those interactions were curated and collected from different types of evidence such as literature curated resources, ChIP-seq peaks, TF binding site motifs, and interactions inferred directly from gene expression. Based on the number of supporting evidence, each interaction is accompanied by an interaction confidence level ranging from A to E, with A being the most confidence interactions and E the least. In addition, a summary TF confidence level is assigned (also from A to E) which is derived from the leading confidence level of its interactions (e.g., a TF is assigned confidence level A if at least ten targets have confidence level A as well). DoRothEA contains in total 470,711 interactions covering 1396 TFs targeting 20,238 unique genes. We use VIPER in combination with DoRothEA to estimate TF activities from gene expression data, as described in [<xref ref-type="bibr" rid="CR13">13</xref>].</p></sec><sec id="Sec13"><title>GO-GSEA</title><p id="Par60">We define GO-GSEA as an analysis tool that couples GO-terms from MsigDB with the GSEA framework [<xref ref-type="bibr" rid="CR7">7</xref>].</p></sec><sec id="Sec14"><title>VIPER</title><p id="Par61">VIPER is a statistical framework that was developed to estimate protein activity from gene expression data using enriched regulon analysis performed by the algorithm aREA [<xref ref-type="bibr" rid="CR15">15</xref>]. It requires information about interactions (if possible signed) between a protein and its transcriptional targets and the likelihood of their interaction. If not further specified, this likelihood is set to 1. In the original workflow, this regulatory network was inferred from gene expression by the algorithm ARACNe providing mode of regulation and likelihood for each interaction [<xref ref-type="bibr" rid="CR36">36</xref>]. However, it can be replaced by any other data resource reporting protein target interactions.</p></sec><sec id="Sec15"><title>metaVIPER</title><p id="Par62">metaVIPER is an extension of VIPER that uses multiple gene regulatory networks [<xref ref-type="bibr" rid="CR19">19</xref>]. TF activities predicted with each individual gene regulatory network are finally integrated to a consensus TF activity score.</p></sec><sec id="Sec16"><title>SCENIC</title><p id="Par63">SCENIC is a computational workflow that predicts TF activities from scRNA-seq data [<xref ref-type="bibr" rid="CR18">18</xref>]. Instead of interrogating predefined regulons, individual regulons are constructed from the scRNA-seq data. First TF-gene co-expression modules are defined in a data-driven manner with GENIE3. Subsequently, those modules are refined via RcisTarget by keeping only those genes than contain the respective transcription factor binding motif. Once the regulons are constructed, the method AUCell scores individual cells by assessing for each TF separately whether target genes are enriched in the top quantile of the cell signature.</p></sec><sec id="Sec17"><title>D-AUCell/P-AUCell</title><p id="Par64">The statistical method AUCell is not limited to SCENIC regulons. In principle, it can be combined with any gene set resources. Thus, we coupled AUCell with gene sets from DoRothEA (D-AUCell) and PROGENy (P-AUCell). In comparison to other statistical methods, AUCell does not include weights of the gene set members. Thus, the mode of regulation or the likelihood of TF-target interactions or weights of the PROGENy gene sets are not considered for the computation of TF and pathway activities.</p></sec></sec><sec id="Sec18"><title>Application of PROGENy on single samples/cells and contrasts</title><p id="Par65">We applied PROGENy on matrices of single samples (genes in rows and either bulk samples or single cells in columns) containing normalized gene expression scores or on contrast matrices (genes in rows and summarized perturbation experiments into contrasts in columns) containing logFCs. In the case of single sample analysis, the contrasts were built based on pathway activity matrices yielding the change in pathway activity (perturbed samples - control sample) summarized as logFC. Independent of the input matrix, we scaled each pathway to have a mean activity of 0 and a standard deviation of 1. We build different PROGENy versions by varying the number of footprint genes per pathway (100, 200, 300, 500, 1000 or all which corresponds to ~&#x02009;29,000 genes).</p></sec><sec id="Sec19"><title>Application of DoRothEA on single samples/cells and contrasts</title><p id="Par66">We applied DoRothEA in combination with the statistical method VIPER on matrices of single samples (genes in rows and either bulk samples or single cells in columns) containing normalized gene expression scores scaled gene-wise to a mean value of 0 and standard deviation of 1 or on contrast matrices (genes in rows and summarized perturbation experiments into contrasts in columns) containing logFCs. In the case of single sample analysis, the contrasts were built based on TF activity matrices yielding the change in TF activity (perturbed samples - control sample) summarized as logFC. TFs with less than four targets listed in the corresponding gene expression matrix were discarded from the analysis. VIPER provides a normalized enrichment score (NES) for each TF which we consider as a metric for the activity. We used the R package <italic>viper</italic> (version 1.17.0) [<xref ref-type="bibr" rid="CR15">15</xref>] to run VIPER in combination with DoRothEA.</p></sec><sec id="Sec20"><title>Application of GO-GSEA sets on contrasts</title><p id="Par67">We applied GSEA with GO&#x000a0;gene sets on contrast matrices (genes in rows and summarized perturbation experiments into contrasts in columns) containing logFCs that serve also as gene-level statistic. We selected only those GO terms which map to PROGENy pathways in order to guarantee a fair comparison between both tools. For the enrichment analysis, we used the R package <italic>fgsea</italic> (version 1.10.0) [<xref ref-type="bibr" rid="CR40">40</xref>] with 1000 permutations per gene signature.</p></sec><sec id="Sec21"><title>Application of metaVIPER on single samples</title><p id="Par68">We ran metaVIPER with 27 tissue-specific gene regulatory networks which we constructed before for one of our previous studies [<xref ref-type="bibr" rid="CR13">13</xref>]. Those tissue-specific gene regulatory networks were derived using ARACNe [<xref ref-type="bibr" rid="CR36">36</xref>] taking the database GTEx [<xref ref-type="bibr" rid="CR41">41</xref>] as tissue-specific gene expression sample resource. We applied metaVIPER on matrices of single samples (genes in rows and single cells in columns) containing normalized gene expression scores scaled gene-wise to a mean value of 0 and a standard deviation of 1. If required, contrasts were built based on TF activity matrices yielding the change in TF activity (perturbed samples - control sample) summarized as logFC. TFs with less than four targets listed in the corresponding input matrix were discarded from the analysis. metaVIPER provides a NES integrated across all regulatory networks for each TF which we consider as a metric for the activity. We used the R package <italic>viper</italic> (version 1.17.0) [<xref ref-type="bibr" rid="CR15">15</xref>] to run metaVIPER.</p></sec><sec id="Sec22"><title>Application of AUCell with either SCENIC, DoRothEA, or PROGENy gene sets on single samples</title><p id="Par69">AUCell is a statistical method to determine specifically for single cells whether a given gene set is enriched at the top quantile of a ranked gene signature. Therefore, AUCell determines the area under the recovery curve to compute the enrichment score. We defined the top quantile as the top 5% of the ranked gene signature. We applied this method coupled with SCENIC, PROGENy, and DoRothEA gene sets. Before applying this method with PROGENy gene sets, we subsetted the footprint gene sets to contain only genes available in the provided gene signature. This guarantees a fair comparison as for the original PROGENy framework with a linear model, the intersection of footprint (gene set) members and signature genes are considered. We applied AUCell with SCENIC, PROGENy, and DoRothEA gene sets on matrices of single samples (genes in rows and single cells in columns) containing raw gene counts. Contrasts were built based on respective TF/pathway activity matrices yielding the change in TF/pathway activity (perturbed samples - control sample) summarized as logFC. For the AUCell analysis, we used the R package <italic>AUCell</italic> (version 1.5.5) [<xref ref-type="bibr" rid="CR18">18</xref>].</p></sec><sec id="Sec23"><title>Induction of artificial low gene coverage in bulk microarray data</title><p id="Par70">We induce the reduction of gene coverage by inserting zeros on the contrast level. In detail, we insert for each contrast separately randomly zeros until we obtained a predefined number of genes with a logFC unequal zero which we consider as &#x0201c;covered&#x0201d;/&#x0201c;measured&#x0201d; genes. We perform this analysis for a gene coverage of 500, 1000, 2000, 3000, 5000, 7000, 8000 and as reference all available genes. To account for stochasticity effects during inserting randomly zero, we repeat this analysis 25 times for each gene coverage value.</p></sec><sec id="Sec24"><title>Simulation of single cells</title><p id="Par71">Let C be a vector representing counts per gene for a single bulk sample. C is normalized for gene length and library size resulting in vector B containing TPM values per gene. We assume that samples are obtained from homogenous cell populations and that the probability of a dropout event is inversely&#x000a0;proportional to the relative TPM of each measured gene in the bulk sample. Therefore, we define a discrete cumulative distribution function from the vector of gene frequencies <inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ P=\frac{B}{\left|B\right|} $$\end{document}</tex-math><mml:math id="M2" display="inline"><mml:mi>P</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mi>B</mml:mi><mml:mfenced close="|" open="|"><mml:mi>B</mml:mi></mml:mfenced></mml:mfrac></mml:math><inline-graphic xlink:href="13059_2020_1949_Article_IEq1.gif"/></alternatives></inline-formula>. To simulate a single cell from this distribution, we draw and aggregate L samples by inverse transform sampling. L corresponds to the library size for the count vector of the simulated single cell. We draw L from a normal distribution <inline-formula id="IEq2"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ N\left(\mu, \frac{\mu }{2}\right) $$\end{document}</tex-math><mml:math id="M4" display="inline"><mml:mi>N</mml:mi><mml:mfenced close=")" open="(" separators=","><mml:mi>&#x003bc;</mml:mi><mml:mfrac><mml:mi>&#x003bc;</mml:mi><mml:mn>2</mml:mn></mml:mfrac></mml:mfenced></mml:math><inline-graphic xlink:href="13059_2020_1949_Article_IEq2.gif"/></alternatives></inline-formula>.</p><p id="Par72">To benchmark the robustness of the methods, we vary the number of cells sampled from a single bulk sample (1, 10, 20, 30, 50, 100) and the value of &#x003bc; (1000, 2000, 5000, 10.000, 20.000). To account for stochasticity effects during sampling, we repeat this analysis 25 times for each parameter combination.</p><p id="Par73">Prior to normalization, we discarded cells with a library size lower than 100. We normalized the count matrices of the simulated cells by using the R package <italic>scran</italic> (version 1.11.27) [<xref ref-type="bibr" rid="CR42">42</xref>]. Contrast matrices were constructed by comparing cells originating from one of the perturbation bulk samples vs cells originating from one of the control bulk samples.</p></sec><sec id="Sec25"><title>Gene regulatory network (GRN) reconstruction using SCENIC</title><p id="Par74">We infer GRNs on individual sub-datasets using the SCENIC (v. 1.1.2-2) workflow [<xref ref-type="bibr" rid="CR18">18</xref>]. In brief, gene expression was filtered using default parameters and log2-transformed for co-expression analysis following the recommendations by the authors. We identified potential targets of transcription factors (TFs) based on their co-expression to TFs using GENIE3 (v. 1.6.0, Random Forest with 1000 trees). We pruned co-expression modules to retrieve only putative direct-binding interactions using RcisTarget (v. 1.4.0) and the cis-regulatory DNA-motif databases for hg38 human genome assembly (Version 9 - mc9nr, with distances TSS+/&#x02212;&#x02009;10kbp and 500bpUp100Dw, from <ext-link ext-link-type="uri" xlink:href="https://resources.aertslab.org/cistarget/">https://resources.aertslab.org/cistarget/</ext-link>) with default parameters. Only modules with a significant motif enrichment of the TF upstream were kept for the final GRN. While we were running the workflow, 75 genes out of 27,091 from the first DNA-motif database (TSS+/&#x02212;&#x02009;10kbp) were inconsistent, i.e., were not described in the second one (500bpUp100Dw), leading to an error of the workflow execution. Thus, these 75 genes were discarded from the database to complete the workflow.</p></sec><sec id="Sec26"><title>Benchmarking process with ROC and PR metrics</title><p id="Par75">To transform the benchmark into a binary setup, all activity scores of experiments with negative perturbation effect (inhibition/knockdown) are multiplied by &#x02212;1. This guarantees that TFs/pathways belong to a binary class either deregulated or not regulated and that the perturbed pathway/TF has in the ideal case the highest activity.</p><p id="Par76">We performed the ROC and PR analysis with the R package <italic>yardstick</italic> (version 0.0.3; <ext-link ext-link-type="uri" xlink:href="https://github.com/tidymodels/yardstick">https://github.com/tidymodels/yardstick</ext-link>). For the construction of ROC and PR curves, we calculated for each perturbation experiment pathway (or TF) activities. As each perturbation experiment targets either a single pathway (or TF), only the activity score of the perturbed pathway (or TF) is associated with the positive class (e.g., EGFR pathway activity score in an experiment where EGFR was perturbed). Accordingly, the activity scores of all non-perturbed pathways (or TFs) belong to the negative class (e.g., EGFR pathway activity score in an experiment where the JAK-STAT pathway was perturbed). Using these positive and negative classes, Sensitivity/(1-Specificity) or Precision/Recall values were calculated at different thresholds of activity, producing the ROC/PR curves.</p></sec><sec id="Sec27"><title>Collecting, curating, and processing of transcriptomic data</title><sec id="Sec28"><title>General robustness study</title><p id="Par77">We extracted single-pathway and single-TF perturbation data profiled with microarrays from a previous study conducted by us [<xref ref-type="bibr" rid="CR33">33</xref>]. We followed the same procedure of collection, curating, and processing the data as described in the previous study.</p></sec><sec id="Sec29"><title>In silico benchmark</title><p id="Par78">For the simulation of single cells, we collected, curated, and processed single TF and single pathway perturbation data profiled with bulk RNA-seq. We downloaded basic metadata of single TF perturbation experiments from the ChEA3 web-server (<ext-link ext-link-type="uri" xlink:href="https://amp.pharm.mssm.edu/chea3/">https://amp.pharm.mssm.edu/chea3/</ext-link>) [<xref ref-type="bibr" rid="CR37">37</xref>] and refined the experiment and sample annotation (Additional&#x000a0;file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>). Metadata of single pathway perturbation experiments were manually extracted by us from Gene Expression Omnibus (GEO) [<xref ref-type="bibr" rid="CR43">43</xref>] (Additional&#x000a0;file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>). Count matrices for all those experiments were downloaded from ARCHS<sup>4</sup> (<ext-link ext-link-type="uri" xlink:href="https://amp.pharm.mssm.edu/archs4/">https://amp.pharm.mssm.edu/archs4/</ext-link>) [<xref ref-type="bibr" rid="CR44">44</xref>].</p><p id="Par79">We normalized count matrices by first calculating normalization factors and second transforming count data to log2 counts per million (CPM) using the R packages <italic>edgeR</italic> (version 3.25.8) [<xref ref-type="bibr" rid="CR45">45</xref>] and <italic>limma</italic> (version 3.39.18) [<xref ref-type="bibr" rid="CR46">46</xref>], respectively.</p></sec><sec id="Sec30"><title>In vitro benchmark</title><p id="Par80">To benchmark VIPER on real single-cell data, we inspected related literature and identified two publications which systematically measure the effects of transcription factors on gene expression in single cells:</p><p id="Par81">Dixit et al. introduced Perturb-seq and measured the knockout-effects of ten transcription factors on K562 cells 7 and 13&#x02009;days after transduction [<xref ref-type="bibr" rid="CR20">20</xref>]. We downloaded the expression data from GEO (GSM2396858 and GSM2396859) and sgRNA-cell mappings made available by the author upon request in the files promoters_concat_all.csv (for GSM2396858) and pt2_concat_all.csv (for GSM2396859) on <ext-link ext-link-type="uri" xlink:href="http://github.com/asncd/MIMOSCA">github.com/asncd/MIMOSCA</ext-link>. We did not consider the High MOI dataset due to the expected high number of duplicate sgRNA assignments. Cells were quality filtered based on expression, keeping the upper half of cells for each dataset. Only sgRNAs detected in at least 30 cells were used. For the day 7 dataset, 16,507, and for day 13 dataset, 9634 cells remained for benchmarking.</p><p id="Par82">Ryan et al. measured knockdown effects of 50 transcription factors implicated in human definitive endoderm differentiation using a CRISPRi variant of CROPseq in human embryonic stem cells 6&#x02009;days after transduction [<xref ref-type="bibr" rid="CR21">21</xref>]. We obtained data of both replicates from GEO (GSM3630200, GSM3630201), which include sgRNA counts next to the rest of the transcription. We refrained from using the targeted sequencing of the sgRNA in GSM3630202, GSM3630203 as it contained less clear mappings due to amplification noise. Expression data lacked information on mitochondrial genes, and therefore, no further quality filtering of cells was performed. From this dataset, only sgRNAs detected in at least 100 cells were used. A combined 5282 cells remained for benchmarking.</p><p id="Par83">Analysis was limited to the 10,000 most expressed genes for all three datasets.</p><p id="Par84">We normalized the count matrices for each individual dataset (Perturb-Seq (7d), Perturb-Seq (13d), and CRISPRi) separately by using the R package <italic>scran</italic> (version 1.11.27) [<xref ref-type="bibr" rid="CR42">42</xref>].</p></sec><sec id="Sec31"><title>Human Cell Atlas study</title><p id="Par85">This scRNA-seq dataset originates from a benchmark study of the Human Cell Atlas project and is available on GEO (GSE133549) [<xref ref-type="bibr" rid="CR22">22</xref>]. The dataset consists of PBMCs and a HEK293T sample which was analyzed with 13 different scRNA-seq technologies (CEL-Seq2, MARS-Seq, Quartz-Seq2, gmcSCRB-Seq, ddSEQ, ICELL8, C1HT-Small, C1HT-Medium, Chromium, Chromium(sn), Drop-seq, inDrop). Most cells are annotated with a specific cell type/cell line (CD4 T cells, CD8 T cells, NK cells, B cells, CD14+ monocytes, FCGR3A+ monocytes, dendritic cells, megakaryocytes, HEK cells). Megakaryocytes (due to their low abundance) and cells without annotation were discarded from this analysis.</p><p id="Par86">We normalized the count matrices for each technology separately by using the R package <italic>scran</italic> (version 1.11.27) [<xref ref-type="bibr" rid="CR42">42</xref>].</p></sec></sec><sec id="Sec32"><title>Dimensionality reduction with UMAP and assessment of cluster purity</title><p id="Par87">We used the R package <italic>umap</italic> (version 0.2.0.0) calling the Python implementation of Uniform Manifold Approximation and Projection (UMAP) with the argument &#x0201c;method = &#x02018;umap-learn&#x02019;&#x0201d; to perform dimensionality reduction on various input matrices (gene expression matrix, pathway/TF activity matrix, etc.). We assume that the dimensionality reduction will result in clustering of cells that corresponds well to the cell type/cell type family. To assess the validity of this assumption, we assigned a cell-type/cell family-specific cluster-id to each point in the low-dimensional space. We then defined a global cluster purity measure based on silhouette widths [<xref ref-type="bibr" rid="CR47">47</xref>], which is a well-known clustering quality measure.</p><p id="Par88">Given the cluster assignments, in the low-dimensional space, for each cell, the average distance (<italic>a</italic>) to the cells that belong to the same cluster is calculated. Then, the smallest average distance (<italic>b</italic>) to all cells belonging to the newest foreign cluster is calculated. The difference, between the latter and the former, indicates the width of the silhouette for that cell, i.e., how well the cell is embedded in the assigned cluster. To make the silhouette widths comparable, they are normalized by dividing the difference with the larger of the two average distances <inline-formula id="IEq3"><alternatives><tex-math id="M5">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ s=\frac{b-a}{\max \left(a,b\right)} $$\end{document}</tex-math><mml:math id="M6" display="inline"><mml:mi>s</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi>b</mml:mi><mml:mo>&#x02212;</mml:mo><mml:mi>a</mml:mi></mml:mrow><mml:mrow><mml:mo>max</mml:mo><mml:mfenced close=")" open="(" separators=","><mml:mi>a</mml:mi><mml:mi>b</mml:mi></mml:mfenced></mml:mrow></mml:mfrac></mml:math><inline-graphic xlink:href="13059_2020_1949_Article_IEq3.gif"/></alternatives></inline-formula>. Therefore, the possible values for the silhouette widths lie in the range &#x02212;&#x02009;1 to 1, where higher values indicate good cluster assignment, while lower values close to 0 indicate poor cluster assignment. Finally, the average silhouette width for every cluster is calculated, and averages are aggregated to obtain a measure of the global purity of clusters. For the silhouette analysis, we used the R package <italic>cluster</italic> (version 2.0.8).</p><p id="Par89">For statistical analysis of cluster quality, we fitted a linear model <italic>score&#x02009;=&#x02009;f(scRNA-seq protocol&#x02009;+&#x02009;input matrix)</italic>, where <italic>score</italic> corresponds to average silhouette width for a given scRNA-seq <italic>protocol</italic> - <italic>input matrix</italic> pair. <italic>Protocol</italic> and <italic>input matrix</italic> are factors, with reference level Quartz-Seq2 and positive control, respectively. We fitted two separate linear models for transcription factor and pathway activity inference methods. We report the estimates and <italic>p</italic> values for the different coefficients of these linear models. Based on these linear models, we performed a two-way ANOVA and pairwise comparisons using TukeyHSD post hoc test.</p></sec><sec id="Sec33"><title>Comparison of PBMCs TF activity with gene essentiality</title><p id="Par90">For each scRNA-seq technology and used TF analysis tool, we calculated mean TF expression for each PBMC type. To focus solely on PBMCs, cells classified as HEK cells or unknown were discarded from this analysis. In addition, we removed megakaryocytes because their abundance was in general too low across all technologies. We used the DepMap shRNA screen [<xref ref-type="bibr" rid="CR31">31</xref>] as gene essentiality data. As a given TF can either increase proliferation (oncogene) or decrease it (tumor suppressor), we can expect either negative or positive correlation (respectively) between gene essentiality and TF activity. To correct for this effect, we calculated Pearson correlations between TF expression (from CCLE data [<xref ref-type="bibr" rid="CR48">48</xref>]) and TF essentiality for each TF and multiplied TF essentiality values by the sign of this correlation coefficients. For categorizing hematologic cancers into myeloid and lymphoid groups, we used CCLE metadata (Additional&#x000a0;file&#x000a0;<xref rid="MOESM4" ref-type="media">4</xref>). Basically, we classified myeloid leukemias as myeloid and lymphoid leukemias and lymphomas as lymphoid cancers. Ambiguous cancer types were removed from our analysis.</p></sec></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec34"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="13059_2020_1949_MOESM1_ESM.docx"><caption><p><bold>Additional file 1.</bold> Supplementary figures S1-S15.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="13059_2020_1949_MOESM2_ESM.csv"><caption><p><bold>Additional file 2.</bold> Metadata of bulk RNA-seq TF perturbation data, including among others perturbation target, perturbation direction, GEO accession ID and annotated GEO sample IDs (whether samples belong to control or perturbation group). Those data were used to simulate single cells. (CSV 13 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="13059_2020_1949_MOESM3_ESM.csv"><caption><p><bold>Additional file 3.</bold> Metadata of bulk RNA-seq pathway perturbation data, including among others perturbation target, perturbation direction, GEO accession ID and annotated GEO sample IDs (whether samples belong to control or perturbation group). Those data were used to simulate single cells. (CSV 1 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="13059_2020_1949_MOESM4_ESM.csv"><caption><p><bold>Additional file 4.</bold> Manual classification of selected hematologic cancer cell lines from the CCLE database into myeloid (M) or lymphoid (L) cancer. (CSV 2 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="13059_2020_1949_MOESM5_ESM.docx"><caption><p><bold>Additional file 5.</bold> Review history.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s13059-020-1949-z.</p></sec><ack><title>Acknowledgements</title><p>We thank Aur&#x000e9;lien Dugourd and Ricardo Ramirez-Flores for helpful discussions. We also thank Minoo Ashtiani for supporting the collection of single pathway perturbation experiments on bulk level.</p><sec id="FPar1"><title>Peer review information</title><p id="Par91">Yixin Yao was the primer editor of this article and managed its editorial process and peer review in collaboration with the rest of the editorial team.</p></sec><sec id="FPar2"><title>Review history</title><p id="Par92">The review history is available as Additional&#x000a0;file&#x000a0;<xref rid="MOESM5" ref-type="media">5</xref>.</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>CHH, BS, and JSR designed the research. CHH performed the analyses and drafted the manuscript. BS and JT supervised by JSR and MPK and BAJ both supervised by DAL supported the development of the single-cell simulation strategy. JT and JPP set up the cluster infrastructure for the simulation. JG supervised by OS processed the real scRNA-seq data. JPP constructed the SCENIC gene regulatory networks. EM and HH provided the PBMC single-cell data and supported the corresponding analysis. BS performed the blood cancer analysis. BS, JT, JG, JPP, and JSR contributed to the manuscript writing. JSR and BS supervised the project. All authors read, commented, and approved the final manuscript.</p></notes><notes notes-type="funding-information"><title>Funding</title><p>CHH is supported by the German Federal Ministry of Education and Research (BMBF)-funded project Systems Medicine of the Liver (LiSyM, FKZ: 031&#x02009;L0049). MPK, BAJ, and DAL are supported by NIH Grant U54-CA217377. BS is supported by the Premium Postdoctoral Fellowship Program of the Hungarian Academy of Sciences. HH is a Miguel Servet (CP14/00229) researcher funded by the Spanish Institute of Health Carlos III (ISCIII). This work has received funding from the Ministerio de Ciencia, Innovaci&#x000f3;n y Universidades (SAF2017-89109-P; AEI/FEDER, UE).</p></notes><notes notes-type="data-availability"><title>Availability of data and materials</title><p>The code to perform all presented studies is written in R [<xref ref-type="bibr" rid="CR49">49</xref>, <xref ref-type="bibr" rid="CR50">50</xref>] and is freely available on GitHub: <ext-link ext-link-type="uri" xlink:href="https://github.com/saezlab/FootprintMethods_on_scRNAseq">https://github.com/saezlab/FootprintMethods_on_scRNAseq</ext-link> [<xref ref-type="bibr" rid="CR51">51</xref>]. The datasets supporting the conclusions of this article are available at Zenodo: 10.5281/zenodo.3564179 [<xref ref-type="bibr" rid="CR52">52</xref>].</p></notes><notes><title>Ethics approval and consent to participate</title><p id="Par93">Not applicable</p></notes><notes><title>Consent for publication</title><p id="Par94">Not applicable</p></notes><notes notes-type="COI-statement"><title>Competing interests</title><p id="Par95">The authors declare that they have no competing interests.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Essaghir</surname><given-names>A</given-names></name><name><surname>Toffalini</surname><given-names>F</given-names></name><name><surname>Knoops</surname><given-names>L</given-names></name><name><surname>Kallin</surname><given-names>A</given-names></name><name><surname>van Helden</surname><given-names>J</given-names></name><name><surname>Demoulin</surname><given-names>J-B</given-names></name></person-group><article-title>Transcription factor regulation can be accurately predicted from the presence of target gene signatures in microarray gene expression data</article-title><source>Nucleic Acids Res</source><year>2010</year><volume>38</volume><fpage>e120</fpage><pub-id pub-id-type="doi">10.1093/nar/gkq149</pub-id><pub-id pub-id-type="pmid">20215436</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hung</surname><given-names>J-H</given-names></name><name><surname>Yang</surname><given-names>T-H</given-names></name><name><surname>Hu</surname><given-names>Z</given-names></name><name><surname>Weng</surname><given-names>Z</given-names></name><name><surname>DeLisi</surname><given-names>C</given-names></name></person-group><article-title>Gene set enrichment analysis: performance evaluation and usage guidelines</article-title><source>Brief Bioinform</source><year>2012</year><volume>13</volume><fpage>281</fpage><lpage>291</lpage><pub-id pub-id-type="doi">10.1093/bib/bbr049</pub-id><pub-id pub-id-type="pmid">21900207</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Khatri</surname><given-names>P</given-names></name><name><surname>Sirota</surname><given-names>M</given-names></name><name><surname>Butte</surname><given-names>AJ</given-names></name></person-group><article-title>Ten years of pathway analysis: current approaches and outstanding challenges</article-title><source>PLoS Comput Biol</source><year>2012</year><volume>8</volume><fpage>e1002375</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1002375</pub-id><pub-id pub-id-type="pmid">22383865</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nguyen</surname><given-names>T-M</given-names></name><name><surname>Shafi</surname><given-names>A</given-names></name><name><surname>Nguyen</surname><given-names>T</given-names></name><name><surname>Draghici</surname><given-names>S</given-names></name></person-group><article-title>Identifying significantly impacted pathways: a comprehensive review and assessment</article-title><source>Genome Biol</source><year>2019</year><volume>20</volume><fpage>203</fpage><pub-id pub-id-type="doi">10.1186/s13059-019-1790-4</pub-id><pub-id pub-id-type="pmid">31597578</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liberzon</surname><given-names>A</given-names></name><name><surname>Birger</surname><given-names>C</given-names></name><name><surname>Thorvaldsdottir</surname><given-names>H</given-names></name><name><surname>Ghandi</surname><given-names>M</given-names></name><name><surname>Mesirov</surname><given-names>JP</given-names></name><name><surname>Tamayo</surname><given-names>P</given-names></name></person-group><article-title>The molecular signatures database (MSigDB) hallmark gene set collection</article-title><source>Cell Syst</source><year>2015</year><volume>1</volume><issue>6</issue><fpage>417</fpage><lpage>425</lpage><pub-id pub-id-type="doi">10.1016/j.cels.2015.12.004</pub-id><?supplied-pmid 26771021?><pub-id pub-id-type="pmid">26771021</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><mixed-citation publication-type="other">Fisher RA. Statistical methods for research workers [Internet]: Genesis Publishing Pvt Ltd; 2006. Available from: <ext-link ext-link-type="uri" xlink:href="https://psychclassics.yorku.ca/Fisher/Methods/chap6.htm">https://psychclassics.yorku.ca/Fisher/Methods/chap6.htm</ext-link></mixed-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Subramanian</surname><given-names>A</given-names></name><name><surname>Tamayo</surname><given-names>P</given-names></name><name><surname>Mootha</surname><given-names>VK</given-names></name><name><surname>Mukherjee</surname><given-names>S</given-names></name><name><surname>Ebert</surname><given-names>BL</given-names></name><name><surname>Gillette</surname><given-names>MA</given-names></name><etal/></person-group><article-title>Gene set enrichment analysis: a knowledge-based approach for interpreting genome-wide expression profiles</article-title><source>Proc Natl Acad Sci U S A</source><year>2005</year><volume>102</volume><fpage>15545</fpage><lpage>15550</lpage><pub-id pub-id-type="doi">10.1073/pnas.0506580102</pub-id><pub-id pub-id-type="pmid">16199517</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Trescher</surname><given-names>S</given-names></name><name><surname>M&#x000fc;nchmeyer</surname><given-names>J</given-names></name><name><surname>Leser</surname><given-names>U</given-names></name></person-group><article-title>Estimating genome-wide regulatory activity from multi-omics data sets using mathematical optimization</article-title><source>BMC Syst Biol</source><year>2017</year><volume>11</volume><fpage>41</fpage><pub-id pub-id-type="doi">10.1186/s12918-017-0419-z</pub-id><pub-id pub-id-type="pmid">28347313</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pang</surname><given-names>H</given-names></name><name><surname>Lin</surname><given-names>A</given-names></name><name><surname>Holford</surname><given-names>M</given-names></name><name><surname>Enerson</surname><given-names>BE</given-names></name><name><surname>Lu</surname><given-names>B</given-names></name><name><surname>Lawton</surname><given-names>MP</given-names></name><etal/></person-group><article-title>Pathway analysis using random forests classification and regression</article-title><source>Bioinformatics</source><year>2006</year><volume>22</volume><fpage>2028</fpage><lpage>2036</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btl344</pub-id><pub-id pub-id-type="pmid">16809386</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tang</surname><given-names>F</given-names></name><name><surname>Barbacioru</surname><given-names>C</given-names></name><name><surname>Wang</surname><given-names>Y</given-names></name><name><surname>Nordman</surname><given-names>E</given-names></name><name><surname>Lee</surname><given-names>C</given-names></name><name><surname>Xu</surname><given-names>N</given-names></name><etal/></person-group><article-title>mRNA-Seq whole-transcriptome analysis of a single cell</article-title><source>Nat Methods</source><year>2009</year><volume>6</volume><fpage>377</fpage><lpage>382</lpage><pub-id pub-id-type="doi">10.1038/nmeth.1315</pub-id><pub-id pub-id-type="pmid">19349980</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stegle</surname><given-names>O</given-names></name><name><surname>Teichmann</surname><given-names>SA</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name></person-group><article-title>Computational and analytical challenges in single-cell transcriptomics</article-title><source>Nat Rev Genet</source><year>2015</year><volume>16</volume><fpage>133</fpage><lpage>145</lpage><pub-id pub-id-type="doi">10.1038/nrg3833</pub-id><pub-id pub-id-type="pmid">25628217</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><mixed-citation publication-type="other">Schubert M, Klinger B, Kl&#x000fc;nemann M, Sieber A, Uhlitz F, Sauer S, et al. Perturbation-response genes reveal signaling footprints in cancer gene expression [Internet]. Nature Communications. 2018; Available from: 10.1038/s41467-017-02391-6.</mixed-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Garcia-Alonso</surname><given-names>L</given-names></name><name><surname>Holland</surname><given-names>CH</given-names></name><name><surname>Ibrahim</surname><given-names>MM</given-names></name><name><surname>Turei</surname><given-names>D</given-names></name><name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name></person-group><article-title>Benchmark and integration of resources for the estimation of human transcription factor activities</article-title><source>Genome Res</source><year>2019</year><volume>29</volume><fpage>1363</fpage><lpage>1375</lpage><pub-id pub-id-type="doi">10.1101/gr.240663.118</pub-id><pub-id pub-id-type="pmid">31340985</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ashburner</surname><given-names>Michael</given-names></name><name><surname>Ball</surname><given-names>Catherine A.</given-names></name><name><surname>Blake</surname><given-names>Judith A.</given-names></name><name><surname>Botstein</surname><given-names>David</given-names></name><name><surname>Butler</surname><given-names>Heather</given-names></name><name><surname>Cherry</surname><given-names>J. Michael</given-names></name><name><surname>Davis</surname><given-names>Allan P.</given-names></name><name><surname>Dolinski</surname><given-names>Kara</given-names></name><name><surname>Dwight</surname><given-names>Selina S.</given-names></name><name><surname>Eppig</surname><given-names>Janan T.</given-names></name><name><surname>Harris</surname><given-names>Midori A.</given-names></name><name><surname>Hill</surname><given-names>David P.</given-names></name><name><surname>Issel-Tarver</surname><given-names>Laurie</given-names></name><name><surname>Kasarskis</surname><given-names>Andrew</given-names></name><name><surname>Lewis</surname><given-names>Suzanna</given-names></name><name><surname>Matese</surname><given-names>John C.</given-names></name><name><surname>Richardson</surname><given-names>Joel E.</given-names></name><name><surname>Ringwald</surname><given-names>Martin</given-names></name><name><surname>Rubin</surname><given-names>Gerald M.</given-names></name><name><surname>Sherlock</surname><given-names>Gavin</given-names></name></person-group><article-title>Gene Ontology: tool for the unification of biology</article-title><source>Nature Genetics</source><year>2000</year><volume>25</volume><issue>1</issue><fpage>25</fpage><lpage>29</lpage><pub-id pub-id-type="doi">10.1038/75556</pub-id><pub-id pub-id-type="pmid">10802651</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alvarez</surname><given-names>MJ</given-names></name><name><surname>Shen</surname><given-names>Y</given-names></name><name><surname>Giorgi</surname><given-names>FM</given-names></name><name><surname>Lachmann</surname><given-names>A</given-names></name><name><surname>Ding</surname><given-names>BB</given-names></name><name><surname>Ye</surname><given-names>BH</given-names></name><etal/></person-group><article-title>Functional characterization of somatic mutations in cancer using network-based inference of protein activity</article-title><source>Nat Genet</source><year>2016</year><volume>48</volume><fpage>838</fpage><lpage>847</lpage><pub-id pub-id-type="doi">10.1038/ng.3593</pub-id><pub-id pub-id-type="pmid">27322546</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><mixed-citation publication-type="other">Dugourd A, Saez-Rodriguez J. Footprint-based functional analysis of multi-omic data. Current Opinion in Systems Biology: Elsevier; 2019. Available from: <ext-link ext-link-type="uri" xlink:href="https://www.sciencedirect.com/science/article/pii/S2452310019300149">https://www.sciencedirect.com/science/article/pii/S2452310019300149</ext-link></mixed-citation></ref><ref id="CR17"><label>17.</label><mixed-citation publication-type="other">Cantini L, Calzone L, Martignetti L, Rydenfelt M, Bl&#x000fc;thgen N, Barillot E, et al. Classification of gene signatures for their information value and functional redundancy. NPJ Syst Biol Appl. 2018; Available from: 10.1038/s41540-017-0038-8.</mixed-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Aibar</surname><given-names>S</given-names></name><name><surname>Gonz&#x000e1;lez-Blas</surname><given-names>CB</given-names></name><name><surname>Moerman</surname><given-names>T</given-names></name><name><surname>Huynh-Thu</surname><given-names>VA</given-names></name><name><surname>Imrichova</surname><given-names>H</given-names></name><name><surname>Hulselmans</surname><given-names>G</given-names></name><etal/></person-group><article-title>SCENIC: single-cell regulatory network inference and clustering</article-title><source>Nat Methods</source><year>2017</year><volume>14</volume><fpage>1083</fpage><lpage>1086</lpage><pub-id pub-id-type="doi">10.1038/nmeth.4463</pub-id><pub-id pub-id-type="pmid">28991892</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ding</surname><given-names>H</given-names></name><name><surname>Douglass</surname><given-names>EF</given-names><suffix>Jr</suffix></name><name><surname>Sonabend</surname><given-names>AM</given-names></name><name><surname>Mela</surname><given-names>A</given-names></name><name><surname>Bose</surname><given-names>S</given-names></name><name><surname>Gonzalez</surname><given-names>C</given-names></name><etal/></person-group><article-title>Quantitative assessment of protein activity in orphan tissues and single cells using the metaVIPER algorithm</article-title><source>Nat Commun</source><year>2018</year><volume>9</volume><fpage>1471</fpage><pub-id pub-id-type="doi">10.1038/s41467-018-03843-3</pub-id><pub-id pub-id-type="pmid">29662057</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dixit</surname><given-names>A</given-names></name><name><surname>Parnas</surname><given-names>O</given-names></name><name><surname>Li</surname><given-names>B</given-names></name><name><surname>Chen</surname><given-names>J</given-names></name><name><surname>Fulco</surname><given-names>CP</given-names></name><name><surname>Jerby-Arnon</surname><given-names>L</given-names></name><etal/></person-group><article-title>Perturb-Seq: dissecting molecular circuits with scalable single-cell RNA profiling of pooled genetic screens</article-title><source>Cell</source><year>2016</year><volume>167</volume><fpage>1853</fpage><lpage>66.e17</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2016.11.038</pub-id><pub-id pub-id-type="pmid">27984732</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Genga</surname><given-names>RMJ</given-names></name><name><surname>Kernfeld</surname><given-names>EM</given-names></name><name><surname>Parsi</surname><given-names>KM</given-names></name><name><surname>Parsons</surname><given-names>TJ</given-names></name><name><surname>Ziller</surname><given-names>MJ</given-names></name><name><surname>Maehr</surname><given-names>R</given-names></name></person-group><article-title>Single-cell RNA-sequencing-based CRISPRi screening resolves molecular drivers of early human endoderm development</article-title><source>Cell Rep</source><year>2019</year><volume>27</volume><fpage>708</fpage><lpage>18.e10</lpage><pub-id pub-id-type="doi">10.1016/j.celrep.2019.03.076</pub-id><pub-id pub-id-type="pmid">30995470</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><mixed-citation publication-type="other">Mereu E, Lafzi A, Moutinho C, Ziegenhain C, MacCarthy DJ, Alvarez A, et al. Benchmarking single-cell RNA sequencing protocols for cell atlas projects. BioRxiv. 2019; biorxiv.org. Available from: <ext-link ext-link-type="uri" xlink:href="https://www.biorxiv.org/content/10.1101/630087v1.abstract">https://www.biorxiv.org/content/10.1101/630087v1.abstract</ext-link>.</mixed-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kharchenko</surname><given-names>PV</given-names></name><name><surname>Silberstein</surname><given-names>L</given-names></name><name><surname>Scadden</surname><given-names>DT</given-names></name></person-group><article-title>Bayesian approach to single-cell differential expression analysis</article-title><source>Nat Methods</source><year>2014</year><volume>11</volume><fpage>740</fpage><lpage>742</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2967</pub-id><pub-id pub-id-type="pmid">24836921</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><mixed-citation publication-type="other">Regev A, Teichmann SA, Lander ES, Amit I, Benoist C. Science forum: the human cell atlas. Elife. 2017; cdn.elifesciences.org. Available from: <ext-link ext-link-type="uri" xlink:href="https://cdn.elifesciences.org/articles/27041/elife-27041-v2.pdf">https://cdn.elifesciences.org/articles/27041/elife-27041-v2.pdf</ext-link>.</mixed-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Butler</surname><given-names>A</given-names></name><name><surname>Hoffman</surname><given-names>P</given-names></name><name><surname>Smibert</surname><given-names>P</given-names></name><name><surname>Papalexi</surname><given-names>E</given-names></name><name><surname>Satija</surname><given-names>R</given-names></name></person-group><article-title>Integrating single-cell transcriptomic data across different conditions, technologies, and species</article-title><source>Nat Biotechnol</source><year>2018</year><volume>36</volume><fpage>411</fpage><lpage>420</lpage><pub-id pub-id-type="doi">10.1038/nbt.4096</pub-id><pub-id pub-id-type="pmid">29608179</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Burd</surname><given-names>AL</given-names></name><name><surname>Ingraham</surname><given-names>RH</given-names></name><name><surname>Goldrick</surname><given-names>SE</given-names></name><name><surname>Kroe</surname><given-names>RR</given-names></name><name><surname>Crute</surname><given-names>JJ</given-names></name><name><surname>Grygon</surname><given-names>CA</given-names></name></person-group><article-title>Assembly of major histocompatibility complex (MHC) class II transcription factors: association and promoter recognition of RFX proteins</article-title><source>Biochemistry</source><year>2004</year><volume>43</volume><fpage>12750</fpage><lpage>12760</lpage><pub-id pub-id-type="doi">10.1021/bi030262o</pub-id><pub-id pub-id-type="pmid">15461447</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zakrzewska</surname><given-names>A</given-names></name><name><surname>Cui</surname><given-names>C</given-names></name><name><surname>Stockhammer</surname><given-names>OW</given-names></name><name><surname>Benard</surname><given-names>EL</given-names></name><name><surname>Spaink</surname><given-names>HP</given-names></name><name><surname>Meijer</surname><given-names>AH</given-names></name></person-group><article-title>Macrophage-specific gene functions in Spi1-directed innate immunity</article-title><source>Blood</source><year>2010</year><volume>116</volume><fpage>e1</fpage><lpage>11</lpage><pub-id pub-id-type="doi">10.1182/blood-2010-01-262873</pub-id><pub-id pub-id-type="pmid">20424185</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Feng</surname><given-names>X</given-names></name><name><surname>Wang</surname><given-names>H</given-names></name><name><surname>Takata</surname><given-names>H</given-names></name><name><surname>Day</surname><given-names>TJ</given-names></name><name><surname>Willen</surname><given-names>J</given-names></name><name><surname>Hu</surname><given-names>H</given-names></name></person-group><article-title>Transcription factor Foxp1 exerts essential cell-intrinsic regulation of the quiescence of naive T cells</article-title><source>Nat Immunol</source><year>2011</year><volume>12</volume><fpage>544</fpage><lpage>550</lpage><pub-id pub-id-type="doi">10.1038/ni.2034</pub-id><pub-id pub-id-type="pmid">21532575</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><mixed-citation publication-type="other">Liu T, Zhang L, Joo D, Sun S-C. NF-&#x003ba;B signaling in inflammation. Signal Transduct Target Ther. 2017;2 Available from: 10.1038/sigtrans.2017.23.</mixed-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Staniek</surname><given-names>J</given-names></name><name><surname>Lorenzetti</surname><given-names>R</given-names></name><name><surname>Heller</surname><given-names>B</given-names></name><name><surname>Janowska</surname><given-names>I</given-names></name><name><surname>Schneider</surname><given-names>P</given-names></name><name><surname>Unger</surname><given-names>S</given-names></name><etal/></person-group><article-title>TRAIL-R1 and TRAIL-R2 mediate TRAIL-dependent apoptosis in activated primary human B lymphocytes</article-title><source>Front Immunol</source><year>2019</year><volume>10</volume><fpage>951</fpage><pub-id pub-id-type="doi">10.3389/fimmu.2019.00951</pub-id><pub-id pub-id-type="pmid">31114586</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><mixed-citation publication-type="other">McFarland JM, Ho ZV, Kugener G, Dempster JM, Montgomery PG, Bryan JG, et al. Improved estimation of cancer dependencies from large-scale RNAi screens using model-based normalization and data integration. Nat Commun. 2018;9:&#x02013;4610 Available from: 10.1038/s41467-018-06916-5.</mixed-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Parikh</surname><given-names>JR</given-names></name><name><surname>Klinger</surname><given-names>B</given-names></name><name><surname>Xia</surname><given-names>Y</given-names></name><name><surname>Marto</surname><given-names>JA</given-names></name><name><surname>Bl&#x000fc;thgen</surname><given-names>N</given-names></name></person-group><article-title>Discovering causal signaling pathways through gene-expression patterns</article-title><source>Nucleic Acids Res</source><year>2010</year><volume>38</volume><fpage>W109</fpage><lpage>W117</lpage><pub-id pub-id-type="doi">10.1093/nar/gkq424</pub-id><pub-id pub-id-type="pmid">20494976</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><mixed-citation publication-type="other">Holland CH, Szalai B, Saez-Rodriguez J. Transfer of regulatory knowledge from human to mouse for functional genomics analysis. Biochim Biophys Acta Gene Regul Mech. 2019:194431 Available from: 10.1016/j.bbagrm.2019.194431.</mixed-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zappia</surname><given-names>L</given-names></name><name><surname>Phipson</surname><given-names>B</given-names></name><name><surname>Oshlack</surname><given-names>A</given-names></name></person-group><article-title>Splatter: simulation of single-cell RNA sequencing data</article-title><source>Genome Biol</source><year>2017</year><volume>18</volume><fpage>174</fpage><pub-id pub-id-type="doi">10.1186/s13059-017-1305-0</pub-id><pub-id pub-id-type="pmid">28899397</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Peng</surname><given-names>T</given-names></name><name><surname>Zhu</surname><given-names>Q</given-names></name><name><surname>Yin</surname><given-names>P</given-names></name><name><surname>Tan</surname><given-names>K</given-names></name></person-group><article-title>SCRABBLE: single-cell RNA-seq imputation constrained by bulk RNA-seq data</article-title><source>Genome Biol</source><year>2019</year><volume>20</volume><fpage>88</fpage><pub-id pub-id-type="doi">10.1186/s13059-019-1681-8</pub-id><pub-id pub-id-type="pmid">31060596</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Margolin</surname><given-names>AA</given-names></name><name><surname>Nemenman</surname><given-names>I</given-names></name><name><surname>Basso</surname><given-names>K</given-names></name><name><surname>Wiggins</surname><given-names>C</given-names></name><name><surname>Stolovitzky</surname><given-names>G</given-names></name><name><surname>Dalla Favera</surname><given-names>R</given-names></name><etal/></person-group><article-title>ARACNE: an algorithm for the reconstruction of gene regulatory networks in a mammalian cellular context</article-title><source>BMC Bioinformatics</source><year>2006</year><volume>7</volume><issue>Suppl 1</issue><fpage>S7</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-7-S1-S7</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Keenan</surname><given-names>Alexandra B</given-names></name><name><surname>Torre</surname><given-names>Denis</given-names></name><name><surname>Lachmann</surname><given-names>Alexander</given-names></name><name><surname>Leong</surname><given-names>Ariel K</given-names></name><name><surname>Wojciechowicz</surname><given-names>Megan L</given-names></name><name><surname>Utti</surname><given-names>Vivian</given-names></name><name><surname>Jagodnik</surname><given-names>Kathleen M</given-names></name><name><surname>Kropiwnicki</surname><given-names>Eryk</given-names></name><name><surname>Wang</surname><given-names>Zichen</given-names></name><name><surname>Ma&#x02019;ayan</surname><given-names>Avi</given-names></name></person-group><article-title>ChEA3: transcription factor enrichment analysis by orthogonal omics integration</article-title><source>Nucleic Acids Research</source><year>2019</year><volume>47</volume><issue>W1</issue><fpage>W212</fpage><lpage>W224</lpage><pub-id pub-id-type="doi">10.1093/nar/gkz446</pub-id><pub-id pub-id-type="pmid">31114921</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hegde</surname><given-names>M</given-names></name><name><surname>Strand</surname><given-names>C</given-names></name><name><surname>Hanna</surname><given-names>RE</given-names></name><name><surname>Doench</surname><given-names>JG</given-names></name></person-group><article-title>Uncoupling of sgRNAs from their associated barcodes during PCR amplification of combinatorial CRISPR screens</article-title><source>PLoS One</source><year>2018</year><volume>13</volume><fpage>e0197547</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0197547</pub-id><pub-id pub-id-type="pmid">29799876</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Smits</surname><given-names>AH</given-names></name><name><surname>Ziebell</surname><given-names>F</given-names></name><name><surname>Joberty</surname><given-names>G</given-names></name><name><surname>Zinn</surname><given-names>N</given-names></name><name><surname>Mueller</surname><given-names>WF</given-names></name><name><surname>Clauder-M&#x000fc;nster</surname><given-names>S</given-names></name><etal/></person-group><article-title>Biological plasticity rescues target activity in CRISPR knock outs</article-title><source>Nat Methods</source><year>2019</year><volume>16</volume><fpage>1087</fpage><lpage>1093</lpage><pub-id pub-id-type="doi">10.1038/s41592-019-0614-5</pub-id><pub-id pub-id-type="pmid">31659326</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><mixed-citation publication-type="other">Sergushichev A. An algorithm for fast preranked gene set enrichment analysis using cumulative statistic calculation. bioRxiv. 2016:060012 [cited 2018 Jul 17]. Available from: <ext-link ext-link-type="uri" xlink:href="https://www.biorxiv.org/content/early/2016/06/20/060012.abstract">https://www.biorxiv.org/content/early/2016/06/20/060012.abstract</ext-link>.</mixed-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Carithers</surname><given-names>LJ</given-names></name><name><surname>Ardlie</surname><given-names>K</given-names></name><name><surname>Barcus</surname><given-names>M</given-names></name><name><surname>Branton</surname><given-names>PA</given-names></name><name><surname>Britton</surname><given-names>A</given-names></name><name><surname>Buia</surname><given-names>SA</given-names></name><etal/></person-group><article-title>A novel approach to high-quality postmortem tissue procurement: the GTEx project</article-title><source>Biopreserv Biobank</source><year>2015</year><volume>13</volume><fpage>311</fpage><lpage>319</lpage><pub-id pub-id-type="doi">10.1089/bio.2015.0032</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lun</surname><given-names>ATL</given-names></name><name><surname>McCarthy</surname><given-names>DJ</given-names></name><name><surname>Marioni</surname><given-names>JC</given-names></name></person-group><article-title>A step-by-step workflow for low-level analysis of single-cell RNA-seq data with Bioconductor</article-title><source>F1000Res</source><year>2016</year><volume>5</volume><fpage>2122</fpage><?supplied-pmid ********?><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edgar</surname><given-names>R</given-names></name><name><surname>Domrachev</surname><given-names>M</given-names></name><name><surname>Lash</surname><given-names>AE</given-names></name></person-group><article-title>Gene Expression Omnibus: NCBI gene expression and hybridization array data repository</article-title><source>Nucleic Acids Res</source><year>2002</year><volume>30</volume><fpage>207</fpage><lpage>210</lpage><pub-id pub-id-type="doi">10.1093/nar/30.1.207</pub-id><pub-id pub-id-type="pmid">11752295</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lachmann</surname><given-names>A</given-names></name><name><surname>Torre</surname><given-names>D</given-names></name><name><surname>Keenan</surname><given-names>AB</given-names></name><name><surname>Jagodnik</surname><given-names>KM</given-names></name><name><surname>Lee</surname><given-names>HJ</given-names></name><name><surname>Wang</surname><given-names>L</given-names></name><etal/></person-group><article-title>Massive mining of publicly available RNA-seq data from human and mouse</article-title><source>Nat Commun</source><year>2018</year><volume>9</volume><fpage>1366</fpage><pub-id pub-id-type="doi">10.1038/s41467-018-03751-6</pub-id><pub-id pub-id-type="pmid">29636450</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Robinson</surname><given-names>MD</given-names></name><name><surname>McCarthy</surname><given-names>DJ</given-names></name><name><surname>Smyth</surname><given-names>GK</given-names></name></person-group><article-title>edgeR: a Bioconductor package for differential expression analysis of digital gene expression data</article-title><source>Bioinformatics</source><year>2010</year><volume>26</volume><fpage>139</fpage><lpage>140</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btp616</pub-id><pub-id pub-id-type="pmid">19910308</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ritchie</surname><given-names>ME</given-names></name><name><surname>Phipson</surname><given-names>B</given-names></name><name><surname>Wu</surname><given-names>D</given-names></name><name><surname>Hu</surname><given-names>Y</given-names></name><name><surname>Law</surname><given-names>CW</given-names></name><name><surname>Shi</surname><given-names>W</given-names></name><etal/></person-group><article-title>limma powers differential expression analyses for RNA-sequencing and microarray studies</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><fpage>e47</fpage><pub-id pub-id-type="doi">10.1093/nar/gkv007</pub-id><pub-id pub-id-type="pmid">25605792</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rousseeuw</surname><given-names>PJ</given-names></name></person-group><article-title>Silhouettes: a graphical aid to the interpretation and validation of cluster analysis</article-title><source>J Comput Appl Math</source><year>1987</year><volume>20</volume><fpage>53</fpage><lpage>65</lpage><pub-id pub-id-type="doi">10.1016/0377-0427(87)90125-7</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ghandi</surname><given-names>M</given-names></name><name><surname>Huang</surname><given-names>FW</given-names></name><name><surname>Jan&#x000e9;-Valbuena</surname><given-names>J</given-names></name><name><surname>Kryukov</surname><given-names>GV</given-names></name><name><surname>Lo</surname><given-names>CC</given-names></name><name><surname>McDonald</surname><given-names>ER</given-names><suffix>3rd</suffix></name><etal/></person-group><article-title>Next-generation characterization of the Cancer Cell Line Encyclopedia</article-title><source>Nature</source><year>2019</year><volume>569</volume><fpage>503</fpage><lpage>508</lpage><pub-id pub-id-type="doi">10.1038/s41586-019-1186-3</pub-id><pub-id pub-id-type="pmid">31068700</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Core Team</surname><given-names>R</given-names></name><etal/></person-group><source>R: a language and environment for statistical computing</source><year>2013</year><publisher-loc>Vienna</publisher-loc><publisher-name>R Foundation for statistical computing</publisher-name></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wickham</surname><given-names>H</given-names></name><name><surname>Averick</surname><given-names>M</given-names></name><name><surname>Bryan</surname><given-names>J</given-names></name><name><surname>Chang</surname><given-names>W</given-names></name><name><surname>McGowan</surname><given-names>L</given-names></name><name><surname>Fran&#x000e7;ois</surname><given-names>R</given-names></name><etal/></person-group><article-title>Welcome to the Tidyverse</article-title><source>JOSS</source><year>2019</year><volume>4</volume><fpage>1686</fpage><pub-id pub-id-type="doi">10.21105/joss.01686</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><mixed-citation publication-type="other">Holland CH, Tanevski J, Perales-Pat&#x000f3;n J, Gleixner J, Kumar MP, Mereu E, et al. Robustness and applicability of transcription factor and pathway analysis tools on single-cell RNA-seq data. GitHub. 2020; Available from: <ext-link ext-link-type="uri" xlink:href="https://github.com/saezlab/FootprintMethods_on_scRNAseq">https://github.com/saezlab/FootprintMethods_on_scRNAseq</ext-link>.</mixed-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Holland</surname><given-names>CH</given-names></name><name><surname>Saez-Rodriguez</surname><given-names>J</given-names></name></person-group><source>Robustness and applicability of transcription factor and pathway analysis tools on single-cell RNA-seq data</source><year>2019</year></element-citation></ref></ref-list></back></article>