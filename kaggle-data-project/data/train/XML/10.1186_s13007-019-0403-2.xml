<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">Plant Methods</journal-id><journal-id journal-id-type="iso-abbrev">Plant Methods</journal-id><journal-title-group><journal-title>Plant Methods</journal-title></journal-title-group><issn pub-type="epub">1746-4811</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6385457</article-id><article-id pub-id-type="publisher-id">403</article-id><article-id pub-id-type="doi">10.1186/s13007-019-0403-2</article-id><article-categories><subj-group subj-group-type="heading"><subject>Software</subject></subj-group></article-categories><title-group><article-title>SpotCard: an optical mark recognition tool to improve field data collection speed and accuracy</article-title></title-group><contrib-group><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-5360-7979</contrib-id><name><surname>Symington</surname><given-names>Hamish A.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1"/></contrib><contrib contrib-type="author"><name><surname>Glover</surname><given-names>Beverley J.</given-names></name><xref ref-type="aff" rid="Aff1"/></contrib><aff id="Aff1"><institution-wrap><institution-id institution-id-type="ISNI">0000000121885934</institution-id><institution-id institution-id-type="GRID">grid.5335.0</institution-id><institution>Department of Plant Sciences, </institution><institution>University of Cambridge, </institution></institution-wrap>Downing Street, Cambridge, CB2 3EA UK </aff></contrib-group><pub-date pub-type="epub"><day>22</day><month>2</month><year>2019</year></pub-date><pub-date pub-type="pmc-release"><day>22</day><month>2</month><year>2019</year></pub-date><pub-date pub-type="collection"><year>2019</year></pub-date><volume>15</volume><elocation-id>19</elocation-id><history><date date-type="received"><day>16</day><month>8</month><year>2018</year></date><date date-type="accepted"><day>14</day><month>2</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2019</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">When taking photographs of plants in the field, it is often necessary to record additional information such as sample number, biological replicate number and subspecies. Manual methods of recording such information are slow, often involve laborious transcription from hand-written notes or the need to have a laptop or tablet on site, and present a risk by separating written data capture from image capture. Existing tools for field data capture focus on recording information rather than capturing pictures of plants.</p></sec><sec><title>Results</title><p id="Par2">We present SpotCard, a tool comprising two macros. The first can be used to create a template for small, reusable cards for use when photographing plants. Information can be encoded on these cards in a human- and machine-readable form, allowing the user to swiftly make annotations before taking the photograph. The second part of the tool automatically reads the annotations from the image and tabulates them in a CSV file, along with picture date, time and GPS coordinates. The SpotCard also provides a convenient scale bar and coordinate location within the image for the flower itself, enabling automated measurement of floral traits such as area and perimeter.</p></sec><sec><title>Conclusions</title><p id="Par3">This tool is shown to read annotations with a high degree of accuracy and at a speed greatly faster than manual transcription. It includes the ability to read the date and time of the photograph, as well as GPS location. It is an open-source ImageJ/Fiji macro and is available online. Its use requires no knowledge of the ImageJ macro coding language, and it is therefore well suited to all researchers taking pictures in the field.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s13007-019-0403-2) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Image analysis</kwd><kwd>Automated analysis</kwd><kwd>Field data collection</kwd><kwd>ImageJ</kwd><kwd>Fiji</kwd></kwd-group><funding-group><award-group><funding-source><institution-wrap><institution-id institution-id-type="FundRef">http://dx.doi.org/10.13039/501100000268</institution-id><institution>Biotechnology and Biological Sciences Research Council</institution></institution-wrap></funding-source><award-id>BB/M011194/1</award-id></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2019</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par4">In many experiments, it is necessary to take photographs of biological samples. These photographs are often used for subsequent trait analysis which would be time-consuming to perform in the field, such as flower number in inflorescences or size and perimeter of flowers in pollinator experiments. It is also often necessary to record further information about the specimen in question, such as sample number, biological replicate or cultivar. Traditionally, this information has been recorded either by writing it on a piece of paper to be included in the photograph, or by making notes in a notebook or on a laptop or tablet and correlating them to the image filename. Such recording is time-consuming when in the field, requiring switching from a camera to pen and paper or a device, and requires subsequent laborious and error-prone manual transcription. Separating data capture from image capture generates the risk of mismatched data at a later stage. Various software packages exist for collecting field data (see Field Book [<xref ref-type="bibr" rid="CR1">1</xref>], PhenoBook [<xref ref-type="bibr" rid="CR2">2</xref>] or The Phenotyper [<xref ref-type="bibr" rid="CR3">3</xref>] as three examples) but these are usually geared towards replicating the functionality of a notebook in a way that makes later processing less time-consuming.</p><p id="Par5">Software to automate image analysis is widespread, and numerous scripts exist to measure a diverse range of plant characteristics (for examples, see [<xref ref-type="bibr" rid="CR4">4</xref>&#x02013;<xref ref-type="bibr" rid="CR6">6</xref>]). However, such tools can only read and measure plant characteristics, and cannot extract information about the plant&#x02019;s genotype, growing conditions etc.; if such information is required, it must be manually correlated to plant characteristic data at a later date.</p><p id="Par6">The motivation behind SpotCard, the tool presented here, is to inextricably link plant and information within the same image in a way that reduces labour and transcription errors. &#x02018;Mark Sense&#x02019; (also known as &#x02018;Optical Mark Recognition&#x02019;), the technique behind SpotCard, has been in use since 1937, when IBM introduced the IBM 805 Test Scoring Machine [<xref ref-type="bibr" rid="CR7">7</xref>]. This machine detected pencil marks on paper; similar systems are widely in use today to assist in processing multiple-choice tests.</p><p id="Par7">SpotCard is easy to use, has a potentially wide application area, and requires no learning of new field notebook software, design, or scripting languages. The card itself provides a convenient scale bar and a coordinate location within the image for the flower itself, and measurement of petal area or perimeter is incorporated in the macro. This allows for a one-pass process of information extraction and measurement.</p><p id="Par8">Producing, using and processing SpotCard requires no specialist equipment, needing only a computer, printer, tape and glue to produce, a camera and dry-wipe pen in the field and a computer for later processing. If the camera features GPS capability (widespread in modern cameras, including Apple iPhone since 2008 and Samsung Galaxy since 2010), location information is embedded into the picture and can be read as part of the SpotCard processing. Such GPS information is generally accurate to approximately 5 m [<xref ref-type="bibr" rid="CR8">8</xref>]. Spotcard therefore integrates notebook, camera and GPS unit into one easy-to-use tool, speeding up both data capture and data processing.</p><p id="Par9">Although developed to aid in measuring flower diameter and perimeter, we see no reason why this tool could not be used when photographing whole plants, portions of plants or slow-moving animals, or in any other situation where several similar annotated images are required.</p></sec><sec id="Sec2"><title>Implementation</title><p id="Par10">Spotcard is implemented as a pair of macros in Fiji [<xref ref-type="bibr" rid="CR9">9</xref>]. Fiji is one of the leading open-source image analysis tools, being a distribution of ImageJ [<xref ref-type="bibr" rid="CR10">10</xref>], the widely used image analysis software. It is simple to install a macro in Fiji, and users can therefore extend its core functionality.</p><sec id="Sec3"><title>Producing the SpotCard</title><p id="Par11">SpotCards can be created using the SpotCard_Create.ijm macro&#x000a0;(Additional file <xref rid="MOESM1" ref-type="media">1</xref>). This allows the user to specify the list of categories and values to display on the SpotCard. The macro only asks for three category/value sets at a time, as otherwise the input dialog would become unwieldy. There is no limit within the software to the number of categories and values which can be displayed on the card, although its physical size could become impractical beyond around nine categories or more than around 15 values within a category. (SpotCards are relatively compact: the SpotCard shown in Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref> (set up for a 20&#x000a0;mm diameter flower and contaning two &#x02018;standard&#x02019; category/value sets and one &#x02018;integer&#x02019; set from 0 to 99) is 62&#x000a0;mm wide &#x000d7; 56&#x000a0;mm high; adding a further three &#x02018;standard&#x02019; category/value sets would make it 86&#x000a0;mm wide &#x000d7; 68&#x000a0;mm high).<fig id="Fig1"><label>Fig. 1</label><caption><p>An example SpotCard. This SpotCard contains values in three categories: Variety (values A, B, C, D), Flower (values 1, 2, 3), and Plant (values 0&#x02013;99, with the top row being ten and the bottom row being units). The line descending from the top of the card is an indicator for where to cut the slit for the plant stem</p></caption><graphic xlink:href="13007_2019_403_Fig1_HTML" id="MO1"/></fig></p><p id="Par12">When creating a SpotCard, the user should first specify the approximate diameter of the flower with which the SpotCard is to be used, with preset options ranging from 20 to 100&#x000a0;mm (though smaller or larger options could be specified by changing the macro code). If the SpotCard is not to be used with specific flowers, this can be set to &#x02018;no flower&#x02019;.</p><p id="Par13">The user then inputs pairs of categories and values. Categories are simple text-based descriptions of the lists of spots (e.g. &#x02018;Variety&#x02019;, &#x02018;Plant number&#x02019; etc).</p><p id="Par14">There are two ways of specifying values. The first is a simple comma-separated list (e.g. &#x02018;1, 2, 3, 4&#x02019; or &#x02018;A, B, C, D&#x02019;). These will appear on the SpotCard as individual spots, with a value assigned to each. The second way of specifying values is useful when the user wishes to record a large series of integers which it would otherwise be impractical to record with single values (e.g. numbers from 1 to 999). In this case, the value is entered as a range, starting with 0 and ending with a series of 9&#x000a0;s (e.g. 0&#x02013;999). The macro will output one row of spots for units, one for 10&#x000a0;s, one for 100&#x000a0;s, etc., all within the same category. Integers within the range can then be specified by marking off their individual digits (e.g. to indicate 532, colour the 5 in the 100&#x000a0;s row, the 3 in the 10&#x000a0;s row and the 2 in the units row).</p><p id="Par15">Once categories and values have been specified, the macro creates a TIF image file of the SpotCard. Category names appear above the spots. If the &#x02018;Flower diameter&#x02019; value has been set earlier, the sets of spots are arranged around a space in the centre of the SpotCard into which a slit can be cut, so the SpotCard can be slid over the stem of the flower being photographed. Otherwise, the sets are positioned one above the other.</p><p id="Par16">The macro then adds four dots in the corners of the SpotCard, which enable recognition of the SpotCard within the photograph. The default colour for these dots is blue, as it is a colour found rarely in plants, but this can be changed to pink when the SpotCard is to be used to capture information about a blue sample. Each corner dot has a different coloured centre, which allows the processing script to determine the orientation of the SpotCard.</p><p id="Par17">Alongside the TIF file, the macro creates a configuration file for the SpotCard. This file contains information about the X and Y coordinates of the spots, the spot width and height, the diameter of the flower with which the SpotCard is to be used, the spot values, the spot categories, the original width and height of the SpotCard, and the flower centroid Y coordinate. (The flower centroid X coordinate is always half the width of the SpotCard.) This file is read by the second macro and used to locate the positions of the spots and the flower.</p><p id="Par18">The user can then print the SpotCard. To make the spots wipe-clean, the card can be laminated; however, reflections from the laminate can interfere with flower trait measurement. Covering the spots with a single layer of sticky tape and affixing matt black card behind the flower area, as in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>, mitigates this.<fig id="Fig2"><label>Fig. 2</label><caption><p>An example image using a SpotCard. The SpotCard can be used at any rotation angle, allowing for easy positioning. Note the tape, to make the spots wipe-clean, and the matt black card behind the flower, reducing reflections which interfere with flower size detection</p></caption><graphic xlink:href="13007_2019_403_Fig2_HTML" id="MO2"/></fig></p></sec><sec id="Sec4"><title>Using the SpotCard</title><p id="Par19">Values are recorded in the spots by colouring them in using a black dry-wipe marker, which requires no solvent to erase. If the card is being used to photograph a flower, the card is then slotted over the stem and the photograph taken. (See Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> for an example.)</p><p id="Par20">Photographs of plants can include the SpotCard in any orientation. The only constraint is that the SpotCard must be photographed facing directly towards the camera, as the detection macro does not apply any affine transformations to the detected images. However, spot data are still processed correctly even when the SpotCard is tilted up to around 18 degrees around any axis; in practice, it is simple to hold it flatter than this. To facilitate correct processing, corner dots must each be over around 60&#x000a0;px in diameter in the photograph; in practice, if using similar SpotCard positioning to the sample data sets, this equates to images of approximately 1.2 megapixels, easily within the range of modern digital cameras and phones.</p></sec><sec id="Sec5"><title>Processing</title><p id="Par21">Once the user has taken photographs, the SpotCard_Analyse.ijm macro&#x000a0;(Additional file <xref rid="MOESM2" ref-type="media">2</xref>) can be used to detect spot values. This macro uses color thresholding on a brightness- and contrast-adjusted copy of the image to detect the four largest blue areas (which should be the four corner dots). Once the four areas have been located, their centres are examined to detect the colours. The angles of the diagonals of the SpotCards are calculated (i.e. red-centre to black-centre; green-centre to white-centre) and these used to rotate the image so that the corner dot with the red centre is at the top left. The image is then cropped to the blue dots and checked to confirm that the red dot is at the top left of the image. (Thus far, the configuration file has not been used.)</p><p id="Par22">The macro then loops through the positions of all the spots on the image, as specified by the configuration file. For each spot, a selection corresponding to 25% of the spot width and height is examined, which allows for small discrepancies in the positioning of the SpotCard (as it is difficult to take pictures which are exactly face on without using a positioning rig for the camera, flower and SpotCard). The mean gray value is read for a brightness- and contrast-adjusted selection, with a low reading indicating that the spot has been coloured, and a high reading indicating that the spot has been left blank.</p><p id="Par23">Additional flower measurements such as area, perimeter etc. can be performed after the spots have been read. Such analysis can output data in SI units of length and area, rather than simply pixels, as the configuration file gives information which can be used to determine the width and height in millimetres of the SpotCard, thus providing a convenient way of determining the scale of the photograph without including a separate scale bar. The SpotCard_Analyse.ijm code includes area and perimeter recording by way of example, and this has been performed for Sample Data Set 1. Reflections from the printed card may interfere with data detection (as was the case in Sample Data Set 2); affixing matt black card to the area of the SpotCard behind the flower can mitigate this (see Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). Cropped copies of the flower image and a flood-filled copy of the area used for measurement can be saved for visual confirmation of flower detection accuracy (for examples, see Sample Data Set 1 Flowers and Sample Data Set 1 Flood Fills, available on GitHub at <ext-link ext-link-type="uri" xlink:href="https://github.com/GloverLab/SpotCard">https://github.com/GloverLab/SpotCard</ext-link>).</p><p id="Par24">Once all measuring is complete, the macro saves its results to a text file in CSV format, which can be processed in a variety of other software. Also saved are image filename and, if the EXIF reader plugin (<ext-link ext-link-type="uri" xlink:href="https://imagej.nih.gov/ij/plugins/exif-reader.html">https://imagej.nih.gov/ij/plugins/exif-reader.html</ext-link>) is installed, the date and time the image was taken, and the latitude, longitude and altitude at which the image was taken.</p><p id="Par25">Regular error checking is used throughout the processing script to prevent the reporting of erroneous results. If an error is detected when processing an image, the script will stop processing that image, record the error condition in the output file, and move on to the next image. These checks include:<list list-type="bullet"><list-item><p id="Par26">Verifying that the lengths of the diagonals between opposing corner dots are within 5% of each other. In practice, this equates to a tilt of 18 degrees around the diagonal of the SpotCard relative to the camera, and it is easy to hold the SpotCard flatter than this.</p></list-item><list-item><p id="Par27">Verifying the aspect ratio of the image once cropped to the corner dots matches the configuration file; the recorded aspect ratio and theoretical aspect ratio should differ by no more than 5%. Again, this equates to an 18-degree tilt around the x or y axes of the SpotCard relative to the camera, and it is easy to hold the SpotCard flatter than this.</p></list-item><list-item><p id="Par28">Verifying that, once the image has been rotated and cropped, the central portion of the top left dot is red. (This checks that the SpotCard has been correctly oriented.)</p></list-item></list>
</p></sec></sec><sec id="Sec6"><title>Results</title><p id="Par29">We present two sample sets of results. Processing was performed on a MacBook Pro with a 3.1 GHz Intel Core I7 processor and 8&#x000a0;GB RAM. Sample data sets and the Flowers and Flood Fills images for Sample Data Set 1 are available on GitHub at <ext-link ext-link-type="uri" xlink:href="https://github.com/GloverLab/SpotCard">https://github.com/GloverLab/SpotCard</ext-link>; sample SpotCard images, configuration files and results can be found in Additional files <xref rid="MOESM3" ref-type="media">3</xref>, <xref rid="MOESM4" ref-type="media">4</xref>, <xref rid="MOESM5" ref-type="media">5</xref>, <xref rid="MOESM6" ref-type="media">6</xref>, <xref rid="MOESM7" ref-type="media">7</xref>, <xref rid="MOESM8" ref-type="media">8</xref>).</p><p id="Par30">The first set of results (SampleDataSet1Results.csv) comes from running the detection macro on a set of 75 images of chrysanthemum flowerheads (Sample Data Set 1), taken with an Apple iPhone X. Spots were made wipe-clean by application of clear gloss tape; matt black card was affixed to the area of the SpotCard behind the flower to mitigate reflections causing problems in measurement of flowerhead area and perimeter. The macro took 153&#x000a0;s (1.96 s/image) to correctly process all images with no errors, including flowerhead area and perimeter measurement. Cropped copies of the flowerhead image and a flood-filled copy of the area used for measurement were also saved (Sample Data Set 1 Flowers and Sample Data Set 1 Flood Fills, respectively); visual inspection of these images shows flood-filled areas match the flowerhead images well.</p><p id="Par31">The second set of results (SampleDataSet2Results.csv) comes from running the detection macro on a set of 256 images of strawberry flowers using a fully laminated SpotCard (Sample Data Set 2), taken with an Apple iPhone 8. The macro took 7&#x000a0;min 31&#x000a0;s (1.76&#x000a0;s/image) to correctly read spot data from all except four images, not including flower and perimeter area. Examining these images after processing revealed that one was a badly taken photograph where one corner dot on the SpotCard was obscured by a leaf (Image00094.jpg), one had extraneous blue items in the image (Image00119.jpg), one had reflections on the laminated SpotCard which interfered with the detection (Image00112.jpg), and one had a badly marked spot (Image00151.jpg). All of these four errors could easily be identified from the CSV of results: errors are displayed in the value columns for the first three, and the fourth has a missing value. These images could easily be reprocessed manually or discarded. Flower and perimeter area was not processed for these cards as reflections from the lamination interfered with flower detection.</p><p id="Par32">By comparison, manually reading the filenames and four sets of spot data from all 256 images in Sample Data Set 2 and transcribing them into a spreadsheet took 39&#x000a0;min 40&#x000a0;s (9.3 s/image), five times longer than detection with the macro. There were two transcription errors in the 1024 individual spot entries recorded, a rate of 0.2%. It was impossible to identify these transcription errors by simple inspection of the data, as they were both simple numeric changes (a 1 where there should have been a 2, and a 7 where there should have been an 8). Double-checking would nearly double the time per image and would be necessary to reduce incorrect transcription as a result of human error. The manual processing also ignored flower area and perimeter, date, time and location information; if these were required, manual processing would take further time.</p></sec><sec id="Sec7"><title>Conclusion</title><p id="Par33">We have presented SpotCard, a tool which can be used to easily record human- and machine-readable information on a reusable card. Programmatically extracting information from this card can cut the time spent processing such information by at least 80%, and can help eliminate transcription errors caused by human processing. Use cases include analysing flower size of different cultivars of plants, for example in pollinator choice experiments, or of different populations of plants of the same species. It would be interesting to build SpotCard detection into software packages which exist to collect field data, as this could speed up data entry and would enable size measurements of photographed flowers to be made within these software packages.</p><sec id="Sec8"><title>Availability and requirements</title><p id="Par34">The tool is open source and available from Github. It requires Fiji [<xref ref-type="bibr" rid="CR9">9</xref>]. Extraction of date, time and GPS coordinates requires the EXIF reader plugin freely available from <ext-link ext-link-type="uri" xlink:href="https://imagej.nih.gov/ij/plugins/exif-reader.html">https://imagej.nih.gov/ij/plugins/exif-reader.html</ext-link>.<list list-type="bullet"><list-item><p id="Par35"><italic>Project name</italic>: SpotCard.</p></list-item><list-item><p id="Par36"><italic>Project home page</italic>: <ext-link ext-link-type="uri" xlink:href="https://github.com/GloverLab/SpotCard">https://github.com/GloverLab/SpotCard</ext-link>.</p></list-item><list-item><p id="Par37"><italic>Operating system(s)</italic>: Any ImageJ/Fiji supports.</p></list-item><list-item><p id="Par38"><italic>Programming language</italic>: ImageJ/Fiji macro language.</p></list-item><list-item><p id="Par39"><italic>Other requirements</italic>: Developed on Fiji 1.52i.</p></list-item><list-item><p id="Par40"><italic>License</italic>: BSD.</p></list-item></list></p></sec></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec9"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="13007_2019_403_MOESM1_ESM.ijm"><caption><p><bold>Additional file 1.</bold> The ImageJ macro code to create&#x000a0;SpotCards.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="13007_2019_403_MOESM2_ESM.ijm"><caption><p><bold>Additional file 2.</bold> The ImageJ macro code to read SpotCards.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="13007_2019_403_MOESM3_ESM.tif"><caption><p><bold>Additional file 3.</bold> The SpotCard used for Sample Data Set 1. (Sample data set available on GitHub).</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="13007_2019_403_MOESM4_ESM.txt"><caption><p><bold>Additional file 4.</bold> The configuration file for the SpotCard used in Sample Data Set 1.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="13007_2019_403_MOESM5_ESM.csv"><caption><p><bold>Additional file 5.</bold> The CSV file generated from running SpotCard_Analyse.ijm on Sample Data Set 1. (Sample Data Set 1 Flood Fills and Sample Data Set 1 Flowers are available on GitHub).</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM6"><media xlink:href="13007_2019_403_MOESM6_ESM.tif"><caption><p><bold>Additional file 6.</bold> The SpotCard used for Sample Data Set 2 (available on GitHub).</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM7"><media xlink:href="13007_2019_403_MOESM7_ESM.txt"><caption><p><bold>Additional file 7.</bold> The configuration file for the SpotCard used in Sample Data Set 2.</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM8"><media xlink:href="13007_2019_403_MOESM8_ESM.csv"><caption><p><bold>Additional file 8.</bold> The CSV file generated from running SpotCard_Analyse.ijm on Sample Data Set 2.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><ack><title>Authors' contributions</title><p>HAS conceived and implemented the software; HAS and BJG drafted the paper. Both authors read and approved the final manuscript.</p><sec id="FPar1"><title>Acknowledgements</title><p id="Par49">We would like to acknowledge Dr. Matthew Harper and Dr. Matt Castle for their advice and Erin Cullen for helpful suggestions for improvements. The two anonymous reviewers provided extremely helpful comments. HAS is funded by a grant from the BBSRC Doctoral Training Partnership with the University of Cambridge, number BB/M011194/1.</p></sec><sec id="FPar2"><title>Competing interests</title><p id="Par50">The authors declare that they have no competing interests.</p></sec><sec id="FPar3"><title>Permissions</title><p id="Par51">Plants used for collection of the sample data set are strawberries (<italic>Fragaria x ananassa</italic>) grown on a commercial fruit farm in Cambridge. Use of such plants needs no licenses; permission to work with the plants was kindly granted by the farm owner.</p></sec><sec id="FPar7"><title>Publisher&#x02019;s Note</title><p id="Par59">Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></ack><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rife</surname><given-names>TW</given-names></name><name><surname>Poland</surname><given-names>JA</given-names></name></person-group><article-title>Field book: an open-source application for field data collection on android</article-title><source>Crop Sci</source><year>2014</year><volume>54</volume><fpage>1624</fpage><lpage>1627</lpage><pub-id pub-id-type="doi">10.2135/cropsci2013.08.0579</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Crescente</surname><given-names>JM</given-names></name><name><surname>Guidobaldi</surname><given-names>F</given-names></name><name><surname>Demichelis</surname><given-names>M</given-names></name><name><surname>Formica</surname><given-names>MB</given-names></name><name><surname>Helguera</surname><given-names>M</given-names></name><name><surname>Vanzetti</surname><given-names>LS</given-names></name></person-group><article-title>Phenobook: an open source software for phenotypic data collection</article-title><source>Gigascience</source><year>2017</year><volume>6</volume><issue>4</issue><fpage>1</fpage><lpage>5</lpage><pub-id pub-id-type="doi">10.1093/gigascience/giw019</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>K&#x000f6;hl</surname><given-names>K</given-names></name><name><surname>Gremmels</surname><given-names>J</given-names></name></person-group><article-title>A software tool for the input and management of phenotypic data using personal digital assistants and other mobile devices</article-title><source>Plant Methods</source><year>2015</year><volume>11</volume><issue>1</issue><fpage>25</fpage><pub-id pub-id-type="doi">10.1186/s13007-015-0069-3</pub-id><pub-id pub-id-type="pmid">25866550</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Maloof</surname><given-names>JN</given-names></name><name><surname>Nozue</surname><given-names>K</given-names></name><name><surname>Mumbach</surname><given-names>MR</given-names></name><name><surname>Palmer</surname><given-names>CM</given-names></name></person-group><article-title>LeafJ: an ImageJ plugin for semi-automated leaf shape measurement</article-title><source>J Vis Exp</source><year>2013</year><volume>71</volume><fpage>50028</fpage></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tajima</surname><given-names>R</given-names></name><name><surname>Kato</surname><given-names>Y</given-names></name></person-group><article-title>A quick method to estimate root length in each diameter class using freeware ImageJ</article-title><source>Plant Prod Sci</source><year>2013</year><volume>16</volume><issue>1</issue><fpage>9</fpage><lpage>11</lpage><pub-id pub-id-type="doi">10.1626/pps.16.9</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Valle</surname><given-names>B</given-names></name><name><surname>Simonneau</surname><given-names>T</given-names></name><name><surname>Boulord</surname><given-names>R</given-names></name><name><surname>Sourd</surname><given-names>F</given-names></name><name><surname>Frisson</surname><given-names>T</given-names></name><name><surname>Ryckewaert</surname><given-names>M</given-names></name><name><surname>Hamard</surname><given-names>P</given-names></name><name><surname>Brichet</surname><given-names>N</given-names></name><name><surname>Dauzat</surname><given-names>M</given-names></name><name><surname>Christophe</surname><given-names>A</given-names></name></person-group><article-title>PYM: a new, affordable, image-based method using a Raspberry Pi to phenotype plant leaf area in a wide diversity of environments</article-title><source>Plant Methods</source><year>2017</year><volume>13</volume><issue>1</issue><fpage>98</fpage><pub-id pub-id-type="doi">10.1186/s13007-017-0248-5</pub-id><pub-id pub-id-type="pmid">29151844</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><mixed-citation publication-type="other">IBM100-Automated Test Scoring. <ext-link ext-link-type="uri" xlink:href="http://www-03.ibm.com/ibm/history/ibm100/us/en/icons/testscore/">http://www-03.ibm.com/ibm/history/ibm100/us/en/icons/testscore/</ext-link>. <ext-link ext-link-type="uri" xlink:href="http://www-03.ibm.com/ibm/history/ibm100/us/en/icons/testscore/">http://www-03.ibm.com/ibm/history/ibm100/us/en/icons/testscore/</ext-link>. Accessed 05 Aug. 2018.</mixed-citation></ref><ref id="CR8"><label>8.</label><mixed-citation publication-type="other">van Diggelen F, Enge P. The world&#x02019;s first GPS MOOC and Worldwide Laboratory using Smartphones. In: Proceedings of the 28th international technical meeting of the satellite division of the institute of navigation (ION GNSS+ 2015), 2015. pp. 361&#x02013;369.</mixed-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schindelin</surname><given-names>J</given-names></name><name><surname>Arganda-Carreras</surname><given-names>I</given-names></name><name><surname>Frise</surname><given-names>E</given-names></name><name><surname>Kaynig</surname><given-names>V</given-names></name><name><surname>Longair</surname><given-names>M</given-names></name><name><surname>Pietzsch</surname><given-names>T</given-names></name><name><surname>Preibisch</surname><given-names>S</given-names></name><name><surname>Rueden</surname><given-names>C</given-names></name><name><surname>Saalfeld</surname><given-names>S</given-names></name><name><surname>Schmid</surname><given-names>B</given-names></name><name><surname>Tinevez</surname><given-names>J-Y</given-names></name><name><surname>White</surname><given-names>DJ</given-names></name><name><surname>Hartenstein</surname><given-names>V</given-names></name><name><surname>Eliceiri</surname><given-names>K</given-names></name><name><surname>Tomancak</surname><given-names>P</given-names></name><name><surname>Cardona</surname><given-names>A</given-names></name></person-group><article-title>Fiji: an open-source platform for biological-image analysis</article-title><source>Nat Methods</source><year>2012</year><volume>9</volume><issue>7</issue><fpage>676</fpage><lpage>682</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2019</pub-id><pub-id pub-id-type="pmid">22743772</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Schindelin</surname><given-names>J</given-names></name><name><surname>Rueden</surname><given-names>CT</given-names></name><name><surname>Hiner</surname><given-names>MC</given-names></name><name><surname>Eliceiri</surname><given-names>KW</given-names></name></person-group><article-title>The ImageJ ecosystem: an open platform for biomedical image analysis</article-title><source>Mol Reprod Dev</source><year>2015</year><volume>82</volume><issue>7&#x02013;8</issue><fpage>518</fpage><lpage>529</lpage><pub-id pub-id-type="doi">10.1002/mrd.22489</pub-id><pub-id pub-id-type="pmid">26153368</pub-id></element-citation></ref></ref-list></back></article>