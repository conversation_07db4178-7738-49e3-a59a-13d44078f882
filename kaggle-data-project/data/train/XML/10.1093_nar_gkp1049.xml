<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.0 20120330//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mml="http://www.w3.org/1998/Math/MathML" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//NLM//DTD Journal Publishing DTD v2.3 20070202//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName journalpublishing.dtd?><?SourceDTD.Version 2.3?><?ConverterInfo.XSLTName jp2nlmx2.xsl?><?ConverterInfo.Version 2?><front><?epub November-18-2009?><journal-meta><journal-id journal-id-type="nlm-ta">Nucleic Acids Res</journal-id><journal-id journal-id-type="iso-abbrev">Nucleic Acids Res</journal-id><journal-id journal-id-type="publisher-id">nar</journal-id><journal-id journal-id-type="hwp">nar</journal-id><journal-title-group><journal-title>Nucleic Acids Research</journal-title></journal-title-group><issn pub-type="ppub">0305-1048</issn><issn pub-type="epub">1362-4962</issn><publisher><publisher-name>Oxford University Press</publisher-name></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">2817468</article-id><article-id pub-id-type="pmid">19923231</article-id><article-id pub-id-type="doi">10.1093/nar/gkp1049</article-id><article-id pub-id-type="publisher-id">gkp1049</article-id><article-categories><subj-group subj-group-type="heading"><subject>Computational Biology</subject></subj-group></article-categories><title-group><article-title>GeMMA: functional subfamily classification within superfamilies of predicted protein structural domains</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Lee</surname><given-names>David A.</given-names></name><xref ref-type="corresp" rid="COR1">*</xref></contrib><contrib contrib-type="author"><name><surname>Rentzsch</surname><given-names>Robert</given-names></name></contrib><contrib contrib-type="author"><name><surname>Orengo</surname><given-names>Christine</given-names></name></contrib></contrib-group><aff>University College London &#x02013; Structural and Molecular Biology, London, UK</aff><author-notes><corresp id="COR1">*To whom correspondence should be addressed. Tel: <phone>+44 20 7679 3890</phone>; Fax: <fax>+44 20 7679 7193</fax>; Email: <email><EMAIL></email></corresp><fn id="FN1"><p>The authors wish it to be known that, in their opinion, the first two authors should be regarded as joint First Authors.</p></fn></author-notes><pub-date pub-type="ppub"><month>1</month><year>2010</year></pub-date><pub-date pub-type="epub"><day>18</day><month>11</month><year>2009</year></pub-date><pub-date pub-type="pmc-release"><day>18</day><month>11</month><year>2009</year></pub-date><!-- PMC Release delay is 0 months and 0 days and was based on the
							<pub-date pub-type="epub"/>. --><volume>38</volume><issue>3</issue><fpage>720</fpage><lpage>737</lpage><history><date date-type="received"><day>16</day><month>7</month><year>2009</year></date><date date-type="rev-recd"><day>2</day><month>10</month><year>2009</year></date><date date-type="accepted"><day>27</day><month>10</month><year>2009</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2009. Published by Oxford University Press.</copyright-statement><copyright-year>2009</copyright-year><license license-type="creative-commons" xlink:href="http://creativecommons.org/licenses/by-nc/2.5/uk/"><license-p><!--CREATIVE COMMONS-->This is an Open Access article distributed under the terms of the Creative Commons Attribution Non-Commercial License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by-nc/2.5/uk/">http://creativecommons.org/licenses/by-nc/2.5/uk/</ext-link>) which permits unrestricted non-commercial use, distribution, and reproduction in any medium, provided the original work is properly cited.</license-p></license></permissions><abstract><p>GeMMA (Genome Modelling and Model Annotation) is a new approach to automatic functional subfamily classification within families and superfamilies of protein sequences. A major advantage of GeMMA is its ability to subclassify very large and diverse superfamilies with tens of thousands of members, without the need for an initial multiple sequence alignment. Its performance is shown to be comparable to the established high-performance method SCI-PHY. GeMMA follows an agglomerative clustering protocol that uses existing software for sensitive and accurate multiple sequence alignment and profile&#x02013;profile comparison. The produced subfamilies are shown to be equivalent in quality whether whole protein sequences are used or just the sequences of component predicted structural domains. A faster, heuristic version of GeMMA that also uses distributed computing is shown to maintain the performance levels of the original implementation. The use of GeMMA to increase the functional annotation coverage of functionally diverse Pfam families is demonstrated. It is further shown how GeMMA clusters can help to predict the impact of experimentally determining a protein domain structure on comparative protein modelling coverage, in the context of structural genomics.</p></abstract></article-meta></front><body><sec><title>INTRODUCTION</title><p>Clustering proteins according to function would be much easier if all proteins were fully functionally annotated. A biologically meaningful clustering might then group together all proteins with the same function that could be shown to be homologous to each other. The vast majority of proteins in a genome, however, do not have the highest quality experimentally characterized and traceable author statement annotations (<xref ref-type="bibr" rid="B1">1</xref>). Computational methods exist for protein function prediction but many proteins still lack even an inferred functional annotation and many of the annotations that are available are not very specific. Furthermore, estimates of the error rate for the annotation of complete genomes vary from &#x0003c;5 to &#x0003e;40% depending on the types of function (<xref ref-type="bibr" rid="B2">2</xref>,<xref ref-type="bibr" rid="B3">3</xref>).</p><p>Gene3D (<xref ref-type="bibr" rid="B4">4</xref>), derived from the CATH classification of protein structure (<xref ref-type="bibr" rid="B5">5</xref>), and Superfamily (<xref ref-type="bibr" rid="B6">6</xref>), derived from the SCOP classification of protein structure (<xref ref-type="bibr" rid="B7">7</xref>), contain predicted protein domain sequences assigned to structural domain superfamilies. These superfamilies can show homology beyond the sensitivity of sequence comparison methods. A very general functional annotation is assigned to each superfamily; however, the domain sequences that they contain are not divided into more specific functional subfamilies. If they were, much greater insights might be gained into the nature and evolution of protein function. Potential applications of this subfamily classification could be in protein molecular function prediction, identification and characterization of protein active sites, provision of sequence alignments for homology modelling in the twilight zone, and phylogenetic profiling. In addition, structural genomics targets could be selected to improve the structural coverage of protein function (<xref ref-type="bibr" rid="B8">8</xref>,<xref ref-type="bibr" rid="B9">9</xref>).</p><p>There has been much progress in the last few years in the development of computational methods for protein function prediction. The main approach relies on the fundamental notion of inheriting functional annotations from a sequence homologue. However, it is difficult to establish what the necessary level of similarity is, and very similar sequences or even clear homologues are often not available. There are three main types of methods that attempt to overcome these difficulties and that are appropriate to function prediction within superfamilies: phylogenomics; pattern recognition; and clustering, all of which have been reviewed recently (<xref ref-type="bibr" rid="B10">10</xref>).</p><p>Phylogenomics (<xref ref-type="bibr" rid="B11">11</xref>) uses the evolutionary relationships within a family of proteins to improve the accuracy of functional annotation transfer. All identifiable homologues of a query sequence are aligned, a phylogenetic tree is built and all bifurcations are marked, by reference to a taxonomic tree, as duplication events giving rise to paralogues or speciation events giving rise to orthologues (reconciliation). Orthologues generally retain the same molecular function, while paralogues are often free to evolve new functions. SIFTER (<xref ref-type="bibr" rid="B12">12</xref>) is a method designed to inherit GO annotations from orthologues and inparalogues within a Pfam (<xref ref-type="bibr" rid="B13">13</xref>) domain family. Inparalogues are paralogues within the same genome that arose from a gene duplication event after speciation. These tend to be more functionally similar than outparalogues that arose from a gene duplication event in an ancestral species.</p><p>Pattern recognition methods classify proteins using locally conserved sequence patterns. This contrasts with phylogenomics, which typically uses whole protein sequences. The classic patterns used are PROSITE motifs (<xref ref-type="bibr" rid="B14">14</xref>), typically just a few amino acids long and stored in the form of regular expressions. PRINTS (<xref ref-type="bibr" rid="B15">15</xref>) uses discontinuous profiles referred to as &#x02018;fingerprints&#x02019; that are similar to a number of PROSITE patterns spread out along a protein sequence. Most resources developed today, however, use profiles that represent protein functional domains. ProDom (<xref ref-type="bibr" rid="B16">16</xref>) uses profiles in the form of PSI-BLAST position-specific scoring matrices (PSSMs), while Pfam (<xref ref-type="bibr" rid="B13">13</xref>), SMART (<xref ref-type="bibr" rid="B17">17</xref>), PANTHER (<xref ref-type="bibr" rid="B18">18</xref>), PIRSF (<xref ref-type="bibr" rid="B19">19</xref>) and TIGRFAMs (<xref ref-type="bibr" rid="B20">20</xref>) use hidden Markov models (HMMs).</p><p>Pfam is a comprehensive collection of protein families that is, amongst many other applications, extensively used in structural genomics. However, as is common with many functional family resources, these are generally families of paralogues and show some diversification of function. Some approaches have been developed to address the subdivision of protein functional families into functional subfamilies. Funshift (<xref ref-type="bibr" rid="B21">21</xref>), for example, is an automatic entropy-based approach that uses Rate Shifting Sites and Conservation Shifting Sites to subdivide Pfam families into subfamilies whose functions are likely to have shifted away from those of other subfamilies. PANTHER on the other hand relies on expert curation to divide families into subfamilies, which requires broad expertise and is time consuming.</p><p>Clustering can be used to group together sequences based on some measure of similarity. If it is assumed that the obtained clusters are functionally pure then sequences without annotations may inherit from annotated members of the same cluster. ProtoNet (<xref ref-type="bibr" rid="B22">22</xref>) aims to cluster the whole of protein sequence space using a very efficient algorithm (<xref ref-type="bibr" rid="B23">23</xref>). A hierarchical tree of clusters is automatically generated, where the lower a cluster is situated in the tree the smaller it is and the more similar are its proteins to each other. The user is required to browse the clustering hierarchy to identify the level most appropriate to their requirements. The CluSTr database (<xref ref-type="bibr" rid="B24">24</xref>) offers a similarly comprehensive and automatic classification of UniProt Knowledgebase (<xref ref-type="bibr" rid="B25">25</xref>) and IPI (<xref ref-type="bibr" rid="B26">26</xref>) proteins into groups of related proteins.</p><p>Similar to the use of evolutionary relationships in phylogenomics, some resources attempt to improve the accuracy of clustering by restricting themselves to orthologues and inparalogues. Notable amongst these are eggNOG (<xref ref-type="bibr" rid="B27">27</xref>), InParanoid (<xref ref-type="bibr" rid="B28">28</xref>) and OrthoMCL (<xref ref-type="bibr" rid="B29">29</xref>). eggNOG clusters orthologues at different levels of taxonomic granularity, allowing the user to choose the most appropriate level. To accommodate the modularity of protein sequences, different domains within a protein are assigned to different orthologous groups. InParanoid specializes in orthologous pairs in eukaryotic model organisms while MultiParanoid (<xref ref-type="bibr" rid="B30">30</xref>) extends these binary relationships to groups. Both eggNOG and InParanoid infer orthology and paralogy using BLAST reciprocal best hits, while OrthoMCL uses the Markov clustering (MCL) algorithm (<xref ref-type="bibr" rid="B31">31</xref>)</p><p>PhyloFacts (<xref ref-type="bibr" rid="B32">32</xref>) is another collection of protein families divided into functional subfamilies, where the latter are represented by subfamily HMMs. It uses an automatic and computationally efficient pipeline to carry out phylogenomic subclassification of the members of protein families. The central algorithm, SCI-PHY (Subfamily Classification in Phylogenomics) (<xref ref-type="bibr" rid="B33">33</xref>), uses a minimum-encoding-cost criterion to automatically determine the number of subfamilies to divide a family into. The method may be regarded as a hybrid of pattern recognition and clustering methods. SCI-PHY, however, in common with phylogenomic methods, requires an accurate multiple alignment of protein sequences as a starting point. Such an alignment, however, cannot be constructed for many of the large domain superfamilies since they have many members and their sequences are typically very diverse. Even if it is technically feasible to force an alignment of all the members of a very large superfamily, the alignment is likely to be erroneous and thus inappropriate as the starting point for SCI-PHY.</p><p>In this article, we present a new automated method, GeMMA (Genome Modelling and Model Annotation), for functional subfamily identification in Gene3D superfamilies of predicted CATH domains (<xref ref-type="bibr" rid="B4">4</xref>). GeMMA may be regarded as another hybrid method for subfamily classification like SCI-PHY, using pattern recognition and clustering, but unlike SCI-PHY and phylogenomic methods it does not require an initial multiple alignment of all sequences that are to be analysed. Furthermore, GeMMA can be &#x02018;trained&#x02019; on high-quality annotated protein families, to derive generalized similarity thresholds for clustering sparsely or low-quality annotated families. Since it is built on top of the CATH classification of protein structures (<xref ref-type="bibr" rid="B5">5</xref>), GeMMA has the potential to be a particularly powerful tool in the study of protein evolution and for target selection in structural genomics. In the CATH database, protein structures in the PDB (<xref ref-type="bibr" rid="B34">34</xref>) are chopped into globular structural domains and these are assigned to very carefully manually curated superfamilies, on the basis of core similarities in their structures. Structure is more conserved than sequence and so these superfamilies can reveal very remote homology that is undetectable by sequence comparison methods. In addition, domains within a CATH superfamily often contain extensive embellishments around the conserved core structure (<xref ref-type="bibr" rid="B35">35</xref>), which further adds to the sequence diversity observed in these families.</p><p>In Gene3D, all UniProt sequences are compared to a large collection of HMMs constructed using all representative CATH domains as their seed sequences. Since CATH domain boundaries are carefully manually checked the boundaries of Gene3D predicted CATH domains are reasonably accurate. This improves the chances of their expression and crystallization for structure determination in structural genomics. The collection of all sequences that match the representative CATH HMMs within a superfamily can be extremely large and diverse. The top 25 superfamilies of predicted CATH domains in Gene3D 7.0 contain &#x0003e;40 000 sequences each, with the largest containing more than 300 000. GeMMA can cope with very large sets of sequences and extreme sequence diversity unlike phylogenomic methods and SCI-PHY, since it is not necessary to generate an alignment of all sequences as the first step in the analysis. Moreover, by dividing proteins up into their component domains, domains from different domain contexts may be easily compared, and the chaining together of unrelated domain sequences during sequence comparison is avoided.</p><p>While there is a shortage of good benchmarks for computational protein function prediction, the SCI-PHY method has been extensively benchmarked (<xref ref-type="bibr" rid="B36">36</xref>). Its performance was compared to the three other sequence-only methods Secator (<xref ref-type="bibr" rid="B37">37</xref>), Ncut (<xref ref-type="bibr" rid="B38">38</xref>) and CD-HIT (<xref ref-type="bibr" rid="B39">39</xref>), and found to be superior. Part of this benchmark was derived from one of the few readily available and high-quality benchmark sets, the protein families in the Structure&#x02013;Function Linkage Database (SFLD) (<xref ref-type="bibr" rid="B40">40</xref>). The latter currently contains six manually curated, mechanistically diverse enzyme superfamilies. These six superfamilies are further divided into a total of 140 annotated subfamilies. Use of the SFLD as a challenging benchmark for function prediction methods has been described previously (<xref ref-type="bibr" rid="B41">41</xref>).</p><p>Since the SFLD families are probably the best curated set to date we chose them as a training set to derive generalized cluster similarity cut-offs for clustering whole protein and domain sequences. This was necessary because family-specific cut-offs cannot be derived in case of sparsely or low-quality annotated families (as in many of the Pfam families). However, at the same time, we felt it would be important to use the most reliable (i.e. SFLD) families in benchmarking, above all because this enables a reliable performance comparison with SCI-PHY. We thus followed a dual strategy: firstly, benchmarking of GeMMA versus SCI-PHY on each individual SFLD family with the respective family not included in the GeMMA training set (&#x02018;leave-one-out&#x02019; approach), and secondly, derivation of a generalized GeMMA cut-off for a larger Pfam benchmark from all six SFLD families. The SFLD benchmark is extended in a logical step-wise manner: first to the analysis of the conserved CATH domains that are predicted in the SFLD whole proteins and then to the whole Gene3D 7.0 superfamilies of these predicted domains. The latter are considerably larger than the subsets found in the SFLD. The success of GeMMA is measured using three previously published scoring functions used to assess the performance of SCI-PHY.</p><p>Subsequent to benchmarking GeMMA on the expert SLFD sequence set, its performance is compared to SCI-PHY again, in a larger, more diverse benchmark set derived from Pfam. This is intended to ensure that the generalized GeMMA threshold derived is broadly applicable and not optimized to just a limited set of small families. A high-throughput version of GeMMA is developed for use on a compute cluster in order to make the analysis of large superfamilies of protein domains possible. Various strategies are employed to speed up this version of the algorithm while the SFLD benchmark is used to ensure there is no deterioration in performance.</p><p>Finally, GeMMA is applied to the analysis of 11 CATH superfamilies selected by the Midwest Center for Structural Genomics (MCSG) for target selection to improve the coverage of structurally under-represented superfamilies in the second phase of the Protein Structure Initiative (PSI-2) (<xref ref-type="bibr" rid="B42">42</xref>). It is demonstrated that GeMMA accurately predicts many more targets for comparative protein modelling that produce acceptable models than are predicted by the commonly used approach (sequence comparison and selection of targets that share at least 30% sequence identity with the available template structures).</p></sec><sec sec-type="materials|methods"><title>MATERIALS AND METHODS</title><p>The GeMMA algorithm is analogous to building a tree by starting at the leaves and working inwards towards the trunk, rather than starting at the trunk and working outwards. It is somewhat similar to the agglomerative clustering approach previously used in the SATCHMO sequence alignment and tree construction method (<xref ref-type="bibr" rid="B43">43</xref>). Most tree-based approaches require an initial multiple alignment of all the sequences that are to be analyzed. However, this becomes impossible as the size and diversity of the sequence set grows above a certain level. GeMMA avoids this problem, since it is unlikely that all of the sequences in a data set will need to be grouped together into a single alignment (and if so in the very last, not the very first iteration). It is thus possible to analyse data sets such as very large Gene3D superfamilies.</p><sec><title>FS-GeMMA</title><p>The basic GeMMA algorithm is referred to as &#x02018;Full Scale&#x02019; or FS-GeMMA to distinguish it from the high-throughput method described later (<xref ref-type="fig" rid="F1">Figure 1</xref>). Put simply, the GeMMA algorithm performs iterative all-against-all profile&#x02013;profile comparison of a set of sequence clusters followed by merging of the most similar clusters and then re-alignment of the newly created (merged) clusters. This is a modular process and in principle any profile&#x02013;profile comparison and sequence alignment method may be used. GeMMA currently uses COMPASS (<xref ref-type="bibr" rid="B44">44</xref>) for profile&#x02013;profile comparison and MAFFT (<xref ref-type="bibr" rid="B45">45</xref>) for sequence alignment, due to the speed and accuracy of these two methods. In addition, the latest version of MAFFT is capable of aligning up to 50 000 sequences, substantially more than many other methods are capable of aligning. Profile&#x02013;profile comparison scores are stored after each iteration to avoid unnecessarily repeating comparisons. COMPASS calculates an <italic>E</italic>-value corresponding to the profile-profile comparison score and this <italic>E</italic>-value is used to monitor the progress of GeMMA. An <italic>E</italic>-value cut-off is used as a termination condition for the algorithm (see below).
<fig id="F1" position="float"><label>Figure 1.</label><caption><p>A flow chart outlining the basic GeMMA method. This low-throughput approach is referred to as &#x02018;Full Scale&#x02019; or &#x02018;FS-GeMMA&#x02019;.</p></caption><graphic xlink:href="gkp1049f1"/></fig></p><p>The output of GeMMA is a set of sequence clusters each in the form of a FASTA format multiple sequence alignment (with a hierarchical tree of these clusters as a by-product). Ideally, the aim of the GeMMA analysis is to partition the sequences in the data set into separate clusters with one cluster representing each function, and each member of a cluster having the same function as all the other members. In the case of convergent evolution to the same function within a data set, multiple subfamilies would be expected for this function.</p></sec><sec><title>Speeding up GeMMA</title><p>GeMMA is a computationally expensive algorithm, with a number of factors effecting the time that is required to complete execution. The total number of sequences determines the number of comparisons that must be made, longer sequences require more time to be compared and aligned; and the larger the clusters get as the GeMMA iterations proceed, the longer the time that is needed to align sequences and compare those alignments. Generally speaking execution on a single processor quickly becomes impractical as the number of protein sequences rises above about one thousand. Therefore, an alternative algorithm is devised for high-throughput analyses using a cluster of compute nodes. The high-throughput version of GeMMA will subsequently be referred to as &#x02018;High Throughput&#x02019; or &#x02018;HT-GeMMA&#x02019;.</p></sec><sec><title>HT-GeMMA</title><p>HT-GeMMA (<xref ref-type="fig" rid="F2">Figure 2</xref>) maintains the overall strategy of merging the most similar clusters first, as implemented in FS-GeMMA, by carrying out a series of stages of iterations where the <italic>E</italic>-value cut-off is progressively stepped up. Therefore, in the first stage all iterations are carried out with an <italic>E</italic>-value cut-off of 1 &#x000d7; 10<sup>&#x02212;80</sup>, in the second stage the <italic>E</italic>-value cut-off is stepped up to 1 &#x000d7; 10<sup>&#x02212;70</sup>, and so on.
<fig id="F2" position="float"><label>Figure 2.</label><caption><p>A flow chart outlining the high-throughput or HT-GeMMA method. Steps within the grey box are executed on the nodes of a compute cluster. Pre-clustering is used to reduce the number of clusters in the initial HT-GeMMA iteration.</p></caption><graphic xlink:href="gkp1049f2"/></fig></p><p>HT-GeMMA achieves high levels of speed-up by distributing most steps of the GeMMA method to nodes on a compute cluster. Some steps must, however, execute on the master node since it is necessary to complete all comparisons in an iteration before merges can take place and also all merges must be completed before the subsequent comparisons in the next iteration can take place. The master node monitors the completion of these steps in the method. A number of other strategies are used to further speed up HT-GeMMA.</p><p>An all-v-all matrix of clusters to be compared is loaded into random access memory (RAM) and this matrix becomes too large for the amount of RAM typically available in modern computers when the number of clusters is above a few thousands. HT-GeMMA is applied in this work to superfamilies containing up to almost 50 000 sequences, and so to avoid exceeding the amount of RAM available a pre-clustering scheme is employed. This also dramatically reduces the total number of comparisons that must be made. Gene3D S30 clusters are chosen as the starting point for pre-clustering. These are multi-linkage clusters constructed with a 30% sequence identity cut-off following an all-by-all pair wise comparison of sequences in a Gene3D superfamily. Many of the sequences within the S30 clusters are likely to be associated with the same molecular function, and the clusters are typically small (the mean and maximum cluster sizes being of the order of 10 and a few 100 sequences, respectively); they are therefore accessible to FS-GeMMA. A simplified example is presented in an <xref ref-type="app" rid="APP1">appendix</xref> to illustrate the reduction in the total number of comparisons that can be achieved by pre-clustering. Further details of the strategies and parameters implemented in the GeMMA algorithm are also included in the <xref ref-type="app" rid="APP1">appendix</xref>. Benchmarking as described below is used to compare the performance of FS-GeMMA to HT-GeMMA, and the results are shown to be very similar.</p></sec><sec><title>Benchmarking and optimization</title><p>FS-GeMMA is applied to an SFLD benchmark similar to that used by Brown <italic>et al.</italic> (<xref ref-type="bibr" rid="B36">36</xref>), where they demonstrate the superior performance of SCI-PHY compared to a number of other approaches to protein subfamily clustering. The same three basic scoring schemes are applied: purity, edit distance, and VI distance (&#x02018;<xref ref-type="app" rid="APP1">Appendix</xref>&#x02019; section).</p></sec><sec><title>Performance score</title><p>It is useful to have a single measure for optimizing GeMMA and comparing its performance to SCI-PHY that captures the desired balance between high sensitivity and high specificity. Edit and VI distances are expressed as a percentage of their initial values for the given data set by multiplying by the scaling factors <italic>c<sub>e</sub></italic> and <italic>c<sub>v</sub></italic> respectively, where
<disp-formula><graphic xlink:href="gkp1049um1"/></disp-formula>
and
<disp-formula><graphic xlink:href="gkp1049um2"/></disp-formula>
Here <italic>e</italic> = edit distance, <italic>v</italic> = VI distance, and <italic>e</italic><sub>0</sub> and <italic>v</italic><sub>0</sub> are the initial values of edit and VI distance. respectively. Initial values of edit and VI distance are calculated after placing each sequence in the data set into a separate subfamily. Then,
<disp-formula><graphic xlink:href="gkp1049um3"/></disp-formula>
where <italic>p</italic> = purity expressed as a percentage.</p><p>Since both edit and VI distance are measures of sensitivity but only purity is a measure of specificity then purity is multiplied by a factor of 2.</p></sec><sec><title>The SFLD benchmark</title><p>The benchmark was derived from the Structure-Function Linkage Database (<xref ref-type="bibr" rid="B40">40</xref>); <ext-link ext-link-type="uri" xlink:href="http://sfld.rbvi.ucsf.edu/">http://sfld.rbvi.ucsf.edu/</ext-link> on 8 January 2009. This database of mechanistically diverse enzyme superfamilies is manually curated and continuously updated and now contains six superfamilies of whole proteins. These are listed in <xref ref-type="table" rid="T1">Table 1</xref>.
<table-wrap id="T1" position="float"><label>Table 1.</label><caption><p>Composition of the SFLD and corresponding SFLD-Gene3D and Gene3D benchmarks</p></caption><table frame="hsides" rules="groups"><thead align="left"><tr><th rowspan="1" colspan="1">SFLD superfamily</th><th rowspan="1" colspan="1">Sequences in the SFLD and SFLD-Gene3D benchmarks</th><th rowspan="1" colspan="1">Annotated sequences (% of total)</th><th rowspan="1" colspan="1">Annotation types (excluding &#x02018;None&#x02019;)</th><th rowspan="1" colspan="1">Conserved CATH domain superfamily</th><th rowspan="1" colspan="1">Domain sequences predicted in Gene3D 7.0</th></tr></thead><tbody align="left"><tr><td rowspan="1" colspan="1">Amidohydrolase</td><td rowspan="1" colspan="1">1693</td><td rowspan="1" colspan="1">802 (47.4)</td><td rowspan="1" colspan="1">35</td><td rowspan="1" colspan="1">***********</td><td rowspan="1" colspan="1">15 932</td></tr><tr><td rowspan="1" colspan="1">Crotonase</td><td rowspan="1" colspan="1">1330</td><td rowspan="1" colspan="1">931 (70.0)</td><td rowspan="1" colspan="1">14</td><td rowspan="1" colspan="1">***********</td><td rowspan="1" colspan="1">19 323</td></tr><tr><td rowspan="1" colspan="1">Enolase</td><td rowspan="1" colspan="1">1556</td><td rowspan="1" colspan="1">1152 (74.0)</td><td rowspan="1" colspan="1">17</td><td rowspan="1" colspan="1">3.20.20.120</td><td rowspan="1" colspan="1">4114</td></tr><tr><td rowspan="1" colspan="1">Haloacid dehalogenase</td><td rowspan="1" colspan="1">1285</td><td rowspan="1" colspan="1">936 (72.8)</td><td rowspan="1" colspan="1">17</td><td rowspan="1" colspan="1">3.40.50.1000</td><td rowspan="1" colspan="1">20 614</td></tr><tr><td rowspan="1" colspan="1">Terpene cyclase</td><td rowspan="1" colspan="1">228</td><td rowspan="1" colspan="1">228 (100.0)</td><td rowspan="1" colspan="1">40</td><td rowspan="1" colspan="1">&#x02013;</td><td rowspan="1" colspan="1">&#x02013;</td></tr><tr><td rowspan="1" colspan="1">Vicinal oxygen chelate</td><td rowspan="1" colspan="1">683</td><td rowspan="1" colspan="1">291 (42.6)</td><td rowspan="1" colspan="1">17</td><td rowspan="1" colspan="1">3.10.180.10</td><td rowspan="1" colspan="1">11 592</td></tr></tbody></table></table-wrap></p></sec><sec><title>The SFLD-Gene3D and Gene3D benchmarks</title><p>A fully conserved CATH domain is found in most of the SFLD superfamilies. For example, a *********** CATH domain is found in all sequences in the Amidohydrolase superfamily (<xref ref-type="table" rid="T1">Table 1</xref>). A new data set is created called the SFLD-Gene3D benchmark, consisting of just the Gene3D 7.0 predicted domain sequences of the conserved domains that are mapped onto SFLD whole proteins. Members of the Vicinal oxygen chelate superfamily are composed of a single CATH domain while the other five superfamilies are multi-domain proteins. A variety of different CATH domains accompany the conserved CATH domain, while the Terpene cyclase superfamily is not fully classified in CATH and must be excluded from the domain based analyses.</p><p>The Gene3D benchmark consists of all predicted CATH domains from the Gene3D 7.0 superfamilies that are identified as being conserved in the SFLD superfamilies and that are included in the SFLD-Gene3D benchmark. Domain predictions are made in Gene3D for all UniProt sequences and there is a considerably larger number of domains than those that are found in the proteins classified in the SFLD. SFLD-Gene3D benchmark sequences are thus subsets of the Gene3D benchmark sequences. Some of the additional domains in the Gene3D benchmark may belong to proteins that have not yet been classified in the SFLD, while others may belong to functional subfamilies that are not in the SFLD and may yet need to be functionally characterized. Although some of the extra protein sequences that domains are derived from have functional annotations, these are not used in the Gene3D benchmark. Only the SFLD annotations are used, since these are the most reliable and allow the results of the Gene3D benchmark to be compared to those of the SFLD and SFLD-Gene3D benchmarks.</p></sec><sec><title>Superfamily-specific and generalized <italic>E</italic>-value cut-offs</title><p>The performance score is used to determine the optimal, family-specific <italic>E</italic>-value cut-off for each SFLD superfamily. Further, a generalized <italic>E</italic>-value cut-off is derived from this training set, based on where the peak average performance is observed. This generalized cut-off is used in applying GeMMA to a larger Pfam benchmark and a set of Gene3D superfamilies that have been selected for structural genomics target selection. SCI-PHY and GeMMA (using both superfamily-specific and generalized <italic>E</italic>-value cut-offs) are compared in the SFLD benchmark. SCI-PHY is then again compared to GeMMA (using the generalized cut-off) in the larger Pfam benchmark. In both comparisons the performance score forms the central measure.</p></sec><sec><title>The Pfam benchmark</title><p>This is a larger and more diverse benchmark than the SFLD benchmark. However, it is not suitable for establishing a generalized <italic>E</italic>-value cut-off for GeMMA, due to the lower level of annotation and the likelihood of these annotations being, in some cases, less accurate. The broad applicability of the GeMMA generalized <italic>E</italic>-value cut-off is tested by comparing the performance of GeMMA and SCI-PHY on this benchmark. Annotations in the form of four-level Enzyme Commission (EC) numbers are used, since these represent annotations of the type and specificity used in the SFLD benchmark and are relatively easy to compare. Families are selected from Pfam 23.0 that contain at least two enzyme types annotated with EC numbers in UniProt (<xref ref-type="bibr" rid="B25">25</xref>). A total of 1741 families are identified and these contain between 5 and 71 535 members each. The largest variety of EC numbers found in a single Pfam family is for family PF00106, the short-chain dehydrogenase family, which is annotated with 87 different EC numbers. The largest Pfam family for which SCI-PHY successfully produces a result contains 29 970 members and so 15 larger families are removed from the benchmark. This appears to be a problem with memory allocation for SCI-PHY and ancillary programs. Furthermore, due to the computational expense of this analysis a representative set of 571 families is selected to constitute the final benchmark, with approximately the same distribution of family size and diversity as is found in the original 1741 families. The mean number of different EC annotations per family in this data set is 3.6.</p><p>The use of Pfam families means that the starting alignments that SCI-PHY requires are available. EC annotations are taken from the Gene3D 7.0 database (<xref ref-type="bibr" rid="B4">4</xref>), which are in turn imported from UniProt. An average of 20.1% of sequences in these 571 Pfam families have an annotation, compared to an average of 64.1% of sequences that are annotated in the SFLD benchmark (and the Pfam annotations are not expected to be as accurate as the SFLD ones). Performance in the Pfam benchmark is assessed using purity, edit and VI distance, and the same performance score that is used in the SFLD benchmark.</p><p>To test whether family size or family diversity have a differential effect on the two methods, the difference in the performance score of GeMMA and SCI-PHY is plotted against Pfam family size and Pfam family diversity in the results section. Family diversity is calculated as the number of multi-linkage clusters at 30% sequence identity, these being obtained from Gene3D 7.0. Although family diversity correlates with family size, the correlation is weak, thus necessitating both plots.</p><p>One important use of the function prediction aspect of GeMMA is to increase the coverage of annotations within a protein family. This not only increases the number of sequences with a putative annotation, but also increases the power of genomic inference methods such as phylogenetic profiling that can be used to predict functional associations between different proteins. The relatively low percentage of functionally annotated sequences in this Pfam data set allows the inheritance of annotation coverage of GeMMA to be compared to SCI-PHY. Firstly, all unannotated sequences within a predicted subfamily are allowed to inherit the annotation of any annotated member of that subfamily. Then the total coverage of all sequences in all Pfam families in the data set is calculated. As well as comparing GeMMA to SCI-PHY, a comparison is made to using multi-linkage clusters at 60% sequence identity (Gene3D S60 clusters), which is considered to be a safe level of similarity for functional inheritance if simple pair wise sequence identity is used (<xref ref-type="bibr" rid="B46 B47 B48">46&#x02013;48</xref>).</p></sec><sec><title>Application of GeMMA to CATH superfamilies targeted by structural genomics</title><p>The 11 superfamilies of predicted CATH domains chosen for target selection by the Midwest Center for Structural Genomics (MCSG) are listed in <xref ref-type="table" rid="T2">Table 2</xref>. MCSG aims to broaden the structural coverage of these large and functionally and structurally diverse superfamilies. Note that the conserved CATH domains from three of the SFLD superfamilies are also found in this data set (***********, 3.40.50.1000 and ***********). These superfamilies are too large and diverse for the construction of single accurate multiple sequence alignments for each superfamily, and are thus not accessible to analysis using SCI-PHY. Analysis is performed using HT-GeMMA with a generalized <italic>E</italic>-value cut-off of 1e<sup>&#x02212;30</sup>, as derived in the SFLD-Gene3D benchmark.
<table-wrap id="T2" position="float"><label>Table 2.</label><caption><p>Composition of the 11 superfamilies of predicted CATH domains chosen for target selection by the Midwest Center for Structural Genomics and analysed using GeMMA and comparative protein modelling</p></caption><table frame="hsides" rules="groups"><thead align="left"><tr><th rowspan="1" colspan="1">Superfamily</th><th rowspan="1" colspan="1">Total sequences</th><th rowspan="1" colspan="1">Annotated sequences (% of total)</th><th rowspan="1" colspan="1">Annotation types</th></tr></thead><tbody align="left"><tr><td rowspan="1" colspan="1">***********</td><td rowspan="1" colspan="1">15 932</td><td rowspan="1" colspan="1">4355 (27.3)</td><td rowspan="1" colspan="1">55</td></tr><tr><td rowspan="1" colspan="1">3.30.450.20</td><td rowspan="1" colspan="1">29 871</td><td rowspan="1" colspan="1">8859 (29.7)</td><td rowspan="1" colspan="1">17</td></tr><tr><td rowspan="1" colspan="1">3.30.450.40</td><td rowspan="1" colspan="1">10 922</td><td rowspan="1" colspan="1">1702 (15.6)</td><td rowspan="1" colspan="1">20</td></tr><tr><td rowspan="1" colspan="1">3.30.930.10</td><td rowspan="1" colspan="1">15 250</td><td rowspan="1" colspan="1">7888 (51.7)</td><td rowspan="1" colspan="1">31</td></tr><tr><td rowspan="1" colspan="1">3.40.30.10</td><td rowspan="1" colspan="1">38 265</td><td rowspan="1" colspan="1">3182 (8.3)</td><td rowspan="1" colspan="1">86</td></tr><tr><td rowspan="1" colspan="1">3.40.50.1000</td><td rowspan="1" colspan="1">20 614</td><td rowspan="1" colspan="1">2240 (10.9)</td><td rowspan="1" colspan="1">80</td></tr><tr><td rowspan="1" colspan="1">3.40.50.1820</td><td rowspan="1" colspan="1">48 416</td><td rowspan="1" colspan="1">3570 (7.4)</td><td rowspan="1" colspan="1">127</td></tr><tr><td rowspan="1" colspan="1">3.40.50.620</td><td rowspan="1" colspan="1">35 974</td><td rowspan="1" colspan="1">12 022 (33.4)</td><td rowspan="1" colspan="1">75</td></tr><tr><td rowspan="1" colspan="1">3.40.630.30</td><td rowspan="1" colspan="1">27 036</td><td rowspan="1" colspan="1">1384 (5.1)</td><td rowspan="1" colspan="1">77</td></tr><tr><td rowspan="1" colspan="1">3.90.1200.10</td><td rowspan="1" colspan="1">5671</td><td rowspan="1" colspan="1">383 (6.8)</td><td rowspan="1" colspan="1">25</td></tr><tr><td rowspan="1" colspan="1">***********</td><td rowspan="1" colspan="1">19 323</td><td rowspan="1" colspan="1">4838 (25.0)</td><td rowspan="1" colspan="1">47</td></tr></tbody></table></table-wrap></p></sec><sec><title>Comparative protein structure modelling</title><p>One of the major goals of structural genomics is to increase the coverage of protein sequences for which reasonably accurate structural models can be built through comparative modelling. A common approach to predicting the coverage that could be achieved by experimentally determining a domain structure is to identify the 30% sequence identity cluster that contains this proposed target and then count the number of sequences within the cluster that would subsequently be targets for comparative modelling. It is generally assumed that a sequence identity of at least 30% between template and modelling target will result in a reasonably accurate model. The proportion of accurate models generated decreases rapidly as the sequence identity falls below 30%. Notably, however, at the same time, the number of predicted homologues for a structural template generally increases rapidly below this threshold. Therefore, a significant number of reasonably accurate models may be built with sequence identities &#x0003c;30%. Many pair-wise sequence identities within GeMMA subfamilies are below 30%, and so GeMMA subfamilies have the potential to predict significantly increased modelling coverage compared to using 30% sequence identity clusters. This of course depends on the quality of models built within GeMMA subfamilies being sufficiently accurate. To assess the increased level of coverage that GeMMA can predict, comparative models are built within subfamilies of the 11 CATH superfamilies and their accuracy is predicted. This test is particularly important for sequence identities below 30%.</p><p>Several methods exist for predicting the accuracy of a comparative model when the true structure is unknown (<xref ref-type="bibr" rid="B49">49</xref>). Comparative models are built in this study using Modeller 9 (<xref ref-type="bibr" rid="B50">50</xref>), which has an in-built model assessment score called GA341. The GA341 score is a nonlinear combination of the percentage sequence identity of the alignment used to build the model, the model compactness, and the <italic>Z</italic>-score for a combined distance and surface statistical potential. A GA341 score of at least 0.6 indicates a reasonably accurate model (<xref ref-type="bibr" rid="B51">51</xref>).</p><p>Comparative models are built using Modeller for all sequences without an experimentally determined structure, within subfamilies of the 11 superfamilies of predicted CATH domains that contain a structural template. For each sequence, a model is built using each template within the same subfamily, and the quality of the models is assessed using the GA341 score. Some subfamilies contain more than one unique template and here a model is built for each sequence using each template. Where multiple models are available for a target sequence, the model with the highest GA341 score is retained.</p></sec></sec><sec><title>RESULTS AND DISCUSSION</title><sec><title>SFLD benchmark</title><p>FS-GeMMA iterations in the SFLD and SFLD-Gene3D benchmark are terminated at regular <italic>E</italic>-value cut-offs ranging from 10<sup>&#x02212;200</sup> to 10<sup>&#x02212;10</sup> and results are calculated. As a general trend, the purity of the resulting clusters (specificity) decreases as the <italic>E</italic>-value cut-off is increased above a certain level, while the edit distance decreases (sensitivity increases) and the VI distance decreases to a minimum and then increases again (sensitivity increases to a maximum and then decreases) as the <italic>E</italic>-value cut-off is increased. Purity is sometimes seen to decrease and then increase again, for example for the Crotonase superfamily in <xref ref-type="fig" rid="F3">Figure 3</xref>(a). This can arise in at least two different ways. Firstly, two impure clusters can be merged together, so that the total proportion of impure clusters decreases. Secondly, a new pure cluster can be created with two annotated members that were previously in separate clusters (and thus were not counted), so that the overall proportion of pure clusters increases. Note also that for the SFLD-Gene3D Vicinal oxygen chelate domains in <xref ref-type="fig" rid="F3">Figure 3</xref>(b) at very low <italic>E</italic>-value cut-offs the purity is 0%. This is because only clusters with at least two annotated members are included in the calculation of purity and at very low <italic>E</italic>-value cut-offs there is no more than one annotated member in each cluster.
<fig id="F3" position="float"><label>Figure 3.</label><caption><p>GeMMA purity, edit distance, VI distance and performance scores at a range of <italic>E</italic>-value cut-offs for (<bold>a</bold>) whole protein sequences in the SFLD benchmark, and (<bold>b</bold>) predicted conserved CATH domain sequences in the SFLD-Gene3D benchmark.</p></caption><graphic xlink:href="gkp1049f3"/></fig></p><p>The highest performance scores are obtained at different <italic>E</italic>-value cut-offs for different superfamilies. For example, the peak for the Amidohydrolase SFLD superfamily in <xref ref-type="fig" rid="F3">Figure 3</xref>(a) is at 10<sup>&#x02212;60</sup> while for the Crotonase SFLD superfamily the peak is at 10<sup>&#x02212;40</sup>. Average performance scores are calculated for the six SFLD superfamilies in <xref ref-type="fig" rid="F3">Figure 3</xref>(a) and the five SFLD-Gene3D superfamilies of conserved domains in <xref ref-type="fig" rid="F3">Figure 3</xref>(b). The average peak performance for the SFLD superfamilies in <xref ref-type="fig" rid="F3">Figure 3</xref>(a) is at an <italic>E</italic>-value cut-off of 10<sup>&#x02212;40</sup>, while for SFLD-Gene3D in <xref ref-type="fig" rid="F3">Figure 3</xref>(b) the peak is at 10<sup>&#x02212;30</sup>. In general, because the SFLD-Gene3D domains have significantly shorter sequences than the whole SFLD proteins, the significance of the profile&#x02013;profile similarities between different subfamilies of each superfamily is reduced for SFLD-Gene3D and the optimal <italic>E</italic>-value cut-off is shifted to a higher value. The SFLD and SFLD-Gene3D benchmarks suggest a generalized <italic>E</italic>-value cut-off of 10<sup>&#x02212;40</sup> for multi-domain proteins and 10<sup>&#x02212;30</sup> for single domains. Performance scores achieved by SCI-PHY and GeMMA on the set of SFLD protein superfamilies (using family-specific <italic>E</italic>-value cut-offs and the generalized <italic>E</italic>-value cut-off) are listed in detail in <xref ref-type="table" rid="T3">Table 3</xref>. <xref ref-type="fig" rid="F3">Figure 3</xref>(b) also suggests the safe initial <italic>E</italic>-value cut-off of 10<sup>&#x02212;80</sup> that is used in HT-GeMMA.
<table-wrap id="T3" position="float"><label>Table 3.</label><caption><p>Performance scores for SCI-PHY, GeMMA with a generalized <italic>E</italic>-value cut-off (GeMMA generalized), and GeMMA with superfamily-specific <italic>E</italic>-value cut-off (GeMMA specific) in the SFLD benchmark</p></caption><table frame="hsides" rules="groups"><thead align="left"><tr><th rowspan="1" colspan="1">Superfamily</th><th rowspan="1" colspan="1">Method</th><th rowspan="1" colspan="1">Performance score</th></tr></thead><tbody align="left"><tr><td rowspan="3" colspan="1">Amidohydrolase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">77.99</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">84.83</td></tr><tr><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">92.77</td></tr><tr><td rowspan="3" colspan="1">Crotonase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">81.29</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">88.25</td></tr><tr><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">88.25</td></tr><tr><td rowspan="3" colspan="1">Enolase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">91.70</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">90.14</td></tr><tr><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">90.59</td></tr><tr><td rowspan="3" colspan="1">Haloacid dehalogenase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">77.18</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">90.70</td></tr><tr><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">98.28</td></tr><tr><td rowspan="3" colspan="1">Terpene cyclase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">54.99</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">53.64</td></tr><tr><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">67.80</td></tr><tr><td rowspan="3" colspan="1">Vicinal oxygen chelate</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">69.02</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">74.97</td></tr><tr><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">84.38</td></tr></tbody></table><table-wrap-foot><fn id="TF1"><p>Note that the generalized GeMMA cut-offs used for each superfamily correspond to the level of peak performance when performance scores are averaged over the remaining five families, respectively (leave-one-out approach).</p></fn></table-wrap-foot></table-wrap></p></sec><sec><title>GeMMA compared to SCI-PHY</title><p>Generally, SCI-PHY appears to be optimized for high specificity (high purity) at the expense of rather low sensitivity (high edit and VI distances) compared to GeMMA, as can be seen in <xref ref-type="fig" rid="F4">Figure 4</xref>. Both <xref ref-type="fig" rid="F4">Figure 4</xref> and the performance scores in <xref ref-type="table" rid="T3">Table 3</xref> indicate that GeMMA usually achieves a greater balance between sensitivity and specificity, which is particularly important in such applications as structural genomics target selection: increased sensitivity generally results in a smaller number of clusters and thus a more manageable number of targets for structure determination. The reduction in the number of clusters when using GeMMA compared to SCI-PHY in the SFLD benchmark may be seen in <xref ref-type="table" rid="T4">Table 4</xref>. Only in one and two out of six cases SCI-PHY yields the lower number of clusters, when GeMMA is used with superfamily-specific and generalized thresholds, respectively.
<fig id="F4" position="float"><label>Figure 4.</label><caption><p>Purity, edit distance and VI distance for GeMMA with generalized (leave-one-out approach) and superfamily-specific <italic>E</italic>-value cut-offs and for SCI-PHY in the SFLD benchmark. Values for edit distance and VI distance for unclustered sequences are the initial values that are used in the calculation of the performance score. For unclustered sequences purity always has a value of zero.</p></caption><graphic xlink:href="gkp1049f4"/></fig>
<table-wrap id="T4" position="float"><label>Table 4.</label><caption><p>Number of clusters generated by SCI-PHY, GeMMA with a generalized <italic>E</italic>-value cut-off (GeMMA generalized), and GeMMA with superfamily-specific <italic>E</italic>-value cut-offs (GeMMA specific) in the SFLD benchmark</p></caption><table frame="hsides" rules="groups"><thead align="left"><tr><th rowspan="1" colspan="1">Superfamily</th><th rowspan="1" colspan="1">Method</th><th rowspan="1" colspan="1">Clusters</th><th rowspan="1" colspan="1">Singletons</th></tr></thead><tbody align="left"><tr><td rowspan="2" colspan="1">Amidohydrolase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">638</td><td rowspan="1" colspan="1">364</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">120</td><td rowspan="1" colspan="1">70</td></tr><tr><td rowspan="1" colspan="1"/><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">304</td><td rowspan="1" colspan="1">132</td></tr><tr><td rowspan="2" colspan="1">Crotonase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">320</td><td rowspan="1" colspan="1">149</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">223</td><td rowspan="1" colspan="1">165</td></tr><tr><td rowspan="1" colspan="1"/><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">223</td><td rowspan="1" colspan="1">165</td></tr><tr><td rowspan="2" colspan="1">Enolase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">201</td><td rowspan="1" colspan="1">75</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">65</td><td rowspan="1" colspan="1">40</td></tr><tr><td rowspan="1" colspan="1"/><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">143</td><td rowspan="1" colspan="1">84</td></tr><tr><td rowspan="2" colspan="1">Haloacid dehalogenase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">332</td><td rowspan="1" colspan="1">181</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">445</td><td rowspan="1" colspan="1">406</td></tr><tr><td rowspan="1" colspan="1"/><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">188</td><td rowspan="1" colspan="1">136</td></tr><tr><td rowspan="2" colspan="1">Terpene cyclase</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">22</td><td rowspan="1" colspan="1">1</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">5</td><td rowspan="1" colspan="1">0</td></tr><tr><td rowspan="1" colspan="1"/><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">89</td><td rowspan="1" colspan="1">55</td></tr><tr><td rowspan="2" colspan="1">Vicinal oxygen chelate</td><td rowspan="1" colspan="1">SCI-PHY</td><td rowspan="1" colspan="1">302</td><td rowspan="1" colspan="1">163</td></tr><tr><td rowspan="1" colspan="1">GeMMA generalized</td><td rowspan="1" colspan="1">361</td><td rowspan="1" colspan="1">307</td></tr><tr><td rowspan="1" colspan="1"/><td rowspan="1" colspan="1">GeMMA specific</td><td rowspan="1" colspan="1">146</td><td rowspan="1" colspan="1">90</td></tr></tbody></table><table-wrap-foot><fn id="TF2"><p>The number of these clusters that are singletons (clusters with only one member) is also given.</p></fn></table-wrap-foot></table-wrap></p><p>It is envisioned, however, that SCI-PHY could potentially be used in conjunction with GeMMA, where GeMMA breaks down a large superfamily into subfamilies that are then accessible to SCI-PHY and the multiple sequence alignment that is required by SCI-PHY is provided by GeMMA. Subdivision by SCI-PHY could be performed within GeMMA subfamilies to improve purity where it is found to be too low.</p></sec><sec><title>Superfamily-specific <italic>E</italic>-value cut-offs compared to a generalized <italic>E</italic>-value cut-off</title><p>The results in <xref ref-type="table" rid="T3">Table 3</xref> suggest that the performance of GeMMA with a generalized <italic>E</italic>-value cut-off is typically quite close to or the same as can be achieved with a family-specific <italic>E</italic>-value cut-off. This can also be seen in <xref ref-type="fig" rid="F3">Figure 3</xref> where the peak in the performance score for each superfamily is quite blunt and close to the position of the generalized <italic>E</italic>-value cut-off. These observations support the use of a generalized <italic>E</italic>-value cut-off for GeMMA. It could be argued that the availability of functional annotations for some members of a superfamily would make it attractive to establish a superfamily-specific cut-off for the analysis of that superfamily. In the case of the Amidohydrolase superfamily, for example, superior performance is achieved with a superfamily-specific cut-off. However, a sufficient level, accuracy, diversity and even distribution of functional annotations will not typically be available to determine a suitable cut-off for most superfamilies that are to be analysed. Inaccuracy, an insufficient level and diversity or a skewed distribution of functional annotations may easily suggest an inappropriate cut-off leading to inaccurate results. Therefore, the generalized <italic>E</italic>-value cut-offs (for whole proteins and domains) derived from the very well annotated SFLD benchmark are recommended for use in most situations.</p></sec><sec><title>High-throughput GeMMA</title><p>The results in <xref ref-type="fig" rid="F5">Figure 5</xref> show the SFLD benchmark being extended from FS-GeMMA applied to SFLD sequences to the type of situation in which GeMMA is intended to be applied, i.e. clustering whole superfamilies of predicted CATH domains using high-throughput computational methods. In <xref ref-type="table" rid="T1">Table 1</xref> it can be seen that relative to the SFLD benchmark superfamilies there is an up to 17-fold increase (for the Vicinal oxygen chelate superfamily) in the number of sequences found in the corresponding Gene3D 7.0 superfamilies.
<fig id="F5" position="float"><label>Figure 5.</label><caption><p>Purity, edit distance and VI distance for FS-GeMMA and HT-GeMMA as the SFLD benchmark is progressively extended from SFLD whole protein to SFLD-Gene3D domain to Gene3D domain sequences. SFLD functional annotations are used throughout with no extra annotations being used in the Gene3D benchmark. Note that the high-throughput method HT-GeMMA is necessary to analyse the (large) Gene3D benchmark sets.</p></caption><graphic xlink:href="gkp1049f5"/></fig></p><p>The step-wise progression in <xref ref-type="fig" rid="F5">Figure 5</xref> starts with a comparison of FS-GeMMA applied to SFLD sequences to FS-GeMMA applied to the conserved SFLD-Gene3D domain sequences. For all three scores the results are very similar, with no overall trend upwards or downwards exhibited for any of the scores. This can also be seen in the performance scores in <xref ref-type="table" rid="T5">Table 5</xref>. It may be concluded that there is sufficient sequence information in the conserved domains alone to reproduce the results that are obtained when analysing the whole protein sequences.
<table-wrap id="T5" position="float"><label>Table 5.</label><caption><p>Performance scores for FS-GeMMA and HT-GeMMA (using superfamily-specific thresholds) as the benchmark is progressively extended from SFLD to SFLD-Gene3D to Gene3D</p></caption><table frame="hsides" rules="groups"><thead align="left"><tr><th rowspan="1" colspan="1">Superfamily</th><th rowspan="1" colspan="1">Method and data set</th><th rowspan="1" colspan="1">Performance score</th></tr></thead><tbody align="left"><tr><td rowspan="4" colspan="1">Amidohydrolase</td><td rowspan="1" colspan="1">FS-GeMMA SFLD</td><td rowspan="1" colspan="1">92.77</td></tr><tr><td rowspan="1" colspan="1">FS-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">91.08</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">92.29</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA Gene3D</td><td rowspan="1" colspan="1">91.10</td></tr><tr><td rowspan="4" colspan="1">Crotonase</td><td rowspan="1" colspan="1">FS-GeMMA SFLD</td><td rowspan="1" colspan="1">88.25</td></tr><tr><td rowspan="1" colspan="1">FS-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">90.87</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">90.42</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA Gene3D</td><td rowspan="1" colspan="1">87.85</td></tr><tr><td rowspan="4" colspan="1">Enolase</td><td rowspan="1" colspan="1">FS-GeMMA SFLD</td><td rowspan="1" colspan="1">90.59</td></tr><tr><td rowspan="1" colspan="1">FS-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">90.80</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">90.88</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA Gene3D</td><td rowspan="1" colspan="1">90.14</td></tr><tr><td rowspan="4" colspan="1">Haloacid dehalogenase</td><td rowspan="1" colspan="1">FS-GeMMA SFLD</td><td rowspan="1" colspan="1">98.28</td></tr><tr><td rowspan="1" colspan="1">FS-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">94.83</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">95.63</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA Gene3D</td><td rowspan="1" colspan="1">92.41</td></tr><tr><td rowspan="4" colspan="1">Vicinal oxygen chelate</td><td rowspan="1" colspan="1">FS-GeMMA SFLD</td><td rowspan="1" colspan="1">84.38</td></tr><tr><td rowspan="1" colspan="1">FS-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">82.95</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA SFLD-Gene3D</td><td rowspan="1" colspan="1">82.53</td></tr><tr><td rowspan="1" colspan="1">HT-GeMMA Gene3D</td><td rowspan="1" colspan="1">77.24</td></tr></tbody></table></table-wrap></p><p>The analysis is then extended to show that HT-GeMMA produces the same or very similar results to FS-GeMMA when applied to the same sets of SFLD-Gene3D domain sequences. Edit distances are consistently lower for HT-GeMMA but the difference is not thought to be significant. Overall performance scores are very similar throughout.</p><p>Finally, the results are shown for HT-GeMMA applied to the Gene3D benchmark of whole superfamilies of predicted CATH domains obtained from Gene3D 7.0. Only the original SFLD annotations are used in this last step of the benchmark even though some additional annotations are available for the extra sequences in Gene3D. Overall, there is a small decrease in the performance score when HT-GeMMA is applied to the much larger Gene3D superfamilies, with purity generally being a little lower and edit and VI distances generally being a little higher. Although the level of decrease in the performance is not large, with performance scores falling by no more than about 7% in the worst case (<xref ref-type="table" rid="T5">Table 5</xref>), and many other methods, such as SCI-PHY, are not applicable to such large and diverse data sets, some possible explanations are put forward. The SFLD superfamilies only contain carefully manually filtered sequences while all available sequences from UniProt are imported into Gene3D and this probably includes some protein fragments and less rigorously validated sequences. In addition, the increased sequence diversity in the Gene3D superfamilies may make these more challenging data sets for GeMMA.</p></sec><sec><title>Pfam benchmark</title><sec><title>GeMMA compared to SCI-PHY</title><p>For both GeMMA with a generalized <italic>E</italic>-value cut-off and SCI-PHY, the majority of performance scores in the Pfam benchmark are found in the top three bins (<xref ref-type="fig" rid="F6">Figure 6</xref>). This suggests a similar level of performance to that seen in the SFLD benchmark, but for a much larger and more diverse set of families. Since the total sums of the performance scores for each method are very similar to each other, neither method is clearly superior to the other (the total for GeMMA is 2.8% higher than that for SCI-PHY). Further, it can be seen in <xref ref-type="fig" rid="F7">Figure 7</xref> that family size and family diversity have no significant differential effect on the two methods.
<fig id="F6" position="float"><label>Figure 6.</label><caption><p>Distribution of performance scores for GeMMA and SCI-PHY in the Pfam benchmark.</p></caption><graphic xlink:href="gkp1049f6"/></fig>
<fig id="F7" position="float"><label>Figure 7.</label><caption><p>Average difference in performance scores between GeMMA and SCI-PHY in the Pfam benchmark (GeMMA score minus SCI-PHY score) versus (<bold>a</bold>) family size and (<bold>b</bold>) family diversity. Family diversity is calculated as the number of 30% sequence identity multi-linkage clusters in the family.</p></caption><graphic xlink:href="gkp1049f7"/></fig></p><p>It is assumed in this benchmark that the often low level of functional annotation and the probable inaccuracy of some annotations has an equal effect on the scoring of both methods. An inadequate level of annotation can result in a score that is higher than it should be because errors within clusters are not detected, while incorrect annotations can result in a score that is lower than it should be. It is assumed that these effects cancel each other out to a certain extent in this benchmark.</p><p>The Pfam families in this benchmark contain multiple types of functional annotation in the form of EC numbers, i.e. multiple subfamilies. Both GeMMA and SCI-PHY are effective in subdividing the annotated sequences into functionally pure subfamilies (<xref ref-type="fig" rid="F8">Figure 8</xref>). SCI-PHY achieves a slightly higher proportion of functionally pure subfamilies overall (&#x0223c;3.1%).
<fig id="F8" position="float"><label>Figure 8.</label><caption><p>The number of annotation types found in Pfam families and the resultant SCI-PHY and GeMMA subfamilies in the Pfam benchmark. Annotation types were counted as the number of four-level EC numbers. Only families and subfamilies containing up to eight different types of annotation are shown.</p></caption><graphic xlink:href="gkp1049f8"/></fig></p></sec><sec sec-type="results"><title>Example results for a Pfam family</title><p>Pfam family PF03372 is a moderately large family in the Pfam benchmark, described in Pfam as an Endonuclease/Exonuclease/phosphatase family, and contains 4955 sequences in the Pfam-A full alignment. This family was not used to train GeMMA nor is it homologous to any of the SFLD superfamilies and thus is chosen to illustrate, in additional detail, the results of GeMMA analysis on a Pfam family. Nineteen different four-level EC numbers are assigned to this family in Gene3D: 2.1.1.14, 2.5.1.6, 2.7.1.33, 2.7.7.49, 3.1.1.29, 3.1.11.2, 3.1.11.6, 3.1.13.4, 3.1.21.1, 3.1.3.n1, 3.1.3.8, 3.1.3.25, 3.1.3.36, 3.1.3.56, 3.1.4.3, 3.1.4.12, 4.2.1.51, 4.2.99.18 and 6.3.5.2. GeMMA achieves a performance score of 80.75 (purity = 70%, edit distance = 33, VI distance = 0.70), while SCI-PHY achieves a performance score of 69.25 (purity = 71%, edit distance = 113, VI distance = 2.56). GeMMA produces 560 subfamilies of which 339 are singletons, while SCI-PHY produces 1028 subfamilies of which 648 are singletons.</p><p>In several cases, GeMMA and SCI-PHY display examples of &#x02018;wrongly&#x02019; merging the same two types of functions. This happens for 3.1.3.36 and 3.1.3.56, i.e. two phosphoric monoester hydrolases with different substrate specificities. Another example is the merging of 3.1.4.3 and 3.1.4.12, i.e. two phosphoric diester hydrolases with different substrate specificities. A third case is the merging of 3.1.11.2 with 3.1.11.6, i.e. two exodeoxyribonucleases producing 5&#x02032;-phosphomonoesters, again with different specificities. Finally, both methods merge 3.1.11.2 with 4.2.99.18, i.e. exodeoxyribonuclease III with DNA-(apurinic or apyrimidinic site) lyase.</p><p>In other cases, only one of the two methods merges sequences of different function. SCI-PHY merges the single instance of 2.5.1.6 (methionine adenosyltransferase) with instances of 3.1.3.56 (inositol-polyphosphate 5-phosphatase) while GeMMA does not. On the other hand, GeMMA displays examples of merging 3.1.3.n1 (another phosphoric monoester hydrolase with different substrate specificity) with both 3.1.3.36 and 3.1.3.56, while SCI-PHY does not. It also sometimes merges 3.1.11.2 with 4.2.99.18 as well as 3.1.11.6 (exodeoxyribonuclease VII) with 4.2.99.18 while SCI-PHY does not.</p><p>As can be seen for both methods from the above analysis, in most cases where different EC numbers are merged in a subfamily the difference in the enzyme reaction is a shift in substrate specificity rather than a more significant shift in enzyme type or mechanism of action. As well as showing in greater detail the result of GeMMA analysis on a Pfam family, this example also supports the possible application of SCI-PHY to GeMMA subfamilies in the analysis of Gene3D superfamilies (where SCI-PHY cannot be used directly on the whole superfamily). In some cases, SCI-PHY may be able to untangle functions that GeMMA has merged together.</p></sec><sec><title>Inheritance of annotations</title><p>Inheritance of functional annotations significantly increases the annotation coverage of families in the Pfam benchmark (<xref ref-type="fig" rid="F9">Figure 9</xref>). Both SCI-PHY and GeMMA show the advantage of using more sophisticated methods of protein function prediction than using a safe pair wise sequence identity cut-off. The greater sensitivity of GeMMA compared to SCI-PHY allows for greater annotation coverage.
<fig id="F9" position="float"><label>Figure 9.</label><caption><p>Inheritance of functional annotations within the Pfam benchmark families. This shows the post-transfer annotation coverage achievable using Gene3D S60 clusters (multi-linkage sequence clusters at 60% sequence identity) and SCI-PHY and GeMMA subfamilies, respectively.</p></caption><graphic xlink:href="gkp1049f9"/></fig></p></sec></sec><sec><title>Comparative protein structure modelling</title><p>This analysis is conducted to help select target subfamilies for structure determination by MCSG in PSI-2. It is not performed using SCI-PHY since the superfamilies of predicted CATH domains are too large and diverse to construct a single accurate multiple sequence alignment for each superfamily (a necessary prerequisite for SCI-PHY analysis). For most of the superfamilies comparative modelling coverage is significantly greater within GeMMA subfamilies than it is within S30 clusters. Almost all models generated within S30 clusters are assessed as being of good quality using the GA341 score but only a slightly lower proportion of models are assessed as being of good quality within GeMMA subfamilies (<xref ref-type="fig" rid="F10">Figure 10</xref>). In the case of superfamily ***********, GeMMA coverage is more than 15 times &#x0003e;S30 coverage while the proportion of good models is 99.8% compared to 100%.
<fig id="F10" position="float"><label>Figure 10.</label><caption><p>Comparative modelling coverage of 11 superfamilies of predicted CATH domains chosen for structural genomics target selection by the Midwest Center for Structural Genomics. Coverage achieved within GeMMA subfamilies is compared to that within Gene3D S30 clusters (multi-linkage clusters at 30% sequence identity). The numbers above the columns are the percentage of good models as determined using the GA341 score incorporated in Modeller.</p></caption><graphic xlink:href="gkp1049f10"/></fig></p><p>S30 clusters are often used to assess the impact on modelling coverage that determining an experimental structure would have or to determine the number of structures that would be needed to provide total modelling coverage of a superfamily. Here, however, we propose the use of GeMMA subfamilies instead, since the use of S30 clusters can significantly underestimate the impact of a structure and overestimate the number of structures that are needed for total coverage. A further advantage of GeMMA subfamilies is that they correspond well to functional subfamilies and could thus help provide a structure for each function within a superfamily at a lower cost to the structural genomics initiative. Some of the superfamilies presented here already have quite good modelling coverage. Greater than 60% modelling coverage of the whole 3.30.930.10 superfamily is found using GeMMA. The coverage of other superfamilies could be greatly improved by identifying the largest GeMMA subfamilies that contain no experimentally solved structure and then targeting members of these subfamilies. A refinement of the targeting might be the selection of interesting or unknown functions associated with the subfamilies.</p></sec></sec><sec><title>FUNDING</title><p>The <funding-source>National Institutes of Health</funding-source> Protein Structure Initiative (Midwest Center for Structural Genomics grant code GM074942 to D.L.); the Experimental Network for Functional Integration EU (to R.R.). Funding for open access charge: Midwest Center for Structural Genomics grant code GM074942.</p><p><italic>Conflict of interest statement</italic>. None declared.</p></sec></body><back><ack><title>ACKNOWLEDGEMENTS</title><p>The authors acknowledge the use of the UCL Legion High Performance Computing facility, and associated services, in the completion of this work.</p></ack><ref-list><title>REFERENCES</title><ref id="B1"><label>1</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lee</surname><given-names>D</given-names></name><name><surname>Redfern</surname><given-names>O</given-names></name><name><surname>Orengo</surname><given-names>C</given-names></name></person-group><article-title>Predicting protein function from sequence and structure</article-title><source>Nat. Rev. Mol. Cell Biol.</source><year>2007</year><volume>8</volume><fpage>995</fpage><lpage>1005</lpage><pub-id pub-id-type="pmid">18037900</pub-id></element-citation></ref><ref id="B2"><label>2</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Brenner</surname><given-names>SE</given-names></name></person-group><article-title>Errors in genome annotation</article-title><source>Trends Genet.</source><year>1999</year><volume>15</volume><fpage>132</fpage><lpage>133</lpage><pub-id pub-id-type="pmid">10203816</pub-id></element-citation></ref><ref id="B3"><label>3</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Devos</surname><given-names>D</given-names></name><name><surname>Valencia</surname><given-names>A</given-names></name></person-group><article-title>Intrinsic errors in genome annotation</article-title><source>Trends Genet.</source><year>2001</year><volume>17</volume><fpage>429</fpage><lpage>431</lpage><pub-id pub-id-type="pmid">11485799</pub-id></element-citation></ref><ref id="B4"><label>4</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yeats</surname><given-names>C</given-names></name><name><surname>Lees</surname><given-names>J</given-names></name><name><surname>Reid</surname><given-names>A</given-names></name><name><surname>Kellam</surname><given-names>P</given-names></name><name><surname>Martin</surname><given-names>N</given-names></name><name><surname>Liu</surname><given-names>X</given-names></name><name><surname>Orengo</surname><given-names>C</given-names></name></person-group><article-title>Gene3D: comprehensive structural and functional annotation of genomes</article-title><source>Nucleic Acids Res.</source><year>2008</year><volume>36</volume><fpage>D414</fpage><lpage>D418</lpage><pub-id pub-id-type="pmid">18032434</pub-id></element-citation></ref><ref id="B5"><label>5</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cuff</surname><given-names>AL</given-names></name><name><surname>Sillitoe</surname><given-names>I</given-names></name><name><surname>Lewis</surname><given-names>T</given-names></name><name><surname>Redfern</surname><given-names>OC</given-names></name><name><surname>Garratt</surname><given-names>R</given-names></name><name><surname>Thornton</surname><given-names>J</given-names></name><name><surname>Orengo</surname><given-names>CA</given-names></name></person-group><article-title>The CATH classification revisited &#x02013; architectures reviewed and new ways to characterize structural divergence in superfamilies</article-title><source>Nucleic Acids Res.</source><year>2009</year><volume>37</volume><fpage>D310</fpage><lpage>D314</lpage><pub-id pub-id-type="pmid">18996897</pub-id></element-citation></ref><ref id="B6"><label>6</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wilson</surname><given-names>D</given-names></name><name><surname>Pethica</surname><given-names>R</given-names></name><name><surname>Zhou</surname><given-names>Y</given-names></name><name><surname>Talbot</surname><given-names>C</given-names></name><name><surname>Vogel</surname><given-names>C</given-names></name><name><surname>Madera</surname><given-names>M</given-names></name><name><surname>Chothia</surname><given-names>C</given-names></name><name><surname>Gough</surname><given-names>J</given-names></name></person-group><article-title>SUPERFAMILY&#x02014;sophisticated comparative genomics, data mining, visualization and phylogeny</article-title><source>Nucleic Acids Res.</source><year>2009</year><volume>37</volume><fpage>D380</fpage><lpage>D386</lpage><pub-id pub-id-type="pmid">19036790</pub-id></element-citation></ref><ref id="B7"><label>7</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Andreeva</surname><given-names>A</given-names></name><name><surname>Howorth</surname><given-names>D</given-names></name><name><surname>Chandonia</surname><given-names>JM</given-names></name><name><surname>Brenner</surname><given-names>SE</given-names></name><name><surname>Jubbard</surname><given-names>TJP</given-names></name><name><surname>Chothia</surname><given-names>C</given-names></name><name><surname>Murzin</surname><given-names>AG</given-names></name></person-group><article-title>Data growth and its impact on the SCOP database: new developments</article-title><source>Nucleic Acids Res.</source><year>2008</year><volume>36</volume><fpage>D419</fpage><lpage>D425</lpage><pub-id pub-id-type="pmid">18000004</pub-id></element-citation></ref><ref id="B8"><label>8</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Friedberg</surname><given-names>I</given-names></name><name><surname>Godzik</surname><given-names>A</given-names></name></person-group><article-title>Functional differentiation of proteins: implications for structural genomics</article-title><source>Structure</source><year>2007</year><volume>15</volume><fpage>405</fpage><lpage>415</lpage><pub-id pub-id-type="pmid">17437713</pub-id></element-citation></ref><ref id="B9"><label>9</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pieper</surname><given-names>U</given-names></name><name><surname>Chiang</surname><given-names>R</given-names></name><name><surname>Seffernick</surname><given-names>JJ</given-names></name><name><surname>Brown</surname><given-names>SD</given-names></name><name><surname>Glasner</surname><given-names>ME</given-names></name><name><surname>Kelly</surname><given-names>L</given-names></name><name><surname>Eswar</surname><given-names>N</given-names></name><name><surname>Sauder</surname><given-names>JM</given-names></name><name><surname>Bonanno</surname><given-names>JB</given-names></name><name><surname>Swaminathan</surname><given-names>S</given-names></name><etal/></person-group><article-title>Target selection and annotation for the structural genomics of the amidohydrolase and enolase superfamilies</article-title><source>J. Struct. Funct. Genomics</source><year>2009</year><volume>10</volume><fpage>107</fpage><lpage>125</lpage><pub-id pub-id-type="pmid">19219566</pub-id></element-citation></ref><ref id="B10"><label>10</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rentzsch</surname><given-names>R</given-names></name><name><surname>Orengo</surname><given-names>CA</given-names></name></person-group><article-title>Protein function prediction &#x02013; the power of multiplicity</article-title><source>Trends Biotechnol.</source><year>2009</year><volume>27</volume><fpage>210</fpage><lpage>219</lpage><pub-id pub-id-type="pmid">19251332</pub-id></element-citation></ref><ref id="B11"><label>11</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eisen</surname><given-names>JA</given-names></name></person-group><article-title>Phylogenomics: improving functional predictions for uncharacterized genes by evolutionary analysis</article-title><source>Genome Res.</source><year>1998</year><volume>8</volume><fpage>163</fpage><lpage>167</lpage><pub-id pub-id-type="pmid">9521918</pub-id></element-citation></ref><ref id="B12"><label>12</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Engelhardt</surname><given-names>BE</given-names></name><name><surname>Jordan</surname><given-names>MI</given-names></name><name><surname>Muratore</surname><given-names>KE</given-names></name><name><surname>Brenner</surname><given-names>SE</given-names></name></person-group><article-title>Protein molecular function prediction by Bayesian phylogenomics</article-title><source>PLoS Comput. Biol.</source><year>2005</year><volume>1</volume><fpage>e45</fpage><pub-id pub-id-type="pmid">16217548</pub-id></element-citation></ref><ref id="B13"><label>13</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Finn</surname><given-names>RD</given-names></name><name><surname>Tate</surname><given-names>J</given-names></name><name><surname>Mistry</surname><given-names>J</given-names></name><name><surname>Coggill</surname><given-names>PC</given-names></name><name><surname>Sammut</surname><given-names>SJ</given-names></name><name><surname>Hotz</surname><given-names>HR</given-names></name><name><surname>Ceric</surname><given-names>G</given-names></name><name><surname>Forslund</surname><given-names>K</given-names></name><name><surname>Eddy</surname><given-names>SR</given-names></name><name><surname>Sonnhammer</surname><given-names>EL</given-names></name><etal/></person-group><article-title>The Pfam protein families database</article-title><source>Nucleic Acids Res.</source><year>2008</year><volume>36</volume><fpage>D281</fpage><lpage>D288</lpage><pub-id pub-id-type="pmid">18039703</pub-id></element-citation></ref><ref id="B14"><label>14</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sigrist</surname><given-names>CJA</given-names></name><name><surname>Cerutti</surname><given-names>L</given-names></name><name><surname>Hulo</surname><given-names>N</given-names></name><name><surname>Gattiker</surname><given-names>A</given-names></name><name><surname>Falquet</surname><given-names>L</given-names></name><name><surname>Pagni</surname><given-names>M</given-names></name><name><surname>Bairoch</surname><given-names>A</given-names></name><name><surname>Bucher</surname><given-names>P</given-names></name></person-group><article-title>PROSITE: a documented database using patterns and profiles as motif descriptors</article-title><source>Brief Bioinform.</source><year>2002</year><volume>3</volume><fpage>265</fpage><lpage>274</lpage><pub-id pub-id-type="pmid">12230035</pub-id></element-citation></ref><ref id="B15"><label>15</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Attwood</surname><given-names>TK</given-names></name><name><surname>Bradley</surname><given-names>P</given-names></name><name><surname>Flower</surname><given-names>DR</given-names></name><name><surname>Gaulton</surname><given-names>A</given-names></name><name><surname>Maudling</surname><given-names>N</given-names></name><name><surname>Mitchell</surname><given-names>AL</given-names></name><name><surname>Moulton</surname><given-names>G</given-names></name><name><surname>Nordle</surname><given-names>A</given-names></name><name><surname>Paine</surname><given-names>K</given-names></name><name><surname>Taylor</surname><given-names>P</given-names></name><etal/></person-group><article-title>PRINTS and its automatic supplement, prePRINTS</article-title><source>Nucleic Acids Res.</source><year>2003</year><volume>31</volume><fpage>400</fpage><lpage>402</lpage><pub-id pub-id-type="pmid">12520033</pub-id></element-citation></ref><ref id="B16"><label>16</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bru</surname><given-names>C</given-names></name><name><surname>Courcelle</surname><given-names>E</given-names></name><name><surname>Carr&#x000e8;re</surname><given-names>S</given-names></name><name><surname>Beausse</surname><given-names>Y</given-names></name><name><surname>Dalmar</surname><given-names>S</given-names></name><name><surname>Kahn</surname><given-names>D</given-names></name></person-group><article-title>The ProDom database of protein domain families: more emphasis on 3D</article-title><source>Nucleic Acids Res.</source><year>2005</year><volume>33</volume><fpage>D212</fpage><lpage>D215</lpage><pub-id pub-id-type="pmid">15608179</pub-id></element-citation></ref><ref id="B17"><label>17</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Letunic</surname><given-names>I</given-names></name><name><surname>Copley</surname><given-names>RR</given-names></name><name><surname>Pils</surname><given-names>B</given-names></name><name><surname>Pinkert</surname><given-names>S</given-names></name><name><surname>Schultz</surname><given-names>J</given-names></name><name><surname>Bork</surname><given-names>P</given-names></name></person-group><article-title>SMART 5: domains in the context of genomes and networks</article-title><source>Nucleic Acids Res.</source><year>2006</year><volume>34</volume><fpage>D257</fpage><lpage>D269</lpage><pub-id pub-id-type="pmid">16381859</pub-id></element-citation></ref><ref id="B18"><label>18</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Thomas</surname><given-names>PD</given-names></name><name><surname>Campbell</surname><given-names>MJ</given-names></name><name><surname>Kejariwal</surname><given-names>A</given-names></name><name><surname>Mi</surname><given-names>H</given-names></name><name><surname>Karlak</surname><given-names>B</given-names></name><name><surname>Daverman</surname><given-names>R</given-names></name><name><surname>Diemer</surname><given-names>K</given-names></name><name><surname>Muruganujan</surname><given-names>A</given-names></name><name><surname>Narechania</surname><given-names>A</given-names></name></person-group><article-title>PANTHER: a library of protein families and subfamilies indexed by function</article-title><source>Genome Res.</source><year>2003</year><volume>13</volume><fpage>2129</fpage><lpage>2141</lpage><pub-id pub-id-type="pmid">12952881</pub-id></element-citation></ref><ref id="B19"><label>19</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>CH</given-names></name><name><surname>Nikolskaya</surname><given-names>A</given-names></name><name><surname>Huang</surname><given-names>H</given-names></name><name><surname>Yeh</surname><given-names>LS</given-names></name><name><surname>Natale</surname><given-names>DA</given-names></name><name><surname>Vinayaka</surname><given-names>CR</given-names></name><name><surname>Hu</surname><given-names>ZZ</given-names></name><name><surname>Mazumder</surname><given-names>R</given-names></name><name><surname>Kumar</surname><given-names>S</given-names></name><name><surname>Kourtesis</surname><given-names>P</given-names></name><etal/></person-group><article-title>PIRSF: family classification system at the Protein Information Resource</article-title><source>Nucleic Acids Res.</source><year>2004</year><volume>32</volume><fpage>D112</fpage><lpage>D114</lpage><pub-id pub-id-type="pmid">14681371</pub-id></element-citation></ref><ref id="B20"><label>20</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Haft</surname><given-names>DH</given-names></name><name><surname>Selengut</surname><given-names>JD</given-names></name><name><surname>White</surname><given-names>O</given-names></name></person-group><article-title>The TIGRFAMs database of protein families</article-title><source>Nucleic Acids Res.</source><year>2003</year><volume>31</volume><fpage>371</fpage><lpage>373</lpage><pub-id pub-id-type="pmid">12520025</pub-id></element-citation></ref><ref id="B21"><label>21</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Abhiman</surname><given-names>S</given-names></name><name><surname>Sonnhammer</surname><given-names>EL</given-names></name></person-group><article-title>FunShift: a database of function shift analysis on protein subfamilies</article-title><source>Nucleic Acids Res.</source><year>2005</year><volume>33</volume><fpage>D197</fpage><lpage>D200</lpage><pub-id pub-id-type="pmid">15608176</pub-id></element-citation></ref><ref id="B22"><label>22</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kaplan</surname><given-names>N</given-names></name><name><surname>Sasson</surname><given-names>O</given-names></name><name><surname>Inbar</surname><given-names>U</given-names></name><name><surname>Friedlich</surname><given-names>M</given-names></name><name><surname>Fromer</surname><given-names>M</given-names></name><name><surname>Fleischer</surname><given-names>H</given-names></name><name><surname>Portugaly</surname><given-names>E</given-names></name><name><surname>Linial</surname><given-names>N</given-names></name><name><surname>Linial</surname><given-names>M</given-names></name></person-group><article-title>ProtoNet 4.0: a hierarchical classification of one million protein sequences</article-title><source>Nucleic Acids Res.</source><year>2005</year><volume>33</volume><fpage>D216</fpage><lpage>D218</lpage><pub-id pub-id-type="pmid">15608180</pub-id></element-citation></ref><ref id="B23"><label>23</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Loewenstein</surname><given-names>Y</given-names></name><name><surname>Portugaly</surname><given-names>E</given-names></name><name><surname>Fromer</surname><given-names>M</given-names></name><name><surname>Linial</surname><given-names>M</given-names></name></person-group><article-title>Efficient algorithms for accurate hierarchical clustering of huge datasets: tackling the entire protein space</article-title><source>Bioinformatics</source><year>2008</year><volume>24</volume><fpage>i41</fpage><lpage>i49</lpage><pub-id pub-id-type="pmid">18586742</pub-id></element-citation></ref><ref id="B24"><label>24</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Petryszak</surname><given-names>R</given-names></name><name><surname>Kretschmann</surname><given-names>E</given-names></name><name><surname>Wieser</surname><given-names>D</given-names></name><name><surname>Apweiler</surname><given-names>R</given-names></name></person-group><article-title>The predictive power of the CluSTr database</article-title><source>Bioinformatics</source><year>2005</year><volume>21</volume><fpage>3604</fpage><lpage>3609</lpage><pub-id pub-id-type="pmid">15961444</pub-id></element-citation></ref><ref id="B25"><label>25</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mulder</surname><given-names>NJ</given-names></name><name><surname>Kersey</surname><given-names>P</given-names></name><name><surname>Pruess</surname><given-names>M</given-names></name><name><surname>Apweiler</surname><given-names>R</given-names></name></person-group><article-title>In silico characterization of proteins: UniProt, InterPro and Integr8</article-title><source>Mol. Biotechnol.</source><year>2008</year><volume>38</volume><fpage>165</fpage><lpage>77</lpage><pub-id pub-id-type="pmid">18219596</pub-id></element-citation></ref><ref id="B26"><label>26</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kersey</surname><given-names>PJ</given-names></name><name><surname>Duarte</surname><given-names>J</given-names></name><name><surname>Williams</surname><given-names>A</given-names></name><name><surname>Karavidopoulou</surname><given-names>Y</given-names></name><name><surname>Birney</surname><given-names>E</given-names></name><name><surname>Apweiler</surname><given-names>R</given-names></name></person-group><article-title>The International Protein Index: an integrated database for proteomics experiments</article-title><source>Proteomics</source><year>2004</year><volume>4</volume><fpage>1985</fpage><lpage>1988</lpage><pub-id pub-id-type="pmid">15221759</pub-id></element-citation></ref><ref id="B27"><label>27</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jensen</surname><given-names>LJ</given-names></name><name><surname>Julien</surname><given-names>P</given-names></name><name><surname>Kuhn</surname><given-names>M</given-names></name><name><surname>von Mering</surname><given-names>C</given-names></name><name><surname>Muller</surname><given-names>J</given-names></name><name><surname>Doerks</surname><given-names>T</given-names></name><name><surname>Bork</surname><given-names>P</given-names></name></person-group><article-title>eggNOG: automated construction and annotation of orthologous groups of genes</article-title><source>Nucleic Acids Res.</source><year>2008</year><volume>36</volume><fpage>D250</fpage><lpage>D254</lpage><pub-id pub-id-type="pmid">17942413</pub-id></element-citation></ref><ref id="B28"><label>28</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>O&#x02019;Brien</surname><given-names>KP</given-names></name><name><surname>Remm</surname><given-names>M</given-names></name><name><surname>Sonnhammer</surname><given-names>EL</given-names></name></person-group><article-title>Inparanoid: a comprehensive database of eukaryotic orthologs</article-title><source>Nucleic Acids Res.</source><year>2005</year><volume>33</volume><fpage>D476</fpage><lpage>D480</lpage><pub-id pub-id-type="pmid">15608241</pub-id></element-citation></ref><ref id="B29"><label>29</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>F</given-names></name><name><surname>Mackey</surname><given-names>AJ</given-names></name><name><surname>Stoeckert</surname><given-names>CJ</given-names><suffix>Jr</suffix></name><name><surname>Roos</surname><given-names>DS</given-names></name></person-group><article-title>OrthoMCL-DB: querying a comprehensive multi-species collection of ortholog groups</article-title><source>Nucleic Acids Res.</source><year>2006</year><volume>34</volume><fpage>D363</fpage><lpage>D368</lpage><pub-id pub-id-type="pmid">16381887</pub-id></element-citation></ref><ref id="B30"><label>30</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alexeyenko</surname><given-names>A</given-names></name><name><surname>Tamas</surname><given-names>I</given-names></name><name><surname>Liu</surname><given-names>G</given-names></name><name><surname>Sonnhammer</surname><given-names>EL</given-names></name></person-group><article-title>Automatic clustering of orthologs and inparalogs shared by multiple proteomes</article-title><source>Bioinformatics</source><year>2006</year><volume>22</volume><fpage>e9</fpage><lpage>e15</lpage><pub-id pub-id-type="pmid">16873526</pub-id></element-citation></ref><ref id="B31"><label>31</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Enright</surname><given-names>AJ</given-names></name><name><surname>Van Dongen</surname><given-names>S</given-names></name><name><surname>Ouzounis</surname><given-names>CA</given-names></name></person-group><article-title>An efficient algorithm for large-scale detection of protein families</article-title><source>Nucleic Acids Res.</source><year>2002</year><volume>30</volume><fpage>1575</fpage><lpage>1584</lpage><pub-id pub-id-type="pmid">11917018</pub-id></element-citation></ref><ref id="B32"><label>32</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Krishnamurthy</surname><given-names>N</given-names></name><name><surname>Brown</surname><given-names>DP</given-names></name><name><surname>Kirshner</surname><given-names>D</given-names></name><name><surname>Sjolander</surname><given-names>K</given-names></name></person-group><article-title>PhyloFacts: an online structural phylogenomic encyclopaedia for protein functional and structural classification</article-title><source>Genome Biol.</source><year>2006</year><volume>7</volume><fpage>R83</fpage><pub-id pub-id-type="pmid">16973001</pub-id></element-citation></ref><ref id="B33"><label>33</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Brown</surname><given-names>DP</given-names></name><name><surname>Krishnamurthy</surname><given-names>N</given-names></name><name><surname>Sjolander</surname><given-names>K</given-names></name></person-group><article-title>Automated protein subfamily identification and classification</article-title><source>PLoS Comput. Biol.</source><year>2007</year><volume>3</volume><fpage>1526</fpage><lpage>1538</lpage></element-citation></ref><ref id="B34"><label>34</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Berman</surname><given-names>HM</given-names></name><name><surname>Westbrook</surname><given-names>J</given-names></name><name><surname>Feng</surname><given-names>Z</given-names></name><name><surname>Gilliland</surname><given-names>G</given-names></name><name><surname>Bhat</surname><given-names>TN</given-names></name><name><surname>Weissig</surname><given-names>H</given-names></name><name><surname>Shindyalov</surname><given-names>IN</given-names></name><name><surname>Bourne</surname><given-names>PE</given-names></name></person-group><article-title>The Protein Data Bank</article-title><source>Nucleic Acids Res.</source><year>2000</year><volume>28</volume><fpage>235</fpage><lpage>242</lpage><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="B35"><label>35</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Reeves</surname><given-names>GA</given-names></name><name><surname>Dallman</surname><given-names>TJ</given-names></name><name><surname>Redfern</surname><given-names>OC</given-names></name><name><surname>Akpor</surname><given-names>A</given-names></name><name><surname>Orengo</surname><given-names>CA</given-names></name></person-group><article-title>Structural diversity of domain superfamilies in the CATH database</article-title><source>J. Mol. Biol.</source><year>2006</year><volume>360</volume><fpage>725</fpage><lpage>741</lpage><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="B36"><label>36</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Godzik</surname><given-names>A</given-names></name><name><surname>Jambon</surname><given-names>M</given-names></name><name><surname>Friedberg</surname><given-names>I</given-names></name></person-group><article-title>Computational protein function prediction: are we making progress?</article-title><source>Cell Mol. Life Sci.</source><year>2007</year><volume>64</volume><fpage>2505</fpage><lpage>2511</lpage><pub-id pub-id-type="pmid">17611711</pub-id></element-citation></ref><ref id="B37"><label>37</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wicker</surname><given-names>N</given-names></name><name><surname>Perrin</surname><given-names>GR</given-names></name><name><surname>Thierry</surname><given-names>JC</given-names></name><name><surname>Poch</surname><given-names>O</given-names></name></person-group><article-title>Secator: A program for inferring protein subfamilies from phylogenetic trees</article-title><source>Mol. Biol. Evol.</source><year>2001</year><volume>18</volume><fpage>1435</fpage><lpage>1441</lpage><pub-id pub-id-type="pmid">11470834</pub-id></element-citation></ref><ref id="B38"><label>38</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Abascal</surname><given-names>F</given-names></name><name><surname>Valencia</surname><given-names>A</given-names></name></person-group><article-title>Clustering of proximal sequence space for the identification of protein families</article-title><source>Bioinformatics</source><year>2002</year><volume>18</volume><fpage>908</fpage><lpage>921</lpage><pub-id pub-id-type="pmid">12117788</pub-id></element-citation></ref><ref id="B39"><label>39</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Li</surname><given-names>W</given-names></name><name><surname>Godzik</surname><given-names>A</given-names></name></person-group><article-title>CD-hit: a fast program for clustering and comparing large sets of protein or nucleotide sequences</article-title><source>Bioinformatics</source><year>2006</year><volume>22</volume><fpage>1658</fpage><lpage>1659</lpage><pub-id pub-id-type="pmid">16731699</pub-id></element-citation></ref><ref id="B40"><label>40</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Pegg</surname><given-names>SC</given-names></name><name><surname>Brown</surname><given-names>SD</given-names></name><name><surname>Ojha</surname><given-names>S</given-names></name><name><surname>Seffernick</surname><given-names>J</given-names></name><name><surname>Meng</surname><given-names>EC</given-names></name><name><surname>Morris</surname><given-names>JH</given-names></name><name><surname>Chang</surname><given-names>PJ</given-names></name><name><surname>Huang</surname><given-names>CC</given-names></name><name><surname>Ferrin</surname><given-names>TE</given-names></name><name><surname>Babbitt</surname><given-names>PC</given-names></name></person-group><article-title>Leveraging enzyme structure&#x02013;function relationships for functional inference and experimental design: the structure&#x02013;function linkage database</article-title><source>Biochem.</source><year>2006</year><volume>45</volume><fpage>2545</fpage><lpage>2555</lpage><pub-id pub-id-type="pmid">16489747</pub-id></element-citation></ref><ref id="B41"><label>41</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Brown</surname><given-names>SD</given-names></name><name><surname>Gerlt</surname><given-names>JA</given-names></name><name><surname>Seffernick</surname><given-names>JL</given-names></name><name><surname>Babbitt</surname><given-names>PC</given-names></name></person-group><article-title>A gold standard set of mechanistically diverse enzyme superfamilies</article-title><source>Genome Biol.</source><year>2006</year><volume>7</volume><fpage>R8</fpage><pub-id pub-id-type="pmid">16507141</pub-id></element-citation></ref><ref id="B42"><label>42</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dessailly</surname><given-names>BH</given-names></name><name><surname>Nair</surname><given-names>R</given-names></name><name><surname>Jaroszewski</surname><given-names>L</given-names></name><name><surname>Fajardo</surname><given-names>JE</given-names></name><name><surname>Kouranov</surname><given-names>A</given-names></name><name><surname>Lee</surname><given-names>D</given-names></name><name><surname>Fiser</surname><given-names>A</given-names></name><name><surname>Godzik</surname><given-names>A</given-names></name><name><surname>Rost</surname><given-names>B</given-names></name><name><surname>Orengo</surname><given-names>C</given-names></name></person-group><article-title>PSI-2: structural genomics to cover protein domain family space</article-title><source>Structure</source><year>2009</year><volume>17</volume><fpage>869</fpage><lpage>881</lpage><pub-id pub-id-type="pmid">19523904</pub-id></element-citation></ref><ref id="B43"><label>43</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Edgar</surname><given-names>RC</given-names></name><name><surname>Sj&#x000f6;lander</surname><given-names>K</given-names></name></person-group><article-title>SATCHMO: sequence alignment and tree construction using hidden Markov models</article-title><source>Bioinformatics</source><year>2003</year><volume>19</volume><fpage>1404</fpage><lpage>1411</lpage><pub-id pub-id-type="pmid">12874053</pub-id></element-citation></ref><ref id="B44"><label>44</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sadreyev</surname><given-names>RI</given-names></name><name><surname>Grishin</surname><given-names>NV</given-names></name></person-group><article-title>COMPASS: a tool for comparison of multiple protein alignments with assessment of statistical significance</article-title><source>J. Mol. Biol.</source><year>2003</year><volume>326</volume><fpage>317</fpage><lpage>336</lpage><pub-id pub-id-type="pmid">12547212</pub-id></element-citation></ref><ref id="B45"><label>45</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Katoh</surname><given-names>K</given-names></name><name><surname>Kuma</surname><given-names>K</given-names></name><name><surname>Toh</surname><given-names>H</given-names></name><name><surname>Miyata</surname><given-names>T</given-names></name></person-group><article-title>MAFFT version 5: improvement in accuracy of multiple sequence alignment</article-title><source>Nucleic Acids Res.</source><year>2005</year><volume>33</volume><fpage>511</fpage><lpage>518</lpage><pub-id pub-id-type="pmid">15661851</pub-id></element-citation></ref><ref id="B46"><label>46</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Addou</surname><given-names>S</given-names></name><name><surname>Rentzsch</surname><given-names>R</given-names></name><name><surname>Lee</surname><given-names>D</given-names></name><name><surname>Orengo</surname><given-names>CA</given-names></name></person-group><article-title>Domain-based and family-specific sequence identity thresholds increase the levels of reliable protein function transfer</article-title><source>J. Mol. Biol.</source><year>2009</year><volume>387</volume><fpage>416</fpage><lpage>430</lpage><pub-id pub-id-type="pmid">19135455</pub-id></element-citation></ref><ref id="B47"><label>47</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rost</surname><given-names>B</given-names></name></person-group><article-title>Enzyme function less conserved than anticipated</article-title><source>J. Mol. Biol.</source><year>2002</year><volume>318</volume><fpage>595</fpage><lpage>608</lpage><pub-id pub-id-type="pmid">12051862</pub-id></element-citation></ref><ref id="B48"><label>48</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tian</surname><given-names>W</given-names></name><name><surname>Skolnick</surname><given-names>J</given-names></name></person-group><article-title>How well is enzyme function conserved as a function of pairwise sequence identity?</article-title><source>J. Mol. Biol.</source><year>2003</year><volume>333</volume><fpage>863</fpage><lpage>882</lpage><pub-id pub-id-type="pmid">14568541</pub-id></element-citation></ref><ref id="B49"><label>49</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eramian</surname><given-names>D</given-names></name><name><surname>Eswar</surname><given-names>N</given-names></name><name><surname>Shen</surname><given-names>MY</given-names></name><name><surname>Sali</surname><given-names>A</given-names></name></person-group><article-title>How well can the accuracy of comparative protein structure models be predicted?</article-title><source>Protein Sci.</source><year>2008</year><volume>17</volume><fpage>1881</fpage><lpage>1893</lpage><pub-id pub-id-type="pmid">18832340</pub-id></element-citation></ref><ref id="B50"><label>50</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sali</surname><given-names>A</given-names></name><name><surname>Blundell</surname><given-names>TL</given-names></name></person-group><article-title>Comparative protein modelling by satisfaction of spatial restraints</article-title><source>J. Mol. Biol.</source><year>1993</year><volume>234</volume><fpage>779</fpage><lpage>815</lpage><pub-id pub-id-type="pmid">8254673</pub-id></element-citation></ref><ref id="B51"><label>51</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>John</surname><given-names>B</given-names></name><name><surname>Sali</surname><given-names>A</given-names></name></person-group><article-title>Comparative protein structure modeling by iterative alignment, model building and model assessment</article-title><source>Nucleic Acids Res.</source><year>2003</year><volume>31</volume><fpage>3982</fpage><lpage>3992</lpage><pub-id pub-id-type="pmid">12853614</pub-id></element-citation></ref></ref-list><app-group><app id="APP1"><title>APPENDIX</title><p><bold>Benchmark measures</bold></p><p>GeMMA is applied to a benchmark similar to that used by Brown <italic>et al.</italic> (<xref ref-type="bibr" rid="B36">36</xref>). The following equations and large parts of the explanatory text are thus directly taken from this publication.</p><p><bold>Purity</bold></p><p>Purity is the percentage of subfamilies within which all annotated members are annotated with the same function. A 100% purity may be attained trivially by having each sequence in a separate subfamily and so only subfamilies with at least two annotated members are included in the calculation of purity.</p><p><bold>Edit distance</bold></p><p>Edit distance is the number of split or merge operations that are required to transform the computed subfamilies (the predicted partition) into the subfamilies that correspond to the available functional annotations (the reference partition). The edit distance between a reference and a predicted partition with clusters <italic>k</italic> and <italic>k&#x02032;</italic>, respectively, is calculated as
<disp-formula><graphic xlink:href="gkp1049um4"/></disp-formula>
where <inline-formula><inline-graphic xlink:href="gkp1049i1.jpg"/></inline-formula> equals 1 if clusters <italic>k</italic> and <italic>k</italic>&#x02032; have items in common, and zero otherwise, and <italic>k</italic> and <italic>k</italic>&#x02032; are the number of clusters in each partition.</p><p><bold>VI distance</bold></p><p>Given two partitions, the VI distance measures the amount of information in each partition that is not shared between them. It is calculated as
<disp-formula><graphic xlink:href="gkp1049um5"/></disp-formula>
where <italic>H</italic> is the entropy of a partition and <italic>I</italic> is the mutual information between two partitions:
<disp-formula><graphic xlink:href="gkp1049um6"/></disp-formula>
<disp-formula><graphic xlink:href="gkp1049um7"/></disp-formula>
Here, <italic>n<sub>k</sub></italic> is the number of items in cluster <italic>k</italic> of partition <italic>S</italic>, <inline-formula><inline-graphic xlink:href="gkp1049i2.jpg"/></inline-formula> is the number of overlapping items between cluster <italic>k</italic> in partition <italic>S</italic> and cluster <italic>k</italic>&#x02032; in partition <italic>S</italic>&#x02032;, <italic>K</italic> and <italic>K</italic>&#x02032; are the total number of clusters in the partitions <italic>S</italic> and <italic>S</italic>&#x02032;, respectively, and <italic>N</italic> is the total number of items in the set. Identical partitions will have both an edit and VI distance of zero.</p><p>Both Edit distance and VI distance penalize over-division as well as mixing of subtypes, but the edit distance penalizes over-division of subtypes proportionately more than joining a few subtypes into large clusters. These two are analogous to sensitivity or recall, while purity is analogous to precision or specificity. The VI distance takes cluster size into account, and errors in large clusters (affecting many sequences) contribute more to the distance than errors in small clusters.</p><p><bold>Further details of the HT-GeMMA method</bold></p><p>When very large superfamilies are analyzed using HT-GeMMA, the memory requirements for storing an all-v-all matrix of profile&#x02013;profile comparisons to be performed can be prohibitively large. This can be avoided by pre-clustering the sequences to reduce the initial number of clusters for HT-GeMMA analysis (<xref ref-type="fig" rid="F2">Figures 2</xref> and <xref ref-type="fig" rid="F11">11</xref>). This also reduces the time required by HT-GeMMA to converge on the final set of clusters. Gene3D S30 clusters are currently chosen as the starting point for pre-clustering. These are multi-linkage clusters constructed with a 30% sequence identity cut-off following an all-v-all pair wise comparison of sequences in a Gene3D superfamily. A simplified example is presented here to illustrate the reduction in the total number of comparisons that can be achieved by pre-clustering. Steps in the example are numbered in <xref ref-type="fig" rid="F11">Figure 11</xref>.
<fig id="F11" position="anchor"><label>Figure 11.</label><caption><p>Illustration of the strategy employed to speed up HT-GeMMA. This uses a worked example described in the &#x02018;<xref ref-type="app" rid="APP1">Appendix</xref>&#x02019; section. Steps in the HT-GeMMA method are listed on the left and may be identified in the flow chart giving an overview of the method in Figure 2.</p></caption><graphic xlink:href="gkp1049f11"/></fig></p><p><bold>Pre-clustering</bold></p><p>The all-v-all comparison of 50 000 sequences would require 1 249 975 000 comparisons [(50 000 &#x000d7; 49 999)/2] or nearly one and a quarter billion comparisons before any merging is performed. However, if these sequences were already clustered in Gene3D into 5000 S30 clusters each containing 10 sequences and FS-GeMMA (<xref ref-type="fig" rid="F1">Figure 1</xref>) were used to analyse each S30 cluster then the total number of comparisons in the initial FS-GeMMA iterations would become 225 000 comparisons [5000 &#x000d7; (10 &#x000d7; 9)/2], which is a relatively manageable number (see steps 1 and 2 in <xref ref-type="fig" rid="F11">Figure 11</xref>). FS-GeMMA usually requires multiple iterations so more comparisons than this would actually be performed but subsequent iterations require fewer comparisons and the total number of comparisons would still be relatively small. Not all sequences within a Gene3D S30 cluster necessarily have the same function and so FS-GeMMA when applied to the 5000 S30 clusters in this simplified example might typically produce 10 000 GeMMA clusters (an average of two GeMMA clusters per S30 cluster; see step 3 in <xref ref-type="fig" rid="F11">Figure 11</xref>).</p><p>Sequences associated with the same molecular function are often found in different Gene3D S30 clusters so further analysis is required to merge together GeMMA clusters with the same function but HT-GeMMA analysis can now commence on the compute cluster with just 10 000 input clusters rather than the initial 50 000 (see steps 3&#x02013;6 in <xref ref-type="fig" rid="F11">Figure 11</xref>). The all-v-all comparison of 10 000 clusters would require 49 995 000 comparisons [(10,000 &#x000d7; 9,999)/2] or nearly 50 million comparisons. By commencing with 10 000 rather than 50 000 clusters there is a 25-fold reduction in the number of comparisons that need to be stored in memory when HT-GeMMA commences and a significantly shorter run time. A very stringent 1 &#x000d7; 10<sup>&#x02212;80</sup>
<italic>E</italic>-value cut-off is used for FS-GeMMA in the pre-clustering step since it is important at this stage to avoid merging together any sequences associated with different functions before a superfamily wide all-v-all comparison is performed using HT-GeMMA.</p><p><bold>HT-GeMMA clustering</bold></p><p>HT-GeMMA must now undertake an all-v-all comparison of the 10 000 starting clusters to determine which clusters are functionally related and should be merged. A further strategy is employed to avoid performing the full 49 995 000 comparisons. Randomly selected batches of clusters are sent to each processor for all-v-all profile-profile comparison at the start of each iteration (steps 4&#x02013;6 in <xref ref-type="fig" rid="F11">Figure 11</xref>). The full 49 995 000 comparisons are not performed in the first iteration but rather just a subset, and similar clusters are merged so that there is a smaller total number of clusters remaining to select from for the next iteration.</p><p><bold>Setting the HT-GeMMA batch size</bold></p><p>The number of comparisons performed in each iteration is determined by the batch size, i.e. the number of comparisons that are selected for each batch that is sent to each processor. This is an adjustable parameter that depends on the total number of processors that are available in the compute cluster. For most of this work, 60 processors are available and the batch size is set to 30 000 (see step 4 in <xref ref-type="fig" rid="F11">Figure 11</xref>). When the total number of comparisons for the whole data set is no more than 1 800 000 (60 &#x000d7; 30 000), an all-v-all comparison of the whole data set is accomplished in the first iteration. A total of 1 800 000 comparisons corresponds to an all-v-all comparison of no more than 1897 clusters. However, for the 10 000 starting clusters in this worked example up to about 28 iterations (49 995 000/1 800 000) might be required to achieve all-v-all comparison of the whole data set (see step 5 in <xref ref-type="fig" rid="F11">Figure 11</xref>). An additional speed up is achieved with each iteration since merging clusters results in a reduction in the total number of clusters and hence in the total number of comparisons remaining to be completed in subsequent iterations (see step 6 in <xref ref-type="fig" rid="F11">Figure 11</xref>). Some of the comparisons that would have needed to be performed no longer need to be performed.</p><p>Automatically setting the number of batches of comparisons sent to each processor in the compute cluster at the start of each iteration to be equal to the number of processors in the compute cluster reduces the waiting time between iterations (step 4 in <xref ref-type="fig" rid="F11">Figure 11</xref>). As the GeMMA sequence clusters vary significantly in size, randomization of the pairs of clusters to be compared in each batch is used to even the load on the processors as well as provide an efficient sampling of pairs. Updated lists of all previously completed comparisons (written by scripts executing on the nodes) are read before commencing each iteration (except, of course, the first iteration; see step 5 in <xref ref-type="fig" rid="F11">Figure 11</xref>). In this way, no comparisons need to be repeated. If the number of possible batches is less than the number of processors (e.g. during the final iterations before convergence on a high <italic>E</italic>-value cut-off, when the total number of clusters is small) then not all processors are used.</p><p>There is a potential problem associated with setting the batch size to a value that is too low. If an iteration completes before the all-v-all comparison of the whole data set had been achieved without any pairs being found with a sufficiently similar score then HT-GeMMA would terminate prematurely. For this reason it is necessary to set the batch size to a sufficiently large value so that the whole data set is sampled. It is found for the largest data sets analyzed in this work that a batch size of 30 000 is adequate to ensure that an all-v-all comparison is achieved in the first stage of HT-GeMMA with an <italic>E</italic>-value cut-off of 1 &#x000d7; 10<sup>&#x02212;80</sup> even when the matrix of all-v-all comparisons is quite sparse in terms of the number of clusters that have a similarity with a significance better than this cut-off, i.e<italic>.</italic> there may be only a small number of merges per iteration but never zero merges prior to convergence of the whole data set.</p><p>A smaller value for the batch size could be chosen for a compute cluster with more processors. If too few comparisons are available to allocate the batch size number of comparisons to each processor, HT-GeMMA automatically adjusts the batch size to equal the total number of comparisons divided by the number of processors. A large batch size also reduces the communication overhead on the compute cluster. This needs to be balanced against the speed up that is achieved, by keeping the batch size small enough for there to be multiple iterations leading to merges and a reduction in the number of pairs before the all-v-all comparison of the whole data set has been achieved, as described above.</p></app></app-group></back></article>