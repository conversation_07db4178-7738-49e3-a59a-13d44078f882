<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Syst Biol</journal-id><journal-id journal-id-type="iso-abbrev">BMC Syst Biol</journal-id><journal-title-group><journal-title>BMC Systems Biology</journal-title></journal-title-group><issn pub-type="epub">1752-0509</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6195963</article-id><article-id pub-id-type="publisher-id">620</article-id><article-id pub-id-type="doi">10.1186/s12918-018-0620-8</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research Article</subject></subj-group></article-categories><title-group><article-title>Module-detection approaches for the integration of multilevel omics data highlight the comprehensive response of <italic>Aspergillus fumigatus</italic> to caspofungin</article-title></title-group><contrib-group><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0002-9497-4716</contrib-id><name><surname>Conrad</surname><given-names>T.</given-names></name><address><phone>+49 3641 532 1521</phone><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Kniemeyer</surname><given-names>O.</given-names></name><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Henkel</surname><given-names>S. G.</given-names></name><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author"><name><surname>Kr&#x000fc;ger</surname><given-names>T.</given-names></name><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Mattern</surname><given-names>D. J.</given-names></name><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff9">9</xref></contrib><contrib contrib-type="author"><name><surname>Valiante</surname><given-names>V.</given-names></name><xref ref-type="aff" rid="Aff4">4</xref></contrib><contrib contrib-type="author"><name><surname>Guthke</surname><given-names>R.</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Jacobsen</surname><given-names>I. D.</given-names></name><xref ref-type="aff" rid="Aff5">5</xref><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Brakhage</surname><given-names>A. A.</given-names></name><xref ref-type="aff" rid="Aff2">2</xref><xref ref-type="aff" rid="Aff6">6</xref></contrib><contrib contrib-type="author"><name><surname>Vlaic</surname><given-names>S.</given-names></name><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author"><name><surname>Linde</surname><given-names>J.</given-names></name><xref ref-type="aff" rid="Aff7">7</xref><xref ref-type="aff" rid="Aff8">8</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0143 807X</institution-id><institution-id institution-id-type="GRID">grid.418398.f</institution-id><institution>Systems Biology/Bioinformatics, </institution><institution>Leibniz Institute for Natural Product Research and Infection Biology &#x02013; Hans Kn&#x000f6;ll Institute, </institution></institution-wrap>Jena, Germany </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0143 807X</institution-id><institution-id institution-id-type="GRID">grid.418398.f</institution-id><institution>Molecular and Applied Microbiology, </institution><institution>Leibniz Institute for Natural Product Research and Infection Biology &#x02013; Hans Kn&#x000f6;ll Institute, </institution></institution-wrap>Jena, Germany </aff><aff id="Aff3"><label>3</label>BioControl Jena GmbH, Jena, Germany </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0143 807X</institution-id><institution-id institution-id-type="GRID">grid.418398.f</institution-id><institution>Biobricks of Microbial Natural Product Syntheses, </institution><institution>Leibniz Institute for Natural Product Research and Infection Biology &#x02013; Hans Kn&#x000f6;ll Institute, </institution></institution-wrap>Jena, Germany </aff><aff id="Aff5"><label>5</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0143 807X</institution-id><institution-id institution-id-type="GRID">grid.418398.f</institution-id><institution>Microbial Immunology, </institution><institution>Leibniz Institute for Natural Product Research and Infection Biology &#x02013; Hans Kn&#x000f6;ll Institute, </institution></institution-wrap>Jena, Germany </aff><aff id="Aff6"><label>6</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 1939 2794</institution-id><institution-id institution-id-type="GRID">grid.9613.d</institution-id><institution>Institute for Microbiology, Friedrich Schiller University, </institution></institution-wrap>Jena, Germany </aff><aff id="Aff7"><label>7</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 0143 807X</institution-id><institution-id institution-id-type="GRID">grid.418398.f</institution-id><institution>Research Group PiDOMICs, </institution><institution>Leibniz Institute for Natural Product Research and Infection Biology &#x02013; Hans Kn&#x000f6;ll Institute, </institution></institution-wrap>Jena, Germany </aff><aff id="Aff8"><label>8</label>Institute for Bacterial Infections and Zoonoses, Federal Research Institute for Animal Health &#x02013; Friedrich Loeffler Institute, Jena, Germany </aff><aff id="Aff9"><label>9</label>Present address: PerkinElmer Inc., Rodgau, Germany </aff></contrib-group><pub-date pub-type="epub"><day>20</day><month>10</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>20</day><month>10</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>12</volume><elocation-id>88</elocation-id><history><date date-type="received"><day>12</day><month>3</month><year>2018</year></date><date date-type="accepted"><day>8</day><month>10</month><year>2018</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s). 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Omics data provide deep insights into overall biological processes of organisms. However, integration of data from different molecular levels such as transcriptomics and proteomics, still remains challenging. Analyzing lists of differentially abundant molecules from diverse molecular levels often results in a small overlap mainly due to different regulatory mechanisms, temporal scales, and/or inherent properties of measurement methods. Module-detecting algorithms identifying sets of closely related proteins from protein-protein interaction networks (PPINs) are promising approaches for a better data integration.</p></sec><sec><title>Results</title><p id="Par2">Here, we made use of transcriptome, proteome and secretome data from the human pathogenic fungus <italic>Aspergillus fumigatus</italic> challenged with the antifungal drug caspofungin. Caspofungin targets the fungal cell wall which leads to a compensatory stress response. We analyzed the omics data using two different approaches: First, we applied a simple, classical approach by comparing lists of differentially expressed genes (DEGs), differentially synthesized proteins (DSyPs) and differentially secreted proteins (DSePs); second, we used a recently published module-detecting approach, ModuleDiscoverer, to identify regulatory modules from PPINs in conjunction with the experimental data. Our results demonstrate that regulatory modules show a notably higher overlap between the different molecular levels and time points than the classical approach. The additional structural information provided by regulatory modules allows for topological analyses. As a result, we detected a significant association of omics data with distinct biological processes such as regulation of kinase activity, transport mechanisms or amino acid metabolism. We also found a previously unreported increased production of the secondary metabolite fumagillin by <italic>A. fumigatus</italic> upon exposure to caspofungin. Furthermore, a topology-based analysis of potential key factors contributing to drug-caused side effects identified the highly conserved protein polyubiquitin as a central regulator. Interestingly, polyubiquitin UbiD neither belonged to the groups of DEGs, DSyPs nor DSePs but most likely strongly influenced their levels.</p></sec><sec><title>Conclusion</title><p id="Par3">Module-detecting approaches support the effective integration of multilevel omics data and provide a deep insight into complex biological relationships connecting these levels. They facilitate the identification of potential key players in the organism&#x02019;s stress response which cannot be detected by commonly used approaches comparing lists of differentially abundant molecules.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s12918-018-0620-8) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Multilevel</kwd><kwd>Omics</kwd><kwd>Protein-protein interaction network</kwd><kwd>Module</kwd><kwd><italic>Aspergillus fumigatus</italic></kwd><kwd>Caspofungin</kwd><kwd>Stress response</kwd><kwd>ModuleDiscoverer</kwd></kwd-group><funding-group><award-group><funding-source><institution>Jena School for Microbial Communication</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>Deutsche Forschungsgemeinschaft (DFG) CRC/Transregio 124, subproject A1</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>Deutsche Forschungsgemeinschaft (DFG) CRC/Transregio 124, subproject C5</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>Deutsche Forschungsgemeinschaft (DFG) CRC/Transregio 124, subproject INF</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>Deutsche Forschungsgemeinschaft (DFG) CRC/Transregio 124, subproject Z2</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>Th&#x000fc;ringer Aufbaubank (DE)</institution></funding-source></award-group></funding-group><funding-group><award-group><funding-source><institution>German Federal Ministry of Education &#x00026; Research (BMBF FKZ 0315439)</institution></funding-source></award-group></funding-group><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par4">The permanent growth in the development and improvement of new measurement techniques have led to a wealth of data from heterogeneous sources. The integration of all available data obtained from diverse studies has the potential to provide a more comprehensive and deeper understanding of the studied subject [<xref ref-type="bibr" rid="CR1">1</xref>&#x02013;<xref ref-type="bibr" rid="CR3">3</xref>]. One example is the investigation of an organism&#x02019;s response to an external stimulus at different molecular levels. Large-scale studies at molecular levels like transcriptomics, proteomics, lipidomics or metabolomics can be summarized by the term &#x02018;omics levels&#x02019;. These omics levels are linked to each other and are considered in their entirety. They describe the overall biological processes which occur in the analyzed organism. Potential links can be characterized by level-shared (&#x02018;overlapping&#x02019;) components (such as genes or proteins) or the participation of components of different molecular levels in level-shared pathways.</p><p id="Par5">As widely reported, the integration and analysis of data from multiple levels measured with diverse techniques at different time points are challenging. In an intuitive and commonly used approach (&#x02018;simple approach&#x02019;), the analysis of several sets of omics data is based on the comparison of lists of differentially expressed genes (DEGs) and differentially synthesized proteins (DSyPs) identified in experimental datasets. However, the use of only DEGs and DSyPs is threshold-dependent and usually incomplete due to experimental limitations. For example, the use of liquid chromatography-mass spectrometry (LC-MS/MS)-based shotgun proteomics analysis for the identification of DSyPs is usually limited in the quantification of low abundant proteins due to the large dynamic range of protein abundances that needs to be covered [<xref ref-type="bibr" rid="CR4">4</xref>, <xref ref-type="bibr" rid="CR5">5</xref>]. Other approaches, including diverse pathway enrichment analyses, assign both differentially and non-differentially expressed genes or their synthesized proteins to specific pathways which are part of biological processes. The level of activity of such pathways can be estimated by taking into account measurements of changes in gene expression or protein synthesis. However, as these approaches are based on pre-defined lists of pathways, they exclude unknown pathways which may also have important functions [<xref ref-type="bibr" rid="CR6">6</xref>]. Over the last decades, the analysis of protein-protein interaction networks (PPINs) has become a useful approach [<xref ref-type="bibr" rid="CR7">7</xref>]. By identifying direct (physical) contacts and indirect interactions (e.g., via regulatory cascades) between two or more proteins, PPINs point to structural and functional relationships between their nodes [<xref ref-type="bibr" rid="CR8">8</xref>]. Several de novo network enrichment approaches were developed to extract connected sub-networks from larger interaction networks. Such sub-networks containing sets of closely related proteins are defined as modules [<xref ref-type="bibr" rid="CR9">9</xref>]. There are many examples in the literature demonstrating the usefulness of modules in research data interpretation. For instance, Stuart et al. analyzed genetic modules to detect co-expressed genes that are involved in similar biological processes [<xref ref-type="bibr" rid="CR10">10</xref>], while Trevino et al. [<xref ref-type="bibr" rid="CR11">11</xref>] have shown the usefulness of investigating inter-module connectivity to identify molecular cross-talk between normal prostate epithelial and prostate carcinoma cells.</p><p id="Par6">Another very interesting application of modules is the identification of prognostic or drug response biomarkers [<xref ref-type="bibr" rid="CR12">12</xref>]. In this context, modules also show their potential for the characterization of drug-caused side effects occurring in addition to effects on the intended primary drug target. Wang et al. [<xref ref-type="bibr" rid="CR13">13</xref>] demonstrated that major contributing factors of such side effects can be investigated by considering the primary drug target and its local network structure.</p><p id="Par7">Several categories of modules have been described until now (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref>). Examples are topological modules composed of proteins showing a high degree of inner-connectiveness or functional modules that contain proteins associated to specific biological functions [<xref ref-type="bibr" rid="CR14">14</xref>, <xref ref-type="bibr" rid="CR15">15</xref>]. So-called regulatory modules are defined as sets of co-expressed genes which share a common function [<xref ref-type="bibr" rid="CR16">16</xref>]. Popular methods for the detection of regulatory modules are: DEGAS [<xref ref-type="bibr" rid="CR17">17</xref>], MATISSE [<xref ref-type="bibr" rid="CR15">15</xref>], KeyPathwayMiner [<xref ref-type="bibr" rid="CR18">18</xref>] and ModuleDiscoverer [<xref ref-type="bibr" rid="CR19">19</xref>]. Among them, the recently published ModuleDiscoverer (MD) includes a heuristic that approximates the PPIN&#x02019;s underlying community structure based on maximal cliques. While a community defines a group of proteins featuring a higher within-edge density in comparison to the edge density connecting them, a clique represents a set of proteins with edges between each pair of them. A clique is maximal if no node (e.g., protein) exists which extends that clique. MD was shown to be very efficient in the detection of regulatory modules for gene expression data in the context of animal models of non-alcoholic fatty liver disease [<xref ref-type="bibr" rid="CR19">19</xref>].<fig id="Fig1"><label>Fig. 1</label><caption><p>Module categories. Exemplarily selected categories of modules within protein-protein interaction networks. Proteins are represented by circles, interactions by edges</p></caption><graphic xlink:href="12918_2018_620_Fig1_HTML" id="MO1"/></fig></p><p id="Par8">In this study, we applied the simple approach (SA), the recently published module-detection approach MD as well as KeyPathwayMiner to experimental data of different molecular levels, measurement techniques and time points. As a case study, we analyzed the molecular response of the human pathogenic fungus <italic>Aspergillus fumigatus</italic> to the antifungal drug caspofungin. <italic>A. fumigatus</italic> causes local and systemic infections in immunocompromised individuals [<xref ref-type="bibr" rid="CR20">20</xref>]. One therapeutic approach is the use of the lipopeptide caspofungin of the group of echinocandins. Caspofungin specifically targets the fungal cell wall by inhibiting the synthesis of the polysaccharide &#x003b2;-(1,3)-D-glucan [<xref ref-type="bibr" rid="CR21">21</xref>]. Fungal cells respond to caspofungin by the adaption of gene expression and, consequently, protein biosynthesis and secretion of molecules [<xref ref-type="bibr" rid="CR22">22</xref>]. Therefore, we analyzed the transcriptomic, proteomic and secretomic response of <italic>A. fumigatus</italic> to caspofungin at several time points to gain a deeper understanding of the overall molecular response of this fungus to this drug.</p><p id="Par9">We demonstrated the so far untested capacity of MD to integrate multilevel omics data and showed that this level of integration is not achievable using SA. Moreover, module-detecting approaches facilitate the identification of potential key players in the organism&#x02019;s stress response which are not detectable by commonly used approaches comparing lists of differentially abundant molecules.</p></sec><sec id="Sec2"><title>Methods</title><sec id="Sec3"><title>Omics data and data processing</title><p id="Par10">Data analyses were performed in R version 3.4.1 using packages provided by Bioconductor [<xref ref-type="bibr" rid="CR23">23</xref>].</p></sec><sec id="Sec4"><title>Strain and culture conditions</title><p id="Par11">Mycelia of the <italic>Aspergillus fumigatus</italic> strain CEA17 &#x00394;<italic>akuB</italic> [<xref ref-type="bibr" rid="CR24">24</xref>] were pre-cultured for 16&#x000a0;h in <italic>Aspergillus</italic> minimal medium (AMM, [<xref ref-type="bibr" rid="CR25">25</xref>]) containing 50&#x000a0;mM glucose and 70&#x000a0;mM NaNO<sub>3</sub> and then stressed with a sub-inhibitory concentration of caspofungin (100&#x000a0;ng/ml) as described in Altwasser et al. [<xref ref-type="bibr" rid="CR26">26</xref>]. Liquid cultures were inoculated with 1&#x02009;&#x000d7;&#x02009;10<sup>6</sup> conidia/ml and cultivated at 37&#x000a0;&#x000b0;C with shaking at 200&#x000a0;rpm. Samples for analyzing the transcriptomic, proteomic and secretomic response of the fungus were taken at the indicated time points after treatment. Secreted proteins were precipitated overnight from culture supernatants as described below.</p></sec><sec id="Sec5"><title>Transcriptome data</title><p id="Par12">RNA extraction, cDNA library construction and RNA-Seq analysis by Illumina next-generation sequencing of samples taken at 0&#x000a0;h, 0.5&#x000a0;h, 1&#x000a0;h, 4&#x000a0;h and 8&#x000a0;h after caspofungin treatment were performed as described in [<xref ref-type="bibr" rid="CR26">26</xref>]. Likewise, data were pre-processed as described in [<xref ref-type="bibr" rid="CR26">26</xref>]. Genes were annotated by identifiers provided by the <italic>Aspergillus</italic> Genome Database (AspGD, as of September 2015 [<xref ref-type="bibr" rid="CR27">27</xref>]). In addition, identifiers provided by the Central <italic>Aspergillus</italic> Data Repository (CADRE) [<xref ref-type="bibr" rid="CR28">28</xref>] were obtained using the package <italic>biomaRt</italic> [<xref ref-type="bibr" rid="CR29">29</xref>] provided by Bioconductor as of February 2017. For each time point, expression values were compared to the control sample taken at 0&#x000a0;h. Only those genes with an absolute log2 Fold Change (log2FC) value greater 1 and a False Discovery Rate (FDR) corrected <italic>p</italic>-value below 0.05 were considered to be differentially expressed.</p></sec><sec id="Sec6"><title>Proteome and secretome data</title><p id="Par13">Samples for proteome analysis were taken at 0&#x000a0;h, 4&#x000a0;h and 8&#x000a0;h after treatment. The mycelium was collected by filtering through Miracloth (Merck Millipore), subsequently washed with water and snap frozen with liquid nitrogen. Sample preparation of the mycelium for the proteome analysis was performed as previously described [<xref ref-type="bibr" rid="CR30">30</xref>]. Samples for secretome analysis were taken at 0&#x000a0;h and 8&#x000a0;h after treatment and prepared as follows: Cell free-filtered supernatant of AMM medium from <italic>A. fumigatus</italic> cultures was precipitated by trichloroacetic acid (TCA) at 15% (<italic>w</italic>/<italic>v</italic>) final concentration (4&#x000a0;&#x000b0;C, overnight). Precipitates were washed with acetone and resolubilized in trifluoroethanol (TFE) mixed 1:1 with 100&#x000a0;mM triethylammonium bicarbonate (TEAB). Samples containing 100&#x000a0;&#x003bc;g of total protein (in 100&#x000a0;&#x003bc;l) were reduced with 50&#x000a0;mM tris(2-carboxyethyl)phosphine (TCEP) for 1&#x000a0;h at 55&#x000a0;&#x000b0;C and subsequently cysteine thiols were alkylated with 12.5&#x000a0;mM iodoacetamide for 30&#x000a0;min at room temperature. Proteins were digested at 37&#x000a0;&#x000b0;C for 18&#x000a0;h with trypsin+LysC mix (Promega) at 1:25 protease:protein ratio. Proteome samples were labeled with tandem mass tags (TMT) 6plex and secretome samples were labeled with isobaric tags for relative and absolute quantification (iTRAQ) 4plex according to the manufacturer&#x02019;s protocols.</p><p id="Par14">LC-MS/MS analysis was performed as previously described [<xref ref-type="bibr" rid="CR30">30</xref>] with the following modifications: Eluents A (0.1% <italic>v</italic>/v formic acid in H<sub>2</sub>O) and B (0.1% v/v formic acid in 90/10 ACN/H<sub>2</sub>O <italic>v</italic>/v) were mixed for 10&#x000a0;h gradient elution: 0&#x02013;4&#x000a0;min at 4% B, 15&#x000a0;min at 5.5% B, 30&#x000a0;min at 6.5%, 220&#x000a0;min at 12.5% B, 300&#x000a0;min at 17% B, 400&#x000a0;min at 26% B, 450&#x000a0;min at 35% B, 475&#x000a0;min at 42% B, 490&#x000a0;min at 51% B, 500&#x000a0;min at 60% B, 515&#x02013;529&#x000a0;min at 96% B, 530&#x02013;600&#x000a0;min at 4% B. Precursor ions were monitored at m/z 300&#x02013;1500, <italic>R</italic>&#x02009;=&#x02009;140&#x000a0;k (FWHM), 3e6 AGC (automatic gain control) target, and 120 maximum injection time (maxIT). Top ten precursor ions (0.8&#x000a0;Da isolation width; z&#x02009;=&#x02009;2&#x02013;5) underwent data-dependent higher-energy collisional dissociation (HCD) fragmentation at normalized collision energy (NCE) 36% using N<sub>2</sub> gas. Dynamic exclusion was set to 40&#x000a0;s. MS<sup>2</sup> spectra were monitored at <italic>R</italic>&#x02009;=&#x02009;17.5&#x000a0;k (FWHM), 2e5 AGC target, and 120 maxIT. The fixed first mass was set to m/z 110 to match the iTRAQ reporter ions (m/z 114&#x02013;117).</p><p id="Par15">Database searches were performed by Proteome Discoverer (PD) 1.4 (Thermo Fisher Scientific, Dreieich, Germany) using the AspGD protein database of <italic>A. fumigatus</italic> Af293 [<xref ref-type="bibr" rid="CR31">31</xref>] and the algorithms of MASCOT 2.4.1 (Matrix Science, UK), SEQUEST HT (integral search engine of PD 1.4), and MS Amanda 1.0. Two missed cleavages were allowed for tryptic digestion. The precursor mass tolerance and the integration tolerance (most confident centroid) were set to 5&#x000a0;ppm and the MS2 tolerance to 0.02&#x000a0;Da. Static modifications were carbamidomethylation of cysteine and either TMT6plex (proteome) or iTRAQ4plex (secretome) at lysine residues and the peptide N-terminus. Dynamic modifications were oxidation of methionine and either TMT6plex of threonine or iTRAQ4plex of tyrosine. Percolator and a reverse decoy database were used for <italic>q</italic>-value validation of the spectral matches (&#x00394;cn&#x02009;&#x0003c;&#x02009;0.05). At least two peptides per protein and a strict target FDR&#x02009;&#x0003c;&#x02009;1% were required for confident protein hits. The significance threshold for differential protein abundances for TMT and iTRAQ experiments was set to factor 1.5.</p><p id="Par16">With the aid of the <italic>biomaRt</italic> package, proteins were annotated using identifiers provided by AspGD as of September 2015 and CADRE as of February 2017.</p></sec><sec id="Sec7"><title>Chemical analysis of secondary metabolites</title><p id="Par17">For quantification of fumagillin, fungal cultures were extracted and run on a LC-MS system consisting of an HPLC, UltiMate 3000 binary RSLC with photo diode array detector (Thermo Fisher Scientific, Dreieich, Germany) and the mass spectrometer (LTQ XL Linear Ion Trap from Thermo Fisher Scientific, Dreieich, Germany) with an electrospray ion source as described in J&#x000f6;hnk et al. [<xref ref-type="bibr" rid="CR32">32</xref>]. Data were obtained from three biological replicates and three technical replicates. A standard curve (1000, 500, 250, 125 and 62.5&#x000a0;&#x003bc;g/mL) using an authentic fumagillin standard (Abcam, United Kingdom) was calculated. The Xcalibur Quan Browser software (Thermo Fisher Scientific, Dreieich, Germany) was used to calculate the amounts of fumagillin.</p></sec><sec id="Sec8"><title>Application of module-detecting approaches</title><p id="Par18">A high-confidence (score&#x02009;&#x0003e;&#x02009;0.7) PPIN of <italic>A. fumigatus</italic> strain A1163 was downloaded from STRING version 10 [<xref ref-type="bibr" rid="CR33">33</xref>]. Both the PPIN and the pre-processed omics data were taken as input for the module-detecting approaches. Thereby, protein identifier annotations provided by CADRE were used.</p></sec><sec id="Sec9"><title>ModuleDiscoverer</title><p id="Par19">In order to apply MD for transcriptome data, the background contains all known <italic>A. fumigatus</italic> proteins described in AspGD. Analyzing proteome and secretome data, all proteins detected via LC-MS/MS were taken as background. The single-seed MD algorithm was applied to the input data as described by Vlaic et al. [<xref ref-type="bibr" rid="CR19">19</xref>]. In brief, maximal cliques were identified using only one seed node in the PPIN. Cliques were tested for their enrichment with DEGs/DSyPs/DSePs using a permutation-based test as described in Vlaic et al. [<xref ref-type="bibr" rid="CR19">19</xref>]. Cliques with a <italic>p</italic>-value &#x0003c;&#x02009;0.01 were considered significantly enriched. Based on the union of these significantly enriched cliques, the regulatory module was assembled.</p><p id="Par20">For the integration of different omics datasets, all regulatory modules were merged by forming the union of all nodes and edges. The resulting union regulatory module is defined as &#x02018;overall regulatory module&#x02019; (ORM). Sub-modules with a number of nodes &#x0003c;&#x02009;10 were not considered. Cytoscape version 3.2.1 [<xref ref-type="bibr" rid="CR34">34</xref>] was used to visualize and analyze regulatory modules, for example, regarding their nodes&#x02019; degree and betweenness centrality.</p></sec><sec id="Sec10"><title>KeyPathwayMiner</title><p id="Par21">KeyPathwayMiner (KPM) detects maximal connected sub-networks. In these sub-networks, all but a specific number <italic>K</italic> components are DEGs, DSyPs or DSePs in all but at most a specific number <italic>L</italic> cases [<xref ref-type="bibr" rid="CR18">18</xref>]. In this study, cases are defined as the available time points. In a first analysis (I), KPM was applied to each single experimental dataset to receive one module for each time point of the respective molecular level. In the single-level analysis (II), the modules for each molecular level over all time points were identified. A third analysis (III) directly combined all of the experimental datasets to get the overall regulatory module. For the KPM input, one matrix for each time point (I) or molecular level ((II) and (III)) were generated consisting of information about the components&#x02019; regulation at the respective time points. For (II) and (III), only those components were considered that were DEGs/DSyPs/DSePs in at least one of the time points of the respective molecular level. With these matrices, the <italic>A. fumigatus</italic> PPIN and with the aid of KeyPathwayMiner Cytoscape App [<xref ref-type="bibr" rid="CR18">18</xref>], sub-networks were computed using following settings: Ant colony optimization meta heuristic (ACO) as search algorithm, individual node exceptions (INEs) as search strategy, maximum of exception nodes <italic>K</italic>&#x02009;=&#x02009;2. For (I) and (II), the maximal case exception parameter was set to <italic>L</italic>&#x02009;=&#x02009;0. For the multilevel omics analysis (III), the logical connector of the different levels was set to the logical &#x02018;OR&#x02019; and <italic>L</italic> was set to <italic>L1</italic>&#x02009;=&#x02009;3 (transcriptome data),<italic> L2</italic>&#x02009;=&#x02009;1 (proteome data) and <italic>L3</italic>&#x02009;=&#x02009;0 (secretome data). These <italic>L</italic> values were based on the number of time points of the respective molecular level. The assumption was that the considered component is a DEG/DSyP/DSeP in at least one measured time point. For instance, as four measured transcriptome time points were available, a gene was allowed to be not differentially expressed in maximal three out of four time points. The top ten best-scoring sub-networks were selected for further analysis. A KPM regulatory module describes the union of these top ten sub-networks of the respectively considered datasets.</p></sec><sec id="Sec11"><title>Comparison of the simple approach and a module-detecting approach</title><sec id="Sec12"><title>Overlap of components</title><p id="Par22">The overlap (percentage value) is defined as fraction of the intersection of the respective datasets from the union of the datasets. For the simple approach (SA), the overlap of different molecular levels was analyzed by comparing lists of DEGs, DSyPs and DSePs at the considered time points. For the module-detecting approach, the overlap of all components of the respective regulatory modules was considered.</p><p id="Par23">In addition to the comparison of percentage values of overlapping components, a more objective measurement based on a permutation-based test was considered. Considering all known <italic>A. fumigatus</italic> proteins (<italic>N</italic>) described in AspGD, <italic>D</italic> &#x02208; <italic>N</italic> is a set of components (DEGs, DSyPs or DSePs) for each of the molecular levels. In <italic>I</italic>&#x000a0;=&#x02009;100,000 iterations, datasets <italic>B</italic> were created where each set consists of |<italic>D</italic>| components sampled from <italic>N</italic>. In every iteration, the overlap <italic>P</italic> of the molecular levels was calculated based on the generated datasets for transcriptome, proteome and secretome. The <italic>p</italic>-value was calculated by dividing the number of iterations in which <italic>P</italic>&#x02009;&#x02265;&#x02009;<italic>O,</italic> where <italic>O</italic> represents the overlap received by SA or MD, and the total number of iterations <italic>I</italic>.</p></sec><sec id="Sec13"><title>Correlation of the components&#x02019; regulation</title><p id="Par24">All components detected in at least one of the transcriptomic and one of the proteomic time points were considered for correlation analyses. The distance between results obtained for different molecular levels and time points was estimated based on the correlation of ranked lists of the components&#x02019; absolute gene expression or protein synthesis regulation values (absolute log2FCs). Lists of ordered, absolute regulation values were rank-transformed. Indices corresponding to ties (equal values) were randomly ordered. Spearman&#x02019;s rank correlation coefficient <italic>r</italic> was calculated. The ranking was repeated 1000 times. Over all repeats, the final correlation between the regulation lists was averaged. The distance <italic>d</italic> is defined as <italic>d&#x02009;=</italic>&#x000a0;1 - <italic>r</italic>.</p></sec></sec><sec id="Sec14"><title>Generalized topological overlap</title><p id="Par25">The ORM was clustered via the generalized topological overlap measure (GTOM) as described in [<xref ref-type="bibr" rid="CR35">35</xref>]. Matrix <inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {T}^{\left[m\right]}=\left[{t}_{ij}^{\left[m\right]}\right] $$\end{document}</tex-math><mml:math id="M2" display="inline"><mml:msup><mml:mi>T</mml:mi><mml:mfenced close="]" open="["><mml:mi>m</mml:mi></mml:mfenced></mml:msup><mml:mo>=</mml:mo><mml:mfenced close="]" open="["><mml:msubsup><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ij</mml:mi><mml:mfenced close="]" open="["><mml:mi>m</mml:mi></mml:mfenced></mml:msubsup></mml:mfenced></mml:math><inline-graphic xlink:href="12918_2018_620_Article_IEq1.gif"/></alternatives></inline-formula> is called the <italic>m</italic>-th order GTOM matrix and includes the overlap of nodes reachable from the nodes <italic>i</italic> and <italic>j</italic> within <italic>m</italic> steps:<disp-formula id="Equa"><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ {t}_{ij}^{\left[m\right]}=\frac{\left|{N}_m(i)\cap {N}_m(j)\right|+{a}_{ij}+{I}_{i=j}}{\mathit{\min}\left\{\left|{N}_m(i)\right|,\left|{N}_m(j)\right|\right\}+1-{a}_{ij}} $$\end{document}</tex-math><mml:math id="M4" display="block"><mml:msubsup><mml:mi>t</mml:mi><mml:mi mathvariant="italic">ij</mml:mi><mml:mfenced close="]" open="["><mml:mi>m</mml:mi></mml:mfenced></mml:msubsup><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mfenced close="|" open="|"><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>m</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mi>i</mml:mi></mml:mfenced><mml:mo>&#x02229;</mml:mo><mml:msub><mml:mi>N</mml:mi><mml:mi>m</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mi>j</mml:mi></mml:mfenced></mml:mrow></mml:mfenced><mml:mo>+</mml:mo><mml:msub><mml:mi>a</mml:mi><mml:mi mathvariant="italic">ij</mml:mi></mml:msub><mml:mo>+</mml:mo><mml:msub><mml:mi>I</mml:mi><mml:mrow><mml:mi>i</mml:mi><mml:mo>=</mml:mo><mml:mi>j</mml:mi></mml:mrow></mml:msub></mml:mrow><mml:mrow><mml:mo mathvariant="italic">min</mml:mo><mml:mfenced close="}" open="{" separators=","><mml:mfenced close="|" open="|"><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>m</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mi>i</mml:mi></mml:mfenced></mml:mrow></mml:mfenced><mml:mfenced close="|" open="|"><mml:mrow><mml:msub><mml:mi>N</mml:mi><mml:mi>m</mml:mi></mml:msub><mml:mfenced close=")" open="("><mml:mi>j</mml:mi></mml:mfenced></mml:mrow></mml:mfenced></mml:mfenced><mml:mo>+</mml:mo><mml:mn>1</mml:mn><mml:mo>&#x02212;</mml:mo><mml:msub><mml:mi>a</mml:mi><mml:mi mathvariant="italic">ij</mml:mi></mml:msub></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12918_2018_620_Article_Equa.gif" position="anchor"/></alternatives></disp-formula></p><p id="Par26"><italic>A</italic>&#x02009;=&#x02009;[<italic>a</italic><sub><italic>ij</italic></sub>] is defined as adjacency matrix, <italic>N</italic><sub><italic>m</italic></sub>(<italic>i</italic>) as the set of neighbors of <italic>i,</italic> the Identity matrix <italic>I</italic><sub><italic>i</italic>&#x02009;=&#x02009;<italic>j</italic></sub>equals 1 if <italic>i&#x02009;=&#x02009;j</italic> and zero else, |&#x000b7;| denotes the number of elements (cardinality) in its argument <italic>j.</italic> The clustering was performed for second-order connections. With the aid of the <italic>hclust</italic> function <italic>(method&#x02009;=&#x02009;average),</italic> a dendrogram based on all distances between proteins were generated. A cutoff of 0.65 was chosen to receive the clusters. R packages <italic>RcolorBrewer</italic> [<xref ref-type="bibr" rid="CR36">36</xref>] <italic>a</italic>nd <italic>WGCNA</italic> [<xref ref-type="bibr" rid="CR37">37</xref>] were applied for coloring the single clusters.</p></sec><sec id="Sec15"><title>Enrichment analysis (functional annotation of biological processes)</title><p id="Par27">Gene Ontology (GO) terms were applied for functional annotation concerning biological processes. Gene (gene product) terms of <italic>A. fumigatus</italic> were retrieved from AspGD as of October 2017. In particular, GO information about the Af293 strain was extracted and imported into R and was transformed into custom annotation objects by packages <italic>AnnotationDbi</italic> [<xref ref-type="bibr" rid="CR38">38</xref>] and <italic>GSEABase</italic> [<xref ref-type="bibr" rid="CR39">39</xref>] (each of version 1.38.2 as part of Bioconductor package collection version 3.5). In addition, the packages <italic>GO.db</italic> [<xref ref-type="bibr" rid="CR40">40</xref>], <italic>GOstats</italic> [<xref ref-type="bibr" rid="CR41">41</xref>] as well as the helper function <italic>GSEAGOHyperGParams</italic> of package <italic>Category</italic> [<xref ref-type="bibr" rid="CR42">42</xref>] were applied for the enrichment analysis. For SA, all <italic>A. fumigatus</italic> proteins described in AspGD were taken as background. For the MD approach, all proteins which are part of the PPIN downloaded from STRING, were taken as background. GO terms composed of at least two members, associated with at least two components and leading to <italic>p</italic>-values below 0.05 were considered as significantly enriched.</p></sec></sec><sec id="Sec16"><title>Results</title><sec id="Sec17"><title>Data overview</title><p id="Par28">We used experimental omics data of a <italic>A. fumigatus</italic> study that investigated the stress response to the antifungal drug caspofungin at different molecular levels (transcriptome, proteome, secretome) including different&#x000a0;time points. Figure&#x000a0;<xref rid="Fig2" ref-type="fig">2a</xref> provides an overview of the available datasets including all genes and proteins detected by RNA-Seq and LC-MS/MS. Over all considered time points, 9881 genes were measured for the transcriptomic response, 3858 proteins for the proteomic response and 1110 proteins for the secretome. Filtering the data for DEGs, DSyPs and DSePs resulted in 1058 DEGs (498 upregulated (&#x02191;), 560 downregulated (&#x02193;)) at 0.5&#x000a0;h, 1237 DEGs (876 &#x02191;, 361 &#x02193;) at 1&#x000a0;h, 1322 DEGs (784 &#x02191;, 538 &#x02193;) at 4&#x000a0;h and 1068 DEGs (600 &#x02191;, 468 &#x02193;) at 8&#x000a0;h after caspofungin treatment. In the proteome, 230 DSyPs (88 &#x02191;, 142 &#x02193;) were identified at 4&#x000a0;h after treatment, and 204 DSyPs (114 &#x02191;, 90 &#x02193;) at the 8&#x000a0;h time point. 136&#x000a0;DSePs (118 &#x02191;, 18 &#x02193;) were detected for the secretome at 8&#x000a0;h after treatment (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2b</xref>). Complete lists of DEGs, DSyPs and DSePs are provided in the Additional file <xref rid="MOESM1" ref-type="media">1</xref>.<fig id="Fig2"><label>Fig. 2</label><caption><p>Overview of the available datasets. <bold>a</bold> Number and overlap of all measured genes and proteins. <bold>b</bold> Number of differentially expressed genes (DEGs), differentially synthesized proteins (DSyPs) and differentially secreted proteins (DSePs) in all available experimental datasets</p></caption><graphic xlink:href="12918_2018_620_Fig2_HTML" id="MO2"/></fig></p></sec><sec id="Sec18"><title>Overlap of datasets of the different molecular levels</title><p id="Par29">We started to analyze the molecular level overlap by comparing all measured genes or proteins (hereafter called &#x02018;components&#x02019;) independently of their differential regulation and time points. This comparison showed that the overlap of all three molecular levels amounted to 10.5% (Fig. <xref rid="Fig2" ref-type="fig">2a</xref>). Applying SA and MD to the experimental data (Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref>), this level overlap accounted for 0.5% (SA) and 6.1% (MD). Considering only two out of three molecular levels (including data of all considered time points, respectively), both approaches resulted in the highest overlap for the proteome/secretome comparison (11.2% SA, 21.4% MD). This observation was not surprising as the secreted proteins are also included in the global proteome. We found that MD provided an up to 12-fold higher overlap than SA.<fig id="Fig3"><label>Fig. 3</label><caption><p>Overlap of molecular levels. Overlap of transcriptome (T), proteome (P) and secretome (S) regarding their components (genes or proteins)</p></caption><graphic xlink:href="12918_2018_620_Fig3_HTML" id="MO3"/></fig></p><p id="Par30">A further analysis of overlapping components considered a more objective measurement based on a permutation-based test. In 100,000 iterations, random datasets for transcriptome, proteome and secretome were generated and the overlap of all three datasets was calculated. The median-value of all 100,000 random overlaps equaled 3. Thus, the level overlap accounted for 0.1%. For the SA-obtained overlap (11 components or 0.5% as presented in Fig. <xref rid="Fig3" ref-type="fig">3</xref>), we calculated a <italic>p</italic>-value&#x02009;=&#x02009;2.8&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;&#x02009;4</sup> which is statistically significant in comparison to random overlaps. In contrast, the MD-received overlap (58 components or 6.1% as presented in Fig. <xref rid="Fig3" ref-type="fig">3</xref>) resulted in the smaller <italic>p</italic>-value&#x02009;=&#x02009;1.0&#x02009;&#x000d7;&#x02009;10<sup>&#x02212;&#x02009;5</sup>. Comparing the overlap percentage values, SA produced 5-fold and MD even 61-fold higher overlap values than random overlaps. The comparison of the SA- and MD-received overlap values resulted in the above-mentioned 12-fold higher values for MD.</p></sec><sec id="Sec19"><title>Estimation of the best match of transcriptomic and proteomic time points</title><p id="Par31">The selection of measured time points was based on the following assumption: The expression of a gene and the synthesis of its corresponding protein do not occur at the same time since they are consecutive processes. Thus, changes in the transcriptional regulation are also reflected in the differential synthesis of proteins at the proteomic level but most likely at a later time point. Therefore, different time points at the transcriptomic and proteomic level were selected to consider the delay between transcription and translation during the fungal response. Hence, we analyzed our results regarding best matches of level- and time point-dependent sub-responses.</p><p id="Par32">We tested two approaches for estimating the best transcriptome-proteome time point match: Comparison of components, and correlation of the components&#x02019; regulation. The first estimation approach aimed at analyzing overlapping components in the transcriptome and proteome which can be observed, for instance, as transcripts and their synthesized proteins. For the second estimation approach, the correlation of the components&#x02019; regulation was calculated based on absolute gene expression or protein synthesis regulation values. This approach represents the regulation of response pathways which not necessarily contain overlapping components but also other genes or proteins contributing to these pathways. Therefore, in this approach not only the overlapping components were analyzed but also components which are part from at least one of the respectively compared transcriptome and proteome time points. This leads to a higher number of considered components.</p><p id="Par33">Starting with the comparison of components (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4a</xref>), both SA and MD demonstrated the best match for the transcriptomic response 1&#x000a0;h and the proteomic response 4&#x000a0;h after caspofungin treatment (5.3% SA, 16.5% MD). While SA resulted in the best match of transcriptome at 8&#x000a0;h with proteome at 8&#x000a0;h (7.3%), MD showed the best match with transcriptome at 4&#x000a0;h (16.8%). Consequently, for both time point comparisons, MD-produced results indicated a delay of 3&#x02013;4&#x000a0;h between the different sub-responses. Taking into account also the correlation of components&#x02019; regulation, Fig. <xref rid="Fig4" ref-type="fig">4b</xref> shows that similar to the previous analyses MD provided a better, i.e., here lower, distance for MD values than for SA. Oppositely to SA, the MD results confirmed the best time point match of transcriptome at earlier time point (1&#x000a0;h) and proteome at the later one (8&#x000a0;h) (Fig. <xref rid="Fig4" ref-type="fig">4b</xref>), similarly to the aforementioned comparison of components (Fig. <xref rid="Fig4" ref-type="fig">4a</xref>). The lowest distances were observed for the proteome at 8&#x000a0;h and transcriptome at 1&#x000a0;h (Fig. <xref rid="Fig4" ref-type="fig">4b</xref>, highlighted in dark green), followed by the proteome at 4&#x000a0;h and transcriptome at 1&#x000a0;h (Fig.&#x000a0;<xref rid="Fig4" ref-type="fig">4b</xref>, dark green). These findings were also in agreement with the highest and second highest overlap values in Fig. <xref rid="Fig4" ref-type="fig">4a</xref>. Together with the observation that both approaches showed very high distance values (yellow and light yellow) between the same transcriptome and proteome time points, our results support the assumption of a time delay between level-dependent sub-responses (transcription and translation). Tendencies in the coherence of time points and an estimation of the resulting time delay between molecular levels may be helpful for further wet-lab studies regarding time- and cost-saving by focusing on the most relevant time points.<fig id="Fig4"><label>Fig. 4</label><caption><p>Transcriptome-proteome time point match. Estimation of the best time point match for transcriptome (T) and proteome (P) time points regarding <bold>a</bold> comparison of components and <bold>b</bold> correlation of the components&#x02019; regulation. Distance is defined as 1 minus correlation coefficient</p></caption><graphic xlink:href="12918_2018_620_Fig4_HTML" id="MO4"/></fig></p><p id="Par34">Another observation can be made by comparing the respective results of the two estimation approaches: There is a tendency that the correlation-based approach resulted in best matches for earlier transcriptome time points than the overlap-based approach. This observation may be based on the activation of stress response pathways induced by the fungus shortly after the caspofungin treatment. As such response pathways could involve components from both molecular levels transcriptome and proteome, we assume that the actual regulation of response pathways represented by the correlation-based approach already starts before the main translation process of potentially involved components occurs (represented by the overlap-based approach).</p></sec><sec id="Sec20"><title>Integration of multilevel omics data</title><sec id="Sec21"><title>Analysis of the overall fungal response to caspofungin</title><p id="Par35">All regulatory modules of each molecular level and time point identified by MD (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> and Additional file <xref rid="MOESM2" ref-type="media">2:</xref> Table S1) can be considered to be part of the overall fungal response to caspofungin. Forming the union of them, the resulting overall regulatory module (ORM) was composed of five sub-modules including 894 components (Fig.&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref>). For a focused enrichment analysis based on the ORM&#x02019;s underlying topology, we performed a generalized topological overlap measurement regarding the main sub-module 1. Figure&#x000a0;<xref rid="Fig5" ref-type="fig">5</xref> represents the ORM with its five sub-modules and the 15 clusters of sub-module 1, where the cluster membership of each protein is color-coded. An overview of all components of the ORM including sub-modules and clusters is available in Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S2). GO term enrichment analyses showed that the clusters were significantly enriched with distinct biological functions (see Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Tables S3&#x02013;21) for a list of all significantly associated biological processes of each cluster and the remaining sub-modules). Examples of such processes are protein phosphorylation and response to oxidative stress (cluster 2, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S4), actin filament-based process (cluster 3, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S5), regulation of kinase activity (cluster 5, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S7), amino acid metabolic processes (cluster 6, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S8 and cluster 9, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S11), (1,3)-alpha-D-glucan biosynthesis (cluster 7, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S9), secondary and lipid metabolic process (cluster 12, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S14 and cluster 13, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S15) or transport mechanisms (cluster 15, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S17 and sub-module 5, Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S21).<table-wrap id="Tab1"><label>Table 1</label><caption><p>Regulatory modules generated by ModuleDiscoverer</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Underlying experimental dataset</th><th>Number of nodes (components)</th><th>Number of edges (interactions)</th></tr></thead><tbody><tr><td>Transcriptome 0.5&#x000a0;h</td><td>511</td><td>2967</td></tr><tr><td>Transcriptome 1&#x000a0;h</td><td>256</td><td>1336</td></tr><tr><td>Transcriptome 4&#x000a0;h</td><td>313</td><td>1604</td></tr><tr><td>Transcriptome 8&#x000a0;h</td><td>256</td><td>1208</td></tr><tr><td>Proteome 4&#x000a0;h</td><td>147</td><td>845</td></tr><tr><td>Proteome 8&#x000a0;h</td><td>124</td><td>520</td></tr><tr><td>Secretome 8&#x000a0;h</td><td>293</td><td>2413</td></tr><tr><td>Overall regulatory module</td><td>894</td><td>6111</td></tr></tbody></table><table-wrap-foot><p>Number of nodes (representing gene or protein components) and edges (representing interactions between the components) of the regulatory modules received by ModuleDiscoverer</p></table-wrap-foot></table-wrap><fig id="Fig5"><label>Fig. 5</label><caption><p>Overall regulatory module representing the response of <italic>A. fumigatus</italic> to caspofungin. The overall regulatory module identified by ModuleDiscoverer is composed of five sub-modules including 894 components (see Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Table S2). Clusters with exemplarily selected significantly enriched biological processes are color-coded</p></caption><graphic xlink:href="12918_2018_620_Fig5_HTML" id="MO5"/></fig></p></sec></sec><sec id="Sec22"><title>Polyubiquitin and CBF/NF-Y family transcription factor as potential key factors contributing to the caspofungin-induced response</title><p id="Par36">To investigate potential key factors in the fungal response contributing to, e.g., caspofungin-caused side effects, we analyzed the underlying topological network structure of the ORM. We took into account the network node-associated degree (number of edges connected to the node) and betweenness centrality (number of shortest paths that go through each node) [<xref ref-type="bibr" rid="CR13">13</xref>]. We identified the node representing polyubiquitin UbiD with the fifth highest degree (Table <xref rid="Tab2" ref-type="table">2</xref>) and the third highest betweenness centrality (Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>). It was furthermore the only node that could be found in the top ten lists of both measures. Ubiquitin is a highly conserved 76-residue protein which can be found in all eukaryotic organisms [<xref ref-type="bibr" rid="CR43">43</xref>]. In <italic>Saccharomyces cerevisiae,</italic> the orthologous gene UBI4, one out of four ubiquitin genes in yeast, was shown to be essential for resistance to different stresses including high temperatures and starvation [<xref ref-type="bibr" rid="CR44">44</xref>].<table-wrap id="Tab2"><label>Table 2</label><caption><p>Nodes of the overall regulatory module with highest degree</p></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="2">CADRE-IDs</th><th rowspan="2">AspGD-IDs</th><th rowspan="2">Protein names</th><th rowspan="2">Degree</th><th rowspan="2">BC</th><th colspan="7">log2FC</th></tr><tr><th>T 0.5&#x000a0;h</th><th>T 1&#x000a0;h</th><th>T 4&#x000a0;h</th><th>T 8&#x000a0;h</th><th>P 4&#x000a0;h</th><th>P 8&#x000a0;h</th><th>S 8&#x000a0;h</th></tr></thead><tbody><tr><td>CADAFUBP00004294</td><td>AFUB_043760</td><td>Fatty acid synthase beta subunit, putative</td><td>146</td><td>0.126</td><td>&#x02212;1.159</td><td>&#x02212;0.628</td><td>&#x02212;&#x02009;0.693</td><td>&#x02212;&#x02009;0.828</td><td>&#x02212;&#x02009;0.006</td><td>&#x02212;&#x02009;0.136</td><td>0.534</td></tr><tr><td>CADAFUBP00004295</td><td>AFUB_043770</td><td>Fatty acid synthase alpha subunit FasA, putative</td><td>142</td><td>0.082</td><td>&#x02212;1.008</td><td>&#x02212;0.517</td><td>&#x02212;&#x02009;0.678</td><td>&#x02212;&#x02009;0.726</td><td>&#x02212;0.038</td><td>&#x02212;&#x02009;0.105</td><td>1.448</td></tr><tr><td>CADAFUBP00002402</td><td>AFUB_024590</td><td>Acetyl-CoA carboxylase</td><td>124</td><td>0.122</td><td>&#x02212;1.697</td><td>&#x02212;0.812</td><td>&#x02212;1.285</td><td>&#x02212;&#x02009;1.380</td><td>&#x02212;&#x02009;0.456</td><td>&#x02212;0.628</td><td>1.518</td></tr><tr><td>CADAFUBP00004404</td><td>AFUB_044900</td><td>Nonribosomal peptide synthase SidE</td><td>114</td><td>0.048</td><td>1.077</td><td>1.918</td><td>1.613</td><td>1.229</td><td>NA</td><td>NA</td><td>NA</td></tr><tr><td>CADAFUBP00006564</td><td>AFUB_067450</td><td>Polyubiquitin UbiD/Ubi4, putative</td><td>111</td><td>0.396</td><td>&#x02212;0.762</td><td>0.238</td><td>&#x02212;0.248</td><td>&#x02212;0.688</td><td>NA</td><td>NA</td><td>NA</td></tr><tr><td>CADAFUBP00007473</td><td>AFUB_076690</td><td>ATP citrate lyase, subunit 1, putative</td><td>98</td><td>0.037</td><td>&#x02212;1.636</td><td>&#x02212;0.705</td><td>&#x02212;0.698</td><td>&#x02212;&#x02009;0.584</td><td>&#x02212;0.474</td><td>&#x02212;&#x02009;0.521</td><td>0.683</td></tr><tr><td>CADAFUBP00007537</td><td>AFUB_077330</td><td>Bifunctional pyrimidine biosynthesis protein (PyrABCN), putative</td><td>82</td><td>0.073</td><td>&#x02212;0.438</td><td>&#x02212;0.468</td><td>&#x02212;&#x02009;0.214</td><td>&#x02212;0.974</td><td>&#x02212;&#x02009;0.206</td><td>&#x02212;0.340</td><td>1.586</td></tr><tr><td>CADAFUBP00000761</td><td>AFUB_007730</td><td>Glutamate synthase Glt1, putative</td><td>74</td><td>0.035</td><td>&#x02212;2.168</td><td>&#x02212;0.525</td><td>&#x02212;0.119</td><td>&#x02212;&#x02009;0.483</td><td>&#x02212;0.255</td><td>&#x02212;&#x02009;0.234</td><td>1.135</td></tr><tr><td>CADAFUBP00001006</td><td>AFUB_010250</td><td>Succinyl-CoA synthetase, alpha subunit, putative</td><td>72</td><td>0.021</td><td>&#x02212;0.497</td><td>&#x02212;0.273</td><td>0.167</td><td>&#x02212;0.011</td><td>0.234</td><td>0.069</td><td>NA</td></tr><tr><td>CADAFUBP00003062</td><td>AFUB_031240</td><td>Sulfite reductase, putative</td><td>68</td><td>0.047</td><td>&#x02212;0.900</td><td>&#x02212;0.774</td><td>&#x02212;&#x02009;0.103</td><td>&#x02212;0.598</td><td>&#x02212;&#x02009;0.016</td><td>&#x02212;0.022</td><td>1.5</td></tr></tbody></table><table-wrap-foot><p>Top ten nodes of the overall regulatory module showing the highest degree and additional information regarding their betweenness centrality (BC) and gene- or protein-associated log2 Fold Change (log2FC) measured for the transcriptomic (T), proteomic (P) and secretomic (S) fungal response to caspofungin at all time points, respectively</p></table-wrap-foot></table-wrap><table-wrap id="Tab3"><label>Table 3</label><caption><p>Nodes of the overall regulatory module with highest betweenness centrality</p></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="2">CADRE-IDs</th><th rowspan="2">AspGD-IDs</th><th rowspan="2">Protein names</th><th rowspan="2">Degree</th><th rowspan="2">BC</th><th colspan="7">log2FC</th></tr><tr><th>T 0.5&#x000a0;h</th><th>T 1&#x000a0;h</th><th>T 4&#x000a0;h</th><th>T 8&#x000a0;h</th><th>P 4&#x000a0;h</th><th>P 8&#x000a0;h</th><th>S 8&#x000a0;h</th></tr></thead><tbody><tr><td>CADAFUBP00007914</td><td>AFUB_081260</td><td>Peptidyl-arginine deiminase domain protein</td><td>4</td><td>0.6</td><td>0.696</td><td>3.125</td><td>2.328</td><td>1.647</td><td>NA</td><td>NA</td><td>NA</td></tr><tr><td>CADAFUBP00001626</td><td>AFUB_016580</td><td>Long-chain-fatty-acid-CoA ligase, putative</td><td>11</td><td>0.405</td><td>&#x02212;1.395</td><td>&#x02212;1.605</td><td>&#x02212;0.406</td><td>&#x02212;0.750</td><td>NA</td><td>NA</td><td>NA</td></tr><tr><td>CADAFUBP00006564</td><td>AFUB_067450</td><td>Polyubiquitin UbiD/Ubi4, putative</td><td>111</td><td>0.396</td><td>&#x02212;0.762</td><td>0.238</td><td>&#x02212;0.248</td><td>&#x02212;0.688</td><td>NA</td><td>NA</td><td>NA</td></tr><tr><td>CADAFUBP00008739</td><td>AFUB_089890</td><td>Mandelate racemase/muconate lactonizing enzyme family protein</td><td>10</td><td>0.304</td><td>1.791</td><td>0.546</td><td>0.247</td><td>0.055</td><td>NA</td><td>NA</td><td>NA</td></tr><tr><td>CADAFUBP00003378</td><td>AFUB_034540</td><td>Lysophospholipase 3</td><td>5</td><td>0.303</td><td>0.906</td><td>1.357</td><td>1.185</td><td>1.357</td><td>0.706</td><td>0.937</td><td>1.513</td></tr><tr><td>CADAFUBP00002707</td><td>AFUB_027690</td><td>Lysophospholipase</td><td>10</td><td>0.273</td><td>&#x02212;1.454</td><td>&#x02212;0.217</td><td>&#x02212;0.854</td><td>&#x02212;1.700</td><td>0.387</td><td>0.192</td><td>NA</td></tr><tr><td>CADAFUBP00006379</td><td>AFUB_065540</td><td>Patatin-like phospholipase domain-containing protein</td><td>10</td><td>0.273</td><td>&#x02212;0.639</td><td>&#x02212;&#x02009;0.570</td><td>&#x02212;0.931</td><td>&#x02212;1.460</td><td>NA</td><td>NA</td><td>NA</td></tr><tr><td>CADAFUBP00008747</td><td>AFUB_089980</td><td>Ribosome biogenesis protein (Rrs1), putative</td><td>26</td><td>0.259</td><td>1.045</td><td>&#x02212;0.096</td><td>&#x02212;0.154</td><td>0.413</td><td>0.185</td><td>0.045</td><td>NA</td></tr><tr><td>CADAFUBP00004062</td><td>AFUB_041460</td><td>Plasma membrane ATPase</td><td>4</td><td>0.25</td><td>&#x02212;1.535</td><td>&#x02212;0.670</td><td>&#x02212;1.002</td><td>&#x02212;1.152</td><td>0.041</td><td>&#x02212;0.023</td><td>1.910</td></tr><tr><td>CADAFUBP00005096</td><td>AFUB_052070</td><td>Plasma membrane ATPase</td><td>4</td><td>0.25</td><td>&#x02212;0.378</td><td>&#x02212;&#x02009;0.299</td><td>0.441</td><td>0.463</td><td>NA</td><td>NA</td><td>NA</td></tr><tr><td>CADAFUBP00000491</td><td>AFUB_004970</td><td>Alcohol dehydrogenase, zinc-containing, putative</td><td>3</td><td>0.167</td><td>&#x02212;1.712</td><td>&#x02212;1.178</td><td>&#x02212;1.125</td><td>&#x02212;0.941</td><td>&#x02212;0.759</td><td>&#x02212;0.454</td><td>0.914</td></tr></tbody></table><table-wrap-foot><p>Top ten nodes of the overall regulatory module showing the highest betweenness centrality (BC) and additional information regarding their node degree and gene- or protein-associated log2 Fold Change (log2FC) measured for the transcriptomic (T), proteomic (P) and secretomic (S) fungal response to caspofungin at all time points, respectively</p></table-wrap-foot></table-wrap></p><p id="Par37">In addition to this topology-based approach, we also applied an approach focused on transcription factors. Transcription factors play an important role in regulating the compensatory stress response to drugs. However, in many cases, it is difficult to measure transcription factors&#x02019; activity since they are often constitutively expressed and/or activated posttranscriptionally. Therefore, we scanned the ORM for transcription factors connected to DEG-associated proteins, DSyPs or DSePs (Table <xref rid="Tab4" ref-type="table">4</xref>). Among them, we detected the CBF/NF-Y family transcription factor. It shows similarities to DNA polymerase epsilon subunit <italic>DPB4</italic> of <italic>S. cerevisiae</italic> and <italic>Schizosaccharomyces pombe</italic>.<table-wrap id="Tab4"><label>Table 4</label><caption><p>Transcription factors within the overall regulatory module</p></caption><table frame="hsides" rules="groups"><thead><tr><th rowspan="2">CADRE-IDs</th><th rowspan="2">AspGD-IDs</th><th rowspan="2">Protein names</th><th colspan="7">log2FC</th></tr><tr><th>T 0.5&#x000a0;h</th><th>T 1&#x000a0;h</th><th>T 4&#x000a0;h</th><th>T 8&#x000a0;h</th><th>P 4&#x000a0;h</th><th>P 8&#x000a0;h</th><th>S 8&#x000a0;h</th></tr></thead><tbody><tr><td>CADAFUBP00000978</td><td>AFUB_009970</td><td>CBF/NF-Y family transcription factor, putative</td><td>0.230</td><td>0.447</td><td>&#x02212;0.124</td><td>0.002</td><td>&#x02212;0.034</td><td>&#x02212;0.150</td><td>NA</td></tr><tr><td>CADAFUBP00001789</td><td>AFUB_018340</td><td>HLH transcription factor, putative</td><td>&#x02212;1.441</td><td>&#x02212;0.236</td><td>0.128</td><td>&#x02212;0.355</td><td>NA</td><td>NA</td><td>NA</td></tr><tr><td>CADAFUBP00003751</td><td>AFUB_038290</td><td>Zinc knuckle transcription factor/splicing factor MSL5/ZFM1, putative</td><td>0.366</td><td>0.062</td><td>0.111</td><td>0.143</td><td>&#x02212;0.368</td><td>&#x02212;0.322</td><td>3.379</td></tr><tr><td>CADAFUBP00004232</td><td>AFUB_043140</td><td>Transcription elongation factor SPT6, putative</td><td>&#x02212;0.369</td><td>&#x02212;0.235</td><td>&#x02212;&#x02009;0.177</td><td>&#x02212;0.563</td><td>0.143</td><td>0.188</td><td>NA</td></tr><tr><td>CADAFUBP00005084</td><td>AFUB_051950</td><td>PHD transcription factor (Rum1), putative</td><td>&#x02212;1.069</td><td>0.036</td><td>0.089</td><td>&#x02212;0.780</td><td>&#x02212;0.071</td><td>&#x02212;&#x02009;0.042</td><td>NA</td></tr><tr><td>CADAFUBP00007653</td><td>AFUB_078520</td><td>Stress response regulator/HFS transcription factor, putative</td><td>0.317</td><td>&#x02212;0.271</td><td>0.034</td><td>&#x02212;0.102</td><td>&#x02212;0.130</td><td>0.072</td><td>NA</td></tr><tr><td>CADAFUBP00001318</td><td>AFUB_013400</td><td>TFIIH complex helicase Rad3, putative</td><td>0.154</td><td>0.034</td><td>&#x02212;0.055</td><td>&#x02212;&#x02009;0.226</td><td>&#x02212;0.032</td><td>0.222</td><td>NA</td></tr><tr><td>CADAFUBP00003811</td><td>AFUB_038920</td><td>Ccr4-Not transcription complex subunit (NOT1), putative</td><td>&#x02212;0.786</td><td>&#x02212;0.076</td><td>0.038</td><td>&#x02212;0.834</td><td>&#x02212;&#x02009;0.100</td><td>&#x02212;0.041</td><td>NA</td></tr></tbody></table><table-wrap-foot><p>Transcription factors detected in the overall regulatory module and their log2 Fold Change (log2FC) measured for the transcriptomic (T), proteomic (P) and secretomic (S) fungal response to caspofungin at all time points, respectively</p></table-wrap-foot></table-wrap></p><p id="Par38">Both polyubiquitin and the CBF/NF-Y family transcription factor were detected in all transcriptome and, in case of the CBF/NF-Y family transcription factor, proteome time points but neither as DEG nor as DSyP. Figure&#x000a0;<xref rid="Fig6" ref-type="fig">6</xref> represents these two nodes and their respective first neighbors (including DEGs, DSyPs or DSePs) within the ORM.<fig id="Fig6"><label>Fig. 6</label><caption><p>Potential key factors within the overall regulatory module contributing to the caspofungin-caused fungal response. <bold>a</bold> Polyubiquitin and <bold>b</bold> CBF/NF-Y family transcription factor (centrally arranged, respectively) and their first neighbors in the overall regulatory module. DEG-associated proteins, DSyPs and DSePs are highlighted with a yellow border</p></caption><graphic xlink:href="12918_2018_620_Fig6_HTML" id="MO6"/></fig></p><p id="Par39">The investigation of potential key factors in the drug-induced response, like polyubiquitin and CBF/NF-Y family transcription factor, may help to better understand the position and dynamics of drug targets and associated proteins in the interaction network and can potentially contribute to increase the safety of drugs.</p></sec><sec id="Sec23"><title>Caspofungin induces increased production of the secondary metabolite fumagillin</title><p id="Par40">As described above, the ORM contained two clusters, cluster 12 and 13, which included several enzymes that are involved in the biosynthesis of secondary metabolites. In particular, transcripts and their corresponding proteins of the antimicrobial agent fumagillin biosynthesis gene cluster (11 out of 15 cluster genes) showed increased levels after exposure of <italic>A. fumigatus</italic> to caspofungin. To verify whether caspofungin triggers the production of this meroterpenoid, we extracted <italic>A. fumigatus</italic> cultures exposed for 8&#x000a0;h to caspofungin (100&#x000a0;ng/ml) and control cultures with ethyl acetate and determined the fumagillin concentration by LC-MS. In cultures without caspofungin the concentration of fumagillin was 67.3&#x02009;&#x000b1;&#x02009;21.7&#x000a0;&#x003bc;g/ml, while in cultures with caspofungin the concentration increased by 3-fold to 208.1&#x02009;&#x000b1;&#x02009;63.8&#x000a0;&#x003bc;g/ml (Fig.&#x000a0;<xref rid="Fig7" ref-type="fig">7</xref>). The level of other secondary metabolites such as pseurotin A stayed almost unchanged (Additional file <xref rid="MOESM3" ref-type="media">3</xref>).<fig id="Fig7"><label>Fig. 7</label><caption><p>Caspofungin-induced increased production of the secondary metabolite fumagillin. LC-ESI-ITMS extracted ion chromatograms (EIC) at m/z 459.0&#x02013;459.4&#x000a0;amu (left), HPLC-UV/PDA chromatograms (center) and UV/PDA spectra at RT&#x02009;=&#x02009;13.67&#x000a0;min (right) of 250&#x000a0;&#x003bc;g/ml fumagillin reference standard (top) and crude extract of <italic>A. fumigatus</italic> without (center) and with caspofungin treatment (bottom)</p></caption><graphic xlink:href="12918_2018_620_Fig7_HTML" id="MO7"/></fig></p></sec><sec id="Sec24"><title>Comparison of ModuleDiscoverer- and KeyPathwayMiner-generated regulatory modules</title><p id="Par41">To estimate the comprehensiveness of MD-generated regulatory modules, we applied another available module-detecting approach, KeyPathwayMiner (KPM), to our experimental datasets and compared the identified regulatory modules with those identified by MD (Table <xref rid="Tab5" ref-type="table">5</xref>).<table-wrap id="Tab5"><label>Table 5</label><caption><p>Comparison of ModuleDiscoverer- and KeyPathwayMiner-detected regulatory modules</p></caption><table frame="hsides" rules="groups"><thead><tr><th>Underlying experimental dataset</th><th>Component number of MD modules</th><th>Overlap (percentage value regarding KPM module)</th><th>Component number of KPM modules</th></tr></thead><tbody><tr><td>Transcriptome 0.5&#x000a0;h</td><td>511</td><td>134 (75.7%)</td><td>177</td></tr><tr><td>Transcriptome 1&#x000a0;h</td><td>256</td><td>62 (63.9%)</td><td>97</td></tr><tr><td>Transcriptome 4&#x000a0;h</td><td>313</td><td>123 (74.1%)</td><td>166</td></tr><tr><td>Transcriptome 8&#x000a0;h</td><td>256</td><td>89 (65.0%)</td><td>137</td></tr><tr><td>Proteome 4&#x000a0;h</td><td>147</td><td>36 (75.0%)</td><td>48</td></tr><tr><td>Proteome 8&#x000a0;h</td><td>124</td><td>30 (63.8%)</td><td>47</td></tr><tr><td>Secretome 8&#x000a0;h</td><td>293</td><td>42 (93.3%)</td><td>45</td></tr><tr><td>Overall regulatory module</td><td>894</td><td>343 (59.6%)</td><td>576</td></tr></tbody></table><table-wrap-foot><p>Comparison of ModuleDiscoverer (MD) and KeyPathwayMiner (KPM) regarding their number of module components. The overlap is defined as fraction of the intersection of the respective datasets from the KPM datasets</p></table-wrap-foot></table-wrap></p><p id="Par42">Table <xref rid="Tab5" ref-type="table">5</xref> shows the numbers of components of the KPM-produced regulatory modules for each time point and the overall regulatory module in comparison with those based on MD. Exemplarily, the comparison showed that the ORM received by MD contains a 1.5-fold higher number of components by covering more than 60% of KPM module components. Considering the modules of the single time point datasets, e.g. secretome at 8&#x000a0;h, we found an up to 6.5-fold higher component number by covering up to 93% of KPM components. Hence, we focused on the results received by MD. Nevertheless, additional KPM analyses regarding the overlap of molecular levels and the estimation of the best match of transcriptomic and proteomic time points are shown in Additional file <xref rid="MOESM2" ref-type="media">2</xref>: Figures S1 and S2 and Additional file <xref rid="MOESM4" ref-type="media">4</xref>.</p></sec></sec><sec id="Sec25"><title>Discussion</title><p id="Par43">In this study, we focused on the integration of omics data derived from heterogeneous sources. Therefore, we used experimental data of an <italic>A. fumigatus</italic> study investigating the stress response to the antifungal drug caspofungin at different molecular levels and time points. For the analyses, we applied SA considering only DEGs/DSyPs/DSePs and the regulatory module-detecting single-seed MD approach considering DEGs/DSyPs/DSePs, non-DEGs/DSyPs/DSePs as well as structural PPIN information. We focused on the single-seed approach instead of the also available multi-seed MD approach since the single-seed approach is comparable with other well-established maximal clique enumeration problem-based algorithms (e.g., Barren&#x000e4;s et al. [<xref ref-type="bibr" rid="CR45">45</xref>] or Gustafsson et al. [<xref ref-type="bibr" rid="CR46">46</xref>]). In addition, Vlaic et al. showed that the multi-seed-identified modules can be essentially considered as an extension of the single-seed modules. However, we also applied the multi-seed approach to our experimental data set. In summary, the multi-seed MD approach allows for effectively integrating multilevel omics data. Multi-seed-generated results contain the regulatory modules received by the single-seed approach and are even more comprehensive. The overall regulatory module generated by the multi-seed approach confirms the already observed key players and significantly associated processes. Details on the analyses can be found in the Additional files <xref rid="MOESM2" ref-type="media">2</xref> and <xref rid="MOESM5" ref-type="media">5</xref>.</p><sec id="Sec26"><title>Relation of transcriptomic, proteomic and secretomic data</title><p id="Par44">The comparison of all three molecular levels regarding all measured, SA- or MD-considered components resulted in only small overlap values. This observation is in agreement with other integrative transcriptomic and proteomic studies reporting that there is no or only a weak correlation between different molecular levels [<xref ref-type="bibr" rid="CR47">47</xref>&#x02013;<xref ref-type="bibr" rid="CR49">49</xref>]. Potential explanations are biological (e.g., translational regulation or differences in protein and mRNA half-lives in vivo<italic>)</italic> or methodological origins (e.g., detection limits of the techniques or the choice of measured time points) [<xref ref-type="bibr" rid="CR48">48</xref>, <xref ref-type="bibr" rid="CR49">49</xref>]. Figures&#x000a0;<xref rid="Fig2" ref-type="fig">2a</xref> and <xref rid="Fig3" ref-type="fig">3</xref> show an apparently contradictory outcome regarding the overlap of datasets of different molecular levels: Fig. <xref rid="Fig2" ref-type="fig">2a</xref> shows the highest overlap percentage value for transcriptome and proteome, Fig. <xref rid="Fig3" ref-type="fig">3</xref> for proteome and secretome. This can be explained by the fact that Figs. <xref rid="Fig2" ref-type="fig">2a</xref> and <xref rid="Fig3" ref-type="fig">3</xref> are based on analyses that considered diverse datasets. For Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2a</xref>, all detected genes and proteins were analyzed. In contrast, Fig. <xref rid="Fig3" ref-type="fig">3</xref> comprises only a fraction of these components because of a further filtering step to only compare DEGs/DSyPs/DSePs (SA) or regulatory module components (DEGs/DSyPs/DSePs and associated background proteins, MD). Actually, in Fig. <xref rid="Fig3" ref-type="fig">3</xref>, both approaches MD and SA showed the highest overlap between proteome and secretome. On the one hand, this highest overlap percentage reflects the same underlying measurement technique. In this study, the transcriptome was measured by RNA-Seq, the proteome and secretome by LC-MS/MS. As the techniques themselves are very different, also differences in their respective outcome can be expected. Therefore, as the intracellular proteome and secretome are based on the same measurement technique, they are more similar to each other than, for instance, transcriptome and proteome. On the other hand, the highest overlap also demonstrates the biological similarity in terms of immediately consecutive protein-based levels. Thus, both levels consist of proteins which differ only in the secretion step via classical (i.e., N-terminal secretory signal peptide triggered) or non-classical (i.e., without involvement of N-terminal signal peptides) secretory pathways [<xref ref-type="bibr" rid="CR50">50</xref>]. Hence, proteome and secretome can be considered as immediately consecutive levels which can both be measured by LC-MS/MS.</p><p id="Par45">By a general comparison of MD- and SA-received results, we determined up to 12-fold higher overlap values provided by MD than those calculated by SA. This is reasonable as SA focuses on the comparison of lists of DEGs, DSyPs and DSePs, exclusively. Hence, non-DEGs/DSyPs/DSePs measured in the experimental background were not considered which results in a high loss of data for the analyses. In contrast, the additional information considered by MD led to a much higher number of (overlapping) components.</p></sec><sec id="Sec27"><title>Analysis of the overall fungal response and potential key factors</title><p id="Par46">With the aid of the ORM, we analyzed the <italic>A. fumigatus</italic> response to caspofungin over all molecular levels and time points. We found that ORM clusters are significantly enriched with biological functions like (1,3)-alpha-D-glucan biosynthesis and carbohydrate metabolic processes, actin filament-based processes, activation of protein kinase activity and response to oxidative stress. These results are in agreement with a genome-wide expression profiling study of <italic>Aspergillus niger</italic> in response to caspofungin [<xref ref-type="bibr" rid="CR51">51</xref>]. Here, many of the upregulated genes were predicted or confirmed to function in cell wall assembly and remodeling, cytoskeletal organization, signaling and oxidative stress response. Also, genes and proteins of the electron transport chain were specifically enriched which supports the hypothesis that caspofungin acts as an effector of mitochondrial oxidative phosphorylation [<xref ref-type="bibr" rid="CR52">52</xref>]. This is consistent with results from Cagas et al. [<xref ref-type="bibr" rid="CR47">47</xref>] who analyzed the proteomic response of <italic>A. fumigatus</italic> to caspofungin and identified the largest change in a mitochondrial protein that has a role in mitochondrial respiratory chain complex IV assembly. The significant enrichment of genes and proteins of the amino acid metabolic process is best explained by the growth inhibitory activity of caspofungin that leads to the downregulation of the primary metabolisms including amino acid biosynthesis [<xref ref-type="bibr" rid="CR53">53</xref>].</p><p id="Par47">The cluster 5 represents (gene-associated) proteins involved in the activation of protein kinase activity. Mitogen-activated kinases (MAPK) are important regulators in the fungal response to stress that is induced by environmental changes or the disruption of cell wall integrity ([<xref ref-type="bibr" rid="CR54">54</xref>], and references therein) which are both consequences of the caspofungin treatment. Also cellular transport mechanisms were influenced by this antifungal drug leading to osmotic stress as already reported in Altwasser et al. [<xref ref-type="bibr" rid="CR26">26</xref>]. In addition, we observed the association of ORM cluster components with the (1,3)-alpha-D-glucan biosynthesis as well as carbohydrate metabolic processes. Consistently, caspofungin inhibits the synthesis of &#x003b2;-(1,3)-glucan which is the principal component of the fungal cell wall [<xref ref-type="bibr" rid="CR55">55</xref>]. As a compensatory response, the production of other cell wall polymers was stimulated. Another interesting finding was the increased production of the secondary metabolite fumagillin upon exposure of <italic>A. fumigatus</italic> to caspofungin. So far, only the release of the secondary metabolite gliotoxin has been reported for cultures of <italic>A. fumigatus</italic> in the presence of caspofungin [<xref ref-type="bibr" rid="CR56">56</xref>]. Fumagillin has anti-angiogenic activity [<xref ref-type="bibr" rid="CR57">57</xref>] and induces cell death in erythrocytes [<xref ref-type="bibr" rid="CR58">58</xref>]. It is therefore possible that administration of caspofungin induces the production of secondary metabolites that have adverse effects on host cells during the infection. Another interesting aspect of our finding is that the induction of fumagillin production upon caspofungin exposure may represent a form of &#x02018;microbial communication&#x02019; between fungi, in particular taking into account that echinocandins like caspofungin are produced by a diverse set of fungi [<xref ref-type="bibr" rid="CR59">59</xref>].</p><p id="Par48">As Wang et al. [<xref ref-type="bibr" rid="CR13">13</xref>] reported, studying key factors of a drug-induced response by analyzing the underlying network structure may help to better understand the position and dynamics of drug targets and associated proteins potentially involved in drug-caused side effects. Here, in addition to the main target &#x003b2;-(1,3)-D-glucan synthase, we detected polyubiquitin UbiD among the top five nodes of the ORM ranked by both node degree and betweenness centrality. Polyubiquitin is known to encode multiple ubiquitin units in tandem, each of these transcribed as a single transcript. It is involved in several metabolic pathways and plays an important role in the regulation of the proteasome-based protein degradation processes [<xref ref-type="bibr" rid="CR43">43</xref>, <xref ref-type="bibr" rid="CR60">60</xref>]. Some recent studies have already reported the importance of polyubiquitin in the fungal stress response. In the pathogenic yeast <italic>Candida albicans</italic>, Leach et al. [<xref ref-type="bibr" rid="CR61">61</xref>] have shown that polyubiquitin is required for the adaption to sudden stress induced, e.g., by heat or caspofungin and is critical for the fungus&#x02019; pathogenicity. In another study in <italic>S. cerevisiae</italic>, Lesage et al. [<xref ref-type="bibr" rid="CR62">62</xref>] described ubiquitin-related protein degradation as an important process in the compensation for defects in glucan biosynthesis. We hypothesize that polyubiquitin is an important player in the compensatory response of <italic>A. fumigatus</italic> to caspofungin. In line, the corresponding gene <italic>ubi4</italic> was shown to be induced upon heat-shock in <italic>A. nidulans</italic> [<xref ref-type="bibr" rid="CR43">43</xref>].</p><p id="Par49">Exemplarily, CBF/NF-Y family transcription factor was detected among the list of TFs. Its <italic>C. albicans</italic> ortholog DPB4 represents a putative DNA polymerase epsilon subunit D and was shown to be involved in filamentous growth and maintenance of the mitochondrial DNA genome [<xref ref-type="bibr" rid="CR63">63</xref>]. This role in mitochondrial processes in conjunction with caspofungin treatment is in agreement with the in previous studies shown importance of mitochondrial functions for drug tolerance and virulence of fungal pathogens ([<xref ref-type="bibr" rid="CR47">47</xref>], and references therein). Also for <italic>C. albicans</italic>, Khamooshi et al. [<xref ref-type="bibr" rid="CR64">64</xref>] have reported that deletion of DPB4 results in a decreased resistance to caspofungin in drop plate assays. These facts could indicate an involvement of CBF/NF-Y family transcription factor in the resistance of <italic>A. fumigatus</italic> to caspofungin<italic>.</italic></p><p id="Par50">Interestingly, in our study, both the polyubiquitin and the CBF/NF-Y family transcription factor were detected in all transcriptome and, in case of CBF/NF-Y family transcription factor, proteome time points but neither as DEG nor as DSyP. However, their location within the ORM had shown that they are closely related to DEGs, DSyPs or DSePs. Consequently, by considering DEGs, DSyPs or DSePs for data analyses by SA, these proteins would not have been taken into account as factors in the fungal response despite the fact that they likely have a strong influence on DEGs, DSyPs or DSePs as shown in the ORM. To our knowledge, the role of both the polyubiquitin and the CBF/NF-Y family transcription factor has not been examined yet in the context of caspofungin-induced stress in <italic>A. fumigatus.</italic> Hence, our analyses offer novel hypotheses which have to be verified in future studies.</p></sec><sec id="Sec28"><title>The module-detecting approach KeyPathwayMiner</title><p id="Par51">In addition to MD, also other approaches identifying regulatory modules are available, for instance, KPM. Similar to MD, KPM can be used for the analyses of both, single-level and multilevel omics data. However, it does not make assumptions about community structures. KPM combines DEGs, DSyPs or DSePs with non-DEG/DSyP/DSeP exception nodes acting as &#x02018;bridges&#x02019; to detect maximal connected sub-networks [<xref ref-type="bibr" rid="CR15">15</xref>]. The comparison of MD- and KPM-generated regulatory modules showed that MD generates modules with a significant higher number of components than KPM. Additionally, these MD module components cover most of the KPM components. As these findings indicate that MD-generated modules are more comprehensive than modules derived by KPM, we focused on the results obtained by MD.</p></sec><sec id="Sec29"><title>PPIN information as limiting factor</title><p id="Par52">The basis of module-detecting approaches like MD or KPM is information from underlying organism-specific PPINs. Hence, the quality of results provided by these approaches also depends on the comprehensiveness of the underlying PPIN itself. Only those components of the experimental data which do also occur in the PPIN are considered for the regulatory module. For example, the PPIN of <italic>A. fumigatus</italic> strain A1163 downloaded from STRING consists of 4123 proteins. But according to current information provided by CADRE, the fungus itself is known to comprise 9916 protein-coding genes. Hence, more than half of the known fungal components cannot be considered for analyses based on this PPIN. Consequently, the available PPIN information can be considered as limiting factor in the data analyses. Thus, while our results highlight the benefits and potential provided by the regulatory module detection-based analysis of multilevel omics data, future studies will have to focus on the expansion of organism-specific PPINs.</p></sec></sec><sec id="Sec30"><title>Conclusion</title><p id="Par53">PPINs enable the consideration of both structural and functional relationships between network proteins. Thus, they facilitate a focused view on closely related components in terms of modules. In this study, we demonstrated so far untested capacity of the module-detecting MD approach to integrate omics data coming from different molecular levels and time points. Moreover, we showed that this level of integration is not achievable using a simple approach of comparing lists of DEGs/DSyPs/DSePs. The integration of these data in one ORM can provide an overview of the overall organism&#x02019;s response to an external stimulus. We presented several approaches for analyzing this response and potential key factors contributing to, e.g., drug-caused side effects in more detail. With the aid of the regulatory module-detecting approach, it is possible to identify potential response key factors which cannot be detected in commonly used approaches comparing DEGs, DSyPs and DSePs, exclusively.</p></sec><sec sec-type="supplementary-material"><title>Additional files</title><sec id="Sec31"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12918_2018_620_MOESM1_ESM.xlsx"><label>Additional file 1:</label><caption><p> Lists of differentially expressed genes, differentially synthesized proteins and differentially secreted proteins. (XLSX 227 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12918_2018_620_MOESM2_ESM.pdf"><label>Additional file 2:</label><caption><p> Supplementary Materials. (PDF 3110 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12918_2018_620_MOESM3_ESM.xlsx"><label>Additional file 3:</label><caption><p> Quantification of the secondary metabolites fumagillin and pseurotin A. (XLSX 21 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM4"><media xlink:href="12918_2018_620_MOESM4_ESM.xlsx"><label>Additional file 4:</label><caption><p> KeyPathwayMiner-generated overall regulatory module and significantly enriched biological processes. (XLSX 55 kb)</p></caption></media></supplementary-material>
<supplementary-material content-type="local-data" id="MOESM5"><media xlink:href="12918_2018_620_MOESM5_ESM.xlsx"><label>Additional file 5:</label><caption><p> Significantly enriched biological processes of the MD-multi-seed-based overall regulatory module. (XLSX 49 kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><ack><title>Acknowledgements</title><p>The authors would like to thank Silke Steinbach for excellent technical assistance. We also thank Dominik Driesch for fruitful discussions.</p><sec id="FPar1"><title>Funding</title><p id="Par54">This work was supported by the Jena School for Microbial Communication (JSMC) [to TC], Deutsche Forschungsgemeinschaft (DFG) CRC/Transregio 124 &#x02018;Pathogenic fungi and their human host: Networks of interaction&#x02019; (subprojects A1 [to AAB], C5 [to IJ], INF [to JL, RG] and Z2 [to OK, TK]), Th&#x000fc;ringer Aufbaubank (TAB) [to JL] and the German Federal Ministry of Education &#x00026; Research (BMBF FKZ 0315439) [to AAB, VV].</p></sec><sec id="FPar2"><title>Availability of data and materials</title><p id="Par55">The datasets supporting the conclusion of this article are included within the article and its additional files. The mass spectrometry proteomics data have been deposited to the ProteomeXchange Consortium via the PRIDE [<xref ref-type="bibr" rid="CR65">65</xref>] partner repository with the dataset identifier PXD008153. The RNA-Seq data that support the findings of this study are available as mentioned in Altwasser et al. [<xref ref-type="bibr" rid="CR26">26</xref>].</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>TC, SGH, TK, JL and SV performed the data analyses. OK, DJM and VV performed the experiments. TC, OK, SGH, RG, IDJ, AAB, SV and JL interpreted the results. TC, OK, TK, SV and JL wrote the paper. All authors read and approved the final manuscript.</p></notes><notes notes-type="COI-statement"><sec id="FPar3"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec><sec id="FPar4"><title>Consent for publication</title><p>Not applicable.</p></sec><sec id="FPar5"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec id="FPar6"><title>Publisher&#x02019;s Note</title><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ebrahim</surname><given-names>A</given-names></name><name><surname>Brunk</surname><given-names>E</given-names></name><name><surname>Tan</surname><given-names>J</given-names></name><name><surname>O&#x02019;Brien</surname><given-names>EJ</given-names></name><name><surname>Kim</surname><given-names>D</given-names></name><name><surname>Szubin</surname><given-names>R</given-names></name><etal/></person-group><article-title>Multi-omic data integration enables discovery of hidden biological regularities</article-title><source>Nat Commun</source><year>2016</year><volume>7</volume><fpage>13091</fpage><pub-id pub-id-type="doi">10.1038/ncomms13091</pub-id><pub-id pub-id-type="pmid">27782110</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wu</surname><given-names>Y</given-names></name><name><surname>Williams</surname><given-names>EG</given-names></name><name><surname>Dubuis</surname><given-names>S</given-names></name><name><surname>Mottis</surname><given-names>A</given-names></name><name><surname>Jovaisaite</surname><given-names>V</given-names></name><name><surname>Houten</surname><given-names>SM</given-names></name><etal/></person-group><article-title>Multilayered genetic and omics dissection of mitochondrial activity in a mouse reference population</article-title><source>Cell</source><year>2014</year><volume>158</volume><fpage>1415</fpage><lpage>1430</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2014.07.039</pub-id><?supplied-pmid 25215496?><pub-id pub-id-type="pmid">25215496</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chen</surname><given-names>R</given-names></name><name><surname>Mias</surname><given-names>GI</given-names></name><name><surname>Li-Pook-Than</surname><given-names>J</given-names></name><name><surname>Jiang</surname><given-names>L</given-names></name><name><surname>Lam</surname><given-names>HYK</given-names></name><name><surname>Chen</surname><given-names>R</given-names></name><etal/></person-group><article-title>Personal omics profiling reveals dynamic molecular and medical phenotypes</article-title><source>Cell</source><year>2012</year><volume>148</volume><fpage>1293</fpage><lpage>1307</lpage><pub-id pub-id-type="doi">10.1016/j.cell.2012.02.009</pub-id><pub-id pub-id-type="pmid">22424236</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Aebersold</surname><given-names>R</given-names></name><name><surname>Mann</surname><given-names>M</given-names></name></person-group><article-title>Mass-spectrometric exploration of proteome structure and function</article-title><source>Nature</source><year>2016</year><volume>537</volume><fpage>347</fpage><lpage>355</lpage><pub-id pub-id-type="doi">10.1038/nature19949</pub-id><pub-id pub-id-type="pmid">27629641</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Michalski</surname><given-names>A</given-names></name><name><surname>Cox</surname><given-names>J</given-names></name><name><surname>Mann</surname><given-names>M</given-names></name></person-group><article-title>More than 100,000 detectable peptide species elute in single shotgun proteomics runs but the majority is inaccessible to data-dependent LC-MS/MS</article-title><source>J Proteome Res</source><year>2011</year><volume>10</volume><fpage>1785</fpage><lpage>1793</lpage><pub-id pub-id-type="doi">10.1021/pr101060v</pub-id><pub-id pub-id-type="pmid">21309581</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>List</surname><given-names>M</given-names></name><name><surname>Alcaraz</surname><given-names>N</given-names></name><name><surname>Dissing-Hansen</surname><given-names>M</given-names></name><name><surname>Ditzel</surname><given-names>HJ</given-names></name><name><surname>Mollenhauer</surname><given-names>J</given-names></name><name><surname>Baumbach</surname><given-names>J</given-names></name></person-group><article-title>KeyPathwayMinerWeb: online multi-omics network enrichment</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>44</volume><fpage>W98</fpage><lpage>104</lpage><pub-id pub-id-type="doi">10.1093/nar/gkw373</pub-id><pub-id pub-id-type="pmid">27150809</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Peng</surname><given-names>C</given-names></name><name><surname>Li</surname><given-names>A</given-names></name><name><surname>Wang</surname><given-names>M</given-names></name></person-group><article-title>Discovery of bladder Cancer-related genes using integrative heterogeneous network modeling of multi-omics data</article-title><source>Sci Rep</source><year>2017</year><volume>7</volume><fpage>15639</fpage><pub-id pub-id-type="doi">10.1038/s41598-017-15890-9</pub-id><pub-id pub-id-type="pmid">29142286</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><mixed-citation publication-type="other">Hua J, Koes D, Kou Z. Finding motifs in protein-protein interaction networks. Proj Final Rep. 2003. <ext-link ext-link-type="uri" xlink:href="http://www.cs.cmu.edu/~dkoes/research/prot-prot.pdf">www.cs.cmu.edu/~dkoes/research/prot-prot.pdf</ext-link>.</mixed-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tornow</surname><given-names>S</given-names></name></person-group><article-title>Functional modules by relating protein interaction networks and gene expression</article-title><source>Nucleic Acids Res</source><year>2003</year><volume>31</volume><fpage>6283</fpage><lpage>6289</lpage><pub-id pub-id-type="doi">10.1093/nar/gkg838</pub-id><?supplied-pmid 14576317?><pub-id pub-id-type="pmid">14576317</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Stuart</surname><given-names>JM</given-names></name><name><surname>Segal</surname><given-names>E</given-names></name><name><surname>Koller</surname><given-names>D</given-names></name><name><surname>Kim</surname><given-names>SK</given-names></name></person-group><article-title>A gene-coexpression network for global discovery of conserved genetic modules</article-title><source>Science (80- )</source><year>2003</year><volume>302</volume><fpage>249</fpage><lpage>255</lpage><pub-id pub-id-type="doi">10.1126/science.1087447</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Trevino</surname><given-names>V</given-names></name><name><surname>Cassese</surname><given-names>A</given-names></name><name><surname>Nagy</surname><given-names>Z</given-names></name><name><surname>Zhuang</surname><given-names>X</given-names></name><name><surname>Herbert</surname><given-names>J</given-names></name><name><surname>Antzack</surname><given-names>P</given-names></name><etal/></person-group><article-title>A network biology approach identifies molecular cross-talk between Normal prostate epithelial and prostate carcinoma cells</article-title><source>PLoS Comput Biol</source><year>2016</year><volume>12</volume><issue>4</issue><fpage>e1004884</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1004884</pub-id><pub-id pub-id-type="pmid">27124473</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>McGee</surname><given-names>SR</given-names></name><name><surname>Tibiche</surname><given-names>C</given-names></name><name><surname>Trifiro</surname><given-names>M</given-names></name><name><surname>Wang</surname><given-names>E</given-names></name></person-group><article-title>Network analysis reveals a signaling regulatory loop in the PIK3CA-mutated breast Cancer predicting survival outcome</article-title><source>Genomics Proteomics Bioinformatics</source><year>2017</year><volume>15</volume><fpage>121</fpage><lpage>129</lpage><pub-id pub-id-type="doi">10.1016/j.gpb.2017.02.002</pub-id><pub-id pub-id-type="pmid">28392480</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>X</given-names></name><name><surname>Thijssen</surname><given-names>B</given-names></name><name><surname>Yu</surname><given-names>H</given-names></name></person-group><article-title>Target essentiality and centrality characterize drug side effects</article-title><source>PLoS Comput Biol</source><year>2013</year><volume>9</volume><issue>7</issue><fpage>e1003119</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1003119</pub-id><pub-id pub-id-type="pmid">23874169</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Hartwell</surname><given-names>LH</given-names></name><name><surname>Hopfield</surname><given-names>JJ</given-names></name><name><surname>Leibler</surname><given-names>S</given-names></name><name><surname>Murray</surname><given-names>AW</given-names></name></person-group><article-title>From molecular to modular cell biology</article-title><source>Nature</source><year>1999</year><volume>402</volume><fpage>C47</fpage><lpage>C52</lpage><pub-id pub-id-type="doi">10.1038/35011540</pub-id><?supplied-pmid 10591225?><pub-id pub-id-type="pmid">10591225</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ulitsky</surname><given-names>I</given-names></name><name><surname>Shamir</surname><given-names>R</given-names></name></person-group><article-title>Identification of functional modules using network topology and high-throughput data</article-title><source>BMC Syst Biol</source><year>2007</year><volume>1</volume><fpage>8</fpage><pub-id pub-id-type="doi">10.1186/1752-0509-1-8</pub-id><pub-id pub-id-type="pmid">17408515</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Segal</surname><given-names>E</given-names></name><name><surname>Shapira</surname><given-names>M</given-names></name><name><surname>Regev</surname><given-names>A</given-names></name><name><surname>Pe&#x02019;er</surname><given-names>D</given-names></name><name><surname>Botstein</surname><given-names>D</given-names></name><name><surname>Koller</surname><given-names>D</given-names></name><etal/></person-group><article-title>Module networks: identifying regulatory modules and their condition-specific regulators from gene expression data</article-title><source>Nat Genet</source><year>2003</year><volume>34</volume><fpage>166</fpage><lpage>176</lpage><pub-id pub-id-type="doi">10.1038/ng1165</pub-id><pub-id pub-id-type="pmid">12740579</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ulitsky</surname><given-names>Igor</given-names></name><name><surname>Krishnamurthy</surname><given-names>Akshay</given-names></name><name><surname>Karp</surname><given-names>Richard M.</given-names></name><name><surname>Shamir</surname><given-names>Ron</given-names></name></person-group><article-title>DEGAS: De Novo Discovery of Dysregulated Pathways in Human Diseases</article-title><source>PLoS ONE</source><year>2010</year><volume>5</volume><issue>10</issue><fpage>e13367</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0013367</pub-id><pub-id pub-id-type="pmid">20976054</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alcaraz</surname><given-names>N</given-names></name><name><surname>Pauling</surname><given-names>J</given-names></name><name><surname>Batra</surname><given-names>R</given-names></name><name><surname>Barbosa</surname><given-names>E</given-names></name><name><surname>Junge</surname><given-names>A</given-names></name><name><surname>Christensen</surname><given-names>AGL</given-names></name><etal/></person-group><article-title>KeyPathwayMiner 4.0: condition-specific pathway analysis by combining multiple omics studies and networks with Cytoscape</article-title><source>BMC Syst Biol</source><year>2014</year><volume>8</volume><fpage>99</fpage><pub-id pub-id-type="doi">10.1186/s12918-014-0099-x</pub-id><pub-id pub-id-type="pmid">25134827</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vlaic</surname><given-names>S</given-names></name><name><surname>Conrad</surname><given-names>T</given-names></name><name><surname>Tokarski-Schnelle</surname><given-names>C</given-names></name><name><surname>Gustafsson</surname><given-names>M</given-names></name><name><surname>Dahmen</surname><given-names>U</given-names></name><name><surname>Guthke</surname><given-names>R</given-names></name><etal/></person-group><article-title>ModuleDiscoverer: identification of regulatory modules in protein-protein interaction networks</article-title><source>Sci Rep</source><year>2018</year><volume>8</volume><issue>1</issue><fpage>433</fpage><pub-id pub-id-type="doi">10.1038/s41598-017-18370-2</pub-id><pub-id pub-id-type="pmid">29323246</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Van De Veerdonk</surname><given-names>FL</given-names></name><name><surname>Gresnigt</surname><given-names>MS</given-names></name><name><surname>Romani</surname><given-names>L</given-names></name><name><surname>Netea</surname><given-names>MG</given-names></name><name><surname>Latg&#x000e9;</surname><given-names>JP</given-names></name></person-group><article-title><italic>Aspergillus fumigatus</italic> morphology and dynamic host interactions</article-title><source>Nat Rev Microbiol</source><year>2017</year><volume>15</volume><fpage>661</fpage><lpage>674</lpage><pub-id pub-id-type="doi">10.1038/nrmicro.2017.90</pub-id><pub-id pub-id-type="pmid">28919635</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><mixed-citation publication-type="other">Moreno-Vel&#x000e1;squez SD, Seidel C, Juvvadi PR, Steinbach WJ, Read ND. Caspofungin-mediated growth inhibition and paradoxical growth in <italic>Aspergillus fumigatus</italic> involve fungicidal hyphal tip lysis coupled with regenerative intrahyphal growth and dynamic changes in &#x003b2;-1,3-glucan synthase localization. Antimicrob Agents Chemother. 2017;61. 10.1128/AAC.00710-17.</mixed-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Spriggs</surname><given-names>KA</given-names></name><name><surname>Bushell</surname><given-names>M</given-names></name><name><surname>Willis</surname><given-names>AE</given-names></name></person-group><article-title>Translational regulation of gene expression during conditions of cell stress</article-title><source>Mol Cell</source><year>2010</year><volume>40</volume><fpage>228</fpage><lpage>237</lpage><pub-id pub-id-type="doi">10.1016/j.molcel.2010.09.028</pub-id><pub-id pub-id-type="pmid">20965418</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Huber</surname><given-names>W</given-names></name><name><surname>Carey</surname><given-names>VJ</given-names></name><name><surname>Gentleman</surname><given-names>R</given-names></name><name><surname>Anders</surname><given-names>S</given-names></name><name><surname>Carlson</surname><given-names>M</given-names></name><name><surname>Carvalho</surname><given-names>BS</given-names></name><etal/></person-group><article-title>Orchestrating high-throughput genomic analysis with Bioconductor</article-title><source>Nat Methods</source><year>2015</year><volume>12</volume><fpage>115</fpage><lpage>121</lpage><pub-id pub-id-type="doi">10.1038/nmeth.3252</pub-id><pub-id pub-id-type="pmid">25633503</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>da Silva Ferreira</surname><given-names>ME</given-names></name><name><surname>Kress</surname><given-names>MR</given-names></name><name><surname>Savoldi</surname><given-names>M</given-names></name><name><surname>Goldman</surname><given-names>MH</given-names></name><name><surname>H&#x000e4;rtl</surname><given-names>A</given-names></name><name><surname>Heinekamp</surname><given-names>T</given-names></name><etal/></person-group><article-title>The akuB KU80 mutant deficient for nonhomologous end joining is a powerful tool for analyzing pathogenicity in <italic>Aspergillus fumigatus</italic></article-title><source>Eukaryot Cell</source><year>2006</year><volume>5</volume><fpage>207</fpage><lpage>211</lpage><pub-id pub-id-type="doi">10.1128/EC.5.**************</pub-id><pub-id pub-id-type="pmid">16400184</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Brakhage</surname><given-names>AA</given-names></name><name><surname>Van den Brulle</surname><given-names>J</given-names></name></person-group><article-title>Use of reporter genes to identify recessive trans-acting mutations specifically involved in the regulation of <italic>Aspergillus nidulans</italic> penicillin biosynthesis genes</article-title><source>J Bacteriol</source><year>1995</year><volume>177</volume><fpage>2781</fpage><lpage>2788</lpage><pub-id pub-id-type="doi">10.1128/jb.177.10.2781-2788.1995</pub-id><pub-id pub-id-type="pmid">7677843</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Altwasser</surname><given-names>R</given-names></name><name><surname>Baldin</surname><given-names>C</given-names></name><name><surname>Weber</surname><given-names>J</given-names></name><name><surname>Guthke</surname><given-names>R</given-names></name><name><surname>Kniemeyer</surname><given-names>O</given-names></name><name><surname>Brakhage</surname><given-names>AA</given-names></name><etal/></person-group><article-title>Network modeling reveals cross talk of MAP kinases during adaptation to caspofungin stress in <italic>aspergillus fumigatus</italic></article-title><source>PLoS One</source><year>2015</year><volume>10</volume><issue>9</issue><fpage>e0136932</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0136932</pub-id><pub-id pub-id-type="pmid">26356475</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cerqueira</surname><given-names>GC</given-names></name><name><surname>Arnaud</surname><given-names>MB</given-names></name><name><surname>Inglis</surname><given-names>DO</given-names></name><name><surname>Skrzypek</surname><given-names>MS</given-names></name><name><surname>Binkley</surname><given-names>G</given-names></name><name><surname>Simison</surname><given-names>M</given-names></name><etal/></person-group><article-title>The <italic>Aspergillus</italic> genome database: multispecies curation and incorporation of RNA-Seq data to improve structural gene annotations</article-title><source>Nucleic Acids Res</source><year>2014</year><volume>42</volume><fpage>D705</fpage><lpage>D710</lpage><pub-id pub-id-type="doi">10.1093/nar/gkt1029</pub-id><pub-id pub-id-type="pmid">24194595</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mabey</surname><given-names>J</given-names></name><name><surname>Anderson</surname><given-names>M</given-names></name><name><surname>Giles</surname><given-names>P</given-names></name><name><surname>Miller</surname><given-names>C</given-names></name><name><surname>Attwood</surname><given-names>T</given-names></name><name><surname>Paton</surname><given-names>N</given-names></name><etal/></person-group><article-title>CADRE: the central <italic>Aspergillus</italic> data REpository</article-title><source>Nucleic Acids Res</source><year>2004</year><volume>1</volume><fpage>D401</fpage><lpage>D405</lpage><pub-id pub-id-type="doi">10.1093/nar/gkh009</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Durinck</surname><given-names>S</given-names></name><name><surname>Spellman</surname><given-names>PT</given-names></name><name><surname>Birney</surname><given-names>E</given-names></name><name><surname>Huber</surname><given-names>W</given-names></name></person-group><article-title>Mapping identifiers for the integration of genomic datasets with the R/ Bioconductor package biomaRt</article-title><source>Nat Protoc</source><year>2009</year><volume>4</volume><fpage>1184</fpage><lpage>1191</lpage><pub-id pub-id-type="doi">10.1038/nprot.2009.97</pub-id><pub-id pub-id-type="pmid">19617889</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Baldin</surname><given-names>C</given-names></name><name><surname>Valiante</surname><given-names>V</given-names></name><name><surname>Kr&#x000fc;ger</surname><given-names>T</given-names></name><name><surname>Schafferer</surname><given-names>L</given-names></name><name><surname>Haas</surname><given-names>H</given-names></name><name><surname>Kniemeyer</surname><given-names>O</given-names></name><etal/></person-group><article-title>Comparative proteomics of a tor inducible <italic>Aspergillus fumigatus</italic> mutant reveals involvement of the Tor kinase in iron regulation</article-title><source>Proteomics</source><year>2015</year><volume>15</volume><fpage>2230</fpage><lpage>2243</lpage><pub-id pub-id-type="doi">10.1002/pmic.201400584</pub-id><pub-id pub-id-type="pmid">25728394</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><mixed-citation publication-type="other"><italic>Aspergillus fumigatus</italic> Af293 Sequence. <ext-link ext-link-type="uri" xlink:href="http://www.aspergillusgenome.org/download/sequence/A_fumigatus_Af293/current/A_fumigatus_Af293_current_orf_trans_all.fasta.gz">www.aspergillusgenome.org/download/sequence/A_fumigatus_Af293/current/A_fumigatus_Af293_current_orf_trans_all.fasta.gz</ext-link>. Accessed 27 Sept 2015.</mixed-citation></ref><ref id="CR32"><label>32.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>J&#x000f6;hnk</surname><given-names>B</given-names></name><name><surname>Bayram</surname><given-names>&#x000d6;</given-names></name><name><surname>Abelmann</surname><given-names>A</given-names></name><name><surname>Heinekamp</surname><given-names>T</given-names></name><name><surname>Mattern</surname><given-names>DJ</given-names></name><name><surname>Brakhage</surname><given-names>AA</given-names></name><etal/></person-group><article-title>SCF ubiquitin ligase F-box protein Fbx15 controls nuclear co-repressor localization, stress response and virulence of the human pathogen <italic>Aspergillus fumigatus</italic></article-title><source>PLoS Pathog</source><year>2016</year><volume>12</volume><fpage>e1005899</fpage><lpage>9</lpage><pub-id pub-id-type="doi">10.1371/journal.ppat.1005899</pub-id><pub-id pub-id-type="pmid">27649508</pub-id></element-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Szklarczyk</surname><given-names>D</given-names></name><name><surname>Franceschini</surname><given-names>A</given-names></name><name><surname>Wyder</surname><given-names>S</given-names></name><name><surname>Forslund</surname><given-names>K</given-names></name><name><surname>Heller</surname><given-names>D</given-names></name><name><surname>Huerta-Cepas</surname><given-names>J</given-names></name><etal/></person-group><article-title>STRING v10: protein-protein interaction networks, integrated over the tree of life</article-title><source>Nucleic Acids Res</source><year>2015</year><volume>43</volume><fpage>D447</fpage><lpage>D452</lpage><pub-id pub-id-type="doi">10.1093/nar/gku1003</pub-id><pub-id pub-id-type="pmid">25352553</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shannon</surname><given-names>P</given-names></name><name><surname>Markiel</surname><given-names>A</given-names></name><name><surname>Ozier</surname><given-names>O</given-names></name><name><surname>Baliga</surname><given-names>NS</given-names></name><name><surname>Wang</surname><given-names>JT</given-names></name><name><surname>Ramage</surname><given-names>D</given-names></name><etal/></person-group><article-title>Cytoscape: a software environment for integrated models of biomolecular interaction networks</article-title><source>Genome Res</source><year>2003</year><volume>13</volume><fpage>2498</fpage><lpage>2504</lpage><pub-id pub-id-type="doi">10.1101/gr.1239303</pub-id><pub-id pub-id-type="pmid">14597658</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yip</surname><given-names>AM</given-names></name><name><surname>Horvath</surname><given-names>S</given-names></name></person-group><article-title>Gene network interconnectedness and the generalized topological overlap measure</article-title><source>BMC Bioinformatics</source><year>2007</year><volume>8</volume><fpage>22</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-8-22</pub-id><pub-id pub-id-type="pmid">17250769</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><mixed-citation publication-type="other">Neuwirth E. RColorBrewer: ColorBrewer palettes. R Package version 11&#x02013;2. 2014. <ext-link ext-link-type="uri" xlink:href="https://CRAN.R-project.org/package=RColorBrewer">https://CRAN.R-project.org/package=RColorBrewer</ext-link>.</mixed-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Langfelder</surname><given-names>P</given-names></name><name><surname>Horvath</surname><given-names>S</given-names></name></person-group><article-title>WGCNA: an R package for weighted correlation network analysis</article-title><source>BMC Bioinformatics.</source><year>2008</year><volume>9</volume><fpage>559</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-9-559</pub-id><pub-id pub-id-type="pmid">19114008</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Pag&#x000e8;s</surname><given-names>H</given-names></name><name><surname>Carlson</surname><given-names>M</given-names></name><name><surname>Falcon</surname><given-names>S</given-names></name><name><surname>Li</surname><given-names>N</given-names></name></person-group><source>AnnotationDbi: Annotation Database Interface. R Package version 1382</source><year>2017</year></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Morgan</surname><given-names>M</given-names></name><name><surname>Falcon</surname><given-names>S</given-names></name><name><surname>Gentleman</surname><given-names>R</given-names></name></person-group><source>GSEABase: Gene set enrichment data structures and methods. R Package version 1382</source><year>2017</year></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Carlson</surname><given-names>M</given-names></name></person-group><source>GO.db: A set of annotation maps describing the entire Gene Ontology. R Package version 341</source><year>2017</year></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Falcon</surname><given-names>S</given-names></name><name><surname>Gentleman</surname><given-names>R</given-names></name></person-group><article-title>Using GOstats to test gene lists for GO term association</article-title><source>Bioinformatics</source><year>2007</year><volume>23</volume><fpage>257</fpage><lpage>258</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btl567</pub-id><pub-id pub-id-type="pmid">17098774</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="book"><person-group person-group-type="author"><name><surname>Gentleman</surname><given-names>R</given-names></name></person-group><source>Category: Category Analysis. R Package version 2421</source><year>2017</year></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Noventa-Jord&#x000e3;o</surname><given-names>MA</given-names></name><name><surname>do Nascimento</surname><given-names>AM</given-names></name><name><surname>Goldman</surname><given-names>MH</given-names></name><name><surname>Terenzi</surname><given-names>HF</given-names></name><name><surname>Goldman</surname><given-names>GH</given-names></name></person-group><article-title>Molecular characterization of ubiquitin genes from <italic>Aspergillus nidulans:</italic> mRNA expression on different stress and growth conditions</article-title><source>Biochim Biophys Acta</source><year>2000</year><volume>1490</volume><fpage>237</fpage><lpage>244</lpage><pub-id pub-id-type="doi">10.1016/S0167-4781(99)00242-0</pub-id><pub-id pub-id-type="pmid">10684969</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Finley</surname><given-names>D</given-names></name><name><surname>&#x000d6;zkaynak</surname><given-names>E</given-names></name><name><surname>Varshavsky</surname><given-names>A</given-names></name></person-group><article-title>The yeast polyubiquitin gene is essential for resistance to high temperatures, starvation, and other stresses</article-title><source>Cell</source><year>1987</year><volume>48</volume><fpage>1035</fpage><lpage>1046</lpage><pub-id pub-id-type="doi">10.1016/0092-8674(87)90711-2</pub-id><pub-id pub-id-type="pmid">3030556</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Barren&#x000e4;s</surname><given-names>F</given-names></name><name><surname>Chavali</surname><given-names>S</given-names></name><name><surname>Alves</surname><given-names>AC</given-names></name><name><surname>Coin</surname><given-names>L</given-names></name><name><surname>Jarvelin</surname><given-names>MR</given-names></name><name><surname>J&#x000f6;rnsten</surname><given-names>R</given-names></name><etal/></person-group><article-title>Highly interconnected genes in disease-specific networks are enriched for disease-associated polymorphisms</article-title><source>Genome Biol</source><year>2012</year><volume>13</volume><issue>6</issue><fpage>R46</fpage><pub-id pub-id-type="doi">10.1186/gb-2012-13-6-r46</pub-id><pub-id pub-id-type="pmid">22703998</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Gustafsson</surname><given-names>M</given-names></name><name><surname>Edstr&#x000f6;m</surname><given-names>M</given-names></name><name><surname>Gawel</surname><given-names>D</given-names></name><name><surname>Nestor</surname><given-names>CE</given-names></name><name><surname>Wang</surname><given-names>H</given-names></name><name><surname>Zhang</surname><given-names>H</given-names></name><etal/></person-group><article-title>Integrated genomic and prospective clinical studies show the importance of modular pleiotropy for disease susceptibility, diagnosis and treatment</article-title><source>Genome Med</source><year>2014</year><volume>6</volume><issue>2</issue><fpage>17</fpage><pub-id pub-id-type="doi">10.1186/gm534</pub-id><pub-id pub-id-type="pmid">24571673</pub-id></element-citation></ref><ref id="CR47"><label>47.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Cagas</surname><given-names>SE</given-names></name><name><surname>Jain</surname><given-names>MR</given-names></name><name><surname>Li</surname><given-names>H</given-names></name><name><surname>Perlin</surname><given-names>DS</given-names></name></person-group><article-title>Profiling the <italic>Aspergillus fumigatus</italic> proteome in response to caspofungin</article-title><source>Antimicrob Agents Chemother</source><year>2011</year><volume>55</volume><fpage>146</fpage><lpage>154</lpage><pub-id pub-id-type="doi">10.1128/AAC.00884-10</pub-id><pub-id pub-id-type="pmid">20974863</pub-id></element-citation></ref><ref id="CR48"><label>48.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Nie</surname><given-names>L</given-names></name><name><surname>Wu</surname><given-names>G</given-names></name><name><surname>Culley</surname><given-names>DE</given-names></name><name><surname>Scholten</surname><given-names>JCM</given-names></name><name><surname>Zhang</surname><given-names>W</given-names></name></person-group><article-title>Integrative analysis of transcriptomic and proteomic data: challenges, solutions and applications</article-title><source>Crit Rev Biotechnol</source><year>2007</year><volume>27</volume><fpage>63</fpage><lpage>75</lpage><pub-id pub-id-type="doi">10.1080/07388550701334212</pub-id><pub-id pub-id-type="pmid">17578703</pub-id></element-citation></ref><ref id="CR49"><label>49.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Albrecht</surname><given-names>D</given-names></name><name><surname>Guthke</surname><given-names>R</given-names></name><name><surname>Brakhage</surname><given-names>AA</given-names></name><name><surname>Kniemeyer</surname><given-names>O</given-names></name></person-group><article-title>Integrative analysis of the heat shock response in <italic>Aspergillus fumigatus</italic></article-title><source>BMC Genomics</source><year>2010</year><volume>11</volume><fpage>32</fpage><pub-id pub-id-type="doi">10.1186/1471-2164-11-32</pub-id><pub-id pub-id-type="pmid">20074381</pub-id></element-citation></ref><ref id="CR50"><label>50.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bendtsen</surname><given-names>JD</given-names></name><name><surname>Jensen</surname><given-names>LJ</given-names></name><name><surname>Blom</surname><given-names>N</given-names></name><name><surname>Von Heijne</surname><given-names>G</given-names></name><name><surname>Brunak</surname><given-names>S</given-names></name></person-group><article-title>Feature-based prediction of non-classical and leaderless protein secretion</article-title><source>Protein Eng Des Sel</source><year>2004</year><volume>17</volume><fpage>349</fpage><lpage>356</lpage><pub-id pub-id-type="doi">10.1093/protein/gzh037</pub-id><pub-id pub-id-type="pmid">15115854</pub-id></element-citation></ref><ref id="CR51"><label>51.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Meyer</surname><given-names>V</given-names></name><name><surname>Damveld</surname><given-names>RA</given-names></name><name><surname>Arentshorst</surname><given-names>M</given-names></name><name><surname>Stahl</surname><given-names>U</given-names></name><name><surname>Van Den Hondel</surname><given-names>CAMJJ</given-names></name><name><surname>Ram</surname><given-names>AFJ</given-names></name></person-group><article-title>Survival in the presence of antifungals: genome-wide expression profiling of <italic>aspergillus niger</italic> in response to sublethal concentrations of caspofungin and fenpropimorph</article-title><source>J Biol Chem</source><year>2007</year><volume>282</volume><fpage>32935</fpage><lpage>32948</lpage><pub-id pub-id-type="doi">10.1074/jbc.M705856200</pub-id><pub-id pub-id-type="pmid">17804411</pub-id></element-citation></ref><ref id="CR52"><label>52.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Shingu-Vazquez</surname><given-names>M</given-names></name><name><surname>Traven</surname><given-names>A</given-names></name></person-group><article-title>Mitochondria and fungal pathogenesis: drug tolerance, virulence, and potential for antifungal therapy</article-title><source>Eukaryot Cell</source><year>2011</year><volume>10</volume><fpage>1376</fpage><lpage>1383</lpage><pub-id pub-id-type="doi">10.1128/EC.05184-11</pub-id><pub-id pub-id-type="pmid">21926328</pub-id></element-citation></ref><ref id="CR53"><label>53.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bowman</surname><given-names>JC</given-names></name><name><surname>Hicks</surname><given-names>PS</given-names></name><name><surname>Kurtz</surname><given-names>MB</given-names></name><name><surname>Rosen</surname><given-names>H</given-names></name><name><surname>Schmatz</surname><given-names>DM</given-names></name><name><surname>Liberator</surname><given-names>PA</given-names></name><etal/></person-group><article-title>The antifungal echinocandin caspofungin acetate kills growing cells of <italic>Aspergillus fumigatus</italic> in vitro</article-title><source>Antimicrob Agents Chemother</source><year>2002</year><volume>46</volume><fpage>3001</fpage><lpage>3012</lpage><pub-id pub-id-type="doi">10.1128/AAC.46.9.3001-3012.2002</pub-id><pub-id pub-id-type="pmid">12183260</pub-id></element-citation></ref><ref id="CR54"><label>54.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>May</surname><given-names>GS</given-names></name><name><surname>Xue</surname><given-names>T</given-names></name><name><surname>Kontoyiannis</surname><given-names>DP</given-names></name><name><surname>Gustin</surname><given-names>MC</given-names></name></person-group><article-title>Mitogen activated protein kinases of <italic>Aspergillus fumigatus</italic></article-title><source>Med Mycol</source><year>2005</year><volume>43</volume><issue>Suppl 1</issue><fpage>S83</fpage><lpage>S86</lpage><pub-id pub-id-type="doi">10.1080/13693780400024784</pub-id><pub-id pub-id-type="pmid">16110797</pub-id></element-citation></ref><ref id="CR55"><label>55.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mayr</surname><given-names>A</given-names></name><name><surname>Aigner</surname><given-names>M</given-names></name><name><surname>Lass-Fl&#x000f6;rl</surname><given-names>C</given-names></name></person-group><article-title>Caspofungin: when and how? The microbiologist&#x02019;s view</article-title><source>Mycoses</source><year>2012</year><volume>55</volume><fpage>27</fpage><lpage>35</lpage><pub-id pub-id-type="doi">10.1111/j.1439-0507.2011.02039.x</pub-id><pub-id pub-id-type="pmid">21668518</pub-id></element-citation></ref><ref id="CR56"><label>56.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Eshwika</surname><given-names>A</given-names></name><name><surname>Kelly</surname><given-names>J</given-names></name><name><surname>Fallon</surname><given-names>JP</given-names></name><name><surname>Kavanagh</surname><given-names>K</given-names></name></person-group><article-title>Exposure of <italic>Aspergillus fumigatus</italic> to caspofungin results in the release, and de novo biosynthesis, of gliotoxin</article-title><source>Med Mycol</source><year>2013</year><volume>51</volume><fpage>121</fpage><lpage>127</lpage><pub-id pub-id-type="doi">10.3109/13693786.2012.688180</pub-id><pub-id pub-id-type="pmid">23323804</pub-id></element-citation></ref><ref id="CR57"><label>57.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Sin</surname><given-names>N</given-names></name><name><surname>Meng</surname><given-names>L</given-names></name><name><surname>Wang</surname><given-names>MQW</given-names></name><name><surname>Wen</surname><given-names>JJ</given-names></name><name><surname>Bornmann</surname><given-names>WG</given-names></name><name><surname>Crews</surname><given-names>CM</given-names></name></person-group><article-title>The anti-angiogenic agent fumagillin covalently binds and inhibits the methionine aminopeptidase, MetAP-2</article-title><source>Proc Natl Acad Sci</source><year>1997</year><volume>94</volume><fpage>6099</fpage><lpage>6103</lpage><pub-id pub-id-type="doi">10.1073/pnas.94.12.6099</pub-id><?supplied-pmid 9177176?><pub-id pub-id-type="pmid">9177176</pub-id></element-citation></ref><ref id="CR58"><label>58.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zbidah</surname><given-names>M</given-names></name><name><surname>Lupescu</surname><given-names>A</given-names></name><name><surname>Jilani</surname><given-names>K</given-names></name><name><surname>Lang</surname><given-names>F</given-names></name></person-group><article-title>Stimulation of suicidal erythrocyte death by fumagillin</article-title><source>Basic Clin Pharmacol Toxicol</source><year>2013</year><volume>112</volume><fpage>346</fpage><lpage>351</lpage><pub-id pub-id-type="doi">10.1111/bcpt.12033</pub-id><pub-id pub-id-type="pmid">23121865</pub-id></element-citation></ref><ref id="CR59"><label>59.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Netzker</surname><given-names>T</given-names></name><name><surname>Fischer</surname><given-names>J</given-names></name><name><surname>Weber</surname><given-names>J</given-names></name><name><surname>Mattern</surname><given-names>DJ</given-names></name><name><surname>K&#x000f6;nig</surname><given-names>CC</given-names></name><name><surname>Valiante</surname><given-names>V</given-names></name><etal/></person-group><article-title>Microbial communication leading to the activation of silent fungal secondary metabolite gene clusters</article-title><source>Front Microbiol</source><year>2015</year><volume>6</volume><fpage>299</fpage><pub-id pub-id-type="doi">10.3389/fmicb.2015.00299</pub-id><pub-id pub-id-type="pmid">25941517</pub-id></element-citation></ref><ref id="CR60"><label>60.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Alfano</surname><given-names>C</given-names></name><name><surname>Faggiano</surname><given-names>S</given-names></name><name><surname>Pastore</surname><given-names>A</given-names></name></person-group><article-title>The ball and chain of Polyubiquitin structures</article-title><source>Trends Biochem Sci</source><year>2016</year><volume>41</volume><fpage>371</fpage><lpage>385</lpage><pub-id pub-id-type="doi">10.1016/j.tibs.2016.01.006</pub-id><pub-id pub-id-type="pmid">26899455</pub-id></element-citation></ref><ref id="CR61"><label>61.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Leach</surname><given-names>MD</given-names></name><name><surname>Stead</surname><given-names>DA</given-names></name><name><surname>Argo</surname><given-names>E</given-names></name><name><surname>Maccallum</surname><given-names>DM</given-names></name><name><surname>Brown</surname><given-names>AJP</given-names></name></person-group><article-title>Molecular and proteomic analyses highlight the importance of ubiquitination for the stress resistance, metabolic adaptation, morphogenetic regulation and virulence of <italic>Candida albicans</italic></article-title><source>Mol Microbiol</source><year>2011</year><volume>79</volume><fpage>1574</fpage><lpage>1593</lpage><pub-id pub-id-type="doi">10.1111/j.1365-2958.2011.07542.x</pub-id><pub-id pub-id-type="pmid">21269335</pub-id></element-citation></ref><ref id="CR62"><label>62.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lesage</surname><given-names>G</given-names></name><name><surname>Sdicu</surname><given-names>AM</given-names></name><name><surname>M&#x000e9;nard</surname><given-names>P</given-names></name><name><surname>Shapiro</surname><given-names>J</given-names></name><name><surname>Hussein</surname><given-names>S</given-names></name><name><surname>Bussey</surname><given-names>H</given-names></name></person-group><article-title>Analysis of &#x003b2;-1,3-glucan assembly in <italic>Saccharomyces cerevisiae</italic> using a synthetic interaction network and altered sensitivity to caspofungin</article-title><source>Genetics</source><year>2004</year><volume>167</volume><fpage>35</fpage><lpage>49</lpage><pub-id pub-id-type="doi">10.1534/genetics.167.1.35</pub-id><pub-id pub-id-type="pmid">15166135</pub-id></element-citation></ref><ref id="CR63"><label>63.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Skrzypek</surname><given-names>MS</given-names></name><name><surname>Binkley</surname><given-names>J</given-names></name><name><surname>Binkley</surname><given-names>G</given-names></name><name><surname>Miyasato</surname><given-names>SR</given-names></name><name><surname>Simison</surname><given-names>M</given-names></name><name><surname>Sherlock</surname><given-names>G</given-names></name></person-group><article-title>The <italic>Candida</italic> genome database (CGD): incorporation of assembly 22, systematic identifiers and visualization of high throughput sequencing data</article-title><source>Nucleic Acids Res</source><year>2017</year><volume>45</volume><fpage>D592</fpage><lpage>D596</lpage><pub-id pub-id-type="doi">10.1093/nar/gkw924</pub-id><pub-id pub-id-type="pmid">27738138</pub-id></element-citation></ref><ref id="CR64"><label>64.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Khamooshi</surname><given-names>K</given-names></name><name><surname>Sikorski</surname><given-names>P</given-names></name><name><surname>Sun</surname><given-names>N</given-names></name><name><surname>Calderone</surname><given-names>R</given-names></name><name><surname>Li</surname><given-names>D</given-names></name></person-group><article-title>The Rbf1, Hfl1 and Dbp4 of <italic>Candida albicans</italic> regulate common as well as transcription factor-specific mitochondrial and other cell activities</article-title><source>BMC Genomics</source><year>2014</year><volume>15</volume><fpage>56</fpage><pub-id pub-id-type="doi">10.1186/1471-2164-15-56</pub-id><pub-id pub-id-type="pmid">24450762</pub-id></element-citation></ref><ref id="CR65"><label>65.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vizca&#x000ed;no</surname><given-names>JA</given-names></name><name><surname>Csordas</surname><given-names>A</given-names></name><name><surname>Del-Toro</surname><given-names>N</given-names></name><name><surname>Dianes</surname><given-names>JA</given-names></name><name><surname>Griss</surname><given-names>J</given-names></name><name><surname>Lavidas</surname><given-names>I</given-names></name><etal/></person-group><article-title>2016 update of the PRIDE database and its related tools</article-title><source>Nucleic Acids Res</source><year>2016</year><volume>44</volume><fpage>D447</fpage><lpage>D456</lpage><pub-id pub-id-type="doi">10.1093/nar/gkv1145</pub-id><pub-id pub-id-type="pmid">26527722</pub-id></element-citation></ref></ref-list></back></article>