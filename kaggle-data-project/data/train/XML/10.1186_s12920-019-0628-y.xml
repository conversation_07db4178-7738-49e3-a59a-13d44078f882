<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.2 20190208//EN" "JATS-archivearticle1-mathml3.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Med Genomics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Med Genomics</journal-id><journal-title-group><journal-title>BMC Medical Genomics</journal-title></journal-title-group><issn pub-type="epub">1755-8794</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6923884</article-id><article-id pub-id-type="publisher-id">628</article-id><article-id pub-id-type="doi">10.1186/s12920-019-0628-y</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>A deep neural network approach to predicting clinical outcomes of neuroblastoma patients</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Tranchevent</surname><given-names>L&#x000e9;on-Charles</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author"><name><surname>Azuaje</surname><given-names>Francisco</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><contrib contrib-type="author" corresp="yes"><contrib-id contrib-id-type="orcid">http://orcid.org/0000-0001-7944-1658</contrib-id><name><surname>Rajapakse</surname><given-names>Jagath C.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff4">4</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 0621 531X</institution-id><institution-id institution-id-type="GRID">grid.451012.3</institution-id><institution>Proteome and Genome Research Unit, Department of Oncology, Luxembourg Institute of Health, </institution></institution-wrap>1A-B, rue Thomas Edison, Strassen, L-1445 Luxembourg </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2295 9843</institution-id><institution-id institution-id-type="GRID">grid.16008.3f</institution-id><institution>Current affiliation: Luxembourg Centre for Systems Biomedicine (LCSB), University of Luxembourg, </institution></institution-wrap>7, avenue des Hauts Fourneaux, Esch-sur-Alzette, L-4362 Luxembourg </aff><aff id="Aff3"><label>3</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 5903 3819</institution-id><institution-id institution-id-type="GRID">grid.418727.f</institution-id><institution>Current affiliation: Data and Translational Sciences, UCB Celltech, </institution></institution-wrap>208 Bath Road, Slough, SL1 3WE UK </aff><aff id="Aff4"><label>4</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0001 2224 0361</institution-id><institution-id institution-id-type="GRID">grid.59025.3b</institution-id><institution>Bioinformatics Research Center, School of Computer Science and Engineering, Nanyang Technological University, </institution></institution-wrap>50, Nanyang Avenue, Singapore, 639798 Singapore </aff></contrib-group><pub-date pub-type="epub"><day>20</day><month>12</month><year>2019</year></pub-date><pub-date pub-type="pmc-release"><day>20</day><month>12</month><year>2019</year></pub-date><pub-date pub-type="collection"><year>2019</year></pub-date><volume>12</volume><issue>Suppl 8</issue><elocation-id>178</elocation-id><history><date date-type="received"><day>8</day><month>11</month><year>2019</year></date><date date-type="accepted"><day>15</day><month>11</month><year>2019</year></date></history><permissions><copyright-statement>&#x000a9; The Author(s) 2019</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold> This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">The availability of high-throughput omics datasets from large patient cohorts has allowed the development of methods that aim at predicting patient clinical outcomes, such as survival and disease recurrence. Such methods are also important to better understand the biological mechanisms underlying disease etiology and development, as well as treatment responses. Recently, different predictive models, relying on distinct algorithms (including Support Vector Machines and Random Forests) have been investigated. In this context, deep learning strategies are of special interest due to their demonstrated superior performance over a wide range of problems and datasets. One of the main challenges of such strategies is the &#x0201c;small n large p&#x0201d; problem. Indeed, omics datasets typically consist of small numbers of samples and large numbers of features relative to typical deep learning datasets. Neural networks usually tackle this problem through feature selection or by including additional constraints during the learning process.</p></sec><sec><title>Methods</title><p id="Par2">We propose to tackle this problem with a novel strategy that relies on a graph-based method for feature extraction, coupled with a deep neural network for clinical outcome prediction. The omics data are first represented as graphs whose nodes represent patients, and edges represent correlations between the patients&#x02019; omics profiles. Topological features, such as centralities, are then extracted from these graphs for every node. Lastly, these features are used as input to train and test various classifiers.</p></sec><sec><title>Results</title><p id="Par3">We apply this strategy to four neuroblastoma datasets and observe that models based on neural networks are more accurate than state of the art models (DNN: 85%-87%, SVM/RF: 75%-82%). We explore how different parameters and configurations are selected in order to overcome the effects of the small data problem as well as the curse of dimensionality.</p></sec><sec><title>Conclusions</title><p id="Par4">Our results indicate that the deep neural networks capture complex features in the data that help predicting patient clinical outcomes.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Machine learning</kwd><kwd>Deep learning</kwd><kwd>Deep neural network</kwd><kwd>Network-based methods</kwd><kwd>Graph topology</kwd><kwd>Disease prediction</kwd><kwd>Clinical outcome prediction</kwd></kwd-group><conference xlink:href="https://incob2019.org/"><conf-name>International Conference on Bioinformatics (InCoB 2019)</conf-name><conf-acronym>InCoB 2019</conf-acronym><conf-loc>Jakarta, Indonesia</conf-loc><conf-date>10-12 Septemebr 2019</conf-date></conference><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2019</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p>A lot of efforts have been made recently to create and validate predictive models for clinical research. In particular, the identification of relevant biomarkers for diagnosis and prognosis has been facilitated by the generation of large scale omics datasets for large patient cohorts. Candidate biomarkers are now identified by looking at all bioentities, including non-coding transcripts such as miRNA [<xref ref-type="bibr" rid="CR1">1</xref>, <xref ref-type="bibr" rid="CR2">2</xref>], in different tissues, including blood [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR4">4</xref>] and by investigating different possible levels of regulation, for instance epigenetics [<xref ref-type="bibr" rid="CR5">5</xref>&#x02013;<xref ref-type="bibr" rid="CR7">7</xref>].</p><p>One challenging objective is to identify prognostic biomarkers, i.e., biomarkers that can be used to predict the clinical outcome of patients such as whether the disease will progress or whether the patient will respond to a treatment. One strategy to identify such biomarkers is to build classifiers that can effectively classify patients into clinically relevant categories. For instance, various machine learning models predicting the progression of the disease and even the death of patients were proposed for neuroblastoma [<xref ref-type="bibr" rid="CR8">8</xref>]. Similar models have also been built for other diseases such as ovarian cancer to predict the patients&#x02019; response to chemotherapy using different variants of classical learning algorithms such as Support Vector Machines (SVM) and Random Forest (RF) [<xref ref-type="bibr" rid="CR9">9</xref>]. More recently, gynecologic and breast cancers were classified into five clinically relevant subtypes based on the patients extensive omics profiles extracted from The Cancer Genome Atlas (TCGA)[<xref ref-type="bibr" rid="CR10">10</xref>]. A simple decision tree was then proposed to classify samples and thus predict the clinical outcome of the associated patients. Although the general performance of these models is encouraging, they still need to be improved before being effectively useful in practice.</p><p>This study aims at improving these approaches by investigating a graph-based feature extraction method, coupled with a deep neural network, for patient clinical outcome prediction. One challenge when applying a machine learning strategy to omics data resides in the properties of the input data. Canonical datasets usually contain many instances but relatively few attributes. In contrast, biomedical datasets such as patient omics datasets usually have a relatively low number of instances (i.e., few samples) and a relatively high number of attributes (i.e., curse of dimensionality). For instance, the large data repository TCGA contains data for more than 11,000 cancer patients, and although the numbers vary from one cancer to another, for each patient, a least a few dozens of thousands of attributes are available [<xref ref-type="bibr" rid="CR11">11</xref>]. The situation is even worse when focusing on a single disease or phenotype, for which less than 1000 patients might have been screened [<xref ref-type="bibr" rid="CR12">12</xref>&#x02013;<xref ref-type="bibr" rid="CR14">14</xref>].</p><p>Previous approaches to handle omics data (with few samples and many features) rely on either feature selection via dimension reduction [<xref ref-type="bibr" rid="CR15">15</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref>] or on imposing constraints on the learning algorithm [<xref ref-type="bibr" rid="CR18">18</xref>, <xref ref-type="bibr" rid="CR19">19</xref>]. For instance, several studies have coupled neural networks to Cox models for survival analysis [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR21">21</xref>]. These methods either perform feature selection before inputing the data to deep neural network [<xref ref-type="bibr" rid="CR20">20</xref>, <xref ref-type="bibr" rid="CR21">21</xref>] or let the Cox model perform the selection afterwards [<xref ref-type="bibr" rid="CR22">22</xref>]. More recently, the GEDFN method was introduced, which relies on a deep neural network to perform disease outcome classification [<xref ref-type="bibr" rid="CR18">18</xref>]. GEDFN handles the curse of dimensionality by imposing a constraint on the first hidden layer. More precisely, a feature graph (in this case, a protein-protein interaction network) is used to enforce sparsity of the connections between the input layer and the first hidden layer.</p><p>We propose a strategy to create machine-learning models starting from patient omics datasets by first reducing the number of features through a graph topological analysis. Predictive models can then be trained and tested, and their parameters can be fine-tuned. Due to their high performance on many complex problems involving high-dimensional datasets, we build our approach around Deep Neural Networks (DNN). Our hypothesis is that the complex features explored by these networks can improve the prediction of patient clinical outcomes. We apply this strategy to four neuroblastoma datasets, in which the gene expression levels of hundreds of patients have been measured using different technologies (i.e., microarray and RNA-sequencing). In this context, we investigate the suitability of our approach by comparing it to state of the art methods such as SVM and RF.</p></sec><sec id="Sec2"><title>Methods</title><sec id="Sec3"><title>Data collection</title><p>The neuroblastoma transcriptomics datasets are summarized in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>. Briefly, the data were downloaded from GEO [<xref ref-type="bibr" rid="CR26">26</xref>] using the identifiers GSE49710 (tag &#x02018;<italic>Fischer-M</italic>&#x02019;), GSE62564 (tag &#x02018;<italic>Fischer-R</italic>&#x02019;) and GSE3960 (tag &#x02018;<italic>Maris</italic>&#x02019;). The pre-processed transcriptomics data are extracted from the GEO matrix files for 498 patients (&#x02018;<italic>Fischer-M</italic>&#x02019; and &#x02018;<italic>Fischer-R</italic>&#x02019;) and 102 patients (&#x02018;<italic>Maris</italic>&#x02019;). In addition, clinical descriptors are also extracted from the headers of the GEO matrix files (&#x02018;<italic>Fischer-M</italic>&#x02019; and &#x02018;<italic>Fischer-R</italic>&#x02019;) or from the associated publications (&#x02018;<italic>Maris</italic>&#x02019;). For &#x02018;<italic>Maris</italic>&#x02019;, survival data for ten patients are missing, leaving 92 patients for analysis. A fourth dataset (tag &#x02018;<italic>Versteeg</italic>&#x02019;) is described in GEO record GSE16476. However the associated clinical descriptors are only available through the R2 tool [<xref ref-type="bibr" rid="CR27">27</xref>]. For consistency, we have also extracted the expression profiles for the 88 patients using the R2 tool. In all four cases, the clinical outcomes include &#x02018;<italic>Death from disease</italic>&#x02019; and &#x02018;<italic>Disease progression</italic>&#x02019;, as binary features (absence or presence of event) which are used to define classes. Genes or transcripts with any missing value are dropped. The number of features remaining after pre-processing are 43,291, 43,827, 12,625 and 40,918 respectively for the &#x02018;<italic>Fischer-M</italic>&#x02019;, &#x02018;<italic>Fischer-R</italic>&#x02019;, &#x02018;<italic>Maris</italic>&#x02019; and &#x02018;<italic>Versteeg</italic>&#x02019; matrices.
<table-wrap id="Tab1"><label>Table 1</label><caption><p>Details about the four expression datasets used in the present study</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Name</th><th align="left">Reference</th><th align="left">Data type</th><th align="left">Size</th><th align="left">Usage</th></tr></thead><tbody><tr><td align="left">Fischer-M</td><td align="left">Zhang et al., 2014 [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR23">23</xref>]</td><td align="left">Microarray</td><td align="left">498 * 43,291</td><td align="left">Training, testing</td></tr><tr><td align="left">Fischer-R</td><td align="left">Zhang et al., 2014 [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR23">23</xref>]</td><td align="left">RNA-seq</td><td align="left">498 * 43,827</td><td align="left">Training, testing</td></tr><tr><td align="left">Maris</td><td align="left">Wang et al., 2006 [<xref ref-type="bibr" rid="CR24">24</xref>]</td><td align="left">Microarray</td><td align="left">92 * 12,625</td><td align="left">Testing</td></tr><tr><td align="left">Versteeg</td><td align="left">Molenar et al., 2012 [<xref ref-type="bibr" rid="CR25">25</xref>]</td><td align="left">Microarray</td><td align="left">88 * 40,918</td><td align="left">Testing</td></tr></tbody></table></table-wrap></p></sec><sec id="Sec4"><title>Data processing through topological analysis</title><p>Each dataset is then reduced through a Wilcoxon analysis that identifies the features (i.e., genes or transcripts) that are most correlated with each clinical outcome using only the training data (Wilcoxon <italic>P</italic>&#x0003c;0.05). When this analysis did not return any feature, the top 5% features were used regardless of their <italic>p</italic>-values (for <italic>Maris</italic>&#x02019; and &#x02018;<italic>Versteeg</italic>&#x02019;). After dimension reduction, there are between 638 and 2196 features left depending on the dataset and the clinical outcome.</p><p>These reduced datasets are then used to infer Patient Similarity Networks (PSN), graphs in which a node represents a patient and an edge between two nodes represents the similarity between the two profiles of the corresponding patients. These graphs are built first, by computing the Pearson correlation coefficients between all profiles pairwise and second, by normalizing and rescaling these coefficients into positive edge weights through a WGCNA analysis [<xref ref-type="bibr" rid="CR28">28</xref>], as described previously [<xref ref-type="bibr" rid="CR29">29</xref>]. These graphs contain one node per patient, are fully connected and their weighted degree distributions follow a power law (i.e., scale-free graphs). Only one graph is derived per dataset, and each of the four datasets is analyzed independently. This means that for &#x02018;<italic>Fischer</italic>&#x02019; datasets, the graph contains both training and testing samples.</p><p>Various topological features are then extracted from the graphs, and will be used to build classifiers. In particular, we compute twelve centrality metrics as described previously (weighted degree, closeness centrality, current-flow closeness centrality, current-flow betweenness centrality, eigen vector centrality, Katz centrality, hit centrality, page-rank centrality, load centrality, local clustering coefficient, iterative weighted degree and iterative local clustering coefficient) for all four datasets. In addition, we perform clustering of each graph using spectral clustering [<xref ref-type="bibr" rid="CR30">30</xref>] and Stochastic Block Models (SBM) [<xref ref-type="bibr" rid="CR31">31</xref>]. The optimal number of modules is determined automatically using dedicated methods from the spectral clustering and SBM packages. For the two &#x02018;<italic>Fischer</italic>&#x02019; datasets and the two clinical outcomes, the optimal number of modules varies between 5 and 10 for spectral clustering and 25 and 42 for SBM. This analysis was not performed for the other datasets. All repartitions are used to create modularity features. Each modularity feature represents one single module and is binary (its value is set to one for members of the module and zero otherwise). All features are normalized before being feed to the classifiers (to have a zero mean and unit variance). Two datasets can be concatenated prior to the model training, all configurations used in this study are summarized in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>.
<table-wrap id="Tab2"><label>Table 2</label><caption><p>List of the possible data configurations (topological feature sets, datasets) used to train classification models</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Datasets</th><th align="left">Topological features</th><th align="left">Total size</th></tr></thead><tbody><tr><td align="left"><italic>Fischer-M</italic></td><td align="left">Centralities</td><td align="left">12</td></tr><tr><td align="left"/><td align="left">Modularities</td><td align="left">{30, 39}<sup>a</sup></td></tr><tr><td align="left"/><td align="left">Both</td><td align="left">{42, 51} <sup><italic>a</italic></sup></td></tr><tr><td align="left"><italic>Fischer-R</italic></td><td align="left">Centralities</td><td align="left">12</td></tr><tr><td align="left"/><td align="left">Modularities</td><td align="left">{36, 47}<sup>a</sup></td></tr><tr><td align="left"/><td align="left">Both</td><td align="left">{48, 59} <sup><italic>a</italic></sup></td></tr><tr><td align="left"><italic>Fischer</italic><sup>b</sup></td><td align="left">Centralities</td><td align="left">24</td></tr><tr><td align="left"/><td align="left">Modularities</td><td align="left">{75, 77}<sup>a</sup></td></tr><tr><td align="left"/><td align="left">Both</td><td align="left">{99, 101}<sup>a</sup></td></tr></tbody></table><table-wrap-foot><p><sup>a</sup>The number of modules for each graph, corresponding to one clinical outcomes of interest, is different</p><p><sup>b</sup>This is the combined dataset in which the topological features of both &#x02018;<italic>Fischer-M</italic>&#x02019; and &#x02018;<italic>Fischer-R</italic>&#x02019; are concatenated</p></table-wrap-foot></table-wrap></p></sec><sec id="Sec5"><title>Modeling through deep neural networks</title><p>Classes are defined by the binary clinical outcomes &#x02018;<italic>Death from disease</italic>&#x02019; and &#x02018;<italic>Disease progression</italic>&#x02019;. For the &#x02018;<italic>Fischer</italic>&#x02019; datasets, the original patient stratification [<xref ref-type="bibr" rid="CR8">8</xref>] is extended to create three groups of samples through stratified sampling: a training set (249 samples, 50%), an evaluation set (125 samples, 25%) and a validation set (124 samples, 25%). The proportions of samples associated to each clinical outcome of interest remain stable among the three groups (Additional file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>).</p><p>Deep Neural Networks (DNN) are feed forward neural networks with hidden layers, which can be trained to solve classification and regression problems. The parameters of these networks are represented by the weights connecting neurons and learned using gradient decent techniques. The DNN models are based on a classical architecture with a varying number of fully connected hidden layers of varying sizes. The activation function of all neurons is the rectified linear unit (ReLU). The softmax function is used as the activation function of the output layer. The training is performed by minimizing the cross-entropy loss function. A mini-batches size of 32 samples is used for training (total size of the training set is 249) and models are ran for 1000 epochs with an evaluation taking place every 10 epochs. Sample weights are introduced to circumvent the unbalance between the classes (the weights are inversely proportional to the class frequencies). To facilitate replications, random seeds are generated and provided to each DNN model. For our application, DNN classifiers with various architectures are trained. First, the number of hidden layers varies between one and four, and the number of neurons per hidden layer also varies from 2 to 8 (&#x02208;{2,4,8}). Second, additional parameters such as dropout, optimizer and learning rate are also optimized using a grid search. In particular, dropout is set between 15% and 40% (step set to 5%), learning rate between 1<italic>e</italic>-4 and 5<italic>e</italic>-2 and the optimizer is one among adam, adadelta, adagrad and proximal adagrad. Each DNN model is run ten times with different initialization weights and biases.</p></sec><sec id="Sec6"><title>Other modeling approaches</title><p>For comparison purposes, SVM and RF models are also trained on the same data. The cost (linear SVM), gamma (linear and RBF SVM) and number of trees (RF) parameters are optimized using a grid search. The cost and gamma parameters are set to 2<sup>2<italic>p</italic></sup>, with <inline-formula id="IEq1"><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$p \in \mathbb {Z}, p \in [-4,4]$\end{document}</tex-math><mml:math id="M2"><mml:mi>p</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mi>&#x02124;</mml:mi><mml:mo>,</mml:mo><mml:mi>p</mml:mi><mml:mo>&#x02208;</mml:mo><mml:mo>[</mml:mo><mml:mo>&#x02212;</mml:mo><mml:mn>4</mml:mn><mml:mo>,</mml:mo><mml:mn>4</mml:mn><mml:mo>]</mml:mo></mml:math><inline-graphic xlink:href="12920_2019_628_Article_IEq1.gif"/></alternatives></inline-formula>. The number of trees varies between 100 and 10,000. Since RF training is non deterministic, the algorithm is run ten times. The SVM optimization problem is however convex and SVM is therefore run only once.</p><p>GEDFN accepts omics data as input together with a feature graph. Similarly to the original paper, we use the HINT database v4 [<xref ref-type="bibr" rid="CR32">32</xref>] to retrieve the human protein-protein interaction network (PPIN) to be used as a feature graph [<xref ref-type="bibr" rid="CR18">18</xref>]. The mapping between identifiers is performed through BioMart at EnsEMBL v92 [<xref ref-type="bibr" rid="CR33">33</xref>]. First, the original microarray features (e.g., microarray probesets) are mapped to RefSeq or EnsEMBL transcripts identifiers. The RNA-seq features are already associated to RefSeq transcripts. In the end, transcript identifiers are mapped to UniProt/TrEMBL identifiers (which are the ones also used in the PPIN). The full datasets are too large for GEDFN so the reduced datasets (after dimension reduction) described above are used as inputs. For comparison purposes, only the &#x02018;<italic>Fischer-M</italic>&#x02019; data is used for &#x02018;<italic>Death from disease</italic>&#x02019; and both &#x02018;<italic>Fischer</italic>&#x02019; datasets are concatenated for &#x02018;<italic>Disease progression</italic>&#x02019;. GEDFN parameter space is explored using a small grid search that always include the default values suggested by the authors. The parameters we optimize are the number of neurons for the second and third layers (&#x02208;{(64,16),(16,4)}), the learning rate (&#x02208;{1<italic>e</italic>-4, 1<italic>e</italic>- 2}), the adam optimizer regularization (&#x02208;{<italic>T</italic><italic>r</italic><italic>u</italic><italic>e</italic>,<italic>F</italic><italic>a</italic><italic>l</italic><italic>s</italic><italic>e</italic>}), the number of epochs (&#x02208;{100,1000}) and the batch size (&#x02208;{8,32}). Each GEDFN model is run ten times with different initialization weights and biases. Optimal models for the two clinical outcomes are obtained by training for 1000 epochs and enforcing regularization.</p></sec><sec id="Sec7"><title>Model performance</title><p>The performance of each classification model is measured using balanced accuracy (bACC) since the dataset is not balanced (e.g., 4:1 for &#x02018;<italic>Death from disease</italic>&#x02019; and 2:1 for &#x02018;<italic>Disease progression</italic>&#x02019; in the &#x02018;<italic>Fischer</italic>&#x02019; datasets, Additional file&#x000a0;<xref rid="MOESM2" ref-type="media">2</xref>). In addition, one way ANOVA tests followed by post-hoc Tukey tests are employed for statistical comparisons. We consider p-values smaller than 0.01 as significant. When comparing two conditions, we also consider the difference in their average performance, and the confidence intervals for that difference (noted <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>). Within any category, the model associated with the best balanced accuracy is considered optimal (including across replicates).</p></sec><sec id="Sec8"><title>Implementation</title><p>The data processing was performed in python (using packages numpy and pandas). The graph inference and topological analyses were performed in python and C++ (using packages networkx, scipy, igraph, graph-tool and SNFtool). The SVM and RF classifiers were built in R (with packages randomForest and e1071). The DNN classifiers were built in python (with TensorFlow) using the <italic>DNNClassifier</italic> estimator. Training was performed using only CPU cores. GEDFN was run in Python using the implementation provided by the authors. Figures and statistical tests were prepared in R.</p></sec></sec><sec id="Sec9" sec-type="results"><title>Results</title><p>We propose a strategy to build patient classification models, starting from a limited set of patient samples associated with large feature vectors. Our approach relies on a graph-based method to perform dimension reduction by extracting features that are then used for classification (Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref> and Methods). Briefly, first the original data are transformed into patient graphs and topological features are extracted from these graphs. These topological features are then used to train deep neural networks. Their classification performance is then compared with those of other classifiers, including Support Vector Machines and Random Forests. We apply this strategy to a previously published cohort of neuroblastoma patients that consist of transcriptomics profiles for 498 patients (&#x02018;<italic>Fischer</italic>&#x02019;, Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>) [<xref ref-type="bibr" rid="CR8">8</xref>]. Predictive models are built with a subset of these data and are then used to predict the clinical outcome of patients whose profiles have not been used for training. We then optimize the models and compare their performance by considering their balanced accuracy. The optimal models obtained on the &#x02018;<italic>Fischer</italic>&#x02019; datasets are then validated using independent cohorts (Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref>) [<xref ref-type="bibr" rid="CR24">24</xref>, <xref ref-type="bibr" rid="CR25">25</xref>].
<fig id="Fig1"><label>Fig. 1</label><caption><p>General workflow of the proposed method. Our strategy relies on a topological analysis to perform dimension reduction of both the training (light green) and test data (dark green). Data matrices are transformed into graphs, from which topological features are extracted. Even if the original features (light blues) are different, the topological features extracted from the graphs (dark blue) have the same meaning and are comparable. These features are then used to train and test several models that rely on different learning algorithms (DNN, SVM and RF). These models are compared based on the accuracy of their predictions on the test data</p></caption><graphic xlink:href="12920_2019_628_Fig1_HTML" id="MO1"/></fig></p><sec id="Sec10"><title>Assessment of the topological features</title><p>We first compare models that accept different topological features extracted from the &#x02018;<italic>Fischer</italic>&#x02019; datasets as input, regardless of the underlying neural network architecture. We have defined nine possible feature sets that can be used as input to the classifiers (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). First, and for each dataset, three feature sets are defined: graph centralities, graph modularities and both combined. Second, we also concatenate the feature sets across the two &#x02018;<italic>Fischer</italic>&#x02019; datasets to create three additional feature sets. These feature sets contain between 12 and 101 topological features.</p><p>The results of this comparison for the two clinical outcomes can be found in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>. For each feature set, the balanced accuracies over all models (different architectures and replicates) are displayed as a single boxplot. The full list of models and their balanced accuracies is provided in Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>. A first observation is that centrality features are associated with better average performances than modularity features (&#x02018;<italic>Death from disease</italic>&#x02019;, <italic>p</italic>&#x02264;1<italic>e</italic>-7; &#x02018;<italic>Disease progression</italic>&#x02019;, <italic>p</italic>&#x02264;1<italic>e</italic>-7). We note that the difference between these average accuracies is modest for &#x02018;<italic>Death from disease</italic>&#x02019; (<italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[2.4,3.9]) but more important for &#x02018;<italic>Disease progression</italic>&#x02019; (<italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[6.7,8.2]). Combining both types of topological features generally does not improve the average performance.
<fig id="Fig2"><label>Fig. 2</label><caption><p>Model performance for different inputs. DNN models relying on different feature sets are compared by reporting their performance on the validation data for &#x02018;<italic>Death from disease</italic>&#x02019; (<bold>a</bold>) and &#x02018;<italic>Disease progression</italic>&#x02019; (<bold>b</bold>). Feature sets are defined by the original data that were used (microarray data, RNA-seq data or the integration of both) and by the topological features considered (centrality, modularity or both). Each single point represents a model. For each feature set, several models are trained by varying the neural network architecture and by performing replicates</p></caption><graphic xlink:href="12920_2019_628_Fig2_HTML" id="MO2"/></fig></p><p>A second observation is that the features extracted from the RNA-seq data are associated with lower average performance than the equivalent features extracted from the microarray data (<italic>p</italic>&#x02264;1<italic>e</italic>-7). The differences indicate that once again the effect is not negligible (&#x02018;<italic>Death from disease</italic>&#x02019;, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[2.1,3.6]); &#x02018;<italic>Disease progression</italic>&#x02019;, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[4.4,6.0]). In addition, the integration of the data across the two expression datasets does not improve the average performance.</p></sec><sec id="Sec11"><title>Influence of the DNN architecture</title><p>Deep neural networks are feed forward neural networks with several hidden layers, with several nodes each. The network architecture (i.e., layers and nodes) as well as the strategy used to train the network can influence its performance. We have therefore defined 35 possible architectures in total by varying the number of hidden layers and the number of neurons per hidden layer (&#x0201c;Methods&#x0201d;).</p><p>We compare the performance of the models relying on these different architectures. The results can be found in Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref> and Supplementary Figure S1 (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>). The full list of models and their balanced accuracies is provided in Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>. We can observe a small inverse correlation between the complexity of the architecture and the average performance. Although significant, the average performance of simple models (one hidden layer) is, on average, only marginally better than the average performance of more complex models (at least two hidden layers) (<italic>p</italic>&#x02264;1<italic>e</italic>- 7,<italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[2,4]).
<table-wrap id="Tab3"><label>Table 3</label><caption><p>Best performing DNN architectures.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Configuration</th><th align="left">Architecture</th><th align="left">Balanced accuracy</th></tr></thead><tbody><tr><td align="left" colspan="3">Clinical outcome = &#x02018;<italic>Death from disease</italic>&#x02019;</td></tr><tr><td align="left">Fischer-M, centralities</td><td align="left">[8,8,8,2]</td><td align="left"><bold>87.3%</bold></td></tr><tr><td align="left">Fischer-M, modularities</td><td align="left">[8,4]</td><td align="left">83.9%</td></tr><tr><td align="left">Fischer-M, both</td><td align="left">[8,8,8]</td><td align="left">86.8%</td></tr><tr><td align="left">Fischer-R, centralities</td><td align="left">[8,8,8,4]</td><td align="left">85.8%</td></tr><tr><td align="left">Fischer-R, modularities</td><td align="left">[8,8,8,2]</td><td align="left">82.1%</td></tr><tr><td align="left">Fischer-R, both</td><td align="left">[2,2,2,2]</td><td align="left">85.2%</td></tr><tr><td align="left">Fischer<sup>a</sup>, centralities</td><td align="left">[8,2,2]</td><td align="left">86.1%</td></tr><tr><td align="left">Fischer<sup>a</sup>, modularities</td><td align="left">[8,2,2]</td><td align="left">84.7%</td></tr><tr><td align="left">Fischer<sup>a</sup>, both</td><td align="left">[8,8,4]</td><td align="left">84.7%</td></tr><tr><td align="left" colspan="3">Clinical outcome = &#x02018;<italic>Disease progression</italic>&#x02019;</td></tr><tr><td align="left">Fischer-M, centralities</td><td align="left">[8,8,8,2]</td><td align="left">84.3%</td></tr><tr><td align="left">Fischer-M, modularities</td><td align="left">[8,8,2]</td><td align="left">82.3%</td></tr><tr><td align="left">Fischer-M, both</td><td align="left">[4,4,2]</td><td align="left">83.7%</td></tr><tr><td align="left">Fischer-R, centralities</td><td align="left">[8,8,4]</td><td align="left">83.7%</td></tr><tr><td align="left">Fischer-R, modularities</td><td align="left">[8,2,2]</td><td align="left">79.1%</td></tr><tr><td align="left">Fischer-R, both</td><td align="left">[8,8,8,8]</td><td align="left">77.9%</td></tr><tr><td align="left">Fischer<sup>a</sup>, centralities</td><td align="left">[4,2,2,2]</td><td align="left"><bold>84.7%</bold></td></tr><tr><td align="left">Fischer<sup>a</sup>, modularities</td><td align="left">[8,8]</td><td align="left">79.6%</td></tr><tr><td align="left">Fischer<sup>a</sup>, both</td><td align="left">[4,2]</td><td align="left">81.5%</td></tr></tbody></table><table-wrap-foot><p>One row corresponds to the best model for a given clinical outcome and configuration (from Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). The best performance (i.e., balanced accuracy) is displayed in bold for each clinical outcome</p><p><sup>a</sup>Combined dataset in which the topological features of both &#x02018;<italic>Fischer-M</italic>&#x02019; and &#x02018;<italic>Fischer-R</italic>&#x02019; are concatenated</p></table-wrap-foot></table-wrap></p></sec><sec id="Sec12"><title>Best models</title><p>Although the differences in average performance are important, our objective is to identify the best models, regardless of the average performance of any category. In the current section, we therefore report the best models for each feature set and each clinical outcome (summarized in Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>). In agreement with the global observations, the best model for &#x02018;<italic>Death from disease</italic>&#x02019; is based on the centrality features extracted from the microarray data. The best model for &#x02018;<italic>Disease progression</italic>&#x02019; relies however on centralities derived from both the microarray and the RNA-seq data (Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>), even if the corresponding category is not associated with the best average performance. This is consistent with the observation that the variance in performance increases when the number of input features increases, which can produce higher maximum values (Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref>). We can also observe some level of agreement between the two outcomes of interest. Indeed, the best feature set for &#x02018;<italic>Death from disease</italic>&#x02019; is actually the second best for &#x02018;<italic>Disease progression</italic>&#x02019;. Similarly, the best feature set for &#x02018;<italic>Disease progression</italic>&#x02019; is the third best for &#x02018;<italic>Death from disease</italic>&#x02019;.</p><p>Regarding the network architecture, models relying on networks with four hidden layers represent the best models for both &#x02018;<italic>Disease progression</italic>&#x02019; and &#x02018;<italic>Death from disease</italic>&#x02019; (Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>). Their respective architectures are still different and the &#x02018;<italic>Disease progression</italic>&#x02019; network contains more neurons. However, the second best network for &#x02018;<italic>Disease progression</italic>&#x02019; and the best network for &#x02018;<italic>Death from disease</italic>&#x02019; share the same architecture (two layers with four neurons each followed by two layers with two neurons each) indicating that this architecture can still perform well in both cases.</p></sec><sec id="Sec13"><title>Fine tuning of the hyper-parameters</title><p>Based on the previous observations, we have selected the best models for each clinical outcome in order to fine tune their hyper-parameters. The optimization was performed using a simple grid search (&#x0201c;<xref rid="Sec2" ref-type="sec">Methods</xref>&#x0201d; section). The hyper-parameters we optimized are the learning rate, the optimization strategy and the dropout (included to circumvent over-fitting during training [<xref ref-type="bibr" rid="CR34">34</xref>]). When considering all models, we can observe that increasing the initial learning rate seems to slightly improve the average performance, although the best models are in fact obtained with a low initial learning rate (Additional file&#x000a0;<xref rid="MOESM1" ref-type="media">1</xref>, Supplementary Figure S2). The most important impact is observed for the optimization strategies, with the Adam optimizer [<xref ref-type="bibr" rid="CR35">35</xref>] representing the best strategy, adadelta the less suitable one, with the adagrad variants in between. We observe that the performance is almost invariant to dropout except when it reaches 0.4 where it seems to have a strong negative impact on performance.</p><p>When focusing on the best models only, we observe similarities between the two clinical outcomes of interest. Indeed, in both cases, the optimal dropout, optimizer, and learning rate are respectively 0.3, Adam and 1e-3. Notice that for &#x02018;<italic>Death from disease</italic>&#x02019;, another learning rate value gives exactly the same performance (5e-4). As mentioned above, learning rate has little influence on the average performance. However, for these two specific models, its influence is important and using a non-optimal value results in a drop up to 19% for &#x02018;<italic>Death from disease</italic>&#x02019; and 29% for &#x02018;<italic>Disease progression</italic>&#x02019;. More important, we observe no significant increase in performance after parameter optimization (Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>), which correlates with the fact that two of the three optimal values actually correspond to the default values that were used before.
<table-wrap id="Tab4"><label>Table 4</label><caption><p>Parameter optimization for all classifiers.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left">Algorithm</th><th align="left">Parameters</th><th align="left">Balanced accuracy</th></tr></thead><tbody><tr><td align="left" colspan="3">Clinical outcome = &#x02018;<italic>Death from disease</italic>&#x02019;,</td></tr><tr><td align="left" colspan="3">Data=<italic>Fischer-M</italic>, centralities</td></tr><tr><td align="left">DNN [8,8,8,2]</td><td align="left">o=Adam, lr=1e-3, d=0.3</td><td align="right"><bold>87.3%</bold> (+0.0)</td></tr><tr><td align="left">GEDFN<sup>a</sup></td><td align="left">lr=1e-2, h=[64,16], b=8</td><td align="right">79.5% (+8.6)</td></tr><tr><td align="left">SVM</td><td align="left">t=RBF, c=64, g=0.25</td><td align="right">75.4% (+5.9)</td></tr><tr><td align="left">RF</td><td align="left">n=100</td><td align="right">75.1% (+3.1)</td></tr><tr><td align="left" colspan="3">Clinical outcome = &#x02018;<italic>Disease progression</italic>&#x02019;,</td></tr><tr><td align="left" colspan="3">Data=<italic>Fischer</italic>, centralities</td></tr><tr><td align="left">DNN [4,2,2,2]</td><td align="left">o=Adam, lr=1e-3, d=0.3</td><td align="right"><bold>84.7%</bold> (+0.0)</td></tr><tr><td align="left">GEDFN<sup>a</sup></td><td align="left">lr=1e-4, h=[16,4], b=32</td><td align="right">81.2% (+0.4)</td></tr><tr><td align="left">SVM</td><td align="left">t=RBF, c=16, g=0.0625</td><td align="right">81.8% (+2.0)</td></tr><tr><td align="left">RF</td><td align="left">n=100</td><td align="right">78.1% (+3.2)</td></tr></tbody></table><table-wrap-foot><p>One row corresponds to the best model for a given clinical outcome and algorithm. The optimal parameter values are provided (o: optimizer, lr: learning rate, d: dropout, h: sizes of the second and third GEDFN hidden layers, b: batch size, t: SVM kernel type, c: cost, g: gamma, n: number of trees). The gain in balanced accuracy with respect to the models run with default parameters is indicated between parentheses (from Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref> for DNN)</p><p><sup>a</sup>for GEDFN, the corresponding omics data is used as input instead of the topological features</p></table-wrap-foot></table-wrap></p><p>Whether we consider the different feature sets or the different network architectures, we also observe that the performance varies across replicates, i.e., models built using the same configuration but different randomization seeds (which are used for sample shuffling and initialization of the weights and biases). This seems to indicate that better models might also be produced simply by running more replicates. We tested this hypothesis by running more replicates of the best configurations (i.e., increasing the number of replicates from 10 to 100). However, we report no improvement of these models with 90 additional replicates (Additional file&#x000a0;<xref rid="MOESM3" ref-type="media">3</xref>).</p></sec><sec id="Sec14"><title>Comparison to other modeling strategies</title><p>We then compare the DNN classifiers to other classifiers relying on different learning algorithms (SVM and RF). These algorithms have previously demonstrated their effectiveness to solve the same classification task on the &#x02018;<italic>Fischer</italic>&#x02019; dataset, albeit using a different patient stratification [<xref ref-type="bibr" rid="CR8">8</xref>, <xref ref-type="bibr" rid="CR29">29</xref>]. For a fair comparison, all classifiers are input the same features and are trained and tested using the same samples. Optimal performance is obtained via a grid search over the parameter space (&#x0201c;<xref rid="Sec2" ref-type="sec">Methods</xref>&#x0201d; section). The results are summarized in Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>. We observe that the DNN classifiers outperform both the SVM and RF classifiers for both outcomes (&#x02018;<italic>Death from disease</italic>&#x02019;, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[11.9,12.2]); &#x02018;<italic>Disease progression</italic>&#x02019;, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[2.9,6.6]).</p><p>We also compare our strategy to GEDFN, an approach based on a neural network which requires a feature graph to enforce sparsity of the connections between the input and the first hidden layers. Unlike the other models, GEDFN models only accept omics data as input (i.e., original features). They are also optimized using a simple grid search. The results are summarized in Table&#x000a0;<xref rid="Tab4" ref-type="table">4</xref>. We can observe that the GEDFN models perform better than the SVM and RF models for &#x02018;<italic>Death from disease</italic>&#x02019;. For &#x02018;<italic>Disease progression</italic>&#x02019;, the GEDFN and SVM models are on par, and both superior to RF models. For both clinical outcomes, the GEDFN models remain however less accurate than the DNN models that use topological features. (&#x02018;<italic>Death from disease</italic>&#x02019;, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>=7.8); &#x02018;<italic>Disease progression</italic>&#x02019;, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>=3.5)</p></sec><sec id="Sec15"><title>Validation with independent datasets</title><p>In a last set of experiments, we tested our models using independent datasets. First, we use the &#x02018;<italic>Fischer-M</italic>&#x02019; dataset to validate models built using the &#x02018;<italic>Fischer-R</italic>&#x02019; dataset and vice-versa. Then, we also make use of two fully independent datasets, &#x02018;<italic>Maris</italic>&#x02019; and &#x02018;<italic>Versteeg</italic>&#x02019; as validation datasets for all models trained with any of the &#x02018;<italic>Fischer</italic>&#x02019; datasets. We compare the performance on these independent datasets to the reference performance (obtained when the same dataset is used for both training and testing).</p><p>The results are summarized in Table&#x000a0;<xref rid="Tab5" ref-type="table">5</xref>. When one of the &#x02018;<italic>Fischer</italic>&#x02019; dataset is used for training and the other dataset for testing, we can, in general, observe a small decrease in performance with respect to the reference (DNN, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[3.7.,7.3]; SVM, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[&#x02212;9.4,21.9]; RF, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[&#x02212;1.7,8.3]). For SVM and RF models, there is sometimes an increased performance (negative <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>).
<table-wrap id="Tab5"><label>Table 5</label><caption><p>External validation results.</p></caption><table frame="hsides" rules="groups"><thead><tr><th align="left" colspan="2">Datasets</th><th align="left" colspan="3">Balanced accuracy</th></tr><tr><th align="left">Training</th><th align="left">Test</th><th align="left">DNN</th><th align="left">SVM</th><th align="left">RF</th></tr></thead><tbody><tr><td align="left" colspan="4">Clinical outcome = &#x02018;<italic>Death from disease</italic>&#x02019;,</td><td align="left"/></tr><tr><td align="left" colspan="4">Data = centralities</td><td align="left"/></tr><tr><td align="left">Fischer-M</td><td align="left"><italic>Fischer-M</italic></td><td align="left"><italic>87.3%</italic></td><td align="left"><italic>75.4%</italic></td><td align="left"><italic>75.1%</italic></td></tr><tr><td align="left"/><td align="left">Fischer-R</td><td align="left"><bold>82.1%</bold></td><td align="left">53.5%</td><td align="left">66.8%</td></tr><tr><td align="left"/><td align="left">Maris</td><td align="left">53.1%</td><td align="left"><bold>54.3%</bold></td><td align="left">50.0%</td></tr><tr><td align="left"/><td align="left">Versteeg</td><td align="left"><bold>75.0%</bold></td><td align="left">53.3%</td><td align="left">67.5%</td></tr><tr><td align="left">Fischer-R</td><td align="left"><italic>Fischer-R</italic></td><td align="left"><italic>85.8%</italic></td><td align="left"><italic>66.0%</italic></td><td align="left"><italic>62.4%</italic></td></tr><tr><td align="left"/><td align="left">Fischer-M</td><td align="left"><bold>81.5%</bold></td><td align="left">75.4%</td><td align="left">61.2%</td></tr><tr><td align="left"/><td align="left">Maris</td><td align="left"><bold>56.2%</bold></td><td align="left">49.7%</td><td align="left">50.0%</td></tr><tr><td align="left"/><td align="left">Versteeg</td><td align="left"><bold>70.8%</bold></td><td align="left">68.3%</td><td align="left">67.5%</td></tr><tr><td align="left" colspan="4">Clinical outcome = &#x02018;<italic>Disease progression</italic>&#x02019;,</td><td align="left"/></tr><tr><td align="left" colspan="4">Data = centralities</td><td align="left"/></tr><tr><td align="left">Fischer-M</td><td align="left"><italic>Fischer-M</italic></td><td align="left"><italic>84.3%</italic></td><td align="left"><italic>83.7%</italic></td><td align="left"><italic>80.0%</italic></td></tr><tr><td align="left"/><td align="left">Fischer-R</td><td align="left"><bold>77.0%</bold></td><td align="left">75.2%</td><td align="left">71.8%</td></tr><tr><td align="left"/><td align="left">Maris</td><td align="left"><bold>67.5%</bold></td><td align="left">66.0%</td><td align="left">53.8%</td></tr><tr><td align="left"/><td align="left">Versteeg</td><td align="left">78.1%</td><td align="left"><bold>82.4%</bold></td><td align="left">78.1%</td></tr><tr><td align="left">Fischer-R</td><td align="left"><italic>Fischer-R</italic></td><td align="left"><italic>83.7%</italic></td><td align="left"><italic>81.0%</italic></td><td align="left"><italic>73.3%</italic></td></tr><tr><td align="left"/><td align="left">Fischer-M</td><td align="left"><bold>80.0%</bold></td><td align="left">76.8%</td><td align="left">75.0%</td></tr><tr><td align="left"/><td align="left">Maris</td><td align="left"><bold>67.5%</bold></td><td align="left">58.8%</td><td align="left">58.8%</td></tr><tr><td align="left"/><td align="left">Versteeg</td><td align="left"><bold>80.1%</bold></td><td align="left">77.2%</td><td align="left">73.9%</td></tr></tbody></table><table-wrap-foot><p>Models are trained using one of the &#x02018;<italic>Fischer</italic>&#x02019; datasets and then tested using either the other &#x02018;<italic>Fischer</italic>&#x02019; dataset or another independent dataset (&#x02018;<italic>Maris</italic>&#x02019; and &#x02018;<italic>Versteeg</italic>&#x02019;). The &#x02018;<italic>Maris</italic>&#x02019; and &#x02018;<italic>Versteeg</italic>&#x02019; datasets are too small to be used for both training and therefore are only used for validation. Rows in italics represent reference models (training and testing extracted from the same datasets)</p></table-wrap-foot></table-wrap></p><p>When considering the fully independent datasets, we observe two different behaviors. For the &#x02018;<italic>Maris</italic>&#x02019; dataset, the performance ranges from random-like (DNN, 53% and 56%) to average (DNN, 68%) for &#x02018;<italic>Death from disease</italic>&#x02019; and &#x02018;<italic>Disease progression</italic>&#x02019; respectively. Similar results are obtained for SVM and RF models (between 50% and 66%). Altogether, these results indicate that none of the models is able to classify the samples of this dataset. However, for the &#x02018;<italic>Versteeg</italic>&#x02019; dataset, and for both clinical outcomes, the models are more accurate (DNN, from 71% to 80%), in the range of the state of the art for neuroblastoma. A similar trend is observed for the SVM and RF models, although the DNN models seem superior in most cases. The drop in performance for <italic>Versteeg</italic>&#x02019; (with respect to the reference models) is within the same range than for &#x02018;<italic>Fischer</italic>&#x02019; (DNN, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[3.6.,15.0]; SVM, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[&#x02212;2.3,22.1]; RF, <italic>&#x00394;</italic><sub><italic>bACC</italic></sub>&#x02208;[&#x02212;5.1,7.6]). For both &#x02018;<italic>Maris</italic>&#x02019; and &#x02018;<italic>Versteeg</italic>&#x02019; datasets, it is difficult to appreciate the classification accuracies in the absence of reference models, due to the small number of samples available for these two cohorts (less than 100).</p></sec></sec><sec id="Sec16" sec-type="discussion"><title>Discussion</title><p>We evaluate several strategies to build models that use expression profiles of patients as input to classify patients according to their clinical outcomes. We propose to tackle the &#x0201c;small n large p&#x0201d; problem, frequently associated with such omics datasets, via a graph-based dimension reduction method. We have applied our approach to four neuroblastoma datasets to create and optimize models based on their classification accuracy.</p><p>We first investigate the usefulness of different sets of topological features by measuring the performance of classification models using different inputs. We observe that centrality features are associated with better average performances than modularity features. We also note that the features extracted from the RNA-seq data are associated with lower performance than the equivalent features extracted from the microarray data. Both seems to contradict our previous study of the same classification problem, in which we reported no statistical difference between models built from both sets [<xref ref-type="bibr" rid="CR29">29</xref>]. It is important to notice however that the learning algorithms and the data stratification are different between the two studies, which might explain this discrepancy. In addition, the accuracies reported here are often greater than the values reported previously, but not always by the same margin, which creates differences that were not apparent before. We also observe that the difference is mostly driven by the weak performance of models relying on the modularity features extracted from the &#x02018;<italic>Fischer-R</italic>&#x02019; dataset. This suggests that although the individual RNA-sequencing features do correlate with clinical outcomes, their integration produces modules whose correlation is lower (in comparison to microarray data). This corroborates a recent observation that deriving meaningful modules from WGCNA co-expression graphs can be rather challenging [<xref ref-type="bibr" rid="CR36">36</xref>].</p><p>We observe that the combined feature sets are not associated with any improvement upon the individual feature sets. This indicates that both sets might actually measure the same topological signal, which is in line with our previous observations [<xref ref-type="bibr" rid="CR29">29</xref>]. Similarly, the integration of the data across the two expression datasets does not improve the average performance. This was rather expected since the two datasets measure the same biological signal (i.e., gene expression) albeit through the use of different technologies.</p><p>Neural networks are known to be rather challenging to optimize, and a small variation in one parameter can have dramatic consequences, especially when the set of instances is rather limited. We indeed observe important variations in performance within the categories we have defined (e.g., models using only centralities or four layer DNN models) as illustrated in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> and Supplementary Figures S1 and S2.</p><p>The parameters with the greatest influence on performance are the optimization strategy (Adam really seems superior in our case) and the dropout (it should be below 0.4). In the latter case, it is not surprising that ignoring at least 40% of the nodes can have a huge impact on networks that have less than 100 input nodes and at best 8 nodes per hidden layer.</p><p>Regarding the network architecture, models relying on four layer networks perform the best for both clinical outcomes (Table&#x000a0;<xref rid="Tab3" ref-type="table">3</xref>). This is in agreement with previous studies that have reported that such relatively small networks (i.e., with three or four layers) can efficiently predict clinical outcomes of kidney cancer patients [<xref ref-type="bibr" rid="CR18">18</xref>] or can capture relevant features for survival analyses of a neuroblastoma cohort [<xref ref-type="bibr" rid="CR16">16</xref>].</p><p>Even if there are differences, as highlighted above, the optimal models and parameters are surprisingly similar for both clinical outcomes. This is true for the input data, the network architecture and the optimal values of the hyper-parameters. We also note, however, that this might be due to the underlying correlation between the two clinical outcomes since almost all patients who died from the disease have experienced progression of the disease.</p><p>When applied on the &#x02018;<italic>Fischer</italic>&#x02019; datasets, the DNN classifiers outperform both SVM and RF classifiers for both outcomes. The gain in performance is modest for &#x02018;<italic>Disease progression</italic>&#x02019; but rather large for &#x02018;<italic>Death from disease</italic>&#x02019;, which was previously considered as the hardest outcome to predict among the two [<xref ref-type="bibr" rid="CR8">8</xref>].</p><p>We also compare our neural networks fed with graph topological features (DNN) to neural networks fed with expression profiles directly (GEDFN). We notice that the GEDFN models perform at least as good as the SVM and RF models, but also that they remain less accurate than the DNN models. Altogether these observations support the idea that deep neural networks could indeed be more effective than traditional SVM and RF models. In addition, it seems that coupling such deep neural networks with a graph-based topological analysis can give even more accurate models.</p><p>Last, we validate the models using independent datasets. The hypothesis of these experiments is that the topological features we derived from the omics data are independent of the technology used in the first place and can therefore enable better generalization. As long as a graph of patients (PSN) can be created, it will be possible to derive topological features even if microarrays have been used in one study and sequencing in another study (or any other biomedical data for that matter). We therefore hypothesize that a model trained using one cohort might be tested using another cohort, especially when this second cohort is too small to be used to train another model by itself.</p><p>We start by comparing the two &#x02018;<italic>Fischer</italic>&#x02019; datasets. As expected, we observe a small decrease in performance in most cases when applying the models on the &#x02018;<italic>Fischer</italic>&#x02019; dataset that was not used for training. Surprisingly, for SVM and RF, the performance for the independent datasets is sometimes better than the reference performance. However, this happens only when the reference performance is moderate at best (i.e., <italic>b</italic><italic>A</italic><italic>C</italic><italic>C</italic>&#x0003c;75<italic>%</italic>). For DNN models, the performance on the independent datasets is still reasonable (at least 81.5% and 77% for &#x02018;<italic>Death from disease</italic>&#x02019; and &#x02018;<italic>Disease progression</italic>&#x02019; respectively) and sometimes even better than reference SVM and RF models (in six of the eight comparisons, Table&#x000a0;<xref rid="Tab5" ref-type="table">5</xref>).</p><p>We then include two additional datasets that are too small to be used to train classification models (&#x02018;<italic>Maris</italic>&#x02019; and &#x02018;<italic>Versteeg</italic>&#x02019; datasets). Similarly to above, we note that, in most cases, the DNN models are more accurate than the corresponding SVM and RF models, especially for the &#x02018;<italic>Death from disease</italic>&#x02019; outcome. Regarding the poor overall performance on the &#x02018;<italic>Maris</italic>&#x02019; dataset, we observe that it is the oldest of the datasets, associated with one of the first human high-throughput microarray platform (HG-U95A), that contains less probes than there are human genes (Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>). In addition, we note that the median patient follow-up for this dataset was 2.3 years, which, according to the authors of the original publication, was too short to allow them to study the relationship between expression profiles and clinical outcome, in particular patient survival [<xref ref-type="bibr" rid="CR24">24</xref>] (page 6052). In contrast, the median patient follow-up for the &#x02018;<italic>Versteeg</italic>&#x02019; dataset was 12.5 years, which allows for a more accurate measure of long term clinical outcomes. Altogether, these reasons might explain why the performance remains poor for the &#x02018;<italic>Maris</italic>&#x02019; dataset (especially for &#x02018;<italic>Death from disease</italic>&#x02019;) in contrast to the other datasets.</p></sec><sec id="Sec17" sec-type="conclusion"><title>Conclusion</title><p>We propose a graph-based method to extract features from patient derived omics data. These topological features are then used as input to a deep neural network that can classify patients according to their clinical outcome. Our models can handle typical omics datasets (with small <italic>n</italic> and large <italic>p</italic>) first, by reducing the number of features (through extraction of topological features) and second, by fine tuning the deep neural networks and their parameters.</p><p>By applying our strategy to four neuroblastoma datasets, we observe that our models make more accurate predictions than models based on other algorithms or different strategies. This indicates that the deep neural networks are indeed capturing complex features in the data that other machine learning strategies might not. In addition, we also demonstrate that our graph-based feature extraction method allows to validate the trained models using external datasets, even when the original features are different.</p><p>Additional studies are however needed to explore the properties of these topological features and their usefulness when coupled to deep learning predictors. In particular, applications to other data types (beside gene expression data) and other genetic disorders (beside neuroblastoma) are necessary to validate the global utility of the proposed approach. Moreover, other modeling strategies that integrate graphs (and their topology) into the learning process, such as graph-based CNN [<xref ref-type="bibr" rid="CR37">37</xref>, <xref ref-type="bibr" rid="CR38">38</xref>] would need to be explored as well.</p></sec><sec sec-type="supplementary-material"><title>Supplementary information</title><sec id="Sec18"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12920_2019_628_MOESM1_ESM.pdf"><caption><p><bold>Additional file 1</bold> Supplementary Figures S1-S2. PDF file.</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM2"><media xlink:href="12920_2019_628_MOESM2_ESM.xlsx"><caption><p><bold>Additional file 2</bold> Patient stratification of the &#x02018;<italic>Fischer</italic>&#x02019; dataset. XLSX file.</p></caption></media></supplementary-material>
</p><p>
<supplementary-material content-type="local-data" id="MOESM3"><media xlink:href="12920_2019_628_MOESM3_ESM.zip"><caption><p><bold>Additional file 3</bold> Full results of all models. Each model is described by its parameters and the corresponding balanced accuracy. Archive of XLSX files.</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>bACC</term><def><p>balanced accuracy</p></def></def-item><def-item><term>CNN</term><def><p>Convolutional Neural Network</p></def></def-item><def-item><term>DNN</term><def><p>Deep Neural Network</p></def></def-item><def-item><term>GEO</term><def><p>Gene Expression Omnibus</p></def></def-item><def-item><term>PPIN</term><def><p>Protein-Protein Interaction Network</p></def></def-item><def-item><term>PSN</term><def><p>Patient Similarity Networks</p></def></def-item><def-item><term>RBF</term><def><p>Radial Basis Function</p></def></def-item><def-item><term>ReLU</term><def><p>Rectified Linear Unit</p></def></def-item><def-item><term>RF</term><def><p>Random Forest</p></def></def-item><def-item><term>RNA</term><def><p>RiboNucleic Acid</p></def></def-item><def-item><term>SBM</term><def><p>Stochastic Block Model</p></def></def-item><def-item><term>SVM</term><def><p>Support Vector Machine</p></def></def-item><def-item><term>TCGA</term><def><p>The Cancer Genome Atlas</p></def></def-item><def-item><term>WGCNA</term><def><p>Weighted Correlation Network Analysis</p></def></def-item></def-list></glossary><fn-group><fn><p><bold>Publisher&#x02019;s Note</bold></p><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></fn></fn-group><sec><title>Supplementary information</title><p><bold>Supplementary information</bold> accompanies this paper at 10.1186/s12920-019-0628-y.</p></sec><ack><title>Acknowledgements</title><p>We thank the Fischer, Maris and Veersteeg laboratories for sharing their neuroblastoma data. In particular, we thank Dr John M. Maris and Dr Alvin Farrel for helping us with the clinical data of their cohort. We thank Dr Liyanaarachchi Lekamalage Chamara Kasun for helpful discussion about the DNN models. We thank Tony Kaoma and Dr Petr V. Nazarov for helpful discussions regarding the model comparison. We thank Dr Enrico Glaab and Dr Rama Kaalia for their support during the project.</p><sec id="d29e2340"><title>About this supplement</title><p>This article has been published as part of <italic>BMC Medical Genomics, Volume 12 Supplement 8, 2019: 18th International Conference on Bioinformatics</italic>. The full contents of the supplement are available at <ext-link ext-link-type="uri" xlink:href="https://bmcmedgenomics.biomedcentral.com/articles/supplements/volume-12-supplement-8">https://bmcmedgenomics.biomedcentral.com/articles/supplements/volume-12-supplement-8</ext-link>.</p></sec></ack><notes notes-type="author-contribution"><sec id="FPar1"><title>Authors&#x02019; contributions</title><p>All authors have developed the strategy. LT has implemented the method and applied it to the neuroblastoma datasets. All authors have analyzed the results. LT wrote an initial draft of the manuscript. All authors have revised the manuscript. All authors read and approved the final manuscript.</p></sec><sec id="FPar2"><title>Authors&#x02019; information</title><p>Not applicable.</p></sec></notes><notes notes-type="funding-information"><title>Funding</title><p>Project supported by the Fonds National de la Recherche (FNR), Luxembourg (SINGALUN project). This research was also partially supported by Tier-2 grant MOE2016-T2-1-029 by the Ministry of Education, Singapore. Publication of this supplement was funded by a Tier-2 grant MOE2016-T2-1-029 by the Ministry of Education, Singapore. The funders had no role in study design, data collection and analysis, decision to publish, or preparation of the manuscript.</p></notes><notes><title>Ethics approval and consent to participate</title><p>Not applicable.</p></notes><notes><title>Consent for publication</title><p>Not applicable.</p></notes><notes notes-type="COI-statement"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xiao</surname><given-names>Bin</given-names></name><name><surname>Zhang</surname><given-names>Weiyun</given-names></name><name><surname>Chen</surname><given-names>Lidan</given-names></name><name><surname>Hang</surname><given-names>Jianfeng</given-names></name><name><surname>Wang</surname><given-names>Lizhi</given-names></name><name><surname>Zhang</surname><given-names>Rong</given-names></name><name><surname>Liao</surname><given-names>Yang</given-names></name><name><surname>Chen</surname><given-names>Jianyun</given-names></name><name><surname>Ma</surname><given-names>Qiang</given-names></name><name><surname>Sun</surname><given-names>Zhaohui</given-names></name><name><surname>Li</surname><given-names>Linhai</given-names></name></person-group><article-title>Analysis of the miRNA&#x02013;mRNA&#x02013;lncRNA network in human estrogen receptor-positive and estrogen receptor-negative breast cancer based on TCGA data</article-title><source>Gene</source><year>2018</year><volume>658</volume><fpage>28</fpage><lpage>35</lpage><pub-id pub-id-type="doi">10.1016/j.gene.2018.03.011</pub-id><pub-id pub-id-type="pmid">29518546</pub-id></element-citation></ref><ref id="CR2"><label>2</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jayasinghe</surname><given-names>Reyka G.</given-names></name><name><surname>Cao</surname><given-names>Song</given-names></name><name><surname>Gao</surname><given-names>Qingsong</given-names></name><name><surname>Wendl</surname><given-names>Michael C.</given-names></name><name><surname>Vo</surname><given-names>Nam Sy</given-names></name><name><surname>Reynolds</surname><given-names>Sheila M.</given-names></name><name><surname>Zhao</surname><given-names>Yanyan</given-names></name><name><surname>Climente-Gonz&#x000e1;lez</surname><given-names>H&#x000e9;ctor</given-names></name><name><surname>Chai</surname><given-names>Shengjie</given-names></name><name><surname>Wang</surname><given-names>Fang</given-names></name><name><surname>Varghese</surname><given-names>Rajees</given-names></name><name><surname>Huang</surname><given-names>Mo</given-names></name><name><surname>Liang</surname><given-names>Wen-Wei</given-names></name><name><surname>Wyczalkowski</surname><given-names>Matthew A.</given-names></name><name><surname>Sengupta</surname><given-names>Sohini</given-names></name><name><surname>Li</surname><given-names>Zhi</given-names></name><name><surname>Payne</surname><given-names>Samuel H.</given-names></name><name><surname>Feny&#x000f6;</surname><given-names>David</given-names></name><name><surname>Miner</surname><given-names>Jeffrey H.</given-names></name><name><surname>Walter</surname><given-names>Matthew J.</given-names></name><name><surname>Vincent</surname><given-names>Benjamin</given-names></name><name><surname>Eyras</surname><given-names>Eduardo</given-names></name><name><surname>Chen</surname><given-names>Ken</given-names></name><name><surname>Shmulevich</surname><given-names>Ilya</given-names></name><name><surname>Chen</surname><given-names>Feng</given-names></name><name><surname>Ding</surname><given-names>Li</given-names></name><name><surname>Caesar-Johnson</surname><given-names>Samantha J.</given-names></name><name><surname>Demchok</surname><given-names>John A.</given-names></name><name><surname>Felau</surname><given-names>Ina</given-names></name><name><surname>Kasapi</surname><given-names>Melpomeni</given-names></name><name><surname>Ferguson</surname><given-names>Martin L.</given-names></name><name><surname>Hutter</surname><given-names>Carolyn M.</given-names></name><name><surname>Sofia</surname><given-names>Heidi J.</given-names></name><name><surname>Tarnuzzer</surname><given-names>Roy</given-names></name><name><surname>Wang</surname><given-names>Zhining</given-names></name><name><surname>Yang</surname><given-names>Liming</given-names></name><name><surname>Zenklusen</surname><given-names>Jean C.</given-names></name><name><surname>Zhang</surname><given-names>Jiashan (Julia)</given-names></name><name><surname>Chudamani</surname><given-names>Sudha</given-names></name><name><surname>Liu</surname><given-names>Jia</given-names></name><name><surname>Lolla</surname><given-names>Laxmi</given-names></name><name><surname>Naresh</surname><given-names>Rashi</given-names></name><name><surname>Pihl</surname><given-names>Todd</given-names></name><name><surname>Sun</surname><given-names>Qiang</given-names></name><name><surname>Wan</surname><given-names>Yunhu</given-names></name><name><surname>Wu</surname><given-names>Ye</given-names></name><name><surname>Cho</surname><given-names>Juok</given-names></name><name><surname>DeFreitas</surname><given-names>Timothy</given-names></name><name><surname>Frazer</surname><given-names>Scott</given-names></name><name><surname>Gehlenborg</surname><given-names>Nils</given-names></name><name><surname>Getz</surname><given-names>Gad</given-names></name><name><surname>Heiman</surname><given-names>David I.</given-names></name><name><surname>Kim</surname><given-names>Jaegil</given-names></name><name><surname>Lawrence</surname><given-names>Michael S.</given-names></name><name><surname>Lin</surname><given-names>Pei</given-names></name><name><surname>Meier</surname><given-names>Sam</given-names></name><name><surname>Noble</surname><given-names>Michael S.</given-names></name><name><surname>Saksena</surname><given-names>Gordon</given-names></name><name><surname>Voet</surname><given-names>Doug</given-names></name><name><surname>Zhang</surname><given-names>Hailei</given-names></name><name><surname>Bernard</surname><given-names>Brady</given-names></name><name><surname>Chambwe</surname><given-names>Nyasha</given-names></name><name><surname>Dhankani</surname><given-names>Varsha</given-names></name><name><surname>Knijnenburg</surname><given-names>Theo</given-names></name><name><surname>Kramer</surname><given-names>Roger</given-names></name><name><surname>Leinonen</surname><given-names>Kalle</given-names></name><name><surname>Liu</surname><given-names>Yuexin</given-names></name><name><surname>Miller</surname><given-names>Michael</given-names></name><name><surname>Reynolds</surname><given-names>Sheila</given-names></name><name><surname>Shmulevich</surname><given-names>Ilya</given-names></name><name><surname>Thorsson</surname><given-names>Vesteinn</given-names></name><name><surname>Zhang</surname><given-names>Wei</given-names></name><name><surname>Akbani</surname><given-names>Rehan</given-names></name><name><surname>Broom</surname><given-names>Bradley M.</given-names></name><name><surname>Hegde</surname><given-names>Apurva M.</given-names></name><name><surname>Ju</surname><given-names>Zhenlin</given-names></name><name><surname>Kanchi</surname><given-names>Rupa S.</given-names></name><name><surname>Korkut</surname><given-names>Anil</given-names></name><name><surname>Li</surname><given-names>Jun</given-names></name><name><surname>Liang</surname><given-names>Han</given-names></name><name><surname>Ling</surname><given-names>Shiyun</given-names></name><name><surname>Liu</surname><given-names>Wenbin</given-names></name><name><surname>Lu</surname><given-names>Yiling</given-names></name><name><surname>Mills</surname><given-names>Gordon B.</given-names></name><name><surname>Ng</surname><given-names>Kwok-Shing</given-names></name><name><surname>Rao</surname><given-names>Arvind</given-names></name><name><surname>Ryan</surname><given-names>Michael</given-names></name><name><surname>Wang</surname><given-names>Jing</given-names></name><name><surname>Weinstein</surname><given-names>John N.</given-names></name><name><surname>Zhang</surname><given-names>Jiexin</given-names></name><name><surname>Abeshouse</surname><given-names>Adam</given-names></name><name><surname>Armenia</surname><given-names>Joshua</given-names></name><name><surname>Chakravarty</surname><given-names>Debyani</given-names></name><name><surname>Chatila</surname><given-names>Walid K.</given-names></name><name><surname>de Bruijn</surname><given-names>Ino</given-names></name><name><surname>Gao</surname><given-names>Jianjiong</given-names></name><name><surname>Gross</surname><given-names>Benjamin E.</given-names></name><name><surname>Heins</surname><given-names>Zachary J.</given-names></name><name><surname>Kundra</surname><given-names>Ritika</given-names></name><name><surname>La</surname><given-names>Konnor</given-names></name><name><surname>Ladanyi</surname><given-names>Marc</given-names></name><name><surname>Luna</surname><given-names>Augustin</given-names></name><name><surname>Nissan</surname><given-names>Moriah G.</given-names></name><name><surname>Ochoa</surname><given-names>Angelica</given-names></name><name><surname>Phillips</surname><given-names>Sarah M.</given-names></name><name><surname>Reznik</surname><given-names>Ed</given-names></name><name><surname>Sanchez-Vega</surname><given-names>Francisco</given-names></name><name><surname>Sander</surname><given-names>Chris</given-names></name><name><surname>Schultz</surname><given-names>Nikolaus</given-names></name><name><surname>Sheridan</surname><given-names>Robert</given-names></name><name><surname>Sumer</surname><given-names>S. Onur</given-names></name><name><surname>Sun</surname><given-names>Yichao</given-names></name><name><surname>Taylor</surname><given-names>Barry S.</given-names></name><name><surname>Wang</surname><given-names>Jioajiao</given-names></name><name><surname>Zhang</surname><given-names>Hongxin</given-names></name><name><surname>Anur</surname><given-names>Pavana</given-names></name><name><surname>Peto</surname><given-names>Myron</given-names></name><name><surname>Spellman</surname><given-names>Paul</given-names></name><name><surname>Benz</surname><given-names>Christopher</given-names></name><name><surname>Stuart</surname><given-names>Joshua M.</given-names></name><name><surname>Wong</surname><given-names>Christopher K.</given-names></name><name><surname>Yau</surname><given-names>Christina</given-names></name><name><surname>Hayes</surname><given-names>D. Neil</given-names></name><name><surname>Parker</surname><given-names>Joel S.</given-names></name><name><surname>Wilkerson</surname><given-names>Matthew D.</given-names></name><name><surname>Ally</surname><given-names>Adrian</given-names></name><name><surname>Balasundaram</surname><given-names>Miruna</given-names></name><name><surname>Bowlby</surname><given-names>Reanne</given-names></name><name><surname>Brooks</surname><given-names>Denise</given-names></name><name><surname>Carlsen</surname><given-names>Rebecca</given-names></name><name><surname>Chuah</surname><given-names>Eric</given-names></name><name><surname>Dhalla</surname><given-names>Noreen</given-names></name><name><surname>Holt</surname><given-names>Robert</given-names></name><name><surname>Jones</surname><given-names>Steven J.M.</given-names></name><name><surname>Kasaian</surname><given-names>Katayoon</given-names></name><name><surname>Lee</surname><given-names>Darlene</given-names></name><name><surname>Ma</surname><given-names>Yussanne</given-names></name><name><surname>Marra</surname><given-names>Marco A.</given-names></name><name><surname>Mayo</surname><given-names>Michael</given-names></name><name><surname>Moore</surname><given-names>Richard A.</given-names></name><name><surname>Mungall</surname><given-names>Andrew J.</given-names></name><name><surname>Mungall</surname><given-names>Karen</given-names></name><name><surname>Robertson</surname><given-names>A. Gordon</given-names></name><name><surname>Sadeghi</surname><given-names>Sara</given-names></name><name><surname>Schein</surname><given-names>Jacqueline E.</given-names></name><name><surname>Sipahimalani</surname><given-names>Payal</given-names></name><name><surname>Tam</surname><given-names>Angela</given-names></name><name><surname>Thiessen</surname><given-names>Nina</given-names></name><name><surname>Tse</surname><given-names>Kane</given-names></name><name><surname>Wong</surname><given-names>Tina</given-names></name><name><surname>Berger</surname><given-names>Ashton C.</given-names></name><name><surname>Beroukhim</surname><given-names>Rameen</given-names></name><name><surname>Cherniack</surname><given-names>Andrew D.</given-names></name><name><surname>Cibulskis</surname><given-names>Carrie</given-names></name><name><surname>Gabriel</surname><given-names>Stacey B.</given-names></name><name><surname>Gao</surname><given-names>Galen F.</given-names></name><name><surname>Ha</surname><given-names>Gavin</given-names></name><name><surname>Meyerson</surname><given-names>Matthew</given-names></name><name><surname>Schumacher</surname><given-names>Steven E.</given-names></name><name><surname>Shih</surname><given-names>Juliann</given-names></name><name><surname>Kucherlapati</surname><given-names>Melanie H.</given-names></name><name><surname>Kucherlapati</surname><given-names>Raju S.</given-names></name><name><surname>Baylin</surname><given-names>Stephen</given-names></name><name><surname>Cope</surname><given-names>Leslie</given-names></name><name><surname>Danilova</surname><given-names>Ludmila</given-names></name><name><surname>Bootwalla</surname><given-names>Moiz S.</given-names></name><name><surname>Lai</surname><given-names>Phillip H.</given-names></name><name><surname>Maglinte</surname><given-names>Dennis T.</given-names></name><name><surname>Van Den Berg</surname><given-names>David J.</given-names></name><name><surname>Weisenberger</surname><given-names>Daniel J.</given-names></name><name><surname>Auman</surname><given-names>J. Todd</given-names></name><name><surname>Balu</surname><given-names>Saianand</given-names></name><name><surname>Bodenheimer</surname><given-names>Tom</given-names></name><name><surname>Fan</surname><given-names>Cheng</given-names></name><name><surname>Hoadley</surname><given-names>Katherine A.</given-names></name><name><surname>Hoyle</surname><given-names>Alan P.</given-names></name><name><surname>Jefferys</surname><given-names>Stuart R.</given-names></name><name><surname>Jones</surname><given-names>Corbin D.</given-names></name><name><surname>Meng</surname><given-names>Shaowu</given-names></name><name><surname>Mieczkowski</surname><given-names>Piotr A.</given-names></name><name><surname>Mose</surname><given-names>Lisle E.</given-names></name><name><surname>Perou</surname><given-names>Amy H.</given-names></name><name><surname>Perou</surname><given-names>Charles M.</given-names></name><name><surname>Roach</surname><given-names>Jeffrey</given-names></name><name><surname>Shi</surname><given-names>Yan</given-names></name><name><surname>Simons</surname><given-names>Janae V.</given-names></name><name><surname>Skelly</surname><given-names>Tara</given-names></name><name><surname>Soloway</surname><given-names>Matthew G.</given-names></name><name><surname>Tan</surname><given-names>Donghui</given-names></name><name><surname>Veluvolu</surname><given-names>Umadevi</given-names></name><name><surname>Fan</surname><given-names>Huihui</given-names></name><name><surname>Hinoue</surname><given-names>Toshinori</given-names></name><name><surname>Laird</surname><given-names>Peter W.</given-names></name><name><surname>Shen</surname><given-names>Hui</given-names></name><name><surname>Zhou</surname><given-names>Wanding</given-names></name><name><surname>Bellair</surname><given-names>Michelle</given-names></name><name><surname>Chang</surname><given-names>Kyle</given-names></name><name><surname>Covington</surname><given-names>Kyle</given-names></name><name><surname>Creighton</surname><given-names>Chad J.</given-names></name><name><surname>Dinh</surname><given-names>Huyen</given-names></name><name><surname>Doddapaneni</surname><given-names>HarshaVardhan</given-names></name><name><surname>Donehower</surname><given-names>Lawrence A.</given-names></name><name><surname>Drummond</surname><given-names>Jennifer</given-names></name><name><surname>Gibbs</surname><given-names>Richard A.</given-names></name><name><surname>Glenn</surname><given-names>Robert</given-names></name><name><surname>Hale</surname><given-names>Walker</given-names></name><name><surname>Han</surname><given-names>Yi</given-names></name><name><surname>Hu</surname><given-names>Jianhong</given-names></name><name><surname>Korchina</surname><given-names>Viktoriya</given-names></name><name><surname>Lee</surname><given-names>Sandra</given-names></name><name><surname>Lewis</surname><given-names>Lora</given-names></name><name><surname>Li</surname><given-names>Wei</given-names></name><name><surname>Liu</surname><given-names>Xiuping</given-names></name><name><surname>Morgan</surname><given-names>Margaret</given-names></name><name><surname>Morton</surname><given-names>Donna</given-names></name><name><surname>Muzny</surname><given-names>Donna</given-names></name><name><surname>Santibanez</surname><given-names>Jireh</given-names></name><name><surname>Sheth</surname><given-names>Margi</given-names></name><name><surname>Shinbrot</surname><given-names>Eve</given-names></name><name><surname>Wang</surname><given-names>Linghua</given-names></name><name><surname>Wang</surname><given-names>Min</given-names></name><name><surname>Wheeler</surname><given-names>David A.</given-names></name><name><surname>Xi</surname><given-names>Liu</given-names></name><name><surname>Zhao</surname><given-names>Fengmei</given-names></name><name><surname>Hess</surname><given-names>Julian</given-names></name><name><surname>Appelbaum</surname><given-names>Elizabeth L.</given-names></name><name><surname>Bailey</surname><given-names>Matthew</given-names></name><name><surname>Cordes</surname><given-names>Matthew G.</given-names></name><name><surname>Ding</surname><given-names>Li</given-names></name><name><surname>Fronick</surname><given-names>Catrina C.</given-names></name><name><surname>Fulton</surname><given-names>Lucinda A.</given-names></name><name><surname>Fulton</surname><given-names>Robert S.</given-names></name><name><surname>Kandoth</surname><given-names>Cyriac</given-names></name><name><surname>Mardis</surname><given-names>Elaine R.</given-names></name><name><surname>McLellan</surname><given-names>Michael D.</given-names></name><name><surname>Miller</surname><given-names>Christopher A.</given-names></name><name><surname>Schmidt</surname><given-names>Heather K.</given-names></name><name><surname>Wilson</surname><given-names>Richard K.</given-names></name><name><surname>Crain</surname><given-names>Daniel</given-names></name><name><surname>Curley</surname><given-names>Erin</given-names></name><name><surname>Gardner</surname><given-names>Johanna</given-names></name><name><surname>Lau</surname><given-names>Kevin</given-names></name><name><surname>Mallery</surname><given-names>David</given-names></name><name><surname>Morris</surname><given-names>Scott</given-names></name><name><surname>Paulauskis</surname><given-names>Joseph</given-names></name><name><surname>Penny</surname><given-names>Robert</given-names></name><name><surname>Shelton</surname><given-names>Candace</given-names></name><name><surname>Shelton</surname><given-names>Troy</given-names></name><name><surname>Sherman</surname><given-names>Mark</given-names></name><name><surname>Thompson</surname><given-names>Eric</given-names></name><name><surname>Yena</surname><given-names>Peggy</given-names></name><name><surname>Bowen</surname><given-names>Jay</given-names></name><name><surname>Gastier-Foster</surname><given-names>Julie M.</given-names></name><name><surname>Gerken</surname><given-names>Mark</given-names></name><name><surname>Leraas</surname><given-names>Kristen M.</given-names></name><name><surname>Lichtenberg</surname><given-names>Tara M.</given-names></name><name><surname>Ramirez</surname><given-names>Nilsa C.</given-names></name><name><surname>Wise</surname><given-names>Lisa</given-names></name><name><surname>Zmuda</surname><given-names>Erik</given-names></name><name><surname>Corcoran</surname><given-names>Niall</given-names></name><name><surname>Costello</surname><given-names>Tony</given-names></name><name><surname>Hovens</surname><given-names>Christopher</given-names></name><name><surname>Carvalho</surname><given-names>Andre L.</given-names></name><name><surname>de Carvalho</surname><given-names>Ana C.</given-names></name><name><surname>Fregnani</surname><given-names>Jos&#x000e9; H.</given-names></name><name><surname>Longatto-Filho</surname><given-names>Adhemar</given-names></name><name><surname>Reis</surname><given-names>Rui M.</given-names></name><name><surname>Scapulatempo-Neto</surname><given-names>Cristovam</given-names></name><name><surname>Silveira</surname><given-names>Henrique C.S.</given-names></name><name><surname>Vidal</surname><given-names>Daniel O.</given-names></name><name><surname>Burnette</surname><given-names>Andrew</given-names></name><name><surname>Eschbacher</surname><given-names>Jennifer</given-names></name><name><surname>Hermes</surname><given-names>Beth</given-names></name><name><surname>Noss</surname><given-names>Ardene</given-names></name><name><surname>Singh</surname><given-names>Rosy</given-names></name><name><surname>Anderson</surname><given-names>Matthew L.</given-names></name><name><surname>Castro</surname><given-names>Patricia D.</given-names></name><name><surname>Ittmann</surname><given-names>Michael</given-names></name><name><surname>Huntsman</surname><given-names>David</given-names></name><name><surname>Kohl</surname><given-names>Bernard</given-names></name><name><surname>Le</surname><given-names>Xuan</given-names></name><name><surname>Thorp</surname><given-names>Richard</given-names></name><name><surname>Andry</surname><given-names>Chris</given-names></name><name><surname>Duffy</surname><given-names>Elizabeth R.</given-names></name><name><surname>Lyadov</surname><given-names>Vladimir</given-names></name><name><surname>Paklina</surname><given-names>Oxana</given-names></name><name><surname>Setdikova</surname><given-names>Galiya</given-names></name><name><surname>Shabunin</surname><given-names>Alexey</given-names></name><name><surname>Tavobilov</surname><given-names>Mikhail</given-names></name><name><surname>McPherson</surname><given-names>Christopher</given-names></name><name><surname>Warnick</surname><given-names>Ronald</given-names></name><name><surname>Berkowitz</surname><given-names>Ross</given-names></name><name><surname>Cramer</surname><given-names>Daniel</given-names></name><name><surname>Feltmate</surname><given-names>Colleen</given-names></name><name><surname>Horowitz</surname><given-names>Neil</given-names></name><name><surname>Kibel</surname><given-names>Adam</given-names></name><name><surname>Muto</surname><given-names>Michael</given-names></name><name><surname>Raut</surname><given-names>Chandrajit P.</given-names></name><name><surname>Malykh</surname><given-names>Andrei</given-names></name><name><surname>Barnholtz-Sloan</surname><given-names>Jill S.</given-names></name><name><surname>Barrett</surname><given-names>Wendi</given-names></name><name><surname>Devine</surname><given-names>Karen</given-names></name><name><surname>Fulop</surname><given-names>Jordonna</given-names></name><name><surname>Ostrom</surname><given-names>Quinn T.</given-names></name><name><surname>Shimmel</surname><given-names>Kristen</given-names></name><name><surname>Wolinsky</surname><given-names>Yingli</given-names></name><name><surname>Sloan</surname><given-names>Andrew E.</given-names></name><name><surname>De Rose</surname><given-names>Agostino</given-names></name><name><surname>Giuliante</surname><given-names>Felice</given-names></name><name><surname>Goodman</surname><given-names>Marc</given-names></name><name><surname>Karlan</surname><given-names>Beth Y.</given-names></name><name><surname>Hagedorn</surname><given-names>Curt H.</given-names></name><name><surname>Eckman</surname><given-names>John</given-names></name><name><surname>Harr</surname><given-names>Jodi</given-names></name><name><surname>Myers</surname><given-names>Jerome</given-names></name><name><surname>Tucker</surname><given-names>Kelinda</given-names></name><name><surname>Zach</surname><given-names>Leigh Anne</given-names></name><name><surname>Deyarmin</surname><given-names>Brenda</given-names></name><name><surname>Hu</surname><given-names>Hai</given-names></name><name><surname>Kvecher</surname><given-names>Leonid</given-names></name><name><surname>Larson</surname><given-names>Caroline</given-names></name><name><surname>Mural</surname><given-names>Richard J.</given-names></name><name><surname>Somiari</surname><given-names>Stella</given-names></name><name><surname>Vicha</surname><given-names>Ales</given-names></name><name><surname>Zelinka</surname><given-names>Tomas</given-names></name><name><surname>Bennett</surname><given-names>Joseph</given-names></name><name><surname>Iacocca</surname><given-names>Mary</given-names></name><name><surname>Rabeno</surname><given-names>Brenda</given-names></name><name><surname>Swanson</surname><given-names>Patricia</given-names></name><name><surname>Latour</surname><given-names>Mathieu</given-names></name><name><surname>Lacombe</surname><given-names>Louis</given-names></name><name><surname>T&#x000ea;tu</surname><given-names>Bernard</given-names></name><name><surname>Bergeron</surname><given-names>Alain</given-names></name><name><surname>McGraw</surname><given-names>Mary</given-names></name><name><surname>Staugaitis</surname><given-names>Susan M.</given-names></name><name><surname>Chabot</surname><given-names>John</given-names></name><name><surname>Hibshoosh</surname><given-names>Hanina</given-names></name><name><surname>Sepulveda</surname><given-names>Antonia</given-names></name><name><surname>Su</surname><given-names>Tao</given-names></name><name><surname>Wang</surname><given-names>Timothy</given-names></name><name><surname>Potapova</surname><given-names>Olga</given-names></name><name><surname>Voronina</surname><given-names>Olga</given-names></name><name><surname>Desjardins</surname><given-names>Laurence</given-names></name><name><surname>Mariani</surname><given-names>Odette</given-names></name><name><surname>Roman-Roman</surname><given-names>Sergio</given-names></name><name><surname>Sastre</surname><given-names>Xavier</given-names></name><name><surname>Stern</surname><given-names>Marc-Henri</given-names></name><name><surname>Cheng</surname><given-names>Feixiong</given-names></name><name><surname>Signoretti</surname><given-names>Sabina</given-names></name><name><surname>Berchuck</surname><given-names>Andrew</given-names></name><name><surname>Bigner</surname><given-names>Darell</given-names></name><name><surname>Lipp</surname><given-names>Eric</given-names></name><name><surname>Marks</surname><given-names>Jeffrey</given-names></name><name><surname>McCall</surname><given-names>Shannon</given-names></name><name><surname>McLendon</surname><given-names>Roger</given-names></name><name><surname>Secord</surname><given-names>Angeles</given-names></name><name><surname>Sharp</surname><given-names>Alexis</given-names></name><name><surname>Behera</surname><given-names>Madhusmita</given-names></name><name><surname>Brat</surname><given-names>Daniel J.</given-names></name><name><surname>Chen</surname><given-names>Amy</given-names></name><name><surname>Delman</surname><given-names>Keith</given-names></name><name><surname>Force</surname><given-names>Seth</given-names></name><name><surname>Khuri</surname><given-names>Fadlo</given-names></name><name><surname>Magliocca</surname><given-names>Kelly</given-names></name><name><surname>Maithel</surname><given-names>Shishir</given-names></name><name><surname>Olson</surname><given-names>Jeffrey J.</given-names></name><name><surname>Owonikoko</surname><given-names>Taofeek</given-names></name><name><surname>Pickens</surname><given-names>Alan</given-names></name><name><surname>Ramalingam</surname><given-names>Suresh</given-names></name><name><surname>Shin</surname><given-names>Dong M.</given-names></name><name><surname>Sica</surname><given-names>Gabriel</given-names></name><name><surname>Van Meir</surname><given-names>Erwin G.</given-names></name><name><surname>Zhang</surname><given-names>Hongzheng</given-names></name><name><surname>Eijckenboom</surname><given-names>Wil</given-names></name><name><surname>Gillis</surname><given-names>Ad</given-names></name><name><surname>Korpershoek</surname><given-names>Esther</given-names></name><name><surname>Looijenga</surname><given-names>Leendert</given-names></name><name><surname>Oosterhuis</surname><given-names>Wolter</given-names></name><name><surname>Stoop</surname><given-names>Hans</given-names></name><name><surname>van Kessel</surname><given-names>Kim E.</given-names></name><name><surname>Zwarthoff</surname><given-names>Ellen C.</given-names></name><name><surname>Calatozzolo</surname><given-names>Chiara</given-names></name><name><surname>Cuppini</surname><given-names>Lucia</given-names></name><name><surname>Cuzzubbo</surname><given-names>Stefania</given-names></name><name><surname>DiMeco</surname><given-names>Francesco</given-names></name><name><surname>Finocchiaro</surname><given-names>Gaetano</given-names></name><name><surname>Mattei</surname><given-names>Luca</given-names></name><name><surname>Perin</surname><given-names>Alessandro</given-names></name><name><surname>Pollo</surname><given-names>Bianca</given-names></name><name><surname>Chen</surname><given-names>Chu</given-names></name><name><surname>Houck</surname><given-names>John</given-names></name><name><surname>Lohavanichbutr</surname><given-names>Pawadee</given-names></name><name><surname>Hartmann</surname><given-names>Arndt</given-names></name><name><surname>Stoehr</surname><given-names>Christine</given-names></name><name><surname>Stoehr</surname><given-names>Robert</given-names></name><name><surname>Taubert</surname><given-names>Helge</given-names></name><name><surname>Wach</surname><given-names>Sven</given-names></name><name><surname>Wullich</surname><given-names>Bernd</given-names></name><name><surname>Kycler</surname><given-names>Witold</given-names></name><name><surname>Murawa</surname><given-names>Dawid</given-names></name><name><surname>Wiznerowicz</surname><given-names>Maciej</given-names></name><name><surname>Chung</surname><given-names>Ki</given-names></name><name><surname>Edenfield</surname><given-names>W. Jeffrey</given-names></name><name><surname>Martin</surname><given-names>Julie</given-names></name><name><surname>Baudin</surname><given-names>Eric</given-names></name><name><surname>Bubley</surname><given-names>Glenn</given-names></name><name><surname>Bueno</surname><given-names>Raphael</given-names></name><name><surname>De Rienzo</surname><given-names>Assunta</given-names></name><name><surname>Richards</surname><given-names>William G.</given-names></name><name><surname>Kalkanis</surname><given-names>Steven</given-names></name><name><surname>Mikkelsen</surname><given-names>Tom</given-names></name><name><surname>Noushmehr</surname><given-names>Houtan</given-names></name><name><surname>Scarpace</surname><given-names>Lisa</given-names></name><name><surname>Girard</surname><given-names>Nicolas</given-names></name><name><surname>Aymerich</surname><given-names>Marta</given-names></name><name><surname>Campo</surname><given-names>Elias</given-names></name><name><surname>Gin&#x000e9;</surname><given-names>Eva</given-names></name><name><surname>Guillermo</surname><given-names>Armando L&#x000f3;pez</given-names></name><name><surname>Van Bang</surname><given-names>Nguyen</given-names></name><name><surname>Hanh</surname><given-names>Phan Thi</given-names></name><name><surname>Phu</surname><given-names>Bui Duc</given-names></name><name><surname>Tang</surname><given-names>Yufang</given-names></name><name><surname>Colman</surname><given-names>Howard</given-names></name><name><surname>Evason</surname><given-names>Kimberley</given-names></name><name><surname>Dottino</surname><given-names>Peter R.</given-names></name><name><surname>Martignetti</surname><given-names>John A.</given-names></name><name><surname>Gabra</surname><given-names>Hani</given-names></name><name><surname>Juhl</surname><given-names>Hartmut</given-names></name><name><surname>Akeredolu</surname><given-names>Teniola</given-names></name><name><surname>Stepa</surname><given-names>Serghei</given-names></name><name><surname>Hoon</surname><given-names>Dave</given-names></name><name><surname>Ahn</surname><given-names>Keunsoo</given-names></name><name><surname>Kang</surname><given-names>Koo Jeong</given-names></name><name><surname>Beuschlein</surname><given-names>Felix</given-names></name><name><surname>Breggia</surname><given-names>Anne</given-names></name><name><surname>Birrer</surname><given-names>Michael</given-names></name><name><surname>Bell</surname><given-names>Debra</given-names></name><name><surname>Borad</surname><given-names>Mitesh</given-names></name><name><surname>Bryce</surname><given-names>Alan H.</given-names></name><name><surname>Castle</surname><given-names>Erik</given-names></name><name><surname>Chandan</surname><given-names>Vishal</given-names></name><name><surname>Cheville</surname><given-names>John</given-names></name><name><surname>Copland</surname><given-names>John A.</given-names></name><name><surname>Farnell</surname><given-names>Michael</given-names></name><name><surname>Flotte</surname><given-names>Thomas</given-names></name><name><surname>Giama</surname><given-names>Nasra</given-names></name><name><surname>Ho</surname><given-names>Thai</given-names></name><name><surname>Kendrick</surname><given-names>Michael</given-names></name><name><surname>Kocher</surname><given-names>Jean-Pierre</given-names></name><name><surname>Kopp</surname><given-names>Karla</given-names></name><name><surname>Moser</surname><given-names>Catherine</given-names></name><name><surname>Nagorney</surname><given-names>David</given-names></name><name><surname>O&#x02019;Brien</surname><given-names>Daniel</given-names></name><name><surname>O&#x02019;Neill</surname><given-names>Brian Patrick</given-names></name><name><surname>Patel</surname><given-names>Tushar</given-names></name><name><surname>Petersen</surname><given-names>Gloria</given-names></name><name><surname>Que</surname><given-names>Florencia</given-names></name><name><surname>Rivera</surname><given-names>Michael</given-names></name><name><surname>Roberts</surname><given-names>Lewis</given-names></name><name><surname>Smallridge</surname><given-names>Robert</given-names></name><name><surname>Smyrk</surname><given-names>Thomas</given-names></name><name><surname>Stanton</surname><given-names>Melissa</given-names></name><name><surname>Thompson</surname><given-names>R. Houston</given-names></name><name><surname>Torbenson</surname><given-names>Michael</given-names></name><name><surname>Yang</surname><given-names>Ju Dong</given-names></name><name><surname>Zhang</surname><given-names>Lizhi</given-names></name><name><surname>Brimo</surname><given-names>Fadi</given-names></name><name><surname>Ajani</surname><given-names>Jaffer A.</given-names></name><name><surname>Gonzalez</surname><given-names>Ana Maria Angulo</given-names></name><name><surname>Behrens</surname><given-names>Carmen</given-names></name><name><surname>Bondaruk</surname><given-names>Jolanta</given-names></name><name><surname>Broaddus</surname><given-names>Russell</given-names></name><name><surname>Czerniak</surname><given-names>Bogdan</given-names></name><name><surname>Esmaeli</surname><given-names>Bita</given-names></name><name><surname>Fujimoto</surname><given-names>Junya</given-names></name><name><surname>Gershenwald</surname><given-names>Jeffrey</given-names></name><name><surname>Guo</surname><given-names>Charles</given-names></name><name><surname>Lazar</surname><given-names>Alexander J.</given-names></name><name><surname>Logothetis</surname><given-names>Christopher</given-names></name><name><surname>Meric-Bernstam</surname><given-names>Funda</given-names></name><name><surname>Moran</surname><given-names>Cesar</given-names></name><name><surname>Ramondetta</surname><given-names>Lois</given-names></name><name><surname>Rice</surname><given-names>David</given-names></name><name><surname>Sood</surname><given-names>Anil</given-names></name><name><surname>Tamboli</surname><given-names>Pheroze</given-names></name><name><surname>Thompson</surname><given-names>Timothy</given-names></name><name><surname>Troncoso</surname><given-names>Patricia</given-names></name><name><surname>Tsao</surname><given-names>Anne</given-names></name><name><surname>Wistuba</surname><given-names>Ignacio</given-names></name><name><surname>Carter</surname><given-names>Candace</given-names></name><name><surname>Haydu</surname><given-names>Lauren</given-names></name><name><surname>Hersey</surname><given-names>Peter</given-names></name><name><surname>Jakrot</surname><given-names>Valerie</given-names></name><name><surname>Kakavand</surname><given-names>Hojabr</given-names></name><name><surname>Kefford</surname><given-names>Richard</given-names></name><name><surname>Lee</surname><given-names>Kenneth</given-names></name><name><surname>Long</surname><given-names>Georgina</given-names></name><name><surname>Mann</surname><given-names>Graham</given-names></name><name><surname>Quinn</surname><given-names>Michael</given-names></name><name><surname>Saw</surname><given-names>Robyn</given-names></name><name><surname>Scolyer</surname><given-names>Richard</given-names></name><name><surname>Shannon</surname><given-names>Kerwin</given-names></name><name><surname>Spillane</surname><given-names>Andrew</given-names></name><name><surname>Stretch</surname><given-names>Jonathan</given-names></name><name><surname>Synott</surname><given-names>Maria</given-names></name><name><surname>Thompson</surname><given-names>John</given-names></name><name><surname>Wilmott</surname><given-names>James</given-names></name><name><surname>Al-Ahmadie</surname><given-names>Hikmat</given-names></name><name><surname>Chan</surname><given-names>Timothy A.</given-names></name><name><surname>Ghossein</surname><given-names>Ronald</given-names></name><name><surname>Gopalan</surname><given-names>Anuradha</given-names></name><name><surname>Levine</surname><given-names>Douglas A.</given-names></name><name><surname>Reuter</surname><given-names>Victor</given-names></name><name><surname>Singer</surname><given-names>Samuel</given-names></name><name><surname>Singh</surname><given-names>Bhuvanesh</given-names></name><name><surname>Tien</surname><given-names>Nguyen Viet</given-names></name><name><surname>Broudy</surname><given-names>Thomas</given-names></name><name><surname>Mirsaidi</surname><given-names>Cyrus</given-names></name><name><surname>Nair</surname><given-names>Praveen</given-names></name><name><surname>Drwiega</surname><given-names>Paul</given-names></name><name><surname>Miller</surname><given-names>Judy</given-names></name><name><surname>Smith</surname><given-names>Jennifer</given-names></name><name><surname>Zaren</surname><given-names>Howard</given-names></name><name><surname>Park</surname><given-names>Joong-Won</given-names></name><name><surname>Hung</surname><given-names>Nguyen Phi</given-names></name><name><surname>Kebebew</surname><given-names>Electron</given-names></name><name><surname>Linehan</surname><given-names>W. Marston</given-names></name><name><surname>Metwalli</surname><given-names>Adam R.</given-names></name><name><surname>Pacak</surname><given-names>Karel</given-names></name><name><surname>Pinto</surname><given-names>Peter A.</given-names></name><name><surname>Schiffman</surname><given-names>Mark</given-names></name><name><surname>Schmidt</surname><given-names>Laura S.</given-names></name><name><surname>Vocke</surname><given-names>Cathy D.</given-names></name><name><surname>Wentzensen</surname><given-names>Nicolas</given-names></name><name><surname>Worrell</surname><given-names>Robert</given-names></name><name><surname>Yang</surname><given-names>Hannah</given-names></name><name><surname>Moncrieff</surname><given-names>Marc</given-names></name><name><surname>Goparaju</surname><given-names>Chandra</given-names></name><name><surname>Melamed</surname><given-names>Jonathan</given-names></name><name><surname>Pass</surname><given-names>Harvey</given-names></name><name><surname>Botnariuc</surname><given-names>Natalia</given-names></name><name><surname>Caraman</surname><given-names>Irina</given-names></name><name><surname>Cernat</surname><given-names>Mircea</given-names></name><name><surname>Chemencedji</surname><given-names>Inga</given-names></name><name><surname>Clipca</surname><given-names>Adrian</given-names></name><name><surname>Doruc</surname><given-names>Serghei</given-names></name><name><surname>Gorincioi</surname><given-names>Ghenadie</given-names></name><name><surname>Mura</surname><given-names>Sergiu</given-names></name><name><surname>Pirtac</surname><given-names>Maria</given-names></name><name><surname>Stancul</surname><given-names>Irina</given-names></name><name><surname>Tcaciuc</surname><given-names>Diana</given-names></name><name><surname>Albert</surname><given-names>Monique</given-names></name><name><surname>Alexopoulou</surname><given-names>Iakovina</given-names></name><name><surname>Arnaout</surname><given-names>Angel</given-names></name><name><surname>Bartlett</surname><given-names>John</given-names></name><name><surname>Engel</surname><given-names>Jay</given-names></name><name><surname>Gilbert</surname><given-names>Sebastien</given-names></name><name><surname>Parfitt</surname><given-names>Jeremy</given-names></name><name><surname>Sekhon</surname><given-names>Harman</given-names></name><name><surname>Thomas</surname><given-names>George</given-names></name><name><surname>Rassl</surname><given-names>Doris M.</given-names></name><name><surname>Rintoul</surname><given-names>Robert C.</given-names></name><name><surname>Bifulco</surname><given-names>Carlo</given-names></name><name><surname>Tamakawa</surname><given-names>Raina</given-names></name><name><surname>Urba</surname><given-names>Walter</given-names></name><name><surname>Hayward</surname><given-names>Nicholas</given-names></name><name><surname>Timmers</surname><given-names>Henri</given-names></name><name><surname>Antenucci</surname><given-names>Anna</given-names></name><name><surname>Facciolo</surname><given-names>Francesco</given-names></name><name><surname>Grazi</surname><given-names>Gianluca</given-names></name><name><surname>Marino</surname><given-names>Mirella</given-names></name><name><surname>Merola</surname><given-names>Roberta</given-names></name><name><surname>de Krijger</surname><given-names>Ronald</given-names></name><name><surname>Gimenez-Roqueplo</surname><given-names>Anne-Paule</given-names></name><name><surname>Pich&#x000e9;</surname><given-names>Alain</given-names></name><name><surname>Chevalier</surname><given-names>Simone</given-names></name><name><surname>McKercher</surname><given-names>Ginette</given-names></name><name><surname>Birsoy</surname><given-names>Kivanc</given-names></name><name><surname>Barnett</surname><given-names>Gene</given-names></name><name><surname>Brewer</surname><given-names>Cathy</given-names></name><name><surname>Farver</surname><given-names>Carol</given-names></name><name><surname>Naska</surname><given-names>Theresa</given-names></name><name><surname>Pennell</surname><given-names>Nathan A.</given-names></name><name><surname>Raymond</surname><given-names>Daniel</given-names></name><name><surname>Schilero</surname><given-names>Cathy</given-names></name><name><surname>Smolenski</surname><given-names>Kathy</given-names></name><name><surname>Williams</surname><given-names>Felicia</given-names></name><name><surname>Morrison</surname><given-names>Carl</given-names></name><name><surname>Borgia</surname><given-names>Jeffrey A.</given-names></name><name><surname>Liptay</surname><given-names>Michael J.</given-names></name><name><surname>Pool</surname><given-names>Mark</given-names></name><name><surname>Seder</surname><given-names>Christopher W.</given-names></name><name><surname>Junker</surname><given-names>Kerstin</given-names></name><name><surname>Omberg</surname><given-names>Larsson</given-names></name><name><surname>Dinkin</surname><given-names>Mikhail</given-names></name><name><surname>Manikhas</surname><given-names>George</given-names></name><name><surname>Alvaro</surname><given-names>Domenico</given-names></name><name><surname>Bragazzi</surname><given-names>Maria Consiglia</given-names></name><name><surname>Cardinale</surname><given-names>Vincenzo</given-names></name><name><surname>Carpino</surname><given-names>Guido</given-names></name><name><surname>Gaudio</surname><given-names>Eugenio</given-names></name><name><surname>Chesla</surname><given-names>David</given-names></name><name><surname>Cottingham</surname><given-names>Sandra</given-names></name><name><surname>Dubina</surname><given-names>Michael</given-names></name><name><surname>Moiseenko</surname><given-names>Fedor</given-names></name><name><surname>Dhanasekaran</surname><given-names>Renumathy</given-names></name><name><surname>Becker</surname><given-names>Karl-Friedrich</given-names></name><name><surname>Janssen</surname><given-names>Klaus-Peter</given-names></name><name><surname>Slotta-Huspenina</surname><given-names>Julia</given-names></name><name><surname>Abdel-Rahman</surname><given-names>Mohamed H.</given-names></name><name><surname>Aziz</surname><given-names>Dina</given-names></name><name><surname>Bell</surname><given-names>Sue</given-names></name><name><surname>Cebulla</surname><given-names>Colleen M.</given-names></name><name><surname>Davis</surname><given-names>Amy</given-names></name><name><surname>Duell</surname><given-names>Rebecca</given-names></name><name><surname>Elder</surname><given-names>J. Bradley</given-names></name><name><surname>Hilty</surname><given-names>Joe</given-names></name><name><surname>Kumar</surname><given-names>Bahavna</given-names></name><name><surname>Lang</surname><given-names>James</given-names></name><name><surname>Lehman</surname><given-names>Norman L.</given-names></name><name><surname>Mandt</surname><given-names>Randy</given-names></name><name><surname>Nguyen</surname><given-names>Phuong</given-names></name><name><surname>Pilarski</surname><given-names>Robert</given-names></name><name><surname>Rai</surname><given-names>Karan</given-names></name><name><surname>Schoenfield</surname><given-names>Lynn</given-names></name><name><surname>Senecal</surname><given-names>Kelly</given-names></name><name><surname>Wakely</surname><given-names>Paul</given-names></name><name><surname>Hansen</surname><given-names>Paul</given-names></name><name><surname>Lechan</surname><given-names>Ronald</given-names></name><name><surname>Powers</surname><given-names>James</given-names></name><name><surname>Tischler</surname><given-names>Arthur</given-names></name><name><surname>Grizzle</surname><given-names>William E.</given-names></name><name><surname>Sexton</surname><given-names>Katherine C.</given-names></name><name><surname>Kastl</surname><given-names>Alison</given-names></name><name><surname>Henderson</surname><given-names>Joel</given-names></name><name><surname>Porten</surname><given-names>Sima</given-names></name><name><surname>Waldmann</surname><given-names>Jens</given-names></name><name><surname>Fassnacht</surname><given-names>Martin</given-names></name><name><surname>Asa</surname><given-names>Sylvia L.</given-names></name><name><surname>Schadendorf</surname><given-names>Dirk</given-names></name><name><surname>Couce</surname><given-names>Marta</given-names></name><name><surname>Graefen</surname><given-names>Markus</given-names></name><name><surname>Huland</surname><given-names>Hartwig</given-names></name><name><surname>Sauter</surname><given-names>Guido</given-names></name><name><surname>Schlomm</surname><given-names>Thorsten</given-names></name><name><surname>Simon</surname><given-names>Ronald</given-names></name><name><surname>Tennstedt</surname><given-names>Pierre</given-names></name><name><surname>Olabode</surname><given-names>Oluwole</given-names></name><name><surname>Nelson</surname><given-names>Mark</given-names></name><name><surname>Bathe</surname><given-names>Oliver</given-names></name><name><surname>Carroll</surname><given-names>Peter R.</given-names></name><name><surname>Chan</surname><given-names>June M.</given-names></name><name><surname>Disaia</surname><given-names>Philip</given-names></name><name><surname>Glenn</surname><given-names>Pat</given-names></name><name><surname>Kelley</surname><given-names>Robin K.</given-names></name><name><surname>Landen</surname><given-names>Charles N.</given-names></name><name><surname>Phillips</surname><given-names>Joanna</given-names></name><name><surname>Prados</surname><given-names>Michael</given-names></name><name><surname>Simko</surname><given-names>Jeffry</given-names></name><name><surname>Smith-McCune</surname><given-names>Karen</given-names></name><name><surname>VandenBerg</surname><given-names>Scott</given-names></name><name><surname>Roggin</surname><given-names>Kevin</given-names></name><name><surname>Fehrenbach</surname><given-names>Ashley</given-names></name><name><surname>Kendler</surname><given-names>Ady</given-names></name><name><surname>Sifri</surname><given-names>Suzanne</given-names></name><name><surname>Steele</surname><given-names>Ruth</given-names></name><name><surname>Jimeno</surname><given-names>Antonio</given-names></name><name><surname>Carey</surname><given-names>Francis</given-names></name><name><surname>Forgie</surname><given-names>Ian</given-names></name><name><surname>Mannelli</surname><given-names>Massimo</given-names></name><name><surname>Carney</surname><given-names>Michael</given-names></name><name><surname>Hernandez</surname><given-names>Brenda</given-names></name><name><surname>Campos</surname><given-names>Benito</given-names></name><name><surname>Herold-Mende</surname><given-names>Christel</given-names></name><name><surname>Jungk</surname><given-names>Christin</given-names></name><name><surname>Unterberg</surname><given-names>Andreas</given-names></name><name><surname>von Deimling</surname><given-names>Andreas</given-names></name><name><surname>Bossler</surname><given-names>Aaron</given-names></name><name><surname>Galbraith</surname><given-names>Joseph</given-names></name><name><surname>Jacobus</surname><given-names>Laura</given-names></name><name><surname>Knudson</surname><given-names>Michael</given-names></name><name><surname>Knutson</surname><given-names>Tina</given-names></name><name><surname>Ma</surname><given-names>Deqin</given-names></name><name><surname>Milhem</surname><given-names>Mohammed</given-names></name><name><surname>Sigmund</surname><given-names>Rita</given-names></name><name><surname>Godwin</surname><given-names>Andrew K.</given-names></name><name><surname>Madan</surname><given-names>Rashna</given-names></name><name><surname>Rosenthal</surname><given-names>Howard G.</given-names></name><name><surname>Adebamowo</surname><given-names>Clement</given-names></name><name><surname>Adebamowo</surname><given-names>Sally N.</given-names></name><name><surname>Boussioutas</surname><given-names>Alex</given-names></name><name><surname>Beer</surname><given-names>David</given-names></name><name><surname>Giordano</surname><given-names>Thomas</given-names></name><name><surname>Mes-Masson</surname><given-names>Anne-Marie</given-names></name><name><surname>Saad</surname><given-names>Fred</given-names></name><name><surname>Bocklage</surname><given-names>Therese</given-names></name><name><surname>Landrum</surname><given-names>Lisa</given-names></name><name><surname>Mannel</surname><given-names>Robert</given-names></name><name><surname>Moore</surname><given-names>Kathleen</given-names></name><name><surname>Moxley</surname><given-names>Katherine</given-names></name><name><surname>Postier</surname><given-names>Russel</given-names></name><name><surname>Walker</surname><given-names>Joan</given-names></name><name><surname>Zuna</surname><given-names>Rosemary</given-names></name><name><surname>Feldman</surname><given-names>Michael</given-names></name><name><surname>Valdivieso</surname><given-names>Federico</given-names></name><name><surname>Dhir</surname><given-names>Rajiv</given-names></name><name><surname>Luketich</surname><given-names>James</given-names></name><name><surname>Pinero</surname><given-names>Edna M. Mora</given-names></name><name><surname>Quintero-Aguilo</surname><given-names>Mario</given-names></name><name><surname>Carlotti</surname><given-names>Carlos Gilberto</given-names></name><name><surname>Dos Santos</surname><given-names>Jose Sebasti&#x000e3;o</given-names></name><name><surname>Kemp</surname><given-names>Rafael</given-names></name><name><surname>Sankarankuty</surname><given-names>Ajith</given-names></name><name><surname>Tirapelli</surname><given-names>Daniela</given-names></name><name><surname>Catto</surname><given-names>James</given-names></name><name><surname>Agnew</surname><given-names>Kathy</given-names></name><name><surname>Swisher</surname><given-names>Elizabeth</given-names></name><name><surname>Creaney</surname><given-names>Jenette</given-names></name><name><surname>Robinson</surname><given-names>Bruce</given-names></name><name><surname>Shelley</surname><given-names>Carl Simon</given-names></name><name><surname>Godwin</surname><given-names>Eryn M.</given-names></name><name><surname>Kendall</surname><given-names>Sara</given-names></name><name><surname>Shipman</surname><given-names>Cassaundra</given-names></name><name><surname>Bradford</surname><given-names>Carol</given-names></name><name><surname>Carey</surname><given-names>Thomas</given-names></name><name><surname>Haddad</surname><given-names>Andrea</given-names></name><name><surname>Moyer</surname><given-names>Jeffey</given-names></name><name><surname>Peterson</surname><given-names>Lisa</given-names></name><name><surname>Prince</surname><given-names>Mark</given-names></name><name><surname>Rozek</surname><given-names>Laura</given-names></name><name><surname>Wolf</surname><given-names>Gregory</given-names></name><name><surname>Bowman</surname><given-names>Rayleen</given-names></name><name><surname>Fong</surname><given-names>Kwun M.</given-names></name><name><surname>Yang</surname><given-names>Ian</given-names></name><name><surname>Korst</surname><given-names>Robert</given-names></name><name><surname>Rathmell</surname><given-names>W. Kimryn</given-names></name><name><surname>Fantacone-Campbell</surname><given-names>J. Leigh</given-names></name><name><surname>Hooke</surname><given-names>Jeffrey A.</given-names></name><name><surname>Kovatich</surname><given-names>Albert J.</given-names></name><name><surname>Shriver</surname><given-names>Craig D.</given-names></name><name><surname>DiPersio</surname><given-names>John</given-names></name><name><surname>Drake</surname><given-names>Bettina</given-names></name><name><surname>Govindan</surname><given-names>Ramaswamy</given-names></name><name><surname>Heath</surname><given-names>Sharon</given-names></name><name><surname>Ley</surname><given-names>Timothy</given-names></name><name><surname>Van Tine</surname><given-names>Brian</given-names></name><name><surname>Westervelt</surname><given-names>Peter</given-names></name><name><surname>Rubin</surname><given-names>Mark A.</given-names></name><name><surname>Lee</surname><given-names>Jung Il</given-names></name><name><surname>Aredes</surname><given-names>Nat&#x000e1;lia D.</given-names></name><name><surname>Mariamidze</surname><given-names>Armaz</given-names></name></person-group><article-title>Systematic Analysis of Splice-Site-Creating Mutations in Cancer</article-title><source>Cell Reports</source><year>2018</year><volume>23</volume><issue>1</issue><fpage>270-281.e3</fpage><pub-id pub-id-type="doi">10.1016/j.celrep.2018.03.052</pub-id><pub-id pub-id-type="pmid">29617666</pub-id></element-citation></ref><ref id="CR3"><label>3</label><mixed-citation publication-type="other">Suhre K, Arnold M, Bhagwat AM, Cotton RJ, Engelke R, Raffler J, Sarwath H, Thareja G, Wahl A, DeLisle RK, Gold L, Pezer M, Lauc G, El-Din Selim MA, Mook-Kanamori DO, Al-Dous EK, Mohamoud YA, Malek J, Strauch K, Grallert H, Peters A, Kastenm&#x000fc;ller G, Gieger C, Graumann J. Connecting genetic risk to disease end points through the human blood plasma proteome. Nat Commun; 8:14357. 10.1038/ncomms14357.</mixed-citation></ref><ref id="CR4"><label>4</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mook-Kanamori</surname><given-names>Dennis O.</given-names></name><name><surname>Selim</surname><given-names>Mohammed M. El-Din</given-names></name><name><surname>Takiddin</surname><given-names>Ahmed H.</given-names></name><name><surname>Al-Homsi</surname><given-names>Hala</given-names></name><name><surname>Al-Mahmoud</surname><given-names>Khoulood A. S.</given-names></name><name><surname>Al-Obaidli</surname><given-names>Amina</given-names></name><name><surname>Zirie</surname><given-names>Mahmoud A.</given-names></name><name><surname>Rowe</surname><given-names>Jillian</given-names></name><name><surname>Yousri</surname><given-names>Noha A.</given-names></name><name><surname>Karoly</surname><given-names>Edward D.</given-names></name><name><surname>Kocher</surname><given-names>Thomas</given-names></name><name><surname>Sekkal Gherbi</surname><given-names>Wafaa</given-names></name><name><surname>Chidiac</surname><given-names>Omar M.</given-names></name><name><surname>Mook-Kanamori</surname><given-names>Marjonneke J.</given-names></name><name><surname>Abdul Kader</surname><given-names>Sara</given-names></name><name><surname>Al Muftah</surname><given-names>Wadha A.</given-names></name><name><surname>McKeon</surname><given-names>Cindy</given-names></name><name><surname>Suhre</surname><given-names>Karsten</given-names></name></person-group><article-title>1,5-Anhydroglucitol in Saliva Is a Noninvasive Marker of Short-Term Glycemic Control</article-title><source>The Journal of Clinical Endocrinology &#x00026; Metabolism</source><year>2014</year><volume>99</volume><issue>3</issue><fpage>E479</fpage><lpage>E483</lpage><pub-id pub-id-type="doi">10.1210/jc.2013-3596</pub-id><pub-id pub-id-type="pmid">24423354</pub-id></element-citation></ref><ref id="CR5"><label>5</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Liloglou</surname><given-names>Triantafillos</given-names></name><name><surname>Bediaga</surname><given-names>Naiara G.</given-names></name><name><surname>Brown</surname><given-names>Benjamin R.B.</given-names></name><name><surname>Field</surname><given-names>John K.</given-names></name><name><surname>Davies</surname><given-names>Michael P.A.</given-names></name></person-group><article-title>Epigenetic biomarkers in lung cancer</article-title><source>Cancer Letters</source><year>2014</year><volume>342</volume><issue>2</issue><fpage>200</fpage><lpage>212</lpage><pub-id pub-id-type="doi">10.1016/j.canlet.2012.04.018</pub-id><pub-id pub-id-type="pmid">22546286</pub-id></element-citation></ref><ref id="CR6"><label>6</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Feng</surname><given-names>Hao</given-names></name><name><surname>Jin</surname><given-names>Peng</given-names></name><name><surname>Wu</surname><given-names>Hao</given-names></name></person-group><article-title>Disease prediction by cell-free DNA methylation</article-title><source>Briefings in Bioinformatics</source><year>2018</year><volume>20</volume><issue>2</issue><fpage>585</fpage><lpage>597</lpage><pub-id pub-id-type="doi">10.1093/bib/bby029</pub-id></element-citation></ref><ref id="CR7"><label>7</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Zehua</given-names></name><name><surname>Yang</surname><given-names>Bo</given-names></name><name><surname>Zhang</surname><given-names>Min</given-names></name><name><surname>Guo</surname><given-names>Weiwei</given-names></name><name><surname>Wu</surname><given-names>Zhiyuan</given-names></name><name><surname>Wang</surname><given-names>Yue</given-names></name><name><surname>Jia</surname><given-names>Lin</given-names></name><name><surname>Li</surname><given-names>Song</given-names></name><name><surname>Xie</surname><given-names>Wen</given-names></name><name><surname>Yang</surname><given-names>Da</given-names></name><name><surname>Caesar-Johnson</surname><given-names>Samantha J.</given-names></name><name><surname>Demchok</surname><given-names>John A.</given-names></name><name><surname>Felau</surname><given-names>Ina</given-names></name><name><surname>Kasapi</surname><given-names>Melpomeni</given-names></name><name><surname>Ferguson</surname><given-names>Martin L.</given-names></name><name><surname>Hutter</surname><given-names>Carolyn M.</given-names></name><name><surname>Sofia</surname><given-names>Heidi J.</given-names></name><name><surname>Tarnuzzer</surname><given-names>Roy</given-names></name><name><surname>Wang</surname><given-names>Zhining</given-names></name><name><surname>Yang</surname><given-names>Liming</given-names></name><name><surname>Zenklusen</surname><given-names>Jean C.</given-names></name><name><surname>Zhang</surname><given-names>Jiashan (Julia)</given-names></name><name><surname>Chudamani</surname><given-names>Sudha</given-names></name><name><surname>Liu</surname><given-names>Jia</given-names></name><name><surname>Lolla</surname><given-names>Laxmi</given-names></name><name><surname>Naresh</surname><given-names>Rashi</given-names></name><name><surname>Pihl</surname><given-names>Todd</given-names></name><name><surname>Sun</surname><given-names>Qiang</given-names></name><name><surname>Wan</surname><given-names>Yunhu</given-names></name><name><surname>Wu</surname><given-names>Ye</given-names></name><name><surname>Cho</surname><given-names>Juok</given-names></name><name><surname>DeFreitas</surname><given-names>Timothy</given-names></name><name><surname>Frazer</surname><given-names>Scott</given-names></name><name><surname>Gehlenborg</surname><given-names>Nils</given-names></name><name><surname>Getz</surname><given-names>Gad</given-names></name><name><surname>Heiman</surname><given-names>David I.</given-names></name><name><surname>Kim</surname><given-names>Jaegil</given-names></name><name><surname>Lawrence</surname><given-names>Michael S.</given-names></name><name><surname>Lin</surname><given-names>Pei</given-names></name><name><surname>Meier</surname><given-names>Sam</given-names></name><name><surname>Noble</surname><given-names>Michael S.</given-names></name><name><surname>Saksena</surname><given-names>Gordon</given-names></name><name><surname>Voet</surname><given-names>Doug</given-names></name><name><surname>Zhang</surname><given-names>Hailei</given-names></name><name><surname>Bernard</surname><given-names>Brady</given-names></name><name><surname>Chambwe</surname><given-names>Nyasha</given-names></name><name><surname>Dhankani</surname><given-names>Varsha</given-names></name><name><surname>Knijnenburg</surname><given-names>Theo</given-names></name><name><surname>Kramer</surname><given-names>Roger</given-names></name><name><surname>Leinonen</surname><given-names>Kalle</given-names></name><name><surname>Liu</surname><given-names>Yuexin</given-names></name><name><surname>Miller</surname><given-names>Michael</given-names></name><name><surname>Reynolds</surname><given-names>Sheila</given-names></name><name><surname>Shmulevich</surname><given-names>Ilya</given-names></name><name><surname>Thorsson</surname><given-names>Vesteinn</given-names></name><name><surname>Zhang</surname><given-names>Wei</given-names></name><name><surname>Akbani</surname><given-names>Rehan</given-names></name><name><surname>Broom</surname><given-names>Bradley M.</given-names></name><name><surname>Hegde</surname><given-names>Apurva M.</given-names></name><name><surname>Ju</surname><given-names>Zhenlin</given-names></name><name><surname>Kanchi</surname><given-names>Rupa S.</given-names></name><name><surname>Korkut</surname><given-names>Anil</given-names></name><name><surname>Li</surname><given-names>Jun</given-names></name><name><surname>Liang</surname><given-names>Han</given-names></name><name><surname>Ling</surname><given-names>Shiyun</given-names></name><name><surname>Liu</surname><given-names>Wenbin</given-names></name><name><surname>Lu</surname><given-names>Yiling</given-names></name><name><surname>Mills</surname><given-names>Gordon B.</given-names></name><name><surname>Ng</surname><given-names>Kwok-Shing</given-names></name><name><surname>Rao</surname><given-names>Arvind</given-names></name><name><surname>Ryan</surname><given-names>Michael</given-names></name><name><surname>Wang</surname><given-names>Jing</given-names></name><name><surname>Weinstein</surname><given-names>John N.</given-names></name><name><surname>Zhang</surname><given-names>Jiexin</given-names></name><name><surname>Abeshouse</surname><given-names>Adam</given-names></name><name><surname>Armenia</surname><given-names>Joshua</given-names></name><name><surname>Chakravarty</surname><given-names>Debyani</given-names></name><name><surname>Chatila</surname><given-names>Walid K.</given-names></name><name><surname>Bruijn</surname><given-names>Inode</given-names></name><name><surname>Gao</surname><given-names>Jianjiong</given-names></name><name><surname>Gross</surname><given-names>Benjamin E.</given-names></name><name><surname>Heins</surname><given-names>Zachary J.</given-names></name><name><surname>Kundra</surname><given-names>Ritika</given-names></name><name><surname>La</surname><given-names>Konnor</given-names></name><name><surname>Ladanyi</surname><given-names>Marc</given-names></name><name><surname>Luna</surname><given-names>Augustin</given-names></name><name><surname>Nissan</surname><given-names>Moriah G.</given-names></name><name><surname>Ochoa</surname><given-names>Angelica</given-names></name><name><surname>Phillips</surname><given-names>Sarah M.</given-names></name><name><surname>Reznik</surname><given-names>Ed</given-names></name><name><surname>Sanchez-Vega</surname><given-names>Francisco</given-names></name><name><surname>Sander</surname><given-names>Chris</given-names></name><name><surname>Schultz</surname><given-names>Nikolaus</given-names></name><name><surname>Sheridan</surname><given-names>Robert</given-names></name><name><surname>Sumer</surname><given-names>S. Onur</given-names></name><name><surname>Sun</surname><given-names>Yichao</given-names></name><name><surname>Taylor</surname><given-names>Barry S.</given-names></name><name><surname>Wang</surname><given-names>Jioajiao</given-names></name><name><surname>Zhang</surname><given-names>Hongxin</given-names></name><name><surname>Anur</surname><given-names>Pavana</given-names></name><name><surname>Peto</surname><given-names>Myron</given-names></name><name><surname>Spellman</surname><given-names>Paul</given-names></name><name><surname>Benz</surname><given-names>Christopher</given-names></name><name><surname>Stuart</surname><given-names>Joshua M.</given-names></name><name><surname>Wong</surname><given-names>Christopher K.</given-names></name><name><surname>Yau</surname><given-names>Christina</given-names></name><name><surname>Hayes</surname><given-names>D. Neil</given-names></name><name><surname>Parker</surname><given-names>Joel S.</given-names></name><name><surname>Wilkerson</surname><given-names>Matthew D.</given-names></name><name><surname>Ally</surname><given-names>Adrian</given-names></name><name><surname>Balasundaram</surname><given-names>Miruna</given-names></name><name><surname>Bowlby</surname><given-names>Reanne</given-names></name><name><surname>Brooks</surname><given-names>Denise</given-names></name><name><surname>Carlsen</surname><given-names>Rebecca</given-names></name><name><surname>Chuah</surname><given-names>Eric</given-names></name><name><surname>Dhalla</surname><given-names>Noreen</given-names></name><name><surname>Holt</surname><given-names>Robert</given-names></name><name><surname>Jones</surname><given-names>Steven J.M.</given-names></name><name><surname>Kasaian</surname><given-names>Katayoon</given-names></name><name><surname>Lee</surname><given-names>Darlene</given-names></name><name><surname>Ma</surname><given-names>Yussanne</given-names></name><name><surname>Marra</surname><given-names>Marco A.</given-names></name><name><surname>Mayo</surname><given-names>Michael</given-names></name><name><surname>Moore</surname><given-names>Richard A.</given-names></name><name><surname>Mungall</surname><given-names>Andrew J.</given-names></name><name><surname>Mungall</surname><given-names>Karen</given-names></name><name><surname>Robertson</surname><given-names>A. Gordon</given-names></name><name><surname>Sadeghi</surname><given-names>Sara</given-names></name><name><surname>Schein</surname><given-names>Jacqueline E.</given-names></name><name><surname>Sipahimalani</surname><given-names>Payal</given-names></name><name><surname>Tam</surname><given-names>Angela</given-names></name><name><surname>Thiessen</surname><given-names>Nina</given-names></name><name><surname>Tse</surname><given-names>Kane</given-names></name><name><surname>Wong</surname><given-names>Tina</given-names></name><name><surname>Berger</surname><given-names>Ashton C.</given-names></name><name><surname>Beroukhim</surname><given-names>Rameen</given-names></name><name><surname>Cherniack</surname><given-names>Andrew D.</given-names></name><name><surname>Cibulskis</surname><given-names>Carrie</given-names></name><name><surname>Gabriel</surname><given-names>Stacey B.</given-names></name><name><surname>Gao</surname><given-names>Galen F.</given-names></name><name><surname>Ha</surname><given-names>Gavin</given-names></name><name><surname>Meyerson</surname><given-names>Matthew</given-names></name><name><surname>Schumacher</surname><given-names>Steven E.</given-names></name><name><surname>Shih</surname><given-names>Juliann</given-names></name><name><surname>Kucherlapati</surname><given-names>Melanie H.</given-names></name><name><surname>Kucherlapati</surname><given-names>Raju S.</given-names></name><name><surname>Baylin</surname><given-names>Stephen</given-names></name><name><surname>Cope</surname><given-names>Leslie</given-names></name><name><surname>Danilova</surname><given-names>Ludmila</given-names></name><name><surname>Bootwalla</surname><given-names>Moiz S.</given-names></name><name><surname>Lai</surname><given-names>Phillip H.</given-names></name><name><surname>Maglinte</surname><given-names>Dennis T.</given-names></name><name><surname>Van Den Berg</surname><given-names>David J.</given-names></name><name><surname>Weisenberger</surname><given-names>Daniel J.</given-names></name><name><surname>Auman</surname><given-names>J. Todd</given-names></name><name><surname>Balu</surname><given-names>Saianand</given-names></name><name><surname>Bodenheimer</surname><given-names>Tom</given-names></name><name><surname>Fan</surname><given-names>Cheng</given-names></name><name><surname>Hoadley</surname><given-names>Katherine A.</given-names></name><name><surname>Hoyle</surname><given-names>Alan P.</given-names></name><name><surname>Jefferys</surname><given-names>Stuart R.</given-names></name><name><surname>Jones</surname><given-names>Corbin D.</given-names></name><name><surname>Meng</surname><given-names>Shaowu</given-names></name><name><surname>Mieczkowski</surname><given-names>Piotr A.</given-names></name><name><surname>Mose</surname><given-names>Lisle E.</given-names></name><name><surname>Perou</surname><given-names>Amy H.</given-names></name><name><surname>Perou</surname><given-names>Charles M.</given-names></name><name><surname>Roach</surname><given-names>Jeffrey</given-names></name><name><surname>Shi</surname><given-names>Yan</given-names></name><name><surname>Simons</surname><given-names>Janae V.</given-names></name><name><surname>Skelly</surname><given-names>Tara</given-names></name><name><surname>Soloway</surname><given-names>Matthew G.</given-names></name><name><surname>Tan</surname><given-names>Donghui</given-names></name><name><surname>Veluvolu</surname><given-names>Umadevi</given-names></name><name><surname>Fan</surname><given-names>Huihui</given-names></name><name><surname>Hinoue</surname><given-names>Toshinori</given-names></name><name><surname>Laird</surname><given-names>Peter W.</given-names></name><name><surname>Shen</surname><given-names>Hui</given-names></name><name><surname>Zhou</surname><given-names>Wanding</given-names></name><name><surname>Bellair</surname><given-names>Michelle</given-names></name><name><surname>Chang</surname><given-names>Kyle</given-names></name><name><surname>Covington</surname><given-names>Kyle</given-names></name><name><surname>Creighton</surname><given-names>Chad J.</given-names></name><name><surname>Dinh</surname><given-names>Huyen</given-names></name><name><surname>Doddapaneni</surname><given-names>HarshaVardhan</given-names></name><name><surname>Donehower</surname><given-names>Lawrence A.</given-names></name><name><surname>Drummond</surname><given-names>Jennifer</given-names></name><name><surname>Gibbs</surname><given-names>Richard A.</given-names></name><name><surname>Glenn</surname><given-names>Robert</given-names></name><name><surname>Hale</surname><given-names>Walker</given-names></name><name><surname>Han</surname><given-names>Yi</given-names></name><name><surname>Hu</surname><given-names>Jianhong</given-names></name><name><surname>Korchina</surname><given-names>Viktoriya</given-names></name><name><surname>Lee</surname><given-names>Sandra</given-names></name><name><surname>Lewis</surname><given-names>Lora</given-names></name><name><surname>Li</surname><given-names>Wei</given-names></name><name><surname>Liu</surname><given-names>Xiuping</given-names></name><name><surname>Morgan</surname><given-names>Margaret</given-names></name><name><surname>Morton</surname><given-names>Donna</given-names></name><name><surname>Muzny</surname><given-names>Donna</given-names></name><name><surname>Santibanez</surname><given-names>Jireh</given-names></name><name><surname>Sheth</surname><given-names>Margi</given-names></name><name><surname>Shinbrot</surname><given-names>Eve</given-names></name><name><surname>Wang</surname><given-names>Linghua</given-names></name><name><surname>Wang</surname><given-names>Min</given-names></name><name><surname>Wheeler</surname><given-names>David A.</given-names></name><name><surname>Xi</surname><given-names>Liu</given-names></name><name><surname>Zhao</surname><given-names>Fengmei</given-names></name><name><surname>Hess</surname><given-names>Julian</given-names></name><name><surname>Appelbaum</surname><given-names>Elizabeth L.</given-names></name><name><surname>Bailey</surname><given-names>Matthew</given-names></name><name><surname>Cordes</surname><given-names>Matthew G.</given-names></name><name><surname>Ding</surname><given-names>Li</given-names></name><name><surname>Fronick</surname><given-names>Catrina C.</given-names></name><name><surname>Fulton</surname><given-names>Lucinda A.</given-names></name><name><surname>Fulton</surname><given-names>Robert S.</given-names></name><name><surname>Kandoth</surname><given-names>Cyriac</given-names></name><name><surname>Mardis</surname><given-names>Elaine R.</given-names></name><name><surname>McLellan</surname><given-names>Michael D.</given-names></name><name><surname>Miller</surname><given-names>Christopher A.</given-names></name><name><surname>Schmidt</surname><given-names>Heather K.</given-names></name><name><surname>Wilson</surname><given-names>Richard K.</given-names></name><name><surname>Crain</surname><given-names>Daniel</given-names></name><name><surname>Curley</surname><given-names>Erin</given-names></name><name><surname>Gardner</surname><given-names>Johanna</given-names></name><name><surname>Lau</surname><given-names>Kevin</given-names></name><name><surname>Mallery</surname><given-names>David</given-names></name><name><surname>Morris</surname><given-names>Scott</given-names></name><name><surname>Paulauskis</surname><given-names>Joseph</given-names></name><name><surname>Penny</surname><given-names>Robert</given-names></name><name><surname>Shelton</surname><given-names>Candace</given-names></name><name><surname>Shelton</surname><given-names>Troy</given-names></name><name><surname>Sherman</surname><given-names>Mark</given-names></name><name><surname>Thompson</surname><given-names>Eric</given-names></name><name><surname>Yena</surname><given-names>Peggy</given-names></name><name><surname>Bowen</surname><given-names>Jay</given-names></name><name><surname>Gastier-Foster</surname><given-names>Julie M.</given-names></name><name><surname>Gerken</surname><given-names>Mark</given-names></name><name><surname>Leraas</surname><given-names>Kristen M.</given-names></name><name><surname>Lichtenberg</surname><given-names>Tara M.</given-names></name><name><surname>Ramirez</surname><given-names>Nilsa C.</given-names></name><name><surname>Wise</surname><given-names>Lisa</given-names></name><name><surname>Zmuda</surname><given-names>Erik</given-names></name><name><surname>Corcoran</surname><given-names>Niall</given-names></name><name><surname>Costello</surname><given-names>Tony</given-names></name><name><surname>Hovens</surname><given-names>Christopher</given-names></name><name><surname>Carvalho</surname><given-names>Andre L.</given-names></name><name><surname>de Carvalho</surname><given-names>Ana C.</given-names></name><name><surname>Fregnani</surname><given-names>Jos&#x000e9; H.</given-names></name><name><surname>Longatto-Filho</surname><given-names>Adhemar</given-names></name><name><surname>Reis</surname><given-names>Rui M.</given-names></name><name><surname>Scapulatempo-Neto</surname><given-names>Cristovam</given-names></name><name><surname>Silveira</surname><given-names>Henrique C.S.</given-names></name><name><surname>Vidal</surname><given-names>Daniel O.</given-names></name><name><surname>Burnette</surname><given-names>Andrew</given-names></name><name><surname>Eschbacher</surname><given-names>Jennifer</given-names></name><name><surname>Hermes</surname><given-names>Beth</given-names></name><name><surname>Noss</surname><given-names>Ardene</given-names></name><name><surname>Singh</surname><given-names>Rosy</given-names></name><name><surname>Anderson</surname><given-names>Matthew L.</given-names></name><name><surname>Castro</surname><given-names>Patricia D.</given-names></name><name><surname>Ittmann</surname><given-names>Michael</given-names></name><name><surname>Huntsman</surname><given-names>David</given-names></name><name><surname>Kohl</surname><given-names>Bernard</given-names></name><name><surname>Le</surname><given-names>Xuan</given-names></name><name><surname>Thorp</surname><given-names>Richard</given-names></name><name><surname>Andry</surname><given-names>Chris</given-names></name><name><surname>Duffy</surname><given-names>Elizabeth R.</given-names></name><name><surname>Lyadov</surname><given-names>Vladimir</given-names></name><name><surname>Paklina</surname><given-names>Oxana</given-names></name><name><surname>Setdikova</surname><given-names>Galiya</given-names></name><name><surname>Shabunin</surname><given-names>Alexey</given-names></name><name><surname>Tavobilov</surname><given-names>Mikhail</given-names></name><name><surname>McPherson</surname><given-names>Christopher</given-names></name><name><surname>Warnick</surname><given-names>Ronald</given-names></name><name><surname>Berkowitz</surname><given-names>Ross</given-names></name><name><surname>Cramer</surname><given-names>Daniel</given-names></name><name><surname>Feltmate</surname><given-names>Colleen</given-names></name><name><surname>Horowitz</surname><given-names>Neil</given-names></name><name><surname>Kibel</surname><given-names>Adam</given-names></name><name><surname>Muto</surname><given-names>Michael</given-names></name><name><surname>Raut</surname><given-names>Chandrajit P.</given-names></name><name><surname>Malykh</surname><given-names>Andrei</given-names></name><name><surname>Barnholtz-Sloan</surname><given-names>Jill S.</given-names></name><name><surname>Barrett</surname><given-names>Wendi</given-names></name><name><surname>Devine</surname><given-names>Karen</given-names></name><name><surname>Fulop</surname><given-names>Jordonna</given-names></name><name><surname>Ostrom</surname><given-names>Quinn T.</given-names></name><name><surname>Shimmel</surname><given-names>Kristen</given-names></name><name><surname>Wolinsky</surname><given-names>Yingli</given-names></name><name><surname>Sloan</surname><given-names>Andrew E.</given-names></name><name><surname>De Rose</surname><given-names>Agostino</given-names></name><name><surname>Giuliante</surname><given-names>Felice</given-names></name><name><surname>Goodman</surname><given-names>Marc</given-names></name><name><surname>Karlan</surname><given-names>Beth Y.</given-names></name><name><surname>Hagedorn</surname><given-names>Curt H.</given-names></name><name><surname>Eckman</surname><given-names>John</given-names></name><name><surname>Harr</surname><given-names>Jodi</given-names></name><name><surname>Myers</surname><given-names>Jerome</given-names></name><name><surname>Tucker</surname><given-names>Kelinda</given-names></name><name><surname>Zach</surname><given-names>Leigh Anne</given-names></name><name><surname>Deyarmin</surname><given-names>Brenda</given-names></name><name><surname>Hu</surname><given-names>Hai</given-names></name><name><surname>Kvecher</surname><given-names>Leonid</given-names></name><name><surname>Larson</surname><given-names>Caroline</given-names></name><name><surname>Mural</surname><given-names>Richard J.</given-names></name><name><surname>Somiari</surname><given-names>Stella</given-names></name><name><surname>Vicha</surname><given-names>Ales</given-names></name><name><surname>Zelinka</surname><given-names>Tomas</given-names></name><name><surname>Bennett</surname><given-names>Joseph</given-names></name><name><surname>Iacocca</surname><given-names>Mary</given-names></name><name><surname>Rabeno</surname><given-names>Brenda</given-names></name><name><surname>Swanson</surname><given-names>Patricia</given-names></name><name><surname>Latour</surname><given-names>Mathieu</given-names></name><name><surname>Lacombe</surname><given-names>Louis</given-names></name><name><surname>T&#x000ea;tu</surname><given-names>Bernard</given-names></name><name><surname>Bergeron</surname><given-names>Alain</given-names></name><name><surname>McGraw</surname><given-names>Mary</given-names></name><name><surname>Staugaitis</surname><given-names>Susan M.</given-names></name><name><surname>Chabot</surname><given-names>John</given-names></name><name><surname>Hibshoosh</surname><given-names>Hanina</given-names></name><name><surname>Sepulveda</surname><given-names>Antonia</given-names></name><name><surname>Su</surname><given-names>Tao</given-names></name><name><surname>Wang</surname><given-names>Timothy</given-names></name><name><surname>Potapova</surname><given-names>Olga</given-names></name><name><surname>Voronina</surname><given-names>Olga</given-names></name><name><surname>Desjardins</surname><given-names>Laurence</given-names></name><name><surname>Mariani</surname><given-names>Odette</given-names></name><name><surname>Roman-Roman</surname><given-names>Sergio</given-names></name><name><surname>Sastre</surname><given-names>Xavier</given-names></name><name><surname>Stern</surname><given-names>Marc-Henri</given-names></name><name><surname>Cheng</surname><given-names>Feixiong</given-names></name><name><surname>Signoretti</surname><given-names>Sabina</given-names></name><name><surname>Berchuck</surname><given-names>Andrew</given-names></name><name><surname>Bigner</surname><given-names>Darell</given-names></name><name><surname>Lipp</surname><given-names>Eric</given-names></name><name><surname>Marks</surname><given-names>Jeffrey</given-names></name><name><surname>McCall</surname><given-names>Shannon</given-names></name><name><surname>McLendon</surname><given-names>Roger</given-names></name><name><surname>Secord</surname><given-names>Angeles</given-names></name><name><surname>Sharp</surname><given-names>Alexis</given-names></name><name><surname>Behera</surname><given-names>Madhusmita</given-names></name><name><surname>Brat</surname><given-names>Daniel J.</given-names></name><name><surname>Chen</surname><given-names>Amy</given-names></name><name><surname>Delman</surname><given-names>Keith</given-names></name><name><surname>Force</surname><given-names>Seth</given-names></name><name><surname>Khuri</surname><given-names>Fadlo</given-names></name><name><surname>Magliocca</surname><given-names>Kelly</given-names></name><name><surname>Maithel</surname><given-names>Shishir</given-names></name><name><surname>Olson</surname><given-names>Jeffrey J.</given-names></name><name><surname>Owonikoko</surname><given-names>Taofeek</given-names></name><name><surname>Pickens</surname><given-names>Alan</given-names></name><name><surname>Ramalingam</surname><given-names>Suresh</given-names></name><name><surname>Shin</surname><given-names>Dong M.</given-names></name><name><surname>Sica</surname><given-names>Gabriel</given-names></name><name><surname>Van Meir</surname><given-names>Erwin G.</given-names></name><name><surname>Zhang</surname><given-names>Hongzheng</given-names></name><name><surname>Eijckenboom</surname><given-names>Wil</given-names></name><name><surname>Gillis</surname><given-names>Ad</given-names></name><name><surname>Korpershoek</surname><given-names>Esther</given-names></name><name><surname>Looijenga</surname><given-names>Leendert</given-names></name><name><surname>Oosterhuis</surname><given-names>Wolter</given-names></name><name><surname>Stoop</surname><given-names>Hans</given-names></name><name><surname>van Kessel</surname><given-names>Kim E.</given-names></name><name><surname>Zwarthoff</surname><given-names>Ellen C.</given-names></name><name><surname>Calatozzolo</surname><given-names>Chiara</given-names></name><name><surname>Cuppini</surname><given-names>Lucia</given-names></name><name><surname>Cuzzubbo</surname><given-names>Stefania</given-names></name><name><surname>DiMeco</surname><given-names>Francesco</given-names></name><name><surname>Finocchiaro</surname><given-names>Gaetano</given-names></name><name><surname>Mattei</surname><given-names>Luca</given-names></name><name><surname>Perin</surname><given-names>Alessandro</given-names></name><name><surname>Pollo</surname><given-names>Bianca</given-names></name><name><surname>Chen</surname><given-names>Chu</given-names></name><name><surname>Houck</surname><given-names>John</given-names></name><name><surname>Lohavanichbutr</surname><given-names>Pawadee</given-names></name><name><surname>Hartmann</surname><given-names>Arndt</given-names></name><name><surname>Stoehr</surname><given-names>Christine</given-names></name><name><surname>Stoehr</surname><given-names>Robert</given-names></name><name><surname>Taubert</surname><given-names>Helge</given-names></name><name><surname>Wach</surname><given-names>Sven</given-names></name><name><surname>Wullich</surname><given-names>Bernd</given-names></name><name><surname>Kycler</surname><given-names>Witold</given-names></name><name><surname>Murawa</surname><given-names>Dawid</given-names></name><name><surname>Wiznerowicz</surname><given-names>Maciej</given-names></name><name><surname>Chung</surname><given-names>Ki</given-names></name><name><surname>Edenfield</surname><given-names>W. Jeffrey</given-names></name><name><surname>Martin</surname><given-names>Julie</given-names></name><name><surname>Baudin</surname><given-names>Eric</given-names></name><name><surname>Bubley</surname><given-names>Glenn</given-names></name><name><surname>Bueno</surname><given-names>Raphael</given-names></name><name><surname>De Rienzo</surname><given-names>Assunta</given-names></name><name><surname>Richards</surname><given-names>William G.</given-names></name><name><surname>Kalkanis</surname><given-names>Steven</given-names></name><name><surname>Mikkelsen</surname><given-names>Tom</given-names></name><name><surname>Noushmehr</surname><given-names>Houtan</given-names></name><name><surname>Scarpace</surname><given-names>Lisa</given-names></name><name><surname>Girard</surname><given-names>Nicolas</given-names></name><name><surname>Aymerich</surname><given-names>Marta</given-names></name><name><surname>Campo</surname><given-names>Elias</given-names></name><name><surname>Gin&#x000e9;</surname><given-names>Eva</given-names></name><name><surname>Guillermo</surname><given-names>Armando L&#x000f3;pez</given-names></name><name><surname>Van Bang</surname><given-names>Nguyen</given-names></name><name><surname>Hanh</surname><given-names>Phan Thi</given-names></name><name><surname>Phu</surname><given-names>Bui Duc</given-names></name><name><surname>Tang</surname><given-names>Yufang</given-names></name><name><surname>Colman</surname><given-names>Howard</given-names></name><name><surname>Evason</surname><given-names>Kimberley</given-names></name><name><surname>Dottino</surname><given-names>Peter R.</given-names></name><name><surname>Martignetti</surname><given-names>John A.</given-names></name><name><surname>Gabra</surname><given-names>Hani</given-names></name><name><surname>Juhl</surname><given-names>Hartmut</given-names></name><name><surname>Akeredolu</surname><given-names>Teniola</given-names></name><name><surname>Stepa</surname><given-names>Serghei</given-names></name><name><surname>Hoon</surname><given-names>Dave</given-names></name><name><surname>Ahn</surname><given-names>Keunsoo</given-names></name><name><surname>Kang</surname><given-names>Koo Jeong</given-names></name><name><surname>Beuschlein</surname><given-names>Felix</given-names></name><name><surname>Breggia</surname><given-names>Anne</given-names></name><name><surname>Birrer</surname><given-names>Michael</given-names></name><name><surname>Bell</surname><given-names>Debra</given-names></name><name><surname>Borad</surname><given-names>Mitesh</given-names></name><name><surname>Bryce</surname><given-names>Alan H.</given-names></name><name><surname>Castle</surname><given-names>Erik</given-names></name><name><surname>Chandan</surname><given-names>Vishal</given-names></name><name><surname>Cheville</surname><given-names>John</given-names></name><name><surname>Copland</surname><given-names>John A.</given-names></name><name><surname>Farnell</surname><given-names>Michael</given-names></name><name><surname>Flotte</surname><given-names>Thomas</given-names></name><name><surname>Giama</surname><given-names>Nasra</given-names></name><name><surname>Ho</surname><given-names>Thai</given-names></name><name><surname>Kendrick</surname><given-names>Michael</given-names></name><name><surname>Kocher</surname><given-names>Jean-Pierre</given-names></name><name><surname>Kopp</surname><given-names>Karla</given-names></name><name><surname>Moser</surname><given-names>Catherine</given-names></name><name><surname>Nagorney</surname><given-names>David</given-names></name><name><surname>O&#x02019;Brien</surname><given-names>Daniel</given-names></name><name><surname>O&#x02019;Neill</surname><given-names>Brian Patrick</given-names></name><name><surname>Patel</surname><given-names>Tushar</given-names></name><name><surname>Petersen</surname><given-names>Gloria</given-names></name><name><surname>Que</surname><given-names>Florencia</given-names></name><name><surname>Rivera</surname><given-names>Michael</given-names></name><name><surname>Roberts</surname><given-names>Lewis</given-names></name><name><surname>Smallridge</surname><given-names>Robert</given-names></name><name><surname>Smyrk</surname><given-names>Thomas</given-names></name><name><surname>Stanton</surname><given-names>Melissa</given-names></name><name><surname>Thompson</surname><given-names>R. Houston</given-names></name><name><surname>Torbenson</surname><given-names>Michael</given-names></name><name><surname>Yang</surname><given-names>Ju Dong</given-names></name><name><surname>Zhang</surname><given-names>Lizhi</given-names></name><name><surname>Brimo</surname><given-names>Fadi</given-names></name><name><surname>Ajani</surname><given-names>Jaffer A.</given-names></name><name><surname>Gonzalez</surname><given-names>Ana Maria Angulo</given-names></name><name><surname>Behrens</surname><given-names>Carmen</given-names></name><name><surname>Bondaruk</surname><given-names>Jolanta</given-names></name><name><surname>Broaddus</surname><given-names>Russell</given-names></name><name><surname>Czerniak</surname><given-names>Bogdan</given-names></name><name><surname>Esmaeli</surname><given-names>Bita</given-names></name><name><surname>Fujimoto</surname><given-names>Junya</given-names></name><name><surname>Gershenwald</surname><given-names>Jeffrey</given-names></name><name><surname>Guo</surname><given-names>Charles</given-names></name><name><surname>Lazar</surname><given-names>Alexander J.</given-names></name><name><surname>Logothetis</surname><given-names>Christopher</given-names></name><name><surname>Meric-Bernstam</surname><given-names>Funda</given-names></name><name><surname>Moran</surname><given-names>Cesar</given-names></name><name><surname>Ramondetta</surname><given-names>Lois</given-names></name><name><surname>Rice</surname><given-names>David</given-names></name><name><surname>Sood</surname><given-names>Anil</given-names></name><name><surname>Tamboli</surname><given-names>Pheroze</given-names></name><name><surname>Thompson</surname><given-names>Timothy</given-names></name><name><surname>Troncoso</surname><given-names>Patricia</given-names></name><name><surname>Tsao</surname><given-names>Anne</given-names></name><name><surname>Wistuba</surname><given-names>Ignacio</given-names></name><name><surname>Carter</surname><given-names>Candace</given-names></name><name><surname>Haydu</surname><given-names>Lauren</given-names></name><name><surname>Hersey</surname><given-names>Peter</given-names></name><name><surname>Jakrot</surname><given-names>Valerie</given-names></name><name><surname>Kakavand</surname><given-names>Hojabr</given-names></name><name><surname>Kefford</surname><given-names>Richard</given-names></name><name><surname>Lee</surname><given-names>Kenneth</given-names></name><name><surname>Long</surname><given-names>Georgina</given-names></name><name><surname>Mann</surname><given-names>Graham</given-names></name><name><surname>Quinn</surname><given-names>Michael</given-names></name><name><surname>Saw</surname><given-names>Robyn</given-names></name><name><surname>Scolyer</surname><given-names>Richard</given-names></name><name><surname>Shannon</surname><given-names>Kerwin</given-names></name><name><surname>Spillane</surname><given-names>Andrew</given-names></name><name><surname>Stretch</surname><given-names>Jonathan</given-names></name><name><surname>Synott</surname><given-names>Maria</given-names></name><name><surname>Thompson</surname><given-names>John</given-names></name><name><surname>Wilmott</surname><given-names>James</given-names></name><name><surname>Al-Ahmadie</surname><given-names>Hikmat</given-names></name><name><surname>Chan</surname><given-names>Timothy A.</given-names></name><name><surname>Ghossein</surname><given-names>Ronald</given-names></name><name><surname>Gopalan</surname><given-names>Anuradha</given-names></name><name><surname>Levine</surname><given-names>Douglas A.</given-names></name><name><surname>Reuter</surname><given-names>Victor</given-names></name><name><surname>Singer</surname><given-names>Samuel</given-names></name><name><surname>Singh</surname><given-names>Bhuvanesh</given-names></name><name><surname>Tien</surname><given-names>Nguyen Viet</given-names></name><name><surname>Broudy</surname><given-names>Thomas</given-names></name><name><surname>Mirsaidi</surname><given-names>Cyrus</given-names></name><name><surname>Nair</surname><given-names>Praveen</given-names></name><name><surname>Drwiega</surname><given-names>Paul</given-names></name><name><surname>Miller</surname><given-names>Judy</given-names></name><name><surname>Smith</surname><given-names>Jennifer</given-names></name><name><surname>Zaren</surname><given-names>Howard</given-names></name><name><surname>Park</surname><given-names>Joong-Won</given-names></name><name><surname>Hung</surname><given-names>Nguyen Phi</given-names></name><name><surname>Kebebew</surname><given-names>Electron</given-names></name><name><surname>Linehan</surname><given-names>W. Marston</given-names></name><name><surname>Metwalli</surname><given-names>Adam R.</given-names></name><name><surname>Pacak</surname><given-names>Karel</given-names></name><name><surname>Pinto</surname><given-names>Peter A.</given-names></name><name><surname>Schiffman</surname><given-names>Mark</given-names></name><name><surname>Schmidt</surname><given-names>Laura S.</given-names></name><name><surname>Vocke</surname><given-names>Cathy D.</given-names></name><name><surname>Wentzensen</surname><given-names>Nicolas</given-names></name><name><surname>Worrell</surname><given-names>Robert</given-names></name><name><surname>Yang</surname><given-names>Hannah</given-names></name><name><surname>Moncrieff</surname><given-names>Marc</given-names></name><name><surname>Goparaju</surname><given-names>Chandra</given-names></name><name><surname>Melamed</surname><given-names>Jonathan</given-names></name><name><surname>Pass</surname><given-names>Harvey</given-names></name><name><surname>Botnariuc</surname><given-names>Natalia</given-names></name><name><surname>Caraman</surname><given-names>Irina</given-names></name><name><surname>Cernat</surname><given-names>Mircea</given-names></name><name><surname>Chemencedji</surname><given-names>Inga</given-names></name><name><surname>Clipca</surname><given-names>Adrian</given-names></name><name><surname>Doruc</surname><given-names>Serghei</given-names></name><name><surname>Gorincioi</surname><given-names>Ghenadie</given-names></name><name><surname>Mura</surname><given-names>Sergiu</given-names></name><name><surname>Pirtac</surname><given-names>Maria</given-names></name><name><surname>Stancul</surname><given-names>Irina</given-names></name><name><surname>Tcaciuc</surname><given-names>Diana</given-names></name><name><surname>Albert</surname><given-names>Monique</given-names></name><name><surname>Alexopoulou</surname><given-names>Iakovina</given-names></name><name><surname>Arnaout</surname><given-names>Angel</given-names></name><name><surname>Bartlett</surname><given-names>John</given-names></name><name><surname>Engel</surname><given-names>Jay</given-names></name><name><surname>Gilbert</surname><given-names>Sebastien</given-names></name><name><surname>Parfitt</surname><given-names>Jeremy</given-names></name><name><surname>Sekhon</surname><given-names>Harman</given-names></name><name><surname>Thomas</surname><given-names>George</given-names></name><name><surname>Rassl</surname><given-names>Doris M.</given-names></name><name><surname>Rintoul</surname><given-names>Robert C.</given-names></name><name><surname>Bifulco</surname><given-names>Carlo</given-names></name><name><surname>Tamakawa</surname><given-names>Raina</given-names></name><name><surname>Urba</surname><given-names>Walter</given-names></name><name><surname>Hayward</surname><given-names>Nicholas</given-names></name><name><surname>Timmers</surname><given-names>Henri</given-names></name><name><surname>Antenucci</surname><given-names>Anna</given-names></name><name><surname>Facciolo</surname><given-names>Francesco</given-names></name><name><surname>Grazi</surname><given-names>Gianluca</given-names></name><name><surname>Marino</surname><given-names>Mirella</given-names></name><name><surname>Merola</surname><given-names>Roberta</given-names></name><name><surname>de Krijger</surname><given-names>Ronald</given-names></name><name><surname>Gimenez-Roqueplo</surname><given-names>Anne-Paule</given-names></name><name><surname>Pich&#x000e9;</surname><given-names>Alain</given-names></name><name><surname>Chevalier</surname><given-names>Simone</given-names></name><name><surname>McKercher</surname><given-names>Ginette</given-names></name><name><surname>Birsoy</surname><given-names>Kivanc</given-names></name><name><surname>Barnett</surname><given-names>Gene</given-names></name><name><surname>Brewer</surname><given-names>Cathy</given-names></name><name><surname>Farver</surname><given-names>Carol</given-names></name><name><surname>Naska</surname><given-names>Theresa</given-names></name><name><surname>Pennell</surname><given-names>Nathan A.</given-names></name><name><surname>Raymond</surname><given-names>Daniel</given-names></name><name><surname>Schilero</surname><given-names>Cathy</given-names></name><name><surname>Smolenski</surname><given-names>Kathy</given-names></name><name><surname>Williams</surname><given-names>Felicia</given-names></name><name><surname>Morrison</surname><given-names>Carl</given-names></name><name><surname>Borgia</surname><given-names>Jeffrey A.</given-names></name><name><surname>Liptay</surname><given-names>Michael J.</given-names></name><name><surname>Pool</surname><given-names>Mark</given-names></name><name><surname>Seder</surname><given-names>Christopher W.</given-names></name><name><surname>Junker</surname><given-names>Kerstin</given-names></name><name><surname>Omberg</surname><given-names>Larsson</given-names></name><name><surname>Dinkin</surname><given-names>Mikhail</given-names></name><name><surname>Manikhas</surname><given-names>George</given-names></name><name><surname>Alvaro</surname><given-names>Domenico</given-names></name><name><surname>Bragazzi</surname><given-names>Maria Consiglia</given-names></name><name><surname>Cardinale</surname><given-names>Vincenzo</given-names></name><name><surname>Carpino</surname><given-names>Guido</given-names></name><name><surname>Gaudio</surname><given-names>Eugenio</given-names></name><name><surname>Chesla</surname><given-names>David</given-names></name><name><surname>Cottingham</surname><given-names>Sandra</given-names></name><name><surname>Dubina</surname><given-names>Michael</given-names></name><name><surname>Moiseenko</surname><given-names>Fedor</given-names></name><name><surname>Dhanasekaran</surname><given-names>Renumathy</given-names></name><name><surname>Becker</surname><given-names>Karl-Friedrich</given-names></name><name><surname>Janssen</surname><given-names>Klaus-Peter</given-names></name><name><surname>Slotta-Huspenina</surname><given-names>Julia</given-names></name><name><surname>Abdel-Rahman</surname><given-names>Mohamed H.</given-names></name><name><surname>Aziz</surname><given-names>Dina</given-names></name><name><surname>Bell</surname><given-names>Sue</given-names></name><name><surname>Cebulla</surname><given-names>Colleen M.</given-names></name><name><surname>Davis</surname><given-names>Amy</given-names></name><name><surname>Duell</surname><given-names>Rebecca</given-names></name><name><surname>Elder</surname><given-names>J. Bradley</given-names></name><name><surname>Hilty</surname><given-names>Joe</given-names></name><name><surname>Kumar</surname><given-names>Bahavna</given-names></name><name><surname>Lang</surname><given-names>James</given-names></name><name><surname>Lehman</surname><given-names>Norman L.</given-names></name><name><surname>Mandt</surname><given-names>Randy</given-names></name><name><surname>Nguyen</surname><given-names>Phuong</given-names></name><name><surname>Pilarski</surname><given-names>Robert</given-names></name><name><surname>Rai</surname><given-names>Karan</given-names></name><name><surname>Schoenfield</surname><given-names>Lynn</given-names></name><name><surname>Senecal</surname><given-names>Kelly</given-names></name><name><surname>Wakely</surname><given-names>Paul</given-names></name><name><surname>Hansen</surname><given-names>Paul</given-names></name><name><surname>Lechan</surname><given-names>Ronald</given-names></name><name><surname>Powers</surname><given-names>James</given-names></name><name><surname>Tischler</surname><given-names>Arthur</given-names></name><name><surname>Grizzle</surname><given-names>William E.</given-names></name><name><surname>Sexton</surname><given-names>Katherine C.</given-names></name><name><surname>Kastl</surname><given-names>Alison</given-names></name><name><surname>Henderson</surname><given-names>Joel</given-names></name><name><surname>Porten</surname><given-names>Sima</given-names></name><name><surname>Waldmann</surname><given-names>Jens</given-names></name><name><surname>Fassnacht</surname><given-names>Martin</given-names></name><name><surname>Asa</surname><given-names>Sylvia L.</given-names></name><name><surname>Schadendorf</surname><given-names>Dirk</given-names></name><name><surname>Couce</surname><given-names>Marta</given-names></name><name><surname>Graefen</surname><given-names>Markus</given-names></name><name><surname>Huland</surname><given-names>Hartwig</given-names></name><name><surname>Sauter</surname><given-names>Guido</given-names></name><name><surname>Schlomm</surname><given-names>Thorsten</given-names></name><name><surname>Simon</surname><given-names>Ronald</given-names></name><name><surname>Tennstedt</surname><given-names>Pierre</given-names></name><name><surname>Olabode</surname><given-names>Oluwole</given-names></name><name><surname>Nelson</surname><given-names>Mark</given-names></name><name><surname>Bathe</surname><given-names>Oliver</given-names></name><name><surname>Carroll</surname><given-names>Peter R.</given-names></name><name><surname>Chan</surname><given-names>June M.</given-names></name><name><surname>Disaia</surname><given-names>Philip</given-names></name><name><surname>Glenn</surname><given-names>Pat</given-names></name><name><surname>Kelley</surname><given-names>Robin K.</given-names></name><name><surname>Landen</surname><given-names>Charles N.</given-names></name><name><surname>Phillips</surname><given-names>Joanna</given-names></name><name><surname>Prados</surname><given-names>Michael</given-names></name><name><surname>Simko</surname><given-names>Jeffry</given-names></name><name><surname>Smith-McCune</surname><given-names>Karen</given-names></name><name><surname>VandenBerg</surname><given-names>Scott</given-names></name><name><surname>Roggin</surname><given-names>Kevin</given-names></name><name><surname>Fehrenbach</surname><given-names>Ashley</given-names></name><name><surname>Kendler</surname><given-names>Ady</given-names></name><name><surname>Sifri</surname><given-names>Suzanne</given-names></name><name><surname>Steele</surname><given-names>Ruth</given-names></name><name><surname>Jimeno</surname><given-names>Antonio</given-names></name><name><surname>Carey</surname><given-names>Francis</given-names></name><name><surname>Forgie</surname><given-names>Ian</given-names></name><name><surname>Mannelli</surname><given-names>Massimo</given-names></name><name><surname>Carney</surname><given-names>Michael</given-names></name><name><surname>Hernandez</surname><given-names>Brenda</given-names></name><name><surname>Campos</surname><given-names>Benito</given-names></name><name><surname>Herold-Mende</surname><given-names>Christel</given-names></name><name><surname>Jungk</surname><given-names>Christin</given-names></name><name><surname>Unterberg</surname><given-names>Andreas</given-names></name><name><surname>von Deimling</surname><given-names>Andreas</given-names></name><name><surname>Bossler</surname><given-names>Aaron</given-names></name><name><surname>Galbraith</surname><given-names>Joseph</given-names></name><name><surname>Jacobus</surname><given-names>Laura</given-names></name><name><surname>Knudson</surname><given-names>Michael</given-names></name><name><surname>Knutson</surname><given-names>Tina</given-names></name><name><surname>Ma</surname><given-names>Deqin</given-names></name><name><surname>Milhem</surname><given-names>Mohammed</given-names></name><name><surname>Sigmund</surname><given-names>Rita</given-names></name><name><surname>Godwin</surname><given-names>Andrew K.</given-names></name><name><surname>Madan</surname><given-names>Rashna</given-names></name><name><surname>Rosenthal</surname><given-names>Howard G.</given-names></name><name><surname>Adebamowo</surname><given-names>Clement</given-names></name><name><surname>Adebamowo</surname><given-names>Sally N.</given-names></name><name><surname>Boussioutas</surname><given-names>Alex</given-names></name><name><surname>Beer</surname><given-names>David</given-names></name><name><surname>Giordano</surname><given-names>Thomas</given-names></name><name><surname>Mes-Masson</surname><given-names>Anne-Marie</given-names></name><name><surname>Saad</surname><given-names>Fred</given-names></name><name><surname>Bocklage</surname><given-names>Therese</given-names></name><name><surname>Landrum</surname><given-names>Lisa</given-names></name><name><surname>Mannel</surname><given-names>Robert</given-names></name><name><surname>Moore</surname><given-names>Kathleen</given-names></name><name><surname>Moxley</surname><given-names>Katherine</given-names></name><name><surname>Postier</surname><given-names>Russel</given-names></name><name><surname>Walker</surname><given-names>Joan</given-names></name><name><surname>Zuna</surname><given-names>Rosemary</given-names></name><name><surname>Feldman</surname><given-names>Michael</given-names></name><name><surname>Valdivieso</surname><given-names>Federico</given-names></name><name><surname>Dhir</surname><given-names>Rajiv</given-names></name><name><surname>Luketich</surname><given-names>James</given-names></name><name><surname>Pinero</surname><given-names>Edna M. Mora</given-names></name><name><surname>Quintero-Aguilo</surname><given-names>Mario</given-names></name><name><surname>Carlotti</surname><given-names>Carlos Gilberto</given-names></name><name><surname>Dos Santos</surname><given-names>Jose Sebasti&#x000e3;o</given-names></name><name><surname>Kemp</surname><given-names>Rafael</given-names></name><name><surname>Sankarankuty</surname><given-names>Ajith</given-names></name><name><surname>Tirapelli</surname><given-names>Daniela</given-names></name><name><surname>Catto</surname><given-names>James</given-names></name><name><surname>Agnew</surname><given-names>Kathy</given-names></name><name><surname>Swisher</surname><given-names>Elizabeth</given-names></name><name><surname>Creaney</surname><given-names>Jenette</given-names></name><name><surname>Robinson</surname><given-names>Bruce</given-names></name><name><surname>Shelley</surname><given-names>Carl Simon</given-names></name><name><surname>Godwin</surname><given-names>Eryn M.</given-names></name><name><surname>Kendall</surname><given-names>Sara</given-names></name><name><surname>Shipman</surname><given-names>Cassaundra</given-names></name><name><surname>Bradford</surname><given-names>Carol</given-names></name><name><surname>Carey</surname><given-names>Thomas</given-names></name><name><surname>Haddad</surname><given-names>Andrea</given-names></name><name><surname>Moyer</surname><given-names>Jeffey</given-names></name><name><surname>Peterson</surname><given-names>Lisa</given-names></name><name><surname>Prince</surname><given-names>Mark</given-names></name><name><surname>Rozek</surname><given-names>Laura</given-names></name><name><surname>Wolf</surname><given-names>Gregory</given-names></name><name><surname>Bowman</surname><given-names>Rayleen</given-names></name><name><surname>Fong</surname><given-names>Kwun M.</given-names></name><name><surname>Yang</surname><given-names>Ian</given-names></name><name><surname>Korst</surname><given-names>Robert</given-names></name><name><surname>Rathmell</surname><given-names>W. Kimryn</given-names></name><name><surname>Fantacone-Campbell</surname><given-names>J. Leigh</given-names></name><name><surname>Hooke</surname><given-names>Jeffrey A.</given-names></name><name><surname>Kovatich</surname><given-names>Albert J.</given-names></name><name><surname>Shriver</surname><given-names>Craig D.</given-names></name><name><surname>DiPersio</surname><given-names>John</given-names></name><name><surname>Drake</surname><given-names>Bettina</given-names></name><name><surname>Govindan</surname><given-names>Ramaswamy</given-names></name><name><surname>Heath</surname><given-names>Sharon</given-names></name><name><surname>Ley</surname><given-names>Timothy</given-names></name><name><surname>Van Tine</surname><given-names>Brian</given-names></name><name><surname>Westervelt</surname><given-names>Peter</given-names></name><name><surname>Rubin</surname><given-names>Mark A.</given-names></name><name><surname>Lee</surname><given-names>Jung Il</given-names></name><name><surname>Aredes</surname><given-names>Nat&#x000e1;lia D.</given-names></name><name><surname>Mariamidze</surname><given-names>Armaz</given-names></name></person-group><article-title>lncRNA Epigenetic Landscape Analysis Identifies EPIC1 as an Oncogenic lncRNA that Interacts with MYC and Promotes Cell-Cycle Progression in Cancer</article-title><source>Cancer Cell</source><year>2018</year><volume>33</volume><issue>4</issue><fpage>706-720.e9</fpage><?supplied-pmid 29622465?><pub-id pub-id-type="pmid">29622465</pub-id></element-citation></ref><ref id="CR8"><label>8</label><mixed-citation publication-type="other">Zhang W, Yu Y, Hertwig F, Thierry-Mieg J, Zhang W, Thierry-Mieg D, Wang J, Furlanello C, Devanarayan V, Cheng J, Deng Y, Hero B, Hong H, Jia M, Li L, Lin SM, Nikolsky Y, Oberthuer A, Qing T, Su Z. Comparison of RNA-seq and microarray-based models for clinical endpoint prediction. Genome Biol. 2015; 16(1). 10.1186/s13059-015-0694-1.</mixed-citation></ref><ref id="CR9"><label>9</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Yu</surname><given-names>Kun-Hsing</given-names></name><name><surname>Levine</surname><given-names>Douglas A.</given-names></name><name><surname>Zhang</surname><given-names>Hui</given-names></name><name><surname>Chan</surname><given-names>Daniel W.</given-names></name><name><surname>Zhang</surname><given-names>Zhen</given-names></name><name><surname>Snyder</surname><given-names>Michael</given-names></name></person-group><article-title>Predicting Ovarian Cancer Patients&#x02019; Clinical Response to Platinum-Based Chemotherapy by Their Tumor Proteomic Signatures</article-title><source>Journal of Proteome Research</source><year>2016</year><volume>15</volume><issue>8</issue><fpage>2455</fpage><lpage>2465</lpage><pub-id pub-id-type="doi">10.1021/acs.jproteome.5b01129</pub-id><pub-id pub-id-type="pmid">27312948</pub-id></element-citation></ref><ref id="CR10"><label>10</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Berger</surname><given-names>Ashton C.</given-names></name><name><surname>Korkut</surname><given-names>Anil</given-names></name><name><surname>Kanchi</surname><given-names>Rupa S.</given-names></name><name><surname>Hegde</surname><given-names>Apurva M.</given-names></name><name><surname>Lenoir</surname><given-names>Walter</given-names></name><name><surname>Liu</surname><given-names>Wenbin</given-names></name><name><surname>Liu</surname><given-names>Yuexin</given-names></name><name><surname>Fan</surname><given-names>Huihui</given-names></name><name><surname>Shen</surname><given-names>Hui</given-names></name><name><surname>Ravikumar</surname><given-names>Visweswaran</given-names></name><name><surname>Rao</surname><given-names>Arvind</given-names></name><name><surname>Schultz</surname><given-names>Andre</given-names></name><name><surname>Li</surname><given-names>Xubin</given-names></name><name><surname>Sumazin</surname><given-names>Pavel</given-names></name><name><surname>Williams</surname><given-names>Cecilia</given-names></name><name><surname>Mestdagh</surname><given-names>Pieter</given-names></name><name><surname>Gunaratne</surname><given-names>Preethi H.</given-names></name><name><surname>Yau</surname><given-names>Christina</given-names></name><name><surname>Bowlby</surname><given-names>Reanne</given-names></name><name><surname>Robertson</surname><given-names>A. Gordon</given-names></name><name><surname>Tiezzi</surname><given-names>Daniel G.</given-names></name><name><surname>Wang</surname><given-names>Chen</given-names></name><name><surname>Cherniack</surname><given-names>Andrew D.</given-names></name><name><surname>Godwin</surname><given-names>Andrew K.</given-names></name><name><surname>Kuderer</surname><given-names>Nicole M.</given-names></name><name><surname>Rader</surname><given-names>Janet S.</given-names></name><name><surname>Zuna</surname><given-names>Rosemary E.</given-names></name><name><surname>Sood</surname><given-names>Anil K.</given-names></name><name><surname>Lazar</surname><given-names>Alexander J.</given-names></name><name><surname>Ojesina</surname><given-names>Akinyemi I.</given-names></name><name><surname>Adebamowo</surname><given-names>Clement</given-names></name><name><surname>Adebamowo</surname><given-names>Sally N.</given-names></name><name><surname>Baggerly</surname><given-names>Keith A.</given-names></name><name><surname>Chen</surname><given-names>Ting-Wen</given-names></name><name><surname>Chiu</surname><given-names>Hua-Sheng</given-names></name><name><surname>Lefever</surname><given-names>Steve</given-names></name><name><surname>Liu</surname><given-names>Liang</given-names></name><name><surname>MacKenzie</surname><given-names>Karen</given-names></name><name><surname>Orsulic</surname><given-names>Sandra</given-names></name><name><surname>Roszik</surname><given-names>Jason</given-names></name><name><surname>Shelley</surname><given-names>Carl Simon</given-names></name><name><surname>Song</surname><given-names>Qianqian</given-names></name><name><surname>Vellano</surname><given-names>Christopher P.</given-names></name><name><surname>Wentzensen</surname><given-names>Nicolas</given-names></name><name><surname>Weinstein</surname><given-names>John N.</given-names></name><name><surname>Mills</surname><given-names>Gordon B.</given-names></name><name><surname>Levine</surname><given-names>Douglas A.</given-names></name><name><surname>Akbani</surname><given-names>Rehan</given-names></name><name><surname>Caesar-Johnson</surname><given-names>Samantha J.</given-names></name><name><surname>Demchok</surname><given-names>John A.</given-names></name><name><surname>Felau</surname><given-names>Ina</given-names></name><name><surname>Kasapi</surname><given-names>Melpomeni</given-names></name><name><surname>Ferguson</surname><given-names>Martin L.</given-names></name><name><surname>Hutter</surname><given-names>Carolyn M.</given-names></name><name><surname>Sofia</surname><given-names>Heidi J.</given-names></name><name><surname>Tarnuzzer</surname><given-names>Roy</given-names></name><name><surname>Wang</surname><given-names>Zhining</given-names></name><name><surname>Yang</surname><given-names>Liming</given-names></name><name><surname>Zenklusen</surname><given-names>Jean C.</given-names></name><name><surname>Zhang</surname><given-names>Jiashan (Julia)</given-names></name><name><surname>Chudamani</surname><given-names>Sudha</given-names></name><name><surname>Liu</surname><given-names>Jia</given-names></name><name><surname>Lolla</surname><given-names>Laxmi</given-names></name><name><surname>Naresh</surname><given-names>Rashi</given-names></name><name><surname>Pihl</surname><given-names>Todd</given-names></name><name><surname>Sun</surname><given-names>Qiang</given-names></name><name><surname>Wan</surname><given-names>Yunhu</given-names></name><name><surname>Wu</surname><given-names>Ye</given-names></name><name><surname>Cho</surname><given-names>Juok</given-names></name><name><surname>DeFreitas</surname><given-names>Timothy</given-names></name><name><surname>Frazer</surname><given-names>Scott</given-names></name><name><surname>Gehlenborg</surname><given-names>Nils</given-names></name><name><surname>Getz</surname><given-names>Gad</given-names></name><name><surname>Heiman</surname><given-names>David I.</given-names></name><name><surname>Kim</surname><given-names>Jaegil</given-names></name><name><surname>Lawrence</surname><given-names>Michael S.</given-names></name><name><surname>Lin</surname><given-names>Pei</given-names></name><name><surname>Meier</surname><given-names>Sam</given-names></name><name><surname>Noble</surname><given-names>Michael S.</given-names></name><name><surname>Saksena</surname><given-names>Gordon</given-names></name><name><surname>Voet</surname><given-names>Doug</given-names></name><name><surname>Zhang</surname><given-names>Hailei</given-names></name><name><surname>Bernard</surname><given-names>Brady</given-names></name><name><surname>Chambwe</surname><given-names>Nyasha</given-names></name><name><surname>Dhankani</surname><given-names>Varsha</given-names></name><name><surname>Knijnenburg</surname><given-names>Theo</given-names></name><name><surname>Kramer</surname><given-names>Roger</given-names></name><name><surname>Leinonen</surname><given-names>Kalle</given-names></name><name><surname>Liu</surname><given-names>Yuexin</given-names></name><name><surname>Miller</surname><given-names>Michael</given-names></name><name><surname>Reynolds</surname><given-names>Sheila</given-names></name><name><surname>Shmulevich</surname><given-names>Ilya</given-names></name><name><surname>Thorsson</surname><given-names>Vesteinn</given-names></name><name><surname>Zhang</surname><given-names>Wei</given-names></name><name><surname>Akbani</surname><given-names>Rehan</given-names></name><name><surname>Broom</surname><given-names>Bradley M.</given-names></name><name><surname>Hegde</surname><given-names>Apurva M.</given-names></name><name><surname>Ju</surname><given-names>Zhenlin</given-names></name><name><surname>Kanchi</surname><given-names>Rupa S.</given-names></name><name><surname>Korkut</surname><given-names>Anil</given-names></name><name><surname>Li</surname><given-names>Jun</given-names></name><name><surname>Liang</surname><given-names>Han</given-names></name><name><surname>Ling</surname><given-names>Shiyun</given-names></name><name><surname>Liu</surname><given-names>Wenbin</given-names></name><name><surname>Lu</surname><given-names>Yiling</given-names></name><name><surname>Mills</surname><given-names>Gordon B.</given-names></name><name><surname>Ng</surname><given-names>Kwok-Shing</given-names></name><name><surname>Rao</surname><given-names>Arvind</given-names></name><name><surname>Ryan</surname><given-names>Michael</given-names></name><name><surname>Wang</surname><given-names>Jing</given-names></name><name><surname>Weinstein</surname><given-names>John N.</given-names></name><name><surname>Zhang</surname><given-names>Jiexin</given-names></name><name><surname>Abeshouse</surname><given-names>Adam</given-names></name><name><surname>Armenia</surname><given-names>Joshua</given-names></name><name><surname>Chakravarty</surname><given-names>Debyani</given-names></name><name><surname>Chatila</surname><given-names>Walid K.</given-names></name><name><surname>de Bruijn</surname><given-names>Ino</given-names></name><name><surname>Gao</surname><given-names>Jianjiong</given-names></name><name><surname>Gross</surname><given-names>Benjamin E.</given-names></name><name><surname>Heins</surname><given-names>Zachary J.</given-names></name><name><surname>Kundra</surname><given-names>Ritika</given-names></name><name><surname>La</surname><given-names>Konnor</given-names></name><name><surname>Ladanyi</surname><given-names>Marc</given-names></name><name><surname>Luna</surname><given-names>Augustin</given-names></name><name><surname>Nissan</surname><given-names>Moriah G.</given-names></name><name><surname>Ochoa</surname><given-names>Angelica</given-names></name><name><surname>Phillips</surname><given-names>Sarah M.</given-names></name><name><surname>Reznik</surname><given-names>Ed</given-names></name><name><surname>Sanchez-Vega</surname><given-names>Francisco</given-names></name><name><surname>Sander</surname><given-names>Chris</given-names></name><name><surname>Schultz</surname><given-names>Nikolaus</given-names></name><name><surname>Sheridan</surname><given-names>Robert</given-names></name><name><surname>Sumer</surname><given-names>S. Onur</given-names></name><name><surname>Sun</surname><given-names>Yichao</given-names></name><name><surname>Taylor</surname><given-names>Barry S.</given-names></name><name><surname>Wang</surname><given-names>Jioajiao</given-names></name><name><surname>Zhang</surname><given-names>Hongxin</given-names></name><name><surname>Anur</surname><given-names>Pavana</given-names></name><name><surname>Peto</surname><given-names>Myron</given-names></name><name><surname>Spellman</surname><given-names>Paul</given-names></name><name><surname>Benz</surname><given-names>Christopher</given-names></name><name><surname>Stuart</surname><given-names>Joshua M.</given-names></name><name><surname>Wong</surname><given-names>Christopher K.</given-names></name><name><surname>Yau</surname><given-names>Christina</given-names></name><name><surname>Hayes</surname><given-names>D. Neil</given-names></name><name><surname>Parker</surname><given-names>Joel S.</given-names></name><name><surname>Wilkerson</surname><given-names>Matthew D.</given-names></name><name><surname>Ally</surname><given-names>Adrian</given-names></name><name><surname>Balasundaram</surname><given-names>Miruna</given-names></name><name><surname>Bowlby</surname><given-names>Reanne</given-names></name><name><surname>Brooks</surname><given-names>Denise</given-names></name><name><surname>Carlsen</surname><given-names>Rebecca</given-names></name><name><surname>Chuah</surname><given-names>Eric</given-names></name><name><surname>Dhalla</surname><given-names>Noreen</given-names></name><name><surname>Holt</surname><given-names>Robert</given-names></name><name><surname>Jones</surname><given-names>Steven J.M.</given-names></name><name><surname>Kasaian</surname><given-names>Katayoon</given-names></name><name><surname>Lee</surname><given-names>Darlene</given-names></name><name><surname>Ma</surname><given-names>Yussanne</given-names></name><name><surname>Marra</surname><given-names>Marco A.</given-names></name><name><surname>Mayo</surname><given-names>Michael</given-names></name><name><surname>Moore</surname><given-names>Richard A.</given-names></name><name><surname>Mungall</surname><given-names>Andrew J.</given-names></name><name><surname>Mungall</surname><given-names>Karen</given-names></name><name><surname>Robertson</surname><given-names>A. Gordon</given-names></name><name><surname>Sadeghi</surname><given-names>Sara</given-names></name><name><surname>Schein</surname><given-names>Jacqueline E.</given-names></name><name><surname>Sipahimalani</surname><given-names>Payal</given-names></name><name><surname>Tam</surname><given-names>Angela</given-names></name><name><surname>Thiessen</surname><given-names>Nina</given-names></name><name><surname>Tse</surname><given-names>Kane</given-names></name><name><surname>Wong</surname><given-names>Tina</given-names></name><name><surname>Berger</surname><given-names>Ashton C.</given-names></name><name><surname>Beroukhim</surname><given-names>Rameen</given-names></name><name><surname>Cherniack</surname><given-names>Andrew D.</given-names></name><name><surname>Cibulskis</surname><given-names>Carrie</given-names></name><name><surname>Gabriel</surname><given-names>Stacey B.</given-names></name><name><surname>Gao</surname><given-names>Galen F.</given-names></name><name><surname>Ha</surname><given-names>Gavin</given-names></name><name><surname>Meyerson</surname><given-names>Matthew</given-names></name><name><surname>Schumacher</surname><given-names>Steven E.</given-names></name><name><surname>Shih</surname><given-names>Juliann</given-names></name><name><surname>Kucherlapati</surname><given-names>Melanie H.</given-names></name><name><surname>Kucherlapati</surname><given-names>Raju S.</given-names></name><name><surname>Baylin</surname><given-names>Stephen</given-names></name><name><surname>Cope</surname><given-names>Leslie</given-names></name><name><surname>Danilova</surname><given-names>Ludmila</given-names></name><name><surname>Bootwalla</surname><given-names>Moiz S.</given-names></name><name><surname>Lai</surname><given-names>Phillip H.</given-names></name><name><surname>Maglinte</surname><given-names>Dennis T.</given-names></name><name><surname>Van Den Berg</surname><given-names>David J.</given-names></name><name><surname>Weisenberger</surname><given-names>Daniel J.</given-names></name><name><surname>Auman</surname><given-names>J. Todd</given-names></name><name><surname>Balu</surname><given-names>Saianand</given-names></name><name><surname>Bodenheimer</surname><given-names>Tom</given-names></name><name><surname>Fan</surname><given-names>Cheng</given-names></name><name><surname>Hoadley</surname><given-names>Katherine A.</given-names></name><name><surname>Hoyle</surname><given-names>Alan P.</given-names></name><name><surname>Jefferys</surname><given-names>Stuart R.</given-names></name><name><surname>Jones</surname><given-names>Corbin D.</given-names></name><name><surname>Meng</surname><given-names>Shaowu</given-names></name><name><surname>Mieczkowski</surname><given-names>Piotr A.</given-names></name><name><surname>Mose</surname><given-names>Lisle E.</given-names></name><name><surname>Perou</surname><given-names>Amy H.</given-names></name><name><surname>Perou</surname><given-names>Charles M.</given-names></name><name><surname>Roach</surname><given-names>Jeffrey</given-names></name><name><surname>Shi</surname><given-names>Yan</given-names></name><name><surname>Simons</surname><given-names>Janae V.</given-names></name><name><surname>Skelly</surname><given-names>Tara</given-names></name><name><surname>Soloway</surname><given-names>Matthew G.</given-names></name><name><surname>Tan</surname><given-names>Donghui</given-names></name><name><surname>Veluvolu</surname><given-names>Umadevi</given-names></name><name><surname>Fan</surname><given-names>Huihui</given-names></name><name><surname>Hinoue</surname><given-names>Toshinori</given-names></name><name><surname>Laird</surname><given-names>Peter W.</given-names></name><name><surname>Shen</surname><given-names>Hui</given-names></name><name><surname>Zhou</surname><given-names>Wanding</given-names></name><name><surname>Bellair</surname><given-names>Michelle</given-names></name><name><surname>Chang</surname><given-names>Kyle</given-names></name><name><surname>Covington</surname><given-names>Kyle</given-names></name><name><surname>Creighton</surname><given-names>Chad J.</given-names></name><name><surname>Dinh</surname><given-names>Huyen</given-names></name><name><surname>Doddapaneni</surname><given-names>HarshaVardhan</given-names></name><name><surname>Donehower</surname><given-names>Lawrence A.</given-names></name><name><surname>Drummond</surname><given-names>Jennifer</given-names></name><name><surname>Gibbs</surname><given-names>Richard A.</given-names></name><name><surname>Glenn</surname><given-names>Robert</given-names></name><name><surname>Hale</surname><given-names>Walker</given-names></name><name><surname>Han</surname><given-names>Yi</given-names></name><name><surname>Hu</surname><given-names>Jianhong</given-names></name><name><surname>Korchina</surname><given-names>Viktoriya</given-names></name><name><surname>Lee</surname><given-names>Sandra</given-names></name><name><surname>Lewis</surname><given-names>Lora</given-names></name><name><surname>Li</surname><given-names>Wei</given-names></name><name><surname>Liu</surname><given-names>Xiuping</given-names></name><name><surname>Morgan</surname><given-names>Margaret</given-names></name><name><surname>Morton</surname><given-names>Donna</given-names></name><name><surname>Muzny</surname><given-names>Donna</given-names></name><name><surname>Santibanez</surname><given-names>Jireh</given-names></name><name><surname>Sheth</surname><given-names>Margi</given-names></name><name><surname>Shinbrot</surname><given-names>Eve</given-names></name><name><surname>Wang</surname><given-names>Linghua</given-names></name><name><surname>Wang</surname><given-names>Min</given-names></name><name><surname>Wheeler</surname><given-names>David A.</given-names></name><name><surname>Xi</surname><given-names>Liu</given-names></name><name><surname>Zhao</surname><given-names>Fengmei</given-names></name><name><surname>Hess</surname><given-names>Julian</given-names></name><name><surname>Appelbaum</surname><given-names>Elizabeth L.</given-names></name><name><surname>Bailey</surname><given-names>Matthew</given-names></name><name><surname>Cordes</surname><given-names>Matthew G.</given-names></name><name><surname>Ding</surname><given-names>Li</given-names></name><name><surname>Fronick</surname><given-names>Catrina C.</given-names></name><name><surname>Fulton</surname><given-names>Lucinda A.</given-names></name><name><surname>Fulton</surname><given-names>Robert S.</given-names></name><name><surname>Kandoth</surname><given-names>Cyriac</given-names></name><name><surname>Mardis</surname><given-names>Elaine R.</given-names></name><name><surname>McLellan</surname><given-names>Michael D.</given-names></name><name><surname>Miller</surname><given-names>Christopher A.</given-names></name><name><surname>Schmidt</surname><given-names>Heather K.</given-names></name><name><surname>Wilson</surname><given-names>Richard K.</given-names></name><name><surname>Crain</surname><given-names>Daniel</given-names></name><name><surname>Curley</surname><given-names>Erin</given-names></name><name><surname>Gardner</surname><given-names>Johanna</given-names></name><name><surname>Lau</surname><given-names>Kevin</given-names></name><name><surname>Mallery</surname><given-names>David</given-names></name><name><surname>Morris</surname><given-names>Scott</given-names></name><name><surname>Paulauskis</surname><given-names>Joseph</given-names></name><name><surname>Penny</surname><given-names>Robert</given-names></name><name><surname>Shelton</surname><given-names>Candace</given-names></name><name><surname>Shelton</surname><given-names>Troy</given-names></name><name><surname>Sherman</surname><given-names>Mark</given-names></name><name><surname>Thompson</surname><given-names>Eric</given-names></name><name><surname>Yena</surname><given-names>Peggy</given-names></name><name><surname>Bowen</surname><given-names>Jay</given-names></name><name><surname>Gastier-Foster</surname><given-names>Julie M.</given-names></name><name><surname>Gerken</surname><given-names>Mark</given-names></name><name><surname>Leraas</surname><given-names>Kristen M.</given-names></name><name><surname>Lichtenberg</surname><given-names>Tara M.</given-names></name><name><surname>Ramirez</surname><given-names>Nilsa C.</given-names></name><name><surname>Wise</surname><given-names>Lisa</given-names></name><name><surname>Zmuda</surname><given-names>Erik</given-names></name><name><surname>Corcoran</surname><given-names>Niall</given-names></name><name><surname>Costello</surname><given-names>Tony</given-names></name><name><surname>Hovens</surname><given-names>Christopher</given-names></name><name><surname>Carvalho</surname><given-names>Andre L.</given-names></name><name><surname>de Carvalho</surname><given-names>Ana C.</given-names></name><name><surname>Fregnani</surname><given-names>Jos&#x000e9; H.</given-names></name><name><surname>Longatto-Filho</surname><given-names>Adhemar</given-names></name><name><surname>Reis</surname><given-names>Rui M.</given-names></name><name><surname>Scapulatempo-Neto</surname><given-names>Cristovam</given-names></name><name><surname>Silveira</surname><given-names>Henrique C.S.</given-names></name><name><surname>Vidal</surname><given-names>Daniel O.</given-names></name><name><surname>Burnette</surname><given-names>Andrew</given-names></name><name><surname>Eschbacher</surname><given-names>Jennifer</given-names></name><name><surname>Hermes</surname><given-names>Beth</given-names></name><name><surname>Noss</surname><given-names>Ardene</given-names></name><name><surname>Singh</surname><given-names>Rosy</given-names></name><name><surname>Anderson</surname><given-names>Matthew L.</given-names></name><name><surname>Castro</surname><given-names>Patricia D.</given-names></name><name><surname>Ittmann</surname><given-names>Michael</given-names></name><name><surname>Huntsman</surname><given-names>David</given-names></name><name><surname>Kohl</surname><given-names>Bernard</given-names></name><name><surname>Le</surname><given-names>Xuan</given-names></name><name><surname>Thorp</surname><given-names>Richard</given-names></name><name><surname>Andry</surname><given-names>Chris</given-names></name><name><surname>Duffy</surname><given-names>Elizabeth R.</given-names></name><name><surname>Lyadov</surname><given-names>Vladimir</given-names></name><name><surname>Paklina</surname><given-names>Oxana</given-names></name><name><surname>Setdikova</surname><given-names>Galiya</given-names></name><name><surname>Shabunin</surname><given-names>Alexey</given-names></name><name><surname>Tavobilov</surname><given-names>Mikhail</given-names></name><name><surname>McPherson</surname><given-names>Christopher</given-names></name><name><surname>Warnick</surname><given-names>Ronald</given-names></name><name><surname>Berkowitz</surname><given-names>Ross</given-names></name><name><surname>Cramer</surname><given-names>Daniel</given-names></name><name><surname>Feltmate</surname><given-names>Colleen</given-names></name><name><surname>Horowitz</surname><given-names>Neil</given-names></name><name><surname>Kibel</surname><given-names>Adam</given-names></name><name><surname>Muto</surname><given-names>Michael</given-names></name><name><surname>Raut</surname><given-names>Chandrajit P.</given-names></name><name><surname>Malykh</surname><given-names>Andrei</given-names></name><name><surname>Barnholtz-Sloan</surname><given-names>Jill S.</given-names></name><name><surname>Barrett</surname><given-names>Wendi</given-names></name><name><surname>Devine</surname><given-names>Karen</given-names></name><name><surname>Fulop</surname><given-names>Jordonna</given-names></name><name><surname>Ostrom</surname><given-names>Quinn T.</given-names></name><name><surname>Shimmel</surname><given-names>Kristen</given-names></name><name><surname>Wolinsky</surname><given-names>Yingli</given-names></name><name><surname>Sloan</surname><given-names>Andrew E.</given-names></name><name><surname>De Rose</surname><given-names>Agostino</given-names></name><name><surname>Giuliante</surname><given-names>Felice</given-names></name><name><surname>Goodman</surname><given-names>Marc</given-names></name><name><surname>Karlan</surname><given-names>Beth Y.</given-names></name><name><surname>Hagedorn</surname><given-names>Curt H.</given-names></name><name><surname>Eckman</surname><given-names>John</given-names></name><name><surname>Harr</surname><given-names>Jodi</given-names></name><name><surname>Myers</surname><given-names>Jerome</given-names></name><name><surname>Tucker</surname><given-names>Kelinda</given-names></name><name><surname>Zach</surname><given-names>Leigh Anne</given-names></name><name><surname>Deyarmin</surname><given-names>Brenda</given-names></name><name><surname>Hu</surname><given-names>Hai</given-names></name><name><surname>Kvecher</surname><given-names>Leonid</given-names></name><name><surname>Larson</surname><given-names>Caroline</given-names></name><name><surname>Mural</surname><given-names>Richard J.</given-names></name><name><surname>Somiari</surname><given-names>Stella</given-names></name><name><surname>Vicha</surname><given-names>Ales</given-names></name><name><surname>Zelinka</surname><given-names>Tomas</given-names></name><name><surname>Bennett</surname><given-names>Joseph</given-names></name><name><surname>Iacocca</surname><given-names>Mary</given-names></name><name><surname>Rabeno</surname><given-names>Brenda</given-names></name><name><surname>Swanson</surname><given-names>Patricia</given-names></name><name><surname>Latour</surname><given-names>Mathieu</given-names></name><name><surname>Lacombe</surname><given-names>Louis</given-names></name><name><surname>T&#x000ea;tu</surname><given-names>Bernard</given-names></name><name><surname>Bergeron</surname><given-names>Alain</given-names></name><name><surname>McGraw</surname><given-names>Mary</given-names></name><name><surname>Staugaitis</surname><given-names>Susan M.</given-names></name><name><surname>Chabot</surname><given-names>John</given-names></name><name><surname>Hibshoosh</surname><given-names>Hanina</given-names></name><name><surname>Sepulveda</surname><given-names>Antonia</given-names></name><name><surname>Su</surname><given-names>Tao</given-names></name><name><surname>Wang</surname><given-names>Timothy</given-names></name><name><surname>Potapova</surname><given-names>Olga</given-names></name><name><surname>Voronina</surname><given-names>Olga</given-names></name><name><surname>Desjardins</surname><given-names>Laurence</given-names></name><name><surname>Mariani</surname><given-names>Odette</given-names></name><name><surname>Roman-Roman</surname><given-names>Sergio</given-names></name><name><surname>Sastre</surname><given-names>Xavier</given-names></name><name><surname>Stern</surname><given-names>Marc-Henri</given-names></name><name><surname>Cheng</surname><given-names>Feixiong</given-names></name><name><surname>Signoretti</surname><given-names>Sabina</given-names></name><name><surname>Berchuck</surname><given-names>Andrew</given-names></name><name><surname>Bigner</surname><given-names>Darell</given-names></name><name><surname>Lipp</surname><given-names>Eric</given-names></name><name><surname>Marks</surname><given-names>Jeffrey</given-names></name><name><surname>McCall</surname><given-names>Shannon</given-names></name><name><surname>McLendon</surname><given-names>Roger</given-names></name><name><surname>Secord</surname><given-names>Angeles</given-names></name><name><surname>Sharp</surname><given-names>Alexis</given-names></name><name><surname>Behera</surname><given-names>Madhusmita</given-names></name><name><surname>Brat</surname><given-names>Daniel J.</given-names></name><name><surname>Chen</surname><given-names>Amy</given-names></name><name><surname>Delman</surname><given-names>Keith</given-names></name><name><surname>Force</surname><given-names>Seth</given-names></name><name><surname>Khuri</surname><given-names>Fadlo</given-names></name><name><surname>Magliocca</surname><given-names>Kelly</given-names></name><name><surname>Maithel</surname><given-names>Shishir</given-names></name><name><surname>Olson</surname><given-names>Jeffrey J.</given-names></name><name><surname>Owonikoko</surname><given-names>Taofeek</given-names></name><name><surname>Pickens</surname><given-names>Alan</given-names></name><name><surname>Ramalingam</surname><given-names>Suresh</given-names></name><name><surname>Shin</surname><given-names>Dong M.</given-names></name><name><surname>Sica</surname><given-names>Gabriel</given-names></name><name><surname>Van Meir</surname><given-names>Erwin G.</given-names></name><name><surname>Zhang</surname><given-names>Hongzheng</given-names></name><name><surname>Eijckenboom</surname><given-names>Wil</given-names></name><name><surname>Gillis</surname><given-names>Ad</given-names></name><name><surname>Korpershoek</surname><given-names>Esther</given-names></name><name><surname>Looijenga</surname><given-names>Leendert</given-names></name><name><surname>Oosterhuis</surname><given-names>Wolter</given-names></name><name><surname>Stoop</surname><given-names>Hans</given-names></name><name><surname>van Kessel</surname><given-names>Kim E.</given-names></name><name><surname>Zwarthoff</surname><given-names>Ellen C.</given-names></name><name><surname>Calatozzolo</surname><given-names>Chiara</given-names></name><name><surname>Cuppini</surname><given-names>Lucia</given-names></name><name><surname>Cuzzubbo</surname><given-names>Stefania</given-names></name><name><surname>DiMeco</surname><given-names>Francesco</given-names></name><name><surname>Finocchiaro</surname><given-names>Gaetano</given-names></name><name><surname>Mattei</surname><given-names>Luca</given-names></name><name><surname>Perin</surname><given-names>Alessandro</given-names></name><name><surname>Pollo</surname><given-names>Bianca</given-names></name><name><surname>Chen</surname><given-names>Chu</given-names></name><name><surname>Houck</surname><given-names>John</given-names></name><name><surname>Lohavanichbutr</surname><given-names>Pawadee</given-names></name><name><surname>Hartmann</surname><given-names>Arndt</given-names></name><name><surname>Stoehr</surname><given-names>Christine</given-names></name><name><surname>Stoehr</surname><given-names>Robert</given-names></name><name><surname>Taubert</surname><given-names>Helge</given-names></name><name><surname>Wach</surname><given-names>Sven</given-names></name><name><surname>Wullich</surname><given-names>Bernd</given-names></name><name><surname>Kycler</surname><given-names>Witold</given-names></name><name><surname>Murawa</surname><given-names>Dawid</given-names></name><name><surname>Wiznerowicz</surname><given-names>Maciej</given-names></name><name><surname>Chung</surname><given-names>Ki</given-names></name><name><surname>Edenfield</surname><given-names>W. Jeffrey</given-names></name><name><surname>Martin</surname><given-names>Julie</given-names></name><name><surname>Baudin</surname><given-names>Eric</given-names></name><name><surname>Bubley</surname><given-names>Glenn</given-names></name><name><surname>Bueno</surname><given-names>Raphael</given-names></name><name><surname>De Rienzo</surname><given-names>Assunta</given-names></name><name><surname>Richards</surname><given-names>William G.</given-names></name><name><surname>Kalkanis</surname><given-names>Steven</given-names></name><name><surname>Mikkelsen</surname><given-names>Tom</given-names></name><name><surname>Noushmehr</surname><given-names>Houtan</given-names></name><name><surname>Scarpace</surname><given-names>Lisa</given-names></name><name><surname>Girard</surname><given-names>Nicolas</given-names></name><name><surname>Aymerich</surname><given-names>Marta</given-names></name><name><surname>Campo</surname><given-names>Elias</given-names></name><name><surname>Gin&#x000e9;</surname><given-names>Eva</given-names></name><name><surname>Guillermo</surname><given-names>Armando L&#x000f3;pez</given-names></name><name><surname>Van Bang</surname><given-names>Nguyen</given-names></name><name><surname>Hanh</surname><given-names>Phan Thi</given-names></name><name><surname>Phu</surname><given-names>Bui Duc</given-names></name><name><surname>Tang</surname><given-names>Yufang</given-names></name><name><surname>Colman</surname><given-names>Howard</given-names></name><name><surname>Evason</surname><given-names>Kimberley</given-names></name><name><surname>Dottino</surname><given-names>Peter R.</given-names></name><name><surname>Martignetti</surname><given-names>John A.</given-names></name><name><surname>Gabra</surname><given-names>Hani</given-names></name><name><surname>Juhl</surname><given-names>Hartmut</given-names></name><name><surname>Akeredolu</surname><given-names>Teniola</given-names></name><name><surname>Stepa</surname><given-names>Serghei</given-names></name><name><surname>Hoon</surname><given-names>Dave</given-names></name><name><surname>Ahn</surname><given-names>Keunsoo</given-names></name><name><surname>Kang</surname><given-names>Koo Jeong</given-names></name><name><surname>Beuschlein</surname><given-names>Felix</given-names></name><name><surname>Breggia</surname><given-names>Anne</given-names></name><name><surname>Birrer</surname><given-names>Michael</given-names></name><name><surname>Bell</surname><given-names>Debra</given-names></name><name><surname>Borad</surname><given-names>Mitesh</given-names></name><name><surname>Bryce</surname><given-names>Alan H.</given-names></name><name><surname>Castle</surname><given-names>Erik</given-names></name><name><surname>Chandan</surname><given-names>Vishal</given-names></name><name><surname>Cheville</surname><given-names>John</given-names></name><name><surname>Copland</surname><given-names>John A.</given-names></name><name><surname>Farnell</surname><given-names>Michael</given-names></name><name><surname>Flotte</surname><given-names>Thomas</given-names></name><name><surname>Giama</surname><given-names>Nasra</given-names></name><name><surname>Ho</surname><given-names>Thai</given-names></name><name><surname>Kendrick</surname><given-names>Michael</given-names></name><name><surname>Kocher</surname><given-names>Jean-Pierre</given-names></name><name><surname>Kopp</surname><given-names>Karla</given-names></name><name><surname>Moser</surname><given-names>Catherine</given-names></name><name><surname>Nagorney</surname><given-names>David</given-names></name><name><surname>O&#x02019;Brien</surname><given-names>Daniel</given-names></name><name><surname>O&#x02019;Neill</surname><given-names>Brian Patrick</given-names></name><name><surname>Patel</surname><given-names>Tushar</given-names></name><name><surname>Petersen</surname><given-names>Gloria</given-names></name><name><surname>Que</surname><given-names>Florencia</given-names></name><name><surname>Rivera</surname><given-names>Michael</given-names></name><name><surname>Roberts</surname><given-names>Lewis</given-names></name><name><surname>Smallridge</surname><given-names>Robert</given-names></name><name><surname>Smyrk</surname><given-names>Thomas</given-names></name><name><surname>Stanton</surname><given-names>Melissa</given-names></name><name><surname>Thompson</surname><given-names>R. Houston</given-names></name><name><surname>Torbenson</surname><given-names>Michael</given-names></name><name><surname>Yang</surname><given-names>Ju Dong</given-names></name><name><surname>Zhang</surname><given-names>Lizhi</given-names></name><name><surname>Brimo</surname><given-names>Fadi</given-names></name><name><surname>Ajani</surname><given-names>Jaffer A.</given-names></name><name><surname>Angulo Gonzalez</surname><given-names>Ana Maria</given-names></name><name><surname>Behrens</surname><given-names>Carmen</given-names></name><name><surname>Bondaruk</surname><given-names>Jolanta</given-names></name><name><surname>Broaddus</surname><given-names>Russell</given-names></name><name><surname>Czerniak</surname><given-names>Bogdan</given-names></name><name><surname>Esmaeli</surname><given-names>Bita</given-names></name><name><surname>Fujimoto</surname><given-names>Junya</given-names></name><name><surname>Gershenwald</surname><given-names>Jeffrey</given-names></name><name><surname>Guo</surname><given-names>Charles</given-names></name><name><surname>Lazar</surname><given-names>Alexander J.</given-names></name><name><surname>Logothetis</surname><given-names>Christopher</given-names></name><name><surname>Meric-Bernstam</surname><given-names>Funda</given-names></name><name><surname>Moran</surname><given-names>Cesar</given-names></name><name><surname>Ramondetta</surname><given-names>Lois</given-names></name><name><surname>Rice</surname><given-names>David</given-names></name><name><surname>Sood</surname><given-names>Anil</given-names></name><name><surname>Tamboli</surname><given-names>Pheroze</given-names></name><name><surname>Thompson</surname><given-names>Timothy</given-names></name><name><surname>Troncoso</surname><given-names>Patricia</given-names></name><name><surname>Tsao</surname><given-names>Anne</given-names></name><name><surname>Wistuba</surname><given-names>Ignacio</given-names></name><name><surname>Carter</surname><given-names>Candace</given-names></name><name><surname>Haydu</surname><given-names>Lauren</given-names></name><name><surname>Hersey</surname><given-names>Peter</given-names></name><name><surname>Jakrot</surname><given-names>Valerie</given-names></name><name><surname>Kakavand</surname><given-names>Hojabr</given-names></name><name><surname>Kefford</surname><given-names>Richard</given-names></name><name><surname>Lee</surname><given-names>Kenneth</given-names></name><name><surname>Long</surname><given-names>Georgina</given-names></name><name><surname>Mann</surname><given-names>Graham</given-names></name><name><surname>Quinn</surname><given-names>Michael</given-names></name><name><surname>Saw</surname><given-names>Robyn</given-names></name><name><surname>Scolyer</surname><given-names>Richard</given-names></name><name><surname>Shannon</surname><given-names>Kerwin</given-names></name><name><surname>Spillane</surname><given-names>Andrew</given-names></name><name><surname>Stretch</surname><given-names>Jonathan</given-names></name><name><surname>Synott</surname><given-names>Maria</given-names></name><name><surname>Thompson</surname><given-names>John</given-names></name><name><surname>Wilmott</surname><given-names>James</given-names></name><name><surname>Al-Ahmadie</surname><given-names>Hikmat</given-names></name><name><surname>Chan</surname><given-names>Timothy A.</given-names></name><name><surname>Ghossein</surname><given-names>Ronald</given-names></name><name><surname>Gopalan</surname><given-names>Anuradha</given-names></name><name><surname>Levine</surname><given-names>Douglas A.</given-names></name><name><surname>Reuter</surname><given-names>Victor</given-names></name><name><surname>Singer</surname><given-names>Samuel</given-names></name><name><surname>Singh</surname><given-names>Bhuvanesh</given-names></name><name><surname>Tien</surname><given-names>Nguyen Viet</given-names></name><name><surname>Broudy</surname><given-names>Thomas</given-names></name><name><surname>Mirsaidi</surname><given-names>Cyrus</given-names></name><name><surname>Nair</surname><given-names>Praveen</given-names></name><name><surname>Drwiega</surname><given-names>Paul</given-names></name><name><surname>Miller</surname><given-names>Judy</given-names></name><name><surname>Smith</surname><given-names>Jennifer</given-names></name><name><surname>Zaren</surname><given-names>Howard</given-names></name><name><surname>Park</surname><given-names>Joong-Won</given-names></name><name><surname>Hung</surname><given-names>Nguyen Phi</given-names></name><name><surname>Kebebew</surname><given-names>Electron</given-names></name><name><surname>Linehan</surname><given-names>W. Marston</given-names></name><name><surname>Metwalli</surname><given-names>Adam R.</given-names></name><name><surname>Pacak</surname><given-names>Karel</given-names></name><name><surname>Pinto</surname><given-names>Peter A.</given-names></name><name><surname>Schiffman</surname><given-names>Mark</given-names></name><name><surname>Schmidt</surname><given-names>Laura S.</given-names></name><name><surname>Vocke</surname><given-names>Cathy D.</given-names></name><name><surname>Wentzensen</surname><given-names>Nicolas</given-names></name><name><surname>Worrell</surname><given-names>Robert</given-names></name><name><surname>Yang</surname><given-names>Hannah</given-names></name><name><surname>Moncrieff</surname><given-names>Marc</given-names></name><name><surname>Goparaju</surname><given-names>Chandra</given-names></name><name><surname>Melamed</surname><given-names>Jonathan</given-names></name><name><surname>Pass</surname><given-names>Harvey</given-names></name><name><surname>Botnariuc</surname><given-names>Natalia</given-names></name><name><surname>Caraman</surname><given-names>Irina</given-names></name><name><surname>Cernat</surname><given-names>Mircea</given-names></name><name><surname>Chemencedji</surname><given-names>Inga</given-names></name><name><surname>Clipca</surname><given-names>Adrian</given-names></name><name><surname>Doruc</surname><given-names>Serghei</given-names></name><name><surname>Gorincioi</surname><given-names>Ghenadie</given-names></name><name><surname>Mura</surname><given-names>Sergiu</given-names></name><name><surname>Pirtac</surname><given-names>Maria</given-names></name><name><surname>Stancul</surname><given-names>Irina</given-names></name><name><surname>Tcaciuc</surname><given-names>Diana</given-names></name><name><surname>Albert</surname><given-names>Monique</given-names></name><name><surname>Alexopoulou</surname><given-names>Iakovina</given-names></name><name><surname>Arnaout</surname><given-names>Angel</given-names></name><name><surname>Bartlett</surname><given-names>John</given-names></name><name><surname>Engel</surname><given-names>Jay</given-names></name><name><surname>Gilbert</surname><given-names>Sebastien</given-names></name><name><surname>Parfitt</surname><given-names>Jeremy</given-names></name><name><surname>Sekhon</surname><given-names>Harman</given-names></name><name><surname>Thomas</surname><given-names>George</given-names></name><name><surname>Rassl</surname><given-names>Doris M.</given-names></name><name><surname>Rintoul</surname><given-names>Robert C.</given-names></name><name><surname>Bifulco</surname><given-names>Carlo</given-names></name><name><surname>Tamakawa</surname><given-names>Raina</given-names></name><name><surname>Urba</surname><given-names>Walter</given-names></name><name><surname>Hayward</surname><given-names>Nicholas</given-names></name><name><surname>Timmers</surname><given-names>Henri</given-names></name><name><surname>Antenucci</surname><given-names>Anna</given-names></name><name><surname>Facciolo</surname><given-names>Francesco</given-names></name><name><surname>Grazi</surname><given-names>Gianluca</given-names></name><name><surname>Marino</surname><given-names>Mirella</given-names></name><name><surname>Merola</surname><given-names>Roberta</given-names></name><name><surname>de Krijger</surname><given-names>Ronald</given-names></name><name><surname>Gimenez-Roqueplo</surname><given-names>Anne-Paule</given-names></name><name><surname>Pich&#x000e9;</surname><given-names>Alain</given-names></name><name><surname>Chevalier</surname><given-names>Simone</given-names></name><name><surname>McKercher</surname><given-names>Ginette</given-names></name><name><surname>Birsoy</surname><given-names>Kivanc</given-names></name><name><surname>Barnett</surname><given-names>Gene</given-names></name><name><surname>Brewer</surname><given-names>Cathy</given-names></name><name><surname>Farver</surname><given-names>Carol</given-names></name><name><surname>Naska</surname><given-names>Theresa</given-names></name><name><surname>Pennell</surname><given-names>Nathan A.</given-names></name><name><surname>Raymond</surname><given-names>Daniel</given-names></name><name><surname>Schilero</surname><given-names>Cathy</given-names></name><name><surname>Smolenski</surname><given-names>Kathy</given-names></name><name><surname>Williams</surname><given-names>Felicia</given-names></name><name><surname>Morrison</surname><given-names>Carl</given-names></name><name><surname>Borgia</surname><given-names>Jeffrey A.</given-names></name><name><surname>Liptay</surname><given-names>Michael J.</given-names></name><name><surname>Pool</surname><given-names>Mark</given-names></name><name><surname>Seder</surname><given-names>Christopher W.</given-names></name><name><surname>Junker</surname><given-names>Kerstin</given-names></name><name><surname>Omberg</surname><given-names>Larsson</given-names></name><name><surname>Dinkin</surname><given-names>Mikhail</given-names></name><name><surname>Manikhas</surname><given-names>George</given-names></name><name><surname>Alvaro</surname><given-names>Domenico</given-names></name><name><surname>Bragazzi</surname><given-names>Maria Consiglia</given-names></name><name><surname>Cardinale</surname><given-names>Vincenzo</given-names></name><name><surname>Carpino</surname><given-names>Guido</given-names></name><name><surname>Gaudio</surname><given-names>Eugenio</given-names></name><name><surname>Chesla</surname><given-names>David</given-names></name><name><surname>Cottingham</surname><given-names>Sandra</given-names></name><name><surname>Dubina</surname><given-names>Michael</given-names></name><name><surname>Moiseenko</surname><given-names>Fedor</given-names></name><name><surname>Dhanasekaran</surname><given-names>Renumathy</given-names></name><name><surname>Becker</surname><given-names>Karl-Friedrich</given-names></name><name><surname>Janssen</surname><given-names>Klaus-Peter</given-names></name><name><surname>Slotta-Huspenina</surname><given-names>Julia</given-names></name><name><surname>Abdel-Rahman</surname><given-names>Mohamed H.</given-names></name><name><surname>Aziz</surname><given-names>Dina</given-names></name><name><surname>Bell</surname><given-names>Sue</given-names></name><name><surname>Cebulla</surname><given-names>Colleen M.</given-names></name><name><surname>Davis</surname><given-names>Amy</given-names></name><name><surname>Duell</surname><given-names>Rebecca</given-names></name><name><surname>Elder</surname><given-names>J. Bradley</given-names></name><name><surname>Hilty</surname><given-names>Joe</given-names></name><name><surname>Kumar</surname><given-names>Bahavna</given-names></name><name><surname>Lang</surname><given-names>James</given-names></name><name><surname>Lehman</surname><given-names>Norman L.</given-names></name><name><surname>Mandt</surname><given-names>Randy</given-names></name><name><surname>Nguyen</surname><given-names>Phuong</given-names></name><name><surname>Pilarski</surname><given-names>Robert</given-names></name><name><surname>Rai</surname><given-names>Karan</given-names></name><name><surname>Schoenfield</surname><given-names>Lynn</given-names></name><name><surname>Senecal</surname><given-names>Kelly</given-names></name><name><surname>Wakely</surname><given-names>Paul</given-names></name><name><surname>Hansen</surname><given-names>Paul</given-names></name><name><surname>Lechan</surname><given-names>Ronald</given-names></name><name><surname>Powers</surname><given-names>James</given-names></name><name><surname>Tischler</surname><given-names>Arthur</given-names></name><name><surname>Grizzle</surname><given-names>William E.</given-names></name><name><surname>Sexton</surname><given-names>Katherine C.</given-names></name><name><surname>Kastl</surname><given-names>Alison</given-names></name><name><surname>Henderson</surname><given-names>Joel</given-names></name><name><surname>Porten</surname><given-names>Sima</given-names></name><name><surname>Waldmann</surname><given-names>Jens</given-names></name><name><surname>Fassnacht</surname><given-names>Martin</given-names></name><name><surname>Asa</surname><given-names>Sylvia L.</given-names></name><name><surname>Schadendorf</surname><given-names>Dirk</given-names></name><name><surname>Couce</surname><given-names>Marta</given-names></name><name><surname>Graefen</surname><given-names>Markus</given-names></name><name><surname>Huland</surname><given-names>Hartwig</given-names></name><name><surname>Sauter</surname><given-names>Guido</given-names></name><name><surname>Schlomm</surname><given-names>Thorsten</given-names></name><name><surname>Simon</surname><given-names>Ronald</given-names></name><name><surname>Tennstedt</surname><given-names>Pierre</given-names></name><name><surname>Olabode</surname><given-names>Oluwole</given-names></name><name><surname>Nelson</surname><given-names>Mark</given-names></name><name><surname>Bathe</surname><given-names>Oliver</given-names></name><name><surname>Carroll</surname><given-names>Peter R.</given-names></name><name><surname>Chan</surname><given-names>June M.</given-names></name><name><surname>Disaia</surname><given-names>Philip</given-names></name><name><surname>Glenn</surname><given-names>Pat</given-names></name><name><surname>Kelley</surname><given-names>Robin K.</given-names></name><name><surname>Landen</surname><given-names>Charles N.</given-names></name><name><surname>Phillips</surname><given-names>Joanna</given-names></name><name><surname>Prados</surname><given-names>Michael</given-names></name><name><surname>Simko</surname><given-names>Jeffry</given-names></name><name><surname>Smith-McCune</surname><given-names>Karen</given-names></name><name><surname>VandenBerg</surname><given-names>Scott</given-names></name><name><surname>Roggin</surname><given-names>Kevin</given-names></name><name><surname>Fehrenbach</surname><given-names>Ashley</given-names></name><name><surname>Kendler</surname><given-names>Ady</given-names></name><name><surname>Sifri</surname><given-names>Suzanne</given-names></name><name><surname>Steele</surname><given-names>Ruth</given-names></name><name><surname>Jimeno</surname><given-names>Antonio</given-names></name><name><surname>Carey</surname><given-names>Francis</given-names></name><name><surname>Forgie</surname><given-names>Ian</given-names></name><name><surname>Mannelli</surname><given-names>Massimo</given-names></name><name><surname>Carney</surname><given-names>Michael</given-names></name><name><surname>Hernandez</surname><given-names>Brenda</given-names></name><name><surname>Campos</surname><given-names>Benito</given-names></name><name><surname>Herold-Mende</surname><given-names>Christel</given-names></name><name><surname>Jungk</surname><given-names>Christin</given-names></name><name><surname>Unterberg</surname><given-names>Andreas</given-names></name><name><surname>von Deimling</surname><given-names>Andreas</given-names></name><name><surname>Bossler</surname><given-names>Aaron</given-names></name><name><surname>Galbraith</surname><given-names>Joseph</given-names></name><name><surname>Jacobus</surname><given-names>Laura</given-names></name><name><surname>Knudson</surname><given-names>Michael</given-names></name><name><surname>Knutson</surname><given-names>Tina</given-names></name><name><surname>Ma</surname><given-names>Deqin</given-names></name><name><surname>Milhem</surname><given-names>Mohammed</given-names></name><name><surname>Sigmund</surname><given-names>Rita</given-names></name><name><surname>Godwin</surname><given-names>Andrew K.</given-names></name><name><surname>Madan</surname><given-names>Rashna</given-names></name><name><surname>Rosenthal</surname><given-names>Howard G.</given-names></name><name><surname>Adebamowo</surname><given-names>Clement</given-names></name><name><surname>Adebamowo</surname><given-names>Sally N.</given-names></name><name><surname>Boussioutas</surname><given-names>Alex</given-names></name><name><surname>Beer</surname><given-names>David</given-names></name><name><surname>Giordano</surname><given-names>Thomas</given-names></name><name><surname>Mes-Masson</surname><given-names>Anne-Marie</given-names></name><name><surname>Saad</surname><given-names>Fred</given-names></name><name><surname>Bocklage</surname><given-names>Therese</given-names></name><name><surname>Landrum</surname><given-names>Lisa</given-names></name><name><surname>Mannel</surname><given-names>Robert</given-names></name><name><surname>Moore</surname><given-names>Kathleen</given-names></name><name><surname>Moxley</surname><given-names>Katherine</given-names></name><name><surname>Postier</surname><given-names>Russel</given-names></name><name><surname>Walker</surname><given-names>Joan</given-names></name><name><surname>Zuna</surname><given-names>Rosemary</given-names></name><name><surname>Feldman</surname><given-names>Michael</given-names></name><name><surname>Valdivieso</surname><given-names>Federico</given-names></name><name><surname>Dhir</surname><given-names>Rajiv</given-names></name><name><surname>Luketich</surname><given-names>James</given-names></name><name><surname>Mora Pinero</surname><given-names>Edna M.</given-names></name><name><surname>Quintero-Aguilo</surname><given-names>Mario</given-names></name><name><surname>Carlotti</surname><given-names>Carlos Gilberto</given-names></name><name><surname>Dos Santos</surname><given-names>Jose Sebasti&#x000e3;o</given-names></name><name><surname>Kemp</surname><given-names>Rafael</given-names></name><name><surname>Sankarankuty</surname><given-names>Ajith</given-names></name><name><surname>Tirapelli</surname><given-names>Daniela</given-names></name><name><surname>Catto</surname><given-names>James</given-names></name><name><surname>Agnew</surname><given-names>Kathy</given-names></name><name><surname>Swisher</surname><given-names>Elizabeth</given-names></name><name><surname>Creaney</surname><given-names>Jenette</given-names></name><name><surname>Robinson</surname><given-names>Bruce</given-names></name><name><surname>Shelley</surname><given-names>Carl Simon</given-names></name><name><surname>Godwin</surname><given-names>Eryn M.</given-names></name><name><surname>Kendall</surname><given-names>Sara</given-names></name><name><surname>Shipman</surname><given-names>Cassaundra</given-names></name><name><surname>Bradford</surname><given-names>Carol</given-names></name><name><surname>Carey</surname><given-names>Thomas</given-names></name><name><surname>Haddad</surname><given-names>Andrea</given-names></name><name><surname>Moyer</surname><given-names>Jeffey</given-names></name><name><surname>Peterson</surname><given-names>Lisa</given-names></name><name><surname>Prince</surname><given-names>Mark</given-names></name><name><surname>Rozek</surname><given-names>Laura</given-names></name><name><surname>Wolf</surname><given-names>Gregory</given-names></name><name><surname>Bowman</surname><given-names>Rayleen</given-names></name><name><surname>Fong</surname><given-names>Kwun M.</given-names></name><name><surname>Yang</surname><given-names>Ian</given-names></name><name><surname>Korst</surname><given-names>Robert</given-names></name><name><surname>Rathmell</surname><given-names>W. Kimryn</given-names></name><name><surname>Fantacone-Campbell</surname><given-names>J. Leigh</given-names></name><name><surname>Hooke</surname><given-names>Jeffrey A.</given-names></name><name><surname>Kovatich</surname><given-names>Albert J.</given-names></name><name><surname>Shriver</surname><given-names>Craig D.</given-names></name><name><surname>DiPersio</surname><given-names>John</given-names></name><name><surname>Drake</surname><given-names>Bettina</given-names></name><name><surname>Govindan</surname><given-names>Ramaswamy</given-names></name><name><surname>Heath</surname><given-names>Sharon</given-names></name><name><surname>Ley</surname><given-names>Timothy</given-names></name><name><surname>Van Tine</surname><given-names>Brian</given-names></name><name><surname>Westervelt</surname><given-names>Peter</given-names></name><name><surname>Rubin</surname><given-names>Mark A.</given-names></name><name><surname>Lee</surname><given-names>Jung Il</given-names></name><name><surname>Aredes</surname><given-names>Nat&#x000e1;lia D.</given-names></name><name><surname>Mariamidze</surname><given-names>Armaz</given-names></name></person-group><article-title>A Comprehensive Pan-Cancer Molecular Study of Gynecologic and Breast Cancers</article-title><source>Cancer Cell</source><year>2018</year><volume>33</volume><issue>4</issue><fpage>690-705.e9</fpage><pub-id pub-id-type="doi">10.1016/j.ccell.2018.03.014</pub-id><pub-id pub-id-type="pmid">29622464</pub-id></element-citation></ref><ref id="CR11"><label>11</label><element-citation publication-type="journal"><person-group person-group-type="author"><collab>The Cancer Genome Atlas Research Network</collab></person-group><article-title>Comprehensive, Integrative Genomic Analysis of Diffuse Lower-Grade Gliomas</article-title><source>N Engl J Med</source><year>2015</year><volume>372</volume><issue>26</issue><fpage>2481</fpage><lpage>98</lpage><pub-id pub-id-type="doi">10.1056/NEJMoa1402121</pub-id><pub-id pub-id-type="pmid">26061751</pub-id></element-citation></ref><ref id="CR12"><label>12</label><mixed-citation publication-type="other">Calvas P, Jamot L, Weinbach J, Chassaing N, RaDiCo Team T. The RaDiCo AC-OEIL : a french rare disease cohort dedicated to ocular developmental anomalies in children; 95. 10.1111/j.1755-3768.2017.02782.</mixed-citation></ref><ref id="CR13"><label>13</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>De Roach</surname><given-names>John N</given-names></name><name><surname>McLaren</surname><given-names>Terri L</given-names></name><name><surname>Paterson</surname><given-names>Rachel L</given-names></name><name><surname>O'Brien</surname><given-names>Emily C</given-names></name><name><surname>Hoffmann</surname><given-names>Ling</given-names></name><name><surname>Mackey</surname><given-names>David A</given-names></name><name><surname>Hewitt</surname><given-names>Alex W</given-names></name><name><surname>Lamey</surname><given-names>Tina M</given-names></name></person-group><article-title>Establishment and evolution of the Australian Inherited Retinal Disease Register and DNA Bank</article-title><source>Clinical &#x00026; Experimental Ophthalmology</source><year>2012</year><volume>41</volume><issue>5</issue><fpage>476</fpage><lpage>483</lpage><pub-id pub-id-type="doi">10.1111/ceo.12020</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR14"><label>14</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Firth</surname><given-names>Helen V.</given-names></name><name><surname>Richards</surname><given-names>Shola M.</given-names></name><name><surname>Bevan</surname><given-names>A. Paul</given-names></name><name><surname>Clayton</surname><given-names>Stephen</given-names></name><name><surname>Corpas</surname><given-names>Manuel</given-names></name><name><surname>Rajan</surname><given-names>Diana</given-names></name><name><surname>Vooren</surname><given-names>Steven Van</given-names></name><name><surname>Moreau</surname><given-names>Yves</given-names></name><name><surname>Pettett</surname><given-names>Roger M.</given-names></name><name><surname>Carter</surname><given-names>Nigel P.</given-names></name></person-group><article-title>DECIPHER: Database of Chromosomal Imbalance and Phenotype in Humans Using Ensembl Resources</article-title><source>The American Journal of Human Genetics</source><year>2009</year><volume>84</volume><issue>4</issue><fpage>524</fpage><lpage>533</lpage><pub-id pub-id-type="doi">10.1016/j.ajhg.2009.03.010</pub-id><pub-id pub-id-type="pmid">19344873</pub-id></element-citation></ref><ref id="CR15"><label>15</label><mixed-citation publication-type="other">Kursa MB. Robustness of random forest-based gene selection methods. BMC Bioinformatics; 15:8. 10.1186/1471-2105-15-8.</mixed-citation></ref><ref id="CR16"><label>16</label><mixed-citation publication-type="other">Francescatto M, Chierici M, Rezvan Dezfooli S, Zandon&#x000e0; A, Jurman G, Furlanello C. Multi-omics integration for neuroblastoma clinical endpoint prediction. Biol Direct; 13(1):5. 10.1186/s13062-018-0207-8.</mixed-citation></ref><ref id="CR17"><label>17</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Way</surname><given-names>Gregory P.</given-names></name><name><surname>Sanchez-Vega</surname><given-names>Francisco</given-names></name><name><surname>La</surname><given-names>Konnor</given-names></name><name><surname>Armenia</surname><given-names>Joshua</given-names></name><name><surname>Chatila</surname><given-names>Walid K.</given-names></name><name><surname>Luna</surname><given-names>Augustin</given-names></name><name><surname>Sander</surname><given-names>Chris</given-names></name><name><surname>Cherniack</surname><given-names>Andrew D.</given-names></name><name><surname>Mina</surname><given-names>Marco</given-names></name><name><surname>Ciriello</surname><given-names>Giovanni</given-names></name><name><surname>Schultz</surname><given-names>Nikolaus</given-names></name><name><surname>Sanchez</surname><given-names>Yolanda</given-names></name><name><surname>Greene</surname><given-names>Casey S.</given-names></name><name><surname>Caesar-Johnson</surname><given-names>Samantha J.</given-names></name><name><surname>Demchok</surname><given-names>John A.</given-names></name><name><surname>Felau</surname><given-names>Ina</given-names></name><name><surname>Kasapi</surname><given-names>Melpomeni</given-names></name><name><surname>Ferguson</surname><given-names>Martin L.</given-names></name><name><surname>Hutter</surname><given-names>Carolyn M.</given-names></name><name><surname>Sofia</surname><given-names>Heidi J.</given-names></name><name><surname>Tarnuzzer</surname><given-names>Roy</given-names></name><name><surname>Wang</surname><given-names>Zhining</given-names></name><name><surname>Yang</surname><given-names>Liming</given-names></name><name><surname>Zenklusen</surname><given-names>Jean C.</given-names></name><name><surname>Zhang</surname><given-names>Jiashan (Julia)</given-names></name><name><surname>Chudamani</surname><given-names>Sudha</given-names></name><name><surname>Liu</surname><given-names>Jia</given-names></name><name><surname>Lolla</surname><given-names>Laxmi</given-names></name><name><surname>Naresh</surname><given-names>Rashi</given-names></name><name><surname>Pihl</surname><given-names>Todd</given-names></name><name><surname>Sun</surname><given-names>Qiang</given-names></name><name><surname>Wan</surname><given-names>Yunhu</given-names></name><name><surname>Wu</surname><given-names>Ye</given-names></name><name><surname>Cho</surname><given-names>Juok</given-names></name><name><surname>DeFreitas</surname><given-names>Timothy</given-names></name><name><surname>Frazer</surname><given-names>Scott</given-names></name><name><surname>Gehlenborg</surname><given-names>Nils</given-names></name><name><surname>Getz</surname><given-names>Gad</given-names></name><name><surname>Heiman</surname><given-names>David I.</given-names></name><name><surname>Kim</surname><given-names>Jaegil</given-names></name><name><surname>Lawrence</surname><given-names>Michael S.</given-names></name><name><surname>Lin</surname><given-names>Pei</given-names></name><name><surname>Meier</surname><given-names>Sam</given-names></name><name><surname>Noble</surname><given-names>Michael S.</given-names></name><name><surname>Saksena</surname><given-names>Gordon</given-names></name><name><surname>Voet</surname><given-names>Doug</given-names></name><name><surname>Zhang</surname><given-names>Hailei</given-names></name><name><surname>Bernard</surname><given-names>Brady</given-names></name><name><surname>Chambwe</surname><given-names>Nyasha</given-names></name><name><surname>Dhankani</surname><given-names>Varsha</given-names></name><name><surname>Knijnenburg</surname><given-names>Theo</given-names></name><name><surname>Kramer</surname><given-names>Roger</given-names></name><name><surname>Leinonen</surname><given-names>Kalle</given-names></name><name><surname>Liu</surname><given-names>Yuexin</given-names></name><name><surname>Miller</surname><given-names>Michael</given-names></name><name><surname>Reynolds</surname><given-names>Sheila</given-names></name><name><surname>Shmulevich</surname><given-names>Ilya</given-names></name><name><surname>Thorsson</surname><given-names>Vesteinn</given-names></name><name><surname>Zhang</surname><given-names>Wei</given-names></name><name><surname>Akbani</surname><given-names>Rehan</given-names></name><name><surname>Broom</surname><given-names>Bradley M.</given-names></name><name><surname>Hegde</surname><given-names>Apurva M.</given-names></name><name><surname>Ju</surname><given-names>Zhenlin</given-names></name><name><surname>Kanchi</surname><given-names>Rupa S.</given-names></name><name><surname>Korkut</surname><given-names>Anil</given-names></name><name><surname>Li</surname><given-names>Jun</given-names></name><name><surname>Liang</surname><given-names>Han</given-names></name><name><surname>Ling</surname><given-names>Shiyun</given-names></name><name><surname>Liu</surname><given-names>Wenbin</given-names></name><name><surname>Lu</surname><given-names>Yiling</given-names></name><name><surname>Mills</surname><given-names>Gordon B.</given-names></name><name><surname>Ng</surname><given-names>Kwok-Shing</given-names></name><name><surname>Rao</surname><given-names>Arvind</given-names></name><name><surname>Ryan</surname><given-names>Michael</given-names></name><name><surname>Wang</surname><given-names>Jing</given-names></name><name><surname>Weinstein</surname><given-names>John N.</given-names></name><name><surname>Zhang</surname><given-names>Jiexin</given-names></name><name><surname>Abeshouse</surname><given-names>Adam</given-names></name><name><surname>Armenia</surname><given-names>Joshua</given-names></name><name><surname>Chakravarty</surname><given-names>Debyani</given-names></name><name><surname>Chatila</surname><given-names>Walid K.</given-names></name><name><surname>de Bruijn</surname><given-names>Ino</given-names></name><name><surname>Gao</surname><given-names>Jianjiong</given-names></name><name><surname>Gross</surname><given-names>Benjamin E.</given-names></name><name><surname>Heins</surname><given-names>Zachary J.</given-names></name><name><surname>Kundra</surname><given-names>Ritika</given-names></name><name><surname>La</surname><given-names>Konnor</given-names></name><name><surname>Ladanyi</surname><given-names>Marc</given-names></name><name><surname>Luna</surname><given-names>Augustin</given-names></name><name><surname>Nissan</surname><given-names>Moriah G.</given-names></name><name><surname>Ochoa</surname><given-names>Angelica</given-names></name><name><surname>Phillips</surname><given-names>Sarah M.</given-names></name><name><surname>Reznik</surname><given-names>Ed</given-names></name><name><surname>Sanchez-Vega</surname><given-names>Francisco</given-names></name><name><surname>Sander</surname><given-names>Chris</given-names></name><name><surname>Schultz</surname><given-names>Nikolaus</given-names></name><name><surname>Sheridan</surname><given-names>Robert</given-names></name><name><surname>Sumer</surname><given-names>S. Onur</given-names></name><name><surname>Sun</surname><given-names>Yichao</given-names></name><name><surname>Taylor</surname><given-names>Barry S.</given-names></name><name><surname>Wang</surname><given-names>Jioajiao</given-names></name><name><surname>Zhang</surname><given-names>Hongxin</given-names></name><name><surname>Anur</surname><given-names>Pavana</given-names></name><name><surname>Peto</surname><given-names>Myron</given-names></name><name><surname>Spellman</surname><given-names>Paul</given-names></name><name><surname>Benz</surname><given-names>Christopher</given-names></name><name><surname>Stuart</surname><given-names>Joshua M.</given-names></name><name><surname>Wong</surname><given-names>Christopher K.</given-names></name><name><surname>Yau</surname><given-names>Christina</given-names></name><name><surname>Hayes</surname><given-names>D. Neil</given-names></name><name><surname>Parker</surname><given-names>Joel S.</given-names></name><name><surname>Wilkerson</surname><given-names>Matthew D.</given-names></name><name><surname>Ally</surname><given-names>Adrian</given-names></name><name><surname>Balasundaram</surname><given-names>Miruna</given-names></name><name><surname>Bowlby</surname><given-names>Reanne</given-names></name><name><surname>Brooks</surname><given-names>Denise</given-names></name><name><surname>Carlsen</surname><given-names>Rebecca</given-names></name><name><surname>Chuah</surname><given-names>Eric</given-names></name><name><surname>Dhalla</surname><given-names>Noreen</given-names></name><name><surname>Holt</surname><given-names>Robert</given-names></name><name><surname>Jones</surname><given-names>Steven J.M.</given-names></name><name><surname>Kasaian</surname><given-names>Katayoon</given-names></name><name><surname>Lee</surname><given-names>Darlene</given-names></name><name><surname>Ma</surname><given-names>Yussanne</given-names></name><name><surname>Marra</surname><given-names>Marco A.</given-names></name><name><surname>Mayo</surname><given-names>Michael</given-names></name><name><surname>Moore</surname><given-names>Richard A.</given-names></name><name><surname>Mungall</surname><given-names>Andrew J.</given-names></name><name><surname>Mungall</surname><given-names>Karen</given-names></name><name><surname>Robertson</surname><given-names>A. Gordon</given-names></name><name><surname>Sadeghi</surname><given-names>Sara</given-names></name><name><surname>Schein</surname><given-names>Jacqueline E.</given-names></name><name><surname>Sipahimalani</surname><given-names>Payal</given-names></name><name><surname>Tam</surname><given-names>Angela</given-names></name><name><surname>Thiessen</surname><given-names>Nina</given-names></name><name><surname>Tse</surname><given-names>Kane</given-names></name><name><surname>Wong</surname><given-names>Tina</given-names></name><name><surname>Berger</surname><given-names>Ashton C.</given-names></name><name><surname>Beroukhim</surname><given-names>Rameen</given-names></name><name><surname>Cherniack</surname><given-names>Andrew D.</given-names></name><name><surname>Cibulskis</surname><given-names>Carrie</given-names></name><name><surname>Gabriel</surname><given-names>Stacey B.</given-names></name><name><surname>Gao</surname><given-names>Galen F.</given-names></name><name><surname>Ha</surname><given-names>Gavin</given-names></name><name><surname>Meyerson</surname><given-names>Matthew</given-names></name><name><surname>Schumacher</surname><given-names>Steven E.</given-names></name><name><surname>Shih</surname><given-names>Juliann</given-names></name><name><surname>Kucherlapati</surname><given-names>Melanie H.</given-names></name><name><surname>Kucherlapati</surname><given-names>Raju S.</given-names></name><name><surname>Baylin</surname><given-names>Stephen</given-names></name><name><surname>Cope</surname><given-names>Leslie</given-names></name><name><surname>Danilova</surname><given-names>Ludmila</given-names></name><name><surname>Bootwalla</surname><given-names>Moiz S.</given-names></name><name><surname>Lai</surname><given-names>Phillip H.</given-names></name><name><surname>Maglinte</surname><given-names>Dennis T.</given-names></name><name><surname>Van Den Berg</surname><given-names>David J.</given-names></name><name><surname>Weisenberger</surname><given-names>Daniel J.</given-names></name><name><surname>Auman</surname><given-names>J. Todd</given-names></name><name><surname>Balu</surname><given-names>Saianand</given-names></name><name><surname>Bodenheimer</surname><given-names>Tom</given-names></name><name><surname>Fan</surname><given-names>Cheng</given-names></name><name><surname>Hoadley</surname><given-names>Katherine A.</given-names></name><name><surname>Hoyle</surname><given-names>Alan P.</given-names></name><name><surname>Jefferys</surname><given-names>Stuart R.</given-names></name><name><surname>Jones</surname><given-names>Corbin D.</given-names></name><name><surname>Meng</surname><given-names>Shaowu</given-names></name><name><surname>Mieczkowski</surname><given-names>Piotr A.</given-names></name><name><surname>Mose</surname><given-names>Lisle E.</given-names></name><name><surname>Perou</surname><given-names>Amy H.</given-names></name><name><surname>Perou</surname><given-names>Charles M.</given-names></name><name><surname>Roach</surname><given-names>Jeffrey</given-names></name><name><surname>Shi</surname><given-names>Yan</given-names></name><name><surname>Simons</surname><given-names>Janae V.</given-names></name><name><surname>Skelly</surname><given-names>Tara</given-names></name><name><surname>Soloway</surname><given-names>Matthew G.</given-names></name><name><surname>Tan</surname><given-names>Donghui</given-names></name><name><surname>Veluvolu</surname><given-names>Umadevi</given-names></name><name><surname>Fan</surname><given-names>Huihui</given-names></name><name><surname>Hinoue</surname><given-names>Toshinori</given-names></name><name><surname>Laird</surname><given-names>Peter W.</given-names></name><name><surname>Shen</surname><given-names>Hui</given-names></name><name><surname>Zhou</surname><given-names>Wanding</given-names></name><name><surname>Bellair</surname><given-names>Michelle</given-names></name><name><surname>Chang</surname><given-names>Kyle</given-names></name><name><surname>Covington</surname><given-names>Kyle</given-names></name><name><surname>Creighton</surname><given-names>Chad J.</given-names></name><name><surname>Dinh</surname><given-names>Huyen</given-names></name><name><surname>Doddapaneni</surname><given-names>HarshaVardhan</given-names></name><name><surname>Donehower</surname><given-names>Lawrence A.</given-names></name><name><surname>Drummond</surname><given-names>Jennifer</given-names></name><name><surname>Gibbs</surname><given-names>Richard A.</given-names></name><name><surname>Glenn</surname><given-names>Robert</given-names></name><name><surname>Hale</surname><given-names>Walker</given-names></name><name><surname>Han</surname><given-names>Yi</given-names></name><name><surname>Hu</surname><given-names>Jianhong</given-names></name><name><surname>Korchina</surname><given-names>Viktoriya</given-names></name><name><surname>Lee</surname><given-names>Sandra</given-names></name><name><surname>Lewis</surname><given-names>Lora</given-names></name><name><surname>Li</surname><given-names>Wei</given-names></name><name><surname>Liu</surname><given-names>Xiuping</given-names></name><name><surname>Morgan</surname><given-names>Margaret</given-names></name><name><surname>Morton</surname><given-names>Donna</given-names></name><name><surname>Muzny</surname><given-names>Donna</given-names></name><name><surname>Santibanez</surname><given-names>Jireh</given-names></name><name><surname>Sheth</surname><given-names>Margi</given-names></name><name><surname>Shinbrot</surname><given-names>Eve</given-names></name><name><surname>Wang</surname><given-names>Linghua</given-names></name><name><surname>Wang</surname><given-names>Min</given-names></name><name><surname>Wheeler</surname><given-names>David A.</given-names></name><name><surname>Xi</surname><given-names>Liu</given-names></name><name><surname>Zhao</surname><given-names>Fengmei</given-names></name><name><surname>Hess</surname><given-names>Julian</given-names></name><name><surname>Appelbaum</surname><given-names>Elizabeth L.</given-names></name><name><surname>Bailey</surname><given-names>Matthew</given-names></name><name><surname>Cordes</surname><given-names>Matthew G.</given-names></name><name><surname>Ding</surname><given-names>Li</given-names></name><name><surname>Fronick</surname><given-names>Catrina C.</given-names></name><name><surname>Fulton</surname><given-names>Lucinda A.</given-names></name><name><surname>Fulton</surname><given-names>Robert S.</given-names></name><name><surname>Kandoth</surname><given-names>Cyriac</given-names></name><name><surname>Mardis</surname><given-names>Elaine R.</given-names></name><name><surname>McLellan</surname><given-names>Michael D.</given-names></name><name><surname>Miller</surname><given-names>Christopher A.</given-names></name><name><surname>Schmidt</surname><given-names>Heather K.</given-names></name><name><surname>Wilson</surname><given-names>Richard K.</given-names></name><name><surname>Crain</surname><given-names>Daniel</given-names></name><name><surname>Curley</surname><given-names>Erin</given-names></name><name><surname>Gardner</surname><given-names>Johanna</given-names></name><name><surname>Lau</surname><given-names>Kevin</given-names></name><name><surname>Mallery</surname><given-names>David</given-names></name><name><surname>Morris</surname><given-names>Scott</given-names></name><name><surname>Paulauskis</surname><given-names>Joseph</given-names></name><name><surname>Penny</surname><given-names>Robert</given-names></name><name><surname>Shelton</surname><given-names>Candace</given-names></name><name><surname>Shelton</surname><given-names>Troy</given-names></name><name><surname>Sherman</surname><given-names>Mark</given-names></name><name><surname>Thompson</surname><given-names>Eric</given-names></name><name><surname>Yena</surname><given-names>Peggy</given-names></name><name><surname>Bowen</surname><given-names>Jay</given-names></name><name><surname>Gastier-Foster</surname><given-names>Julie M.</given-names></name><name><surname>Gerken</surname><given-names>Mark</given-names></name><name><surname>Leraas</surname><given-names>Kristen M.</given-names></name><name><surname>Lichtenberg</surname><given-names>Tara M.</given-names></name><name><surname>Ramirez</surname><given-names>Nilsa C.</given-names></name><name><surname>Wise</surname><given-names>Lisa</given-names></name><name><surname>Zmuda</surname><given-names>Erik</given-names></name><name><surname>Corcoran</surname><given-names>Niall</given-names></name><name><surname>Costello</surname><given-names>Tony</given-names></name><name><surname>Hovens</surname><given-names>Christopher</given-names></name><name><surname>Carvalho</surname><given-names>Andre L.</given-names></name><name><surname>de Carvalho</surname><given-names>Ana C.</given-names></name><name><surname>Fregnani</surname><given-names>Jos&#x000e9; H.</given-names></name><name><surname>Longatto-Filho</surname><given-names>Adhemar</given-names></name><name><surname>Reis</surname><given-names>Rui M.</given-names></name><name><surname>Scapulatempo-Neto</surname><given-names>Cristovam</given-names></name><name><surname>Silveira</surname><given-names>Henrique C.S.</given-names></name><name><surname>Vidal</surname><given-names>Daniel O.</given-names></name><name><surname>Burnette</surname><given-names>Andrew</given-names></name><name><surname>Eschbacher</surname><given-names>Jennifer</given-names></name><name><surname>Hermes</surname><given-names>Beth</given-names></name><name><surname>Noss</surname><given-names>Ardene</given-names></name><name><surname>Singh</surname><given-names>Rosy</given-names></name><name><surname>Anderson</surname><given-names>Matthew L.</given-names></name><name><surname>Castro</surname><given-names>Patricia D.</given-names></name><name><surname>Ittmann</surname><given-names>Michael</given-names></name><name><surname>Huntsman</surname><given-names>David</given-names></name><name><surname>Kohl</surname><given-names>Bernard</given-names></name><name><surname>Le</surname><given-names>Xuan</given-names></name><name><surname>Thorp</surname><given-names>Richard</given-names></name><name><surname>Andry</surname><given-names>Chris</given-names></name><name><surname>Duffy</surname><given-names>Elizabeth R.</given-names></name><name><surname>Lyadov</surname><given-names>Vladimir</given-names></name><name><surname>Paklina</surname><given-names>Oxana</given-names></name><name><surname>Setdikova</surname><given-names>Galiya</given-names></name><name><surname>Shabunin</surname><given-names>Alexey</given-names></name><name><surname>Tavobilov</surname><given-names>Mikhail</given-names></name><name><surname>McPherson</surname><given-names>Christopher</given-names></name><name><surname>Warnick</surname><given-names>Ronald</given-names></name><name><surname>Berkowitz</surname><given-names>Ross</given-names></name><name><surname>Cramer</surname><given-names>Daniel</given-names></name><name><surname>Feltmate</surname><given-names>Colleen</given-names></name><name><surname>Horowitz</surname><given-names>Neil</given-names></name><name><surname>Kibel</surname><given-names>Adam</given-names></name><name><surname>Muto</surname><given-names>Michael</given-names></name><name><surname>Raut</surname><given-names>Chandrajit P.</given-names></name><name><surname>Malykh</surname><given-names>Andrei</given-names></name><name><surname>Barnholtz-Sloan</surname><given-names>Jill S.</given-names></name><name><surname>Barrett</surname><given-names>Wendi</given-names></name><name><surname>Devine</surname><given-names>Karen</given-names></name><name><surname>Fulop</surname><given-names>Jordonna</given-names></name><name><surname>Ostrom</surname><given-names>Quinn T.</given-names></name><name><surname>Shimmel</surname><given-names>Kristen</given-names></name><name><surname>Wolinsky</surname><given-names>Yingli</given-names></name><name><surname>Sloan</surname><given-names>Andrew E.</given-names></name><name><surname>De Rose</surname><given-names>Agostino</given-names></name><name><surname>Giuliante</surname><given-names>Felice</given-names></name><name><surname>Goodman</surname><given-names>Marc</given-names></name><name><surname>Karlan</surname><given-names>Beth Y.</given-names></name><name><surname>Hagedorn</surname><given-names>Curt H.</given-names></name><name><surname>Eckman</surname><given-names>John</given-names></name><name><surname>Harr</surname><given-names>Jodi</given-names></name><name><surname>Myers</surname><given-names>Jerome</given-names></name><name><surname>Tucker</surname><given-names>Kelinda</given-names></name><name><surname>Zach</surname><given-names>Leigh Anne</given-names></name><name><surname>Deyarmin</surname><given-names>Brenda</given-names></name><name><surname>Hu</surname><given-names>Hai</given-names></name><name><surname>Kvecher</surname><given-names>Leonid</given-names></name><name><surname>Larson</surname><given-names>Caroline</given-names></name><name><surname>Mural</surname><given-names>Richard J.</given-names></name><name><surname>Somiari</surname><given-names>Stella</given-names></name><name><surname>Vicha</surname><given-names>Ales</given-names></name><name><surname>Zelinka</surname><given-names>Tomas</given-names></name><name><surname>Bennett</surname><given-names>Joseph</given-names></name><name><surname>Iacocca</surname><given-names>Mary</given-names></name><name><surname>Rabeno</surname><given-names>Brenda</given-names></name><name><surname>Swanson</surname><given-names>Patricia</given-names></name><name><surname>Latour</surname><given-names>Mathieu</given-names></name><name><surname>Lacombe</surname><given-names>Louis</given-names></name><name><surname>T&#x000ea;tu</surname><given-names>Bernard</given-names></name><name><surname>Bergeron</surname><given-names>Alain</given-names></name><name><surname>McGraw</surname><given-names>Mary</given-names></name><name><surname>Staugaitis</surname><given-names>Susan M.</given-names></name><name><surname>Chabot</surname><given-names>John</given-names></name><name><surname>Hibshoosh</surname><given-names>Hanina</given-names></name><name><surname>Sepulveda</surname><given-names>Antonia</given-names></name><name><surname>Su</surname><given-names>Tao</given-names></name><name><surname>Wang</surname><given-names>Timothy</given-names></name><name><surname>Potapova</surname><given-names>Olga</given-names></name><name><surname>Voronina</surname><given-names>Olga</given-names></name><name><surname>Desjardins</surname><given-names>Laurence</given-names></name><name><surname>Mariani</surname><given-names>Odette</given-names></name><name><surname>Roman-Roman</surname><given-names>Sergio</given-names></name><name><surname>Sastre</surname><given-names>Xavier</given-names></name><name><surname>Stern</surname><given-names>Marc-Henri</given-names></name><name><surname>Cheng</surname><given-names>Feixiong</given-names></name><name><surname>Signoretti</surname><given-names>Sabina</given-names></name><name><surname>Berchuck</surname><given-names>Andrew</given-names></name><name><surname>Bigner</surname><given-names>Darell</given-names></name><name><surname>Lipp</surname><given-names>Eric</given-names></name><name><surname>Marks</surname><given-names>Jeffrey</given-names></name><name><surname>McCall</surname><given-names>Shannon</given-names></name><name><surname>McLendon</surname><given-names>Roger</given-names></name><name><surname>Secord</surname><given-names>Angeles</given-names></name><name><surname>Sharp</surname><given-names>Alexis</given-names></name><name><surname>Behera</surname><given-names>Madhusmita</given-names></name><name><surname>Brat</surname><given-names>Daniel J.</given-names></name><name><surname>Chen</surname><given-names>Amy</given-names></name><name><surname>Delman</surname><given-names>Keith</given-names></name><name><surname>Force</surname><given-names>Seth</given-names></name><name><surname>Khuri</surname><given-names>Fadlo</given-names></name><name><surname>Magliocca</surname><given-names>Kelly</given-names></name><name><surname>Maithel</surname><given-names>Shishir</given-names></name><name><surname>Olson</surname><given-names>Jeffrey J.</given-names></name><name><surname>Owonikoko</surname><given-names>Taofeek</given-names></name><name><surname>Pickens</surname><given-names>Alan</given-names></name><name><surname>Ramalingam</surname><given-names>Suresh</given-names></name><name><surname>Shin</surname><given-names>Dong M.</given-names></name><name><surname>Sica</surname><given-names>Gabriel</given-names></name><name><surname>Van Meir</surname><given-names>Erwin G.</given-names></name><name><surname>Zhang</surname><given-names>Hongzheng</given-names></name><name><surname>Eijckenboom</surname><given-names>Wil</given-names></name><name><surname>Gillis</surname><given-names>Ad</given-names></name><name><surname>Korpershoek</surname><given-names>Esther</given-names></name><name><surname>Looijenga</surname><given-names>Leendert</given-names></name><name><surname>Oosterhuis</surname><given-names>Wolter</given-names></name><name><surname>Stoop</surname><given-names>Hans</given-names></name><name><surname>van Kessel</surname><given-names>Kim E.</given-names></name><name><surname>Zwarthoff</surname><given-names>Ellen C.</given-names></name><name><surname>Calatozzolo</surname><given-names>Chiara</given-names></name><name><surname>Cuppini</surname><given-names>Lucia</given-names></name><name><surname>Cuzzubbo</surname><given-names>Stefania</given-names></name><name><surname>DiMeco</surname><given-names>Francesco</given-names></name><name><surname>Finocchiaro</surname><given-names>Gaetano</given-names></name><name><surname>Mattei</surname><given-names>Luca</given-names></name><name><surname>Perin</surname><given-names>Alessandro</given-names></name><name><surname>Pollo</surname><given-names>Bianca</given-names></name><name><surname>Chen</surname><given-names>Chu</given-names></name><name><surname>Houck</surname><given-names>John</given-names></name><name><surname>Lohavanichbutr</surname><given-names>Pawadee</given-names></name><name><surname>Hartmann</surname><given-names>Arndt</given-names></name><name><surname>Stoehr</surname><given-names>Christine</given-names></name><name><surname>Stoehr</surname><given-names>Robert</given-names></name><name><surname>Taubert</surname><given-names>Helge</given-names></name><name><surname>Wach</surname><given-names>Sven</given-names></name><name><surname>Wullich</surname><given-names>Bernd</given-names></name><name><surname>Kycler</surname><given-names>Witold</given-names></name><name><surname>Murawa</surname><given-names>Dawid</given-names></name><name><surname>Wiznerowicz</surname><given-names>Maciej</given-names></name><name><surname>Chung</surname><given-names>Ki</given-names></name><name><surname>Edenfield</surname><given-names>W. Jeffrey</given-names></name><name><surname>Martin</surname><given-names>Julie</given-names></name><name><surname>Baudin</surname><given-names>Eric</given-names></name><name><surname>Bubley</surname><given-names>Glenn</given-names></name><name><surname>Bueno</surname><given-names>Raphael</given-names></name><name><surname>De Rienzo</surname><given-names>Assunta</given-names></name><name><surname>Richards</surname><given-names>William G.</given-names></name><name><surname>Kalkanis</surname><given-names>Steven</given-names></name><name><surname>Mikkelsen</surname><given-names>Tom</given-names></name><name><surname>Noushmehr</surname><given-names>Houtan</given-names></name><name><surname>Scarpace</surname><given-names>Lisa</given-names></name><name><surname>Girard</surname><given-names>Nicolas</given-names></name><name><surname>Aymerich</surname><given-names>Marta</given-names></name><name><surname>Campo</surname><given-names>Elias</given-names></name><name><surname>Gin&#x000e9;</surname><given-names>Eva</given-names></name><name><surname>Guillermo</surname><given-names>Armando L&#x000f3;pez</given-names></name><name><surname>Van Bang</surname><given-names>Nguyen</given-names></name><name><surname>Hanh</surname><given-names>Phan Thi</given-names></name><name><surname>Phu</surname><given-names>Bui Duc</given-names></name><name><surname>Tang</surname><given-names>Yufang</given-names></name><name><surname>Colman</surname><given-names>Howard</given-names></name><name><surname>Evason</surname><given-names>Kimberley</given-names></name><name><surname>Dottino</surname><given-names>Peter R.</given-names></name><name><surname>Martignetti</surname><given-names>John A.</given-names></name><name><surname>Gabra</surname><given-names>Hani</given-names></name><name><surname>Juhl</surname><given-names>Hartmut</given-names></name><name><surname>Akeredolu</surname><given-names>Teniola</given-names></name><name><surname>Stepa</surname><given-names>Serghei</given-names></name><name><surname>Hoon</surname><given-names>Dave</given-names></name><name><surname>Ahn</surname><given-names>Keunsoo</given-names></name><name><surname>Kang</surname><given-names>Koo Jeong</given-names></name><name><surname>Beuschlein</surname><given-names>Felix</given-names></name><name><surname>Breggia</surname><given-names>Anne</given-names></name><name><surname>Birrer</surname><given-names>Michael</given-names></name><name><surname>Bell</surname><given-names>Debra</given-names></name><name><surname>Borad</surname><given-names>Mitesh</given-names></name><name><surname>Bryce</surname><given-names>Alan H.</given-names></name><name><surname>Castle</surname><given-names>Erik</given-names></name><name><surname>Chandan</surname><given-names>Vishal</given-names></name><name><surname>Cheville</surname><given-names>John</given-names></name><name><surname>Copland</surname><given-names>John A.</given-names></name><name><surname>Farnell</surname><given-names>Michael</given-names></name><name><surname>Flotte</surname><given-names>Thomas</given-names></name><name><surname>Giama</surname><given-names>Nasra</given-names></name><name><surname>Ho</surname><given-names>Thai</given-names></name><name><surname>Kendrick</surname><given-names>Michael</given-names></name><name><surname>Kocher</surname><given-names>Jean-Pierre</given-names></name><name><surname>Kopp</surname><given-names>Karla</given-names></name><name><surname>Moser</surname><given-names>Catherine</given-names></name><name><surname>Nagorney</surname><given-names>David</given-names></name><name><surname>O&#x02019;Brien</surname><given-names>Daniel</given-names></name><name><surname>O&#x02019;Neill</surname><given-names>Brian Patrick</given-names></name><name><surname>Patel</surname><given-names>Tushar</given-names></name><name><surname>Petersen</surname><given-names>Gloria</given-names></name><name><surname>Que</surname><given-names>Florencia</given-names></name><name><surname>Rivera</surname><given-names>Michael</given-names></name><name><surname>Roberts</surname><given-names>Lewis</given-names></name><name><surname>Smallridge</surname><given-names>Robert</given-names></name><name><surname>Smyrk</surname><given-names>Thomas</given-names></name><name><surname>Stanton</surname><given-names>Melissa</given-names></name><name><surname>Thompson</surname><given-names>R. Houston</given-names></name><name><surname>Torbenson</surname><given-names>Michael</given-names></name><name><surname>Yang</surname><given-names>Ju Dong</given-names></name><name><surname>Zhang</surname><given-names>Lizhi</given-names></name><name><surname>Brimo</surname><given-names>Fadi</given-names></name><name><surname>Ajani</surname><given-names>Jaffer A.</given-names></name><name><surname>Gonzalez</surname><given-names>Ana Maria Angulo</given-names></name><name><surname>Behrens</surname><given-names>Carmen</given-names></name><name><surname>Bondaruk</surname><given-names>Jolanta</given-names></name><name><surname>Broaddus</surname><given-names>Russell</given-names></name><name><surname>Czerniak</surname><given-names>Bogdan</given-names></name><name><surname>Esmaeli</surname><given-names>Bita</given-names></name><name><surname>Fujimoto</surname><given-names>Junya</given-names></name><name><surname>Gershenwald</surname><given-names>Jeffrey</given-names></name><name><surname>Guo</surname><given-names>Charles</given-names></name><name><surname>Lazar</surname><given-names>Alexander J.</given-names></name><name><surname>Logothetis</surname><given-names>Christopher</given-names></name><name><surname>Meric-Bernstam</surname><given-names>Funda</given-names></name><name><surname>Moran</surname><given-names>Cesar</given-names></name><name><surname>Ramondetta</surname><given-names>Lois</given-names></name><name><surname>Rice</surname><given-names>David</given-names></name><name><surname>Sood</surname><given-names>Anil</given-names></name><name><surname>Tamboli</surname><given-names>Pheroze</given-names></name><name><surname>Thompson</surname><given-names>Timothy</given-names></name><name><surname>Troncoso</surname><given-names>Patricia</given-names></name><name><surname>Tsao</surname><given-names>Anne</given-names></name><name><surname>Wistuba</surname><given-names>Ignacio</given-names></name><name><surname>Carter</surname><given-names>Candace</given-names></name><name><surname>Haydu</surname><given-names>Lauren</given-names></name><name><surname>Hersey</surname><given-names>Peter</given-names></name><name><surname>Jakrot</surname><given-names>Valerie</given-names></name><name><surname>Kakavand</surname><given-names>Hojabr</given-names></name><name><surname>Kefford</surname><given-names>Richard</given-names></name><name><surname>Lee</surname><given-names>Kenneth</given-names></name><name><surname>Long</surname><given-names>Georgina</given-names></name><name><surname>Mann</surname><given-names>Graham</given-names></name><name><surname>Quinn</surname><given-names>Michael</given-names></name><name><surname>Saw</surname><given-names>Robyn</given-names></name><name><surname>Scolyer</surname><given-names>Richard</given-names></name><name><surname>Shannon</surname><given-names>Kerwin</given-names></name><name><surname>Spillane</surname><given-names>Andrew</given-names></name><name><surname>Stretch</surname><given-names>Jonathan</given-names></name><name><surname>Synott</surname><given-names>Maria</given-names></name><name><surname>Thompson</surname><given-names>John</given-names></name><name><surname>Wilmott</surname><given-names>James</given-names></name><name><surname>Al-Ahmadie</surname><given-names>Hikmat</given-names></name><name><surname>Chan</surname><given-names>Timothy A.</given-names></name><name><surname>Ghossein</surname><given-names>Ronald</given-names></name><name><surname>Gopalan</surname><given-names>Anuradha</given-names></name><name><surname>Levine</surname><given-names>Douglas A.</given-names></name><name><surname>Reuter</surname><given-names>Victor</given-names></name><name><surname>Singer</surname><given-names>Samuel</given-names></name><name><surname>Singh</surname><given-names>Bhuvanesh</given-names></name><name><surname>Tien</surname><given-names>Nguyen Viet</given-names></name><name><surname>Broudy</surname><given-names>Thomas</given-names></name><name><surname>Mirsaidi</surname><given-names>Cyrus</given-names></name><name><surname>Nair</surname><given-names>Praveen</given-names></name><name><surname>Drwiega</surname><given-names>Paul</given-names></name><name><surname>Miller</surname><given-names>Judy</given-names></name><name><surname>Smith</surname><given-names>Jennifer</given-names></name><name><surname>Zaren</surname><given-names>Howard</given-names></name><name><surname>Park</surname><given-names>Joong-Won</given-names></name><name><surname>Hung</surname><given-names>Nguyen Phi</given-names></name><name><surname>Kebebew</surname><given-names>Electron</given-names></name><name><surname>Linehan</surname><given-names>W. Marston</given-names></name><name><surname>Metwalli</surname><given-names>Adam R.</given-names></name><name><surname>Pacak</surname><given-names>Karel</given-names></name><name><surname>Pinto</surname><given-names>Peter A.</given-names></name><name><surname>Schiffman</surname><given-names>Mark</given-names></name><name><surname>Schmidt</surname><given-names>Laura S.</given-names></name><name><surname>Vocke</surname><given-names>Cathy D.</given-names></name><name><surname>Wentzensen</surname><given-names>Nicolas</given-names></name><name><surname>Worrell</surname><given-names>Robert</given-names></name><name><surname>Yang</surname><given-names>Hannah</given-names></name><name><surname>Moncrieff</surname><given-names>Marc</given-names></name><name><surname>Goparaju</surname><given-names>Chandra</given-names></name><name><surname>Melamed</surname><given-names>Jonathan</given-names></name><name><surname>Pass</surname><given-names>Harvey</given-names></name><name><surname>Botnariuc</surname><given-names>Natalia</given-names></name><name><surname>Caraman</surname><given-names>Irina</given-names></name><name><surname>Cernat</surname><given-names>Mircea</given-names></name><name><surname>Chemencedji</surname><given-names>Inga</given-names></name><name><surname>Clipca</surname><given-names>Adrian</given-names></name><name><surname>Doruc</surname><given-names>Serghei</given-names></name><name><surname>Gorincioi</surname><given-names>Ghenadie</given-names></name><name><surname>Mura</surname><given-names>Sergiu</given-names></name><name><surname>Pirtac</surname><given-names>Maria</given-names></name><name><surname>Stancul</surname><given-names>Irina</given-names></name><name><surname>Tcaciuc</surname><given-names>Diana</given-names></name><name><surname>Albert</surname><given-names>Monique</given-names></name><name><surname>Alexopoulou</surname><given-names>Iakovina</given-names></name><name><surname>Arnaout</surname><given-names>Angel</given-names></name><name><surname>Bartlett</surname><given-names>John</given-names></name><name><surname>Engel</surname><given-names>Jay</given-names></name><name><surname>Gilbert</surname><given-names>Sebastien</given-names></name><name><surname>Parfitt</surname><given-names>Jeremy</given-names></name><name><surname>Sekhon</surname><given-names>Harman</given-names></name><name><surname>Thomas</surname><given-names>George</given-names></name><name><surname>Rassl</surname><given-names>Doris M.</given-names></name><name><surname>Rintoul</surname><given-names>Robert C.</given-names></name><name><surname>Bifulco</surname><given-names>Carlo</given-names></name><name><surname>Tamakawa</surname><given-names>Raina</given-names></name><name><surname>Urba</surname><given-names>Walter</given-names></name><name><surname>Hayward</surname><given-names>Nicholas</given-names></name><name><surname>Timmers</surname><given-names>Henri</given-names></name><name><surname>Antenucci</surname><given-names>Anna</given-names></name><name><surname>Facciolo</surname><given-names>Francesco</given-names></name><name><surname>Grazi</surname><given-names>Gianluca</given-names></name><name><surname>Marino</surname><given-names>Mirella</given-names></name><name><surname>Merola</surname><given-names>Roberta</given-names></name><name><surname>de Krijger</surname><given-names>Ronald</given-names></name><name><surname>Gimenez-Roqueplo</surname><given-names>Anne-Paule</given-names></name><name><surname>Pich&#x000e9;</surname><given-names>Alain</given-names></name><name><surname>Chevalier</surname><given-names>Simone</given-names></name><name><surname>McKercher</surname><given-names>Ginette</given-names></name><name><surname>Birsoy</surname><given-names>Kivanc</given-names></name><name><surname>Barnett</surname><given-names>Gene</given-names></name><name><surname>Brewer</surname><given-names>Cathy</given-names></name><name><surname>Farver</surname><given-names>Carol</given-names></name><name><surname>Naska</surname><given-names>Theresa</given-names></name><name><surname>Pennell</surname><given-names>Nathan A.</given-names></name><name><surname>Raymond</surname><given-names>Daniel</given-names></name><name><surname>Schilero</surname><given-names>Cathy</given-names></name><name><surname>Smolenski</surname><given-names>Kathy</given-names></name><name><surname>Williams</surname><given-names>Felicia</given-names></name><name><surname>Morrison</surname><given-names>Carl</given-names></name><name><surname>Borgia</surname><given-names>Jeffrey A.</given-names></name><name><surname>Liptay</surname><given-names>Michael J.</given-names></name><name><surname>Pool</surname><given-names>Mark</given-names></name><name><surname>Seder</surname><given-names>Christopher W.</given-names></name><name><surname>Junker</surname><given-names>Kerstin</given-names></name><name><surname>Omberg</surname><given-names>Larsson</given-names></name><name><surname>Dinkin</surname><given-names>Mikhail</given-names></name><name><surname>Manikhas</surname><given-names>George</given-names></name><name><surname>Alvaro</surname><given-names>Domenico</given-names></name><name><surname>Bragazzi</surname><given-names>Maria Consiglia</given-names></name><name><surname>Cardinale</surname><given-names>Vincenzo</given-names></name><name><surname>Carpino</surname><given-names>Guido</given-names></name><name><surname>Gaudio</surname><given-names>Eugenio</given-names></name><name><surname>Chesla</surname><given-names>David</given-names></name><name><surname>Cottingham</surname><given-names>Sandra</given-names></name><name><surname>Dubina</surname><given-names>Michael</given-names></name><name><surname>Moiseenko</surname><given-names>Fedor</given-names></name><name><surname>Dhanasekaran</surname><given-names>Renumathy</given-names></name><name><surname>Becker</surname><given-names>Karl-Friedrich</given-names></name><name><surname>Janssen</surname><given-names>Klaus-Peter</given-names></name><name><surname>Slotta-Huspenina</surname><given-names>Julia</given-names></name><name><surname>Abdel-Rahman</surname><given-names>Mohamed H.</given-names></name><name><surname>Aziz</surname><given-names>Dina</given-names></name><name><surname>Bell</surname><given-names>Sue</given-names></name><name><surname>Cebulla</surname><given-names>Colleen M.</given-names></name><name><surname>Davis</surname><given-names>Amy</given-names></name><name><surname>Duell</surname><given-names>Rebecca</given-names></name><name><surname>Elder</surname><given-names>J. Bradley</given-names></name><name><surname>Hilty</surname><given-names>Joe</given-names></name><name><surname>Kumar</surname><given-names>Bahavna</given-names></name><name><surname>Lang</surname><given-names>James</given-names></name><name><surname>Lehman</surname><given-names>Norman L.</given-names></name><name><surname>Mandt</surname><given-names>Randy</given-names></name><name><surname>Nguyen</surname><given-names>Phuong</given-names></name><name><surname>Pilarski</surname><given-names>Robert</given-names></name><name><surname>Rai</surname><given-names>Karan</given-names></name><name><surname>Schoenfield</surname><given-names>Lynn</given-names></name><name><surname>Senecal</surname><given-names>Kelly</given-names></name><name><surname>Wakely</surname><given-names>Paul</given-names></name><name><surname>Hansen</surname><given-names>Paul</given-names></name><name><surname>Lechan</surname><given-names>Ronald</given-names></name><name><surname>Powers</surname><given-names>James</given-names></name><name><surname>Tischler</surname><given-names>Arthur</given-names></name><name><surname>Grizzle</surname><given-names>William E.</given-names></name><name><surname>Sexton</surname><given-names>Katherine C.</given-names></name><name><surname>Kastl</surname><given-names>Alison</given-names></name><name><surname>Henderson</surname><given-names>Joel</given-names></name><name><surname>Porten</surname><given-names>Sima</given-names></name><name><surname>Waldmann</surname><given-names>Jens</given-names></name><name><surname>Fassnacht</surname><given-names>Martin</given-names></name><name><surname>Asa</surname><given-names>Sylvia L.</given-names></name><name><surname>Schadendorf</surname><given-names>Dirk</given-names></name><name><surname>Couce</surname><given-names>Marta</given-names></name><name><surname>Graefen</surname><given-names>Markus</given-names></name><name><surname>Huland</surname><given-names>Hartwig</given-names></name><name><surname>Sauter</surname><given-names>Guido</given-names></name><name><surname>Schlomm</surname><given-names>Thorsten</given-names></name><name><surname>Simon</surname><given-names>Ronald</given-names></name><name><surname>Tennstedt</surname><given-names>Pierre</given-names></name><name><surname>Olabode</surname><given-names>Oluwole</given-names></name><name><surname>Nelson</surname><given-names>Mark</given-names></name><name><surname>Bathe</surname><given-names>Oliver</given-names></name><name><surname>Carroll</surname><given-names>Peter R.</given-names></name><name><surname>Chan</surname><given-names>June M.</given-names></name><name><surname>Disaia</surname><given-names>Philip</given-names></name><name><surname>Glenn</surname><given-names>Pat</given-names></name><name><surname>Kelley</surname><given-names>Robin K.</given-names></name><name><surname>Landen</surname><given-names>Charles N.</given-names></name><name><surname>Phillips</surname><given-names>Joanna</given-names></name><name><surname>Prados</surname><given-names>Michael</given-names></name><name><surname>Simko</surname><given-names>Jeffry</given-names></name><name><surname>Smith-McCune</surname><given-names>Karen</given-names></name><name><surname>VandenBerg</surname><given-names>Scott</given-names></name><name><surname>Roggin</surname><given-names>Kevin</given-names></name><name><surname>Fehrenbach</surname><given-names>Ashley</given-names></name><name><surname>Kendler</surname><given-names>Ady</given-names></name><name><surname>Sifri</surname><given-names>Suzanne</given-names></name><name><surname>Steele</surname><given-names>Ruth</given-names></name><name><surname>Jimeno</surname><given-names>Antonio</given-names></name><name><surname>Carey</surname><given-names>Francis</given-names></name><name><surname>Forgie</surname><given-names>Ian</given-names></name><name><surname>Mannelli</surname><given-names>Massimo</given-names></name><name><surname>Carney</surname><given-names>Michael</given-names></name><name><surname>Hernandez</surname><given-names>Brenda</given-names></name><name><surname>Campos</surname><given-names>Benito</given-names></name><name><surname>Herold-Mende</surname><given-names>Christel</given-names></name><name><surname>Jungk</surname><given-names>Christin</given-names></name><name><surname>Unterberg</surname><given-names>Andreas</given-names></name><name><surname>von Deimling</surname><given-names>Andreas</given-names></name><name><surname>Bossler</surname><given-names>Aaron</given-names></name><name><surname>Galbraith</surname><given-names>Joseph</given-names></name><name><surname>Jacobus</surname><given-names>Laura</given-names></name><name><surname>Knudson</surname><given-names>Michael</given-names></name><name><surname>Knutson</surname><given-names>Tina</given-names></name><name><surname>Ma</surname><given-names>Deqin</given-names></name><name><surname>Milhem</surname><given-names>Mohammed</given-names></name><name><surname>Sigmund</surname><given-names>Rita</given-names></name><name><surname>Godwin</surname><given-names>Andrew K.</given-names></name><name><surname>Madan</surname><given-names>Rashna</given-names></name><name><surname>Rosenthal</surname><given-names>Howard G.</given-names></name><name><surname>Adebamowo</surname><given-names>Clement</given-names></name><name><surname>Adebamowo</surname><given-names>Sally N.</given-names></name><name><surname>Boussioutas</surname><given-names>Alex</given-names></name><name><surname>Beer</surname><given-names>David</given-names></name><name><surname>Giordano</surname><given-names>Thomas</given-names></name><name><surname>Mes-Masson</surname><given-names>Anne-Marie</given-names></name><name><surname>Saad</surname><given-names>Fred</given-names></name><name><surname>Bocklage</surname><given-names>Therese</given-names></name><name><surname>Landrum</surname><given-names>Lisa</given-names></name><name><surname>Mannel</surname><given-names>Robert</given-names></name><name><surname>Moore</surname><given-names>Kathleen</given-names></name><name><surname>Moxley</surname><given-names>Katherine</given-names></name><name><surname>Postier</surname><given-names>Russel</given-names></name><name><surname>Walker</surname><given-names>Joan</given-names></name><name><surname>Zuna</surname><given-names>Rosemary</given-names></name><name><surname>Feldman</surname><given-names>Michael</given-names></name><name><surname>Valdivieso</surname><given-names>Federico</given-names></name><name><surname>Dhir</surname><given-names>Rajiv</given-names></name><name><surname>Luketich</surname><given-names>James</given-names></name><name><surname>Pinero</surname><given-names>Edna M. Mora</given-names></name><name><surname>Quintero-Aguilo</surname><given-names>Mario</given-names></name><name><surname>Carlotti</surname><given-names>Carlos Gilberto</given-names></name><name><surname>Dos Santos</surname><given-names>Jose Sebasti&#x000e3;o</given-names></name><name><surname>Kemp</surname><given-names>Rafael</given-names></name><name><surname>Sankarankuty</surname><given-names>Ajith</given-names></name><name><surname>Tirapelli</surname><given-names>Daniela</given-names></name><name><surname>Catto</surname><given-names>James</given-names></name><name><surname>Agnew</surname><given-names>Kathy</given-names></name><name><surname>Swisher</surname><given-names>Elizabeth</given-names></name><name><surname>Creaney</surname><given-names>Jenette</given-names></name><name><surname>Robinson</surname><given-names>Bruce</given-names></name><name><surname>Shelley</surname><given-names>Carl Simon</given-names></name><name><surname>Godwin</surname><given-names>Eryn M.</given-names></name><name><surname>Kendall</surname><given-names>Sara</given-names></name><name><surname>Shipman</surname><given-names>Cassaundra</given-names></name><name><surname>Bradford</surname><given-names>Carol</given-names></name><name><surname>Carey</surname><given-names>Thomas</given-names></name><name><surname>Haddad</surname><given-names>Andrea</given-names></name><name><surname>Moyer</surname><given-names>Jeffey</given-names></name><name><surname>Peterson</surname><given-names>Lisa</given-names></name><name><surname>Prince</surname><given-names>Mark</given-names></name><name><surname>Rozek</surname><given-names>Laura</given-names></name><name><surname>Wolf</surname><given-names>Gregory</given-names></name><name><surname>Bowman</surname><given-names>Rayleen</given-names></name><name><surname>Fong</surname><given-names>Kwun M.</given-names></name><name><surname>Yang</surname><given-names>Ian</given-names></name><name><surname>Korst</surname><given-names>Robert</given-names></name><name><surname>Rathmell</surname><given-names>W. Kimryn</given-names></name><name><surname>Fantacone-Campbell</surname><given-names>J. Leigh</given-names></name><name><surname>Hooke</surname><given-names>Jeffrey A.</given-names></name><name><surname>Kovatich</surname><given-names>Albert J.</given-names></name><name><surname>Shriver</surname><given-names>Craig D.</given-names></name><name><surname>DiPersio</surname><given-names>John</given-names></name><name><surname>Drake</surname><given-names>Bettina</given-names></name><name><surname>Govindan</surname><given-names>Ramaswamy</given-names></name><name><surname>Heath</surname><given-names>Sharon</given-names></name><name><surname>Ley</surname><given-names>Timothy</given-names></name><name><surname>Van Tine</surname><given-names>Brian</given-names></name><name><surname>Westervelt</surname><given-names>Peter</given-names></name><name><surname>Rubin</surname><given-names>Mark A.</given-names></name><name><surname>Lee</surname><given-names>Jung Il</given-names></name><name><surname>Aredes</surname><given-names>Nat&#x000e1;lia D.</given-names></name><name><surname>Mariamidze</surname><given-names>Armaz</given-names></name></person-group><article-title>Machine Learning Detects Pan-cancer Ras Pathway Activation in The Cancer Genome Atlas</article-title><source>Cell Reports</source><year>2018</year><volume>23</volume><issue>1</issue><fpage>172-180.e3</fpage><pub-id pub-id-type="doi">10.1016/j.celrep.2018.03.046</pub-id><pub-id pub-id-type="pmid">29617658</pub-id></element-citation></ref><ref id="CR18"><label>18</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kong</surname><given-names>Yunchuan</given-names></name><name><surname>Yu</surname><given-names>Tianwei</given-names></name></person-group><article-title>A graph-embedded deep feedforward network for disease outcome classification and feature selection using gene expression data</article-title><source>Bioinformatics</source><year>2018</year><volume>34</volume><issue>21</issue><fpage>3727</fpage><lpage>3737</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/bty429</pub-id><pub-id pub-id-type="pmid">29850911</pub-id></element-citation></ref><ref id="CR19"><label>19</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dutkowski</surname><given-names>Janusz</given-names></name><name><surname>Ideker</surname><given-names>Trey</given-names></name></person-group><article-title>Protein Networks as Logic Functions in Development and Cancer</article-title><source>PLoS Computational Biology</source><year>2011</year><volume>7</volume><issue>9</issue><fpage>e1002180</fpage><pub-id pub-id-type="doi">10.1371/journal.pcbi.1002180</pub-id><pub-id pub-id-type="pmid">21980275</pub-id></element-citation></ref><ref id="CR20"><label>20</label><mixed-citation publication-type="other">Yousefi S, Song C, Nauata N, Cooper L. Learning genomic representations to predict clinical outcomes in cancer. http://arxiv.org/abs/1609.08663.</mixed-citation></ref><ref id="CR21"><label>21</label><mixed-citation publication-type="other">Katzman J, Shaham U, Bates J, Cloninger A, Jiang T, Kluger Y. DeepSurv: Personalized treatment recommender system using a cox proportional hazards deep neural network; 18(1). 10.1186/s12874-018-0482-1.</mixed-citation></ref><ref id="CR22"><label>22</label><mixed-citation publication-type="other">Yousefi S, Amrollahi F, Amgad M, Dong C, Lewis JE, Song C, Gutman DA, Halani SH, Velazquez Vega JE, Brat DJ, Cooper LAD. Predicting clinical outcomes from large scale cancer genomic profiles with deep survival models. Sci Rep; 7(1):11707. 10.1038/s41598-017-11817-6.</mixed-citation></ref><ref id="CR23"><label>23</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Charles</given-names></name><name><surname>Gong</surname><given-names>Binsheng</given-names></name><name><surname>Bushel</surname><given-names>Pierre R</given-names></name><name><surname>Thierry-Mieg</surname><given-names>Jean</given-names></name><name><surname>Thierry-Mieg</surname><given-names>Danielle</given-names></name><name><surname>Xu</surname><given-names>Joshua</given-names></name><name><surname>Fang</surname><given-names>Hong</given-names></name><name><surname>Hong</surname><given-names>Huixiao</given-names></name><name><surname>Shen</surname><given-names>Jie</given-names></name><name><surname>Su</surname><given-names>Zhenqiang</given-names></name><name><surname>Meehan</surname><given-names>Joe</given-names></name><name><surname>Li</surname><given-names>Xiaojin</given-names></name><name><surname>Yang</surname><given-names>Lu</given-names></name><name><surname>Li</surname><given-names>Haiqing</given-names></name><name><surname>&#x00141;abaj</surname><given-names>Pawe&#x00142; P</given-names></name><name><surname>Kreil</surname><given-names>David P</given-names></name><name><surname>Megherbi</surname><given-names>Dalila</given-names></name><name><surname>Gaj</surname><given-names>Stan</given-names></name><name><surname>Caiment</surname><given-names>Florian</given-names></name><name><surname>van Delft</surname><given-names>Joost</given-names></name><name><surname>Kleinjans</surname><given-names>Jos</given-names></name><name><surname>Scherer</surname><given-names>Andreas</given-names></name><name><surname>Devanarayan</surname><given-names>Viswanath</given-names></name><name><surname>Wang</surname><given-names>Jian</given-names></name><name><surname>Yang</surname><given-names>Yong</given-names></name><name><surname>Qian</surname><given-names>Hui-Rong</given-names></name><name><surname>Lancashire</surname><given-names>Lee J</given-names></name><name><surname>Bessarabova</surname><given-names>Marina</given-names></name><name><surname>Nikolsky</surname><given-names>Yuri</given-names></name><name><surname>Furlanello</surname><given-names>Cesare</given-names></name><name><surname>Chierici</surname><given-names>Marco</given-names></name><name><surname>Albanese</surname><given-names>Davide</given-names></name><name><surname>Jurman</surname><given-names>Giuseppe</given-names></name><name><surname>Riccadonna</surname><given-names>Samantha</given-names></name><name><surname>Filosi</surname><given-names>Michele</given-names></name><name><surname>Visintainer</surname><given-names>Roberto</given-names></name><name><surname>Zhang</surname><given-names>Ke K</given-names></name><name><surname>Li</surname><given-names>Jianying</given-names></name><name><surname>Hsieh</surname><given-names>Jui-Hua</given-names></name><name><surname>Svoboda</surname><given-names>Daniel L</given-names></name><name><surname>Fuscoe</surname><given-names>James C</given-names></name><name><surname>Deng</surname><given-names>Youping</given-names></name><name><surname>Shi</surname><given-names>Leming</given-names></name><name><surname>Paules</surname><given-names>Richard S</given-names></name><name><surname>Auerbach</surname><given-names>Scott S</given-names></name><name><surname>Tong</surname><given-names>Weida</given-names></name></person-group><article-title>The concordance between RNA-seq and microarray data depends on chemical treatment and transcript abundance</article-title><source>Nature Biotechnology</source><year>2014</year><volume>32</volume><issue>9</issue><fpage>926</fpage><lpage>932</lpage><pub-id pub-id-type="doi">10.1038/nbt.3001</pub-id></element-citation></ref><ref id="CR24"><label>24</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Qun</given-names></name><name><surname>Diskin</surname><given-names>Sharon</given-names></name><name><surname>Rappaport</surname><given-names>Eric</given-names></name><name><surname>Attiyeh</surname><given-names>Edward</given-names></name><name><surname>Mosse</surname><given-names>Yael</given-names></name><name><surname>Shue</surname><given-names>Daniel</given-names></name><name><surname>Seiser</surname><given-names>Eric</given-names></name><name><surname>Jagannathan</surname><given-names>Jayanti</given-names></name><name><surname>Shusterman</surname><given-names>Suzanne</given-names></name><name><surname>Bansal</surname><given-names>Manisha</given-names></name><name><surname>Khazi</surname><given-names>Deepa</given-names></name><name><surname>Winter</surname><given-names>Cynthia</given-names></name><name><surname>Okawa</surname><given-names>Erin</given-names></name><name><surname>Grant</surname><given-names>Gregory</given-names></name><name><surname>Cnaan</surname><given-names>Avital</given-names></name><name><surname>Zhao</surname><given-names>Huaqing</given-names></name><name><surname>Cheung</surname><given-names>Nai-Kong</given-names></name><name><surname>Gerald</surname><given-names>William</given-names></name><name><surname>London</surname><given-names>Wendy</given-names></name><name><surname>Matthay</surname><given-names>Katherine K.</given-names></name><name><surname>Brodeur</surname><given-names>Garrett M.</given-names></name><name><surname>Maris</surname><given-names>John M.</given-names></name></person-group><article-title>Integrative Genomics Identifies Distinct Molecular Classes of Neuroblastoma and Shows That Multiple Genes Are Targeted by Regional Alterations in DNA Copy Number</article-title><source>Cancer Research</source><year>2006</year><volume>66</volume><issue>12</issue><fpage>6050</fpage><lpage>6062</lpage><pub-id pub-id-type="doi">10.1158/0008-5472.CAN-05-4618</pub-id><pub-id pub-id-type="pmid">16778177</pub-id></element-citation></ref><ref id="CR25"><label>25</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Molenaar</surname><given-names>Jan J.</given-names></name><name><surname>Koster</surname><given-names>Jan</given-names></name><name><surname>Zwijnenburg</surname><given-names>Danny A.</given-names></name><name><surname>van Sluis</surname><given-names>Peter</given-names></name><name><surname>Valentijn</surname><given-names>Linda J.</given-names></name><name><surname>van der Ploeg</surname><given-names>Ida</given-names></name><name><surname>Hamdi</surname><given-names>Mohamed</given-names></name><name><surname>van Nes</surname><given-names>Johan</given-names></name><name><surname>Westerman</surname><given-names>Bart A.</given-names></name><name><surname>van Arkel</surname><given-names>Jennemiek</given-names></name><name><surname>Ebus</surname><given-names>Marli E.</given-names></name><name><surname>Haneveld</surname><given-names>Franciska</given-names></name><name><surname>Lakeman</surname><given-names>Arjan</given-names></name><name><surname>Schild</surname><given-names>Linda</given-names></name><name><surname>Molenaar</surname><given-names>Piet</given-names></name><name><surname>Stroeken</surname><given-names>Peter</given-names></name><name><surname>van Noesel</surname><given-names>Max M.</given-names></name><name><surname>&#x000d8;ra</surname><given-names>Ingrid</given-names></name><name><surname>Santo</surname><given-names>Evan E.</given-names></name><name><surname>Caron</surname><given-names>Huib N.</given-names></name><name><surname>Westerhout</surname><given-names>Ellen M.</given-names></name><name><surname>Versteeg</surname><given-names>Rogier</given-names></name></person-group><article-title>Sequencing of neuroblastoma identifies chromothripsis and defects in neuritogenesis genes</article-title><source>Nature</source><year>2012</year><volume>483</volume><issue>7391</issue><fpage>589</fpage><lpage>593</lpage><pub-id pub-id-type="doi">10.1038/nature10910</pub-id><pub-id pub-id-type="pmid">22367537</pub-id></element-citation></ref><ref id="CR26"><label>26</label><mixed-citation publication-type="other">Gene Expression Omnibus. <ext-link ext-link-type="uri" xlink:href="https://www.ncbi.nlm.nih.gov/geo/">https://www.ncbi.nlm.nih.gov/geo/</ext-link>. Accessed 21 Mar 2017.</mixed-citation></ref><ref id="CR27"><label>27</label><mixed-citation publication-type="other">R, 2: Genomics Analysis and Visualization Platform. <ext-link ext-link-type="uri" xlink:href="https://hgserver1.amc.nl/cgi-bin/r2/main.cgi">https://hgserver1.amc.nl/cgi-bin/r2/main.cgi</ext-link>. Accessed 20 June 2018.</mixed-citation></ref><ref id="CR28"><label>28</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhang</surname><given-names>B</given-names></name><name><surname>Horvath</surname><given-names>S</given-names></name></person-group><article-title>A general framework for weighted gene co-expression network analysis</article-title><source>Stat Appl Genet Mol Biol</source><year>2005</year><volume>4</volume><fpage>17</fpage><pub-id pub-id-type="doi">10.2202/1544-6115.1128</pub-id></element-citation></ref><ref id="CR29"><label>29</label><mixed-citation publication-type="other">Tranchevent L-C, Nazarov PV, Kaoma T, Schmartz GP, Muller A, Kim S-Y, Rajapakse JC, Azuaje F. Predicting clinical outcome of neuroblastoma patients using an integrative network-based approach. Biol Direct; 13(1):12. 10.1186/s13062-018-0214-9.</mixed-citation></ref><ref id="CR30"><label>30</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Wang</surname><given-names>Bo</given-names></name><name><surname>Mezlini</surname><given-names>Aziz M</given-names></name><name><surname>Demir</surname><given-names>Feyyaz</given-names></name><name><surname>Fiume</surname><given-names>Marc</given-names></name><name><surname>Tu</surname><given-names>Zhuowen</given-names></name><name><surname>Brudno</surname><given-names>Michael</given-names></name><name><surname>Haibe-Kains</surname><given-names>Benjamin</given-names></name><name><surname>Goldenberg</surname><given-names>Anna</given-names></name></person-group><article-title>Similarity network fusion for aggregating data types on a genomic scale</article-title><source>Nature Methods</source><year>2014</year><volume>11</volume><issue>3</issue><fpage>333</fpage><lpage>337</lpage><pub-id pub-id-type="doi">10.1038/nmeth.2810</pub-id><pub-id pub-id-type="pmid">24464287</pub-id></element-citation></ref><ref id="CR31"><label>31</label><mixed-citation publication-type="other">Decelle A., Krzakala F., Moore C., Zdeborov&#x000e1; L.Asymptotic analysis of the stochastic block model for modular networks and its algorithmic applications. Phys Rev E; 84(6):066106. 10.1103/PhysRevE.84.066106.</mixed-citation></ref><ref id="CR32"><label>32</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Das</surname><given-names>Jishnu</given-names></name><name><surname>Yu</surname><given-names>Haiyuan</given-names></name></person-group><article-title>HINT: High-quality protein interactomes and their applications in understanding human disease</article-title><source>BMC Systems Biology</source><year>2012</year><volume>6</volume><issue>1</issue><fpage>92</fpage><pub-id pub-id-type="doi">10.1186/1752-0509-6-92</pub-id><pub-id pub-id-type="pmid">22846459</pub-id></element-citation></ref><ref id="CR33"><label>33</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zerbino</surname><given-names>Daniel R</given-names></name><name><surname>Achuthan</surname><given-names>Premanand</given-names></name><name><surname>Akanni</surname><given-names>Wasiu</given-names></name><name><surname>Amode</surname><given-names>M&#x000a0;Ridwan</given-names></name><name><surname>Barrell</surname><given-names>Daniel</given-names></name><name><surname>Bhai</surname><given-names>Jyothish</given-names></name><name><surname>Billis</surname><given-names>Konstantinos</given-names></name><name><surname>Cummins</surname><given-names>Carla</given-names></name><name><surname>Gall</surname><given-names>Astrid</given-names></name><name><surname>Gir&#x000f3;n</surname><given-names>Carlos Garc&#x000ed;a</given-names></name><name><surname>Gil</surname><given-names>Laurent</given-names></name><name><surname>Gordon</surname><given-names>Leo</given-names></name><name><surname>Haggerty</surname><given-names>Leanne</given-names></name><name><surname>Haskell</surname><given-names>Erin</given-names></name><name><surname>Hourlier</surname><given-names>Thibaut</given-names></name><name><surname>Izuogu</surname><given-names>Osagie G</given-names></name><name><surname>Janacek</surname><given-names>Sophie H</given-names></name><name><surname>Juettemann</surname><given-names>Thomas</given-names></name><name><surname>To</surname><given-names>Jimmy Kiang</given-names></name><name><surname>Laird</surname><given-names>Matthew R</given-names></name><name><surname>Lavidas</surname><given-names>Ilias</given-names></name><name><surname>Liu</surname><given-names>Zhicheng</given-names></name><name><surname>Loveland</surname><given-names>Jane E</given-names></name><name><surname>Maurel</surname><given-names>Thomas</given-names></name><name><surname>McLaren</surname><given-names>William</given-names></name><name><surname>Moore</surname><given-names>Benjamin</given-names></name><name><surname>Mudge</surname><given-names>Jonathan</given-names></name><name><surname>Murphy</surname><given-names>Daniel N</given-names></name><name><surname>Newman</surname><given-names>Victoria</given-names></name><name><surname>Nuhn</surname><given-names>Michael</given-names></name><name><surname>Ogeh</surname><given-names>Denye</given-names></name><name><surname>Ong</surname><given-names>Chuang Kee</given-names></name><name><surname>Parker</surname><given-names>Anne</given-names></name><name><surname>Patricio</surname><given-names>Mateus</given-names></name><name><surname>Riat</surname><given-names>Harpreet Singh</given-names></name><name><surname>Schuilenburg</surname><given-names>Helen</given-names></name><name><surname>Sheppard</surname><given-names>Dan</given-names></name><name><surname>Sparrow</surname><given-names>Helen</given-names></name><name><surname>Taylor</surname><given-names>Kieron</given-names></name><name><surname>Thormann</surname><given-names>Anja</given-names></name><name><surname>Vullo</surname><given-names>Alessandro</given-names></name><name><surname>Walts</surname><given-names>Brandon</given-names></name><name><surname>Zadissa</surname><given-names>Amonida</given-names></name><name><surname>Frankish</surname><given-names>Adam</given-names></name><name><surname>Hunt</surname><given-names>Sarah E</given-names></name><name><surname>Kostadima</surname><given-names>Myrto</given-names></name><name><surname>Langridge</surname><given-names>Nicholas</given-names></name><name><surname>Martin</surname><given-names>Fergal J</given-names></name><name><surname>Muffato</surname><given-names>Matthieu</given-names></name><name><surname>Perry</surname><given-names>Emily</given-names></name><name><surname>Ruffier</surname><given-names>Magali</given-names></name><name><surname>Staines</surname><given-names>Dan M</given-names></name><name><surname>Trevanion</surname><given-names>Stephen J</given-names></name><name><surname>Aken</surname><given-names>Bronwen L</given-names></name><name><surname>Cunningham</surname><given-names>Fiona</given-names></name><name><surname>Yates</surname><given-names>Andrew</given-names></name><name><surname>Flicek</surname><given-names>Paul</given-names></name></person-group><article-title>Ensembl 2018</article-title><source>Nucleic Acids Research</source><year>2017</year><volume>46</volume><issue>D1</issue><fpage>D754</fpage><lpage>D761</lpage><pub-id pub-id-type="doi">10.1093/nar/gkx1098</pub-id></element-citation></ref><ref id="CR34"><label>34</label><mixed-citation publication-type="other">Srivastava N, Hinton G, Krizhevsky A, Sutskever I, Salakhutdinov R. Dropout: A simple way to prevent neural networks from overfitting. J Mach Learn Res; 15:1929&#x02013;58.</mixed-citation></ref><ref id="CR35"><label>35</label><mixed-citation publication-type="other">Kingma DP, Ba J. Adam: A method for stochastic optimization. http://arxiv.org/abs/1412.6980.</mixed-citation></ref><ref id="CR36"><label>36</label><mixed-citation publication-type="other">Choobdar S, Ahsen ME, Crawford J, Tomasoni M, Fang T, Lamparter D, Lin J, Hescott B, Hu X, Mercer J, Natoli T, Narayan R, Consortium TDMIC, Subramanian A, Zhang JD, Stolovitzky G, Kutalik Z, Lage K, Slonim DK, Saez-Rodriguez J, Cowen LJ, Bergmann S, Marbach D. Assessment of network module identification across complex diseases. bioRxiv. 2019:265553. 10.1101/265553.</mixed-citation></ref><ref id="CR37"><label>37</label><mixed-citation publication-type="other">Defferrard M, Bresson X, Vandergheynst P. Convolutional neural networks on graphs with fast localized spectral filtering. http://arxiv.org/abs/1606.09375.</mixed-citation></ref><ref id="CR38"><label>38</label><mixed-citation publication-type="other">Kipf TN, Welling M. Semi-supervised classification with graph convolutional networks. http://arxiv.org/abs/1609.02907.</mixed-citation></ref></ref-list></back></article>