<!DOCTYPE article PUBLIC "-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD v1.1 20151215//EN" "JATS-archivearticle1.dtd"> 
<article xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink" article-type="research-article"><?properties open_access?><?DTDIdentifier.IdentifierValue -//Springer-Verlag//DTD A++ V2.4//EN?><?DTDIdentifier.IdentifierType public?><?SourceDTD.DTDName A++V2.4.dtd?><?SourceDTD.Version 2.4?><?ConverterInfo.XSLTName springer2nlmx2.xsl?><?ConverterInfo.Version 1?><front><journal-meta><journal-id journal-id-type="nlm-ta">BMC Bioinformatics</journal-id><journal-id journal-id-type="iso-abbrev">BMC Bioinformatics</journal-id><journal-title-group><journal-title>BMC Bioinformatics</journal-title></journal-title-group><issn pub-type="epub">1471-2105</issn><publisher><publisher-name>BioMed Central</publisher-name><publisher-loc>London</publisher-loc></publisher></journal-meta><article-meta><article-id pub-id-type="pmcid">6266931</article-id><article-id pub-id-type="pmid">30497368</article-id><article-id pub-id-type="publisher-id">2414</article-id><article-id pub-id-type="doi">10.1186/s12859-018-2414-9</article-id><article-categories><subj-group subj-group-type="heading"><subject>Research</subject></subj-group></article-categories><title-group><article-title>Distinguishing crystallographic from biological interfaces in protein complexes: role of intermolecular contacts and energetics for classification</article-title></title-group><contrib-group><contrib contrib-type="author"><name><surname>Elez</surname><given-names>Katarina</given-names></name><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff2">2</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Bonvin</surname><given-names>Alexandre M. J. J.</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref></contrib><contrib contrib-type="author" corresp="yes"><name><surname>Vangone</surname><given-names>Anna</given-names></name><address><email><EMAIL></email></address><xref ref-type="aff" rid="Aff1">1</xref><xref ref-type="aff" rid="Aff3">3</xref></contrib><aff id="Aff1"><label>1</label><institution-wrap><institution-id institution-id-type="ISNI">0000000120346234</institution-id><institution-id institution-id-type="GRID">grid.5477.1</institution-id><institution>Bijvoet Center for Biomolecular Research, Faculty of Science &#x02013; Chemistry, </institution><institution>Utrecht University, </institution></institution-wrap>Padualaan 8, 3584 CH Utrecht, The Netherlands </aff><aff id="Aff2"><label>2</label><institution-wrap><institution-id institution-id-type="ISNI">0000 0004 1757 1758</institution-id><institution-id institution-id-type="GRID">grid.6292.f</institution-id><institution>Present address: University of Bologna, </institution></institution-wrap>Via Selmi 3, 40126 Bologna, Italy </aff><aff id="Aff3"><label>3</label>present address: Pharma Research and Early Development, Large Molecule Research, Roche Innovation Center Munich, Nonnenwald 2, Penzberg, Germany </aff></contrib-group><pub-date pub-type="epub"><day>30</day><month>11</month><year>2018</year></pub-date><pub-date pub-type="pmc-release"><day>30</day><month>11</month><year>2018</year></pub-date><pub-date pub-type="collection"><year>2018</year></pub-date><volume>19</volume><issue>Suppl 15</issue><issue-sponsor>Publication of this supplement has not been supported by sponsorship. Information about the source of funding for publication charges can be found in the individual articles. The articles have undergone the journal's standard peer review process for supplements. The Supplement Editors declare that they have no competing interests.</issue-sponsor><elocation-id>438</elocation-id><permissions><copyright-statement>&#x000a9; The Author(s). 2018</copyright-statement><license license-type="OpenAccess"><license-p><bold>Open Access</bold>This article is distributed under the terms of the Creative Commons Attribution 4.0 International License (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</ext-link>), which permits unrestricted use, distribution, and reproduction in any medium, provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (<ext-link ext-link-type="uri" xlink:href="http://creativecommons.org/publicdomain/zero/1.0/">http://creativecommons.org/publicdomain/zero/1.0/</ext-link>) applies to the data made available in this article, unless otherwise stated.</license-p></license></permissions><abstract id="Abs1"><sec><title>Background</title><p id="Par1">Study of macromolecular assemblies is fundamental to understand functions in cells. X-ray crystallography is the most common technique to solve their 3D structure at atomic resolution. In a crystal, however, both biologically-relevant interfaces and non-specific interfaces resulting from crystallographic packing are observed. Due to the complexity of the biological assemblies currently tackled, classifying those interfaces, i.e. distinguishing biological from crystal lattice interfaces, is not trivial and often prone to errors. In this context, analyzing the physico-chemical characteristics of biological/crystal interfaces can help researchers identify possible features that distinguish them and gain a better understanding of the systems.</p></sec><sec><title>Results</title><p id="Par2">In this work, we are providing new insights into the differences between biological and crystallographic complexes by focusing on &#x0201c;pair-properties&#x0201d; of interfaces that have not yet been fully investigated. We investigated properties such intermolecular residue-residue contacts (already successfully applied to the prediction of binding affinities) and interaction energies (electrostatic, Van der Waals and desolvation). By using the XtalMany and BioMany interface datasets, we show that interfacial residue contacts, classified as a function of their physico-chemical properties, can distinguish between biological and crystallographic interfaces. The energetic terms show, on average, higher values for crystal interfaces, reflecting a less stable interface due to crystal packing compared to biological interfaces. By using a variety of machine learning approaches, we trained a new interface classification predictor based on contacts and interaction energetic features. Our predictor reaches an accuracy in classifying biological vs crystal interfaces of 0.92, compared to 0.88 for EPPIC (one of the main state-of-the-art classifiers reporting same performance as PISA).</p></sec><sec><title>Conclusion</title><p id="Par3">In this work we have gained insights into the nature of intermolecular contacts and energetics terms distinguishing biological from crystallographic interfaces. Our findings might have a broader applicability in structural biology, for example for the identification of near native poses in docking. We implemented our classification approach into an easy-to-use and fast software, freely available to the scientific community from <ext-link ext-link-type="uri" xlink:href="http://github.com/haddocking/interface-classifier">http://github.com/haddocking/interface-classifier</ext-link>.</p></sec><sec><title>Electronic supplementary material</title><p>The online version of this article (10.1186/s12859-018-2414-9) contains supplementary material, which is available to authorized users.</p></sec></abstract><kwd-group xml:lang="en"><title>Keywords</title><kwd>Biological interface</kwd><kwd>Crystal interfaces</kwd><kwd>Protein-protein interface</kwd><kwd>Classification</kwd><kwd>Intermolecular contacts</kwd><kwd>Residue contacts</kwd><kwd>Predictor</kwd><kwd>EPPIC</kwd><kwd>PISA</kwd></kwd-group><conference xlink:href="https://www.bbcc-meetings.it/"><conf-name>BBCC Conference 2017</conf-name><conf-acronym>BBCC 2017</conf-acronym><conf-loc>Naples, Italy</conf-loc><conf-date>18 - 20 December 2017</conf-date></conference><custom-meta-group><custom-meta><meta-name>issue-copyright-statement</meta-name><meta-value>&#x000a9; The Author(s) 2018</meta-value></custom-meta></custom-meta-group></article-meta></front><body><sec id="Sec1"><title>Background</title><p id="Par12">Many essential cellular functions are mediated through specific protein-protein interactions. The physiological functions associated with those interactions are closely related to their three-dimensional (3D) structure. Hence, knowledge of the biologically-relevant 3D assembly of a complex is necessary for a proper understanding of interactions and their related function. Nowadays, structures of proteins and their assemblies are experimentally accessible through several techniques, the most common approach still being X-ray crystallography. Researchers are requested to deposit the coordinates of the solved crystal structures into the Protein Data Bank [<xref ref-type="bibr" rid="CR1">1</xref>]. These, by convention, contain only the asymmetric unit (ASU). In the case of complexes, researchers are also asked to define the interface corresponding to what they believe to be the biologically-relevant assembly. This is however not trivial since protein crystal lattices contains usually various interfaces: The biologically-relevant one(s), occurring in solution or physiological state, and crystallographic ones, which are non-specific and artifacts of the crystallization process. Identification of biological interfaces cannot be inferred without ambiguity solely from crystallographic data. Additional, complementary experiments might have to be conducted, e.g. mutagenesis. Traditionally, the definition of the biological interface has often been performed by simple visual inspection since it has been shown that most crystallographic interfaces are smaller than the biological ones in terms of buried surface area [<xref ref-type="bibr" rid="CR2">2</xref>&#x02013;<xref ref-type="bibr" rid="CR5">5</xref>]. However, the increasing complexity of biomolecular complexes solved nowadays has revealed biological and crystallographic interfaces of similar sizes, making their classification very challenging. For this reason, computational approaches have also been developed to tackle this problem.</p><p id="Par13">Most of the computational classification methods proposed in the last decades rely on geometrical features [<xref ref-type="bibr" rid="CR3">3</xref>, <xref ref-type="bibr" rid="CR6">6</xref>&#x02013;<xref ref-type="bibr" rid="CR8">8</xref>], evolution information [<xref ref-type="bibr" rid="CR9">9</xref>&#x02013;<xref ref-type="bibr" rid="CR12">12</xref>], energetics aspects [<xref ref-type="bibr" rid="CR13">13</xref>] or a combinations of those [<xref ref-type="bibr" rid="CR14">14</xref>&#x02013;<xref ref-type="bibr" rid="CR17">17</xref>]. Among those, EPPIC [<xref ref-type="bibr" rid="CR15">15</xref>] and PISA [<xref ref-type="bibr" rid="CR13">13</xref>], both available as web servers, have shown the highest classification performance so far, with PISA being the current standard in the field. While PISA attempts to estimate the energy of binding, EPPIC relies on evolutionary and geometrical criteria.</p><p id="Par14">The comparison of the physico-chemical characteristics of biological/crystal interfaces has helped researchers identify key features that distinguish the two. For example, the size of the interface, measured in terms of solvent-accessible area buried upon complex formation (BSA), has been shown, on average, to be larger in biological interfaces compared to crystallographic ones. A difference has also been found in the amino acids composition: Interfaces of biological complexes are enriched with aliphatic and aromatic residues, while the composition of crystallographic interfaces is not significantly different from that of the solvent accessible surface [<xref ref-type="bibr" rid="CR3">3</xref>]. So far, no single complex property on its own has been shown to be specific enough to distinguish the two types of interfaces. Further analysis of properties of interfaces and the combination of the derived features are necessary to develop accurate classifiers and gain a better understanding of recognition mechanisms.</p><p id="Par15">In this context, the role of pair-wise residue contacts (RCs) made at the interface has not yet been fully explored. The importance of intermolecular residue contacts at the interface of biological complexes has already been recognized and applied in several cases [<xref ref-type="bibr" rid="CR18">18</xref>&#x02013;<xref ref-type="bibr" rid="CR21">21</xref>]: In docking, the number of native residue-residue contacts is used as one of the assessment criteria by the blind assessment experiment CAPRI [<xref ref-type="bibr" rid="CR22">22</xref>], for scoring in various docking protocols [<xref ref-type="bibr" rid="CR23">23</xref>&#x02013;<xref ref-type="bibr" rid="CR27">27</xref>], or for fast clustering [<xref ref-type="bibr" rid="CR28">28</xref>, <xref ref-type="bibr" rid="CR29">29</xref>]. Recently, we have shown how the number and type of contacts can be a simple but robust predictor of binding affinity in protein-protein [<xref ref-type="bibr" rid="CR30">30</xref>] and protein-ligand [<xref ref-type="bibr" rid="CR31">31</xref>] complexes. Our predictor, implemented in the PRODIGY web-server [<xref ref-type="bibr" rid="CR32">32</xref>, <xref ref-type="bibr" rid="CR33">33</xref>], is one the best performing so far reported in the literature over protein-protein complexes.</p><p id="Par16">In this study, we analyze the properties of biological and crystallographic interfaces in terms of intermolecular residues contacts and interaction energies (such as electrostatic and van der Waals intermolecular energies and an empirically-derived desolvation energy term), in order to gain new insights into the different rules governing the two types of interfaces and better understand the interaction process. Based on our findings, we propose a simple, robust and competitive classification approach which is implemented into a program freely available at: <ext-link ext-link-type="uri" xlink:href="http://github.com/haddocking/interface-classifier">http://github.com/haddocking/interface-classifier</ext-link>.</p></sec><sec id="Sec2"><title>Results</title><p id="Par17">Given the importance of distinguishing a biologically-relevant interface from a crystallographic one when complementary experimental information is missing, we provide here new insights into the structural &#x02013; focusing on the intermolecular residue contacts - and energetics differences between those interfaces, and present a novel approach to accurately classify them. For this purpose we use the Many datasets, compiled by Baskaran et al. (2014) [<xref ref-type="bibr" rid="CR34">34</xref>]. It is the largest dataset reported so far in the literature, containing about 3000 biological (BioMany) and 3000 crystallographic (XtalMany) interfaces. From the original 5743 entries, we removed 8 cases that have too many clashes at the interface, making energetics calculations unreliable (see &#x0201c;<xref rid="Sec9" ref-type="sec">Method</xref>&#x0201d; for details). For the remaining 5735 cases, we calculated contact-based structural properties, energetics values, and then applied machine learning approaches to train novel predictors for their classification into biological (bio) and crystallographic (xtal) interfaces. In the following we first present the differences of the various properties between the two sets of interfaces and then discuss the accuracy reached by the various classification approaches.</p><sec id="Sec3"><title>Description of structural and energetic features</title><p id="Par18">For each complex in the Many dataset, we first built any missing atoms (those not observed in the electron density) and then calculated the number and type of residue-residue contacts (RCs) made at the interfaces using a 5&#x000a0;&#x000c5; cutoff (see also &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d;). The 5&#x000a0;&#x000c5; cutoff was the one giving the best prediction performance from a systematic scan between 3.5&#x000a0;&#x000c5; and 12.5&#x000a0;&#x000c5; (Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Table S1). Considering that the interface buried surface area (BSA) is a widely-used property in the description of protein complexes and that it was originally considered an effective approach for such a classification, we included it in our analysis for comparison.</p><p id="Par19">In Fig.&#x000a0;<xref rid="Fig1" ref-type="fig">1</xref> we reported the distribution of RCs (<italic>panel A</italic>) and BSA (<italic>panel B</italic>) values for bio/xtal interfaces (in pink and blue, respectively). As expected, both in terms of BSA and RCs, crystallographic interfaces are generally smaller than biological ones, with averages values of 47&#x02009;&#x000b1;&#x02009;11 and 1698&#x02009;&#x000b1;&#x02009;359&#x000a0;&#x000c5;<sup>2</sup>, for RCs and BSA, respectively, for crystallographic interfaces, versus 91&#x02009;&#x000b1;&#x02009;31 and 2706&#x02009;&#x000b1;&#x02009;803&#x000a0;&#x000c5;<sup>2</sup> for the biological interfaces. None of the RCs and BSA distributions is completely separated, which implies that they cannot be used as classification criteria by themselves. However, the residue contacts plots reveal better separated distributions with less outliers compared to BSA. This is in agreement with our previous observation that RCs are a better descriptor of binding affinity in protein-protein complexes than the BSA [<xref ref-type="bibr" rid="CR30">30</xref>]. The number of RCs does not only reflect the size of the interface, as the BSA, but also its topology. For instance, by calculating the number of intermolecular residue contacts instead of the buried surface area, it is possible to capture differences between geometries that would allow different number of contacts despite similar buried surfaces. By evaluating the effect of the distance cut-off in defining a RC (range 3.5&#x000a0;&#x000c5; - 12.5&#x000a0;&#x000c5;) we observed the optimal cutoff at 5&#x000a0;&#x000c5;, while the distributions trend is reproduced also at different cut-off values (see Additional file <xref rid="MOESM1" ref-type="media">1</xref>: Figure S1). In addition to the total number of residues RCs, we analyzed the density of contacts at the interface by calculating the link density (LD) (see Eq. <xref rid="Equ1" ref-type="">1</xref> in Methods). It varies between 0 and 1, with higher values indicating a denser contact network at the interface. The LD, however, does not have a better discrimination power than the number of RCs (data not shown).<fig id="Fig1"><label>Fig. 1</label><caption><p>Boxplot of the structural and energetics properties. Boxplots of the interfacial residue-contacts (RCs) (<italic>panel A</italic>), the buried surface area (BSA) (<italic>panel B</italic>) and energetics values (<italic>panels C, D, E</italic>) are reported for the BioMany and XtalMany entries in pink and blue, respectively. The electrostatics (Eelec), Van der Waals (Evdw) and Desolvation (Edes) energies have been calculated with the HADDOCK refinement server. The black line&#x000a0;in the middle of the boxes corresponds to the median, while the lower and upper hinges correspond to the 25th and 75th percentile, respectively, with the whiskers extending no longer than 1.5 times the interquartile range from the hinge. Point beyond the range are considered outliers and drawn as black points</p></caption><graphic xlink:href="12859_2018_2414_Fig1_HTML" id="MO1"/></fig></p><p id="Par20">We then went into more details by classifying the RCs according to the interacting amino acid type (with a total of 20 RCs-classes, one for each standard amino acid) and their polar/apolar/charged character, with a total of 6 contact classes: charged-charged (CC), charged-polar (CP), charged-apolar (CA), polar-polar (PP), polar-apolar (PA) and apolar-apolar (AA). As we can observe in Fig.&#x000a0;<xref rid="Fig2" ref-type="fig">2</xref> (<italic>top-left</italic> panel), the largest difference between bio and xtal interfaces contacts is clearly found in the number of apolar-apolar contacts, while the smallest is in the number of polar-polar contacts. Biological interfaces are clearly richer in apolar-apolar contacts than crystallographic ones. This is in agreement with previous findings [<xref ref-type="bibr" rid="CR35">35</xref>, <xref ref-type="bibr" rid="CR36">36</xref>] showing that the association of soluble proteins into larger complexes involves often hydrophobic patches, for which exposure to the solvent would be destabilizing. Crystallographic interfaces instead, which do not occur in solution, show no preference into the composition of the interfacial amino acids. This difference is clearly captured by AA-RCs distributions between bio and xtal interfaces. By looking at the specific amino acids contributions (Fig. <xref rid="Fig2" ref-type="fig">2</xref>, <italic>bottom</italic> panel), aliphatic apolar residues as LEU, ILE, VAL and ALA are the ones showing the largest differences in their number of contacts between bio and xtal interfaces.<fig id="Fig2"><label>Fig. 2</label><caption><p>Boxplot of the structural properties divided by physico-chemical properties. Boxplots of the interfacial Residue-Contacts (RCs) and of the Non-Interacting Surface (NIS) classified by the charged/polar/apolar character of the residues (<italic>top left panel and top right panel,</italic> respectively). The analysis of the number of RCs for each of the 20 standard amino acids is reported in the bottom panel. BioMany and XtalMany entries are reported in pink and blue, respectively. The black&#x000a0;line in the middle of the boxes corresponds to the median, while the lower and upper hinges correspond to the 25th and 75th percentile, respectively, with the whiskers extending no longer than 1.5 times the interquartile range from the hinge. Point beyond the range are considered outliers and drawn as black points</p></caption><graphic xlink:href="12859_2018_2414_Fig2_HTML" id="MO2"/></fig></p><p id="Par21">We further analyzed the Non-Interacting Surface (NIS) properties (see &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d;), since these have been described in previous works to contribute to the binding affinity in protein complexes [<xref ref-type="bibr" rid="CR37">37</xref>, <xref ref-type="bibr" rid="CR38">38</xref>]. The percentages of polar (PNIS), apolar (ANIS) and charged (CNIS) residues on the non-interacting surface, does not reveal any significant differences between biological and crystallographic interfaces (Fig. <xref rid="Fig2" ref-type="fig">2</xref>, <italic>top right</italic> panel). The physico-chemical properties of the non-interacting surface are thus quite similar in both types of complexes.</p><p id="Par22">Finally, in order to calculate the intermolecular Electrostatic (Eelec), Van der Waal (Evdw) and Desolvation (Edes) energies, we refined all interfaces of the Many dataset using the online HADDOCK refinement server [<xref ref-type="bibr" rid="CR39">39</xref>] (see also &#x0201c;<xref rid="Sec9" ref-type="sec">Method</xref>&#x0201d; for details). The various energy terms all point to more stable biological interfaces compared to the crystallographic ones, with lower energy values in all cases (Fig. <xref rid="Fig1" ref-type="fig">1</xref>, panels C, D and E). The intermolecular van der Waals energy seems the most discriminating energetic component between biological and crystallographic interfaces. It shows quite favorable (negative) values for biological interfaces with better separated distributions. Empirical desolvation energies show slightly overlapping distribution. The electrostatic energies, instead, are rather similar for both kind of interfaces.</p></sec><sec id="Sec4"><title>Machine learning training and testing of classification models</title><p id="Par23">In order to distinguish between crystallographic and biological interfaces, we trained various predictors using different classification methods based on combinations of the above described properties. The best performing one was implemented into an easy-to-use software. For the purpose of training, the Many data set provides a broad and balanced data set. Ten fold cross-validation was performed by randomly dividing the data into ten subsets, 9/10 of whose used for training and 1/10 for testing. This process was repeated 10 times to reduce the randomness of the data partitioning. Five different machine learning algorithms were used to train the models: Bagging, Random Forest, Adaptive Boosting, Gradient Boosting and Neural Network. Their performance measured on the left-out testing data is reported in terms of accuracy (i.e. the percentage of entries correctly predicted, see Eq. <xref rid="Equ2" ref-type="">2</xref> Methods).</p><p id="Par24">Classification models were trained using nine different sets of features consisting of a combination of Structural properties (feature sets labelled as S1, S2, S3, S4, S5, S6), Energetics (sets E1 and E2) and a Combination on the two (set C). Details of the various features sets are reported in Table&#x000a0;<xref rid="Tab1" ref-type="table">1</xref> (columns 1 and 2).<table-wrap id="Tab1"><label>Table 1</label><caption><p>Performance of classification models based on different features and training algorithms</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th>Training Features</th><th>Bagging</th><th>Random Forest</th><th>Adaptive Boosting</th><th>Gradient Boosting</th><th>Neural Network</th><th>Average</th></tr></thead><tbody><tr><td rowspan="2">S1</td><td rowspan="2">BSA</td><td>0.74</td><td>0.74</td><td>0.81</td><td>0.81</td><td>0.55</td><td>0.73</td></tr><tr><td>
<italic>(0.51)</italic>
</td><td>
<italic>(0.51)</italic>
</td><td>
<italic>(0.43)</italic>
</td><td>
<italic>(0.41)</italic>
</td><td>
<italic>(0.50)</italic>
</td><td>
<italic>(0.47)</italic>
</td></tr><tr><td rowspan="2">S2</td><td rowspan="2">RCs</td><td>0.86</td><td>0.86</td><td>0.85</td><td>0.86</td><td>0.85</td><td>0.86</td></tr><tr><td>
<italic>(0.50)</italic>
</td><td>
<italic>(0.50)</italic>
</td><td>
<italic>(0.51)</italic>
</td><td>
<italic>(0.50)</italic>
</td><td>
<italic>(0.54)</italic>
</td><td>
<italic>(0.51)</italic>
</td></tr><tr><td rowspan="2">S3</td><td rowspan="2">CC, CP, CA, PP, AP, AA</td><td>0.89</td><td>0.90</td><td>0.89</td><td>0.89</td><td>0.89</td><td>0.89</td></tr><tr><td>
<italic>(0.67)</italic>
</td><td>
<italic>(0.70)</italic>
</td><td>
<italic>(0.69)</italic>
</td><td>
<italic>(0.67)</italic>
</td><td>
<italic>(0.67)</italic>
</td><td>
<italic>(0.68)</italic>
</td></tr><tr><td>S4</td><td>CC, CP, CA, PP, AP, AA, ANIS, CNIS, PNIS</td><td>0.90<break/>
<italic>(0.69)</italic>
</td><td>0.90<break/>
<italic>(0.69)</italic>
</td><td>0.89<break/>
<italic>(0.66)</italic>
</td><td>0.89<break/>
<italic>(0.67)</italic>
</td><td>0.89<break/>
<italic>(0.67)</italic>
</td><td>0.89<break/>
<italic>(0.68)</italic>
</td></tr><tr><td>S5</td><td>CC, CP, CA, PP, AP, AA, LD, G, A, L, M, F, W, K, Q, E, S, P, V, I, C, Y, H, R, N, D, T</td><td>0.92<break/>
<italic>(0.74)</italic>
</td><td>0.92<break/>
<italic>(0.73)</italic>
</td><td>0.91<break/>
<italic>(0.74)</italic>
</td><td>0.92<break/>
<italic>(0.71)</italic>
</td><td>0.91<break/>
<italic>(0.77)</italic>
</td><td>0.92<break/>
<italic>(0.74)</italic>
</td></tr><tr><td>S6</td><td>CC, CP, CA, PP, AP, AA, ANIS, CNIS, PNIS, LD, G, A, L, M, F, W, K, Q, E, S, P, V, I, C, Y, H, R, N, D, T</td><td>0.92<break/>
<italic>(0.73)</italic>
</td><td>0.92<break/>
<italic>(0.75)</italic>
</td><td>0.91<break/>
<italic>(0.74)</italic>
</td><td>0.93<break/>
<italic>(0.70)</italic>
</td><td>0.92<break/>
<italic>(0.76)</italic>
</td><td>0.92<break/>
<italic>(0.74)</italic>
</td></tr><tr><td rowspan="2">E1</td><td rowspan="2">HS</td><td>0.76</td><td>0.76</td><td>0.83</td><td>0.82</td><td>0.82</td><td>0.80</td></tr><tr><td>
<italic>(0.59)</italic>
</td><td>
<italic>(0.59)</italic>
</td><td>
<italic>(0.62)</italic>
</td><td>
<italic>(0.62)</italic>
</td><td>
<italic>(0.59)</italic>
</td><td>
<italic>(0.60)</italic>
</td></tr><tr><td rowspan="2">E2</td><td rowspan="2">Eelec, Evdw, Edes</td><td>0.87</td><td>0.87</td><td>0.87</td><td>0.87</td><td>0.85</td><td>0.87</td></tr><tr><td>
<italic>(0.64)</italic>
</td><td>
<italic>(0.61)</italic>
</td><td>
<italic>(0.62)</italic>
</td><td>
<italic>(0.62)</italic>
</td><td>
<italic>(0.68)</italic>
</td><td>
<italic>(0.63)</italic>
</td></tr><tr><td/><td rowspan="2">CC, CP, CA, PP, AP, AA, ANIS, CNIS, PNIS, LD, G, A, L, M, F, W, K, Q, E, S, P, V, I, C, Y, H, R, N, D, T, Eelec, Evdw, Edes</td><td rowspan="2">0.92<break/>
<italic>(0.72)</italic>
</td><td rowspan="2">0.93<break/>
<italic>(0.73)</italic>
</td><td rowspan="2">0.92<break/>
<italic>(0.74)</italic>
</td><td rowspan="2">0.93<break/>
<italic>(0.72)</italic>
</td><td rowspan="2">0.90<break/>
<italic>(0.77)</italic>
</td><td rowspan="2">0.92<break/>
<italic>(0.74)</italic>
</td></tr><tr><td>C</td></tr></tbody></table><table-wrap-foot><p>Accuracy values calculated according Eq. <xref rid="Equ2" ref-type="">2</xref> in &#x0201c;<xref rid="Sec9" ref-type="sec">Methods</xref>&#x0201d;</p><p>The predictive accuracies have been reported for several classification models tested. Nine sets of features have been used to train new predictive models, based on structural properties (S1, S2, S3, S4, S4, S6), energetics (E1, E2) and a combination of structure and energetics (C). For each set of training features, five machine learning algorithms have been used for the training (Bagging, Random Forest, Adaptive Boosting, Gradient Boosting and Neural Network). For the trained models, the accuracies on the Many [<xref ref-type="bibr" rid="CR34">34</xref>] and the DC [<xref ref-type="bibr" rid="CR15">15</xref>] (numbers in brackets) datasets are reported. The accuracy on the Many is reported as average of the 10-fold cross validation. In brackets the accuracy over the DC dataset is reported</p></table-wrap-foot></table-wrap></p><p id="Par25">From our analysis, models based on structural features (i.e., the ones trained on the S sets) achieve, on average, a higher accuracy that energy-based ones (E sets) (see Fig.&#x000a0;<xref rid="Fig3" ref-type="fig">3</xref> and Table <xref rid="Tab1" ref-type="table">1</xref>). Interestingly, all five machine learning algorithms give rather consistent answers over the various feature sets (Fig. <xref rid="Fig3" ref-type="fig">3</xref>), which underscores the robustness of our training results. The BSA is the structural feature (set S1) that performs the worst, with an average accuracy of 0.73&#x02009;&#x000b1;&#x02009;0.11 over the five different machine learning algorithms used. The combination of residue-contacts classified by amino acid type and physico-chemical properties (S5 set) gives the best classification performance, reaching an average accuracy of 0.92&#x02009;&#x000b1;&#x02009;0.01 (set S5). The NIS properties do not add much to the trained models, which was to be expected considering their similarity between the bio/xtal interfaces: Models trained on the S4 and S6 sets show similar accuracy than the ones trained on S3 and S5 (the only difference between those being the addition of NIS properties in the training features). Overall, the best predictive model over the 5 algorithms is the one trained on both structural/energetics features together (set C). However, considering the computational time and resources needed to perform energetics calculations on protein complexes (i.e. running the HADDOCK refinement), the little gain in accuracy (average accuracy of 0.920&#x02009;&#x000b1;&#x02009;0.01 vs 0.916&#x02009;&#x000b1;&#x02009;0.01 of S5-models) can be ignored, making the simple and fast contact-based models best suited for classification purpose. We therefore decided to focus on the S5-set models. We used this set to analyze the importance of the various features. By applying a recursive feature elimination, we identify all the features (i.e., the one of set S5) as important (Additional files <xref rid="MOESM1" ref-type="media">1</xref>: Table S2). The most and least important feature for the classification is the number of contacts between two apolar residues at the interface (AA) and the number of contacts between two polar residues at the interface (PP), respectively. The last 5 ranked features in terms of importance (LYS, CC, TRP, ASN and PP) were not selected by the feature selection and hence excluded. On the remaining 22 we optimized (see Methods) the five machine learning algorithms, which were initially trained using default settings, to find the best predictive model to discriminate xtal from bio interfaces, the results of which are reported in Table&#x000a0;<xref rid="Tab2" ref-type="table">2</xref>. We finally selected as best classifier the Random Forest approach with 250 base estimators and the maximum number of features to consider when looking for the best split set to log2 on the total number of features. Its accuracy on the entire training dataset is 0.92.<fig id="Fig3"><label>Fig. 3</label><caption><p>Accuracy of machine learning classifiers. Prediction accuracies (y-axis) of the various predictors as a function of the feature set used for training (x-axis). The training sets consist of structural properties (S1, S2, S3, S4, S4, S6), energetics (E1, E2) and a combination of both (C). Refer to Table <xref rid="Tab1" ref-type="table">1</xref> for the detailed list of features included in each set. Five different machine learning algorithms have been used for the training: Bagging, Random Forest, Adaptive Boosting, Gradient Boosting and Neural Network, reported in blue, purple, green, red and brown, respectively</p></caption><graphic xlink:href="12859_2018_2414_Fig3_HTML" id="MO3"/></fig><table-wrap id="Tab2"><label>Table 2</label><caption><p>Optimization of the machine learning classifiers</p></caption><table frame="hsides" rules="groups"><thead><tr><th/><th>Classification accuracy on the MANY dataset</th><th>Classification accuracy on the independent DC dataset</th></tr></thead><tbody><tr><td>Bagging</td><td>0.92</td><td>0.73</td></tr><tr><td>Random Forest</td><td>0.92</td><td>0.74</td></tr><tr><td>Adaptive Boosting</td><td>0.92</td><td>0.74</td></tr><tr><td>Gradient Boosting</td><td>0.93</td><td>0.74</td></tr><tr><td>Neural Network</td><td>0.91</td><td>0.75</td></tr></tbody></table><table-wrap-foot><p>The maximum accuracy reached by optimizing the settings is reported by each classifier for the Many (as average over the 10-fold cross-validation) and the DC datasets</p></table-wrap-foot></table-wrap></p></sec><sec id="Sec5"><title>Validation on an independent dataset</title><p id="Par26">To further assess the performance of our classifiers, we applied them to the independent DC dataset [<xref ref-type="bibr" rid="CR15">15</xref>] that consists of about manually curated complexes with 80 xtal and 80 bio interfaces not present in the training dataset. The observed accuracies of the different models on the DC set are consistent with the training set ones, although showing lower values (Tables <xref rid="Tab1" ref-type="table">1</xref> and <xref rid="Tab2" ref-type="table">2</xref>).</p></sec></sec><sec id="Sec6"><title>Discussion</title><sec id="Sec7"><title>Limit of the classifier and comparison with current state of art</title><p id="Par27">Our final pair-wise contact-based model was trained over the S5 set of features (RCs divided by type/physico-chemical characteristics and Link Density), based on a Random Forest approach. On the Many dataset, our classifier shows a sensitivity and specificity of 91% and 93%, respectively; in other words, we correctly predict 91% and 93% of the biological and crystallographic interfaces, respectively, while we misinterpret about 7% of the xtal cases as bio, and 9% of the bio cases as xtal (average over the 10 test sets from the 10 fold training on the Many entries). We compared our results with the performance reported for the state of the art methods EPPIC [<xref ref-type="bibr" rid="CR15">15</xref>] and PISA [<xref ref-type="bibr" rid="CR13">13</xref>]. The concept at the basis of these prediction methods are different. PISA is a well-establish method in the field based on a thermodynamic estimation of the interface stability in order to predict whether it should exist in solution (biological interface) or not (crystallographic interface). EPPIC is a more recent method, based on evolutionary criteria; It uses a geometrical measure (number of buried residues) and two evolutionary indicators based on the sequence entropy of homolog sequences.</p><p id="Par28">PISA and EPPIC have comparable performance. They have shown the same recall on a PDB-wide scale of 88% of PDB-interfaces [<xref ref-type="bibr" rid="CR34">34</xref>]. Comparing our performance on the Many dataset, we reached a prediction accuracy of 92% versus the 88% of EPPIC (as reported in [<xref ref-type="bibr" rid="CR34">34</xref>]). On the smaller DC dataset (about 160 cases vs the 5735 of the Many), our accuracy is lower with 74%, against 81% for EPPIC and 79% for PISA (data reported in [<xref ref-type="bibr" rid="CR40">40</xref>]). However, the performance of EPPIC on the DC dataset has only been calculated for entries for which enough evolutionary information was found (about 82% of the DC entries). In fact, being an evolutionary-based method, its principal limitation is the sequence alignment depth. Next to its competitive performance, our predictor is very fast, returning predictions within a few seconds, compared to the minutes needed for the EPPIC and PISA web-servers. Our predictor is freely available for download from our GitHub repository (<ext-link ext-link-type="uri" xlink:href="https://github.com/haddocking/interface-classifier">https://github.com/haddocking/interface-classifier</ext-link>).</p></sec></sec><sec id="Sec8"><title>Conclusions</title><p id="Par29">We have carried out the first study focused on using residue contacts and interaction energies to distinguish biological from crystallographic interfaces. The number and nature of residue and atom contacts have already been shown to be good descriptors of binding affinity in protein-protein [<xref ref-type="bibr" rid="CR30">30</xref>] and protein-ligand [<xref ref-type="bibr" rid="CR31">31</xref>] complexes. Here they have been shown to be good descriptors for the classification of interfaces in crystal structures, even when no other energetics contributions are included. This allowed us to train a simple machine learning predictor for fast and robust classification. Its simplicity, together with the advantage that energetic calculations are not required per se (which might depend on how complete the crystal structures are) nor are sequence alignments, should make it a useful tool for structural biology. We implemented our classification approach into an easy-to-use and fast software, freely available to the scientific community from <ext-link ext-link-type="uri" xlink:href="http://github.com/haddocking/interface-classifier">http://github.com/haddocking/interface-classifier</ext-link>. We aim in a near future to offer it as a web server as an extension of our related binding affinity server PRODIGY [<xref ref-type="bibr" rid="CR33">33</xref>]. Inter-residue contacts, which have now proven valuable features for both binding affinity prediction and crystallographic interface classification, have the potential to benefit other fields of research in structural biology, such the identification of near natives poses in docking.</p></sec><sec id="Sec9"><title>Methods</title><sec id="Sec10"><title>The benchmark</title><p id="Par30">In order to study and compare biological and crystallographic interfaces, we used the bio/xtal complexes in the &#x0201c;.pdb&#x0201d; format retrieved from the automatically generated Many dataset [<xref ref-type="bibr" rid="CR34">34</xref>]. The 2831 biological entries (BioMany) includes dimeric crystal structures with interfaces present in multiple crystal forms retrieved from the ProtCID database [<xref ref-type="bibr" rid="CR12">12</xref>, <xref ref-type="bibr" rid="CR41">41</xref>]. The 2913 crystallographic interfaces (XtalMany) are instead derived from interfaces that would lead to infinite assemblies (concept firstly introduced by Monod [<xref ref-type="bibr" rid="CR42">42</xref>]). The interfaces span a difficult-to-classify interfacial area range of 500&#x02013;2000&#x000a0;&#x000c5; and represent a quite extensive and balanced set.</p></sec><sec id="Sec11"><title>Interface refinement and calculation of energetic features</title><p id="Par31">Each PDB entry of the Many dataset was refined by using the refinement protocol of our HADDOCK web server [<xref ref-type="bibr" rid="CR39">39</xref>, <xref ref-type="bibr" rid="CR43">43</xref>] (<ext-link ext-link-type="uri" xlink:href="http://haddock.science.uu.nl/services/HADDOCK2.2/haddockserver-refinement.html">http://haddock.science.uu.nl/services/HADDOCK2.2/haddockserver-refinement.html</ext-link>). HADDOCK automatically builds eventually missing atoms and side-chains, which is necessary in order to get a more realistic model of the interaction, and refine the interface in explicit solvent using the TIP3P water model and the Optimized Potentials for Liquid Simulations (OPLS) force field [<xref ref-type="bibr" rid="CR44">44</xref>] with an 8.5&#x000a0;&#x000c5; cut-off for the calculations of the non-bonded interactions. The intermolecular energy terms and buried surface area (BSA) of the refined complexes were extracted from the top ranked HADDOCK model. The HADDOCK-derived features are:<list list-type="bullet"><list-item><p id="Par32">Evdw, the intermolecular van der Waals energy described by a 12&#x02013;6 Lennard-Jones potential.</p></list-item><list-item><p id="Par33">Eelec, the intermolecular electrostatic energy described by a Coulomb potential.</p></list-item><list-item><p id="Par34">Edes, an empirical desolvation energy term [<xref ref-type="bibr" rid="CR45">45</xref>].</p></list-item><list-item><p id="Par35">BSA, the buried surface area calculated by taking the difference between the sum of the solvent accessible surface area (SASA) for each individual protein and the SASA of the protein complex using 1.4&#x000a0;&#x000c5; water probe radius.</p></list-item></list></p><p id="Par36">We changed the non-standard amino acid MSE in MET in three cases: 5cnp_3, 3wnb_1 and 4py9_2. From the original dataset consisting of 5743 cases, we removed 8 entries that presented too many clashes at the interface, making the refinement unreliable: 1rzm_1, 3qv9_2, 2v9y_2, 4e0h_1, 3n94_1, 3axc_1, 4izv_2, 2bh7_2.</p></sec><sec id="Sec12"><title>Contact-based structural properties</title><p id="Par37">Intermolecular contacts were calculated from the HADDOCK models with built missing side-chains, but before refinement. Contacts were obtained through our PRODIGY web server [<xref ref-type="bibr" rid="CR33">33</xref>] (<ext-link ext-link-type="uri" xlink:href="http://milou.science.uu.nl/services/PRODIGY/">http://milou.science.uu.nl/services/PRODIGY/</ext-link>) and its stand-alone version available from GitHub (<ext-link ext-link-type="uri" xlink:href="https://github.com/haddocking/prodigy">https://github.com/haddocking/prodigy</ext-link>). The following features were calculated:<list list-type="bullet"><list-item><p id="Par38"><italic>Pair-wise Residue Contacts (RCs).</italic> Two residues were considered in contact if any atom pair of the two residues is closer than a defined cut-off distance of 5.0&#x000a0;&#x000c5;. To systematically evaluate the impact of the cut-off values on the results, we varied the cut-off from 3.5&#x000a0;&#x000c5; to 12&#x000a0;&#x000c5;. RCs were further classified by the types of the residues in contact: charged-charged (CC), charged-polar (CP), charged-apolar (CA), polar-polar (PP), polar-apolar (PA) and apolar-apolar (AA). Residue classes were the same as reported in Vangone and Bonvin 2015 [<xref ref-type="bibr" rid="CR30">30</xref>]: N, Q, S and T as polar, E, D, H, K and R as charged and A, C, G, F, I, M, L, P, W, V and Y as apolar.</p></list-item><list-item><p id="Par39"><italic>RCs per amino acid.</italic> For each of the 20 standard amino acids, the number of RCs in which the amino acid is involved was calculated.</p></list-item><list-item><p id="Par40"><italic>Link Density (LD)</italic>, that expresses the density of the interfacial network. This is calculated as the total number of RCs at the interface divided by the maximum possible number of pair-wise contacts for that interface, as:</p></list-item></list></p><p id="Par41">
<disp-formula id="Equ1"><label>1</label><alternatives><tex-math id="M1">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ LD=\frac{RCs}{Res1\ast Res2} $$\end{document}</tex-math><mml:math id="M2" display="block"><mml:mi mathvariant="italic">LD</mml:mi><mml:mo>=</mml:mo><mml:mfrac><mml:mi mathvariant="italic">RCs</mml:mi><mml:mrow><mml:mi mathvariant="italic">Res</mml:mi><mml:mn>1</mml:mn><mml:mo>&#x02217;</mml:mo><mml:mi mathvariant="italic">Res</mml:mi><mml:mn>2</mml:mn></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2414_Article_Equ1.gif" position="anchor"/></alternatives></disp-formula>
</p><p id="Par42">where Res1 and Res2 are the number of residues involved in RCs for interface 1 and 2, respectively.</p><p id="Par43">As reported in [<xref ref-type="bibr" rid="CR37">37</xref>, <xref ref-type="bibr" rid="CR38">38</xref>], the Non-Interacting Surface (NIS) is defined as the residues that show a maximum change of 5% between unbound and bound forms in terms of relative solvent accessibility. The percentage of polar, apolar and charged NIS (PNIS, ANIS and CNIS, respectively) are defined as the percentage&#x000a0;the number of polar/apolar/charged residues divided by the total number of the residues on the NIS. Those properties have been obtained by the PRODIGY web server as well [<xref ref-type="bibr" rid="CR33">33</xref>].</p></sec><sec id="Sec13"><title>Machine learning and assessment of the prediction</title><p id="Par44">We used five machine learning algorithms from the scikit-learn python package (<ext-link ext-link-type="uri" xlink:href="http://jmlr.csail.mit.edu/papers/v12/pedregosa11a.html">http://jmlr.csail.mit.edu/papers/v12/pedregosa11a.html</ext-link>) to train our classification models: Bagging, Random Forest, Adaptive Boosting, Gradient Boosting and Neural Network. We validated our results applying a 10-fold cross-validation approach on the training dataset. We systematically evaluated the impact of different settings of the machine learning algorithms on the accuracy. We tuned the hyper-parameters of the models by performing an exhaustive grid search on the selected parameters. We explored different values for the number of trees in the 4 ensemble methods (Bagging, Random Forest, Adaptive Boosting and Gradient Boosting). For Bagging and Random Forest, the maximum number of features to consider when looking for the best split was also changed. In addition, we optimized the learning rate for the two boosting methods (Adaptive Boosting and Gradient Boosting) and the maximum depth of the individual tree, in the case of the latter. The Neural Network was fine-tuned on the size of the hidden layer and its activation function.</p><p id="Par45">A Random Forest of 500 trees was used to analyze the feature importance and perform recursive feature elimination with cross-validation in scikit-learn. First, the estimator is trained on the initial set of features and the importance of each feature is obtained through a feature importance attribute. Then, the least important features are pruned from the current set. The procedure is recursively repeated on the pruned set until there is no increase in the accuracy. The feature importance is measured by the decrease of Gini impurity index when splitting on a feature, averaged over all trees.</p><p id="Par46">The prediction performance was evaluated by calculating the accuracy defined as:<disp-formula id="Equ2"><label>2</label><alternatives><tex-math id="M3">\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$ Accuracy=\frac{TP+ TN}{P+N} $$\end{document}</tex-math><mml:math id="M4" display="block"><mml:mtext mathvariant="italic">Accuracy</mml:mtext><mml:mo>=</mml:mo><mml:mfrac><mml:mrow><mml:mi mathvariant="italic">TP</mml:mi><mml:mo>+</mml:mo><mml:mi mathvariant="italic">TN</mml:mi></mml:mrow><mml:mrow><mml:mi>P</mml:mi><mml:mo>+</mml:mo><mml:mi>N</mml:mi></mml:mrow></mml:mfrac></mml:math><graphic xlink:href="12859_2018_2414_Article_Equ2.gif" position="anchor"/></alternatives></disp-formula>where TP and TN are the true positive and true negative cases, respectively, and P and N are the total number of positive and negative cases, respectively.</p></sec><sec id="Sec14"><title>Data and model availability</title><p id="Par47">All the HADDOCK-refined models are available on the SBGrid Data repository <ext-link ext-link-type="uri" xlink:href="https://data.sbgrid.org/dataset/566/">https://data.sbgrid.org/dataset/566/</ext-link> [<xref ref-type="bibr" rid="CR46">46</xref>]. The bio/xtal classification predictor and the features used for training and testing are freely available on GitHub: <ext-link ext-link-type="uri" xlink:href="http://github.com/haddocking/interface-classifier">http://github.com/haddocking/interface-classifier</ext-link>.</p></sec></sec><sec sec-type="supplementary-material"><title>Additional file</title><sec id="Sec15"><p>
<supplementary-material content-type="local-data" id="MOESM1"><media xlink:href="12859_2018_2414_MOESM1_ESM.zip"><label>Additional file 1:</label><caption><p><bold>Figure S1.</bold> Boxplot of the RCs as function of the distance cut-off. <bold>Table S1.</bold> Evaluation of machine learning accuracy models VS distance cut-off. <bold>Table S2.</bold> Feature selection on the final predictive model. (ZIP 207 kb)</p></caption></media></supplementary-material>
</p></sec></sec></body><back><glossary><title>Abbreviations</title><def-list><def-item><term>BSA</term><def><p id="Par4">Buried Surface Area</p></def></def-item><def-item><term>EPPIC</term><def><p id="Par5">Evolutionary Protein-Protein Interface Classifier</p></def></def-item><def-item><term>HADDOCK</term><def><p id="Par6">High Ambiguity Driven biomolecular DOCKing</p></def></def-item><def-item><term>NIS</term><def><p id="Par7">Non Interacting Surface</p></def></def-item><def-item><term>PDB</term><def><p id="Par8">Protein Data Bank</p></def></def-item><def-item><term>PISA</term><def><p id="Par9">Protein Interface, Surface and Assemblies</p></def></def-item><def-item><term>PRODIGY</term><def><p id="Par10">PROtein binDIng enerGY prediction</p></def></def-item><def-item><term>RCs</term><def><p id="Par11">Residue-residue Contacts</p></def></def-item></def-list></glossary><ack><title>Acknowledgements</title><p>The authors thank Dr. Li Xue and Panagiotis Koukos for helpful discussions over data analysis. Further, the authors thank Dr. Jose Duarte, author of the Many dataset, for his valuable and prompt help in providing detailed information about the dataset.</p><sec id="FPar1"><title>Funding</title><p id="Par48">This work was supported by the European H2020 e-Infrastructure grants West-Life (grant no. 675858) and BioExcel (grant no. 675728) and by the Dutch Foundation for Scientific Research (NWO) (TOP-PUNT grant 718.015.001) and the ASDI eScience grant (027016G04). KE was supported by Programma Erasmus+/KA1 [no. 2015-IT02-KA103&#x02013;014384]. AV was supported by the Marie Sk&#x00142;odowska-Curie Individual Fellowship H2020 MSCA-IF- 2015 [BAP-659025]. Publication costs for this manuscript were sponsored by European H2020 e-Infrastructure grants West-Life (grant no. 675858).</p></sec><sec id="FPar2"><title>Availability of data and materials</title><p id="Par49">The dataset generated during the current study is available on the SBGrid Data repository <ext-link ext-link-type="uri" xlink:href="https://data.sbgrid.org/dataset/566/">https://data.sbgrid.org/dataset/566/</ext-link>, comprising the HADDOCK-refined models. The bio/xtal classification predictor and the features used for training and testing are freely available on GitHub: <ext-link ext-link-type="uri" xlink:href="http://github.com/haddocking/interface-classifier">http://github.com/haddocking/interface-classifier</ext-link>.</p></sec><sec id="FPar3"><title>About this supplement</title><p id="Par50">This article has been published as part of BMC Bioinformatics Volume 19 Supplement 15, 2018: Proceedings of the 12th International BBCC conference. The full contents of the supplement are available online at https://bmcbioinformatics.biomedcentral.com/articles/supplements/volume-19-supplement-15.</p></sec></ack><notes notes-type="author-contribution"><title>Authors&#x02019; contributions</title><p>KE carried out the preparation of the files, the measures and wrote the code. AV carried out the analysis and coordinated and drafted the manuscript. AV and AMJJB conceived the study and participated in its design. All authors read and approved the final manuscript.</p></notes><notes notes-type="COI-statement"><sec id="FPar4"><title>Ethics approval and consent to participate</title><p>Not applicable.</p></sec><sec id="FPar5"><title>Consent for publication</title><p>Not applicable.</p></sec><sec id="FPar6"><title>Competing interests</title><p>The authors declare that they have no competing interests.</p></sec><sec id="FPar7"><title>Publisher&#x02019;s Note</title><p>Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.</p></sec></notes><ref-list id="Bib1"><title>References</title><ref id="CR1"><label>1.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Berman</surname><given-names>HM</given-names></name><name><surname>Westbrook</surname><given-names>J</given-names></name><name><surname>Feng</surname><given-names>Z</given-names></name><name><surname>Gilliland</surname><given-names>G</given-names></name><name><surname>Bhat</surname><given-names>TN</given-names></name><name><surname>Weissig</surname><given-names>H</given-names></name><name><surname>Shindyalov</surname><given-names>IN</given-names></name><name><surname>Bourne</surname><given-names>PE</given-names></name></person-group><article-title>The Protein Data Bank</article-title><source>Nucleic Acids Res</source><year>2000</year><volume>28</volume><issue>1</issue><fpage>235</fpage><lpage>242</lpage><pub-id pub-id-type="doi">10.1093/nar/28.1.235</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR2"><label>2.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bahadur</surname><given-names>RP</given-names></name><name><surname>Chakrabarti</surname><given-names>P</given-names></name><name><surname>Rodier</surname><given-names>F</given-names></name><name><surname>Janin</surname><given-names>J</given-names></name></person-group><article-title>Dissecting subunit interfaces in homodimeric proteins</article-title><source>Proteins</source><year>2003</year><volume>53</volume><issue>3</issue><fpage>708</fpage><lpage>719</lpage><pub-id pub-id-type="doi">10.1002/prot.10461</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR3"><label>3.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bahadur</surname><given-names>RP</given-names></name><name><surname>Chakrabarti</surname><given-names>P</given-names></name><name><surname>Rodier</surname><given-names>F</given-names></name><name><surname>Janin</surname><given-names>J</given-names></name></person-group><article-title>A dissection of specific and non-specific protein-protein interfaces</article-title><source>J Mol Biol</source><year>2004</year><volume>336</volume><issue>4</issue><fpage>943</fpage><lpage>955</lpage><pub-id pub-id-type="doi">10.1016/j.jmb.2003.12.073</pub-id><pub-id pub-id-type="pmid">15095871</pub-id></element-citation></ref><ref id="CR4"><label>4.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Carugo</surname><given-names>O</given-names></name><name><surname>Argos</surname><given-names>P</given-names></name></person-group><article-title>Correlation between side chain mobility and conformation in protein structures</article-title><source>Protein Eng</source><year>1997</year><volume>10</volume><issue>7</issue><fpage>777</fpage><lpage>787</lpage><pub-id pub-id-type="doi">10.1093/protein/10.7.777</pub-id><pub-id pub-id-type="pmid">9342144</pub-id></element-citation></ref><ref id="CR5"><label>5.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Janin</surname><given-names>J</given-names></name><name><surname>Rodier</surname><given-names>F</given-names></name></person-group><article-title>Protein-protein interaction at crystal contacts</article-title><source>Proteins</source><year>1995</year><volume>23</volume><issue>4</issue><fpage>580</fpage><lpage>587</lpage><pub-id pub-id-type="doi">10.1002/prot.340230413</pub-id><pub-id pub-id-type="pmid">8749854</pub-id></element-citation></ref><ref id="CR6"><label>6.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Capitani</surname><given-names>G</given-names></name><name><surname>Duarte</surname><given-names>JM</given-names></name><name><surname>Baskaran</surname><given-names>K</given-names></name><name><surname>Bliven</surname><given-names>S</given-names></name><name><surname>Somody</surname><given-names>JC</given-names></name></person-group><article-title>Understanding the fabric of protein crystals: computational classification of biological interfaces and crystal contacts</article-title><source>Bioinformatics</source><year>2016</year><volume>32</volume><issue>4</issue><fpage>481</fpage><lpage>489</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btv622</pub-id><pub-id pub-id-type="pmid">26508758</pub-id></element-citation></ref><ref id="CR7"><label>7.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Janin</surname><given-names>J</given-names></name></person-group><article-title>Specific versus non-specific contacts in protein crystals</article-title><source>Nat Struct Biol</source><year>1997</year><volume>4</volume><issue>12</issue><fpage>973</fpage><lpage>974</lpage><pub-id pub-id-type="doi">10.1038/nsb1297-973</pub-id><pub-id pub-id-type="pmid">9406542</pub-id></element-citation></ref><ref id="CR8"><label>8.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Ponstingl</surname><given-names>H</given-names></name><name><surname>Henrick</surname><given-names>K</given-names></name><name><surname>Thornton</surname><given-names>JM</given-names></name></person-group><article-title>Discriminating between homodimeric and monomeric proteins in the crystalline state</article-title><source>Proteins</source><year>2000</year><volume>41</volume><issue>1</issue><fpage>47</fpage><lpage>57</lpage><pub-id pub-id-type="doi">10.1002/1097-0134(20001001)41:1&#x0003c;47::AID-PROT80&#x0003e;3.0.CO;2-8</pub-id><pub-id pub-id-type="pmid">10944393</pub-id></element-citation></ref><ref id="CR9"><label>9.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Elcock</surname><given-names>AH</given-names></name><name><surname>McCammon</surname><given-names>JA</given-names></name></person-group><article-title>Identification of protein oligomerization states by analysis of interface conservation</article-title><source>Proc Natl Acad Sci U S A</source><year>2001</year><volume>98</volume><issue>6</issue><fpage>2990</fpage><lpage>2994</lpage><pub-id pub-id-type="doi">10.1073/pnas.061411798</pub-id><pub-id pub-id-type="pmid">11248019</pub-id></element-citation></ref><ref id="CR10"><label>10.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Guharoy</surname><given-names>M</given-names></name><name><surname>Chakrabarti</surname><given-names>P</given-names></name></person-group><article-title>Conservation and relative importance of residues across protein-protein interfaces</article-title><source>Proc Natl Acad Sci U S A</source><year>2005</year><volume>102</volume><issue>43</issue><fpage>15447</fpage><lpage>15452</lpage><pub-id pub-id-type="doi">10.1073/pnas.0505425102</pub-id><pub-id pub-id-type="pmid">16221766</pub-id></element-citation></ref><ref id="CR11"><label>11.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Valdar</surname><given-names>WS</given-names></name><name><surname>Thornton</surname><given-names>JM</given-names></name></person-group><article-title>Conservation helps to identify biologically relevant crystal contacts</article-title><source>J Mol Biol</source><year>2001</year><volume>313</volume><issue>2</issue><fpage>399</fpage><lpage>416</lpage><pub-id pub-id-type="doi">10.1006/jmbi.2001.5034</pub-id><pub-id pub-id-type="pmid">11800565</pub-id></element-citation></ref><ref id="CR12"><label>12.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>Q</given-names></name><name><surname>Dunbrack</surname><given-names>RL</given-names><suffix>Jr</suffix></name></person-group><article-title>The protein common interface database (ProtCID)--a comprehensive database of interactions of homologous proteins in multiple crystal forms</article-title><source>Nucleic Acids Res</source><year>2011</year><volume>39</volume><issue>Database issue</issue><fpage>D761</fpage><lpage>D770</lpage><pub-id pub-id-type="doi">10.1093/nar/gkq1059</pub-id><pub-id pub-id-type="pmid">21036862</pub-id></element-citation></ref><ref id="CR13"><label>13.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Krissinel</surname><given-names>E</given-names></name><name><surname>Henrick</surname><given-names>K</given-names></name></person-group><article-title>Inference of macromolecular assemblies from crystalline state</article-title><source>J Mol Biol</source><year>2007</year><volume>372</volume><issue>3</issue><fpage>774</fpage><lpage>797</lpage><pub-id pub-id-type="doi">10.1016/j.jmb.2007.05.022</pub-id><pub-id pub-id-type="pmid">17681537</pub-id></element-citation></ref><ref id="CR14"><label>14.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Bernauer</surname><given-names>J</given-names></name><name><surname>Bahadur</surname><given-names>RP</given-names></name><name><surname>Rodier</surname><given-names>F</given-names></name><name><surname>Janin</surname><given-names>J</given-names></name><name><surname>Poupon</surname><given-names>A</given-names></name></person-group><article-title>DiMoVo: a Voronoi tessellation-based method for discriminating crystallographic and biological protein-protein interactions</article-title><source>Bioinformatics</source><year>2008</year><volume>24</volume><issue>5</issue><fpage>652</fpage><lpage>658</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btn022</pub-id><pub-id pub-id-type="pmid">18204058</pub-id></element-citation></ref><ref id="CR15"><label>15.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Duarte</surname><given-names>JM</given-names></name><name><surname>Srebniak</surname><given-names>A</given-names></name><name><surname>Scharer</surname><given-names>MA</given-names></name><name><surname>Capitani</surname><given-names>G</given-names></name></person-group><article-title>Protein interface classification by evolutionary analysis</article-title><source>BMC Bioinformatics</source><year>2012</year><volume>13</volume><fpage>334</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-13-334</pub-id><pub-id pub-id-type="pmid">23259833</pub-id></element-citation></ref><ref id="CR16"><label>16.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Mitra</surname><given-names>P</given-names></name><name><surname>Pal</surname><given-names>D</given-names></name></person-group><article-title>Combining Bayes classification and point group symmetry under Boolean framework for enhanced protein quaternary structure inference</article-title><source>Structure</source><year>2011</year><volume>19</volume><issue>3</issue><fpage>304</fpage><lpage>312</lpage><pub-id pub-id-type="doi">10.1016/j.str.2011.01.009</pub-id><pub-id pub-id-type="pmid">21397182</pub-id></element-citation></ref><ref id="CR17"><label>17.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Zhu</surname><given-names>H</given-names></name><name><surname>Domingues</surname><given-names>FS</given-names></name><name><surname>Sommer</surname><given-names>I</given-names></name><name><surname>Lengauer</surname><given-names>T</given-names></name></person-group><article-title>NOXclass: prediction of protein-protein interaction types</article-title><source>BMC Bioinformatics</source><year>2006</year><volume>7</volume><fpage>27</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-7-27</pub-id><pub-id pub-id-type="pmid">16423290</pub-id></element-citation></ref><ref id="CR18"><label>18.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vangone</surname><given-names>A</given-names></name><name><surname>Spinelli</surname><given-names>R</given-names></name><name><surname>Scarano</surname><given-names>V</given-names></name><name><surname>Cavallo</surname><given-names>L</given-names></name><name><surname>Oliva</surname><given-names>R</given-names></name></person-group><article-title>COCOMAPS: a web application to analyze and visualize contacts at the interface of biomolecular complexes</article-title><source>Bioinformatics</source><year>2011</year><volume>27</volume><issue>20</issue><fpage>2915</fpage><lpage>2916</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btr484</pub-id><pub-id pub-id-type="pmid">21873642</pub-id></element-citation></ref><ref id="CR19"><label>19.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vangone</surname><given-names>A</given-names></name><name><surname>Abdel-Azeim</surname><given-names>S</given-names></name><name><surname>Caputo</surname><given-names>I</given-names></name><name><surname>Sblattero</surname><given-names>D</given-names></name><name><surname>Di Niro</surname><given-names>R</given-names></name><name><surname>Cavallo</surname><given-names>L</given-names></name><name><surname>Oliva</surname><given-names>R</given-names></name></person-group><article-title>Structural basis for the recognition in an idiotype-anti-idiotype antibody complex related to celiac disease</article-title><source>PLoS One</source><year>2014</year><volume>9</volume><issue>7</issue><fpage>e102839</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0102839</pub-id><pub-id pub-id-type="pmid">25076134</pub-id></element-citation></ref><ref id="CR20"><label>20.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Calvanese</surname><given-names>L</given-names></name><name><surname>D'Auria</surname><given-names>G</given-names></name><name><surname>Vangone</surname><given-names>A</given-names></name><name><surname>Falcigno</surname><given-names>L</given-names></name><name><surname>Oliva</surname><given-names>R</given-names></name></person-group><article-title>Analysis of the interface variability in NMR structure ensembles of protein-protein complexes</article-title><source>J Struct Biol</source><year>2016</year><volume>194</volume><issue>3</issue><fpage>317</fpage><lpage>324</lpage><pub-id pub-id-type="doi">10.1016/j.jsb.2016.03.008</pub-id><pub-id pub-id-type="pmid">26968364</pub-id></element-citation></ref><ref id="CR21"><label>21.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Abdel-Azeim</surname><given-names>S</given-names></name><name><surname>Chermak</surname><given-names>E</given-names></name><name><surname>Vangone</surname><given-names>A</given-names></name><name><surname>Oliva</surname><given-names>R</given-names></name><name><surname>Cavallo</surname><given-names>L</given-names></name></person-group><article-title>MDcons: intermolecular contact maps as a tool to analyze the interface of protein complexes from molecular dynamics trajectories</article-title><source>BMC Bioinformatics</source><year>2014</year><volume>15</volume><issue>Suppl 5</issue><fpage>S1</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-15-S5-S1</pub-id></element-citation></ref><ref id="CR22"><label>22.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Lensink</surname><given-names>MF</given-names></name><name><surname>Wodak</surname><given-names>SJ</given-names></name></person-group><article-title>Docking, scoring, and affinity prediction in CAPRI</article-title><source>Proteins</source><year>2013</year><volume>81</volume><issue>12</issue><fpage>2082</fpage><lpage>2095</lpage><pub-id pub-id-type="doi">10.1002/prot.24428</pub-id><pub-id pub-id-type="pmid">24115211</pub-id></element-citation></ref><ref id="CR23"><label>23.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vreven</surname><given-names>T</given-names></name><name><surname>Hwang</surname><given-names>H</given-names></name><name><surname>Weng</surname><given-names>Z</given-names></name></person-group><article-title>Integrating atom-based and residue-based scoring functions for protein-protein docking</article-title><source>Protein Sci</source><year>2011</year><volume>20</volume><issue>9</issue><fpage>1576</fpage><lpage>1586</lpage><pub-id pub-id-type="doi">10.1002/pro.687</pub-id><pub-id pub-id-type="pmid">21739500</pub-id></element-citation></ref><ref id="CR24"><label>24.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vangone</surname><given-names>A</given-names></name><name><surname>Oliva</surname><given-names>R</given-names></name><name><surname>Cavallo</surname><given-names>L</given-names></name></person-group><article-title>CONS-COCOMAPS: a novel tool to measure and visualize the conservation of inter-residue contacts in multiple docking solutions</article-title><source>BMC Bioinformatics</source><year>2012</year><volume>13</volume><issue>Suppl 4</issue><fpage>S19</fpage><pub-id pub-id-type="doi">10.1186/1471-2105-13-S4-S19</pub-id></element-citation></ref><ref id="CR25"><label>25.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vangone</surname><given-names>A</given-names></name><name><surname>Cavallo</surname><given-names>L</given-names></name><name><surname>Oliva</surname><given-names>R</given-names></name></person-group><article-title>Using a consensus approach based on the conservation of inter-residue contacts to rank CAPRI models</article-title><source>Proteins</source><year>2013</year><volume>81</volume><issue>12</issue><fpage>2210</fpage><lpage>2220</lpage><pub-id pub-id-type="doi">10.1002/prot.24423</pub-id><pub-id pub-id-type="pmid">24115176</pub-id></element-citation></ref><ref id="CR26"><label>26.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Oliva</surname><given-names>R</given-names></name><name><surname>Vangone</surname><given-names>A</given-names></name><name><surname>Cavallo</surname><given-names>L</given-names></name></person-group><article-title>Ranking multiple docking solutions based on the conservation of inter-residue contacts</article-title><source>Proteins</source><year>2013</year><volume>81</volume><issue>9</issue><fpage>1571</fpage><lpage>1584</lpage><pub-id pub-id-type="doi">10.1002/prot.24314</pub-id><pub-id pub-id-type="pmid">23609916</pub-id></element-citation></ref><ref id="CR27"><label>27.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chermak</surname><given-names>E</given-names></name><name><surname>Petta</surname><given-names>A</given-names></name><name><surname>Serra</surname><given-names>L</given-names></name><name><surname>Vangone</surname><given-names>A</given-names></name><name><surname>Scarano</surname><given-names>V</given-names></name><name><surname>Cavallo</surname><given-names>L</given-names></name><name><surname>Oliva</surname><given-names>R</given-names></name></person-group><article-title>CONSRANK: a server for the analysis, comparison and ranking of docking models based on inter-residue contacts</article-title><source>Bioinformatics</source><year>2015</year><volume>31</volume><issue>9</issue><fpage>1481</fpage><lpage>1483</lpage><pub-id pub-id-type="doi">10.1093/bioinformatics/btu837</pub-id><pub-id pub-id-type="pmid">25535242</pub-id></element-citation></ref><ref id="CR28"><label>28.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Rodrigues</surname><given-names>JP</given-names></name><name><surname>Trellet</surname><given-names>M</given-names></name><name><surname>Schmitz</surname><given-names>C</given-names></name><name><surname>Kastritis</surname><given-names>P</given-names></name><name><surname>Karaca</surname><given-names>E</given-names></name><name><surname>Melquiond</surname><given-names>AS</given-names></name><name><surname>Bonvin</surname><given-names>AM</given-names></name></person-group><article-title>Clustering biomolecular complexes by residue contacts similarity</article-title><source>Proteins</source><year>2012</year><volume>80</volume><issue>7</issue><fpage>1810</fpage><lpage>1817</lpage><?supplied-pmid 22489062?><pub-id pub-id-type="pmid">22489062</pub-id></element-citation></ref><ref id="CR29"><label>29.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Chermak</surname><given-names>E</given-names></name><name><surname>De Donato</surname><given-names>R</given-names></name><name><surname>Lensink</surname><given-names>MF</given-names></name><name><surname>Petta</surname><given-names>A</given-names></name><name><surname>Serra</surname><given-names>L</given-names></name><name><surname>Scarano</surname><given-names>V</given-names></name><name><surname>Cavallo</surname><given-names>L</given-names></name><name><surname>Oliva</surname><given-names>R</given-names></name></person-group><article-title>Introducing a clustering step in a consensus approach for the scoring of protein-protein docking models</article-title><source>PLoS One</source><year>2016</year><volume>11</volume><issue>11</issue><fpage>e0166460</fpage><pub-id pub-id-type="doi">10.1371/journal.pone.0166460</pub-id><pub-id pub-id-type="pmid">27846259</pub-id></element-citation></ref><ref id="CR30"><label>30.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Vangone</surname><given-names>A</given-names></name><name><surname>Bonvin</surname><given-names>AM</given-names></name></person-group><article-title>Contacts-based prediction of binding affinity in protein-protein complexes</article-title><source>Elife</source><year>2015</year><volume>4</volume><fpage>e07454</fpage><pub-id pub-id-type="doi">10.7554/eLife.07454</pub-id><pub-id pub-id-type="pmid">26193119</pub-id></element-citation></ref><ref id="CR31"><label>31.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kurkcuoglu</surname><given-names>Zeynep</given-names></name><name><surname>Koukos</surname><given-names>Panagiotis I.</given-names></name><name><surname>Citro</surname><given-names>Nevia</given-names></name><name><surname>Trellet</surname><given-names>Mikael E.</given-names></name><name><surname>Rodrigues</surname><given-names>J. P. G. L. M.</given-names></name><name><surname>Moreira</surname><given-names>Irina S.</given-names></name><name><surname>Roel-Touris</surname><given-names>Jorge</given-names></name><name><surname>Melquiond</surname><given-names>Adrien S. J.</given-names></name><name><surname>Geng</surname><given-names>Cunliang</given-names></name><name><surname>Schaarschmidt</surname><given-names>J&#x000f6;rg</given-names></name><name><surname>Xue</surname><given-names>Li C.</given-names></name><name><surname>Vangone</surname><given-names>Anna</given-names></name><name><surname>Bonvin</surname><given-names>A. M. J. J.</given-names></name></person-group><article-title>Performance of HADDOCK and a simple contact-based protein&#x02013;ligand binding affinity predictor in the D3R Grand Challenge 2</article-title><source>Journal of Computer-Aided Molecular Design</source><year>2017</year><volume>32</volume><issue>1</issue><fpage>175</fpage><lpage>185</lpage><pub-id pub-id-type="doi">10.1007/s10822-017-0049-y</pub-id><pub-id pub-id-type="pmid">28831657</pub-id></element-citation></ref><ref id="CR32"><label>32.</label><mixed-citation publication-type="other">Vangone A, Bonvin AM. PRODIGY: a contact-based predictor of binding affinity in protein-protein complexes. Bio-protocols. 2017;7(3). <ext-link ext-link-type="uri" xlink:href="https://bio-protocol.org/e2124">https://bio-protocol.org/e2124</ext-link>.</mixed-citation></ref><ref id="CR33"><label>33.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xue</surname><given-names>LC</given-names></name><name><surname>Rodrigues</surname><given-names>JP</given-names></name><name><surname>Kastritis</surname><given-names>PL</given-names></name><name><surname>Bonvin</surname><given-names>AM</given-names></name><name><surname>Vangone</surname><given-names>A</given-names></name></person-group><article-title>PRODIGY: a web server for predicting the binding affinity of protein-protein complexes</article-title><source>Bioinformatics</source><year>2016</year><volume>32</volume><issue>23</issue><fpage>3676</fpage><lpage>3678</lpage><?supplied-pmid 27503228?><pub-id pub-id-type="pmid">27503228</pub-id></element-citation></ref><ref id="CR34"><label>34.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Baskaran</surname><given-names>K</given-names></name><name><surname>Duarte</surname><given-names>JM</given-names></name><name><surname>Biyani</surname><given-names>N</given-names></name><name><surname>Bliven</surname><given-names>S</given-names></name><name><surname>Capitani</surname><given-names>G</given-names></name></person-group><article-title>A PDB-wide, evolution-based assessment of protein-protein interfaces</article-title><source>BMC Struct Biol</source><year>2014</year><volume>14</volume><fpage>22</fpage><pub-id pub-id-type="doi">10.1186/s12900-014-0022-0</pub-id><pub-id pub-id-type="pmid">25326082</pub-id></element-citation></ref><ref id="CR35"><label>35.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jones</surname><given-names>S</given-names></name><name><surname>Thornton</surname><given-names>JM</given-names></name></person-group><article-title>Principles of protein-protein interactions</article-title><source>Proc Natl Acad Sci U S A</source><year>1996</year><volume>93</volume><issue>1</issue><fpage>13</fpage><lpage>20</lpage><pub-id pub-id-type="doi">10.1073/pnas.93.1.13</pub-id><pub-id pub-id-type="pmid">8552589</pub-id></element-citation></ref><ref id="CR36"><label>36.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Tsai</surname><given-names>CJ</given-names></name><name><surname>Lin</surname><given-names>SL</given-names></name><name><surname>Wolfson</surname><given-names>HJ</given-names></name><name><surname>Nussinov</surname><given-names>R</given-names></name></person-group><article-title>Studies of protein-protein interfaces: a statistical analysis of the hydrophobic effect</article-title><source>Protein Sci</source><year>1997</year><volume>6</volume><issue>1</issue><fpage>53</fpage><lpage>64</lpage><pub-id pub-id-type="doi">10.1002/pro.5560060106</pub-id><pub-id pub-id-type="pmid">9007976</pub-id></element-citation></ref><ref id="CR37"><label>37.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Kastritis</surname><given-names>PL</given-names></name><name><surname>Rodrigues</surname><given-names>JP</given-names></name><name><surname>Folkers</surname><given-names>GE</given-names></name><name><surname>Boelens</surname><given-names>R</given-names></name><name><surname>Bonvin</surname><given-names>AM</given-names></name></person-group><article-title>Proteins feel more than they see: fine-tuning of binding affinity by properties of the non-interacting surface</article-title><source>J Mol Biol</source><year>2014</year><volume>426</volume><issue>14</issue><fpage>2632</fpage><lpage>2652</lpage><pub-id pub-id-type="doi">10.1016/j.jmb.2014.04.017</pub-id><pub-id pub-id-type="pmid">24768922</pub-id></element-citation></ref><ref id="CR38"><label>38.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Marillet</surname><given-names>S</given-names></name><name><surname>Boudinot</surname><given-names>P</given-names></name><name><surname>Cazals</surname><given-names>F</given-names></name></person-group><article-title>High-resolution crystal structures leverage protein binding affinity predictions</article-title><source>Proteins</source><year>2016</year><volume>84</volume><issue>1</issue><fpage>9</fpage><lpage>20</lpage><pub-id pub-id-type="doi">10.1002/prot.24946</pub-id><pub-id pub-id-type="pmid">26471944</pub-id></element-citation></ref><ref id="CR39"><label>39.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>van Zundert</surname><given-names>GC</given-names></name><name><surname>Bonvin</surname><given-names>AM</given-names></name></person-group><article-title>Modeling protein-protein complexes using the HADDOCK webserver &#x0201c;modeling protein complexes with HADDOCK&#x0201d;</article-title><source>Methods Mol Biol</source><year>2014</year><volume>1137</volume><fpage>163</fpage><lpage>179</lpage><pub-id pub-id-type="doi">10.1007/978-1-4939-0366-5_12</pub-id><pub-id pub-id-type="pmid">24573481</pub-id></element-citation></ref><ref id="CR40"><label>40.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Duarte</surname><given-names>JM</given-names></name><name><surname>Biyani</surname><given-names>N</given-names></name><name><surname>Baskaran</surname><given-names>K</given-names></name><name><surname>Capitani</surname><given-names>G</given-names></name></person-group><article-title>An analysis of oligomerization interfaces in transmembrane proteins</article-title><source>BMC Struct Biol</source><year>2013</year><volume>13</volume><fpage>21</fpage><pub-id pub-id-type="doi">10.1186/1472-6807-13-21</pub-id><pub-id pub-id-type="pmid">24134166</pub-id></element-citation></ref><ref id="CR41"><label>41.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Xu</surname><given-names>Q</given-names></name><name><surname>Canutescu</surname><given-names>AA</given-names></name><name><surname>Wang</surname><given-names>G</given-names></name><name><surname>Shapovalov</surname><given-names>M</given-names></name><name><surname>Obradovic</surname><given-names>Z</given-names></name><name><surname>Dunbrack</surname><given-names>RL</given-names><suffix>Jr</suffix></name></person-group><article-title>Statistical analysis of interface similarity in crystals of homologous proteins</article-title><source>J Mol Biol</source><year>2008</year><volume>381</volume><issue>2</issue><fpage>487</fpage><lpage>507</lpage><pub-id pub-id-type="doi">10.1016/j.jmb.2008.06.002</pub-id><pub-id pub-id-type="pmid">18599072</pub-id></element-citation></ref><ref id="CR42"><label>42.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Monod</surname><given-names>J</given-names></name><name><surname>Wyman</surname><given-names>J</given-names></name><name><surname>Changeux</surname><given-names>JP</given-names></name></person-group><article-title>On the nature of allosteric transitions: a plausible model</article-title><source>J Mol Biol</source><year>1965</year><volume>12</volume><fpage>88</fpage><lpage>118</lpage><pub-id pub-id-type="doi">10.1016/S0022-2836(65)80285-6</pub-id><pub-id pub-id-type="pmid">14343300</pub-id></element-citation></ref><ref id="CR43"><label>43.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Dominguez</surname><given-names>C</given-names></name><name><surname>Boelens</surname><given-names>R</given-names></name><name><surname>Bonvin</surname><given-names>AM</given-names></name></person-group><article-title>HADDOCK: a protein-protein docking approach based on biochemical or biophysical information</article-title><source>J Am Chem Soc</source><year>2003</year><volume>125</volume><issue>7</issue><fpage>1731</fpage><lpage>1737</lpage><pub-id pub-id-type="doi">10.1021/ja026939x</pub-id><pub-id pub-id-type="pmid">12580598</pub-id></element-citation></ref><ref id="CR44"><label>44.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Jorgensen</surname><given-names>WL</given-names></name><name><surname>Tirado-Rives</surname><given-names>J</given-names></name></person-group><article-title>The OPLS [optimized potentials for liquid simulations] potential functions for proteins, energy minimizations for crystals of cyclic peptides and crambin</article-title><source>J Am Chem Soc</source><year>1988</year><volume>110</volume><issue>6</issue><fpage>1657</fpage><lpage>1666</lpage><pub-id pub-id-type="doi">10.1021/ja00214a001</pub-id><pub-id pub-id-type="pmid">27557051</pub-id></element-citation></ref><ref id="CR45"><label>45.</label><element-citation publication-type="journal"><person-group person-group-type="author"><name><surname>Fernandez-Recio</surname><given-names>J</given-names></name><name><surname>Totrov</surname><given-names>M</given-names></name><name><surname>Abagyan</surname><given-names>R</given-names></name></person-group><article-title>Identification of protein-protein interaction sites from docking energy landscapes</article-title><source>J Mol Biol</source><year>2004</year><volume>335</volume><issue>3</issue><fpage>843</fpage><lpage>865</lpage><pub-id pub-id-type="doi">10.1016/j.jmb.2003.10.069</pub-id><pub-id pub-id-type="pmid">********</pub-id></element-citation></ref><ref id="CR46"><label>46.</label><mixed-citation publication-type="other">Vangone A, Elez K, Bonvin AM. HADDOCK-refined Biological/Crystallographic protein-protein interfaces. In: SBGrid Data Bank: SBGrid; 2018. <ext-link ext-link-type="uri" xlink:href="https://data.sbgrid.org/dataset/566/">https://data.sbgrid.org/dataset/566/</ext-link>.</mixed-citation></ref></ref-list></back></article>