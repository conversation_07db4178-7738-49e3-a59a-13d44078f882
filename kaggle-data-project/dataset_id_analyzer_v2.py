#!/usr/bin/env python3
"""
Dataset ID Analyzer Script v2

This script analyzes PDFs to identify and classify dataset IDs as DOI or non-DOI,
and extracts preceding English words for analysis. Uses the pdf_utils module for
reusable functions.

Features:
- Identifies DOI dataset IDs (URLs starting with https://doi.org)
- Identifies non-DOI dataset IDs (other identifiers)
- Extracts 50 preceding English words (4+ characters) before dataset IDs
- Tests on PDFs in the data/test/PDF folder
- Provides detailed analysis and classification
"""

import pandas as pd
import os
from pathlib import Path
from pdf_utils import (
    extract_text_from_pdf,
    is_doi_dataset_id,
    is_non_doi_dataset_id,
    classify_dataset_id,
    find_dataset_ids_in_text,
    extract_preceding_words_from_text,
    clean_and_extract_words
)


def analyze_single_pdf(pdf_path, num_preceding_words=50, max_dataset_ids=5):
    """
    Analyze a single PDF to identify dataset IDs and extract preceding words.
    Limits the number of dataset IDs per PDF to avoid excessive duplicates.

    Args:
        pdf_path (Path): Path to the PDF file
        num_preceding_words (int): Number of preceding words to extract
        max_dataset_ids (int): Maximum number of dataset IDs to extract per PDF

    Returns:
        list: List of dictionaries with analysis results
    """
    results = []
    article_id = pdf_path.stem

    print(f"  Processing: {pdf_path.name}")

    try:
        # Extract text from PDF (limit to first 20 pages for efficiency)
        text = extract_text_from_pdf(pdf_path, max_pages=20)

        if not text:
            results.append({
                'article_id': article_id,
                'dataset_id': 'EXTRACTION_FAILED',
                'dataset_type': 'Error',
                'preceding_words': '',
                'word_count': 0
            })
            return results

        # Find dataset IDs in the text
        dataset_ids = find_dataset_ids_in_text(text)

        if not dataset_ids:
            results.append({
                'article_id': article_id,
                'dataset_id': 'Missing',
                'dataset_type': 'Missing',
                'preceding_words': '',
                'word_count': 0
            })
        else:
            # Limit the number of dataset IDs to process
            # Prioritize DOI dataset IDs over non-DOI ones
            doi_ids = [id for id in dataset_ids if classify_dataset_id(id) == 'DOI']
            non_doi_ids = [id for id in dataset_ids if classify_dataset_id(id) == 'non-DOI']

            # Take up to max_dataset_ids, prioritizing DOIs
            selected_ids = []

            # Add DOIs first (limit to 3 to leave room for non-DOI)
            selected_ids.extend(doi_ids[:min(3, max_dataset_ids)])

            # Add non-DOI IDs if we have room
            remaining_slots = max_dataset_ids - len(selected_ids)
            if remaining_slots > 0:
                selected_ids.extend(non_doi_ids[:remaining_slots])

            print(f"    Found {len(dataset_ids)} dataset IDs, processing {len(selected_ids)}")

            # Process each selected dataset ID
            for dataset_id in selected_ids:
                # Classify the dataset ID
                dataset_type = classify_dataset_id(dataset_id)

                # Extract preceding words with improved method
                preceding_words = extract_preceding_words_from_text(text, dataset_id, num_preceding_words)
                preceding_text = ' '.join(preceding_words)

                results.append({
                    'article_id': article_id,
                    'dataset_id': dataset_id,
                    'dataset_type': dataset_type,
                    'preceding_words': preceding_text,
                    'word_count': len(preceding_words)
                })

    except Exception as e:
        print(f"    Error processing {pdf_path.name}: {e}")
        results.append({
            'article_id': article_id,
            'dataset_id': 'PROCESSING_ERROR',
            'dataset_type': 'Error',
            'preceding_words': '',
            'word_count': 0
        })

    return results


def analyze_pdfs_in_folder(pdf_folder_path, max_files=None, num_preceding_words=50, max_dataset_ids=5):
    """
    Analyze PDFs in a folder to identify dataset IDs and extract preceding words.

    Args:
        pdf_folder_path (str): Path to folder containing PDFs
        max_files (int): Maximum number of files to process (None for all)
        num_preceding_words (int): Number of preceding words to extract
        max_dataset_ids (int): Maximum number of dataset IDs to extract per PDF

    Returns:
        pd.DataFrame: DataFrame with analysis results
    """
    pdf_folder = Path(pdf_folder_path)
    if not pdf_folder.exists():
        print(f"Error: Folder {pdf_folder_path} does not exist.")
        return pd.DataFrame()

    all_results = []
    pdf_files = list(pdf_folder.glob("*.pdf"))

    if max_files:
        pdf_files = pdf_files[:max_files]

    total_files = len(list(pdf_folder.glob("*.pdf")))
    print(f"Found {total_files} PDF files, processing {len(pdf_files)}...")

    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"Processing {i}/{len(pdf_files)}: {pdf_file.name}")

        # Analyze this PDF
        pdf_results = analyze_single_pdf(pdf_file, num_preceding_words, max_dataset_ids)
        all_results.extend(pdf_results)

    return pd.DataFrame(all_results)


def analyze_dataset_id_patterns(results_df):
    """
    Analyze patterns in the identified dataset IDs.
    
    Args:
        results_df (pd.DataFrame): DataFrame with analysis results
        
    Returns:
        dict: Dictionary with pattern analysis
    """
    analysis = {}
    
    # Filter out errors and missing
    valid_ids = results_df[
        (~results_df['dataset_id'].isin(['Missing', 'EXTRACTION_FAILED', 'PROCESSING_ERROR']))
    ].copy()
    
    if valid_ids.empty:
        return analysis
    
    # Classify by type
    doi_ids = valid_ids[valid_ids['dataset_type'] == 'DOI']['dataset_id'].tolist()
    non_doi_ids = valid_ids[valid_ids['dataset_type'] == 'non-DOI']['dataset_id'].tolist()
    
    analysis['doi_count'] = len(doi_ids)
    analysis['non_doi_count'] = len(non_doi_ids)
    analysis['doi_examples'] = doi_ids[:5]
    analysis['non_doi_examples'] = non_doi_ids[:5]
    
    # Analyze non-DOI patterns
    non_doi_patterns = {}
    for dataset_id in non_doi_ids:
        # Identify pattern type
        if dataset_id.startswith('CHEMBL'):
            pattern = 'CHEMBL'
        elif dataset_id.startswith('IPR'):
            pattern = 'InterPro'
        elif dataset_id.startswith('PF'):
            pattern = 'Pfam'
        elif dataset_id.startswith('GSE'):
            pattern = 'GEO'
        elif dataset_id.startswith('SRP'):
            pattern = 'SRA_Project'
        elif dataset_id.startswith('EMPIAR'):
            pattern = 'EMPIAR'
        elif dataset_id.startswith('ENSBTAG'):
            pattern = 'Ensembl'
        elif dataset_id.startswith('SAMN'):
            pattern = 'BioSample'
        elif dataset_id.startswith('EPI'):
            pattern = 'GISAID'
        elif dataset_id.startswith('SRR') or dataset_id.startswith('ERR'):
            pattern = 'SRA_Run'
        else:
            pattern = 'Other'
        
        non_doi_patterns[pattern] = non_doi_patterns.get(pattern, 0) + 1
    
    analysis['non_doi_patterns'] = non_doi_patterns
    
    return analysis


def main():
    """Main function to run the PDF analysis."""
    pdf_folder_path = 'data/test/PDF'
    
    if not os.path.exists(pdf_folder_path):
        print(f"Error: Folder {pdf_folder_path} not found.")
        print("Please make sure the PDF folder exists.")
        return
    
    print("Starting PDF Dataset ID Analysis v2...")
    print("=" * 60)
    
    # Analyze PDFs (limit to first 10 for testing, max 3 dataset IDs per PDF)
    results_df = analyze_pdfs_in_folder(pdf_folder_path, max_files=10, num_preceding_words=50, max_dataset_ids=3)
    
    if results_df.empty:
        print("No results to analyze.")
        return
    
    # Save results
    os.makedirs('results', exist_ok=True)
    output_file = 'results/pdf_dataset_analysis_v2.csv'
    results_df.to_csv(output_file, index=False)
    print(f"\nResults saved to: {output_file}")
    
    # Analyze patterns
    pattern_analysis = analyze_dataset_id_patterns(results_df)
    
    # Print summary statistics
    print(f"\nSUMMARY STATISTICS:")
    print("=" * 60)
    print(f"Total PDFs processed: {len(results_df['article_id'].unique())}")
    print(f"Total dataset IDs found: {len(results_df)}")
    
    # Count by dataset type
    type_counts = results_df['dataset_type'].value_counts()
    print(f"\nDataset ID Types:")
    for dtype, count in type_counts.items():
        print(f"  {dtype}: {count}")
    
    # Show pattern analysis
    if pattern_analysis:
        print(f"\nDOI vs Non-DOI Analysis:")
        print(f"  DOI dataset IDs: {pattern_analysis.get('doi_count', 0)}")
        print(f"  Non-DOI dataset IDs: {pattern_analysis.get('non_doi_count', 0)}")
        
        if pattern_analysis.get('non_doi_patterns'):
            print(f"\nNon-DOI Patterns:")
            for pattern, count in pattern_analysis['non_doi_patterns'].items():
                print(f"  {pattern}: {count}")
        
        print(f"\nExamples:")
        if pattern_analysis.get('doi_examples'):
            print(f"  DOI examples: {pattern_analysis['doi_examples'][:3]}")
        if pattern_analysis.get('non_doi_examples'):
            print(f"  Non-DOI examples: {pattern_analysis['non_doi_examples'][:3]}")
    
    # Word extraction statistics
    valid_words = results_df[results_df['word_count'] > 0]
    if not valid_words.empty:
        word_stats = valid_words['word_count'].describe()
        print(f"\nPreceding Words Statistics:")
        print(f"  Mean words extracted: {word_stats['mean']:.1f}")
        print(f"  Max words extracted: {int(word_stats['max'])}")
        print(f"  Min words extracted: {int(word_stats['min'])}")
        print(f"  Records with words: {len(valid_words)}/{len(results_df)}")


if __name__ == "__main__":
    main()
