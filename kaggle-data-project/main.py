"""
Main entry point for the kaggle data project.
Demonstrates usage of numpy, pandas, matplotlib, and pypdf.
Output files are saved to the 'results' folder.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pypdf import PdfReader
import os
from pathlib import Path


def demonstrate_numpy():
    """Demonstrate basic numpy operations."""
    print("=== NumPy Demonstration ===")

    # Create arrays
    arr1 = np.array([1, 2, 3, 4, 5])
    arr2 = np.random.randn(5)

    print(f"Array 1: {arr1}")
    print(f"Array 2: {arr2}")
    print(f"Sum: {arr1 + arr2}")
    print(f"Mean of arr2: {np.mean(arr2):.3f}")
    print(f"Standard deviation of arr2: {np.std(arr2):.3f}")

    # Matrix operations
    matrix = np.random.randn(3, 3)
    print(f"3x3 Random Matrix:\n{matrix}")
    print(f"Matrix determinant: {np.linalg.det(matrix):.3f}")
    print()


def demonstrate_pandas():
    """Demonstrate basic pandas operations."""
    print("=== Pandas Demonstration ===")

    # Create sample data
    data = {
        'Name': ['Alice', 'Bob', 'Charlie', 'Diana', 'Eve'],
        'Age': [25, 30, 35, 28, 32],
        'City': ['New York', 'London', 'Tokyo', 'Paris', 'Sydney'],
        'Salary': [50000, 60000, 70000, 55000, 65000]
    }

    df = pd.DataFrame(data)
    print("Sample DataFrame:")
    print(df)
    print(f"\nDataFrame Info:")
    print(df.info())
    print(f"\nBasic Statistics:")
    print(df.describe())

    # Save to CSV in results folder
    csv_path = Path("results/sample_data.csv")
    csv_path.parent.mkdir(exist_ok=True)
    df.to_csv(csv_path, index=False)
    print(f"Data saved to {csv_path}")
    print()


def demonstrate_matplotlib():
    """Demonstrate basic matplotlib plotting."""
    print("=== Matplotlib Demonstration ===")

    # Create sample data
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)

    # Create plots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    # Plot 1: Line plot
    ax1.plot(x, y1, label='sin(x)', color='blue')
    ax1.plot(x, y2, label='cos(x)', color='red')
    ax1.set_title('Trigonometric Functions')
    ax1.set_xlabel('x')
    ax1.set_ylabel('y')
    ax1.legend()
    ax1.grid(True)

    # Plot 2: Histogram
    data = np.random.normal(0, 1, 1000)
    ax2.hist(data, bins=30, alpha=0.7, color='green')
    ax2.set_title('Normal Distribution')
    ax2.set_xlabel('Value')
    ax2.set_ylabel('Frequency')

    plt.tight_layout()

    # Save plot to results folder
    plot_path = Path("results/sample_plots.png")
    plot_path.parent.mkdir(exist_ok=True)
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Plot saved to {plot_path}")

    # Show plot (comment out if running headless)
    # plt.show()
    plt.close()
    print()


def demonstrate_pypdf():
    """Demonstrate basic pypdf operations."""
    print("=== PyPDF Demonstration ===")

    # Create a simple PDF for demonstration in results folder
    from matplotlib.backends.backend_pdf import PdfPages

    pdf_path = Path("results/sample_document.pdf")
    pdf_path.parent.mkdir(exist_ok=True)

    # Create a simple PDF with matplotlib
    with PdfPages(pdf_path) as pdf:
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.text(0.5, 0.5, 'Sample PDF Document\nCreated with Python',
                ha='center', va='center', fontsize=16,
                transform=ax.transAxes)
        ax.text(0.5, 0.3, 'This demonstrates PDF creation and reading',
                ha='center', va='center', fontsize=12,
                transform=ax.transAxes)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    print(f"PDF created at {pdf_path}")

    # Read the PDF
    try:
        reader = PdfReader(pdf_path)
        print(f"PDF has {len(reader.pages)} page(s)")

        # Extract text from first page
        page = reader.pages[0]
        text = page.extract_text()
        print(f"Extracted text: {text[:100]}...")

    except Exception as e:
        print(f"Error reading PDF: {e}")

    print()


def main():
    """Main function to run all demonstrations."""
    print("Welcome to the Kaggle Data Project!")
    print("This project demonstrates the usage of numpy, pandas, matplotlib, and pypdf.")
    print("Output files will be saved to the 'results/' directory.\n")

    # Run demonstrations
    demonstrate_numpy()
    demonstrate_pandas()
    demonstrate_matplotlib()
    demonstrate_pypdf()

    print("All demonstrations completed!")
    print("Check the 'results/' directory for generated output files.")
    print("Original data remains safely in the 'data/' directory.")


if __name__ == "__main__":
    main()
