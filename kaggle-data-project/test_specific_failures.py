"""
Test the improved extraction on specific failing cases.
"""

import pandas as pd
from extract_all_pdfs import process_pdf

def test_failing_cases():
    """Test specific cases that were failing before improvements."""
    
    # Cases that were showing "DATASET_ID_NOT_FOUND" in the original partial_extraction.csv
    failing_cases = [
        ("10.1002_ece3.6144", "https://doi.org/10.5061/dryad.zw3r22854"),
        ("10.1002_ece3.6303", "https://doi.org/10.5061/dryad.37pvmcvgb"),
        ("10.1002_esp.5058", "https://doi.org/10.5061/dryad.jh9w0vt9t"),
        ("10.1007_s00259-022-06053-8", "https://doi.org/10.7937/k9/tcia.2017.7hs46erv"),
        ("10.1007_s00259-022-06053-8", "https://doi.org/10.7937/tcia.2019.30ilqfcl"),
        ("10.1007_s00382-022-06361-7", "https://doi.org/10.6075/j0154fjj"),
        ("10.1007_s00382-022-06361-7", "https://doi.org/10.6075/j089161b"),
        ("10.1007_s00442-022-05201-z", "https://doi.org/10.5061/dryad.wpzgmsbps")
    ]
    
    print("=== Testing Improved Dataset ID Extraction on Previously Failing Cases ===\n")
    
    results = []
    
    for article_id, dataset_id in failing_cases:
        print(f"Testing: {article_id}")
        print(f"Dataset ID: {dataset_id}")
        
        try:
            result = process_pdf(article_id, dataset_id)
            status = result[2]
            
            if "DATASET_ID_NOT_FOUND" in status:
                print(f"❌ Still not found: {status}")
            elif "ERROR" in status:
                print(f"⚠️  Error: {status}")
            elif "PDF_NOT_FOUND" in status:
                print(f"📄 PDF not available: {status}")
            else:
                print(f"✅ Found: {status}")
            
            results.append(result)
            
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
            results.append((article_id, dataset_id, f"EXCEPTION: {str(e)}"))
        
        print("-" * 80)
        print()
    
    # Save results
    results_df = pd.DataFrame(results, columns=['article_id', 'dataset_id', 'preceding_words'])
    results_df.to_csv('results/test_failing_cases_results.csv', index=False)
    
    # Summary
    total = len(results)
    found = len([r for r in results if not any(x in r[2] for x in ['NOT_FOUND', 'ERROR', 'PDF_NOT_FOUND'])])
    
    print(f"📊 Summary:")
    print(f"Total tested: {total}")
    print(f"Successfully found: {found}")
    print(f"Success rate: {(found/total)*100:.1f}%")
    
    return results

if __name__ == "__main__":
    test_failing_cases()
