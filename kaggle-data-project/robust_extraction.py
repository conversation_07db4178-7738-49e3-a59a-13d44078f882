"""
Script robusto para extrair dataset_ids de TODOS os PDFs com timeout e recuperação de erros.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm
import signal
import time


class TimeoutError(Exception):
    pass


def timeout_handler(signum, frame):
    raise TimeoutError("Timeout na leitura do PDF")


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]  # Versão completa
    
    # Se começar com https://, adicionar versão sem https://
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        # Se contém doi.org, adicionar apenas a parte após doi.org/
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
    
    # Se não começar com https:// mas contém doi.org, adicionar a parte após doi.org/
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
    
    return variations


def extract_preceding_words(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto."""
    try:
        # Escapar caracteres especiais para regex
        escaped_target = re.escape(target)
        
        # Procurar o target no texto (case insensitive)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            # Dividir em palavras e pegar as últimas num_words
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
    except Exception:
        pass
    
    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias do texto."""
    try:
        words = re.findall(r'\b\w+\b', text)
        
        if len(words) < num_words:
            return ' '.join(words)
        
        # Escolher posição inicial aleatória
        start_pos = random.randint(0, len(words) - num_words)
        return ' '.join(words[start_pos:start_pos + num_words])
    except Exception:
        return "ERROR_EXTRACTING_RANDOM_WORDS"


def process_pdf_with_timeout(article_id, dataset_id, data_dir="data", timeout_seconds=30):
    """Processa um PDF individual com timeout."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"
    
    if not pdf_path.exists():
        return article_id, dataset_id, "PDF_NOT_FOUND"
    
    # Configurar timeout
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(timeout_seconds)
    
    try:
        # Carregar PDF
        reader = PdfReader(pdf_path)
        
        # Extrair texto de todas as páginas (limitado para evitar PDFs muito grandes)
        full_text = ""
        max_pages = min(len(reader.pages), 50)  # Limitar a 50 páginas
        
        for i in range(max_pages):
            try:
                page_text = reader.pages[i].extract_text()
                full_text += page_text + "\n"
                
                # Limitar tamanho do texto para evitar problemas de memória
                if len(full_text) > 500000:  # 500KB de texto
                    break
            except Exception:
                continue
        
        # Cancelar timeout
        signal.alarm(0)
        
        if dataset_id == "Missing":
            # Para casos Missing, extrair 10 palavras aleatórias
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Tentar encontrar o dataset_id e suas variações
        variations = normalize_dataset_id(dataset_id)
        
        for variation in variations:
            preceding_words = extract_preceding_words(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        # Se não encontrou nenhuma variação
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except TimeoutError:
        signal.alarm(0)
        return article_id, dataset_id, "TIMEOUT_ERROR"
    except Exception as e:
        signal.alarm(0)
        return article_id, dataset_id, f"ERROR: {str(e)[:100]}"


def load_existing_results(results_dir="results"):
    """Carrega resultados existentes se houver."""
    results_path = Path(results_dir) / "complete_pdf_extraction_results.csv"
    partial_files = list(Path(results_dir).glob("partial_extraction_*.csv"))
    
    if results_path.exists():
        print(f"Encontrado arquivo de resultados existente: {results_path}")
        return pd.read_csv(results_path)
    elif partial_files:
        # Encontrar o arquivo parcial mais recente
        latest_partial = max(partial_files, key=lambda x: x.stat().st_mtime)
        print(f"Encontrado arquivo parcial: {latest_partial}")
        return pd.read_csv(latest_partial)
    
    return None


def main():
    """Função principal para processar TODOS os PDFs com recuperação."""
    print("=== Extração Robusta de Dataset IDs ===")
    print("Este script processará TODOS os PDFs com timeout e recuperação de erros.\n")
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    total_files = len(labels_df)
    
    print(f"Total de entradas a processar: {total_files}")
    
    # Verificar se há resultados existentes
    existing_results = load_existing_results()
    start_idx = 0
    
    if existing_results is not None:
        start_idx = len(existing_results)
        print(f"Continuando do arquivo {start_idx + 1}/{total_files}")
        results = existing_results.values.tolist()
    else:
        results = []
    
    print(f"\nIniciando processamento a partir do arquivo {start_idx + 1}...")
    
    # Processar arquivos restantes
    remaining_df = labels_df.iloc[start_idx:]
    
    # Usar tqdm para barra de progresso
    for idx, row in tqdm(remaining_df.iterrows(), 
                        total=len(remaining_df), 
                        desc="Processando PDFs",
                        initial=0):
        
        article_id = row['article_id']
        dataset_id = row['dataset_id']
        
        result = process_pdf_with_timeout(article_id, dataset_id, timeout_seconds=30)
        results.append(result)
        
        # Salvar resultados parciais a cada 50 arquivos
        if (len(results)) % 50 == 0:
            partial_df = pd.DataFrame(results, columns=[
                'article_id', 'dataset_id', 'preceding_words'
            ])
            
            results_dir = Path("results")
            results_dir.mkdir(exist_ok=True)
            
            partial_path = results_dir / f"partial_extraction_{len(results)}.csv"
            partial_df.to_csv(partial_path, index=False, encoding='utf-8')
            print(f"\nSalvamento parcial: {partial_path}")
    
    # Criar DataFrame final com resultados
    results_df = pd.DataFrame(results, columns=[
        'article_id', 'dataset_id', 'preceding_words'
    ])
    
    # Salvar resultados finais
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    output_path = results_dir / "complete_pdf_extraction_results.csv"
    results_df.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados finais salvos em: {output_path}")
    
    # Mostrar estatísticas finais
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR|TIMEOUT', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    not_found = total - found
    
    print(f"\n📊 Estatísticas Finais:")
    print(f"Total de entradas: {total}")
    print(f"Dataset IDs encontrados: {found - missing} ({((found - missing) / max(1, total - missing) * 100):.1f}% dos não-Missing)")
    print(f"Casos Missing: {missing}")
    print(f"Não encontrados/Erros/Timeouts: {not_found}")
    
    # Contar tipos de erro
    error_types = results_df['preceding_words'].value_counts()
    print(f"\nTipos de erro mais comuns:")
    for error_type in ['PDF_NOT_FOUND', 'DATASET_ID_NOT_FOUND', 'TIMEOUT_ERROR']:
        if error_type in error_types:
            print(f"  {error_type}: {error_types[error_type]}")
    
    print("🎉 Processamento completo finalizado!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Processamento interrompido pelo usuário.")
        print("Os resultados parciais foram salvos na pasta 'results/'.")
    except Exception as e:
        print(f"\n❌ Erro durante o processamento: {e}")
        import traceback
        traceback.print_exc()
