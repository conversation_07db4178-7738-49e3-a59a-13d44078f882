"""
Script simples para extrair dataset_ids dos PDFs.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import Pdf<PERSON><PERSON><PERSON>


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]  # Versão completa
    
    # Se começar com https://, adicionar versão sem https://
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        # Se contém doi.org, adicionar apenas a parte após doi.org/
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
    
    # Se não começar com https:// mas contém doi.org, adicionar a parte após doi.org/
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
    
    return variations


def extract_preceding_words(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto."""
    # Escapar caracteres especiais para regex
    escaped_target = re.escape(target)
    
    # Procurar o target no texto (case insensitive)
    pattern = rf'(.+?)\b{escaped_target}\b'
    match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
    
    if match:
        preceding_text = match.group(1)
        # Dividir em palavras e pegar as últimas num_words
        words = re.findall(r'\b\w+\b', preceding_text)
        if len(words) >= num_words:
            return ' '.join(words[-num_words:])
        else:
            return ' '.join(words)
    
    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias do texto."""
    words = re.findall(r'\b\w+\b', text)
    
    if len(words) < num_words:
        return ' '.join(words)
    
    # Escolher posição inicial aleatória
    start_pos = random.randint(0, len(words) - num_words)
    return ' '.join(words[start_pos:start_pos + num_words])


def process_pdf(article_id, dataset_id, data_dir="data"):
    """Processa um PDF individual."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"
    
    if not pdf_path.exists():
        return article_id, dataset_id, "PDF_NOT_FOUND"
    
    try:
        # Carregar PDF
        reader = PdfReader(pdf_path)
        
        # Extrair texto de todas as páginas
        full_text = ""
        for page in reader.pages:
            full_text += page.extract_text() + "\n"
        
        if dataset_id == "Missing":
            # Para casos Missing, extrair 10 palavras aleatórias
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Tentar encontrar o dataset_id e suas variações
        variations = normalize_dataset_id(dataset_id)
        
        for variation in variations:
            preceding_words = extract_preceding_words(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        # Se não encontrou nenhuma variação
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)}"


def main():
    """Função principal."""
    print("=== Extração Simples de Dataset IDs ===")
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    
    # Processar todos os arquivos
    sample_df = labels_df
    
    print(f"Processando {len(sample_df)} arquivos...")

    results = []

    for idx, row in sample_df.iterrows():
        article_id = row['article_id']
        dataset_id = row['dataset_id']

        if (idx + 1) % 50 == 0:  # Progress update a cada 50 arquivos
            print(f"Processando {idx + 1}/{len(sample_df)}: {article_id}")

        result = process_pdf(article_id, dataset_id)
        results.append(result)

        # Salvar progresso a cada 100 arquivos
        if (idx + 1) % 100 == 0:
            temp_df = pd.DataFrame(results, columns=[
                'article_id', 'dataset_id', 'preceding_words'
            ])

            results_dir = Path("results")
            results_dir.mkdir(exist_ok=True)

            temp_path = results_dir / f"progress_extraction_{idx + 1}.csv"
            temp_df.to_csv(temp_path, index=False, encoding='utf-8')
            print(f"Progresso salvo: {temp_path}")
    
    # Criar DataFrame com resultados
    results_df = pd.DataFrame(results, columns=[
        'article_id', 'dataset_id', 'preceding_words'
    ])
    
    # Salvar resultados
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    output_path = results_dir / "complete_simple_extraction.csv"
    results_df.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados salvos em: {output_path}")
    
    # Mostrar estatísticas
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    
    print(f"\nEstatísticas:")
    print(f"Total de entradas: {total}")
    print(f"Dataset IDs encontrados: {found - missing}")
    print(f"Casos Missing: {missing}")
    print(f"Não encontrados/Erros: {total - found}")
    
    # Mostrar alguns exemplos
    print(f"\nPrimeiros 5 resultados:")
    for idx, row in results_df.head().iterrows():
        print(f"  {row['article_id']}: {row['preceding_words'][:50]}...")


if __name__ == "__main__":
    main()
