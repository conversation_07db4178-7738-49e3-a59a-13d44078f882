"""
Script de extração em lotes - processa em grupos pequenos para evitar travamentos.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm
import time


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]
    
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
    
    return variations


def extract_preceding_words(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto."""
    try:
        escaped_target = re.escape(target)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
    except Exception:
        pass
    
    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias do texto."""
    try:
        words = re.findall(r'\b\w+\b', text)
        
        if len(words) < num_words:
            return ' '.join(words)
        
        start_pos = random.randint(0, len(words) - num_words)
        return ' '.join(words[start_pos:start_pos + num_words])
    except Exception:
        return "ERROR_EXTRACTING_RANDOM_WORDS"


def process_pdf_safe(article_id, dataset_id, data_dir="data"):
    """Processa um PDF com tratamento de erro mais agressivo."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"
    
    if not pdf_path.exists():
        return article_id, dataset_id, "PDF_NOT_FOUND"
    
    try:
        # Tentar carregar PDF com timeout implícito
        start_time = time.time()
        reader = PdfReader(pdf_path)
        
        # Se demorou mais de 10 segundos só para carregar, pular
        if time.time() - start_time > 10:
            return article_id, dataset_id, "SLOW_PDF_LOAD"
        
        # Extrair texto apenas das primeiras páginas
        full_text = ""
        max_pages = min(len(reader.pages), 10)  # Apenas 10 primeiras páginas
        
        for i in range(max_pages):
            try:
                page_start = time.time()
                page_text = reader.pages[i].extract_text()
                
                # Se uma página demora mais de 5 segundos, pular
                if time.time() - page_start > 5:
                    break
                    
                full_text += page_text + "\n"
                
                # Limitar tamanho do texto
                if len(full_text) > 100000:  # 100KB
                    break
                    
            except Exception:
                continue
        
        if dataset_id == "Missing":
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Tentar encontrar o dataset_id
        variations = normalize_dataset_id(dataset_id)
        
        for variation in variations:
            preceding_words = extract_preceding_words(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)[:50]}"


def process_batch(batch_df, batch_num):
    """Processa um lote de PDFs."""
    print(f"\n--- Processando Lote {batch_num} ({len(batch_df)} arquivos) ---")
    
    results = []
    
    for idx, row in tqdm(batch_df.iterrows(), total=len(batch_df), desc=f"Lote {batch_num}"):
        article_id = row['article_id']
        dataset_id = row['dataset_id']
        
        result = process_pdf_safe(article_id, dataset_id)
        results.append(result)
    
    return results


def main():
    """Função principal para processar em lotes."""
    print("=== Extração em Lotes de Dataset IDs ===")
    print("Processamento em lotes pequenos para evitar travamentos.\n")
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    total_files = len(labels_df)
    
    print(f"Total de entradas: {total_files}")
    
    # Dividir em lotes de 100 arquivos
    batch_size = 100
    num_batches = (total_files + batch_size - 1) // batch_size
    
    print(f"Dividindo em {num_batches} lotes de {batch_size} arquivos cada")
    
    all_results = []
    
    for batch_num in range(num_batches):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, total_files)
        
        batch_df = labels_df.iloc[start_idx:end_idx]
        
        try:
            batch_results = process_batch(batch_df, batch_num + 1)
            all_results.extend(batch_results)
            
            # Salvar resultados parciais após cada lote
            partial_df = pd.DataFrame(all_results, columns=[
                'article_id', 'dataset_id', 'preceding_words'
            ])
            
            results_dir = Path("results")
            results_dir.mkdir(exist_ok=True)
            
            partial_path = results_dir / f"batch_extraction_progress_{len(all_results)}.csv"
            partial_df.to_csv(partial_path, index=False, encoding='utf-8')
            
            print(f"✅ Lote {batch_num + 1} concluído. Progresso salvo: {partial_path}")
            
        except Exception as e:
            print(f"❌ Erro no lote {batch_num + 1}: {e}")
            continue
    
    # Salvar resultados finais
    results_df = pd.DataFrame(all_results, columns=[
        'article_id', 'dataset_id', 'preceding_words'
    ])
    
    output_path = Path("results") / "complete_batch_extraction_results.csv"
    results_df.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados finais salvos em: {output_path}")
    
    # Estatísticas
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR|SLOW', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    
    print(f"\n📊 Estatísticas Finais:")
    print(f"Total processado: {total}")
    print(f"Dataset IDs encontrados: {found - missing}")
    print(f"Casos Missing: {missing}")
    print(f"Problemas/Erros: {total - found}")
    
    # Mostrar alguns sucessos
    success_cases = results_df[
        (~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR|SLOW', na=False)) &
        (results_df['dataset_id'] != 'Missing')
    ]
    
    if len(success_cases) > 0:
        print(f"\n🎯 Exemplos de sucessos:")
        for idx, row in success_cases.head(3).iterrows():
            print(f"  📄 {row['article_id']}")
            print(f"  📝 {row['preceding_words']}")
            print()
    
    print("🎉 Processamento em lotes concluído!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Processamento interrompido.")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
        import traceback
        traceback.print_exc()
