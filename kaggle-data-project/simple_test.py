#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test the normalize function
def test_normalize():
    import re
    
    def clean_text_for_urls(text):
        """Clean text to handle URLs split across lines and other formatting issues."""
        # Fix common URL splits
        text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
        text = re.sub(r'doi\.org/\s*\n\s*', 'doi.org/', text)
        text = re.sub(r'https://doi\.\s+org/', 'https://doi.org/', text)
        text = re.sub(r'10\.\d+/\s*\n\s*', '10.', text)
        
        # Fix dryad URLs specifically
        text = re.sub(r'dryad\.\s*\n\s*', 'dryad.', text)
        text = re.sub(r'10\.5061/\s*\n\s*dryad\.', '10.5061/dryad.', text)
        
        # Fix other common splits in dataset IDs
        text = re.sub(r'(\w+)\s*\n\s*(\w+)', r'\1\2', text)
        text = re.sub(r'(\d+)\s*\n\s*(\d+)', r'\1\2', text)
        
        # Remove excessive whitespace but preserve single spaces
        text = re.sub(r'\s+', ' ', text)
        
        return text

    def normalize_dataset_id(dataset_id):
        """Normaliza o dataset_id para diferentes variações."""
        if dataset_id == "Missing":
            return []
        
        variations = [dataset_id]  # Versão completa
        
        # Se começar com https://, adicionar versão sem https://
        if dataset_id.startswith("https://"):
            without_https = dataset_id.replace("https://", "")
            variations.append(without_https)
            
            # Se contém doi.org, adicionar apenas a parte após doi.org/
            if "doi.org/" in without_https:
                after_doi = without_https.split("doi.org/", 1)[1]
                variations.append(after_doi)

            # Adicionar versão com doi: prefix
            variations.append(f"doi:{dataset_id}")
            variations.append(f"doi: {dataset_id}")
            variations.append(f"doi:https://{without_https}")
        
        # Se não começar com https:// mas contém doi.org, adicionar a parte após doi.org/
        elif "doi.org/" in dataset_id:
            after_doi = dataset_id.split("doi.org/", 1)[1]
            variations.append(after_doi)
            variations.append(f"https://{dataset_id}")
            variations.append(f"doi:{dataset_id}")
        
        # Adicionar variações específicas para DOIs
        if "10." in dataset_id:
            # Extrair apenas a parte do DOI
            doi_match = re.search(r'10\.\d+/[\w\d\.-]+', dataset_id)
            if doi_match:
                doi_part = doi_match.group()
                variations.append(doi_part)
                variations.append(f"doi:{doi_part}")
                variations.append(f"doi: {doi_part}")
        
        # Adicionar variações para dryad específicamente
        if "dryad" in dataset_id.lower():
            # Extrair apenas o identificador após dryad.
            dryad_match = re.search(r'dryad\.(\w+)', dataset_id, re.IGNORECASE)
            if dryad_match:
                dryad_id = dryad_match.group(1)
                variations.append(dryad_id)
                variations.append(f"dryad.{dryad_id}")
        
        # Remover duplicatas mantendo ordem
        seen = set()
        unique_variations = []
        for var in variations:
            if var not in seen:
                seen.add(var)
                unique_variations.append(var)
        
        return unique_variations

    # Test cases
    test_cases = [
        "https://doi.org/10.5061/dryad.zw3r22854",
        "https://doi.org/10.5061/dryad.37pvmcvgb",
        "https://doi.org/10.7937/k9/tcia.2017.7hs46erv"
    ]
    
    for dataset_id in test_cases:
        print(f"\nTesting: {dataset_id}")
        variations = normalize_dataset_id(dataset_id)
        for i, var in enumerate(variations):
            print(f"  {i+1}. {var}")

if __name__ == "__main__":
    test_normalize()
