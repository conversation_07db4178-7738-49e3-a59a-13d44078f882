#!/usr/bin/env python3
"""
Test script to verify the fixes made to extract_all_pdfs.py
"""

import re
from extract_all_pdfs import clean_text_for_urls, extract_non_reference_text, extract_random_words_from_page

def test_hyphenated_words():
    """Test that hyphenated words across lines are properly joined."""
    print("=== Testing Hyphenated Words ===")
    
    # Test case 1: Simple hyphenated word
    test_text1 = "This is a hyphen-\nated word in the text."
    cleaned1 = clean_text_for_urls(test_text1)
    print(f"Original: {repr(test_text1)}")
    print(f"Cleaned:  {repr(cleaned1)}")
    print(f"Expected: 'This is a hyphenated word in the text.'")
    print(f"Correct:  {'hyphenated' in cleaned1 and 'hyphen-' not in cleaned1}")
    print()
    
    # Test case 2: Multiple hyphenated words
    test_text2 = "The pre-\nprocessing and post-\nanalysis steps are important."
    cleaned2 = clean_text_for_urls(test_text2)
    print(f"Original: {repr(test_text2)}")
    print(f"Cleaned:  {repr(cleaned2)}")
    print(f"Expected: 'The preprocessing and postanalysis steps are important.'")
    print(f"Correct:  {'preprocessing' in cleaned2 and 'postanalysis' in cleaned2}")
    print()
    
    # Test case 3: DOI with hyphenated words
    test_text3 = "The data-\nset is available at https://doi.org/10.1234/test-\ndata.2023"
    cleaned3 = clean_text_for_urls(test_text3)
    print(f"Original: {repr(test_text3)}")
    print(f"Cleaned:  {repr(cleaned3)}")
    print(f"Expected: 'The dataset is available at https://doi.org/10.1234/testdata.2023'")
    print(f"Correct:  {'dataset' in cleaned3 and 'testdata.2023' in cleaned3}")
    print()

def test_reference_extraction():
    """Test that non-reference text extraction works."""
    print("=== Testing Reference Text Extraction ===")
    
    # Test case: Page with references section
    test_page = """
    This is the main content of the paper.
    
    DATA AVAILABILITY STATEMENT
    The data used in this study is available at https://doi.org/10.1234/example.
    
    REFERENCES
    1. Smith, J. (2023). A study on data. Journal of Data, 1(1), 1-10.
    2. Jones, A. (2022). Another paper. Science Journal, 2(2), 20-30.
    """
    
    non_ref_text = extract_non_reference_text(test_page)
    print(f"Original length: {len(test_page)}")
    print(f"Non-ref length: {len(non_ref_text)}")
    print(f"Contains main content: {'main content' in non_ref_text}")
    print(f"Contains DATA AVAILABILITY: {'DATA AVAILABILITY' in non_ref_text}")
    print(f"Excludes references: {'Smith, J.' not in non_ref_text}")
    print()

def test_random_words_from_page():
    """Test that random word extraction avoids references."""
    print("=== Testing Random Words from Page ===")
    
    # Test case: Page with both content and references
    test_page = """
    INTRODUCTION
    This paper presents a comprehensive analysis of climate data collected over multiple years.
    The methodology involves statistical modeling and machine learning techniques.
    
    METHODS
    We used advanced computational methods to process the environmental data.
    The analysis framework incorporates multiple validation steps.
    
    REFERENCES
    1. Climate Research Group (2023). Global temperature trends. Nature Climate, 15(3), 45-67.
    2. Environmental Data Consortium (2022). Methodology for climate analysis. Science, 380(6645), 123-145.
    """
    
    # Extract random words
    random_words = extract_random_words_from_page(test_page, num_words=10)
    print(f"Random words extracted: {random_words}")
    
    # Check if words are from content, not references
    reference_words = ['Climate', 'Research', 'Group', 'Global', 'temperature', 'trends', 'Nature', 'Environmental', 'Data', 'Consortium', 'Methodology']
    content_words = ['comprehensive', 'analysis', 'climate', 'collected', 'methodology', 'statistical', 'modeling', 'machine', 'learning', 'techniques', 'advanced', 'computational', 'methods', 'process', 'environmental', 'framework', 'incorporates', 'validation']
    
    extracted_words = random_words.split()
    has_reference_words = any(word in reference_words for word in extracted_words)
    has_content_words = any(word in content_words for word in extracted_words)
    
    print(f"Contains reference words: {has_reference_words}")
    print(f"Contains content words: {has_content_words}")
    print(f"Preference for content: {has_content_words and not has_reference_words}")
    print()

if __name__ == "__main__":
    print("Testing fixes to PDF extraction script...\n")
    
    test_hyphenated_words()
    test_reference_extraction()
    test_random_words_from_page()
    
    print("=== Test Summary ===")
    print("1. Hyphenated words should be properly joined without hyphens")
    print("2. Reference sections should be excluded from text extraction")
    print("3. Random word extraction should prefer content over references")
    print("\nIf all tests show expected behavior, the fixes are working correctly!")
