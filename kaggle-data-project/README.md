# Kaggle Data Project

A comprehensive data analysis toolkit built with Python, featuring utilities for data processing, visualization, and PDF handling. This project is ready to use with `uv` and includes popular data science libraries: numpy, pandas, matplotlib, and pypdf.

## Features

- **Data Processing**: Comprehensive data manipulation and analysis with pandas and numpy
- **Visualization**: Create beautiful plots and charts with matplotlib
- **PDF Handling**: Extract text, split, merge, and analyze PDF documents
- **Modern Python**: Built with Python 3.12+ and managed with uv
- **Ready to Use**: Pre-configured project structure with examples
- **Organized Output**: Results are saved to the `results/` folder while preserving original data in `data/`

## Quick Start

### Prerequisites

- Python 3.12+
- [uv](https://docs.astral.sh/uv/) package manager

### Installation

1. Clone or download this project
2. Navigate to the project directory:
   ```bash
   cd kaggle-data-project
   ```

3. The project is already set up with uv! The virtual environment and dependencies are ready to use.

### Running the Examples

Run the main demonstration:
```bash
uv run python main.py
```

Run the basic usage examples:
```bash
uv run python examples/basic_usage.py
```

## Project Structure

```
kaggle-data-project/
├── src/
│   └── kaggle_data_project/
│       ├── __init__.py
│       ├── data_processor.py    # Data processing utilities
│       ├── visualizer.py        # Visualization tools
│       └── pdf_handler.py       # PDF handling utilities
├── examples/
│   └── basic_usage.py          # Usage examples
├── data/                       # Input data files (preserved)
├── results/                    # Generated output files
├── notebooks/                  # Jupyter notebooks
├── tests/                      # Unit tests
├── main.py                     # Main demonstration script
├── pyproject.toml             # Project configuration
└── README.md                  # This file
```

## Core Components

### DataProcessor

Handle data loading, cleaning, and basic analysis:

```python
from kaggle_data_project import DataProcessor

processor = DataProcessor()
data = processor.load_csv('your_data.csv')
info = processor.basic_info()
cleaned_data = processor.clean_data(drop_duplicates=True)
processor.save_data('results/cleaned_data.csv')  # Saves to results folder
```

### Visualizer

Create various types of plots and visualizations:

```python
from kaggle_data_project import Visualizer

viz = Visualizer(figsize=(12, 8))
viz.line_plot(data, 'x', 'y', title='My Plot', save_path='results/plot.png')
viz.scatter_plot(data, 'x', 'y', color='category')
viz.histogram(data, 'column', bins=30)
viz.correlation_heatmap(data)
```

### PDFHandler

Work with PDF documents:

```python
from kaggle_data_project import PDFHandler

pdf = PDFHandler()
pdf.load_pdf('data/document.pdf')  # Load from data folder
text = pdf.extract_text()
results = pdf.search_text('keyword')
pdf.save_text_to_file('results/extracted_text.txt')  # Save to results folder
```

## Dependencies

This project includes the following key dependencies:

- **numpy** (≥2.3.0): Numerical computing
- **pandas** (≥2.3.0): Data manipulation and analysis
- **matplotlib** (≥3.10.3): Plotting and visualization
- **pypdf** (≥5.6.0): PDF processing

All dependencies are automatically managed by uv.

## Folder Organization

- **`data/`**: Store your input data files here. This folder is preserved and never modified by the scripts.
- **`results/`**: All output files (plots, processed data, extracted text, etc.) are saved here.
- **`src/`**: Source code for the project modules.
- **`examples/`**: Example scripts showing how to use the project.
- **`notebooks/`**: Jupyter notebooks for interactive analysis.
- **`tests/`**: Unit tests for the project.

## Usage Examples

### Data Analysis Workflow

```python
from kaggle_data_project import DataProcessor, Visualizer

# Load and process data
processor = DataProcessor()
data = processor.load_csv('data/sales_data.csv')  # Load from data folder
data = processor.clean_data(drop_duplicates=True, fill_na_method='drop')

# Get insights
print(processor.basic_info())
print(processor.get_numerical_summary())

# Create visualizations (saved to results folder)
viz = Visualizer()
viz.line_plot(data, 'date', 'sales', title='Sales Over Time',
              save_path='results/sales_trend.png')
viz.histogram(data, 'sales', title='Sales Distribution',
              save_path='results/sales_dist.png')
```

### PDF Text Extraction

```python
from kaggle_data_project import PDFHandler

pdf = PDFHandler()
pdf.load_pdf('data/research_paper.pdf')  # Load from data folder

# Get document info
info = pdf.get_pdf_info()
print(f"Document has {info['num_pages']} pages")

# Extract and save text
text = pdf.extract_text(page_numbers=[0, 1, 2])  # First 3 pages
pdf.save_text_to_file('results/extracted_text.txt')  # Save to results folder

# Search for keywords
results = pdf.search_text('machine learning', case_sensitive=False)
for result in results:
    print(f"Found on page {result['page']}: {result['context']}")
```