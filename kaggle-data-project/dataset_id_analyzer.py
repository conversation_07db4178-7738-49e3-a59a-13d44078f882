#!/usr/bin/env python3
"""
Dataset ID Analyzer Script

This script analyzes PDFs to identify and classify dataset IDs as DOI or non-DOI,
and extracts preceding English words for analysis.

Features:
- Identifies DOI dataset IDs (URLs starting with https://doi.org)
- Identifies non-DOI dataset IDs (other identifiers)
- Extracts 50 preceding English words (4+ characters) before dataset IDs
- Tests on PDFs in the data/test/PDF folder
- Provides detailed analysis and classification
"""

import pandas as pd
import re
from collections import Counter
import os
import pypdf
from pathlib import Path

# For English word validation
try:
    import nltk
    from nltk.corpus import words
    # Download words corpus if not already present
    try:
        nltk.data.find('corpora/words')
    except LookupError:
        print("Downloading NLTK words corpus...")
        nltk.download('words', quiet=True)

    # Load English words set
    english_words = set(word.lower() for word in words.words())
    ENGLISH_DICT_AVAILABLE = True
    print(f"Loaded {len(english_words)} English words from NLTK")
except ImportError:
    print("NLTK not available. Install with: pip install nltk")
    print("Will proceed without English word filtering.")
    ENGLISH_DICT_AVAILABLE = False
    english_words = set()
except Exception as e:
    print(f"Error loading English dictionary: {e}")
    print("Will proceed without English word filtering.")
    ENGLISH_DICT_AVAILABLE = False
    english_words = set()

def is_doi_dataset_id(dataset_id):
    """
    Check if a dataset ID is a DOI (starts with https://doi.org).
    
    Args:
        dataset_id (str): The dataset ID to check
        
    Returns:
        bool: True if it's a DOI, False otherwise
    """
    if pd.isna(dataset_id) or dataset_id == '' or dataset_id == 'Missing':
        return False
    
    return str(dataset_id).startswith('https://doi.org')

def is_non_doi_dataset_id(dataset_id):
    """
    Check if a dataset ID is a non-DOI identifier.
    
    Args:
        dataset_id (str): The dataset ID to check
        
    Returns:
        bool: True if it's a non-DOI identifier, False otherwise
    """
    if pd.isna(dataset_id) or dataset_id == '' or dataset_id == 'Missing':
        return False
    
    # Not a DOI and not missing/empty
    return not str(dataset_id).startswith('https://doi.org')

def classify_dataset_id(dataset_id):
    """
    Classify a dataset ID as DOI, non-DOI, or missing.
    
    Args:
        dataset_id (str): The dataset ID to classify
        
    Returns:
        str: 'DOI', 'non-DOI', or 'Missing'
    """
    if pd.isna(dataset_id) or dataset_id == '' or dataset_id == 'Missing':
        return 'Missing'
    elif str(dataset_id).startswith('https://doi.org'):
        return 'DOI'
    else:
        return 'non-DOI'

def extract_text_from_pdf(pdf_path, max_pages=10):
    """
    Extract text from a PDF file with error handling and page limit.

    Args:
        pdf_path (str): Path to the PDF file
        max_pages (int): Maximum number of pages to process

    Returns:
        str: Extracted text from the PDF
    """
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = pypdf.PdfReader(file)
            text = ""

            # Limit the number of pages to process
            num_pages = min(len(pdf_reader.pages), max_pages)

            for i in range(num_pages):
                try:
                    page_text = pdf_reader.pages[i].extract_text()
                    text += page_text + "\n"
                except Exception as page_error:
                    print(f"Error extracting page {i+1} from {pdf_path}: {page_error}")
                    continue

        return text
    except Exception as e:
        print(f"Error extracting text from {pdf_path}: {e}")
        return ""

def clean_and_extract_words(text, num_words=50, min_length=4):
    """
    Clean text and extract English words with specified minimum length.
    
    Args:
        text (str): Input text to process
        num_words (int): Number of words to extract
        min_length (int): Minimum word length
        
    Returns:
        list: List of cleaned English words
    """
    if not text:
        return []
    
    # Convert to lowercase
    text = text.lower()
    
    # Remove URLs, DOIs, and other patterns
    text = re.sub(r'https?://[^\s]+', '', text)
    text = re.sub(r'doi[:\s]*[^\s]+', '', text)
    text = re.sub(r'www\.[^\s]+', '', text)
    
    # Remove special characters and numbers, keep only letters and spaces
    text = re.sub(r'[^a-z\s]', ' ', text)
    
    # Split into words and filter for minimum length
    words = [word.strip() for word in text.split() if len(word.strip()) >= min_length]
    
    # Filter for English words if dictionary is available
    if ENGLISH_DICT_AVAILABLE:
        words = [word for word in words if word in english_words]
    
    # Return the specified number of words
    return words[:num_words]

def find_dataset_ids_in_text(text):
    """
    Find potential dataset IDs in text using various patterns.
    
    Args:
        text (str): Text to search for dataset IDs
        
    Returns:
        list: List of potential dataset IDs found
    """
    dataset_ids = []
    
    # DOI patterns
    doi_patterns = [
        r'https://doi\.org/[^\s\)]+',
        r'doi\.org/[^\s\)]+',
        r'doi:\s*https://[^\s\)]+',
        r'doi:\s*[^\s\)]+',
    ]
    
    for pattern in doi_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            # Clean up the match
            clean_match = match.strip('.,;:)')
            if not clean_match.startswith('https://'):
                clean_match = 'https://doi.org/' + clean_match.replace('doi.org/', '').replace('doi:', '').strip()
            dataset_ids.append(clean_match)
    
    # Non-DOI patterns (common database identifiers)
    non_doi_patterns = [
        r'\bCHEMBL\d+\b',
        r'\bIPR\d{6}\b',
        r'\bPF\d{5}\b',
        r'\bGSE\d+\b',
        r'\bSRP\d+\b',
        r'\bEMPIAR-\d+\b',
        r'\bENSBTAG\d+\b',
        r'\bSAMN\d+\b',
        r'\bEPI\d+\b',
        r'\bSRR\d+\b',
        r'\bERR\d+\b',
        r'\b[A-Z0-9]{4,}\b',  # Generic pattern for other IDs
    ]
    
    for pattern in non_doi_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        dataset_ids.extend(matches)
    
    return list(set(dataset_ids))  # Remove duplicates

def extract_preceding_words_from_pdf(pdf_path, dataset_id, num_words=50):
    """
    Extract preceding words before a dataset ID in a PDF.
    
    Args:
        pdf_path (str): Path to the PDF file
        dataset_id (str): Dataset ID to find
        num_words (int): Number of preceding words to extract
        
    Returns:
        list: List of preceding English words
    """
    text = extract_text_from_pdf(pdf_path)
    if not text:
        return []
    
    # Find the position of the dataset ID in the text
    text_lower = text.lower()
    dataset_id_lower = str(dataset_id).lower()
    
    # Try to find the dataset ID
    pos = text_lower.find(dataset_id_lower)
    if pos == -1:
        # Try partial matches for DOIs
        if dataset_id_lower.startswith('https://doi.org/'):
            partial_id = dataset_id_lower.replace('https://doi.org/', '')
            pos = text_lower.find(partial_id)
    
    if pos == -1:
        return []
    
    # Extract text before the dataset ID
    preceding_text = text[:pos]
    
    # Extract and clean words
    return clean_and_extract_words(preceding_text, num_words)

def analyze_pdfs_in_folder(pdf_folder_path, max_files=5):
    """
    Analyze PDFs in a folder to identify dataset IDs and extract preceding words.

    Args:
        pdf_folder_path (str): Path to folder containing PDFs
        max_files (int): Maximum number of files to process (for testing)

    Returns:
        pd.DataFrame: DataFrame with analysis results
    """
    pdf_folder = Path(pdf_folder_path)
    if not pdf_folder.exists():
        print(f"Error: Folder {pdf_folder_path} does not exist.")
        return pd.DataFrame()

    results = []
    pdf_files = list(pdf_folder.glob("*.pdf"))[:max_files]  # Limit for testing

    print(f"Found {len(list(pdf_folder.glob('*.pdf')))} PDF files, processing first {len(pdf_files)}...")

    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"Processing {i}/{len(pdf_files)}: {pdf_file.name}")

        # Extract article ID from filename
        article_id = pdf_file.stem

        try:
            # Extract text from PDF
            text = extract_text_from_pdf(pdf_file)
            if not text:
                results.append({
                    'article_id': article_id,
                    'dataset_id': 'EXTRACTION_FAILED',
                    'dataset_type': 'Error',
                    'preceding_words': '',
                    'word_count': 0
                })
                continue

            # Find dataset IDs in the text
            dataset_ids = find_dataset_ids_in_text(text)

            if not dataset_ids:
                results.append({
                    'article_id': article_id,
                    'dataset_id': 'Missing',
                    'dataset_type': 'Missing',
                    'preceding_words': '',
                    'word_count': 0
                })
            else:
                # Process each found dataset ID
                for dataset_id in dataset_ids:
                    # Classify the dataset ID
                    dataset_type = classify_dataset_id(dataset_id)

                    # Extract preceding words
                    preceding_words = extract_preceding_words_from_pdf(pdf_file, dataset_id)
                    preceding_text = ' '.join(preceding_words)

                    results.append({
                        'article_id': article_id,
                        'dataset_id': dataset_id,
                        'dataset_type': dataset_type,
                        'preceding_words': preceding_text,
                        'word_count': len(preceding_words)
                    })

        except Exception as e:
            print(f"Error processing {pdf_file.name}: {e}")
            results.append({
                'article_id': article_id,
                'dataset_id': 'PROCESSING_ERROR',
                'dataset_type': 'Error',
                'preceding_words': '',
                'word_count': 0
            })

    return pd.DataFrame(results)

def main():
    """Main function to run the PDF analysis."""
    pdf_folder_path = 'data/test/PDF'
    
    if not os.path.exists(pdf_folder_path):
        print(f"Error: Folder {pdf_folder_path} not found.")
        print("Please make sure the PDF folder exists.")
        return
    
    print("Starting PDF Dataset ID Analysis...")
    print("=" * 50)
    
    # Analyze PDFs
    results_df = analyze_pdfs_in_folder(pdf_folder_path)
    
    if results_df.empty:
        print("No results to analyze.")
        return
    
    # Save results
    os.makedirs('results', exist_ok=True)
    output_file = 'results/pdf_dataset_analysis.csv'
    results_df.to_csv(output_file, index=False)
    print(f"\nResults saved to: {output_file}")
    
    # Print summary statistics
    print(f"\nSUMMARY STATISTICS:")
    print("=" * 50)
    print(f"Total PDFs processed: {len(results_df['article_id'].unique())}")
    print(f"Total dataset IDs found: {len(results_df)}")
    
    # Count by dataset type
    type_counts = results_df['dataset_type'].value_counts()
    print(f"\nDataset ID Types:")
    for dtype, count in type_counts.items():
        print(f"  {dtype}: {count}")
    
    # Show examples of each type
    print(f"\nExamples by Type:")
    for dtype in type_counts.index:
        examples = results_df[results_df['dataset_type'] == dtype]['dataset_id'].head(3).tolist()
        print(f"  {dtype}: {examples}")
    
    # Word extraction statistics
    word_stats = results_df[results_df['word_count'] > 0]['word_count'].describe()
    print(f"\nPreceding Words Statistics:")
    print(f"  Mean words extracted: {word_stats['mean']:.1f}")
    print(f"  Max words extracted: {int(word_stats['max'])}")
    print(f"  Min words extracted: {int(word_stats['min'])}")

if __name__ == "__main__":
    main()
