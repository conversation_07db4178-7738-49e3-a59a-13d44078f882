"""
Script que pula automaticamente arquivos problemáticos.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm
import multiprocessing
import time
from concurrent.futures import ProcessPoolExecutor, TimeoutError as FutureTimeoutError


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]
    
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
    
    return variations


def extract_preceding_words(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto."""
    try:
        escaped_target = re.escape(target)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
    except Exception:
        pass
    
    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias do texto."""
    try:
        words = re.findall(r'\b\w+\b', text)
        
        if len(words) < num_words:
            return ' '.join(words)
        
        start_pos = random.randint(0, len(words) - num_words)
        return ' '.join(words[start_pos:start_pos + num_words])
    except Exception:
        return "ERROR_EXTRACTING_RANDOM_WORDS"


def process_single_pdf(args):
    """Processa um único PDF - função para multiprocessing."""
    article_id, dataset_id, data_dir = args
    
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"
    
    if not pdf_path.exists():
        return article_id, dataset_id, "PDF_NOT_FOUND"
    
    try:
        reader = PdfReader(pdf_path)
        
        # Extrair texto apenas das primeiras 3 páginas para ser mais rápido
        full_text = ""
        max_pages = min(len(reader.pages), 3)
        
        for i in range(max_pages):
            try:
                page_text = reader.pages[i].extract_text()
                full_text += page_text + "\n"
                
                # Limitar tamanho
                if len(full_text) > 30000:  # 30KB
                    break
                    
            except Exception:
                continue
        
        if dataset_id == "Missing":
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Tentar encontrar o dataset_id
        variations = normalize_dataset_id(dataset_id)
        
        for variation in variations:
            preceding_words = extract_preceding_words(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)[:20]}"


def main():
    """Função principal com processamento paralelo e timeout."""
    print("=== Extração com Skip Automático ===")
    print("Processamento paralelo com timeout automático.\n")
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    total_files = len(labels_df)
    
    print(f"Total de entradas: {total_files}")
    
    # Preparar argumentos para processamento paralelo
    args_list = [
        (row['article_id'], row['dataset_id'], "data")
        for _, row in labels_df.iterrows()
    ]
    
    results = []
    
    # Usar ProcessPoolExecutor com timeout
    with ProcessPoolExecutor(max_workers=2) as executor:
        print("Iniciando processamento paralelo...")
        
        # Submeter todos os jobs
        future_to_args = {
            executor.submit(process_single_pdf, args): args 
            for args in args_list
        }
        
        # Processar resultados com timeout
        for i, future in enumerate(tqdm(future_to_args, desc="Processando")):
            try:
                # Timeout de 30 segundos por arquivo
                result = future.result(timeout=30)
                results.append(result)
                
            except FutureTimeoutError:
                # Se timeout, adicionar erro
                args = future_to_args[future]
                article_id, dataset_id, _ = args
                results.append((article_id, dataset_id, "TIMEOUT_ERROR"))
                print(f"\nTimeout: {article_id}")
                
            except Exception as e:
                # Se outro erro, adicionar erro
                args = future_to_args[future]
                article_id, dataset_id, _ = args
                results.append((article_id, dataset_id, f"PROCESS_ERROR: {str(e)[:20]}"))
                print(f"\nErro: {article_id}")
            
            # Salvar progresso a cada 100 arquivos
            if (i + 1) % 100 == 0:
                temp_df = pd.DataFrame(results, columns=[
                    'article_id', 'dataset_id', 'preceding_words'
                ])
                
                results_dir = Path("results")
                results_dir.mkdir(exist_ok=True)
                
                temp_path = results_dir / f"parallel_progress_{len(results)}.csv"
                temp_df.to_csv(temp_path, index=False, encoding='utf-8')
                print(f"\nProgresso salvo: {temp_path}")
    
    # Salvar resultados finais
    results_df = pd.DataFrame(results, columns=[
        'article_id', 'dataset_id', 'preceding_words'
    ])
    
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    final_path = results_dir / "parallel_extraction_results.csv"
    results_df.to_csv(final_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados finais: {final_path}")
    
    # Estatísticas
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR|TIMEOUT', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    errors = total - found
    
    print(f"\n📊 Estatísticas:")
    print(f"Total processado: {total}")
    print(f"Dataset IDs encontrados: {found - missing}")
    print(f"Casos Missing: {missing}")
    print(f"Erros/Timeouts: {errors}")
    
    # Taxa de sucesso
    non_missing = total - missing
    if non_missing > 0:
        success_rate = ((found - missing) / non_missing) * 100
        print(f"Taxa de sucesso: {success_rate:.1f}%")
    
    # Contar tipos de erro
    error_counts = results_df['preceding_words'].value_counts()
    print(f"\nTipos de problema:")
    for error_type in ['TIMEOUT_ERROR', 'PDF_NOT_FOUND', 'DATASET_ID_NOT_FOUND']:
        if error_type in error_counts:
            print(f"  {error_type}: {error_counts[error_type]}")
    
    print("\n🎉 Processamento paralelo concluído!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Interrompido.")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
        import traceback
        traceback.print_exc()
