"""
Script de debug para analisar como o texto aparece nos PDFs problemáticos.
"""

import pandas as pd
import re
from pathlib import Path
from pypdf import Pdf<PERSON>eader


def analyze_pdf_text(article_id, dataset_id, data_dir="data"):
    """Analisa o texto do PDF para entender como o dataset_id aparece."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"
    
    if not pdf_path.exists():
        print(f"❌ PDF não encontrado: {pdf_path}")
        return
    
    try:
        reader = PdfReader(pdf_path)
        
        print(f"📄 PDF: {article_id}")
        print(f"🔗 Dataset ID procurado: {dataset_id}")
        print(f"📊 Número de páginas: {len(reader.pages)}")
        print()
        
        # Extrair texto de todas as páginas
        full_text = ""
        for i, page in enumerate(reader.pages):
            try:
                page_text = page.extract_text()
                full_text += page_text + "\n"
                
                # Verificar se alguma parte do dataset_id aparece nesta página
                if "dryad" in page_text.lower() or "doi.org" in page_text.lower():
                    print(f"🔍 Página {i+1} contém 'dryad' ou 'doi.org'")
                    
                    # Procurar por padrões relacionados
                    patterns = [
                        r'dryad[^\n]*',
                        r'doi\.org[^\n]*',
                        r'https://doi[^\n]*',
                        r'10\.5061[^\n]*'
                    ]
                    
                    for pattern in patterns:
                        matches = re.findall(pattern, page_text, re.IGNORECASE)
                        if matches:
                            print(f"  Padrão '{pattern}' encontrado:")
                            for match in matches[:3]:  # Mostrar apenas os primeiros 3
                                print(f"    '{match}'")
                    print()
            except Exception as e:
                print(f"❌ Erro na página {i+1}: {e}")
        
        # Buscar por partes específicas do dataset_id
        if "10.5061" in dataset_id:
            doi_part = re.search(r'10\.5061/[\w\d\.]+', dataset_id)
            if doi_part:
                doi_search = doi_part.group()
                print(f"🔍 Buscando parte do DOI: '{doi_search}'")
                
                if doi_search in full_text:
                    print(f"✅ Parte do DOI encontrada no texto!")
                    # Encontrar contexto
                    index = full_text.find(doi_search)
                    start = max(0, index - 200)
                    end = min(len(full_text), index + 200)
                    context = full_text[start:end]
                    print(f"📝 Contexto:")
                    print(f"'{context}'")
                else:
                    print(f"❌ Parte do DOI não encontrada")
        
        # Buscar por "zw3r22854" ou "37pvmcvgb" especificamente
        if "zw3r22854" in dataset_id:
            search_term = "zw3r22854"
        elif "37pvmcvgb" in dataset_id:
            search_term = "37pvmcvgb"
        else:
            search_term = None
        
        if search_term:
            print(f"🔍 Buscando termo específico: '{search_term}'")
            if search_term in full_text:
                print(f"✅ Termo específico encontrado!")
                index = full_text.find(search_term)
                start = max(0, index - 200)
                end = min(len(full_text), index + 200)
                context = full_text[start:end]
                print(f"📝 Contexto:")
                print(f"'{context}'")
            else:
                print(f"❌ Termo específico não encontrado")
                
                # Tentar buscar com espaços
                spaced_term = ' '.join(search_term[i:i+2] for i in range(0, len(search_term), 2))
                print(f"🔍 Tentando com espaços: '{spaced_term}'")
                if spaced_term in full_text:
                    print(f"✅ Versão com espaços encontrada!")
                    index = full_text.find(spaced_term)
                    start = max(0, index - 200)
                    end = min(len(full_text), index + 200)
                    context = full_text[start:end]
                    print(f"📝 Contexto:")
                    print(f"'{context}'")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Erro ao processar PDF: {e}")


def main():
    """Função principal para debug."""
    print("=== Debug de PDFs Problemáticos ===\n")
    
    # Casos específicos mencionados
    test_cases = [
        {
            'article_id': '10.1002_ece3.6144',
            'dataset_id': 'https://doi.org/10.5061/dryad.zw3r22854'
        },
        {
            'article_id': '10.1002_ece3.6303', 
            'dataset_id': 'https://doi.org/10.5061/dryad.37pvmcvgb'
        }
    ]
    
    for case in test_cases:
        analyze_pdf_text(case['article_id'], case['dataset_id'])


if __name__ == "__main__":
    main()
