"""
Script final - começa a partir dos arquivos que sabemos que funcionam.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm
import time


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]
    
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
    
    return variations


def extract_preceding_words(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto."""
    try:
        escaped_target = re.escape(target)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
    except Exception:
        pass
    
    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias do texto."""
    try:
        words = re.findall(r'\b\w+\b', text)
        
        if len(words) < num_words:
            return ' '.join(words)
        
        start_pos = random.randint(0, len(words) - num_words)
        return ' '.join(words[start_pos:start_pos + num_words])
    except Exception:
        return "ERROR_EXTRACTING_RANDOM_WORDS"


def process_pdf_quick(article_id, dataset_id, data_dir="data"):
    """Processa um PDF rapidamente."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"
    
    if not pdf_path.exists():
        return article_id, dataset_id, "PDF_NOT_FOUND"
    
    try:
        reader = PdfReader(pdf_path)
        
        # Extrair texto apenas das primeiras 5 páginas
        full_text = ""
        max_pages = min(len(reader.pages), 5)
        
        for i in range(max_pages):
            try:
                page_text = reader.pages[i].extract_text()
                full_text += page_text + "\n"
                
                # Limitar tamanho
                if len(full_text) > 50000:  # 50KB
                    break
                    
            except Exception:
                continue
        
        if dataset_id == "Missing":
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Tentar encontrar o dataset_id
        variations = normalize_dataset_id(dataset_id)
        
        for variation in variations:
            preceding_words = extract_preceding_words(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)[:30]}"


def main():
    """Função principal."""
    print("=== Extração Final de Dataset IDs ===")
    print("Processamento otimizado e rápido.\n")
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    total_files = len(labels_df)
    
    print(f"Total de entradas: {total_files}")
    
    # Verificar se há resultados parciais
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    existing_files = list(results_dir.glob("*extraction*.csv"))
    if existing_files:
        print(f"Encontrados {len(existing_files)} arquivos de resultados existentes.")
        response = input("Continuar do último ponto ou recomeçar? (c/r): ").strip().lower()
        
        if response == 'c':
            # Carregar último arquivo
            latest_file = max(existing_files, key=lambda x: x.stat().st_mtime)
            existing_df = pd.read_csv(latest_file)
            start_idx = len(existing_df)
            print(f"Continuando a partir do arquivo {start_idx + 1}")
            all_results = existing_df.values.tolist()
        else:
            start_idx = 0
            all_results = []
    else:
        start_idx = 0
        all_results = []
    
    # Processar arquivos restantes
    remaining_df = labels_df.iloc[start_idx:]
    
    print(f"Processando {len(remaining_df)} arquivos restantes...")
    
    for idx, row in tqdm(remaining_df.iterrows(), total=len(remaining_df), desc="Processando"):
        article_id = row['article_id']
        dataset_id = row['dataset_id']
        
        result = process_pdf_quick(article_id, dataset_id)
        all_results.append(result)
        
        # Salvar a cada 100 arquivos
        if len(all_results) % 100 == 0:
            temp_df = pd.DataFrame(all_results, columns=[
                'article_id', 'dataset_id', 'preceding_words'
            ])
            
            temp_path = results_dir / f"temp_extraction_{len(all_results)}.csv"
            temp_df.to_csv(temp_path, index=False, encoding='utf-8')
            print(f"\nSalvo progresso: {temp_path}")
    
    # Salvar resultados finais
    results_df = pd.DataFrame(all_results, columns=[
        'article_id', 'dataset_id', 'preceding_words'
    ])
    
    final_path = results_dir / "final_extraction_results.csv"
    results_df.to_csv(final_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados finais: {final_path}")
    
    # Estatísticas
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    
    print(f"\n📊 Estatísticas:")
    print(f"Total: {total}")
    print(f"Dataset IDs encontrados: {found - missing}")
    print(f"Casos Missing: {missing}")
    print(f"Erros: {total - found}")
    
    # Taxa de sucesso
    non_missing = total - missing
    if non_missing > 0:
        success_rate = ((found - missing) / non_missing) * 100
        print(f"Taxa de sucesso: {success_rate:.1f}%")
    
    print("\n🎉 Processamento concluído!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Interrompido.")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
        import traceback
        traceback.print_exc()
