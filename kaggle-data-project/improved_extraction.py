"""
Script melhorado para extrair dataset_ids dos PDFs com melhor tratamento de texto.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]
    
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
            
            # Adicionar versão com doi: prefix
            variations.append(f"doi:{dataset_id}")
            variations.append(f"doi: {dataset_id}")
            
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
    
    return variations


def clean_text_for_search(text):
    """Limpa o texto para melhorar a busca, removendo quebras de linha problemáticas."""
    # Remover quebras de linha que quebram URLs
    text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
    text = re.sub(r'doi\.\s*\n\s*org/', 'doi.org/', text)
    
    # Remover quebras de linha dentro de DOIs
    text = re.sub(r'(10\.\d+/[^\s\n]*)\s*\n\s*([^\s\n]*)', r'\1\2', text)
    
    # Remover espaços extras dentro de DOIs
    text = re.sub(r'(10\.\d+/\w+)\s+(\w+)', r'\1\2', text)
    
    # Normalizar espaços
    text = re.sub(r'\s+', ' ', text)
    
    return text


def extract_preceding_words_improved(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto com melhor tratamento."""
    try:
        # Limpar texto primeiro
        cleaned_text = clean_text_for_search(text)
        
        # Escapar caracteres especiais para regex
        escaped_target = re.escape(target)
        
        # Tentar busca exata primeiro
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
        
        # Se não encontrou, tentar busca mais flexível
        # Remover caracteres especiais do target para busca flexível
        flexible_target = re.sub(r'[^\w\d]', '', target)
        if len(flexible_target) > 10:  # Só para targets longos o suficiente
            pattern_flexible = rf'(.+?){flexible_target}'
            match = re.search(pattern_flexible, re.sub(r'[^\w\d\s]', '', cleaned_text), re.IGNORECASE)
            
            if match:
                preceding_text = match.group(1)
                words = re.findall(r'\b\w+\b', preceding_text)
                if len(words) >= num_words:
                    return ' '.join(words[-num_words:])
                else:
                    return ' '.join(words)
                    
    except Exception as e:
        print(f"Erro na extração: {e}")
    
    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias do texto."""
    try:
        words = re.findall(r'\b\w+\b', text)
        
        if len(words) < num_words:
            return ' '.join(words)
        
        start_pos = random.randint(0, len(words) - num_words)
        return ' '.join(words[start_pos:start_pos + num_words])
    except Exception:
        return "ERROR_EXTRACTING_RANDOM_WORDS"


def process_pdf_improved(article_id, dataset_id, data_dir="data"):
    """Processa um PDF individual com melhor tratamento de texto."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"
    
    if not pdf_path.exists():
        return article_id, dataset_id, "PDF_NOT_FOUND"
    
    try:
        reader = PdfReader(pdf_path)
        
        # Extrair texto de todas as páginas
        full_text = ""
        for page in reader.pages:
            try:
                page_text = page.extract_text()
                full_text += page_text + "\n"
            except Exception:
                continue
        
        if dataset_id == "Missing":
            random_words = extract_random_words(full_text)
            return article_id, dataset_id, random_words
        
        # Tentar encontrar o dataset_id e suas variações
        variations = normalize_dataset_id(dataset_id)
        
        for variation in variations:
            preceding_words = extract_preceding_words_improved(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        # Se não encontrou nenhuma variação, tentar busca mais agressiva
        # Extrair apenas a parte numérica do DOI para busca
        if "10." in dataset_id:
            doi_match = re.search(r'10\.\d+/[\w\d\.]+', dataset_id)
            if doi_match:
                doi_part = doi_match.group()
                preceding_words = extract_preceding_words_improved(full_text, doi_part)
                if preceding_words:
                    return article_id, dataset_id, f"PARTIAL_MATCH: {preceding_words}"
        
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)[:50]}"


def test_specific_cases():
    """Testa os casos específicos mencionados."""
    test_cases = [
        {
            'article_id': '10.1002_ece3.6144',
            'dataset_id': 'https://doi.org/10.5061/dryad.zw3r22854'
        },
        {
            'article_id': '10.1002_ece3.6303', 
            'dataset_id': 'https://doi.org/10.5061/dryad.37pvmcvgb'
        }
    ]
    
    print("=== Teste dos Casos Específicos ===\n")
    
    for i, case in enumerate(test_cases, 1):
        article_id = case['article_id']
        dataset_id = case['dataset_id']
        
        print(f"Teste {i}: {article_id}")
        print(f"Dataset ID: {dataset_id}")
        
        result = process_pdf_improved(article_id, dataset_id)
        
        print(f"Resultado: {result[2]}")
        print("-" * 60)


def main():
    """Função principal."""
    print("=== Extração Melhorada de Dataset IDs ===")
    print("Versão com melhor tratamento de quebras de linha e espaços.\n")
    
    # Primeiro, testar os casos específicos
    test_specific_cases()
    
    # Perguntar se quer processar todos
    response = input("\nProcessar todos os arquivos? (s/n): ").strip().lower()
    
    if response not in ['s', 'sim', 'y', 'yes']:
        print("Processamento cancelado.")
        return
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    
    print(f"Processando {len(labels_df)} arquivos...")
    
    results = []
    
    for idx, row in labels_df.iterrows():
        article_id = row['article_id']
        dataset_id = row['dataset_id']
        
        if (idx + 1) % 50 == 0:
            print(f"Processando {idx + 1}/{len(labels_df)}: {article_id}")
        
        result = process_pdf_improved(article_id, dataset_id)
        results.append(result)
        
        # Salvar progresso a cada 100 arquivos
        if (idx + 1) % 100 == 0:
            temp_df = pd.DataFrame(results, columns=[
                'article_id', 'dataset_id', 'preceding_words'
            ])
            
            results_dir = Path("results")
            results_dir.mkdir(exist_ok=True)
            
            temp_path = results_dir / f"improved_progress_{idx + 1}.csv"
            temp_df.to_csv(temp_path, index=False, encoding='utf-8')
            print(f"Progresso salvo: {temp_path}")
    
    # Salvar resultados finais
    results_df = pd.DataFrame(results, columns=[
        'article_id', 'dataset_id', 'preceding_words'
    ])
    
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    output_path = results_dir / "improved_extraction_results.csv"
    results_df.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados salvos em: {output_path}")
    
    # Estatísticas
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    partial = len(results_df[results_df['preceding_words'].str.contains('PARTIAL_MATCH', na=False)])
    
    print(f"\n📊 Estatísticas:")
    print(f"Total: {total}")
    print(f"Dataset IDs encontrados: {found - missing - partial}")
    print(f"Matches parciais: {partial}")
    print(f"Casos Missing: {missing}")
    print(f"Não encontrados: {total - found}")
    
    # Taxa de sucesso melhorada
    non_missing = total - missing
    if non_missing > 0:
        success_rate = ((found - missing) / non_missing) * 100
        print(f"Taxa de sucesso: {success_rate:.1f}%")
    
    print("\n🎉 Processamento melhorado concluído!")


if __name__ == "__main__":
    main()
