"""
Test the optimized page-by-page extraction.
"""

import time
from extract_all_pdfs import process_pdf

def test_optimized_extraction():
    """Test the optimized extraction on a few cases."""
    
    # Test cases: mix of found and missing cases
    test_cases = [
        ("10.1002_2017jc013030", "https://doi.org/10.17882/49388"),  # Should be found
        ("10.1002_ece3.6144", "https://doi.org/10.5061/dryad.zw3r22854"),  # Previously not found
        ("10.1002_anie.201916483", "Missing"),  # Missing case
        ("10.1002_ece3.4466", "https://doi.org/10.5061/dryad.r6nq870"),  # Should be found
        ("10.1002_anie.202005531", "Missing"),  # Missing case
    ]
    
    print("=== Testing Optimized Page-by-Page Extraction ===\n")
    
    total_time = 0
    results = []
    
    for i, (article_id, dataset_id) in enumerate(test_cases, 1):
        print(f"Test {i}/5: {article_id}")
        print(f"Dataset ID: {dataset_id}")
        
        start_time = time.time()
        result = process_pdf(article_id, dataset_id)
        end_time = time.time()
        
        processing_time = end_time - start_time
        total_time += processing_time
        
        print(f"Result: {result[2]}")
        print(f"Time: {processing_time:.2f} seconds")
        print("-" * 60)
        print()
        
        results.append((result, processing_time))
    
    print(f"📊 Summary:")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Average time per PDF: {total_time/len(test_cases):.2f} seconds")
    print(f"Estimated time for 1028 PDFs: {(total_time/len(test_cases)) * 1028 / 60:.1f} minutes")
    
    # Show results
    print(f"\n📋 Results:")
    for i, ((article_id, dataset_id, result), time_taken) in enumerate(results, 1):
        status = "✅ Found" if not any(x in result for x in ['NOT_FOUND', 'ERROR', 'PDF_NOT_FOUND']) else "❌ Not found"
        if dataset_id == "Missing":
            status = "📝 Random words"
        print(f"{i}. {article_id}: {status} ({time_taken:.2f}s)")

if __name__ == "__main__":
    test_optimized_extraction()
