"""
Test the improved reference filtering and intro/abstract skipping.
"""

import time
from extract_all_pdfs import process_pdf, is_in_references_section, is_intro_or_abstract_section

def test_reference_detection():
    """Test the reference section detection."""
    
    # Test text with references section
    test_text = """
    DATA AVAILABILITY STATEMENT
    The data supporting this study are available at https://doi.org/10.5061/dryad.zw3r22854
    
    REFERENCES
    1. <PERSON>, J. (2020). Some paper. Available at https://doi.org/10.5061/dryad.zw3r22854
    2. <PERSON>, A. (2021). Another paper.
    """
    
    # Find positions
    data_pos = test_text.find("https://doi.org/10.5061/dryad.zw3r22854")
    ref_pos = test_text.rfind("https://doi.org/10.5061/dryad.zw3r22854")
    
    print("=== Testing Reference Detection ===")
    print(f"Data availability position: {data_pos}")
    print(f"Reference position: {ref_pos}")
    print(f"Data availability in refs: {is_in_references_section(test_text, data_pos)}")
    print(f"Reference mention in refs: {is_in_references_section(test_text, ref_pos)}")
    print()


def test_intro_abstract_detection():
    """Test intro/abstract section detection."""
    
    test_cases = [
        ("ABSTRACT\nThis paper discusses...", True),
        ("INTRODUCTION\nIn recent years...", True),
        ("DATA AVAILABILITY STATEMENT\nData are available...", False),
        ("Short text", True),  # Too short
        ("This is a longer text that doesn't contain intro markers and should not be skipped for processing", False)
    ]
    
    print("=== Testing Intro/Abstract Detection ===")
    for text, expected in test_cases:
        result = is_intro_or_abstract_section(text)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{text[:30]}...' -> Skip: {result} (expected: {expected})")
    print()


def test_improved_extraction():
    """Test the improved extraction on specific cases."""
    
    # Test cases that should show improved reference filtering
    test_cases = [
        ("10.1002_ece3.4466", "https://doi.org/10.5061/dryad.r6nq870"),  # Should find in data section
        ("10.1002_ecs2.4619", "https://doi.org/10.25349/d9qw5x"),  # Should find outside refs
        ("10.1002_2017jc013030", "https://doi.org/10.17882/49388"),  # Test case
    ]
    
    print("=== Testing Improved Reference Filtering ===")
    
    for i, (article_id, dataset_id) in enumerate(test_cases, 1):
        print(f"Test {i}/{len(test_cases)}: {article_id}")
        print(f"Dataset ID: {dataset_id}")
        
        start_time = time.time()
        result = process_pdf(article_id, dataset_id)
        end_time = time.time()
        
        processing_time = end_time - start_time
        result_text = result[2]
        
        # Analyze the result
        if "REF:" in result_text or "REF_SECTION:" in result_text or "REF_PARTIAL:" in result_text:
            status = "📚 Found in REFERENCES (fallback)"
        elif "SECTION:" in result_text:
            status = "✅ Found in DATA SECTION (preferred)"
        elif "PARTIAL:" in result_text:
            status = "🔍 Found PARTIAL match (main text)"
        elif "DATASET_ID_NOT_FOUND" in result_text:
            status = "❌ Not found"
        else:
            status = "✅ Found in MAIN TEXT (preferred)"
        
        print(f"Status: {status}")
        print(f"Result: {result_text}")
        print(f"Time: {processing_time:.2f} seconds")
        print("-" * 80)
        print()


if __name__ == "__main__":
    test_reference_detection()
    test_intro_abstract_detection()
    test_improved_extraction()
