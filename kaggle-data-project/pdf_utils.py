"""
PDF Utilities Module

This module contains reusable functions for PDF text extraction, dataset ID identification,
and text processing that can be used across multiple scripts.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader

# For English word validation
try:
    import nltk
    from nltk.corpus import words
    # Download words corpus if not already present
    try:
        nltk.data.find('corpora/words')
    except LookupError:
        print("Downloading NLTK words corpus...")
        nltk.download('words', quiet=True)

    # Load English words set
    english_words = set(word.lower() for word in words.words())
    ENGLISH_DICT_AVAILABLE = True
except ImportError:
    print("NLTK not available. Install with: pip install nltk")
    ENGLISH_DICT_AVAILABLE = False
    english_words = set()
except Exception as e:
    print(f"Error loading English dictionary: {e}")
    ENGLISH_DICT_AVAILABLE = False
    english_words = set()


def split_text_into_sections(text):
    """
    Split text into sections, identifying reference sections vs main content.

    Args:
        text (str): Input text to split

    Returns:
        list: List of tuples (section_text, section_type)
    """
    sections = []

    # Common reference section headers
    ref_patterns = [
        r'\n\s*REFERENCES?\s*\n',
        r'\n\s*BIBLIOGRAPHY\s*\n',
        r'\n\s*LITERATURE\s+CITED\s*\n',
        r'\n\s*WORKS\s+CITED\s*\n',
        r'\n\s*CITATIONS?\s*\n',
    ]

    # Find reference section start
    ref_start = None
    for pattern in ref_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            ref_start = match.start()
            break

    if ref_start is not None:
        # Split into main content and references
        main_content = text[:ref_start]
        ref_content = text[ref_start:]

        sections.append((main_content, 'main'))
        sections.append((ref_content, 'reference'))
    else:
        # No clear reference section found, treat as main content
        sections.append((text, 'main'))

    return sections


def clean_doi_url(doi_string):
    """
    Clean a DOI URL by removing trailing author names and other non-DOI text.

    Args:
        doi_string (str): Raw DOI string that may have trailing text

    Returns:
        str: Cleaned DOI URL
    """
    if not doi_string:
        return doi_string

    # Remove https://doi.org/ prefix for processing
    prefix = ''
    clean_doi = doi_string
    if doi_string.startswith('https://doi.org/'):
        prefix = 'https://doi.org/'
        clean_doi = doi_string[len(prefix):]
    elif doi_string.startswith('doi.org/'):
        prefix = 'https://'
        clean_doi = doi_string[len('doi.org/'):]

    # DOI pattern: 10.xxxx/identifier
    # The identifier part can contain letters, numbers, hyphens, underscores, periods, parentheses
    # But should not contain standalone author names

    # Split on common separators that indicate end of DOI
    separators = [
        r'[A-Z][a-z]+$',  # Trailing capitalized word (likely author name)
        r'[A-Z][a-z]+[A-Z]',  # CamelCase (likely author names)
        r'\s+[A-Z]',  # Space followed by capital letter
        r'(?<=[a-z])(?=[A-Z][a-z])',  # Transition from lowercase to capitalized word
    ]

    # Find the end of the actual DOI
    for separator in separators:
        match = re.search(separator, clean_doi)
        if match:
            # Check if this looks like an author name pattern
            potential_author = clean_doi[match.start():].strip()
            if is_likely_author_name(potential_author):
                clean_doi = clean_doi[:match.start()]
                break

    # Remove common trailing patterns that aren't part of DOIs
    trailing_patterns = [
        r'[A-Z][a-z]+$',  # Single capitalized word at end
        r'[A-Z]{2,}$',    # Multiple capital letters at end
        r'\s+.*$',        # Anything after a space
    ]

    for pattern in trailing_patterns:
        clean_doi = re.sub(pattern, '', clean_doi)

    # Clean up any trailing punctuation
    clean_doi = clean_doi.rstrip('.,;:)')

    return prefix + clean_doi


def is_likely_author_name(text):
    """
    Check if a text string looks like an author name.

    Args:
        text (str): Text to check

    Returns:
        bool: True if it looks like an author name
    """
    if not text or len(text) < 2:
        return False

    # Common patterns for author names
    author_patterns = [
        r'^[A-Z][a-z]+$',           # Single capitalized word
        r'^[A-Z][a-z]+[A-Z][a-z]+', # CamelCase like "SmithJones"
        r'^[A-Z]{2,}$',             # All caps abbreviation
        r'^\s*[A-Z][a-z]+\s*$',     # Capitalized word with optional spaces
    ]

    for pattern in author_patterns:
        if re.match(pattern, text.strip()):
            return True

    return False


def is_complete_doi(doi_string):
    """
    Check if a DOI string appears to be complete and valid.

    Args:
        doi_string (str): DOI string to validate

    Returns:
        bool: True if DOI appears complete
    """
    if not doi_string:
        return False

    # Remove https://doi.org/ prefix for checking
    clean_doi = doi_string.replace('https://doi.org/', '').replace('doi.org/', '')

    # Should have format like 10.1234/something with reasonable length
    if not re.match(r'10\.\d+/.+', clean_doi):
        return False

    # Should have reasonable length (not too short)
    if len(clean_doi) < 10:
        return False

    # Should not end with incomplete patterns
    incomplete_patterns = [
        r'\($',  # Ends with opening parenthesis
        r'\d$',  # Ends with single digit (likely incomplete)
        r'[A-Z]$',  # Ends with single letter (likely incomplete)
        r'\.$',  # Ends with period
    ]

    for pattern in incomplete_patterns:
        if re.search(pattern, clean_doi):
            return False

    return True


def clean_text_for_urls(text):
    """Clean text to handle URLs split across lines and other formatting issues."""
    # Fix hyphenated words split across lines (remove hyphen and join words)
    text = re.sub(r'(\w+)-\s*\n\s*(\w+)', r'\1\2', text)

    # Fix common URL splits
    text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
    text = re.sub(r'doi\.org/\s*\n\s*', 'doi.org/', text)
    text = re.sub(r'https://doi\.\s+org/', 'https://doi.org/', text)
    text = re.sub(r'10\.\d+/\s*\n\s*', '10.', text)

    # Fix dryad URLs specifically
    text = re.sub(r'dryad\.\s*\n\s*', 'dryad.', text)
    text = re.sub(r'10\.5061/\s*\n\s*dryad\.', '10.5061/dryad.', text)

    # Fix other common splits in dataset IDs
    text = re.sub(r'(\w+)\s*\n\s*(\w+)', r'\1\2', text)
    text = re.sub(r'(\d+)\s*\n\s*(\d+)', r'\1\2', text)

    # Remove excessive whitespace but preserve single spaces
    text = re.sub(r'\s+', ' ', text)

    return text


def extract_text_from_pdf(pdf_path, max_pages=None):
    """
    Extract text from a PDF file with error handling.
    
    Args:
        pdf_path (str): Path to the PDF file
        max_pages (int): Maximum number of pages to process (None for all)
        
    Returns:
        str: Extracted text from the PDF
    """
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            text = ""
            
            # Determine number of pages to process
            num_pages = len(pdf_reader.pages)
            if max_pages:
                num_pages = min(num_pages, max_pages)
            
            for i in range(num_pages):
                try:
                    page_text = pdf_reader.pages[i].extract_text()
                    text += page_text + "\n"
                except Exception as page_error:
                    print(f"Error extracting page {i+1} from {pdf_path}: {page_error}")
                    continue
                    
        return text
    except Exception as e:
        print(f"Error extracting text from {pdf_path}: {e}")
        return ""


def is_doi_dataset_id(dataset_id):
    """
    Check if a dataset ID is a DOI (starts with https://doi.org).
    
    Args:
        dataset_id (str): The dataset ID to check
        
    Returns:
        bool: True if it's a DOI, False otherwise
    """
    if pd.isna(dataset_id) or dataset_id == '' or dataset_id == 'Missing':
        return False
    
    return str(dataset_id).startswith('https://doi.org')


def is_non_doi_dataset_id(dataset_id):
    """
    Check if a dataset ID is a non-DOI identifier.
    
    Args:
        dataset_id (str): The dataset ID to check
        
    Returns:
        bool: True if it's a non-DOI identifier, False otherwise
    """
    if pd.isna(dataset_id) or dataset_id == '' or dataset_id == 'Missing':
        return False
    
    # Not a DOI and not missing/empty
    return not str(dataset_id).startswith('https://doi.org')


def classify_dataset_id(dataset_id):
    """
    Classify a dataset ID as DOI, non-DOI, or missing.
    
    Args:
        dataset_id (str): The dataset ID to classify
        
    Returns:
        str: 'DOI', 'non-DOI', or 'Missing'
    """
    if pd.isna(dataset_id) or dataset_id == '' or dataset_id == 'Missing':
        return 'Missing'
    elif str(dataset_id).startswith('https://doi.org'):
        return 'DOI'
    else:
        return 'non-DOI'


def clean_and_extract_words(text, num_words=50, min_length=4):
    """
    Clean text and extract English words with specified minimum length.
    
    Args:
        text (str): Input text to process
        num_words (int): Number of words to extract
        min_length (int): Minimum word length
        
    Returns:
        list: List of cleaned English words
    """
    if not text:
        return []
    
    # Convert to lowercase
    text = text.lower()
    
    # Remove URLs, DOIs, and other patterns
    text = re.sub(r'https?://[^\s]+', '', text)
    text = re.sub(r'doi[:\s]*[^\s]+', '', text)
    text = re.sub(r'www\.[^\s]+', '', text)
    
    # Remove special characters and numbers, keep only letters and spaces
    text = re.sub(r'[^a-z\s]', ' ', text)
    
    # Split into words and filter for minimum length
    words = [word.strip() for word in text.split() if len(word.strip()) >= min_length]
    
    # Filter for English words if dictionary is available
    if ENGLISH_DICT_AVAILABLE:
        words = [word for word in words if word in english_words]
    
    # Return the specified number of words
    return words[:num_words]


def find_dataset_ids_in_text(text):
    """
    Find potential dataset IDs in text using specific patterns.
    Focus on actual dataset repositories and avoid false positives.
    Prioritizes dataset IDs found outside of reference sections.

    Args:
        text (str): Text to search for dataset IDs

    Returns:
        list: List of potential dataset IDs found
    """
    dataset_ids = []

    # Clean text first
    cleaned_text = clean_text_for_urls(text)

    # Split text into sections to prioritize non-reference sections
    sections = split_text_into_sections(cleaned_text)

    # Process sections in priority order (non-reference sections first)
    priority_sections = []
    reference_sections = []

    for section_text, section_type in sections:
        if section_type == 'reference':
            reference_sections.append((section_text, section_type))
        else:
            priority_sections.append((section_text, section_type))

    # Process priority sections first, then references if needed
    all_sections = priority_sections + reference_sections

    # First, find all DOI patterns with better validation and proper termination
    doi_positions = []
    doi_patterns = [
        r'https://doi\.org/10\.\d+/[^\s\)\.,;]+',  # Basic DOI pattern
        r'doi\.org/10\.\d+/[^\s\)\.,;]+',
        r'doi:\s*https://doi\.org/10\.\d+/[^\s\)\.,;]+',
        r'doi:\s*10\.\d+/[^\s\)\.,;]+',
    ]

    found_dois = set()  # Track unique DOIs to avoid duplicates

    for section_text, section_type in all_sections:
        section_dois = []

        # Find DOI matches in this section
        for pattern in doi_patterns:
            for match in re.finditer(pattern, section_text, re.IGNORECASE):
                start, end = match.span()

                # Clean up the match
                raw_match = match.group().strip('.,;:)')

                # Normalize DOI format first
                if not raw_match.startswith('https://'):
                    if raw_match.startswith('doi.org/'):
                        raw_match = 'https://' + raw_match
                    elif raw_match.startswith('doi:'):
                        raw_match = raw_match.replace('doi:', '').strip()
                        if not raw_match.startswith('https://'):
                            raw_match = 'https://doi.org/' + raw_match.replace('doi.org/', '')
                    else:
                        raw_match = 'https://doi.org/' + raw_match

                # Clean the DOI to remove author names and trailing text
                clean_match = clean_doi_url(raw_match)

                # Validate DOI completeness
                if not is_complete_doi(clean_match):
                    continue

                # Check if this is a unique DOI
                if clean_match not in found_dois:
                    found_dois.add(clean_match)
                    section_dois.append((clean_match, section_type, start, end))

        # Add DOIs from this section
        for doi, sec_type, start, end in section_dois:
            dataset_ids.append(doi)
            doi_positions.append((start, end))

        # If we found DOIs in non-reference sections, we might want to limit reference extraction
        if section_type != 'reference' and section_dois:
            # Found dataset IDs in main content, limit reference extraction
            break

    # Function to check if a position overlaps with any DOI
    def overlaps_with_doi(start, end):
        for doi_start, doi_end in doi_positions:
            if not (end <= doi_start or start >= doi_end):  # Overlaps
                return True
        return False

    # Function to check if an identifier is in a figure/table/graphic context
    def is_in_figure_context(match_obj, text):
        start, end = match_obj.span()
        identifier = match_obj.group()

        # Get surrounding context (800 characters before and after for better detection)
        context_start = max(0, start - 800)
        context_end = min(len(text), end + 800)
        context = text[context_start:context_end].lower()

        # Patterns that indicate figure/table/graphic context
        figure_patterns = [
            r'\bfig\w*\s*\d+',           # fig, figure, etc.
            r'\btable\s*\d+',            # table
            r'\bscheme\s*\d+',           # scheme
            r'\bgraph\w*\s*\d+',         # graph, graphic
            r'\bdiagram\s*\d+',          # diagram
            r'\bchart\s*\d+',            # chart
            r'\bplot\s*\d+',             # plot
            r'\bimage\s*\d+',            # image
            r'\billustration\s*\d+',     # illustration
            r'\bpanel\s*[a-z]?\d*',      # panel
            r'\blegend',                 # legend
            r'\bcaption',                # caption
            r'\baxis\b',                 # axis
            r'\bx-axis\b',               # x-axis
            r'\by-axis\b',               # y-axis
            r'\blabel\w*',               # label, labeling
            r'\bspectrum\b',             # spectrum
            r'\bspectra\b',              # spectra
            r'\bnmr\b',                  # NMR
            r'\bms\b',                   # mass spec
            r'\bhrms\b',                 # high-res mass spec
            r'\bir\b',                   # infrared
            r'\buv\b',                   # UV
            r'\besi\b',                  # electrospray ionization
            r'\bmaldi\b',                # MALDI
            r'\bchromatogram\b',         # chromatogram
            r'\bpeak\s*at\b',           # peak at (spectroscopy)
            r'\bδ\s*=\s*\d+',          # chemical shift
            r'\bm/z\s*=?\s*\d+',       # mass-to-charge ratio
            r'\bwavelength\b',           # wavelength
            r'\babsorbance\b',           # absorbance
            r'\btransmittance\b',        # transmittance
        ]

        # Check if any figure pattern is found in the context
        for pattern in figure_patterns:
            if re.search(pattern, context):
                return True

        # Additional check: if identifier appears multiple times in close proximity,
        # it might be part of a figure or data series
        identifier_pattern = re.escape(identifier)
        matches_in_context = len(re.findall(identifier_pattern, context))
        if matches_in_context > 2:  # Appears more than twice in local context
            return True

        # Check for common figure/table formatting patterns
        formatting_patterns = [
            r'\b' + re.escape(identifier) + r'\s*[:\-]\s*\d+',  # ID: number format
            r'\d+\s*[:\-]\s*' + re.escape(identifier),          # number: ID format
            r'\b' + re.escape(identifier) + r'\s*\([^)]*\)',    # ID (description) format
        ]

        for pattern in formatting_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                return True

        # Additional check for measurement/instrument contexts that often contain non-dataset IDs
        measurement_patterns = [
            r'\bmeasurement\b',
            r'\binstrument\b',
            r'\bdetector\b',
            r'\bsensor\b',
            r'\bdevice\b',
            r'\bequipment\b',
            r'\bmodel\s+' + re.escape(identifier),  # "model M9000"
            r'\btype\s+' + re.escape(identifier),   # "type M9000"
            r'\bseries\s+' + re.escape(identifier), # "series M9000"
            r'\bversion\s+' + re.escape(identifier), # "version M9000"
        ]

        for pattern in measurement_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                return True

        return False

    # Non-DOI patterns (specific database identifiers only)
    non_doi_patterns = [
        r'\bCHEMBL\d+\b',           # ChEMBL database
        r'\bIPR\d{6}\b',            # InterPro
        r'\bPF\d{5}\b',             # Pfam
        r'\bGSE\d+\b',              # Gene Expression Omnibus
        r'\bSRP\d+\b',              # Sequence Read Archive Project
        r'\bEMPIAR-\d+\b',          # Electron Microscopy Public Image Archive
        r'\bENSBTAG\d+\b',          # Ensembl gene IDs
        r'\bSAMN\d+\b',             # BioSample
        r'\bEPI_ISL_\d+\b',         # GISAID (more specific pattern)
        r'\bSRR\d+\b',              # Sequence Read Archive Run
        r'\bERR\d+\b',              # European Nucleotide Archive Run
        r'\bPRJNA\d+\b',            # BioProject
        r'\bCVCL_\d+\b',            # Cell line identifiers
        r'\b[A-Z]\d[A-Z0-9]{3}\b',  # PDB codes like 5VA1
        r'\bMODEL\d+\b',            # Model identifiers
    ]

    # Find non-DOI matches, but exclude those that overlap with DOIs or are in figure contexts
    for pattern in non_doi_patterns:
        for match in re.finditer(pattern, cleaned_text, re.IGNORECASE):
            start, end = match.span()
            if not overlaps_with_doi(start, end) and not is_in_figure_context(match, cleaned_text):
                dataset_ids.append(match.group())

    # Filter out obvious false positives and parts of DOIs
    filtered_ids = []
    for dataset_id in dataset_ids:
        # Skip if it's just a year or common number
        if re.match(r'^\d{4}$', dataset_id):  # Years
            continue
        if re.match(r'^\d{1,3}$', dataset_id):  # Small numbers
            continue
        if re.match(r'^\d+$', dataset_id) and len(dataset_id) < 8:  # Random numbers (but allow long ones like accession numbers)
            continue
        if len(dataset_id) < 4:  # Too short
            continue
        # Skip obvious parts of DOIs or references
        if re.match(r'^\d+[a-zA-Z]*$', dataset_id) and len(dataset_id) < 8:  # Numbers with optional letters
            continue
        filtered_ids.append(dataset_id)

    return list(set(filtered_ids))  # Remove duplicates


def filter_meaningful_words(words, target_parts=None):
    """Filter words to keep only meaningful contextual text."""
    if not words:
        return []

    # Words to avoid (isolated numbers, URLs, etc.)
    avoid_patterns = [
        r'^\d+$',  # Isolated numbers
        r'^[IVX]+$',  # Roman numerals
        r'^(https?|doi|www|org|com|net)$',  # URL parts
        r'^(fig|figure|table|ref|references?)$',  # Figure/table references
        r'^[a-z]$',  # Single letters
        r'^\d{4}$',  # Years
        r'^(pp?|vol|no|issue)$',  # Publication terms
    ]

    # If we have target parts, avoid them too
    if target_parts:
        for part in target_parts:
            if len(part) > 2:  # Only meaningful parts
                avoid_patterns.append(rf'^{re.escape(part)}$')

    filtered_words = []
    for word in words:
        word_lower = word.lower()
        # Check if word should be avoided
        should_avoid = False
        for pattern in avoid_patterns:
            if re.match(pattern, word_lower, re.IGNORECASE):
                should_avoid = True
                break

        if not should_avoid:
            filtered_words.append(word)

    return filtered_words


def extract_preceding_words_from_text(text, target, num_words=50):
    """
    Extract preceding words before a target in text, prioritizing diverse contextual content.

    Args:
        text (str): Text to search in
        target (str): Target string to find
        num_words (int): Number of preceding words to extract

    Returns:
        list: List of preceding English words
    """
    # Clean text first
    cleaned_text = clean_text_for_urls(text)

    # Split into sections to prioritize non-reference content
    sections = split_text_into_sections(cleaned_text)

    # Find the target in the text
    text_lower = cleaned_text.lower()
    target_lower = str(target).lower()

    # Try to find the target
    pos = text_lower.find(target_lower)
    if pos == -1:
        # Try partial matches for DOIs
        if target_lower.startswith('https://doi.org/'):
            partial_target = target_lower.replace('https://doi.org/', '')
            pos = text_lower.find(partial_target)
        elif 'doi.org/' in target_lower:
            partial_target = target_lower.split('doi.org/', 1)[1]
            pos = text_lower.find(partial_target)

    if pos == -1:
        return []

    # Determine which section contains the target
    current_pos = 0
    target_section = None
    target_section_pos = None

    for section_text, section_type in sections:
        section_end = current_pos + len(section_text)
        if current_pos <= pos < section_end:
            target_section = section_text
            target_section_pos = pos - current_pos
            break
        current_pos = section_end

    if target_section is None:
        # Fallback to original method
        preceding_text = cleaned_text[:pos]
        return clean_and_extract_words(preceding_text, num_words)

    # Extract preceding text from the target section
    preceding_text = target_section[:target_section_pos]

    # If we don't have enough words and target is not in references,
    # try to get more context from earlier sections
    words = clean_and_extract_words(preceding_text, num_words)

    if len(words) < num_words and sections:
        # Try to get more diverse context from earlier sections
        additional_words_needed = num_words - len(words)

        # Look for data availability, acknowledgment, or methods sections
        priority_section_patterns = [
            r'data\s+availability',
            r'data\s+accessibility',
            r'acknowledgment',
            r'acknowledgement',
            r'methods',
            r'materials\s+and\s+methods',
            r'experimental',
        ]

        for section_text, section_type in sections:
            if section_type == 'reference':
                continue

            section_lower = section_text.lower()

            # Check if this section contains priority content
            has_priority_content = any(
                re.search(pattern, section_lower)
                for pattern in priority_section_patterns
            )

            if has_priority_content:
                # Extract words from this section
                section_words = clean_and_extract_words(section_text, additional_words_needed)
                # Add unique words to the beginning
                for word in reversed(section_words):
                    if word not in words:
                        words.insert(0, word)
                        if len(words) >= num_words:
                            break

                if len(words) >= num_words:
                    break

    return words[:num_words]


def extract_diverse_context_words(text, target, num_words=50):
    """
    Extract diverse contextual words around a target, avoiding repetitive content.

    Args:
        text (str): Text to search in
        target (str): Target string to find
        num_words (int): Number of words to extract

    Returns:
        list: List of diverse contextual words
    """
    # Clean text first
    cleaned_text = clean_text_for_urls(text)

    # Find target position
    text_lower = cleaned_text.lower()
    target_lower = str(target).lower()

    pos = text_lower.find(target_lower)
    if pos == -1:
        return []

    # Extract context window around target (larger window for diversity)
    context_size = 2000  # Characters before and after
    start_pos = max(0, pos - context_size)
    end_pos = min(len(cleaned_text), pos + len(target) + context_size)

    context_text = cleaned_text[start_pos:end_pos]

    # Split into sentences and extract diverse words
    sentences = re.split(r'[.!?]+', context_text)

    all_words = []
    seen_words = set()

    for sentence in sentences:
        sentence_words = clean_and_extract_words(sentence, 20)  # Get up to 20 words per sentence

        # Add unique words
        for word in sentence_words:
            if word.lower() not in seen_words and len(word) >= 4:
                all_words.append(word)
                seen_words.add(word.lower())

                if len(all_words) >= num_words:
                    break

        if len(all_words) >= num_words:
            break

    return all_words[:num_words]
