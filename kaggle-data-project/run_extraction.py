"""
Script para executar a extração de dataset_ids dos PDFs.
"""

import sys
from pathlib import Path

# Adicionar o diretório atual ao path
sys.path.insert(0, str(Path(__file__).parent))

from pdf_dataset_extractor import DatasetExtractor


def main():
    """Executa a extração com um número limitado de arquivos para teste."""
    
    print("=== Extração de Dataset IDs de PDFs ===")
    print("Processando uma amostra dos arquivos para teste...\n")
    
    # Inicializar extrator
    extractor = DatasetExtractor()
    
    # Processar apenas os primeiros 50 arquivos para teste
    print("Processando os primeiros 50 arquivos...")
    results_df = extractor.process_all_pdfs(max_files=50)
    
    # Salvar resultados
    extractor.save_results(results_df, 'pdf_extraction_sample_50.csv')
    
    # Mostrar alguns exemplos dos resultados
    print(f"\n=== Exemplos de Resultados ===")
    
    # Casos onde encontrou o dataset_id
    found_cases = results_df[
        (~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)) &
        (results_df['dataset_id'] != 'Missing')
    ]
    
    if len(found_cases) > 0:
        print(f"\n✅ Dataset IDs encontrados ({len(found_cases)} casos):")
        for idx, row in found_cases.head(3).iterrows():
            print(f"  📄 {row['article_id']}")
            print(f"  🔗 {row['dataset_id']}")
            print(f"  📝 Palavras anteriores: {row['preceding_words']}")
            print()
    
    # Casos Missing
    missing_cases = results_df[results_df['dataset_id'] == 'Missing']
    if len(missing_cases) > 0:
        print(f"🎲 Casos Missing ({len(missing_cases)} casos):")
        for idx, row in missing_cases.head(2).iterrows():
            print(f"  📄 {row['article_id']}")
            print(f"  📝 Palavras aleatórias: {row['preceding_words']}")
            print()
    
    # Casos não encontrados
    not_found_cases = results_df[
        results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)
    ]
    if len(not_found_cases) > 0:
        print(f"❌ Não encontrados/Erros ({len(not_found_cases)} casos):")
        for idx, row in not_found_cases.head(2).iterrows():
            print(f"  📄 {row['article_id']}")
            print(f"  🔗 {row['dataset_id']}")
            print(f"  ⚠️  Status: {row['preceding_words']}")
            print()
    
    print("=" * 60)
    print("✅ Processamento da amostra concluído!")
    print("📁 Resultados salvos em: results/pdf_extraction_sample_50.csv")
    print("\nPara processar todos os arquivos, modifique o script ou use:")
    print("extractor.process_all_pdfs(max_files=None)")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Processamento interrompido pelo usuário.")
    except Exception as e:
        print(f"\n❌ Erro durante o processamento: {e}")
        import traceback
        traceback.print_exc()
