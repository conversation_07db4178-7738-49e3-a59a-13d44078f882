"""
Simple test script for enhanced extraction.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>


def clean_text_for_urls(text):
    """Clean text to handle URLs split across lines."""
    # Fix common URL splits
    text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
    text = re.sub(r'doi\.org/\s*\n\s*', 'doi.org/', text)
    text = re.sub(r'https://doi\.\s+org/', 'https://doi.org/', text)
    text = re.sub(r'10\.\d+/\s*\n\s*', '10.', text)
    
    # Fix dryad URLs specifically
    text = re.sub(r'dryad\.\s*\n\s*', 'dryad.', text)
    text = re.sub(r'10\.5061/\s*\n\s*dryad\.', '10.5061/dryad.', text)
    
    # Remove excessive whitespace but preserve single spaces
    text = re.sub(r'\s+', ' ', text)
    
    return text


def normalize_dataset_id_comprehensive(dataset_id):
    """Create comprehensive variations of dataset_id for matching."""
    if dataset_id == "Missing":
        return []
    
    variations = [dataset_id]  # Original version
    
    # Handle https:// URLs
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)
        
        # Handle doi.org URLs
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)
    
    # Handle doi.org URLs without https://
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
        variations.append(f"https://{dataset_id}")
        variations.append(f"https://doi.org/{after_doi}")
    
    # Handle bare DOIs (like 10.5061/dryad.r6nq870)
    elif dataset_id.startswith("10."):
        variations.append(f"doi.org/{dataset_id}")
        variations.append(f"https://doi.org/{dataset_id}")
    
    # Remove duplicates while preserving order
    seen = set()
    unique_variations = []
    for var in variations:
        if var not in seen:
            seen.add(var)
            unique_variations.append(var)
    
    return unique_variations


def extract_preceding_words_robust(text, target, num_words=10):
    """Extract preceding words with robust handling."""
    try:
        # Clean text first
        cleaned_text = clean_text_for_urls(text)
        
        # Try exact match first
        escaped_target = re.escape(target)
        pattern = rf'(.+?)\b{escaped_target}\b'
        match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
        
        if match:
            preceding_text = match.group(1)
            words = re.findall(r'\b\w+\b', preceding_text)
            if len(words) >= num_words:
                return ' '.join(words[-num_words:])
            else:
                return ' '.join(words)
        
        # Try partial matching for DOIs
        if "doi.org/" in target or target.startswith("10."):
            # Extract the DOI part
            if "doi.org/" in target:
                doi_part = target.split("doi.org/", 1)[1]
            else:
                doi_part = target
            
            # Clean DOI part
            doi_clean = re.sub(r'[^\w/.-]', '', doi_part)
            
            escaped_doi = re.escape(doi_clean)
            pattern = rf'(.+?)\b{escaped_doi}\b'
            match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
            
            if match:
                preceding_text = match.group(1)
                words = re.findall(r'\b\w+\b', preceding_text)
                if len(words) >= num_words:
                    return ' '.join(words[-num_words:])
                else:
                    return ' '.join(words)
    
    except Exception:
        pass
    
    return None


def process_single_pdf_test(pdf_path, article_id, dataset_id):
    """Process a single PDF for testing."""
    
    try:
        reader = PdfReader(pdf_path)
        
        # Extract text from all pages
        full_text = ""
        for page in reader.pages:
            try:
                page_text = page.extract_text()
                full_text += page_text + "\n"
            except Exception:
                continue
        
        if dataset_id == "Missing":
            return article_id, dataset_id, "MISSING_CASE"
        
        # Get all variations of the dataset ID
        variations = normalize_dataset_id_comprehensive(dataset_id)
        
        # Try to find each variation
        for variation in variations:
            preceding_words = extract_preceding_words_robust(full_text, variation)
            if preceding_words:
                return article_id, dataset_id, preceding_words
        
        return article_id, dataset_id, "DATASET_ID_NOT_FOUND"
        
    except Exception as e:
        return article_id, dataset_id, f"ERROR: {str(e)[:50]}"


def main():
    """Test the specific cases."""
    
    test_cases = [
        ("10.1002_2017jc013030", "https://doi.org/10.17882/49388"),
        ("10.1002_ece3.4466", "https://doi.org/10.5061/dryad.r6nq870")
    ]
    
    print("🧪 Testing enhanced extraction...")
    
    results = []
    for article_id, dataset_id in test_cases:
        pdf_path = f"data/train/PDF/{article_id}.pdf"
        
        if Path(pdf_path).exists():
            print(f"\n📄 Testing {article_id}...")
            result = process_single_pdf_test(pdf_path, article_id, dataset_id)
            results.append(result)
            print(f"   Result: {result[2]}")
        else:
            print(f"❌ PDF not found: {pdf_path}")
    
    # Save results
    if results:
        results_df = pd.DataFrame(results, columns=['article_id', 'dataset_id', 'preceding_words'])
        results_df.to_csv('results/test_enhanced_results.csv', index=False)
        print(f"\n✅ Results saved to results/test_enhanced_results.csv")
    
    return results


if __name__ == "__main__":
    main()
