"""
Debug script to examine specific PDF files that are not being extracted correctly.
"""

import re
from pathlib import Path
from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>


def debug_pdf_content(pdf_path, dataset_id, article_id):
    """Debug function to examine PDF content and find dataset IDs."""
    print(f"\n🔍 Debugging {article_id}")
    print(f"Looking for dataset_id: {dataset_id}")
    print("=" * 60)
    
    try:
        reader = PdfReader(pdf_path)
        
        # Extract text from all pages
        full_text = ""
        for i, page in enumerate(reader.pages):
            try:
                page_text = page.extract_text()
                full_text += page_text + "\n"
                
                # Look for specific patterns in each page
                if any(pattern in page_text.lower() for pattern in ['doi.org', 'dryad', 'data', 'dataset', 'availability']):
                    print(f"\n📄 Page {i+1} contains relevant keywords")
                    
                    # Look for DOI patterns
                    doi_patterns = [
                        r'doi\.org/[^\s\)]+',
                        r'https://doi\.org/[^\s\)]+',
                        r'doi:\s*https://[^\s\)]+',
                        r'10\.\d+/[^\s\)]+',
                        r'dryad[^\s]*',
                        r'47142[^\s]*',
                        r'49388[^\s]*',
                        r'r6nq870[^\s]*'
                    ]
                    
                    for pattern in doi_patterns:
                        matches = re.findall(pattern, page_text, re.IGNORECASE)
                        if matches:
                            print(f"  Pattern '{pattern}' found:")
                            for match in matches[:5]:  # Show first 5 matches
                                print(f"    '{match}'")
                    
                    # Look for specific sections
                    sections = [
                        r'data\s+availability[^\n]*\n[^\n]*\n[^\n]*',
                        r'data\s+accessibilit[^\n]*\n[^\n]*\n[^\n]*',
                        r'acknowledgment[^\n]*\n[^\n]*\n[^\n]*',
                        r'thank[^\n]*\n[^\n]*\n[^\n]*'
                    ]
                    
                    for section_pattern in sections:
                        matches = re.findall(section_pattern, page_text, re.IGNORECASE | re.DOTALL)
                        if matches:
                            print(f"  Section pattern found:")
                            for match in matches[:2]:
                                print(f"    '{match.strip()}'")
                    
            except Exception as e:
                print(f"❌ Error on page {i+1}: {e}")
        
        # Look for the specific dataset ID patterns in full text
        print(f"\n🎯 Searching for dataset ID variations:")
        
        # Create variations of the dataset ID
        variations = []
        if dataset_id.startswith("https://"):
            variations.append(dataset_id)
            variations.append(dataset_id.replace("https://", ""))
            if "doi.org/" in dataset_id:
                after_doi = dataset_id.split("doi.org/", 1)[1]
                variations.append(after_doi)
                variations.append(f"doi:{dataset_id}")
                variations.append(f"doi: {dataset_id}")
        
        for var in variations:
            if var in full_text:
                print(f"✅ Found variation: '{var}'")
                # Find context around it
                escaped_var = re.escape(var)
                context_pattern = rf'.{{0,100}}{escaped_var}.{{0,100}}'
                context_match = re.search(context_pattern, full_text, re.IGNORECASE | re.DOTALL)
                if context_match:
                    print(f"   Context: '{context_match.group().strip()}'")
            else:
                print(f"❌ Not found: '{var}'")
        
        # Look for partial matches
        print(f"\n🔍 Looking for partial matches:")
        if "doi.org/" in dataset_id:
            doi_part = dataset_id.split("doi.org/", 1)[1]
            if doi_part in full_text:
                print(f"✅ Found DOI part: '{doi_part}'")
                # Find context
                escaped_doi = re.escape(doi_part)
                context_pattern = rf'.{{0,100}}{escaped_doi}.{{0,100}}'
                context_match = re.search(context_pattern, full_text, re.IGNORECASE | re.DOTALL)
                if context_match:
                    print(f"   Context: '{context_match.group().strip()}'")
        
        # Look for URLs that might be split across lines
        print(f"\n🔗 Looking for split URLs:")
        split_patterns = [
            r'https://doi\.\s*\n\s*org/[^\s]+',
            r'doi\.org/\s*\n\s*[^\s]+',
            r'https://doi\.\s+org/[^\s]+',
            r'10\.\d+/\s*\n\s*[^\s]+'
        ]
        
        for pattern in split_patterns:
            matches = re.findall(pattern, full_text, re.IGNORECASE)
            if matches:
                print(f"  Split URL pattern found:")
                for match in matches[:3]:
                    cleaned = re.sub(r'\s+', '', match)
                    print(f"    Original: '{match}' -> Cleaned: '{cleaned}'")
        
    except Exception as e:
        print(f"❌ Error processing PDF: {e}")


def main():
    """Main function to debug specific PDFs."""
    
    # Test cases from the user
    test_cases = [
        ("data/train/PDF/10.1002_2017jc013030.pdf", "https://doi.org/10.17882/49388", "10.1002_2017jc013030"),
        ("data/train/PDF/10.1002_ece3.4466.pdf", "https://doi.org/10.5061/dryad.r6nq870", "10.1002_ece3.4466")
    ]
    
    for pdf_path, dataset_id, article_id in test_cases:
        if Path(pdf_path).exists():
            debug_pdf_content(pdf_path, dataset_id, article_id)
        else:
            print(f"❌ PDF not found: {pdf_path}")


if __name__ == "__main__":
    main()
