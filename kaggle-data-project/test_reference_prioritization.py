"""
Test the reference prioritization in dataset ID extraction.
"""

import time
from extract_all_pdfs import process_pdf

def test_reference_prioritization():
    """Test that dataset IDs outside references are prioritized."""
    
    # Test cases that might have dataset IDs in both references and main text
    test_cases = [
        ("10.1002_2017jc013030", "https://doi.org/10.17882/49388"),  # Should be found
        ("10.1002_ece3.4466", "https://doi.org/10.5061/dryad.r6nq870"),  # Should be found in data availability
        ("10.1002_ecs2.4619", "https://doi.org/10.25349/d9qw5x"),  # Should be found
        ("10.1002_ece3.9627", "https://doi.org/10.5061/dryad.b8gtht7h3"),  # Should be found
    ]
    
    print("=== Testing Reference vs Non-Reference Prioritization ===\n")
    
    for i, (article_id, dataset_id) in enumerate(test_cases, 1):
        print(f"Test {i}/{len(test_cases)}: {article_id}")
        print(f"Dataset ID: {dataset_id}")
        
        start_time = time.time()
        result = process_pdf(article_id, dataset_id)
        end_time = time.time()
        
        processing_time = end_time - start_time
        result_text = result[2]
        
        # Analyze the result
        if "REF:" in result_text or "REF_SECTION:" in result_text or "REF_PARTIAL:" in result_text:
            status = "📚 Found in REFERENCES"
        elif "SECTION:" in result_text:
            status = "✅ Found in DATA SECTION"
        elif "PARTIAL:" in result_text:
            status = "🔍 Found PARTIAL match"
        elif "DATASET_ID_NOT_FOUND" in result_text:
            status = "❌ Not found"
        else:
            status = "✅ Found in MAIN TEXT"
        
        print(f"Status: {status}")
        print(f"Result: {result_text}")
        print(f"Time: {processing_time:.2f} seconds")
        print("-" * 80)
        print()

if __name__ == "__main__":
    test_reference_prioritization()
